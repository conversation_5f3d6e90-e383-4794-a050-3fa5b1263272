FROM webdevops/php-apache:7.4-alpine AS base

COPY --from=binxio/gcp-get-secret /gcp-get-secret /usr/local/bin/
ENV DATABASE_PASS=gcp:///DATABASE_PASS
ENV EMAIL_SENDGRID_API_KEY=gcp:///EMAIL_SENDGRID_API_KEY
ENV EMAIL_TRANSPORT_SMTP_PASS=gcp:///EMAIL_TRANSPORT_SMTP_PASS
ENV INV_DATA_PASS=gcp:///INV_DATA_PASS

RUN apk add --no-cache npm
ARG NODE_ENV=production
ENV NODE_ENV=${NODE_ENV}

ARG DEV_MODE
ENV DEV_MODE=${DEV_MODE}

USER www-data
# Fix for the docker legacy builder setting the owner of this implicitly created directory to root.
# https://github.com/moby/moby/issues/42184#issuecomment-804199013
RUN mkdir -p /var/www/html/shipearly/app

FROM base AS dependencies
WORKDIR /var/www/html/shipearly/app
COPY --chown=www-data:www-data ./app/composer.json ./app/composer.lock ./
# Composer does not automatically create vendor directories
RUN for VENDOR_DIR in \
      "./Plugin/" \
      "./Vendor/" \
      "./webroot/css/font-awesome/css/" \
      "./webroot/css/font-awesome/webfonts/" \
      "./webroot/js/vendor/" \
    ; do \
      mkdir -p ${VENDOR_DIR}; \
    done
RUN composer install --no-interaction --no-progress --no-autoloader --no-dev
COPY --chown=www-data:www-data ./app/package.json ./app/package-lock.json ./
RUN npm ci


FROM base AS shipearlyapp
WORKDIR /var/www/html/shipearly
COPY --chown=www-data:www-data . .
RUN for TMP_DIR in \
      "./app/tmp/" \
      "./app/tmp/cache/" \
      "./app/tmp/cache/lightspeed/" \
      "./app/tmp/cache/long/" \
      "./app/tmp/cache/medium/" \
      "./app/tmp/cache/models/" \
      "./app/tmp/cache/persistent/" \
      "./app/tmp/cache/product/" \
      "./app/tmp/cache/shopifypos/" \
      "./app/tmp/cache/short/" \
      "./app/tmp/cache/vend/" \
      "./app/tmp/cache/verylong/" \
      "./app/tmp/cache/views/" \
      "./app/tmp/logs/" \
      "./app/webroot/files/" \
    ; do \
      mkdir -p ${TMP_DIR}; \
    done

COPY --from=dependencies --chown=www-data:www-data /var/www/html/shipearly/app ./app/
RUN ( cd ./app/ && composer dump-autoload --optimize --no-dev )
RUN ( cd ./app/ && npm run build )

# Not bothering to multi-stage or cache the DEV_MODE build. Just rebuild to replace the production build.
RUN if [ "$DEV_MODE" = 1 ]; \
    then \
      echo "Installing app/Vendor composer dev dependencies..." && \
      ( cd ./app/ && composer install --no-interaction --no-progress ) && \
      echo "Installing ShipearlyTests composer dev dependencies..." && \
      ( cd ./ShipearlyTests/ && composer install --no-interaction --no-progress ); \
    fi

# enable fpm status page
#COPY status.conf /opt/docker/etc/httpd/conf.d/

ENTRYPOINT ["/entrypoint"]
CMD ["./setup-docker.sh"]
