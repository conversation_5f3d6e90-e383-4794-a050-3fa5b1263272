<?php

namespace ShipearlyTests\Cases\B2bCatalogue;

use Facebook\WebDriver\Remote\RemoteWebElement;
use ShipearlyTests\Cases\ShipearlyTestCase;
use ShipearlyTests\PageObjects\PageObject;
use ShipearlyTests\PageObjects\Product\B2bCataloguePage;
use ShipearlyTests\PageObjects\Product\ProductViewPage;

class B2bCatalogueConsumerViewTest extends ShipearlyTestCase
{
    const PRODUCT_NAME = 'Bicycle';
    const GRID_VIEW = 'grid';
    const LIST_VIEW = 'list';

    /**
     * @dataProvider providerTestProductListToggleConsumerViewRetailer
     */
    public function testProductListToggleConsumerViewRetailer(string $viewType)
    {
        $this->Login->loginAsRetailer();
        $b2bCatalogue = $this->Pages->Dashboard
            ->navigateTo()
            ->createPurchaseOrder()
            ->setBrand()
            ->setLocation()
            ->selectOrderType();

        if ($viewType === static::GRID_VIEW) {
            $b2bCatalogue->clickDisplayGrid();
        } elseif ($viewType === static::LIST_VIEW) {
            $b2bCatalogue->clickDisplayList();
        }

        $b2bCatalogue->toggleConsumerView(true);
        $this->assertDisplayedConsumerViewElements($b2bCatalogue, true);

        $b2bCatalogue->toggleConsumerView(false);
        $this->assertDisplayedConsumerViewElements($b2bCatalogue, false);

    }

    public function providerTestProductListToggleConsumerViewRetailer()
    {
        return [
            'gridView' => [
                'viewType' => static::GRID_VIEW,
            ],
            'listView' => [
                'viewType' => static::LIST_VIEW,
            ],
        ];
    }

    public function testProductListConsumerViewUnavailableForBrand()
    {
        $this->Login->loginAsBrand();
        $b2bCatalogue = $this->Pages->DealerOrderIndex
            ->navigateTo()
            ->createWholesaleOrder()
            ->setRetailer()
            ->setLocation()
            ->selectOrderType();

        $this->assertFalse($b2bCatalogue->hasConsumerViewToggleElement(), 'Consumer view toggle should not be available for brand');
    }

    public function testProductViewToggleConsumerViewRetailer()
    {
        $this->Login->loginAsRetailer();
        $productViewPage = $this->Pages->Dashboard
            ->navigateTo()
            ->createPurchaseOrder()
            ->setBrand()
            ->setLocation()
            ->selectOrderType()
            ->clickDisplayGrid()
            ->searchForProduct(static::PRODUCT_NAME)
            ->openGridProduct();

        $productViewPage->toggleConsumerView(true);
        $this->assertDisplayedConsumerViewElements($productViewPage, true);

        $productViewPage->toggleConsumerView(false);
        $this->assertDisplayedConsumerViewElements($productViewPage, false);
    }

    public function testProductViewConsumerViewUnavailableForBrand()
    {
        $this->Login->loginAsBrand();
        $productViewPage = $this->Pages->DealerOrderIndex
            ->navigateTo()
            ->createWholesaleOrder()
            ->setRetailer()
            ->setLocation()
            ->selectOrderType()
            ->clickDisplayList()
            ->openListProduct();

        $this->assertFalse($productViewPage->hasConsumerViewToggleElement(), 'Consumer view toggle should not be available for brand');
    }

    /**
     * @param ProductViewPage|B2bCataloguePage $pageObject
     * @param bool $consumerViewShown
     * @return void
     */
    protected function assertDisplayedConsumerViewElements(PageObject $pageObject, bool $consumerViewShown): void
    {
        $consumerViewShownElements = $pageObject->getConsumerViewShownElements();
        $consumerViewHiddenElements = $pageObject->getConsumerViewHiddenElements();
        $getMapElements = function(?bool $isDisplayed): callable {
            return fn(RemoteWebElement $el): array => [
                'element' => $el->getTagName() . ': ' . ($el->getAttribute('textContent') ?: $el->getAttribute('class')),
                'isDisplayed' => $isDisplayed ?? $el->isDisplayed(),
            ];
        };

        $expected = [
            'shown' => array_map($getMapElements($consumerViewShown), $consumerViewShownElements),
            'hidden' => array_map($getMapElements(!$consumerViewShown), $consumerViewHiddenElements),
        ];
        $actual = [
            'shown' => array_map($getMapElements(null), $consumerViewShownElements),
            'hidden' => array_map($getMapElements(null), $consumerViewHiddenElements),
        ];
        $this->assertEquals($expected, $actual, $consumerViewShown ? 'Consumer view elements should be shown' : 'Consumer view elements should be hidden');
    }
}
