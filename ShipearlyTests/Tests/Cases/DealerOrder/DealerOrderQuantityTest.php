<?php
namespace ShipearlyTests\Cases\DealerOrder;

use ShipearlyTests\Cases\ShipearlyTestCase;

class DealerOrderQuantityTest extends ShipearlyTestCase
{
    protected function tearDownDatabase() {}

    /**
     * @dataProvider providerDealerOrderQuantities
     */
    public function testNeedToConfirmResults($quantityInOrder, $quantityInStock, $expectedDealerOrderQuantity)
    {
        $orderNumber = $this->Shopify->createNewInstorePickupOrder($quantityInOrder);
        $this->Login->loginAsRetailer();
        $this->Pages->OrderIndex->navigateTo()->openNeedToConfirmOrder($orderNumber);

        $this->Pages->OrderNeedToConfirm->setAvailableQuantities($quantityInStock);

        $actualDealerOrderQuantity = $this->Pages->OrderNeedToConfirm->getResultQuantities();
        $this->assertEquals($expectedDealerOrderQuantity, $actualDealerOrderQuantity);
    }

    /**
     * @dataProvider providerDealerOrderQuantities
     */
    public function testDealerOrderQuantitiesInRetailerDealerOrdersTab($quantityInOrder, $quantityInStock, $expectedDealerOrderQuantity)
    {
        $orderNumber = $this->Shopify->createNewInstorePickupOrder($quantityInOrder);
        $this->Login->loginAsRetailer();
        $this->Pages->OrderIndex->navigateTo()->openNeedToConfirmOrder($orderNumber);

        $this->Pages->OrderNeedToConfirm->setAvailableQuantities($quantityInStock)
                                        ->submit();

        $this->Pages->DealerOrderIndex->navigateTo()->openOrderEditPage($orderNumber);
        $expectedDealerOrderQuantity = array_filter($expectedDealerOrderQuantity);
        $this->assertEquals($expectedDealerOrderQuantity, $this->Pages->DealerOrderEdit->getDealerOrderProductQuantities());
    }

    /**
     * @dataProvider providerDealerOrderQuantities
     */
    public function testDealerOrderQuantitiesInRetailerOrdersTab($quantityInOrder, $quantityInStock, $expectedDealerOrderQuantity)
    {
        $orderNumber = $this->Shopify->createNewInstorePickupOrder($quantityInOrder);
        $this->Login->loginAsRetailer();
        $this->Pages->OrderIndex->navigateTo()->openNeedToConfirmOrder($orderNumber);

        $this->Pages->OrderNeedToConfirm->setAvailableQuantities($quantityInStock)
                                        ->submit();

        $this->Pages->OrderIndex->openOrderInvoice($orderNumber);
        $this->assertEquals($quantityInOrder, $this->Pages->OrderInvoice->getOrderProductQuantities());
    }

    /**
     * @dataProvider providerDealerOrderQuantities
     */
    public function testDealerOrderQuantitiesInRetailerDashboard($quantityInOrder, $quantityInStock, $expectedDealerOrderQuantity)
    {
        $orderNumber = $this->Shopify->createNewInstorePickupOrder($quantityInOrder);
        $this->Login->loginAsRetailer();
        $this->Pages->OrderIndex->navigateTo()->openNeedToConfirmOrder($orderNumber);

        $this->Pages->OrderNeedToConfirm->setAvailableQuantities($quantityInStock)
                                        ->submit();

        $this->Pages->Dashboard->navigateTo()->openOrder($orderNumber);
        $expectedDealerOrderQuantity = array_filter($expectedDealerOrderQuantity);
        $this->assertEquals($expectedDealerOrderQuantity, $this->Pages->DealerOrderEdit->getDealerOrderProductQuantities());
    }

    /**
     * @dataProvider providerDealerOrderQuantities
     */
    public function testDealerOrderQuantitiesInBrandDealerOrdersTab($quantityInOrder, $quantityInStock, $expectedDealerOrderQuantity)
    {
        $orderNumber = $this->Shopify->createNewInstorePickupOrder($quantityInOrder);
        $this->Login->loginAsRetailer();
        $this->Pages->OrderIndex->navigateTo()->openNeedToConfirmOrder($orderNumber);

        $this->Pages->OrderNeedToConfirm->setAvailableQuantities($quantityInStock)
                                        ->submit();

        $this->Login->loginAsBrand();
        $this->Pages->DealerOrderIndex->navigateTo()->openOrderEditPage($orderNumber);
        $expectedDealerOrderQuantity = array_filter($expectedDealerOrderQuantity);
        $this->assertEquals($expectedDealerOrderQuantity, $this->Pages->DealerOrderEdit->getDealerOrderProductQuantities());
    }

    /**
     * @dataProvider providerDealerOrderQuantities
     */
    public function testDealerOrderQuantitiesInBrandOrdersTab($quantityInOrder, $quantityInStock, $expectedDealerOrderQuantity)
    {
        $orderNumber = $this->Shopify->createNewInstorePickupOrder($quantityInOrder);
        $this->Login->loginAsRetailer();
        $this->Pages->OrderIndex->navigateTo()->openNeedToConfirmOrder($orderNumber);

        $this->Pages->OrderNeedToConfirm->setAvailableQuantities($quantityInStock)
                                        ->submit();

        $this->Login->loginAsBrand();
        $this->Pages->OrderIndex->navigateTo()->openOrderInvoice($orderNumber);
        $this->assertEquals($quantityInOrder, $this->Pages->OrderInvoice->getOrderProductQuantities());
    }

    /**
     * @dataProvider providerDealerOrderQuantities
     */
    public function testDealerOrderQuantitiesInBrandDashboard($quantityInOrder, $quantityInStock, $expectedDealerOrderQuantity)
    {
        $orderNumber = $this->Shopify->createNewInstorePickupOrder($quantityInOrder);
        $this->Login->loginAsRetailer();
        $this->Pages->OrderIndex->navigateTo()->openNeedToConfirmOrder($orderNumber);

        $this->Pages->OrderNeedToConfirm->setAvailableQuantities($quantityInStock)
                                        ->submit();

        $this->Login->loginAsBrand();

        $this->Pages->Dashboard->navigateTo()->openOrder($orderNumber);
        $expectedDealerOrderQuantity = array_filter($expectedDealerOrderQuantity);
        $this->assertEquals($expectedDealerOrderQuantity, $this->Pages->DealerOrderEdit->getDealerOrderProductQuantities());
    }

    public function providerDealerOrderQuantities()
    {
        return array(
            array(
                'quantityInOrder'             => array('Bicycle' => 3, 'No Stock' => 2, 'Missing UPC' => 1),
                'quantityInStock'             => array('Bicycle' => 1, 'No Stock' => 1, 'Missing UPC' => 1),
                'expectedDealerOrderQuantity' => array('Bicycle' => 2, 'No Stock' => 1, 'Missing UPC' => 0)
            ),
            array(
                'quantityInOrder'             => array('Bicycle' => 2, 'No Stock' => 2, 'Missing UPC' => 2),
                'quantityInStock'             => array('Bicycle' => 0, 'No Stock' => 0, 'Missing UPC' => 0),
                'expectedDealerOrderQuantity' => array('Bicycle' => 2, 'No Stock' => 2, 'Missing UPC' => 2)
            ),
        );
    }

    public function testNeedToConfirmShowsNoInventoryForProductsNotInPOS()
    {
        $products = array('No Stock', 'Missing UPC', 'No UPC');
        $expectedInventory = array('No Stock' => 0, 'Missing UPC' => 0, 'No UPC' => 0);

        $orderNumber = $this->Shopify->createNewInstorePickupOrder($products);

        $this->Login->loginAsRetailer();
        $this->Pages->OrderIndex->navigateTo()->openNeedToConfirmOrder($orderNumber);
        $this->assertEquals($expectedInventory, $this->Pages->OrderNeedToConfirm->getAvailableQuantities());
    }

    /**
     * @dataProvider providerDealerOrderExtraQuantities
     */
    public function testDealerOrderExtraQuantitiesInRetailerDealerOrdersTab($quantityInOrder, $quantityInWholesale)
    {
        $orderNumber = $this->Shopify->createNewInstorePickupOrder($quantityInOrder);
        $this->Login->loginAsRetailer();
        $this->Pages->OrderIndex->navigateTo()->openNeedToConfirmOrder($orderNumber);

        $this->Pages->OrderNeedToConfirm->setResultQuantities($quantityInWholesale)
                                        ->submit();

        $this->Pages->DealerOrderIndex->navigateTo()->openOrderEditPage($orderNumber);
        $expectedDealerOrderQuantity = array_filter($quantityInWholesale);
        $this->assertEquals($expectedDealerOrderQuantity, $this->Pages->DealerOrderEdit->getDealerOrderProductQuantities());
    }

    /**
     * @dataProvider providerDealerOrderExtraQuantities
     */
    public function testDealerOrderExtraQuantitiesInRetailerOrdersTab($quantityInOrder, $quantityInWholesale)
    {
        $orderNumber = $this->Shopify->createNewInstorePickupOrder($quantityInOrder);
        $this->Login->loginAsRetailer();
        $this->Pages->OrderIndex->navigateTo()->openNeedToConfirmOrder($orderNumber);

        $this->Pages->OrderNeedToConfirm->setResultQuantities($quantityInWholesale)
                                        ->submit();

        $this->Pages->OrderIndex->openOrderInvoice($orderNumber);
        $this->assertEquals($quantityInOrder, $this->Pages->OrderInvoice->getOrderProductQuantities());
    }

    /**
     * @dataProvider providerDealerOrderExtraQuantities
     */
    public function testDealerOrderExtraQuantitiesInRetailerDashboard($quantityInOrder, $quantityInWholesale)
    {
        $orderNumber = $this->Shopify->createNewInstorePickupOrder($quantityInOrder);
        $this->Login->loginAsRetailer();
        $this->Pages->OrderIndex->navigateTo()->openNeedToConfirmOrder($orderNumber);

        $this->Pages->OrderNeedToConfirm->setResultQuantities($quantityInWholesale)
                                        ->submit();

        $this->Pages->Dashboard->navigateTo()->openOrder($orderNumber);
        $expectedDealerOrderQuantity = array_filter($quantityInWholesale);
        $this->assertEquals($expectedDealerOrderQuantity, $this->Pages->DealerOrderEdit->getDealerOrderProductQuantities());
    }

    /**
     * @dataProvider providerDealerOrderExtraQuantities
     */
    public function testDealerOrderExtraQuantitiesInBrandDealerOrdersTab($quantityInOrder, $quantityInWholesale)
    {
        $orderNumber = $this->Shopify->createNewInstorePickupOrder($quantityInOrder);
        $this->Login->loginAsRetailer();
        $this->Pages->OrderIndex->navigateTo()->openNeedToConfirmOrder($orderNumber);

        $this->Pages->OrderNeedToConfirm->setResultQuantities($quantityInWholesale)
                                        ->submit();

        $this->Login->loginAsBrand();
        $this->Pages->DealerOrderIndex->navigateTo()->openOrderEditPage($orderNumber);
        $expectedDealerOrderQuantity = array_filter($quantityInWholesale);
        $this->assertEquals($expectedDealerOrderQuantity, $this->Pages->DealerOrderEdit->getDealerOrderProductQuantities());
    }

    /**
     * @dataProvider providerDealerOrderExtraQuantities
     */
    public function testDealerOrderExtraQuantitiesInBrandOrdersTab($quantityInOrder, $quantityInWholesale)
    {
        $orderNumber = $this->Shopify->createNewInstorePickupOrder($quantityInOrder);
        $this->Login->loginAsRetailer();
        $this->Pages->OrderIndex->navigateTo()->openNeedToConfirmOrder($orderNumber);

        $this->Pages->OrderNeedToConfirm->setResultQuantities($quantityInWholesale)
                                        ->submit();

        $this->Login->loginAsBrand();
        $this->Pages->OrderIndex->navigateTo()->openOrderInvoice($orderNumber);
        $this->assertEquals($quantityInOrder, $this->Pages->OrderInvoice->getOrderProductQuantities());
    }

    /**
     * @dataProvider providerDealerOrderExtraQuantities
     */
    public function testDealerOrderExtraQuantitiesInBrandDashboard($quantityInOrder, $quantityInWholesale)
    {
        $orderNumber = $this->Shopify->createNewInstorePickupOrder($quantityInOrder);
        $this->Login->loginAsRetailer();
        $this->Pages->OrderIndex->navigateTo()->openNeedToConfirmOrder($orderNumber);

        $this->Pages->OrderNeedToConfirm->setResultQuantities($quantityInWholesale)
                                        ->submit();

        $this->Login->loginAsBrand();

        $this->Pages->Dashboard->navigateTo()->openOrder($orderNumber);
        $expectedDealerOrderQuantity = array_filter($quantityInWholesale);
        $this->assertEquals($expectedDealerOrderQuantity, $this->Pages->DealerOrderEdit->getDealerOrderProductQuantities());
    }

    public function providerDealerOrderExtraQuantities()
    {
        return array(
            array(
                'quantityInOrder'     => array('Bicycle' => 1, 'No Stock' => 1, 'Missing UPC' => 1),
                'quantityInWholesale' => array('Bicycle' => 2, 'No Stock' => 2, 'Missing UPC' => 2),
            ),
        );
    }
}
