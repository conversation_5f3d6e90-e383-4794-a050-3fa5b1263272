<?php
namespace ShipearlyTests\Cases\Ecommerce;

use ShipearlyTests\Cases\ShipearlyTestCase;
use ShipearlyTests\Initializers\OrderInitializer;
use ShipearlyTests\Objects\CardDetails;

class ShopifyAgreeToTermsTest extends ShipearlyTestCase
{
    const TERMS_OF_SERVICE_ENG = 'terms of service \'"';
    const TERMS_OF_SERVICE_FRA = 'conditions d\'utilisation';

    /**
     * @var CardDetails
     */
    private $failingCard;

    protected function setUp()
    {
        parent::setUp();

        $this->failingCard = OrderInitializer::getCardDetailsObject();
        $this->failingCard->card_number_part1 = '4000';
        $this->failingCard->card_number_part2 = '0000';
        $this->failingCard->card_number_part3 = '0000';
        $this->failingCard->card_number_part4 = '0127';

        $this->Login->loginAsBrand();
        $this->Pages->CheckoutSettings
            ->navigateTo()
            ->setRequireTermsOfService(true)
            ->setEngTermsOfService(static::TERMS_OF_SERVICE_ENG)
            ->setFraTermsOfService(static::TERMS_OF_SERVICE_FRA)
            ->submit();
    }

    public function testAgreeToTermsEngContent()
    {
        $this->Pages->ShopifyCheckout
            ->checkoutProducts()
            ->setShippingAddressValues()
            ->submit()
            ->loadRetailers()
            ->selectRetailer()
            ->setPayment($this->failingCard)
            ->clickPlaceOrder();

        $this->assertEquals(static::TERMS_OF_SERVICE_ENG, $this->Pages->TermsOfServicePopup->getContent());
    }

    public function testAgreeToTermsFraContent()
    {
        $this->markTestSkipped('Unable to configure language in headless chrome');

        // Creating a new webdriver with below works but only in non-headless Chrome
        // ```
        // $prefs += ['intl.accept_languages' => 'fr-CA'];
        // $chromeOptions->setExperimentalOption('prefs', $prefs);
        // ```
        $this->webDriver->quit();
        $this->webDriver = \ShipearlyTests\Util\WebDriverFactory::createRemoteWebDriver('fr-CA');

        $this->Pages->ShopifyCheckout
            ->checkoutProducts()
            ->setShippingAddressValues()
            ->submit()
            ->loadRetailers()
            ->selectRetailer()
            ->setPayment($this->failingCard)
            ->clickPlaceOrder();

        $this->assertEquals(static::TERMS_OF_SERVICE_FRA, $this->Pages->TermsOfServicePopup->getContent());
    }

    public function testBlankTermsAreSkipped()
    {
        $this->Pages->CheckoutSettings
            ->setEngTermsOfService('')
            ->submit();

        $this->Pages->ShopifyCheckout
            ->checkoutProducts()
            ->setShippingAddressValues()
            ->submit()
            ->loadRetailers()
            ->selectRetailer()
            ->setPayment($this->failingCard)
            ->clickPlaceOrder();

        $this->assertFalse($this->Pages->TermsOfServicePopup->isPresent());
    }

    public function testConfirmTerms()
    {
        $this->Pages->ShopifyCheckout
            ->checkoutProducts()
            ->setShippingAddressValues()
            ->submit()
            ->loadRetailers()
            ->selectRetailer()
            ->setPayment($this->failingCard)
            ->clickPlaceOrder();

        $this->Pages->TermsOfServicePopup->confirm();

        $this->assertNotEquals('init', $this->Pages->ShopifyDeliveryMethods->getPlaceOrderState());
    }

    public function testCancelTerms()
    {
        $this->Pages->ShopifyCheckout
            ->checkoutProducts()
            ->setShippingAddressValues()
            ->submit()
            ->loadRetailers()
            ->selectRetailer()
            ->setPayment($this->failingCard)
            ->clickPlaceOrder();

        $this->Pages->TermsOfServicePopup->cancel();

        sleep(1);
        $this->assertEquals('init', $this->Pages->ShopifyDeliveryMethods->getPlaceOrderState());
    }
}
