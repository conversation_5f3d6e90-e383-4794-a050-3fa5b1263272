<?php
namespace ShipearlyTests\Cases\Ecommerce;

use ShipearlyTests\Cases\ShipearlyTestCase;
use ShipearlyTests\Initializers\OrderInitializer;
use ShipearlyTests\Objects\CheckoutDetails;

class ShopifyAutocompleteAddressTest extends ShipearlyTestCase
{
    protected function tearDownDatabase() {}

    public function testShopifyAutocompleteShippingAddress()
    {
        $address = OrderInitializer::getCheckoutDetailsObject();

        $expected = new CheckoutDetails();
        $expected->address = str_replace('ave', 'Avenue', $address->address);
        $expected->city = $address->city;
        $expected->country = $address->country;
        $expected->province = $address->province;
        $expected->postalcode = $address->postalcode;

        $this->Shopify->checkoutProducts();

        $actual = $this->Pages->ShopifyCustomerInfo->AddressForm
            ->setAutocompleteAddress(implode(', ', array_filter(get_object_vars($expected))))
            ->getAddressValues();

        $this->assertEquals($expected, $actual);
    }

    public function testShopifyAutocompleteBillingAddress()
    {
        $address = OrderInitializer::getCheckoutDetailsObject();

        $expected = new CheckoutDetails();
        $expected->address = str_replace('ave', 'Avenue', $address->address);
        $expected->city = $address->city;
        $expected->country = $address->country;
        $expected->province = $address->province;
        $expected->postalcode = $address->postalcode;

        $this->Shopify->checkoutProducts();
        $this->Shopify->enterShippingAddress();
        $this->Shopify->loadInstorePickupRetailers();
        $this->Pages->ShopifyDeliveryMethods->setBillingAddressOption(true);

        $actual = $this->Pages->ShopifyDeliveryMethods->AddressForm
            ->setAutocompleteAddress(implode(', ', array_filter(get_object_vars($expected))))
            ->getAddressValues();

        $this->assertEquals($expected, $actual);
    }
}
