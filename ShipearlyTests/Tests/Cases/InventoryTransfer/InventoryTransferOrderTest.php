<?php
namespace ShipearlyTests\Cases\InventoryTransfer;

use ShipearlyTests\Cases\ShipearlyTestCase;

class InventoryTransferOrderTest extends ShipearlyTestCase
{
    const WAREHOUSE = '2400 Nipigon Road';
    const PRODUCT = 'Bicycle';
    const TRANSFER_ID = 4;

    /**
     * @var string
     */
    protected $orderNumber;

    protected function setUp()
    {
        parent::setUp();

        $userId = 12;
        $product = static::PRODUCT;
        $this->Database->executeQuery(<<<SQL
UPDATE `ship_warehouse_products`
INNER JOIN `ship_products` ON (`ship_products`.`id` = `ship_warehouse_products`.`product_id`)
SET `ship_warehouse_products`.`quantity` = 0
WHERE `ship_products`.`user_id` = {$userId}
  AND `ship_products`.`product_title` = '{$product}';
UPDATE `ship_inventory_transfers`
SET `expected_arrival_date` = DATE_ADD(CURDATE(), INTERVAL `id` DAY)
WHERE `user_id` = {$userId};
DELETE FROM `ship_inventory_transfer_product_reservations`
WHERE `order_id` IN ( SELECT `id` FROM `ship_orders` WHERE `user_id` = {$userId} );
SQL
        );
    }

    public function testPurchaseOrderReservations()
    {
        $this->Login->loginAsBrand();
        $this->_createPurchaseOrderWithItemQuantity(1);

        $this->assertEquals(1, $this->_getTransferItemOrderedCount());

        $this->_editPurchaseOrderItemQuantity(5);

        $this->assertEquals(5, $this->_getTransferItemOrderedCount());

        $this->_confirmPurchaseOrder();

        $this->assertEquals(5, $this->_getTransferItemOrderedCount());

        // Separate records are more likely to reveal a calculation error
        $this->_addItemFulfillmentWithItemQuantity(2);
        $this->_addItemFulfillmentWithItemQuantity(1);

        $this->assertEquals(5, $this->_getTransferItemOrderedCount());

        $this->_addItemRefundWithItemQuantity(1);

        $this->assertEquals(4, $this->_getTransferItemOrderedCount());

        $this->_addItemRefundWithItemQuantity(2);

        $this->assertEquals(3, $this->_getTransferItemOrderedCount());
    }

    private function _createPurchaseOrderWithItemQuantity(int $quantity, string $product = self::PRODUCT, string $warehouse = self::WAREHOUSE): string
    {
        $this->Pages->DealerOrderIndex->navigateTo()
            ->createWholesaleOrder()
            ->setRetailer()
            ->setLocation()
            ->selectOrderType()
            ->clickDisplayList()
            ->expandProduct($product)->setQuantity($quantity, $product)->addToCart($product);
        $this->Pages->B2bCartView->navigateTo()
            ->setPlaceEcommerceCheckbox(false)
            ->submit()
            ->setPaymentMethodPopupSelect()
            ->submitPaymentMethodPopup();

        $this->assertTrue((bool)preg_match('/#SE[0-9]+/', $this->Pages->FlashMessage->getSuccess(), $matches), 'Success message order number not found');
        $this->orderNumber = (string)$matches[0];

        return $this->orderNumber;
    }

    private function _editPurchaseOrderItemQuantity(int $quantity, string $product = self::PRODUCT, string $warehouse = self::WAREHOUSE)
    {
        $this->Pages->DealerOrderIndex->navigateTo()
            ->openPurchaseOrderEditPage($this->orderNumber)
            ->setProductInputs([$warehouse => [$product => ['quantity' => $quantity]]]);
    }

    private function _confirmPurchaseOrder()
    {
        $this->Pages->DealerOrderIndex->navigateTo()
            ->openPurchaseOrderEditPage($this->orderNumber)
            ->setPlaceEcommerceOrder(false)
            ->confirmPricing();
    }

    private function _addItemFulfillmentWithItemQuantity(int $quantity, string $product = self::PRODUCT, string $warehouse = self::WAREHOUSE)
    {
        $this->Pages->DealerOrderIndex->navigateTo()
            ->openPurchaseOrderEditPage($this->orderNumber)
            ->openFulfillmentPopup()
            ->setQuantities([$warehouse => [$product => $quantity]])
            ->submit();
    }

    private function _addItemRefundWithItemQuantity(int $quantity, string $product = self::PRODUCT, string $warehouse = self::WAREHOUSE)
    {
        $this->Pages->DealerOrderIndex->navigateTo()
            ->openPurchaseOrderEditPage($this->orderNumber)
            ->openRefundDealerPopup()
            ->setQuantities([$warehouse => [$product => $quantity]])
            ->submit();
    }

    private function _getTransferItemOrderedCount(int $transferId = self::TRANSFER_ID, string $product = self::PRODUCT): int
    {
        $table = $this->Pages->InventoryTransferView->navigateTo($transferId)->getTable();

        return (int)$table[array_search($product, array_column($table, 'product'), true)]['ordered'];
    }
}
