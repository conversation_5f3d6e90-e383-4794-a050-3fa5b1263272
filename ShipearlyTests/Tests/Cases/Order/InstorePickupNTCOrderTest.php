<?php
namespace ShipearlyTests\Cases\Order;

use ShipearlyTests\Cases\ShipearlyTestCase;
use ShipearlyTests\Initializers\BrandInitializer;
use ShipearlyTests\Initializers\OrderInitializer;
use ShipearlyTests\Initializers\RetailerInitializer;
use ShipearlyTests\Objects\CardDetails;
use ShipearlyTests\Objects\ShippingMethods;

class InstorePickupNTCOrderTest extends ShipearlyTestCase
{
    protected function tearDownDatabase() {}

    /**
     * @var string
     */
    protected $shippingMethod = ShippingMethods::InstorePickup;

    /**
     * @var string
     */
    protected $inStockOrderStatus = 'Not Picked Up';

    /**
     * @var string
     */
    protected $orderNumber;

    /**
     * @var string
     */
    protected $pickupCode;

    protected function setUp()
    {
        parent::setUp();
        $this->orderNumber = $this->Shopify->createNewOrder(['products' => ['No Stock'], 'shippingMethod' => $this->shippingMethod]);
        $this->pickupCode = $this->Shopify->getPickupCodeFromSuccessPage();
        $this->Login->loginAsRetailer();
        $this->Pages->OrderIndex->navigateTo()->openNeedToConfirmOrder($this->orderNumber);
    }

    public function testNeedToConfirmInStockIsNotPickedUpOnRetailerOrdersTab()
    {
        $this->Pages->OrderNeedToConfirm->setAvailableQuantity(1)
                                        ->submit();

        $orderStatus = $this->Pages->OrderIndex->getOrderStatus($this->orderNumber);
        $this->assertEquals($this->inStockOrderStatus, $orderStatus);
    }

    public function testNeedToConfirmInStockIsNotPickedUpOnRetailerDashboard()
    {
        $this->Pages->OrderNeedToConfirm->setAvailableQuantity(1)
                                        ->submit();

        $orderStatus = $this->Pages->Dashboard->navigateTo()->getOrderStatus($this->orderNumber);
        $this->assertEquals($this->inStockOrderStatus, $orderStatus);
    }

    public function testNeedToConfirmInStockIsNotPickedUpOnBrandOrdersTab()
    {
        $this->Pages->OrderNeedToConfirm->setAvailableQuantity(1)
                                        ->submit();

        $this->Login->loginAsBrand();
        $orderStatus = $this->Pages->OrderIndex->navigateTo()->getOrderStatus($this->orderNumber);
        $this->assertEquals($this->inStockOrderStatus, $orderStatus);
    }

    public function testNeedToConfirmInStockIsNotOnBrandDashboard()
    {
        $this->Pages->OrderNeedToConfirm->setAvailableQuantity(1)
                                        ->submit();

        $this->Login->loginAsBrand();

        $this->assertFalse($this->Pages->Dashboard->navigateTo()->hasOrder($this->orderNumber), "Unexpected order found on Dashboard {$this->orderNumber}");
    }

    public function testNeedToConfirmInStockIsNotPickedUpOnSuperAdmin()
    {
        $this->Pages->OrderNeedToConfirm->setAvailableQuantity(1)
                                        ->submit();

        $this->SuperAdmin->adminLogin();
        $this->SuperAdmin->openOrder($this->orderNumber);
        $orderStatus = $this->SuperAdmin->getOrderStatus();
        $this->assertEquals($this->inStockOrderStatus, $orderStatus);
    }

    public function testNeedToConfirmInStockCustomerEmailHasSamePickupCodeAsSuccessPage()
    {
        $emailTo = OrderInitializer::getCheckoutDetailsObject()->checkout_email;

        $this->Pages->OrderNeedToConfirm->setAvailableQuantity(1)
                                        ->submit();

        $this->Email->sendEmails();
        $this->Email->openLastEmailTo($emailTo);
        $this->assertTrue($this->Email->hasInstorePickupCode($this->pickupCode), "Email to '{$emailTo}' did not contain the Instore Pickup Code '{$this->pickupCode}'");
    }

    public function testNeedToConfirmNoStockIsDealerOrderOnRetailerOrdersTab()
    {
        $this->Pages->OrderNeedToConfirm->setAvailableQuantity(0)
                                        ->submit();

        $orderStatus = $this->Pages->OrderIndex->getOrderStatus($this->orderNumber);
        $this->assertEquals('Dealer Order', $orderStatus);
    }

    public function testNeedToConfirmNoStockIsDealerOrderOnRetailerDashboard()
    {
        $this->Pages->OrderNeedToConfirm->setAvailableQuantity(0)
                                        ->submit();

        $orderStatus = $this->Pages->Dashboard->navigateTo()->getOrderStatus($this->orderNumber);
        $this->assertEquals('Dealer Order', $orderStatus);
    }

    public function testNeedToConfirmNoStockIsDealerOrderOnBrandOrdersTab()
    {
        $this->Pages->OrderNeedToConfirm->setAvailableQuantity(0)
                                        ->submit();

        $this->Login->loginAsBrand();
        $orderStatus = $this->Pages->OrderIndex->navigateTo()->getOrderStatus($this->orderNumber);
        $this->assertEquals('Dealer Order', $orderStatus);
    }

    public function testNeedToConfirmNoStockIsDealerOrderOnBrandDashboard()
    {
        $this->Pages->OrderNeedToConfirm->setAvailableQuantity(0)
                                        ->submit();

        $this->Login->loginAsBrand();

        $orderStatus = $this->Pages->Dashboard->navigateTo()->getOrderStatus($this->orderNumber);
        $this->assertEquals('Dealer Order', $orderStatus);
    }

    public function testNeedToConfirmNoStockIsOnRetailerDealerOrderTab()
    {
        $this->Pages->OrderNeedToConfirm->setAvailableQuantity(0)
                                        ->submit();

        $this->Pages->DealerOrderIndex->navigateTo()->searchForOrder($this->orderNumber);
        $this->assertTrue($this->Pages->DealerOrderIndex->hasOrder($this->orderNumber), "Order {$this->orderNumber} was not found.");
    }

    public function testNeedToConfirmNoStockIsOnBrandDealerOrderTab()
    {
        $this->Pages->OrderNeedToConfirm->setAvailableQuantity(0)
                                        ->submit();

        $this->Login->loginAsBrand();
        $this->Pages->DealerOrderIndex->navigateTo()->searchForOrder($this->orderNumber);
        $this->assertTrue($this->Pages->DealerOrderIndex->hasOrder($this->orderNumber), "Order {$this->orderNumber} was not found.");
    }

    public function testNeedToConfirmNoStockIsDealerOrderOnSuperAdmin()
    {
        $this->Pages->OrderNeedToConfirm->setAvailableQuantity(0)
                                        ->submit();

        $this->SuperAdmin->adminLogin();
        $this->SuperAdmin->openOrder($this->orderNumber);
        $orderStatus = $this->SuperAdmin->getOrderStatus();
        $this->assertEquals('Dealer Order', $orderStatus);
    }

    public function testNeedToConfirmNoStockEmailHasDealerOrderSummary()
    {
        $this->Pages->OrderNeedToConfirm->setAvailableQuantity(0)
                                        ->submit();

        $this->Email->sendEmails();
        $this->Email->openLastEmailTo(RetailerInitializer::getRetailerLoginTestObject1()->username);
        $expectedDealerOrderSummary = array(
            array(
                'Part #' => 'NO-STOCK',
                'Product Name' => 'No Stock',
                'UPC' => '0101010101010',
                'Quantity' => 1
            )
        );
        $this->assertEquals($expectedDealerOrderSummary, $this->Email->getDealerOrderSummary());
    }

    /**
     * @dataProvider providerNeedToConfirmEmails
     */
    public function testNeedToConfirmSendsNewEmail($emailTo, $stock)
    {
        $this->Pages->OrderNeedToConfirm->setAvailableQuantity($stock)
                                        ->submit();

        $this->Email->sendEmails();
        $this->assertTrue($this->Email->hasNewEmail($emailTo), "Retailer email to '{$emailTo}' is not new");
    }

    public function providerNeedToConfirmEmails()
    {
        return array(
            'In-Stock RetailerEmail' => array(RetailerInitializer::getRetailerLoginTestObject1()->username, 1),
            'In-Stock CustomerEmail' => array(OrderInitializer::getCheckoutDetailsObject()->checkout_email, 1),
            'No Stock RetailerEmail' => array(RetailerInitializer::getRetailerLoginTestObject1()->username, 0),
            'No Stock BrandEmail'    => array(BrandInitializer::getBrandLoginObject()->username, 0),
        );
    }

    public function testPickupShipToStoreInStock()
    {
        $this->Pages->OrderNeedToConfirm->setAvailableQuantity(1)
                                        ->submit();

        $this->Login->loginAsRetailer();
        $this->Pages->OrderIndex->navigateTo()
                                ->openOrderInvoice($this->orderNumber)
                                ->openVerificationCodePopup()
                                ->enterCode($this->pickupCode)
                                ->submit();

        $orderStatus = $this->Pages->OrderIndex->navigateTo()->getOrderStatus($this->orderNumber);
        $this->assertEquals('Delivered', $orderStatus);
    }

    public function testMarkShipToStoreInStockAsDelivered()
    {
        $this->Pages->OrderNeedToConfirm->setAvailableQuantity(1)
            ->submit();

        $this->Login->loginAsBrand();

        $this->Pages->OrderIndex->navigateTo()
            ->openOrderInvoice($this->orderNumber)
            ->markOrderAsDelivered();

        $orderStatus = $this->Pages->OrderIndex->navigateTo()->getOrderStatus($this->orderNumber);
        $this->assertEquals('Delivered', $orderStatus);
    }

    public function testNeedToConfirmStripePayment()
    {
        $this->Pages->OrderNeedToConfirm->setResultQuantity(5)
                                        ->submitWithNegativeBalance();

        $this->assertEquals('Dealer Order', $this->Pages->OrderIndex->getOrderStatus($this->orderNumber));
    }

    /**
     * @dataProvider providerFailingStripeCards
     */
    public function testNeedToConfirmStripeFailure($cardNumber, $errorMessage)
    {
        $cardPart = explode(' ', $cardNumber, 4);
        $failingCard = new CardDetails();
        $failingCard->card_number_part1 = $cardPart[0];
        $failingCard->card_number_part2 = $cardPart[1];
        $failingCard->card_number_part3 = $cardPart[2];
        $failingCard->card_number_part4 = $cardPart[3];
        $failingCard->cc_exp_month = '12';
        $failingCard->cc_exp_year = '73';
        $failingCard->cc_csc = '123';

        $this->Pages->OrderNeedToConfirm->setResultQuantity(5)
                                        ->submitWithNegativeBalance($failingCard);

        $this->assertEquals('Need To Confirm', $this->Pages->OrderIndex->getOrderStatus($this->orderNumber));
        $this->assertContains($errorMessage, $this->Pages->FlashMessage->getError());
    }
    public function providerFailingStripeCards() {
        return array(
            'card_declined' => array('4000 0000 0000 0341', 'Your card was declined.'),
            'fraudulent' => array('4100 0000 0000 0019', 'Your card was declined.'),
        );
    }

    public function testNeedToConfirmStripeDealerOrderShipment()
    {
        $this->Pages->OrderNeedToConfirm->setResultQuantity(5)
                                        ->submitWithNegativeBalance();

        $this->assertEquals('Dealer Order', $this->Pages->OrderIndex->getOrderStatus($this->orderNumber));

        $this->Login->loginAsBrand();
        $this->Pages->DealerOrderIndex->navigateTo()
                                      ->openOrderEditPage($this->orderNumber)
                                      ->confirmPricing();
        $this->Pages->DealerOrderIndex->openOrderEditPage($this->orderNumber)
                                      ->shipWithoutTracking();

        $this->assertEquals($this->inStockOrderStatus, $this->Pages->OrderIndex->navigateTo()->getOrderStatus($this->orderNumber));
    }
}
