<?php

namespace ShipearlyTests\Cases\Products;

use ShipearlyTests\Cases\ShipearlyTestCase;
use ShipearlyTests\Initializers\BrandInitializer;
use ShipearlyTests\Objects\Login;

class ProductB2bMinMaxTest extends ShipearlyTestCase
{

    public function setUp()
    {
        parent::setUp();
        $this->Login->loginAs(BrandInitializer::getBrandLoginObject());
    }

    /**
     * @dataProvider providerTestEditContactPerson
     */
    public function testSetMinAndMax(string $productName, string $min, string $max, array $expected)
    {
        $this->Pages->ProductIndex
            ->navigateTo()
            ->clickEditProduct($productName);

        $this->Pages->ProductIndex->setB2bMinQuantity($min);
        $this->Pages->ProductIndex->setB2bMaxQuantity($max);
        $this->Pages->ProductIndex->saveProductEdit();

        $productIndex = $this->Pages->ProductIndex->clickEditProduct($productName);
        $actual = [
            'min' => $productIndex->getB2bMinQuantity(),
            'max' => $productIndex->getB2bMaxQuantity(),
        ];

        $this->assertEquals($expected, $actual);
    }

    public function providerTestEditContactPerson(): array
    {
        return [
            'Min less than max' => [
                'productName' => 'Bicycle',
                'min' => '3',
                'max' => '5',
                'expected' => [
                    'min' => '3',
                    'max' => '5',
                ],
            ],
            'Min Empty' => [
                'productName' => 'Bicycle',
                'min' => '',
                'max' => '5',
                'expected' => [
                    'min' => '',
                    'max' => '5',
                ],
            ],
            'Max Empty' => [
                'productName' => 'Bicycle',
                'min' => '3',
                'max' => '',
                'expected' => [
                    'min' => '3',
                    'max' => '',
                ],
            ],
            'Both Empty' => [
                'productName' => 'Bicycle',
                'min' => '',
                'max' => '',
                'expected' => [
                    'min' => '',
                    'max' => '',
                ],
            ],
        ];
    }

    public function testSetMaxLessThanMin()
    {
        $productName = 'Bicycle';
        $min = '7';
        $max = '5';

        $this->Pages->ProductIndex
            ->navigateTo()
            ->clickEditProduct($productName);

        $this->Pages->ProductIndex->setB2bMinQuantity($min);
        $this->Pages->ProductIndex->setB2bMaxQuantity($max);
        $this->Pages->ProductIndex->clickProductEditSubmit();

        $actual = [
            'minError' => (bool)preg_match('/\berror\b/', $this->Pages->ProductIndex->getB2bMinOrderQuantityInput()->getAttribute('class')),
            'maxError' => (bool)preg_match('/\berror\b/', $this->Pages->ProductIndex->getB2bMaxOrderQuantityInput()->getAttribute('class')),
        ];
        $expected = [
            'minError' => true,
            'maxError' => true,
        ];
        $this->assertEquals($expected, $actual);
    }
}
