<?php
namespace ShipearlyTests\Cases\Wholesale;

use ShipearlyTests\Cases\ShipearlyTestCase;

class CreateB2bCartAsRetailerTest extends ShipearlyTestCase
{
    protected function setUp()
    {
        parent::setUp();

        $this->Login->loginAsRetailer();
    }

    public function testAddToCartFromB2bCatalogue()
    {
        $this->Pages->DealerOrderIndex
            ->navigateTo()
            ->createPurchaseOrder()
            ->setBrand()
            ->setLocation()
            ->selectOrderType();

        $this->assertRegExp('#/catalogue/15\?.*user_id=12#', $this->Pages->B2bCatalogue->getCurrentURL(), 'currently on catalogue');

        $this->Pages->B2bCatalogue
            ->clickDisplayList()
            ->expandProduct('Bicycle')->setQuantity(3, 'Bicycle')->addToCart('Bicycle')
            ->expandProduct('No Stock')->setQuantity(3, 'No Stock')->addToCart('No Stock');

        $this->assertEquals(6, $this->Pages->AppLayout->getB2BCartCount(), 'cart count');

        $this->Pages->B2bCartIndex->navigateTo();
        $this->assertContains('Sirisha Test Brand Regular Order', $this->Pages->B2bCartView->getPageTitle(), 'page title');
        $this->assertEquals('Sirisha Test Retailer', $this->Pages->B2bCartView->getShipToLocationName(), 'ship to location');
    }

    public function testAddToBranchCartWithDiscount()
    {
        $this->Pages->DealerOrderIndex
            ->navigateTo()
            ->createPurchaseOrder()
            ->setBrand()
            ->setLocation('Sirisha Test Branch')
            ->selectOrderType('Booking (B2BFORDAYS)');

        $this->assertRegExp('#/catalogue/27\?.*user_id=12#', $this->Pages->B2bCatalogue->getCurrentURL(), 'currently on catalogue');

        $this->Pages->B2bCatalogue
            ->clickDisplayList()
            ->expandProduct('Bicycle')->setQuantity(3, 'Bicycle')->addToCart('Bicycle')
            ->expandProduct('No Stock')->setQuantity(3, 'No Stock')->addToCart('No Stock');

        $this->assertEquals(6, $this->Pages->AppLayout->getB2BCartCount(), 'cart count');

        $this->Pages->B2bCartIndex->navigateTo();
        $this->assertContains('Sirisha Test Brand Booking Order', $this->Pages->B2bCartView->getPageTitle(), 'page title');
        $this->assertEquals('Sirisha Test Branch', $this->Pages->B2bCartView->getShipToLocationName(), 'ship to location');
    }

    public function testAddToCartFromProductView()
    {
        $this->Pages->Dashboard
            ->navigateTo()
            ->createPurchaseOrder()
            ->setBrand()
            ->setLocation()
            ->selectOrderType();
        $this->Pages->B2bCatalogue
            ->clickDisplayGrid()
            ->searchForProduct()
            ->openGridProduct()
            ->setQuantity()
            ->addToCart();

        $this->assertRegExp('#/catalogue/15\?.*user_id=12#', $this->Pages->B2bCatalogue->getCurrentURL(), 'currently on catalogue');

        $successAttributes = $this->Pages->ProductView->getSuccessMessageAttributes();
        $this->assertEquals('Product added to draft order. View Cart', $successAttributes['text']);
        $this->assertRegExp('#/draft_orders/[0-9]+#', $successAttributes['link_href']);

        $this->assertEquals(1, $this->Pages->AppLayout->getB2BCartCount(), 'cart count');

        $this->Pages->ProductView->clickSuccessMessageLink();
        $this->assertContains('Sirisha Test Brand Regular Order', $this->Pages->B2bCartView->getPageTitle(), 'page title');
        $this->assertEquals('Sirisha Test Retailer', $this->Pages->B2bCartView->getShipToLocationName(), 'ship to location');
    }

    public function testAddToBranchCartWithDiscountFromProductView()
    {
        $this->Pages->Dashboard
            ->navigateTo()
            ->createPurchaseOrder()
            ->setBrand()
            ->setLocation('Sirisha Test Branch')
            ->selectOrderType('Booking (B2BFORDAYS)');
        $this->Pages->B2bCatalogue
            ->clickDisplayGrid()
            ->searchForProduct()
            ->openGridProduct()
            ->setQuantity()
            ->addToCart();

        $this->assertRegExp('#/catalogue/27\?.*user_id=12#', $this->Pages->B2bCatalogue->getCurrentURL(), 'currently on catalogue');

        $successAttributes = $this->Pages->ProductView->getSuccessMessageAttributes();
        $this->assertEquals('Product added to draft order. View Cart', $successAttributes['text']);
        $this->assertRegExp('#/draft_orders/[0-9]+#', $successAttributes['link_href']);

        $this->assertEquals(1, $this->Pages->AppLayout->getB2BCartCount(), 'cart count');

        $this->Pages->ProductView->clickSuccessMessageLink();
        $this->assertContains('Sirisha Test Brand Booking Order', $this->Pages->B2bCartView->getPageTitle(), 'page title');
        $this->assertEquals('Sirisha Test Branch', $this->Pages->B2bCartView->getShipToLocationName(), 'ship to location');
    }
}
