<?php
namespace ShipearlyTests\Helpers;

use Facebook\WebDriver\Remote\RemoteWebElement;
use Facebook\WebDriver\WebDriverBy;

class ForgotPasswordHelper extends BaseHelper
{

    protected function basePath()
    {
        return rtrim(parent::basePath(), '/') . '/forgot_password';
    }

    public function submitEmail($email)
    {
        $this->setTextInput(WebDriverBy::id('email'), $email);
        $this->webDriver->findElement(WebDriverBy::cssSelector("#forgot_password button"))->click();
    }

    public function getSuccessMessage()
    {
        return $this->webDriver->findElement(WebDriverBy::className('alert-success'))->getText();
    }

    public function getFailureMessage()
    {
        return $this->webDriver->findElement(WebDriverBy::className('alert-danger'))->getText();
    }

    public function getHtml5FieldErrors()
    {
        return array_reduce(
            $this->webDriver->findElements(WebDriverBy::cssSelector('input:invalid')),
            function(array $map, RemoteWebElement $element): array {
                return $map + [$element->getAttribute('id') => $element->getAttribute('validationMessage')];
            },
            []
        );
    }

    public function submitResetPasswordForm($newPassword = 'Test@123', $confirmNewPassword = null)
    {
        $confirmNewPassword = !is_null($confirmNewPassword) ? $confirmNewPassword : $newPassword;

        $this->setTextInput(WebDriverBy::id('new_password'), $newPassword);
        $this->setTextInput(WebDriverBy::id('confirm_new_password'), $confirmNewPassword);
        $this->webDriver->findElement(WebDriverBy::id('resetsubmit'))->click();
    }
}
