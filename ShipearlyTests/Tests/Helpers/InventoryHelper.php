<?php
namespace ShipearlyTests\Helpers;

use Facebook\WebDriver\Remote\RemoteWebDriver;
use Facebook\WebDriver\WebDriverBy;
use ShipearlyTests\Objects\Login;

class InventoryHelper
{
    public function inventoryLogin(RemoteWebDriver $webDriver, $url, Login $inventoryLogin)
    {
        $webDriver->get($url);

        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='login']/div[2]/form/dl/dd[1]/input"))->click();
        $webDriver->getKeyboard()->sendKeys($inventoryLogin->username);

        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='login']/div[2]/form/dl/dd[2]/input"))->click();
        $webDriver->getKeyboard()->sendKeys($inventoryLogin->password);

        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='submitButton']"))->click();
        $webDriver->manage()->window()->maximize();
    }


    public function inventoryLogout(RemoteWebDriver $webDriver)
    {
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='logoutSystem']"))->click();
    }

    public function getProductXpath(RemoteWebDriver $webDriver, $product)
    {
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='drawerMenuInventory']"))->click();
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='itemSearchButton']"))->click();
        $total = count($webDriver->findElements(WebDriverBy::xpath(".//*[@id='listing_loc_matches']/div/table/tbody/tr")));
        for($i=1;$i<=$total;$i++)
        {
            $xpath = ".//*[@id='listing_loc_matches']/div/table/tbody/tr[" . $i . "]/td[2]/a";
            $item = $webDriver->findElement(WebDriverBy::xpath($xpath))->getText();
            if($item == $product)
            {
                $xpathFinal = ".//*[@id='listing_loc_matches']/div/table/tbody/tr[" . $i . "]";
                break;
            }
        }
        return $xpathFinal;
    }

    public function checkProductInventory(RemoteWebDriver $webDriver, $product)
    {
        $xpath = $this->getProductXpath($webDriver, $product);
        $xpath = $xpath . "/td[3]";
        $text = $webDriver->findElement(WebDriverBy::xpath($xpath))->getText();
        print ($text);
        return $text;
    }

}
