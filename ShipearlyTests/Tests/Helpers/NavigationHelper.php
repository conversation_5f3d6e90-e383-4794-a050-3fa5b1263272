<?php
namespace ShipearlyTests\Helpers;

use Facebook\WebDriver\Exception\NoSuchElementException;
use Facebook\WebDriver\Remote\RemoteWebDriver;
use Facebook\WebDriver\Remote\RemoteWebElement;
use Facebook\WebDriver\WebDriverBy;

class NavigationHelper extends BaseHelper
{
    private WebDriverBy $bySidebar;
    private WebDriverBy $bySidebarNavLinks;
    private WebDriverBy $byNoSidebarExitButton;

    protected function selectorConfig()
    {
        $this->bySidebar = WebDriverBy::id('sidebar');
        $this->bySidebarNavLinks = WebDriverBy::cssSelector('#sidebar > ul.sidebar-tabs > li > a');
        $this->byNoSidebarExitButton = WebDriverBy::xpath('//*[@id="content"]//div[contains(@class, "top-bar-wrapper")]//a[text()[normalize-space(.) = "Exit"]]');
    }

    public function hasSidebar(): bool
    {
        try {
            return $this->_doWithNoImplicitWait(fn() => $this->webDriver->findElement($this->bySidebar)->isDisplayed());
        } catch (NoSuchElementException $e) {
            return false;
        }
    }

    public function clickNoSidebarExitButton(): self
    {
        $this->webDriver->findElement($this->byNoSidebarExitButton)->click();

        return $this;
    }

    /**
     * @return string[]
     */
    public function listTabNames(): array
    {
        return array_map(
            fn(RemoteWebElement $tab): string => $tab->getText(),
            $this->webDriver->findElements($this->bySidebarNavLinks)
        );
    }

    public function clickTab(string $tabName): self
    {
        $this->getTabByName($tabName)->click();

        return $this;
    }

    public function isPopupTab(string $tabName): bool
    {
        return ($this->getTabByName($tabName)->getAttribute('href') === 'javascript:void();');
    }

    protected function getTabByName(string $tabName): RemoteWebElement
    {
        return $this->webDriver->findElement($this->bySidebar)
            ->findElement(WebDriverBy::linkText($tabName));
    }

    public function getActiveTabName()
    {
        return $this->webDriver->findElement(WebDriverBy::cssSelector('#sidebar a.on'))->getText();
    }

    public function getPageHeader()
    {
        return $this->webDriver->findElement(WebDriverBy::cssSelector('#content h1'))->getText();
    }

    public function getPageUrlPath()
    {
        return str_replace(BASE_PATH, '', $this->webDriver->getCurrentURL());
    }

    /**
     * @deprecated
     */
    public function collapseTabs(RemoteWebDriver $webDriver)
    {
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='header']/ul/li[1]/a"))->click();
        $val = $webDriver->findElement(WebDriverBy::xpath(".//*[@id='sidebar']/ul/li[1]/a/p"))->getAttribute("style");
        return $val;
    }

    /**
     * @deprecated
     */
    public function clickOnBrandName(RemoteWebDriver $webDriver)
    {
        $text = $webDriver->findElement(WebDriverBy::xpath(".//*[@id='header']/div[3]/div"))->getAttribute("class");
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='header']/div[3]/div"))->click();
        $text1 = $webDriver->findElement(WebDriverBy::xpath(".//*[@id='header']/div[3]/div"))->getAttribute("class");
        return $text . "," . $text1;
    }

    /**
     * @deprecated
     */
    public function settingsMyAccount(RemoteWebDriver $webDriver)
    {
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='header']/div[3]/div"))->click();
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='header']/div[3]/div/div/ul/li[2]/a"))->click();
        $text = $webDriver->findElement(WebDriverBy::xpath(".//*[@id='content']/div[2]/h1"))->getText();
        return $text;
    }

    /**
     * @deprecated
     */
    public function settingsProductCategories(RemoteWebDriver $webDriver)
    {
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='header']/div[3]/div"))->click();
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='header']/div[3]/div/div/ul/li[3]/a"))->click();
        $text = $webDriver->findElement(WebDriverBy::xpath(".//*[@id='content']/div[3]/h1"))->getText();
        return $text;
    }

    /**
     * @deprecated
     */
    public function settingsEcommerceSettings(RemoteWebDriver $webDriver)
    {
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='header']/div[3]/div"))->click();
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='header']/div[3]/div/div/ul/li[4]/a"))->click();
        $text = $webDriver->findElement(WebDriverBy::xpath(".//*[@id='content']/div[2]/h1"))->getText();
        return $text;
    }

    /**
     * @deprecated
     */
    public function settingsSubscription(RemoteWebDriver $webDriver)
    {
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='header']/div[3]/div"))->click();
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='header']/div[3]/div/div/ul/li[6]/a"))->click();
        $text = $webDriver->findElement(WebDriverBy::xpath(".//*[@id='content']/div[2]/h1"))->getText();
        return $text;
    }

    /**
     * @deprecated
     */
    public function settingsChangePassword(RemoteWebDriver $webDriver)
    {
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='header']/div[3]/div"))->click();
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='header']/div[3]/div/div/ul/li[7]/a"))->click();
        $text = $webDriver->findElement(WebDriverBy::xpath(".//*[@id='content']/div[2]/h1"))->getText();
        return $text;
    }

    /**
     * @deprecated
     */
    public function userLogout(RemoteWebDriver $webDriver)
    {
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='header']/div[3]/div"))->click();
        $webDriver->findElement(WebDriverBy::xpath(".//*[@id='header']/div[3]/div/div/ul/li[8]/a"))->click();
        $text = $webDriver->findElement(WebDriverBy::xpath(".//*[@id='login']/h2"))->getText();
        return $text;
    }
}
