<?php
namespace ShipearlyTests\Objects;

/**
 * Class EmailTemplateName contains constants for all email template names defined in the super admin.
 */
class EmailTemplateName
{
    const ForgotPassword = 'Forgot Password';
    const ActivateAccount = 'Activate Account';
    const InstoreCode = 'Instore Code';
    const InstoreRetailer = 'Instore Retailer';
    const InstoreManufacturer = 'Instore Manufacturer';
    const InviteEmail = 'Invite Email';
    const Registration = 'Registration';
    const DealerOrder = 'Dealer Order';
    const ShipfromstoreRetailer = 'Shipfromstore Retailer';
    const ShipfromstoreRetailerNonStocking = 'Non-Stocking Ship from Store';
    const ProductInquiry = 'Product Inquiry';
    const ShipfromstoreCustomer = 'Shipfromstore Customer';
    const RetailerRequest = 'Retailer Request';
    const RetailerApproval = 'Retailer Approval';
    const RetailerOrderInvoice = 'Retailer Order Invoice';
    const NonStockCustomer = 'NonStock Customer';
    const NonStockInstoreCode = 'Nonstock Instore Code';
    const StoreRegistration = 'Store Registration';
    const ActivateStore = 'Activate Store';
    const SecretCodeReset = 'Secret Code Reset';
    const NonStockOrderNotification = 'Non stock order notification';
    const ShipFromStoreOrderNotification = 'Ship from store order notification';
    const OrderCancellationRetailerNotification = 'Order cancellation retailer notification';
    const OrderCancellationBrandNotification = 'Order cancellation brand notification';
    const OrderCancellationCustomerNotification = 'Order cancellation customer notification';
    const OrderCancellationSuperAdminNotification = 'Order cancellation super admin notification';
    const OrderConfirmationNotification = 'Order confirmation notification';
    const OrderConfirmationCustomerNotification = 'Order confirmation customer notification';
    const ShipToStoreRetailer = 'Ship to Store Retailer';
    const AbandonCartCustomerNotification = 'Abandon cart customer notification';
    const OrderAcceptanceRetailer = 'Order Acceptance retailer';
    const RegistrationMail = 'Registration Mail';
    const OrderRefund = 'Order Refund';
    const DealerOrderRetailerNotifications = 'Dealer Order Retailer Notifications';
    const DealerOrderCustomerNotifications = 'Dealer Order Customer Notifications';
    const DealerOrderCustomerNotificationsWithoutTrack = 'Dealer Order Customer Notifications Without Track';
    const _24HrDealerOrderNotification = '24 Hr Dealer Order Notification';
    const OrderEdit = 'Order Edit';
    const LocalDeliveryInstoreCode = 'Local Delivery, Instore Code';
    const LocalDeliveryInstoreRetailer = 'Local Delivery, Instore Retailer';
    const LocalDeliveryNonStockInstoreCode = 'Local Delivery, Nonstock Instore Code';
    const LocalDeliveryShipToStoreRetailer = 'Local Delivery, Ship to Store Retailer';
    const LocalDeliveryNonStockCustomer = 'Local Delivery, NonStock Customer';
    const LocalDeliveryDealerOrderCustomerNotifications = 'Local Delivery, Dealer Order Customer Notifications';
    const LocalDeliveryDealerOrderCustomerNotificationsWithoutTrack = 'Local Delivery, Dealer Order Customer Notifications Without Track';
    const LocalDeliverySecretCodeReset = 'Local Delivery, Secret Code Reset';
    const InStorePickupWithScheduling = 'In-Store Pickup with Scheduling';
    const LocalDeliveryWithScheduling = 'Local Delivery with Scheduling';
    const ActivateStoreAssociate = 'Activate StoreAssociate';
    const ShipfromstoreInStockRetailer = 'Shipfromstore InStock Retailer';
    const ShipfromstoreTrackingCustomer = 'Shipfromstore Tracking Customer';
    const ShipfromstoreDealerOrderRetailer = 'Shipfromstore Dealer Order Retailer';
    const CommissionOrderRetailer = 'Commission Order Retailer';
    const PurchaseOrder = 'Purchase Order';
    const PurchaseOrderCancellation = 'Purchase Order Cancellation';
    const ShipfromstoreInStockOnlyRetailer = 'Shipfromstore In-Stock Only Retailer';
    const ShipToStoreSalesRep = 'Ship to Store Sales Rep';
    const ActivateSalesRep = 'Activate Sales Rep';
    const SalesRepB2bInitiation = 'Sales Rep B2B Initiation';
    const BrandRefund = 'Brand Refund';
}
