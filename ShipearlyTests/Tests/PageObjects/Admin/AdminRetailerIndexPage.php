<?php
namespace ShipearlyTests\PageObjects\Admin;

use ShipearlyTests\PageObjects\Admin\UserIndexRow\AdminRetailerIndexRow;
use ShipearlyTests\PageObjects\Admin\UserIndexRow\AdminUserIndexRow;

/**
 * Class AdminRetailerIndexPage.
 *
 * @package ShipearlyTests\PageObjects\Admin
 */
class AdminRetailerIndexPage extends AdminUserIndexPage
{
    protected $userType = 'Retailer';

    /**
     * @param string $searchTerm
     * @return AdminRetailerIndexRow
     */
    public function getUserRow(string $searchTerm): AdminUserIndexRow
    {
        parent::getUserRow($searchTerm);
        return AdminRetailerIndexRow::instance();
    }
}
