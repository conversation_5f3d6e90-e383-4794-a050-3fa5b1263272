<?php
namespace ShipearlyTests\PageObjects\Admin;

use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverExpectedCondition;
use ShipearlyTests\Initializers\SuperAdminInitializer;
use ShipearlyTests\Objects\Login;
use ShipearlyTests\PageObjects\Dashboard\DashboardPage;
use ShipearlyTests\PageObjects\PageObject;

/**
 * Class AdminUserContactPage.
 *
 * @package ShipearlyTests\PageObjects\Admin
 */
class AdminUserContactPage extends PageObject
{
    /** @var WebDriverBy */
    private $byLoginAsButton;
    /** @var WebDriverBy */
    private $byLoginAsUsernameInput;
    /** @var WebDriverBy */
    private $byLoginAsPasswordInput;
    /** @var WebDriverBy */
    private $byLoginAsSubmitButton;
    /** @var WebDriverBy */
    private $bySuspendButton;
    /** @var WebDriverBy */
    private $byDeleteButton;
    /** @var WebDriverBy */
    private $byCompanyPhoneNumber;
    /** @var WebDriverBy */
    private $byContactPhoneNumber;

    protected function selectorConfig()
    {
        $this->byLoginAsButton = WebDriverBy::cssSelector('.form_title button[data-login-as-url]');
        //TODO extract page object
        $this->byLoginAsUsernameInput = WebDriverBy::id('AdministratorUsername');
        $this->byLoginAsPasswordInput = WebDriverBy::id('AdministratorPassword');
        $this->byLoginAsSubmitButton = WebDriverBy::cssSelector('.modal-footer > button.btn-primary');

        $this->bySuspendButton = WebDriverBy::linkText('Suspend User');
        $this->byDeleteButton = WebDriverBy::xpath('//div[@class="form_title"]//button[text()="Delete"]');

        $this->byCompanyPhoneNumber = WebDriverBy::cssSelector('.profilecon .cont-addr .cont-phn');
        $this->byContactPhoneNumber = WebDriverBy::cssSelector('.personInfo .ComDetailBlock .cont-phn');
    }

    public function navigateTo($userId = '1')
    {
        parent::navigateTo($userId);
        return $this;
    }

    protected function basePath()
    {
        return rtrim(parent::basePath(), '/') . '/admin/contact/';
    }

    public function loginAs(Login $adminlogin = null)
    {
        $adminlogin = $adminlogin ?? SuperAdminInitializer::getAdminLoginObject();

        $this->webDriver->findElement($this->byLoginAsButton)->click();

        $loginAsUsername = $this->waitUntil(WebDriverExpectedCondition::elementToBeClickable($this->byLoginAsUsernameInput));
        $this->setTextInput($loginAsUsername, $adminlogin->username);
        $this->setTextInput($this->byLoginAsPasswordInput, $adminlogin->password);

        $this->webDriver->findElement($this->byLoginAsSubmitButton)->click();

        //TODO
        return DashboardPage::instance();
    }

    public function suspend()
    {
        $this->webDriver->findElement($this->bySuspendButton)->click();

        $dialogConfirmButton = $this->waitUntil(WebDriverExpectedCondition::elementToBeClickable(WebDriverBy::cssSelector('.bootbox-confirm .btn-primary')))
             ->click();
        $this->waitUntil(WebDriverExpectedCondition::stalenessOf($dialogConfirmButton), __METHOD__);

        return $this;
    }

    public function delete()
    {
        $this->webDriver->findElement($this->byDeleteButton)->click();

        $dialogConfirmButton = $this->waitUntil(WebDriverExpectedCondition::elementToBeClickable(WebDriverBy::cssSelector('.bootbox-confirm .btn-primary')))
             ->click();
        $this->waitUntil(WebDriverExpectedCondition::stalenessOf($dialogConfirmButton), __METHOD__);

        return AdminUserIndexPage::instance();
    }

    public function getCompanyPhoneNumber()
    {
        return $this->webDriver->findElement($this->byCompanyPhoneNumber)->getText();
    }

    public function getContactPhoneNumber()
    {
        return $this->webDriver->findElement($this->byContactPhoneNumber)->getText();
    }
}
