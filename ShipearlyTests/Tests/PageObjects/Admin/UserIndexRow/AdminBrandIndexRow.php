<?php
namespace ShipearlyTests\PageObjects\Admin\UserIndexRow;

use Facebook\WebDriver\WebDriverBy;
use ShipearlyTests\Helpers\SuperAdminHelper;
use ShipearlyTests\PageObjects\Admin\AdminBrandIndexPage;

/**
 * Class AdminBrandIndexRow.
 *
 * @package ShipearlyTests\PageObjects\Admin
 */
class AdminBrandIndexRow extends AdminUserIndexRow
{
    /** @var WebDriverBy */
    private $byUserRow_retailerCount;
    /** @var WebDriverBy */
    private $byUserRow_salesRepCount;
    /** @var WebDriverBy */
    private $byUserRow_ecommerceType;
    /** @var WebDriverBy */
    private $byUserRow_sellDirect;

    protected function selectorConfig()
    {
        parent::selectorConfig();
        $this->byUserRow_retailerCount = WebDriverBy::cssSelector('td[aria-describedby="jqGrid01_Retailer Count"]');
        $this->byUserRow_salesRepCount = WebDriverBy::cssSelector('td[aria-describedby="jqGrid01_Sales Rep Count"]');
        $this->byUserRow_ecommerceType = WebDriverBy::cssSelector('td[aria-describedby="jqGrid01_eCommerce Software"]');
        $this->byUserRow_sellDirect = WebDriverBy::cssSelector('td[aria-describedby="jqGrid01_Sell Direct"]');
    }

    public function approveUser()
    {
        parent::approveUser();

        //TODO Create a page object to represent this popup
        SuperAdminHelper::instance()->setBrandPercentage();

        return AdminBrandIndexPage::instance();
    }

    public function getRetailerCount(): string
    {
        return $this->findRow()->findElement($this->byUserRow_retailerCount)->getText();
    }

    public function getSalesRepCount(): string
    {
        return $this->findRow()->findElement($this->byUserRow_salesRepCount)->getText();
    }

    public function getEcommerceType(): string
    {
        return $this->findRow()->findElement($this->byUserRow_ecommerceType)->getText();
    }
}
