<?php
namespace ShipearlyTests\PageObjects\Admin\UserIndexRow;

use Facebook\WebDriver\WebDriverBy;

/**
 * Class AdminRetailerIndexRow.
 *
 * @package ShipearlyTests\PageObjects\Admin
 */
class AdminRetailerIndexRow extends AdminUserIndexRow
{
    /** @var WebDriverBy */
    private $byUserRow_fulfillments;
    /** @var WebDriverBy */
    private $byUserRow_userType;
    /** @var WebDriverBy */
    private $byUserRow_inventoryType;
    /** @var WebDriverBy */
    private $byUserRow_status;

    protected function selectorConfig()
    {
        parent::selectorConfig();
        $this->byUserRow_fulfillments = WebDriverBy::cssSelector('td[aria-describedby="jqGrid01_# of Orders | Fulfillment"]');
        $this->byUserRow_userType = WebDriverBy::cssSelector('td[aria-describedby="jqGrid01_Type"]');
        $this->byUserRow_inventoryType = WebDriverBy::cssSelector('td[aria-describedby="jqGrid01_Inventory Software"]');
        $this->byUserRow_status = WebDriverBy::cssSelector('td[aria-describedby="jqGrid01_Status"]');
    }

    public function getFulfillments(): string
    {
        return $this->findRow()->findElement($this->byUserRow_fulfillments)->getText();
    }

    public function getUserType(): string
    {
        return $this->findRow()->findElement($this->byUserRow_userType)->getText();
    }

    public function getInventoryType(): string
    {
        return $this->findRow()->findElement($this->byUserRow_inventoryType)->getText();
    }

    public function getStatus(): string
    {
        return $this->findRow()->findElement($this->byUserRow_status)->getText();
    }
}
