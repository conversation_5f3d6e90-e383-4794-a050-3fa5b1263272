<?php
namespace ShipearlyTests\PageObjects\Admin\UserIndexRow;

use Facebook\WebDriver\WebDriverBy;
use ShipearlyTests\Helpers\SuperAdminHelper;

/**
 * Class AdminStaffIndexRow.
 *
 * @package ShipearlyTests\PageObjects\Admin
 */
class AdminStaffIndexRow extends AdminUserIndexRow
{
    /** @var WebDriverBy */
    private $byUserRow_retailerLink;
    /** @var WebDriverBy */
    private $byUserRow_retailerName;
    /** @var WebDriverBy */
    private $byUserRow_ordersCount;
    /** @var WebDriverBy */
    private $byUserRow_commission;

    protected function selectorConfig()
    {
        parent::selectorConfig();
        $this->byUserRow_retailerLink = WebDriverBy::cssSelector('td[aria-describedby="jqGrid01_Company Name"] > a:nth-child(2)');
        $this->byUserRow_retailerName = WebDriverBy::cssSelector('td[aria-describedby="jqGrid01_Company Name"]');
        $this->byUserRow_ordersCount = WebDriverBy::cssSelector('td[aria-describedby="jqGrid01_# of Orders"]');
        $this->byUserRow_commission = WebDriverBy::cssSelector('td[aria-describedby="jqGrid01_Commission"]');
    }

    public function openRetailer()
    {
        $this->findRow()->findElement($this->byUserRow_retailerLink)->click();
        //TODO create AdminUserPage
        return SuperAdminHelper::instance();
    }

    public function getRetailerName(): string
    {
        return $this->findRow()->findElement($this->byUserRow_retailerName)->getText();
    }

    public function getOrdersCount(): string
    {
        return $this->findRow()->findElement($this->byUserRow_ordersCount)->getText();
    }

    public function getCommission(): string
    {
        return $this->findRow()->findElement($this->byUserRow_commission)->getText();
    }
}
