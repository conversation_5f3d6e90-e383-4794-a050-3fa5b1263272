<?php
namespace ShipearlyTests\PageObjects\B2bCart;

use Facebook\WebDriver\Exception\NoSuchElementException;
use Facebook\WebDriver\Remote\RemoteWebElement;
use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverExpectedCondition;
use ShipearlyTests\PageObjects\PageObject;
use ShipearlyTests\PageObjects\Product\B2bCataloguePage;

/**
 * Class CreateCartPopup.
 *
 * @package ShipearlyTests\PageObjects\B2bCart
 */
class B2bCreateCartPopup extends PageObject
{
    /** @var WebDriverBy */
    private $byBrandSelectInput;
    /** @var WebDriverBy */
    private $byRetailerSelectInput;
    /** @var WebDriverBy */
    private $byLocationSelectInput;
    /** @var WebDriverBy */
    private $byOrderTypeOptions;

    protected function selectorConfig()
    {
        $this->byBrandSelectInput = WebDriverBy::id('b2bCartBrandSelect');
        $this->byRetailerSelectInput = WebDriverBy::id('b2bCartRetailerSelect');
        $this->byLocationSelectInput = WebDriverBy::id('b2bCartLocationSelect');
        $this->byOrderTypeOptions = WebDriverBy::cssSelector('.js-order-type-row .h4');
    }

    public function setBrand(?string $brandName = 'Sirisha Test Brand'): self
    {
        return $this->_setUserSelectInput($brandName, $this->byBrandSelectInput);
    }

    public function setRetailer(?string $retailerName = 'Sirisha Test Retailer'): self
    {
        return $this->_setUserSelectInput($retailerName, $this->byRetailerSelectInput);
    }

    public function setLocation(?string $locationName = 'Sirisha Test Retailer'): self
    {
        return $this->_setUserSelectInput($locationName, $this->byLocationSelectInput);
    }

    private function _setUserSelectInput(?string $userName, WebDriverBy $bySelectInput): self
    {
        $byVisibleSelect = $bySelectInput;
        if ($bySelectInput->getMechanism() === 'id') {
            
            $byVisibleSelect = WebDriverBy::cssSelector(sprintf('#%s ~ .select2 .select2-selection', $byVisibleSelect->getValue()));
        }
        if ($bySelectInput->getMechanism() === 'class name') {
            $byVisibleSelect = WebDriverBy::cssSelector(sprintf('.%s ~ .select2 .select2-selection', $byVisibleSelect->getValue()));
        }

        $this->waitUntil(
            WebDriverExpectedCondition::visibilityOfElementLocated($byVisibleSelect),
            'Waiting for the create cart popup to open'
        );
        $this->setSelectInputText($bySelectInput, $userName);

        return $this;
    }

    public function selectOrderType(string $typeLabel = 'Regular'): B2bCataloguePage
    {
        $this->waitUntil(
            WebDriverExpectedCondition::visibilityOfElementLocated($this->byOrderTypeOptions),
            'Waiting for the create cart order type ajax'
        );
        $inputs = $this->webDriver->findElements($this->byOrderTypeOptions);

        /** @var RemoteWebElement $input */
        $input = array_reduce($inputs, function(?RemoteWebElement $selected, RemoteWebElement $input) use ($typeLabel) {
            return (strcasecmp($typeLabel, $input->getText()) === 0)
                ? $input
                : $selected;
        });

        if (!$input) {
            throw new NoSuchElementException(sprintf(
                'Order type option "%s" not found among: %s',
                $typeLabel,
                json_encode(array_map(
                    function(RemoteWebElement $input) {
                        return $input->getText();
                    },
                    $inputs
                ))
            ));
        }

        $input->click();
        $this->waitUntil(WebDriverExpectedCondition::stalenessOf($input), __METHOD__);

        return B2bCataloguePage::instance();
    }
}
