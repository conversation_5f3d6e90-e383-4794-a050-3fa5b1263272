<?php
namespace ShipearlyTests\PageObjects\Dashboard;

use Facebook\WebDriver\Exception\NoSuchElementException;
use Facebook\WebDriver\Interactions\WebDriverActions;
use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverExpectedCondition;
use ShipearlyTests\PageObjects\B2bCart\B2bCreateCartPopup;
use ShipearlyTests\PageObjects\PageObject;

/**
 * Class DashboardPage.
 *
 * @package ShipearlyTests\PageObjects\Dashboard
 */
class DashboardPage extends PageObject
{
    /** @var WebDriverBy */
    private $byCreatePurchaseOrder;
    /** @var WebDriverBy */
    private $byTable;
    /** @var WebDriverBy */
    private $byRow_link;
    /** @var WebDriverBy */
    private $byRow_orderStatus;
    /** @var WebDriverBy */
    private $byRow_orderCommentIcon;
    /** @var WebDriverBy */
    private $byRow_orderCustomerMessageIcon;

    protected function selectorConfig()
    {
        $this->byCreatePurchaseOrder = WebDriverBy::id('createPurchaseOrder');
        $this->byTable = WebDriverBy::cssSelector('#AjaxDashboardTable > table');
        $this->byRow_link = WebDriverBy::cssSelector('td:nth-child(1) > a');
        $this->byRow_orderStatus = WebDriverBy::cssSelector('td:nth-child(1) > a > span');
        $this->byRow_orderCommentIcon = WebDriverBy::cssSelector('td:nth-child(2) .fa-comment');
        $this->byRow_orderCustomerMessageIcon = WebDriverBy::cssSelector('td:nth-child(2) .fa-envelope');
    }

    private function byRowWithOrderNumber(string $orderNumber): WebDriverBy
    {
        return WebDriverBy::xpath("//*[@id=\"AjaxDashboardTable\"]/table/tbody/tr[./td[2]//text()[normalize-space(.)=\"{$orderNumber}\"]]");
    }

    protected function basePath()
    {
        return rtrim(parent::basePath(), '/') . '/dashboards';
    }

    public function createPurchaseOrder(): B2bCreateCartPopup
    {
        $this->webDriver->findElement($this->byCreatePurchaseOrder)->click();

        return B2bCreateCartPopup::instance();
    }

    public function hasOrder($orderNumber)
    {
        $this->waitUntil(WebDriverExpectedCondition::visibilityOfElementLocated($this->byTable));
        return $this->_doWithNoImplicitWait(function() use ($orderNumber) {
            try {
                return (bool)$this->webDriver->findElement($this->byRowWithOrderNumber($orderNumber));
            } catch (NoSuchElementException $e) {
                return false;
            }
        });
    }

    public function getOrderStatus($orderNumber)
    {
        return $this->webDriver->findElement($this->byRowWithOrderNumber($orderNumber))
                               ->findElement($this->byRow_orderStatus)
                               ->getText();
    }

    public function getOrderCommentIconTooltip(string $orderNumber): string
    {
        return $this->getOrderIconTooltip($orderNumber, $this->byRow_orderCommentIcon);
    }

    public function getOrderCustomerMessageIconTooltip(string $orderNumber): string
    {
        return $this->getOrderIconTooltip($orderNumber, $this->byRow_orderCustomerMessageIcon);
    }

    private function getOrderIconTooltip(string $orderNumber, WebDriverBy $byRow_Icon): string
    {
        $icon = $this->webDriver
            ->findElement($this->byRowWithOrderNumber($orderNumber))
            ->findElement($byRow_Icon);
        (new WebDriverActions($this->webDriver))->moveToElement($icon)->perform();

        return $this
            ->waitUntil(WebDriverExpectedCondition::visibilityOfElementLocated(WebDriverBy::className('tooltipster-content')))
            ->getText();
    }

    public function openOrder($orderNumber)
    {
        $this->webDriver->findElement($this->byRowWithOrderNumber($orderNumber))
                        ->findElement($this->byRow_link)
                        ->click();
    }
}
