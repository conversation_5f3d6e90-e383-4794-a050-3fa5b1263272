<?php
namespace ShipearlyTests\PageObjects\DealerOrder;

use Facebook\WebDriver\Exception\NoSuchElementException;
use Facebook\WebDriver\Interactions\WebDriverActions;
use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverExpectedCondition;
use ShipearlyTests\PageObjects\B2bCart\B2bCreateCartPopup;
use ShipearlyTests\PageObjects\PageObject;

/**
 * Class DealerOrderIndexPage.
 *
 * @package ShipearlyTests\PageObjects\Order
 */
class DealerOrderIndexPage extends PageObject
{
    // Initializing AJAX call adds query string params
    public $throwsRedirectException = false;

    private WebDriverBy $byCreatePurchaseOrder;
    private WebDriverBy $byCreateWholesaleOrder;
    private WebDriverBy $bySearchFilterButton;
    private WebDriverBy $bySearchFilterPanel;
    private WebDriverBy $byOrderSearch;
    private WebDriverBy $byTable;
    private WebDriverBy $byRow_orderCommentIcon;
    private WebDriverBy $byRow_orderCustomerMessageIcon;
    private WebDriverBy $byRow_orderValue;
    private WebDriverBy $byRow_link;

    protected function selectorConfig()
    {
        $this->byCreatePurchaseOrder = WebDriverBy::id('createPurchaseOrder');
        $this->byCreateWholesaleOrder = WebDriverBy::className('js-select-cart');
        $this->bySearchFilterButton = WebDriverBy::id('searchFilterButton');
        $this->bySearchFilterPanel = WebDriverBy::id('searchFilterPanel');
        $this->byOrderSearch = WebDriverBy::id('orderSearch');
        $this->byTable = WebDriverBy::id('dealerordersTable');
        $this->byRow_orderCommentIcon = WebDriverBy::cssSelector('td:nth-child(1) .fa-comment');
        $this->byRow_orderCustomerMessageIcon = WebDriverBy::cssSelector('td:nth-child(1) .fa-envelope');
        $this->byRow_orderValue = WebDriverBy::cssSelector('td:nth-last-child(4)');
        $this->byRow_link = WebDriverBy::xpath('./td[1]/a');
    }

    private function byRowWithOrderNumber(string $orderNumber): WebDriverBy
    {
        return WebDriverBy::xpath("//table[@id=\"dealerordersTable\"]/tbody/tr[./td//text()[normalize-space(.)=\"{$orderNumber}\"]]");
    }

    protected function basePath()
    {
        return rtrim(parent::basePath(), '/') . '/purchaseorders';
    }

    public function createPurchaseOrder(): B2bCreateCartPopup
    {
        $this->webDriver->findElement($this->byCreatePurchaseOrder)->click();

        return B2bCreateCartPopup::instance();
    }

    public function createWholesaleOrder(): B2bCreateCartPopup
    {
        $this->webDriver->findElement($this->byCreateWholesaleOrder)->click();

        return B2bCreateCartPopup::instance();
    }

    /**
     * @param bool|null $open If true, open the panel. If false, close the panel. If null, toggle the panel.
     * @return $this
     */
    public function toggleSearchFilterPanel(?bool $open = null): self
    {
        if ($this->webDriver->findElement($this->bySearchFilterPanel)->isDisplayed() !== $open) {
            $this->webDriver->findElement($this->bySearchFilterButton)->click();
        }

        return $this;
    }

    public function searchForOrder(string $searchText): self
    {
        $this->toggleSearchFilterPanel(true)
            ->setTextInput($this->byOrderSearch, $searchText)
            ->submit();

        return $this;
    }

    public function hasOrder(string $orderNumber)
    {
        $this->waitUntil(WebDriverExpectedCondition::visibilityOfElementLocated($this->byTable));
        return (bool)$this->_doWithNoImplicitWait(function() use ($orderNumber) {
            try {
                return $this->webDriver->findElement($this->byRowWithOrderNumber($orderNumber));
            } catch (NoSuchElementException $e) {
                return false;
            }
        });
    }

    public function getOrderCommentIconTooltip(string $orderNumber): string
    {
        return $this->getOrderIconTooltip($orderNumber, $this->byRow_orderCommentIcon);
    }

    public function getOrderCustomerMessageIconTooltip(string $orderNumber): string
    {
        return $this->getOrderIconTooltip($orderNumber, $this->byRow_orderCustomerMessageIcon);
    }

    private function getOrderIconTooltip(string $orderNumber, WebDriverBy $byRow_Icon): string
    {
        $icon = $this->webDriver
            ->findElement($this->byRowWithOrderNumber($orderNumber))
            ->findElement($byRow_Icon);
        (new WebDriverActions($this->webDriver))->moveToElement($icon)->perform();

        return $this
            ->waitUntil(WebDriverExpectedCondition::visibilityOfElementLocated(WebDriverBy::className('tooltipster-content')))
            ->getText();
    }

    public function getOrderValue(string $orderNumber)
    {
        return $this->webDriver->findElement($this->byRowWithOrderNumber($orderNumber))
                               ->findElement($this->byRow_orderValue)
                               ->getText();
    }

    public function openOrderEditPage(string $orderNumber)
    {
        $this->openOrder($orderNumber);

        // Wait for initial ajax call to finish
        sleep(1);

        return DealerOrderEditPage::instance();
    }

    public function openPurchaseOrderEditPage(string $orderNumber)
    {
        $this->openOrder($orderNumber);

        // Wait for initial ajax call to finish
        sleep(1);

        return PurchaseOrderEditPage::instance();
    }

    private function openOrder(string $orderNumber)
    {
        $this->webDriver->findElement($this->byRowWithOrderNumber($orderNumber))
                        ->findElement($this->byRow_link)
                        ->click();
    }
}
