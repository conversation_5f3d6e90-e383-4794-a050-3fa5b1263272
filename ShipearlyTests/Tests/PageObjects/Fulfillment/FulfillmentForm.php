<?php
namespace ShipearlyTests\PageObjects\Fulfillment;

use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverExpectedCondition;
use ShipearlyTests\Initializers\OrderInitializer;
use ShipearlyTests\Objects\TrackingObject;
use ShipearlyTests\PageObjects\Elements\ModalElement;
use ShipearlyTests\PageObjects\PageObject;


/**
 * Class FulfillmentPopup.
 *
 * @package ShipearlyTests\PageObjects\Fulfillment
 */
class FulfillmentForm extends PageObject
{
    use ModalElement;

    /** @var WebDriverBy */
    private $byForm;
    /** @var WebDriverBy */
    private $byWarehouseSelect;
    /** @var WebDriverBy */
    private $byForm_productRows_warehouseName;
    /** @var WebDriverBy */
    private $byForm_productRows;
    /** @var WebDriverBy */
    private $byForm_productRows_productTitle;
    /** @var WebDriverBy */
    private $byForm_productRows_availableQuantity;
    /** @var WebDriverBy */
    private $byForm_productRows_quantityInput;
    /** @var WebDriverBy */
    private $byCourierSelect;
    /** @var WebDriverBy */
    private $byTrackingNumberInput;
    /** @var WebDriverBy */
    private $byPlaceEcommerceFulfillmentCheckbox;

    protected function selectorConfig()
    {
        $this->byForm = WebDriverBy::id('FulfillmentForm');
        $this->byWarehouseSelect = WebDriverBy::id('FulfillmentWarehouseId');
        $this->byForm_productRows = WebDriverBy::cssSelector('table.order-popup-products-table > tbody > tr');
        $this->byForm_productRows_warehouseName = WebDriverBy::className('order-popup-warehouse');
        $this->byForm_productRows_productTitle = WebDriverBy::className('no-of-retails');
        $this->byForm_productRows_availableQuantity = WebDriverBy::className('badge');
        $this->byForm_productRows_quantityInput = WebDriverBy::className('js-orderproduct-quantity');
        $this->byCourierSelect = WebDriverBy::id('FulfillmentCourierId');
        $this->byTrackingNumberInput = WebDriverBy::id('FulfillmentTrackingNumber');
        $this->byPlaceEcommerceFulfillmentCheckbox = WebDriverBy::id('FulfillmentPlaceEcommerceFulfillment');
    }

    public function waitUntilVisible(): self
    {
        $this->waitUntil(
            WebDriverExpectedCondition::visibilityOfElementLocated($this->byForm),
            'Fulfillment form not found: ' . json_encode($this->byForm)
        );

        return $this;
    }

    public function getWarehouse(): string
    {
        return $this->waitUntilVisible()->getSelectInputText($this->byWarehouseSelect);
    }

    public function setWarehouse(string $warehouseName): self
    {
        $this->waitUntilVisible()->setSelectInputText($this->byWarehouseSelect, $warehouseName);

        return $this;
    }

    public function setAllItemsFulfilled(): self
    {
        return $this->setQuantities($this->getAvailableQuantities());
    }

    public function getAvailableQuantities(): array
    {
        $this->waitUntilVisible();

        $warehouseProductQuantities = [];

        $rows = $this->webDriver->findElement($this->byForm)->findElements($this->byForm_productRows);
        $warehouse = null;
        foreach ($rows as $row) {
            $isWarehouseRow = strpos($row->getAttribute('class'), 'main') === false;
            if ($isWarehouseRow) {
                $warehouse = $row->findElement($this->byForm_productRows_warehouseName)->getText();

                continue;
            }

            $product = $row->findElement($this->byForm_productRows_productTitle)->getText();
            $quantity = $row->findElement($this->byForm_productRows_availableQuantity)->getText();

            $warehouseProductQuantities[$warehouse][$product] = $quantity;
        }

        return $warehouseProductQuantities;
    }

    public function getQuantities(): array
    {
        $this->waitUntilVisible();

        $warehouseProductQuantities = [];

        $rows = $this->webDriver->findElement($this->byForm)->findElements($this->byForm_productRows);
        $warehouse = null;
        foreach ($rows as $row) {
            $isWarehouseRow = strpos($row->getAttribute('class'), 'main') === false;
            if ($isWarehouseRow) {
                $warehouse = $row->findElement($this->byForm_productRows_warehouseName)->getText();

                continue;
            }

            $product = $row->findElement($this->byForm_productRows_productTitle)->getText();
            $quantity = $this->getTextInput($row->findElement($this->byForm_productRows_quantityInput));

            $warehouseProductQuantities[$warehouse][$product] = $quantity;
        }

        return $warehouseProductQuantities;
    }

    public function setQuantities(array $warehouseProductQuantities): self
    {
        $this->waitUntilVisible();

        $rows = $this->webDriver->findElement($this->byForm)->findElements($this->byForm_productRows);
        $warehouse = null;
        foreach ($rows as $row) {
            $isWarehouseRow = strpos($row->getAttribute('class'), 'main') === false;
            if ($isWarehouseRow) {
                $warehouse = $row->findElement($this->byForm_productRows_warehouseName)->getText();

                continue;
            }

            $product = $row->findElement($this->byForm_productRows_productTitle)->getText();

            $quantity = $warehouseProductQuantities[$warehouse][$product] ?? null;
            if ($quantity !== null) {
                $this->setTextInput($row->findElement($this->byForm_productRows_quantityInput), $quantity);
            }
        }

        return $this;
    }

    public function setWithTrackingObject(?TrackingObject $tracking = null): self
    {
        $tracking = $tracking ?? OrderInitializer::getTrakingObject();

        return $this->waitUntilVisible()
            ->setCourier($tracking->service)
            ->setTrackingNumber($tracking->trackingCode);
    }

    public function getCourier(): string
    {
        return $this->waitUntilVisible()->getSelectInputText($this->byCourierSelect);
    }

    public function setCourier(string $courierName = null): self
    {
        $courierName = $courierName ?? OrderInitializer::getTrakingObject()->service;

        $this->waitUntilVisible()->setSelectInputText($this->byCourierSelect, $courierName);

        return $this;
    }

    public function getTrackingNumber(): string
    {
        return $this->waitUntilVisible()->getTextInput($this->byTrackingNumberInput);
    }

    public function setTrackingNumber(string $trackingNumber = null): self
    {
        $trackingNumber = $trackingNumber ?? OrderInitializer::getTrakingObject()->trackingCode;

        $this->waitUntilVisible()->setTextInput($this->byTrackingNumberInput, $trackingNumber);

        return $this;
    }

    public function getPlaceEcommerceFulfillmentCheckbox(): bool
    {
        return $this->waitUntilVisible()->getCheckboxInput($this->byPlaceEcommerceFulfillmentCheckbox);
    }

    public function setPlaceEcommerceFulfillmentCheckbox(bool $checked = true): self
    {
        $this->waitUntilVisible()->setCheckboxInput($this->byPlaceEcommerceFulfillmentCheckbox, $checked);

        return $this;
    }

    public function submit(): void
    {
        $modal = $this->getModal();
        $this->clickSubmitHandler();
        $this->waitUntil(WebDriverExpectedCondition::stalenessOf($modal), __METHOD__);
    }
}
