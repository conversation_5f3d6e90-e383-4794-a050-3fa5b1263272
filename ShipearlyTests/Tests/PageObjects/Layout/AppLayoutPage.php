<?php
namespace ShipearlyTests\PageObjects\Layout;

use Facebook\WebDriver\WebDriverBy;
use ShipearlyTests\PageObjects\B2bCart\B2bCartIndexPage;
use ShipearlyTests\PageObjects\PageObject;

/**
 * Class AppLayoutPage.
 *
 * @package ShipearlyTests\PageObjects\Layout
 */
class AppLayoutPage extends PageObject
{
    /** @var WebDriverBy */
    private $b2bCartIcon;

    protected function selectorConfig()
    {
        $this->b2bCartIcon = WebDriverBy::id('b2bCartCount');
    }

    public function getB2BCartCount(): int
    {
        return (int)$this->webDriver->findElement($this->b2bCartIcon)->getText();
    }

    public function clickB2BCartIcon(): B2bCartIndexPage
    {
        $this->webDriver->findElement($this->b2bCartIcon)->click();

        return B2bCartIndexPage::instance();
    }
}
