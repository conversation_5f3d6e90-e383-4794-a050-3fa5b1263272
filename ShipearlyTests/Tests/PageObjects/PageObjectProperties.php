<?php
namespace ShipearlyTests\PageObjects;

use ShipearlyTests\PageObjects\Admin\AdminBrandIndexPage;
use ShipearlyTests\PageObjects\Admin\AdminLoginPage;
use ShipearlyTests\PageObjects\Admin\AdminRetailerIndexPage;
use ShipearlyTests\PageObjects\Admin\AdminSalesRepIndexPage;
use ShipearlyTests\PageObjects\Admin\AdminStaffIndexPage;
use ShipearlyTests\PageObjects\Admin\AdminUserContactPage;
use ShipearlyTests\PageObjects\Admin\AdminUserIndexPage;
use ShipearlyTests\PageObjects\Admin\AdminUserProfilePage;
use ShipearlyTests\PageObjects\Api\ApiClientForm;
use ShipearlyTests\PageObjects\Api\ApiSettingsPage;
use ShipearlyTests\PageObjects\B2bCart\B2bCartIndexPage;
use ShipearlyTests\PageObjects\B2bCart\B2bCartViewPage;
use ShipearlyTests\PageObjects\B2bCart\B2bCreateCartPopup;
use ShipearlyTests\PageObjects\Dashboard\DashboardPage;
use ShipearlyTests\PageObjects\DealerOrder\DealerOrderEditPage;
use ShipearlyTests\PageObjects\DealerOrder\DealerOrderIndexPage;
use ShipearlyTests\PageObjects\DealerOrder\DealerOrderRefundForm;
use ShipearlyTests\PageObjects\DealerOrder\PurchaseOrderEditPage;
use ShipearlyTests\PageObjects\DealerOrder\ShipWithTrackingPopup;
use ShipearlyTests\PageObjects\Elements\DatePickerElement;
use ShipearlyTests\PageObjects\Fulfillment\FulfillmentForm;
use ShipearlyTests\PageObjects\Inventory\InventoryIndexPage;
use ShipearlyTests\PageObjects\Inventory\InventoryReservationsPage;
use ShipearlyTests\PageObjects\Inventory\InventoryTransferIndexPage;
use ShipearlyTests\PageObjects\Inventory\InventoryTransferViewPage;
use ShipearlyTests\PageObjects\Layout\AppLayoutPage;
use ShipearlyTests\PageObjects\Layout\FlashMessagePage;
use ShipearlyTests\PageObjects\Order\OrderIndexPage;
use ShipearlyTests\PageObjects\Order\OrderInvoicePage;
use ShipearlyTests\PageObjects\Order\OrderNeedToConfirmPage;
use ShipearlyTests\PageObjects\Order\OrderRefundForm;
use ShipearlyTests\PageObjects\Order\OrderVerificationCodePage;
use ShipearlyTests\PageObjects\OrderTimeline\OrderCommentForm;
use ShipearlyTests\PageObjects\OrderTimeline\OrderCustomerMessageForm;
use ShipearlyTests\PageObjects\OrderTimeline\OrderTimelinePage;
use ShipearlyTests\PageObjects\Product\B2bCataloguePage;
use ShipearlyTests\PageObjects\Product\ProductIndexPage;
use ShipearlyTests\PageObjects\Product\ProductViewPage;
use ShipearlyTests\PageObjects\Registration\SignUpForm;
use ShipearlyTests\PageObjects\Retailer\RetailerCreditsCreatePage;
use ShipearlyTests\PageObjects\Retailer\RetailerCreditsIndexPage;
use ShipearlyTests\PageObjects\Retailer\RetailerCreditsPaymentPage;
use ShipearlyTests\PageObjects\Shopify\ShopifyAddressForm;
use ShipearlyTests\PageObjects\Shopify\ShopifyCheckoutPage;
use ShipearlyTests\PageObjects\Shopify\ShopifyCustomerInfoPage;
use ShipearlyTests\PageObjects\Shopify\ShopifyDeliveryMethodsPage;
use ShipearlyTests\PageObjects\Shopify\ShopifyErrorPage;
use ShipearlyTests\PageObjects\Shopify\ShopifySuccessPage;
use ShipearlyTests\PageObjects\Shopify\TermsOfServicePopup;
use ShipearlyTests\PageObjects\Stripe\StripeCardForm;
use ShipearlyTests\PageObjects\Stripe\StripeCheckoutPopup;
use ShipearlyTests\PageObjects\User\CheckoutSettingsPage;
use ShipearlyTests\PageObjects\User\IntegrationSettingsPage;
use ShipearlyTests\PageObjects\User\ProfilePage;
use ShipearlyTests\PageObjects\User\UserContactPage;
use ShipearlyTests\PageObjects\Widgets\CatalogueWidgetsIndexPage;
use ShipearlyTests\PageObjects\Widgets\CatalogueWidgetsViewPage;
use ShipearlyTests\PageObjects\Widgets\CheckoutWidgetsDeliveryMethodsPage;
use ShipearlyTests\PageObjects\Widgets\CheckoutWidgetsOrderConfirmationPage;
use ShipearlyTests\PageObjects\Widgets\CheckoutWidgetsPaymentDetailsPage;
use ShipearlyTests\PageObjects\Widgets\LocatorWidgetsDealersPage;
use ShipearlyTests\PageObjects\Widgets\LocatorWidgetsProductsPage;

/**
 * Trait PageObjectProperties.
 *
 * Gives a class all page objects as lazy loaded read-only properties.
 *
 * @package ShipearlyTests\PageObjects
 *
 * @property-read AdminBrandIndexPage $AdminBrandIndex
 * @property-read AdminLoginPage $AdminLogin
 * @property-read AdminRetailerIndexPage $AdminRetailerIndex
 * @property-read AdminSalesRepIndexPage $AdminSalesRepIndex
 * @property-read AdminStaffIndexPage $AdminStaffIndex
 * @property-read AdminUserContactPage $AdminUserContact
 * @property-read AdminUserIndexPage $AdminUserIndex
 * @property-read AdminUserProfilePage $AdminUserProfile
 * @property-read ApiClientForm $ApiClientForm
 * @property-read ApiSettingsPage $ApiSettings
 * @property-read B2bCartIndexPage $B2bCartIndex
 * @property-read B2bCartViewPage $B2bCartView
 * @property-read B2bCreateCartPopup $B2bCreateCart
 * @property-read DashboardPage $Dashboard
 * @property-read DatePickerElement $DatePicker
 * @property-read DealerOrderEditPage $DealerOrderEdit
 * @property-read DealerOrderIndexPage $DealerOrderIndex
 * @property-read DealerOrderRefundForm $DealerOrderRefundForm
 * @property-read PurchaseOrderEditPage $PurchaseOrderEdit
 * @property-read ShipWithTrackingPopup $ShipWithTracking
 * @property-read FulfillmentForm $FulfillmentForm
 * @property-read InventoryIndexPage $InventoryIndex
 * @property-read InventoryReservationsPage $InventoryReservations
 * @property-read InventoryTransferIndexPage $InventoryTransferIndex
 * @property-read InventoryTransferViewPage $InventoryTransferView
 * @property-read AppLayoutPage $AppLayout
 * @property-read FlashMessagePage $FlashMessage
 * @property-read OrderIndexPage $OrderIndex
 * @property-read OrderInvoicePage $OrderInvoice
 * @property-read OrderNeedToConfirmPage $OrderNeedToConfirm
 * @property-read OrderRefundForm $OrderRefundForm
 * @property-read OrderVerificationCodePage $OrderVerificationCode
 * @property-read OrderCommentForm $OrderCommentForm
 * @property-read OrderCustomerMessageForm $OrderCustomerMessageForm
 * @property-read OrderTimelinePage $OrderTimeline
 * @property-read B2bCataloguePage $B2bCatalogue
 * @property-read ProductIndexPage $ProductIndex
 * @property-read ProductViewPage $ProductView
 * @property-read SignUpForm $SignUpForm
 * @property-read RetailerCreditsCreatePage $RetailerCreditsCreate
 * @property-read RetailerCreditsIndexPage $RetailerCreditsIndex
 * @property-read RetailerCreditsPaymentPage $RetailerCreditsPayment
 * @property-read ShopifyAddressForm $ShopifyAddressForm
 * @property-read ShopifyCheckoutPage $ShopifyCheckout
 * @property-read ShopifyCustomerInfoPage $ShopifyCustomerInfo
 * @property-read ShopifyDeliveryMethodsPage $ShopifyDeliveryMethods
 * @property-read ShopifyErrorPage $ShopifyError
 * @property-read ShopifySuccessPage $ShopifySuccess
 * @property-read TermsOfServicePopup $TermsOfServicePopup
 * @property-read StripeCardForm $StripeCard
 * @property-read StripeCheckoutPopup $StripeCheckout
 * @property-read CheckoutSettingsPage $CheckoutSettings
 * @property-read IntegrationSettingsPage $IntegrationSettings
 * @property-read UserContactPage $UserContact
 * @property-read ProfilePage $Profile
 * @property-read CatalogueWidgetsIndexPage $CatalogueWidgetsIndex
 * @property-read CatalogueWidgetsViewPage $CatalogueWidgetsView
 * @property-read CheckoutWidgetsDeliveryMethodsPage $CheckoutWidgetsDeliveryMethods
 * @property-read CheckoutWidgetsPaymentDetailsPage $CheckoutWidgetsPaymentDetails
 * @property-read CheckoutWidgetsOrderConfirmationPage $CheckoutWidgetsOrderConfirmation
 * @property-read LocatorWidgetsDealersPage $LocatorWidgetsDealers
 * @property-read LocatorWidgetsProductsPage $LocatorWidgetsProducts
 */
trait PageObjectProperties
{
    /**
     * Configure PageObject class mapping.
     *
     * @var string[]
     */
    protected $pageObjectClasses = array(
        'AdminBrandIndex' => AdminBrandIndexPage::class,
        'AdminLogin' => AdminLoginPage::class,
        'AdminRetailerIndex' => AdminRetailerIndexPage::class,
        'AdminSalesRepIndex' => AdminSalesRepIndexPage::class,
        'AdminStaffIndex' => AdminStaffIndexPage::class,
        'AdminUserContact' => AdminUserContactPage::class,
        'AdminUserIndex' => AdminUserIndexPage::class,
        'AdminUserProfile' => AdminUserProfilePage::class,
        'ApiClientForm' => ApiClientForm::class,
        'ApiSettings' => ApiSettingsPage::class,
        'B2bCartIndex' => B2bCartIndexPage::class,
        'B2bCartView' => B2bCartViewPage::class,
        'B2bCreateCart' => B2bCreateCartPopup::class,
        'Dashboard' => DashboardPage::class,
        'DatePicker' => DatePickerElement::class,
        'DealerOrderEdit' => DealerOrderEditPage::class,
        'DealerOrderIndex' => DealerOrderIndexPage::class,
        'DealerOrderRefundForm' => DealerOrderRefundForm::class,
        'PurchaseOrderEdit' => PurchaseOrderEditPage::class,
        'ShipWithTracking' => ShipWithTrackingPopup::class,
        'FulfillmentForm' => FulfillmentForm::class,
        'InventoryIndex' => InventoryIndexPage::class,
        'InventoryReservations' => InventoryReservationsPage::class,
        'InventoryTransferIndex' => InventoryTransferIndexPage::class,
        'InventoryTransferView' => InventoryTransferViewPage::class,
        'AppLayout' => AppLayoutPage::class,
        'FlashMessage' => FlashMessagePage::class,
        'OrderIndex' => OrderIndexPage::class,
        'OrderInvoice' => OrderInvoicePage::class,
        'OrderNeedToConfirm' => OrderNeedToConfirmPage::class,
        'OrderRefundForm' => OrderRefundForm::class,
        'OrderVerificationCode' => OrderVerificationCodePage::class,
        'OrderCommentForm' => OrderCommentForm::class,
        'OrderCustomerMessageForm' => OrderCustomerMessageForm::class,
        'OrderTimeline' => OrderTimelinePage::class,
        'B2bCatalogue' => B2bCataloguePage::class,
        'ProductIndex' => ProductIndexPage::class,
        'ProductView' => ProductViewPage::class,
        'SignUpForm' => SignUpForm::class,
        'RetailerCreditsCreate' => RetailerCreditsCreatePage::class,
        'RetailerCreditsIndex' => RetailerCreditsIndexPage::class,
        'RetailerCreditsPayment' => RetailerCreditsPaymentPage::class,
        'ShopifyAddressForm' => ShopifyAddressForm::class,
        'ShopifyCheckout' => ShopifyCheckoutPage::class,
        'ShopifyCustomerInfo' => ShopifyCustomerInfoPage::class,
        'ShopifyDeliveryMethods' => ShopifyDeliveryMethodsPage::class,
        'ShopifyError' => ShopifyErrorPage::class,
        'ShopifySuccess' => ShopifySuccessPage::class,
        'TermsOfServicePopup' => TermsOfServicePopup::class,
        'StripeCard' => StripeCardForm::class,
        'StripeCheckout' => StripeCheckoutPopup::class,
        'CheckoutSettings' => CheckoutSettingsPage::class,
        'IntegrationSettings' => IntegrationSettingsPage::class,
        'UserContact' => UserContactPage::class,
        'Profile' => ProfilePage::class,
        'CatalogueWidgetsIndex' => CatalogueWidgetsIndexPage::class,
        'CatalogueWidgetsView' => CatalogueWidgetsViewPage::class,
        'CheckoutWidgetsDeliveryMethods' => CheckoutWidgetsDeliveryMethodsPage::class,
        'CheckoutWidgetsPaymentDetails' => CheckoutWidgetsPaymentDetailsPage::class,
        'CheckoutWidgetsOrderConfirmation' => CheckoutWidgetsOrderConfirmationPage::class,
        'LocatorWidgetsDealers' => LocatorWidgetsDealersPage::class,
        'LocatorWidgetsProducts' => LocatorWidgetsProductsPage::class,
    );

    /**
     * Magic get method allows read access to configured properties.
     *
     * @param string $name The property being accessed.
     * @return mixed Either the value of the parameter or null.
     */
    public function __get($name)
    {
        if (array_key_exists($name, $this->pageObjectClasses)) {
            return PageFactory::getPage($this->pageObjectClasses[$name]);
        }

        // Reproduce what happens when __get() is not implemented
        $trace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 2);
        $lastStackFrame = array_values(array_slice($trace, -1))[0];
        trigger_error('Undefined property: ' . $lastStackFrame['class'] . '::$' . $name, E_USER_NOTICE);
        return null;
    }

    /**
     * Magic isset method allows isset/empty checks on configured properties.
     *
     * @param string $name The property being accessed.
     * @return bool Existence
     */
    public function __isset($name)
    {
        return array_key_exists($name, $this->pageObjectClasses);
    }

}
