<?php
namespace ShipearlyTests\PageObjects\Product;

use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverExpectedCondition;
use RuntimeException;
use ShipearlyTests\PageObjects\B2bCart\B2bCartViewPage;
use ShipearlyTests\PageObjects\PageObject;

class ProductViewPage extends PageObject
{
    use ConsumerViewToggleTrait;

    private WebDriverBy $byB2bCartSelectInput;
    private WebDriverBy $byQuantityInput;
    private WebDriverBy $bySubmitButton;
    private WebDriverBy $bySuccessFlashMessage;

    private $productUuids = [
        'Always Sell Direct' => '57291052-c3d0-4467-bc60-37f591c2de43',
        'Bicycle' => '57291053-afdc-4d37-a57a-37f591c2de43',
        'In-Stock Dealer Protect' => '57291053-7de0-499d-a1e2-37f591c2de43',
        'In-Stock Dealer Protect 2' => '*************-48fc-904e-37f591c2de43',
        'In-Stock Only' => '57291054-07c4-4170-9aa3-37f591c2de43',
        'Missing UPC' => '57291053-6c28-4138-a424-37f591c2de43',
        'No Stock' => '*************-40d8-aacc-37f591c2de43',
        'No UPC' => '576ac8e2-c294-4854-aef7-0aa791c2de43',
        'No UPC 2' => '576ac8e2-52b4-4866-a1be-0aa791c2de43',
        'Retail Exclusive Product' => '57291054-e654-4979-a5ae-37f591c2de43',
        'Sell Direct Exclusively' => '*************-4cf8-ab5a-37f591c2de43',
        'Sell Direct Unless Bundled' => '57291054-7ff4-495f-9e06-37f591c2de43',
        'Variant - Red' => '58f6267b-afc8-4b5f-a707-1ff091c2de43',
        'Variant - Blue' => '58f6267b-0a14-4f1a-acf3-1ff091c2de43',
    ];

    protected function selectorConfig()
    {
        $this->selectorConfigForConsumerViewToggle();
        $this->byB2bCartSelectInput = WebDriverBy::id('b2bCartSelector');
        $this->byQuantityInput = WebDriverBy::className('product-table-quantity-input');
        $this->bySubmitButton = WebDriverBy::className('js-add-to-cart-button');
        $this->bySuccessFlashMessage = WebDriverBy::className('alert-success');
    }

    protected function basePath()
    {
        return rtrim(parent::basePath(), '/') . '/products';
    }

    public function navigateToProduct($product = 'No Stock'): self
    {
        if (!array_key_exists($product, $this->productUuids)) {
            throw new RuntimeException('Unexpected product name ' . json_encode($product));
        }

        return $this->navigateTo($this->productUuids[$product]);
    }

    public function setB2bCart(?string $cartName = 'New Cart'): self
    {
        $this->setSelectInputText($this->byB2bCartSelectInput, $cartName);

        return $this;
    }

    public function setQuantity($quantity = 1, $row = 0): self
    {
        $quantityInputs = $this->webDriver->findElements($this->byQuantityInput);
        $this->setTextInputWithoutClear($quantityInputs[$row], $quantity);

        return $this;
    }

    public function getQuantityInputValue($row = 0): string
    {
        $quantityInputs = $this->webDriver->findElements($this->byQuantityInput);
        return $this->getTextInput($quantityInputs[$row]);
    }

    public function addToCart(): B2bCataloguePage
    {
        $this->waitUntil(WebDriverExpectedCondition::stalenessOf(
            $this->webDriver->findElement($this->bySubmitButton)->click()
        ), __METHOD__);

        return B2bCataloguePage::instance();
    }

    public function getSuccessMessageAttributes(): array
    {
        $element = $this->webDriver->findElement($this->bySuccessFlashMessage);

        return [
            'text' => trim(ltrim($element->getText(), '×')),
            'link_href' => $element->findElement(WebDriverBy::tagName('a'))->getAttribute('href'),
        ];
    }

    public function clickSuccessMessageLink(): B2bCartViewPage
    {
        $this->webDriver->findElement($this->bySuccessFlashMessage)
            ->findElement(WebDriverBy::tagName('a'))
            ->click();

        return B2bCartViewPage::instance();
    }
}
