<?php
namespace ShipearlyTests\PageObjects\Retailer;

use Facebook\WebDriver\WebDriverBy;
use ShipearlyTests\PageObjects\Elements\DatePickerElement;
use ShipearlyTests\PageObjects\Elements\ModalElement;
use ShipearlyTests\PageObjects\PageObject;


class RetailerCreditsCreatePage extends PageObject
{
    use ModalElement;

    public $throwsRedirectException = false;

    /** @var WebDriverBy */
    private $byInvoiceNumberInput;
    /** @var WebDriverBy */
    private $byAmountInput;
    /** @var WebDriverBy */
    private $byCreditTermSelect;
    /** @var WebDriverBy */
    private $byDateInput;

    protected function selectorConfig()
    {
        $this->byInvoiceNumberInput = WebDriverBy::id('RetailerCreditInvoiceNumber');
        $this->byAmountInput = WebDriverBy::id('RetailerCreditItem0Amount');
        $this->byCreditTermSelect = WebDriverBy::id("credit_term_ids");
        $this->byDateInput = WebDriverBy::id('RetailerCreditCreditDate');
    }

    public function setInvoiceNumber(string $invoiceNumber)
    {
        $invoiceInput = $this->webDriver->findElement($this->byInvoiceNumberInput);

        $this->setTextInput($invoiceInput, $invoiceNumber);

        return $this;
    }
    
    public function setAmount(string $amount)
    {
        $amountInput = $this->webDriver->findElement($this->byAmountInput);

        $this->setTextInput($amountInput, $amount);

        return $this;
    }
    
    public function selectCreditTerm(string $creditTerm)
    {
        $this->setSelectInputText($this->byCreditTermSelect, $creditTerm);

        return $this;
    }
    
    public function setCreditDate(?\DateTime $creditDate, bool $clickElement = true)
    {
        $datePicker = DatePickerElement::instance();
        $creditDateInput = $this->webDriver->findElement($this->byDateInput);
        $datePicker->setDatePickerInput($creditDateInput, $creditDate, $clickElement);

        return $this;
    }

    public function submit()
    {
        $this->clickOkHandler();

        return $this;
    }
}
