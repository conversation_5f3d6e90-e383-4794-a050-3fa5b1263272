<?php
namespace ShipearlyTests\PageObjects\Shopify;

use Facebook\WebDriver\WebDriverExpectedCondition;
use RuntimeException;
use ShipearlyTests\PageObjects\PageObject;

/**
 * Class ShopifyCheckoutPage.
 *
 * @package ShipearlyTests\PageObjects\Shopify
 */
class ShopifyCheckoutPage extends PageObject
{
    public $throwsRedirectException = false;

    /**
     * @var string[]
     */
    private $_variantIds;

    protected function selectorConfig()
    {
        $this->_variantIds = [
            'Always Sell Direct' => '10723515137',
            'Bicycle' => '10723507649',
            'In-Stock Dealer Protect' => '16885145025',
            'In-Stock Dealer Protect 2' => '16885170497',
            'In-Stock Only' => '16885353665',
            'Missing UPC' => '16885254017',
            'No Stock' => '11130571329',
            'No UPC' => '21063173953',
            'No UPC 2' => '21063824705',
            'Retail Exclusive Product' => '16885323009',
            'Sell Direct Exclusively' => '16885412801',
            'Sell Direct Unless Bundled' => '16885440705',
            'Variant - Red' => '27557890305',
            'Variant - Blue' => '27557890369',
        ];
    }

    protected function basePath()
    {
        return TEST_SHOPIFY_STORE_URL;
    }

    /**
     * Build a Shopify cart and checkout to the Customer Information page.
     *
     * @param array $products
     * @return ShopifyCustomerInfoPage
     */
    public function checkoutProducts($products = ['Bicycle'])
    {
        $products = $this->_formatProductsArray($products);
        $this->_validateFormattedProducts($products);

        $this->_checkout($products);

        $this->waitUntil(
            WebDriverExpectedCondition::titleContains('Customer Information'),
            'Checkout failed to redirect to the Customer Information page.'
        );

        return ShopifyCustomerInfoPage::instance();
    }

    private function _formatProductsArray(array $products)
    {
        $formattedProducts = array();
        foreach ($products as $key => $value) {
            if (is_int($value)) {
                $product = $key;
                $quantity = $value;
            } else {
                $product = $value;
                $quantity = 1;
            }
            $formattedProducts[$product] = $quantity;
        }
        return $formattedProducts;
    }

    private function _validateFormattedProducts(array $products)
    {
        if (!$products) {
            throw new RuntimeException('No products provided.');
        }
        $notConfigured = array_diff_key($products, $this->_variantIds);
        if ($notConfigured) {
            throw new RuntimeException('Some products were not configured with variant ids ' . print_r($notConfigured, true));
        }
        $invalidQuantities = array_filter($products, function($quantity) { return ($quantity < 1); });
        if ($invalidQuantities) {
            throw new RuntimeException('Some products have quantities < 1 ' . print_r($invalidQuantities, true));
        }
        return true;
    }

    private function _checkout($products = [])
    {
        $updates = [];
        foreach ($products as $product => $quantity) {
            $updates[$this->_variantIds[$product]] = $quantity;
        }
        ksort($updates);
        $queryString = http_build_query(compact('updates'));

        $form = $this->buildFakeCheckoutRedirectForm($queryString);

        $this->navigateTo();
        $this->webDriver->executeScript('jQuery(arguments[0]).appendTo("body").submit();', [$form]);
    }

    private function buildFakeCheckoutRedirectForm(string $queryString)
    {
        $shopifyStoreBasePath = TEST_SHOPIFY_STORE_URL;
        $shopifyRedirectBasePath = SHOPIFY_BASE_PATH;

        $formHtml = <<<HTML
<form method="post" id="shipearlyOrder" action="{$shopifyRedirectBasePath}getBilling/5723a179-34d4-4368-ac7f-0ed891c2de43?{$queryString}">
    <input type="hidden" name="token" value="5723a179-34d4-4368-ac7f-0ed891c2de43">
    <input type="hidden" name="referer" value="{$shopifyStoreBasePath}pages/checkout?{$queryString}">
    <input type="hidden" name="trackingId" value="***********-1">
    <input type="hidden" name="gtmId" value="">
    <input type="hidden" name="items" value="">
    <input type="hidden" name="customer" value="">
    <input type="hidden" name="storeId" value="">
</form>
HTML;

        return preg_replace('/\s+/', ' ', $formHtml);
    }
}
