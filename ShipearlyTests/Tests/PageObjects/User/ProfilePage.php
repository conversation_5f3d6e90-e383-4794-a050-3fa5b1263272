<?php

namespace ShipearlyTests\PageObjects\User;

use Facebook\WebDriver\WebDriverBy;
use ShipearlyTests\PageObjects\PageObject;

class ProfilePage extends PageObject
{
    public $byFirstName;
    public $byLastName;

    protected function selectorConfig()
    {
        $this->byFirstName = WebDriverBy::id('first_name');
        $this->byLastName = WebDriverBy::id('last_name');
    }

    protected function basePath()
    {
        return rtrim(parent::basePath(), '/') . '/profile';
    }

    public function isStaff()
    {
        $isStaff = true;
        $isStaff &= (bool)$this->webDriver->findElement($this->byFirstName);
        $isStaff &= (bool)$this->webDriver->findElement($this->byLastName);

        return (bool)$isStaff;
    }
}
