<?php

namespace ShipearlyTests\PageObjects\Widgets;

use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverExpectedCondition;
use ShipearlyTests\Objects\CheckoutDetails;
use ShipearlyTests\PageObjects\PageObject;
use ShipearlyTests\PageObjects\Widgets\Elements\CheckoutWidgetsAddressInputs;
use ShipearlyTests\PageObjects\Widgets\Elements\CheckoutWidgetsOrderSummary;
use ShipearlyTests\PageObjects\Widgets\Elements\CheckoutWidgetsRetailerSummary;

/**
 * @property CheckoutWidgetsRetailerSummary $RetailerSummary
 * @property CheckoutWidgetsOrderSummary $OrderSummary
 * @property CheckoutWidgetsAddressInputs $ShippingAddress
 */
class CheckoutWidgetsDeliveryMethodsPage extends PageObject
{
    /** @var WebDriverBy */
    private $byMethodRadioInput;
    /** @var WebDriverBy */
    private $byCustomerEmailInput;
    /** @var WebDriverBy */
    private $bySubmitButton;
    /** @var WebDriverBy */
    private $byAcceptsMarketingCheckbox;

    protected function selectorConfig()
    {
        $this->RetailerSummary = CheckoutWidgetsRetailerSummary::instance();
        $this->OrderSummary = CheckoutWidgetsOrderSummary::instance();
        $this->ShippingAddress = CheckoutWidgetsAddressInputs::instance();

        $this->byMethodRadioInput = WebDriverBy::cssSelector('input[type="radio"][name="data[method]"]');
        $this->byCustomerEmailInput = WebDriverBy::id('checkout_email');
        $this->byAcceptsMarketingCheckbox = WebDriverBy::cssSelector('input[type="checkbox"][name="data[Customer][accepts_marketing]"]');
        $this->bySubmitButton = WebDriverBy::cssSelector('#DeliveryMethodsForm [type="submit"]');
    }

    protected function basePath()
    {
        return rtrim(parent::basePath(), '/') . '/widgets/checkout/delivery_methods';
    }

    public function getMethodLabelText(): ?string
    {
        return $this->getRadioInputLabelText($this->byMethodRadioInput);
    }

    public function setMethodByLabel(string $label): self
    {
        $this->setRadioInputByLabelText($this->byMethodRadioInput, $label);

        return $this;
    }

    public function getMethodValue(): ?string
    {
        return $this->getRadioInputValue($this->byMethodRadioInput);
    }

    public function setMethodByValue(string $value): self
    {
        $this->setRadioInputByValue($this->byMethodRadioInput, $value);

        return $this;
    }

    public function getCustomerEmail(): string
    {
        return $this->getTextInput($this->byCustomerEmailInput);
    }

    public function setCustomerEmail(string $customerEmail): self
    {
        $this->setTextInput($this->byCustomerEmailInput, $customerEmail);

        return $this;
    }

    public function getAcceptsMarketing(): bool
    {
        return $this->getCheckboxInput($this->byAcceptsMarketingCheckbox);
    }

    public function setAcceptsMarketing(?bool $checked = true): self
    {
        $this->setCheckboxInput($this->byAcceptsMarketingCheckbox, $checked);

        return $this;
    }

    public function getShippingAddress(): CheckoutDetails
    {
        return $this->ShippingAddress->getAddressValues();
    }

    public function setShippingAddress(?CheckoutDetails $address = null): self
    {
        $this->ShippingAddress->setAddressValues($address);

        return $this;
    }

    public function continueToPaymentDetails(): CheckoutWidgetsPaymentDetailsPage
    {
        $button = $this->webDriver->findElement($this->bySubmitButton)->click();
        $this->waitUntil(WebDriverExpectedCondition::stalenessOf($button), __METHOD__);

        return CheckoutWidgetsPaymentDetailsPage::instance();
    }
}
