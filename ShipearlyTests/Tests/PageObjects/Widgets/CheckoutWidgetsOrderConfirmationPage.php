<?php

namespace ShipearlyTests\PageObjects\Widgets;

use Facebook\WebDriver\WebDriverBy;
use ShipearlyTests\PageObjects\PageObject;
use ShipearlyTests\PageObjects\Widgets\Elements\CheckoutWidgetsDeliveryMethodSummary;
use ShipearlyTests\PageObjects\Widgets\Elements\CheckoutWidgetsOrderSummary;
use ShipearlyTests\PageObjects\Widgets\Elements\CheckoutWidgetsPaymentDetailsSummary;
use ShipearlyTests\PageObjects\Widgets\Elements\CheckoutWidgetsRetailerSummary;

/**
 * @property CheckoutWidgetsRetailerSummary $RetailerSummary
 * @property CheckoutWidgetsDeliveryMethodSummary $DeliveryMethodSummary
 * @property CheckoutWidgetsPaymentDetailsSummary $PaymentDetailsSummary
 * @property CheckoutWidgetsOrderSummary $OrderSummary
 */
class CheckoutWidgetsOrderConfirmationPage extends PageObject
{
    /** @var WebDriverBy */
    private $byOrderNunber;
    /** @var WebDriverBy */
    private $byPickupCode;

    protected function selectorConfig()
    {
        $this->RetailerSummary = CheckoutWidgetsRetailerSummary::instance();
        $this->DeliveryMethodSummary = CheckoutWidgetsDeliveryMethodSummary::instance();
        $this->PaymentDetailsSummary = CheckoutWidgetsOrderSummary::instance();
        $this->OrderSummary = CheckoutWidgetsOrderSummary::instance();

        $this->byOrderNunber = WebDriverBy::id('orderNo');
        $this->byPickupCode = WebDriverBy::id('code');
    }

    protected function basePath()
    {
        return rtrim(parent::basePath(), '/') . '/widgets/checkout/order_confirmation';
    }

    public function getOrderNumber(): string
    {
        return $this->webDriver->findElement($this->byOrderNunber)->getText();
    }

    public function getPickupCode(): string
    {
        return $this->webDriver->findElement($this->byPickupCode)->getText();
    }
}
