<?php

namespace ShipearlyTests\PageObjects\Widgets\Elements;

use Facebook\WebDriver\Exception\NoSuchElementException;
use Facebook\WebDriver\Exception\StaleElementReferenceException;
use Facebook\WebDriver\Remote\RemoteWebElement;
use Facebook\WebDriver\Remote\ShadowRoot;
use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverExpectedCondition;
use ShipearlyTests\PageObjects\PageObject;
use ShipearlyTests\PageObjects\Widgets\CheckoutWidgetsDeliveryMethodsPage;

class CatalogueWidgetsShoppingCart extends PageObject
{
    private WebDriverBy $byShadowHost;
    private WebDriverBy $bySlideOverPanel;
    private WebDriverBy $byToggleCount;
    private WebDriverBy $byOpenButton;
    private WebDriverBy $byCloseButton;
    private WebDriverBy $byCartItems;
    private WebDriverBy $byCartItem__productTitle;
    private WebDriverBy $byCartItem__variantTitle;
    private WebDriverBy $byCartItem__totalPrice;
    private WebDriverBy $byCartItem__quantityInput;
    private WebDriverBy $byCartItem__quantityIncrement;
    private WebDriverBy $byCartItem__quantityDecrement;
    private WebDriverBy $bySubmitButton;
    private WebDriverBy $bySubtotal;
    private WebDriverBy $byCartItem__remove;

    protected function selectorConfig()
    {
        $this->byShadowHost = WebDriverBy::cssSelector('shipearly-buy-button');
        $this->bySlideOverPanel = WebDriverBy::cssSelector('#slide-over-panel');
        $this->byToggleCount = WebDriverBy::cssSelector('[data-element="toggle.count"]');
        $this->byOpenButton = WebDriverBy::cssSelector('[data-element="cart.open"]');
        $this->byCloseButton = WebDriverBy::cssSelector('[data-element="cart.close"]');
        $this->byCartItems = WebDriverBy::cssSelector('ul#cart-items > li');
        $this->byCartItem__productTitle = WebDriverBy::cssSelector('[data-element="lineItem.productTitle"]');
        $this->byCartItem__variantTitle = WebDriverBy::cssSelector('[data-element="lineItem.variantTitle"]');
        $this->byCartItem__totalPrice = WebDriverBy::cssSelector('[data-element="lineItem.totalPrice"]');
        $this->byCartItem__quantityInput = WebDriverBy::cssSelector('[data-element="lineItem.quantityInput"]');
        $this->byCartItem__quantityIncrement = WebDriverBy::cssSelector('[data-element="lineItem.quantityIncrement"]');
        $this->byCartItem__quantityDecrement = WebDriverBy::cssSelector('[data-element="lineItem.quantityDecrement"]');
        $this->byCartItem__remove = WebDriverBy::cssSelector('[data-element="lineItem.remove"]');
        $this->bySubtotal = WebDriverBy::cssSelector('#subtotal');
        $this->bySubmitButton = WebDriverBy::cssSelector('[type="submit"]');
    }

    private function byCartItemWithId(string $id): WebDriverBy
    {
        return WebDriverBy::cssSelector(sprintf('li[data-key="%s"]', $id));
    }

    protected function getShadowRoot(): ShadowRoot
    {
        return $this->webDriver->findElement($this->byShadowHost)->getShadowRoot();
    }

    public function getToggleCount(): string
    {
        return $this->getShadowRoot()->findElement($this->byToggleCount)->getText();
    }

    public function isOpen(): bool
    {
        $panel = $this->getShadowRoot()->findElement($this->bySlideOverPanel);

        return $panel->isDisplayed() && !in_array('ease-in-out', explode(' ', $panel->getAttribute('class')), true);
    }

    public function isClosed(): bool
    {
        $panel = $this->getShadowRoot()->findElement($this->bySlideOverPanel);

        return !$panel->isDisplayed() && !in_array('ease-in-out', explode(' ', $panel->getAttribute('class')), true);
    }

    public function open(): self
    {
        $this->getShadowRoot()->findElement($this->byOpenButton)->click();
        $this->waitUntil(function(): bool {
            try {
                return $this->isOpen();
            } catch (StaleElementReferenceException $e) {
                return false;
            }
        }, __METHOD__);

        return $this;
    }

    public function close(): self
    {
        $this->getShadowRoot()->findElement($this->byCloseButton)->click();
        $this->waitUntil(function(): bool {
            try {
                return $this->isClosed();
            } catch (NoSuchElementException|StaleElementReferenceException $e) {
                return true;
            }
        }, __METHOD__);

        return $this;
    }

    public function decrementItemQuantity(string $id): self
    {
        $button = $this->getShadowRoot()
            ->findElement($this->byCartItemWithId($id))
            ->findElement($this->byCartItem__quantityDecrement)
            ->click();
        $this->waitUntil(WebDriverExpectedCondition::stalenessOf($button), __METHOD__);

        return $this;
    }

    public function incrementItemQuantity(string $id): self
    {
        $button = $this->getShadowRoot()
            ->findElement($this->byCartItemWithId($id))
            ->findElement($this->byCartItem__quantityIncrement)
            ->click();
        $this->waitUntil(WebDriverExpectedCondition::stalenessOf($button), __METHOD__);

        return $this;
    }

    public function removeItem(string $id): self
    {
        $button = $this->getShadowRoot()
            ->findElement($this->byCartItemWithId($id))
            ->findElement($this->byCartItem__remove)
            ->click();
        $this->waitUntil(WebDriverExpectedCondition::stalenessOf($button), __METHOD__);

        return $this;
    }

    public function getCartItems(): array
    {
        return array_map(fn(RemoteWebElement $item): array => [
            'id' => $item->getAttribute('data-key'),
            'product_title' => $item->findElement($this->byCartItem__productTitle)->getText(),
            'variant_title' => $item->findElement($this->byCartItem__variantTitle)->getText(),
            'total_price' => $this->_convertFormattedPriceToNumeric($item->findElement($this->byCartItem__totalPrice)->getText()),
            'quantity' => $item->findElement($this->byCartItem__quantityInput)->getAttribute('value'),
            'max_quantity' => $item->findElement($this->byCartItem__quantityInput)->getAttribute('max'),
        ], $this->getShadowRoot()->findElements($this->byCartItems));
    }

    public function getSubtotal(): string
    {
        return $this->_convertFormattedPriceToNumeric($this->getShadowRoot()->findElement($this->bySubtotal)->getText());
    }

    public function submit(): CheckoutWidgetsDeliveryMethodsPage
    {
        $button = $this->getShadowRoot()->findElement($this->bySubmitButton)->click();
        $this->waitUntil(WebDriverExpectedCondition::stalenessOf($button), __METHOD__);

        return CheckoutWidgetsDeliveryMethodsPage::instance();
    }
}
