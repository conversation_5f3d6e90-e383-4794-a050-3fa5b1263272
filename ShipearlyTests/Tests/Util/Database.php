<?php
namespace ShipearlyTests\Util;

use RuntimeException;

/**
 * Class Database
 *
 * @package ShipearlyTests\Util
 */
class Database
{
    /**
     * @var Database[]
     */
    private static $_instances = array();

    /**
     * @var array
     */
    private $_config = array();

    /**
     * @var JsonFileBackup
     */
    private $_checksumFile;

    /**
     * Database constructor.
     *
     * @param string $configName
     * @see \DATABASE_CONFIG
     */
    public function __construct($configName = 'default')
    {
        $this->setConfig($configName);
    }

    /**
     * Database factory method.
     *
     * @param string $configName
     * @return Database
     * @see \DATABASE_CONFIG
     */
    public static function getInstance($configName = 'default')
    {
        if (empty(self::$_instances[$configName])) {
            self::$_instances[$configName] = new Database($configName);
        }
        return self::$_instances[$configName];
    }

    public function resetChangedTables($sourceConfigName = 'test_seed')
    {
        $expectedChecksums = $this->readChecksumFile();
        $actualChecksums = $this->checksumAllTables();
        $changedTables = array();
        foreach ($actualChecksums as $table => $checksum) {
            if ($checksum !== ($expectedChecksums[$table] ?? -1)) {
                $changedTables[] = $table;
            }
        }
        $this->resetTableData($changedTables, $sourceConfigName);
        $this->writeChecksumFile();
    }

    public function resetTableData(array $destinationTables, $sourceConfigName = 'test_seed')
    {
        $sourceDatabase = Database::getInstance($sourceConfigName)->getConfig('database');
        $tableResetQueries = implode(PHP_EOL, array_map(
            function($destinationTable) use ($sourceDatabase) {
                list($database, $table) = explode('.', $destinationTable);
                $sourceTable = $sourceDatabase . '.' . $table;
                return "TRUNCATE TABLE {$destinationTable}; INSERT INTO {$destinationTable} SELECT * FROM {$sourceTable};";
            },
            $destinationTables
        ));
        $query = <<<SQL
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
{$tableResetQueries}
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
SQL;
        $this->executeQuery($query);
    }

    private function checksumAllTables(): array
    {
        $response = $this->executeQuery($this->generateChecksumQuery(), ['--skip-column-names']);
        $checksumData = array_map(
            function($row) {
                $cols = explode("\t", $row);
                if (count($cols) !== 2 || !is_numeric($cols[1]) || strpos($cols[0], $this->getConfig('database')) !== 0) {
                    throw new RuntimeException('Unexpected row content ' . json_encode($cols));
                }
                return array_combine(['Table', 'Checksum'], $cols);
            },
            array_filter(explode(PHP_EOL, $response))
        );
        return array_column($checksumData, 'Checksum', 'Table');
    }

    private function generateChecksumQuery(): string
    {
        $query = <<<SQL
SELECT CONCAT('CHECKSUM TABLE ', `table_name`, ';') 
FROM `information_schema`.`tables` 
WHERE `table_schema` = '{$this->getConfig('database')}';
SQL;
        return $this->executeQuery($query, ['--skip-column-names']);
    }

    public function executeQuery(string $query, array $options = array())
    {
        return Shell::execMysql($query, $this, $options);
    }

    private function writeChecksumFile()
    {
        if ($this->_checksumFile->writeArray($this->checksumAllTables()) === false) {
            throw new RuntimeException("Unable to save file {$this->_checksumFile->filename}");
        }
    }

    private function readChecksumFile(): array
    {
        return $this->_checksumFile->readArray();
    }

    /**
     * @param null|string $key
     * @return array|string
     * @see \DATABASE_CONFIG
     */
    public function getConfig($key = null)
    {
        if ($key !== null) {
            return $this->_config[$key];
        }
        return $this->_config;
    }

    /**
     * @param string $configName
     * @see \DATABASE_CONFIG
     */
    private function setConfig(string $configName)
    {
        static $DATABASE_CONFIG = null;
        $DATABASE_CONFIG = $DATABASE_CONFIG ?: new \DATABASE_CONFIG();

        $this->_config = $DATABASE_CONFIG->{$configName};
        $this->_checksumFile = new JsonFileBackup(TESTS_CHECKSUMS . $configName . '.checksums.json');
    }

}
