#!/bin/sh

# Configure to match your major version of chrome
#VERSION_KEY="LATEST_RELEASE" # For Google Chrome latest
#VERSION_KEY="LATEST_RELEASE_87" # For Google Chrome 87.x
VERSION_KEY="LATEST_RELEASE_$(google-chrome --version | grep -o '[0-9]\+' | head -1)"

# See https://stackoverflow.com/a/57306360/4240654
VERSION=$(curl -s "https://googlechromelabs.github.io/chrome-for-testing/${VERSION_KEY}")
wget -O "/tmp/chromedriver-linux64.zip" "https://storage.googleapis.com/chrome-for-testing-public/${VERSION}/linux64/chromedriver-linux64.zip"

unzip -oj /tmp/chromedriver-linux64.zip -d $(pwd)
rm -v /tmp/chromedriver-linux64.zip
