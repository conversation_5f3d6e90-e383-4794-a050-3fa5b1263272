#!/bin/bash
# Run from script directory even if symlinked
cd "$(dirname -- "$(readlink -f -- "${BASH_SOURCE[0]}")")" || exit 1

SOURCE_FILE="wordpress.sql"

for FILE in ${SOURCE_FILE}; do
  if [ ! -f "${FILE}" ]; then
    echo "${FILE} not found!" 1>&2
    exit 1
  fi
done

. "../app/Config/.env"
DATABASE_NAME="wordpress"

mysql --host="${DATABASE_HOST}" --port="${DATABASE_PORT}" --user="${DATABASE_USER}" --password="${DATABASE_PASS}" --execute="
  DROP DATABASE IF EXISTS ${DATABASE_NAME};
  CREATE DATABASE ${DATABASE_NAME};
  USE ${DATABASE_NAME};
  SOURCE ${SOURCE_FILE};
"
