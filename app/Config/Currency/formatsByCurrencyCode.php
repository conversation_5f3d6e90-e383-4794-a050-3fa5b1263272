<?php
App::uses('Hash', 'Utility');

/**
 * Mapping of ISO 4217 currency codes to formatting arguments.
 *
 * ```
 * [
 *     string $currencyCode => [int $decimals, string $decimal_separator, string $thousands_separator, string $currency_symbol],
 * ];
 * ```
 *
 * @see https://github.com/ashokbasnet/Cakephp-Currency-Helper
 */
$value = [
    'ARS' => [2, ',', '.', '$'], //  Argentine Peso
    'AMD' => [2, '.', ',', ''], //  Armenian Dram
    'AWG' => [2, '.', ',', 'ƒ'], //  Aruban Guilder
    'AUD' => [2, '.', ' ', '$'], //  Australian Dollar
    'BSD' => [2, '.', ',', '$'], //  Bahamian Dollar
    'BHD' => [3, '.', ',', '.د.ب'], //  Bahraini Dinar
    'BDT' => [2, '.', ',', '৳'], //  Bangladesh, Taka
    'BZD' => [2, '.', ',', '$'], //  Belize Dollar
    'BMD' => [2, '.', ',', '$'], //  Bermudian Dollar
    'BOB' => [2, '.', ',', 'Bs.'], //  Bolivia, Boliviano
    'BAM' => [2, '.', ',', 'КМ'], //  Bosnia and Herzegovina, Convertible Marks
    'BWP' => [2, '.', ',', 'P'], //  Botswana, Pula
    'BRL' => [2, ',', '.', 'R$'], //  Brazilian Real
    'BND' => [2, '.', ',', '$'], //  Brunei Dollar
    'CAD' => [2, '.', ',', 'CA$'], //  Canadian Dollar
    'KYD' => [2, '.', ',', '$'], //  Cayman Islands Dollar
    'CLP' => [0, '', '.', '$'], //  Chilean Peso
    'CNY' => [2, '.', ',', '元'], //  China Yuan Renminbi
    'COP' => [2, ',', '.', '$'], //  Colombian Peso
    'CRC' => [2, ',', '.', '₡'], //  Costa Rican Colon
    'HRK' => [2, ',', '.', 'kn'], //  Croatian Kuna
    'CUC' => [2, '.', ',', '$'], //  Cuban Convertible Peso
    'CUP' => [2, '.', ',', '$'], //  Cuban Peso
    'CYP' => [2, '.', ',', '€'], //  Cyprus Pound
    'CZK' => [2, '.', ',', 'Kč'], //  Czech Koruna
    'DKK' => [2, ',', '.', 'kr'], //  Danish Krone
    'DOP' => [2, '.', ',', '$'], //  Dominican Peso
    'XCD' => [2, '.', ',', '$'], //  East Caribbean Dollar
    'EGP' => [2, '.', ',', 'ج.م'], //  Egyptian Pound
    'SVC' => [2, '.', ',', '$'], //  El Salvador Colon
    'ATS' => [2, ',', '.', '€'], //  Euro
    'BEF' => [2, ',', '.', '€'], //  Euro
    'DEM' => [2, ',', '.', '€'], //  Euro
    'EEK' => [2, ',', '.', '€'], //  Euro
    'ESP' => [2, ',', '.', '€'], //  Euro
    'EUR' => [2, ',', '.', '€'], //  Euro
    'FIM' => [2, ',', '.', '€'], //  Euro
    'FRF' => [2, ',', '.', '€'], //  Euro
    'GRD' => [2, ',', '.', '€'], //  Euro
    'IEP' => [2, ',', '.', '€'], //  Euro
    'ITL' => [2, ',', '.', '€'], //  Euro
    'LUF' => [2, ',', '.', '€'], //  Euro
    'NLG' => [2, ',', '.', '€'], //  Euro
    'PTE' => [2, ',', '.', '€'], //  Euro
    'GHC' => [2, '.', ',', '¢'], //  Ghana, Cedi
    'GIP' => [2, '.', ',', '£'], //  Gibraltar Pound
    'GTQ' => [2, '.', ',', 'Q'], //  Guatemala, Quetzal
    'HNL' => [2, '.', ',', 'L'], //  Honduras, Lempira
    'HKD' => [2, '.', ',', '$'], //  Hong Kong Dollar
    'HUF' => [0, '', '.', 'Ft'], //  Hungary, Forint
    'ISK' => [0, '', '.', 'kr'], //  Iceland Krona
    'INR' => [2, '.', ',', '₹'], //  Indian Rupee
    'IDR' => [2, ',', '.', 'Rp'], //  Indonesia, Rupiah
    'IRR' => [2, '.', ',', '﷼'], //  Iranian Rial
    'JMD' => [2, '.', ',', '$'], //  Jamaican Dollar
    'JPY' => [0, '', ',', '¥'], //  Japan, Yen
    'JOD' => [3, '.', ',', 'د.ا'], //  Jordanian Dinar
    'KES' => [2, '.', ',', 'Sh'], //  Kenyan Shilling
    'KWD' => [3, '.', ',', 'د.ك'], //  Kuwaiti Dinar
    'LVL' => [2, '.', ',', 'Ls'], //  Latvian Lats
    'LBP' => [0, '', ' ', 'ل.ل'], //  Lebanese Pound
    'LTL' => [2, ',', ' ', 'Lt'], //  Lithuanian Litas
    'MKD' => [2, '.', ',', 'ден'], //  Macedonia, Denar
    'MYR' => [2, '.', ',', 'RM'], //  Malaysian Ringgit
    'MTL' => [2, '.', ',', ''], //  Maltese Lira
    'MUR' => [0, '', ',', '₨'], //  Mauritius Rupee
    'MXN' => [2, '.', ',', '$'], //  Mexican Peso
    'MZM' => [2, ',', '.', ''], //  Mozambique Metical
    'NPR' => [2, '.', ',', '₨'], //  Nepalese Rupee
    'ANG' => [2, '.', ',', 'ƒ'], //  Netherlands Antillian Guilder
    'ILS' => [2, '.', ',', '₪'], //  New Israeli Shekel
    'TRY' => [2, '.', ',', ''], //  New Turkish Lira
    'NZD' => [2, '.', ',', '$'], //  New Zealand Dollar
    'NOK' => [2, ',', '.', 'kr'], //  Norwegian Krone
    'PKR' => [2, '.', ',', '₨'], //  Pakistan Rupee
    'PEN' => [2, '.', ',', 'S/.'], //  Peru, Nuevo Sol
    'UYU' => [2, ',', '.', '$'], //  Peso Uruguayo
    'PHP' => [2, '.', ',', '₱'], //  Philippine Peso
    'PLN' => [2, '.', ' ', 'zł'], //  Poland, Zloty
    'GBP' => [2, '.', ',', '£'], //  Pound Sterling
    'OMR' => [3, '.', ',', 'ر.ع.'], //  Rial Omani
    'RON' => [2, ',', '.', 'lei'], //  Romania, New Leu
    'ROL' => [2, ',', '.', ''], //  Romania, Old Leu
    'RUB' => [2, ',', '.', 'руб'], //  Russian Ruble
    'SAR' => [2, '.', ',', 'ر.س'], //  Saudi Riyal
    'SGD' => [2, '.', ',', '$'], //  Singapore Dollar
    'SKK' => [2, ',', ' ', ''], //  Slovak Koruna
    'SIT' => [2, ',', '.', ''], //  Slovenia, Tolar
    'ZAR' => [2, '.', ' ', 'R'], //  South Africa, Rand
    'KRW' => [0, '', ',', '₩'], //  South Korea, Won
    'SZL' => [2, '.', ', ', 'L'], //  Swaziland, Lilangeni
    'SEK' => [2, ',', '.', 'kr'], //  Swedish Krona
    'CHF' => [2, '.', '\'', 'Fr'], //  Swiss Franc
    'TZS' => [2, '.', ',', 'Sh'], //  Tanzanian Shilling
    'THB' => [2, '.', ',', '฿'], //  Thailand, Baht
    'TOP' => [2, '.', ',', 'T$'], //  Tonga, Paanga
    'AED' => [2, '.', ',', 'د.إ'], //  UAE Dirham
    'UAH' => [2, ',', ' ', '₴'], //  Ukraine, Hryvnia
    'USD' => [2, '.', ',', 'US$'], //  US Dollar
    'VUV' => [0, '', ',', 'Vt'], //  Vanuatu, Vatu
    'VEF' => [2, ',', '.', 'Bs F'], //  Venezuela Bolivares Fuertes
    'VEB' => [2, ',', '.', ''], //  Venezuela, Bolivar
    'VND' => [0, '', '.', '₫'], //  Viet Nam, Dong
    'ZWD' => [2, '.', ' ', 'Z$'], //  Zimbabwe Dollar
    'TWD' => [2, '.', ',', 'NT$'], //Taiwan Dollar
];

// Set the configuration access path to match this file's path relative to CONFIG
$accessPath = str_replace(DS, '.', str_replace([CONFIG, '.php'], '', __FILE__));

/**
 * Output var for PhpReader.
 *
 * @see PhpReader::read
 */
$config = Hash::insert([], $accessPath, $value);
