<?php
/**
 * Routes configuration
 *
 * In this file, you set up routes to your controllers and their actions.
 * Routes are very important mechanism that allows you to freely connect
 * different URLs to chosen controllers and their actions (functions).
 *
 * CakePHP(tm) : Rapid Development Framework (https://cakephp.org)
 * Copyright (c) Cake Software Foundation, Inc. (https://cakefoundation.org)
 *
 * Licensed under The MIT License
 * For full copyright and license information, please see the LICENSE.txt
 * Redistributions of files must retain the above copyright notice.
 *
 * @copyright     Copyright (c) Cake Software Foundation, Inc. (https://cakefoundation.org)
 * @link          https://cakephp.org CakePHP(tm) Project
 * @package       app.Config
 * @since         CakePHP(tm) v 0.2.9
 * @license       https://opensource.org/licenses/mit-license.php MIT License
 */

	App::uses('SubdomainRoute', 'Routing/Route');
	App::uses('User', 'Model');
	Router::defaultRouteClass('SubdomainRoute');

	Router::connect('/admin', array('controller' => 'users', 'action' => 'login', 'admin'=>true, 'home'));
	Router::connect('/admin/users', array('controller' => 'users', 'action' => 'login', 'admin'=>true, 'home'));
	Router::connect('/admin/users/login', array('controller' => 'users', 'action' => 'login', 'admin'=>true, 'home'));
	Router::connect('/admin/users/logout', array('controller' => 'users', 'action' => 'logout', 'admin'=>true, 'home'));
	Router::connect('/admin/users/login_as/:id', array('controller' => 'users', 'action' => 'login_as', 'admin' => true), array('id' => Router::ID, 'pass' => array('id')));
	Router::connect('/admin/users/revenue_model/:id', array('controller' => 'users', 'action' => 'revenue_model', 'admin' => true), array('id' => Router::ID, 'pass' => array('id')));
	Router::connect('/admin/users/brand_revenue_model/:id', array('controller' => 'users', 'action' => 'brand_revenue_model', 'admin' => true), array('id' => Router::ID, 'pass' => array('id')));
	Router::connect('/admin/users/change_store_radius/:id', array('controller' => 'users', 'action' => 'change_store_radius', 'admin' => true), array('id' => Router::ID, 'pass' => array('id')));
	Router::connect('/admin/users/store_associate_pin/:id', array('controller' => 'users', 'action' => 'store_associate_pin', 'admin' => true), array('id' => Router::ID, 'pass' => array('id')));
	Router::connect('/admin/pages/list', array('controller' => 'pages', 'action' => 'list', 'admin'=>true, 'home'));
	Router::connect('/admin/pages/edit/:uuid', array('controller' => 'pages', 'action' => 'add', 'admin'=>true, 'home'));
	Router::connect('/admin/pages/add', array('controller' => 'pages', 'action' => 'add', 'admin'=>true, 'home'));
	Router::connect('/admin/users/:filter', array('controller' => 'pages', 'action' => 'userlist', 'admin'=>true, 'home'), array('filter' => implode('|', User::TYPES_ALL)));
	Router::connect('/admin/ajaxUsers/:filter', array('controller' => 'pages', 'action' => 'ajax_userlists', 'admin'=>true, 'home'));
	Router::connect('/admin/Suspend/:id', array('controller' => 'pages', 'action' => 'suspend', 'admin'=>true, 'home'));
	Router::connect('/admin/Active/:id', array('controller' => 'pages', 'action' => 'active', 'admin'=>true, 'home'));
	Router::connect('/admin/users/:act/:id', array('controller' => 'pages', 'action' => 'userapp', 'admin'=>true, 'home'));
	Router::connect('/admin/users/:act/:id/:companycode', array('controller' => 'pages', 'action' => 'userapp', 'admin'=>true, 'home'));
	Router::connect('/admin/brands/:act/:id/:revenuemodel', array('controller' => 'pages', 'action' => 'userapp', 'admin'=>true, 'home'));
	Router::connect('/admin/StoreAssociate/:act/:id/:store_associate_pin', array('controller' => 'pages', 'action' => 'userapp', 'admin'=>true, 'home'));
	Router::connect('/admin/StoreAssociate/:act/:id/:store_associate_pin/:store_associate_default_amount', array('controller' => 'pages', 'action' => 'userapp', 'admin'=>true, 'home'));
	Router::connect('/admin/contact/:id', array('admin' => true, 'controller' => 'users', 'action' => 'contact'), array('id' => Router::ID, 'pass' => array('id')));
	Router::connect('/admin/contact/:id/delete', array('controller' => 'users', 'action' => 'contact_delete', 'admin' => true), array('id' => Router::ID, 'pass' => array('id')));
	Router::connect('/admin/orders/list', array('controller' => 'pages', 'action' => 'orders', 'admin'=>true, 'home'));
	Router::connect('/admin/dealerorders/list', array('controller' => 'pages', 'action' => 'dealerorders', 'admin'=>true, 'home'));
	Router::connect('/admin/emails/list', array('admin' => true, 'controller' => 'pages', 'action' => 'emails'));
	Router::connect('/admin/pages/emailedit/:id', array('admin' => true, 'controller' => 'pages', 'action' => 'emailedit'), array('id' => Router::ID, 'pass' => array('id')));
	Router::connect('/admin/payment/list', array('controller' => 'pages', 'action' => 'deliveredorders', 'admin'=>true, 'home'));
	Router::connect('/admin/addtomasspay', array('controller' => 'Orders', 'action' => 'addtomasspay', 'admin'=>true, 'home'));
	Router::connect('/admin/setting', array('controller' => 'pages', 'action' => 'configuration', 'admin'=>true, 'home'));
	Router::connect('/admin/editsetting', array('controller' => 'pages', 'action' => 'editconfiguration', 'admin'=>true, 'home'));
	Router::connect('/admin/orders/view/:orderID', array('controller' => 'orders', 'action' => 'view', 'admin' => true), array('pass' => array('orderID')));
	Router::connect('/admin/orders/dealer/view/:orderID', array('controller' => 'orders', 'action' => 'view', 'admin' => true, 'type' => 'dealer'), array('pass' => array('orderID')));
	Router::connect('/admin/products/:uuid', array('controller' => 'products', 'action' => 'view', 'admin' => true), array('uuid' => Router::UUID, 'pass' => array('uuid')));
	Router::connect('/admin/:id/reset', array('controller' => 'orders', 'action' => 'resetCode', 'admin' => true), array('id' => Router::ID, 'pass' => array('id')));
	Router::connect('/:id/reset', array('controller' => 'orders', 'action' => 'retailerResetCode'), array('id' => Router::ID, 'pass' => array('id')));
	Router::connect('/:id/:type/:schedule_id/reset', array('controller' => 'orders', 'action' => 'retailerResetCode'), array('id' => Router::ID, 'pass' => array('id')));
	Router::connect('/admin/resetPassword', array('controller' => 'pages', 'action' => 'resetPassword', 'admin' => true));
	Router::connect('/admin/:id/areaedit', array('controller' => 'pages', 'action' => 'interestedareaedit', 'admin' => true));
	Router::connect('/admin/coupon/:id/:couponcode', array('controller' => 'pages', 'action' => 'stripeCoupon', 'admin' => true));
	Router::connect('/admin/associateRevenueModel/:id', array('controller' => 'pages', 'action' => 'associateRevenueModel', 'admin' => true));
	Router::connect('/admin/selldirectoption/:id/:selldirect', array('controller' => 'pages', 'action' => 'sellDirectOption', 'admin' => true));
	Router::connect('/admin/useredit/:type/:id', array('controller' => 'pages', 'action' => 'useredit', 'admin'=>true, 'home'));
	Router::connect('/admin/user_settings/:field/:user_id', array('controller' => 'user_settings', 'action' => 'edit_policy', 'admin' => true), array('user_id' => Router::ID, 'pass' => array('field', 'user_id')));
	Router::connect('/admin/account_setting/:id', array('controller' => 'users', 'action' => 'account_setting', 'admin'=>true, 'home'));
	Router::connect('/admin/configuration/:id', array('controller' => 'users', 'action' => 'configuration', 'admin'=>true, 'home'));
	Router::connect('/admin/storehours/:id', array('controller' => 'users', 'action' => 'storehours', 'admin' => true), array('id' => Router::ID, 'pass' => array('id')));
	Router::connect('/admin/bank/disconnect/:user_id', array('controller' => 'users', 'action' => 'stripe_disconnect', 'admin' => true), array('user_id' => Router::ID, 'pass' => array('user_id')));

	Router::connect('/customers', array('controller' => 'customers', 'action' => 'index'));
	Router::connect('/customer/:cid/orders', array('controller' => 'orders', 'action' => 'customer_index', 'filter' => 'customerOrder'), array('cid' => Router::ID, 'pass' => array('cid')));
	Router::connect('/customers/:cid/orders/export', array('controller' => 'orders', 'action' => 'export', 'filter' => 'customerOrder'), array('cid' => Router::ID, 'pass' => array('cid')));

	Router::connect('/login', array('controller' => 'users', 'action' => 'login'));
	Router::connect('/activated/login', array('controller' => 'users', 'action' => 'login', 'filter' => 'Activated'));
	Router::connect('/logout', array('controller' => 'users', 'action' => 'logout'));
	Router::connect('/forgot_password', array('controller' => 'users', 'action' => 'forgot_password'));
	Router::connect('/signup/:user_type', array('controller' => 'users', 'action' => 'signup'), array('pass' => array('user_type')));
	Router::connect('/retailers/search', array('controller' => 'users', 'action' => 'retailer_search'));
	Router::connect('/profile', array('controller' => 'users', 'action' => 'profile'));
	Router::connect('/listuser', array('controller' => 'users', 'action' => 'listuser'));
	Router::connect('/contact/:id', array('controller' => 'users', 'action' => 'contact'), array('id' => Router::ID, 'pass' => array('id')));
	Router::connect('/:userID/review', array('controller' => 'users', 'action' => 'review'));
	Router::connect('/:userID/reviews', array('controller' => 'users', 'action' => 'reviews'));
	Router::connect('/subscription', array('controller' => 'users', 'action' => 'subscription'));
	Router::connect('/shipearly/subscription', array('controller' => 'users', 'action' => 'acc_subscription'));

	Router::connect('/stripe_login', array('controller' => 'users', 'action' => 'stripelogin'));
	Router::connect('/bank/connect', array('controller' => 'users', 'action' => 'stripeconnect'));
	Router::connect('/stripepaymentdetails/:id', array('controller' => 'users', 'action' => 'stripepaymentdetails'));
	Router::connect('/resendLink/:id', array('controller' => 'users', 'action' => 'resendLink'), array('id' => Router::ID, 'pass' => array('id')));
	Router::connect('/resetPassword/:token', array('controller' => 'users', 'action' => 'reset_password'), array('pass' => array('token')));

	// Connect Brand/Retailer Sliders
	Router::connect('/adddealer/:id', array('controller' => 'users', 'action' => 'adddealer'), array('id' => Router::ID, 'pass' => array('id')));
	Router::connect('/admin/adddealer/:id', array('controller' => 'users', 'action' => 'adddealer', 'admin' => true), array('id' => Router::ID, 'pass' => array('id')));
	Router::connect('/removedealer/:id', array('controller' => 'users', 'action' => 'removedealer'), array('id' => Router::ID, 'pass' => array('id')));
	Router::connect('/admin/removedealer/:id', array('controller' => 'users', 'action' => 'removedealer', 'admin' => true), array('id' => Router::ID, 'pass' => array('id')));

	Router::connect('/change_password', array('controller' => 'users', 'action' => 'change_password'));
	Router::connect('/account_setting', array('controller' => 'users', 'action' => 'account_setting'));
	Router::connect('/activate/:id', array('controller' => 'users', 'action' => 'activate'));
	Router::connect('/configuration', array('controller' => 'users', 'action' => 'configuration'));
	Router::connect('/configuration/:id', array('controller' => 'users', 'action' => 'inventory_settings'), array('id' => Router::ID, 'pass' => array('id')));
	Router::connect('/settings/checkout', array('controller' => 'users', 'action' => 'checkout_settings'));
	Router::connect('/editperson', array('controller' => 'users', 'action' => 'editperson'));
	Router::connect('/customersupport', array('controller' => 'users', 'action' => 'editperson'));
	Router::connect('/storehours/:id', array('controller' => 'users', 'action' => 'storehours'), array('id' => Router::ID, 'pass' => array('id')));
	Router::connect('/storehours', array('controller' => 'users', 'action' => 'storehours'));
	Router::connect('/shipment/configuration', array('controller' => 'users', 'action' => 'shipment_setting'));
	Router::connect('/admin/shipment/configuration/:id', array('controller' => 'users', 'action' => 'shipment_setting', 'admin' => true), array('id' => Router::ID, 'pass' => array('id')));
	Router::connect('/shipment/dealer_options', array('controller' => 'users', 'action' => 'shipment_dealer_options'));
	Router::connect('/admin/shipment/dealer_options/:id', array('controller' => 'users', 'action' => 'shipment_setting', 'admin' => true), array('id' => Router::ID, 'pass' => array('id')));
	Router::connect('/settings/notification', array('controller' => 'users', 'action' => 'notification_settings_menu'));
	Router::connect('/settings/notification/:id', array('controller' => 'users', 'action' => 'notification_settings'), array('id' => Router::ID, 'pass' => array('id')));
	Router::connect('/payouts', array('controller' => 'users', 'action' => 'payouts'));
	Router::connect('/bank_accounts/:brand_id/add', array('controller' => 'bank_accounts', 'action' => 'add'), ['pass' => ['brand_id']]);
	Router::connect('/bank_accounts/:brand_id/remove', array('controller' => 'bank_accounts', 'action' => 'remove'), ['pass' => ['brand_id']]);
	Router::connect('/bank_accounts/:brand_id/get_setup_token', array('controller' => 'bank_accounts', 'action' => 'get_setup_token'), ['pass' => ['brand_id']]);

	Router::connect('/settings/api/clients', array('controller' => 'api_clients', 'action' => 'index'));
	Router::connect('/settings/api/clients/new', array('controller' => 'api_clients', 'action' => 'add'));
	Router::connect('/settings/api/clients/:client_id', array('controller' => 'api_clients', 'action' => 'edit'), array('pass' => array('client_id')));

	Router::connect('/settings/integrations', array('controller' => 'users', 'action' => 'integrations'));

	//Shipping Zone pages
	Router::connect('/shipment/shippingzones', array('controller' => 'shipping_zones', 'action' => 'shipping_zones'));
	Router::connect('/shipment/shippingzones/:option', array('controller' => 'shipping_zones', 'action' => 'shipping_zones'));
	Router::connect('/shipment/shippingzones/:type/:id', array('controller' => 'shipping_zones', 'action' => 'shipping_zones'));
	Router::connect('/shipment/shippingzones/:type/:id/:option', array('controller' => 'shipping_zones', 'action' => 'shipping_zones'));
	Router::connect('/shipment/manageshippingzones/:type', array('controller' => 'shipping_zones', 'action' => 'shipping_zone_manage'));
	Router::connect('/shipment/shipping_carriers', array('controller' => 'shipping_zones', 'action' => 'shipping_carriers'));
	Router::connect('/shipment/ajax_user_carrier/:type', array('controller' => 'shipping_zones', 'action' => 'ajax_user_carrier'));
	Router::connect('/shipment/ajax_user_package/:type', array('controller' => 'shipping_zones', 'action' => 'ajax_user_package'));
	Router::connect('/shipment/ajax_shipment_origin', array('controller' => 'shipping_zones', 'action' => 'ajax_shipment_origin'));
	Router::connect('/weightbasedshippingprice', array('controller' => 'shipping_zones', 'action' => 'weightBasedShippingPrice'));
	Router::connect('/weightbasedshippingprice/:type/:id', array('controller' => 'shipping_zones', 'action' => 'weightBasedShippingPrice'));

	//B2B Shipping Zone pages
	Router::connect('/shipment/wholesale', array('controller' => 'b2b_shipping_zones', 'action' => 'index'));
	Router::connect('/shipment/wholesale/new', array('controller' => 'b2b_shipping_zones', 'action' => 'edit'));
	Router::connect('/shipment/wholesale/:id', array('controller' => 'b2b_shipping_zones', 'action' => 'edit'), array('id' => Router::ID, 'pass' => array('id')));

	Router::connect('/admin/settings/taxes', array('admin' => true, 'controller' => 'user_taxes', 'action' => 'index'));
	Router::connect('/admin/settings/taxes/:country_code', array('admin' => true, 'controller' => 'user_taxes', 'action' => 'edit'), array('pass' => array('country_code')));
	Router::connect('/settings/taxes', array('controller' => 'user_taxes', 'action' => 'index'));
	Router::connect('/settings/taxes/:country_code', array('controller' => 'user_taxes', 'action' => 'registrations'), array('country_code' => 'US|CA', 'pass' => array('country_code')));
	Router::connect('/settings/taxes/:country_code/registrations', array('controller' => 'user_taxes', 'action' => 'edit_registration'), array('country_code' => 'US|CA', 'pass' => array('country_code')));
	Router::connect('/settings/taxes/:country_code/registrations/:state_id', array('controller' => 'user_taxes', 'action' => 'edit_registration'), array('country_code' => 'US|CA', 'state_id' => Router::ID, 'pass' => array('country_code', 'state_id')));
	Router::connect('/settings/taxes/:country_code', array('controller' => 'user_taxes', 'action' => 'edit'), array('pass' => array('country_code')));

	//Search pages
	Router::connect('/admin/brands/:id', array('controller' => 'Search', 'action' => 'brand_search', 'admin' => true), array('id' => Router::ID, 'pass' => array('id')));
	Router::connect('/admin/ajaxsearch', array('controller' => 'Search', 'action' => 'ajax_search', 'admin' => true));
	Router::connect('/productsearch', array('controller' => 'Search', 'action' => 'productsearch'));
	Router::connect('/ajaxproductsearch', array('controller' => 'Search', 'action' => 'ajax_productsearch'));

	Router::connect('/dashboards', array('controller' => 'dashboards', 'action' => 'index'));
	Router::connect('/myactivities/ajax_act', array('controller' => 'dashboards', 'action' => 'ajax_act'));
	Router::connect('/profile/setup', array('controller' => 'dashboards', 'action' => 'profile_setup'));

	Router::connect('/topsellingProduct', array('controller' => 'dashboards', 'action' => 'topSellingProduct'));
	Router::connect('/recentProduct', array('controller' => 'dashboards', 'action' => 'recentProduct'));

	Router::connect('/getheadernot', array('controller' => 'Notifications', 'action' => 'getheadernot'));
	Router::connect('/notification', array('controller' => 'Notifications', 'action' => 'getUsersNotifications'));
	Router::connect('/notification/ajax_not', array('controller' => 'Notifications', 'action' => 'ajax_not'));
	Router::connect('/comment/notification', array('controller' => 'Notifications', 'action' => 'getUsersNotifications', 'filter' => 'comment'));
	Router::connect('/comment/notification/ajax_not', array('controller' => 'Notifications', 'action' => 'ajax_not', 'filter' => 'comment'));
	Router::connect('/order/notification', array('controller' => 'Notifications', 'action' => 'getUsersNotifications', 'filter' => 'order'));
	Router::connect('/order/notification/ajax_not', array('controller' => 'Notifications', 'action' => 'ajax_not', 'filter' => 'order'));
	Router::connect('/popupnotification', array('controller' => 'Notifications', 'action' => 'ajax_popup', 'filter' => 'notification'));
	Router::connect('/popupcomment', array('controller' => 'Notifications', 'action' => 'ajax_popup', 'filter' => 'comment'));
	Router::connect('/markread/:id', array('controller' => 'Notifications', 'action' => 'markRead'));

	//b2b_carts pages
	Router::connect('/draft_orders', array('controller' => 'b2b_carts', 'action' => 'index'));
	Router::connect('/draft_orders/:id', array('controller' => 'b2b_carts', 'action' => 'view'), array('id' => Router::ID, 'pass' => array('id')));
	Router::connect('/draft_orders/:id/preorder', array('controller' => 'b2b_carts', 'action' => 'place_order', 'filter' => 'preorder'), array('id' => Router::ID, 'pass' => array('id')));
	Router::connect('/draft_orders/:id/place_order', array('controller' => 'b2b_carts', 'action' => 'place_order'), array('id' => Router::ID, 'pass' => array('id')));
	Router::connect('/draft_orders/add/:retailer_id', array('controller' => 'b2b_carts', 'action' => 'add'), array('retailer_id' => Router::ID, 'pass' => array('retailer_id')));
	Router::connect('/draft_orders/add', array('controller' => 'orders', 'action' => 'draft_order_add'));

	//products pages
	Router::connect('/products', array('controller' => 'products', 'action' => 'index'));
	Router::connect('/productedit/:id', array('controller' => 'products', 'action' => 'ajax_edit'), array('id' => Router::ID, 'pass' => array('id')));
	Router::connect('/watch_products', array('controller' => 'products', 'action' => 'watch_products'));
	Router::connect('/products/ajax_index', array('controller' => 'products', 'action' => 'ajax_index'));
	Router::connect('/products/ajax_watch_products', array('controller' => 'products', 'action' => 'ajax_watch_products'));
	Router::connect('/products/:uuid/watch', array('controller' => 'products', 'action' => 'watch_product'));
	Router::connect('/products/export', array('controller' => 'products', 'action' => 'export'));
	Router::connect('/products/import', array('controller' => 'products', 'action' => 'import'));
	Router::connect('/products/:uuid', array('controller' => 'products', 'action' => 'view'), array('uuid' => Router::UUID, 'pass' => array('uuid')));
	Router::connect('/:userID/products', array('controller' => 'products', 'action' => 'index'));
	Router::connect('/:userID/products/:pageNo', array('controller' => 'products', 'action' => 'index'));
	Router::connect('/catalogue/:retailer_id', array('controller' => 'products', 'action' => 'index_retailer'), array('retailer_id' => Router::ID, 'pass' => array('retailer_id')));
	Router::connect('/catalogue/:retailer_id/variants/:product_id', array('controller' => 'products', 'action' => 'catalogue_variants'), array('product_id' => Router::ID, 'retailer_id' => Router::ID, 'pass' => array('product_id', 'retailer_id')));
	Router::connect('/addretailers/:productID', array('controller' => 'products', 'action' => 'addretailers'));
	Router::connect('/:productID/addmedia', array('controller' => 'products', 'action' => 'newmedia'));
	Router::connect('/:mediaID/media', array('controller' => 'products', 'action' => 'viewmedia'));
	Router::connect('/:mediaID/editmedia', array('controller' => 'products', 'action' => 'newmedia'));
	Router::connect('/:productID/allmedia', array('controller' => 'products', 'action' => 'allmedia'));
	Router::connect('/:productID/enquiry', array('controller' => 'products', 'action' => 'enquiry'));
	Router::connect('/:mediaID/deletemedia', array('controller' => 'products', 'action' => 'deletemedia'));
	Router::connect('/products/save_state', array('controller' => 'products', 'action' => 'save_state'));

	// Product Pricing
	Router::connect('/products/:product_id/pricing', array('controller' => 'product_pricing', 'action' => 'index'), array('product_id' => Router::ID, 'pass' => array('product_id')));
	Router::connect('/products/:product_id/pricing/:action/*', array('controller' => 'product_pricing'), array('product_id' => Router::ID, 'pass' => array('product_id')));

	// Product State Fees
	Router::connect('/product_state_fees/import/:fee_product_id', array('controller' => 'product_state_fees', 'action' => 'import'), array('fee_product_id' => Router::ID, 'pass' => array('fee_product_id')));
	Router::connect('/product_state_fees/export/:fee_product_id', array('controller' => 'product_state_fees', 'action' => 'export'), array('fee_product_id' => Router::ID, 'pass' => array('fee_product_id')));

	// Orders
	Router::connect('/orders', array('controller' => 'orders', 'action' => 'index'));
	Router::connect('/orders/export', array('controller' => 'orders', 'action' => 'export'));
	Router::connect('/orderswithproduct/export', array('controller' => 'orders', 'action' => 'export', 'filter' => 'orderswithproduct'));

	Router::connect('/orders/history', array('controller' => 'orders', 'action' => 'index', 'filter' => 'ownorders'));
	Router::connect('/ownorders/export', array('controller' => 'orders', 'action' => 'export', 'filter' => 'ownorders'));

	Router::connect('/shipments', array('controller' => 'orders', 'action' => 'index', 'filter' => 'shipment'));
	Router::connect('/payments', array('controller' => 'orders', 'action' => 'index', 'filter' => 'payment'));

	Router::connect('/updateDeliveryDate', array('controller' => 'orders', 'action' => 'updateDeliveryDate'));
	Router::connect('/newdealerorder', array('controller' => 'orders', 'action' => 'newDealerOrder'));
	Router::connect('/dealerordershipmenttracking', array('controller' => 'orders', 'action' => 'dealerOrderShipmentTracking'));
	Router::connect('/orders/:id/recharge_expired', array('controller' => 'orders', 'action' => 'recharge_expired'), array('id' => Router::ID, 'pass' => array('id')));

	Router::connect('/orders/:order_id/comments/tooltip', array('controller' => 'order_comments', 'action' => 'tooltip'), array('order_id' => Router::ID, 'pass' => array('order_id')));
	Router::connect('/orders/:order_id/customer_messages/tooltip', array('controller' => 'order_customer_messages', 'action' => 'tooltip'), array('order_id' => Router::ID, 'pass' => array('order_id')));
	Router::connect('/orders/:order_id/comments/new', array('controller' => 'order_comments', 'action' => 'add'), array('order_id' => Router::ID, 'pass' => array('order_id')));
	Router::connect('/orders/:order_id/comments/:id', array('controller' => 'order_comments', 'action' => 'view', '[method]' => 'GET'), array('order_id' => Router::ID, 'id' => Router::ID, 'pass' => array('order_id', 'id')));
	Router::connect('/orders/:order_id/comments/:id', array('controller' => 'order_comments', 'action' => 'delete', '[method]' => 'DELETE'), array('order_id' => Router::ID, 'id' => Router::ID, 'pass' => array('order_id', 'id')));

	Router::connect('/orderedit/:id', array('controller' => 'orders', 'action' => 'edit'), array('id' => Router::ID, 'pass' => array('id')));
	Router::connect('/webhook', array('controller' => 'orders', 'action' => 'aftershipwebhook'));
	Router::connect('/:id/invoice', array('controller' => 'orders', 'action' => 'invoice'), array('id' => Router::ID, 'pass' => array('id')));
	Router::connect('/:id/dealerorder_invoice', array('controller' => 'orders', 'action' => 'invoice', 'filter' => 'dealerorderTable'), array('id' => Router::ID, 'pass' => array('id')));
	Router::connect('/:OrderID/ajax_order_product', array('controller' => 'orders', 'action' => 'ajax_order_product'), array('OrderID' => Router::ID, 'pass' => array('OrderID')));
	Router::connect('/:OrderID/transaction', array('controller' => 'orders', 'action' => 'transaction'));
	Router::connect('/release/:OrderID', array('controller' => 'orders', 'action' => 'release_ipn'));
	Router::connect('/release_cancel/:OrderID', array('controller' => 'orders', 'action' => 'release_cancel'));
	Router::connect('/rel_cron_ipn', array('controller' => 'orders', 'action' => 'rel_cron_ipn'));

	// DealerOrders
	Router::connect('/purchaseorders', array('controller' => 'orders', 'action' => 'dealerorders_index'));
	Router::redirect('/dealerorders', '/purchaseorders', ['persist' => true]);
	Router::connect('/dealerorders/export', array('controller' => 'orders', 'action' => 'dealerordersExport'));
	Router::connect('/dealerorderproducts/export', array('controller' => 'orders', 'action' => 'dealerordersExport', 'filter' => 'include_products'));

	//Fulfillments
	Router::connect('/orders/:order_id/fulfillments', array('controller' => 'fulfillments', 'action' => 'index'), array('order_id' => Router::ID, 'pass' => array('order_id')));
	Router::connect('/orders/:order_id/fulfillments/:action/*', array('controller' => 'fulfillments'), array('order_id' => Router::ID, 'pass' => array('order_id')));

	Router::connect('/dealerorders/:dealer_order_id/fulfillments', array('controller' => 'fulfillments', 'action' => 'index'), array('dealer_order_id' => Router::ID, 'pass' => array('dealer_order_id')));
	Router::connect('/dealerorders/:dealer_order_id/fulfillments/:action/*', array('controller' => 'fulfillments'), array('dealer_order_id' => Router::ID, 'pass' => array('dealer_order_id')));

	//OrderRefunds
	Router::connect('/orders/:order_id/refund', array('controller' => 'order_refunds', 'action' => 'refund'), array('order_id' => Router::ID, 'pass' => array('order_id')));
	Router::connect('/admin/orders/:order_id/refund', array('admin' => true, 'controller' => 'order_refunds', 'action' => 'refund'), array('order_id' => Router::ID, 'pass' => array('order_id')));
	Router::connect('/orders/:order_id/refund_expired', array('controller' => 'order_refunds', 'action' => 'refund_expired'), array('order_id' => Router::ID, 'pass' => array('order_id')));
	Router::connect('/admin/orders/:order_id/refund_expired', array('admin' => true, 'controller' => 'order_refunds', 'action' => 'refund_expired'), array('order_id' => Router::ID, 'pass' => array('order_id')));

	//DealerOrderRefunds
	Router::connect('/dealerorders/:order_id/refund', array('controller' => 'dealer_order_refunds', 'action' => 'refund'), array('order_id' => Router::ID, 'pass' => array('order_id')));

	Router::connect('/payment_ipn/:OrderID', array('controller' => 'Ws', 'action' => 'payment_ipn'));
	Router::connect('/payment_cancel/:OrderID', array('controller' => 'Ws', 'action' => 'payment_cancel'));
	Router::connect('/payment_cron_ipn', array('controller' => 'Ws', 'action' => 'payment_cron_ipn'));

	//Inventory
	Router::connect('/inventory', array('controller' => 'inventory', 'action' => 'index'));
	Router::connect('/inventory/export', array('controller' => 'inventory', 'action' => 'export'));
	Router::connect('/inventory/:productID', array('controller' => 'inventory', 'action' => 'product'), array('productID' => Router::ID, 'pass' => array('productID')));
	Router::connect('/inventory/:productID/ajax_product', array('controller' => 'inventory', 'action' => 'ajax_product'), array('productID' => Router::ID, 'pass' => array('productID')));
	Router::connect('/retailerproducts/:retailerID', array('controller' => 'inventory', 'action' => 'retailerproducts'), array('retailerID' => Router::ID, 'pass' => array('retailerID')));
	Router::connect('/ajax_retailerproducts/:retailerID', array('controller' => 'inventory', 'action' => 'ajax_retailerproducts'), array('retailerID' => Router::ID, 'pass' => array('retailerID')));
	Router::connect('/inventory/:product_id/reservations', array('controller' => 'inventory', 'action' => 'reservations'), array('product_id' => Router::ID, 'pass' => array('product_id')));

	//Inventory Transfers
	Router::connect('/transfers', array('controller' => 'inventory_transfers', 'action' => 'index'));
	Router::connect('/transfers/new', array('controller' => 'inventory_transfers', 'action' => 'add'));
	Router::connect('/transfers/:id', array('controller' => 'inventory_transfers', 'action' => 'edit'), array('id' => Router::ID, 'pass' => array('id')));
	Router::connect('/transfers/:action/:id', array('controller' => 'inventory_transfers'), array('id' => Router::ID, 'pass' => array('id')));
	Router::connect('/transfers/:action/*', array('controller' => 'inventory_transfers'));

	//Brand
	Router::connect('/manufacturers', array('controller' => 'manufacturers', 'action' => 'index'));
	Router::connect('/activeBrand', array('controller' => 'manufacturers', 'action' => 'index', 'filter' => 'active'));

	Router::connect('/getstates', array('controller' => 'users', 'action' => 'getstates'));
	Router::connect('/getstatesbycountry', array('controller' => 'users', 'action' => 'getstatesbycountry'));

	//Cron
	Router::connect('/updateinv', array('controller' => 'Crons', 'action' => 'index'));
	Router::connect('/updateStores/:id', array('controller' => 'Crons', 'action' => 'index'));

	Router::connect('/invite', array('controller' => 'Crons', 'action' => 'inviteRetailers'));
	Router::connect('/UpdateRetailerCount', array('controller' => 'Crons', 'action' => 'UpdateRetailerCount'));
	Router::connect('/checkNonstockOrders', array('controller' => 'Crons', 'action' => 'checkNonstockOrders'));
	Router::connect('/syncAll', array('controller' => 'Ws', 'action' => 'syncAll'));
	Router::connect('/stripeWebHook', array('controller' => 'Crons', 'action' => 'stripeWebHook'));

	//Branch
	Router::connect('/locations', array('controller' => 'branchs', 'action' => 'index'));
	Router::connect('/addBranch', array('controller' => 'branchs', 'action' => 'addBranch'));
	Router::connect('/deActiveBranch/:id', array('controller' => 'branchs', 'action' => 'deActiveBranch'));
	Router::connect('/ActiveBranch/:id', array('controller' => 'branchs', 'action' => 'ActiveBranch'));
	Router::connect('/editBranch/:id', array('controller' => 'branchs', 'action' => 'editBranch'), array('id' => Router::ID, 'pass' => array('id')));
	Router::connect('/accessLevel/:id', array('controller' => 'branchs', 'action' => 'storePermission'), array('id' => Router::ID, 'pass' => array('id')));
	Router::connect('/locations/schedule', array('controller' => 'branchs', 'action' => 'getUserSchedule'));
	Router::connect('/locations/schedule/save', array('controller' => 'branchs', 'action' => 'saveSchedule'));
	Router::connect('/locations/taxData/:id/save', array('controller' => 'branchs', 'action' => 'saveTaxData'), array('id' => Router::ID, 'pass' => array('id')));
	Router::connect('/locations/:id/exportInventory', array('controller' => 'branchs', 'action' => 'exportInventory'), array('id' => Router::ID, 'pass' => array('id')));
	Router::connect('/locations/:id/importInventory', array('controller' => 'branchs', 'action' => 'importInventory'), array('id' => Router::ID, 'pass' => array('id')));

	//Retailer
	Router::connect('/retailers', array('controller' => 'retailers', 'action' => 'index'));
	Router::connect('/retailers/ajax_index', array('controller' => 'retailers', 'action' => 'ajax_index'));
	Router::connect('/retailers/categories/:id', array('controller' => 'retailers', 'action' => 'retailer_categories'), array('id' => Router::ID, 'pass' => array('id')));
	Router::connect('/retailerpurchaseorders/:id', array('controller' => 'orders', 'action' => 'retailerpurchaseorders_index'), array('id' => Router::ID, 'pass' => array('id')));
	Router::connect('/disconnected/retailers', array('controller' => 'retailers', 'action' => 'index', 'filter' => 'disconnected'));
	Router::connect('/disconnected/retailers/ajax_index', array('controller' => 'retailers', 'action' => 'ajax_index', 'filter' => 'disconnected'));
	Router::connect('/active/retailers', array('controller' => 'retailers', 'action' => 'index', 'filter' => 'activeretailer'));
	Router::connect('/active/retailers/ajax_index', array('controller' => 'retailers', 'action' => 'ajax_index', 'filter' => 'activeretailer'));
	Router::connect('/inquiry/retailers', array('controller' => 'retailers', 'action' => 'index', 'filter' => 'retailerinquiries'));
	Router::connect('/inquiry/retailers/ajax_index', array('controller' => 'retailers', 'action' => 'ajax_index', 'filter' => 'retailerinquiries'));
	Router::connect('/retailers/export', array('controller' => 'retailers', 'action' => 'export'));

	//Storefronts
	Router::connect('/storefronts', array('controller' => 'storefronts', 'action' => 'index'));
	Router::connect('/storefronts/:set_id', array('controller' => 'storefronts', 'action' => 'index'), array('set_id' => Router::ID, 'pass' => array('set_id')));

	//Menus
	Router::connect('/menus', array('controller' => 'menus', 'action' => 'index'));

	//Retailer Credits
	Router::connect('/retailer_credits/:id/payments', array('controller' => 'retailer_credits', 'action' => 'payments'), array('id' => Router::ID, 'pass' => array('id')));
	Router::connect('/retailers/:retailer_id/credits', array('controller' => 'retailer_credits', 'action' => 'index'), array('retailer_id' => Router::ID, 'pass' => array('retailer_id')));
	Router::connect('/retailers/:retailer_id/credit_vouchers', array('controller' => 'retailer_credit_vouchers', 'action' => 'index'), array('retailer_id' => Router::ID, 'pass' => array('retailer_id')));
	Router::connect('/retailers/:retailer_id/credit_vouchers/create', array('controller' => 'retailer_credit_vouchers', 'action' => 'create'), array('retailer_id' => Router::ID, 'pass' => array('retailer_id')));
	Router::connect('/retailers/:retailer_id/credits/create', array('controller' => 'retailer_credits', 'action' => 'create'), array('retailer_id' => Router::ID, 'pass' => array('retailer_id')));
	Router::connect('/retailers/:retailer_id/credits/:id', array('controller' => 'retailer_credits', 'action' => 'edit'), array('retailer_id' => Router::ID, 'id' => Router::ID, 'pass' => array('retailer_id', 'id')));
	Router::connect('/retailers/:retailer_id/credits/:id/delete', array('controller' => 'retailer_credits', 'action' => 'delete'), array('retailer_id' => Router::ID, 'id' => Router::ID, 'pass' => array('retailer_id', 'id')));
	Router::connect('/manufacturers/:user_id/credits', array('controller' => 'retailer_credits', 'action' => 'index'), array('user_id' => Router::ID, 'pass' => array('user_id')));
	Router::connect('/manufacturers/:user_id/credit_vouchers', array('controller' => 'retailer_credit_vouchers', 'action' => 'index'), array('user_id' => Router::ID, 'pass' => array('user_id')));
	Router::connect('/manufacturers/:user_id/credits/add', array('controller' => 'retailer_credits', 'action' => 'add'), array('user_id' => Router::ID, 'pass' => array('user_id')));
	Router::connect('/manufacturers/:user_id/credits/:id', array('controller' => 'retailer_credits', 'action' => 'edit'), array('user_id' => Router::ID, 'id' => Router::ID, 'pass' => array('user_id', 'id')));
	Router::connect('/manufacturers/:user_id/credits/:id/delete', array('controller' => 'retailer_credits', 'action' => 'delete'), array('user_id' => Router::ID, 'id' => Router::ID, 'pass' => array('user_id', 'id')));
	Router::connect('/manufacturers/:user_id/retailers/:retailer_id/credits', array('controller' => 'retailer_credits', 'action' => 'index'), array('user_id' => Router::ID, 'retailer_id' => Router::ID, 'pass' => array('user_id', 'retailer_id')));

	// Sales Reps
	Router::connect('/sales_reps', array('controller' => 'sales_reps', 'action' => 'index'));

	// Brand Staff
	Router::connect('/settings/staff', array('controller' => 'brand_staff', 'action' => 'index'));
	Router::connect('/settings/staff/new', array('controller' => 'brand_staff', 'action' => 'view'));
	Router::connect('/settings/staff/:staff_id', array('controller' => 'brand_staff', 'action' => 'view'), array('staff_id' => Router::ID, 'pass' => array('staff_id')));

	// Staff
	Router::connect('/staff', array('controller' => 'staff', 'action' => 'index'));
	Router::connect('/staff/new', array('controller' => 'staff', 'action' => 'view'));
	Router::connect('/staff/:id', array('controller' => 'staff', 'action' => 'view'), array('id' => Router::ID, 'pass' => array('id')));

	//PROMOTIONS
	Router::connect('/promotions', array('controller' => 'discounts', 'action' => 'index'));
	Router::connect('/promotions/new', array('controller' => 'discounts', 'action' => 'manage_discounts'));
	Router::connect('/promotions/edit/:token', array('controller' => 'discounts', 'action' => 'manage_discounts'), array('pass' => array('token')));
	Router::connect('/promotions/status/:id/:option', array('controller' => 'discounts', 'action' => 'setStatus'), array('pass' => array('id', 'option')));
	Router::connect('/promotions/export', array('controller' => 'discounts', 'action' => 'export'));
	Router::connect('/promotions/export/:type', array('controller' => 'discounts', 'action' => 'export'));
	Router::connect('/promotions/export/:type/:status', array('controller' => 'discounts', 'action' => 'export'));
	Router::connect('/promotions/export/:type/:status/:search', array('controller' => 'discounts', 'action' => 'export'));
	Router::connect('/promotions/:action/:id', array('controller' => 'discounts'), array('id' => Router::ID, 'pass' => array('id')));
	Router::connect('/promotions/:action/*', array('controller' => 'discounts'));

	//Warehouses
	Router::connect('/warehouses', array('controller' => 'warehouses', 'action' => 'index'));
	Router::connect('/warehouses/new', array('controller' => 'warehouses', 'action' => 'add'));
	Router::connect('/warehouses/:id', array('controller' => 'warehouses', 'action' => 'edit'), array('id' => Router::ID, 'pass' => array('id')));
	Router::connect('/warehouses/:action/:id', array('controller' => 'warehouses'), array('id' => Router::ID, 'pass' => array('id')));
	Router::connect('/warehouses/:action/*', array('controller' => 'warehouses'));
	
	//Lightspeed
	Router::connect('/getLightspeed/:retailer_id', array('controller' => 'users', 'action' => 'lightSpeedList'), array('retailer_id' => Router::ID, 'pass' => array('retailer_id')));
	Router::connect('/lightspeed/connect/:retailer_id', array('controller' => 'users', 'action' => 'lightSpeedConnect'), array('retailer_id' => Router::ID, 'pass' => array('retailer_id')));
	Router::connect('/lightspeed/connect', array('controller' => 'users', 'action' => 'lightSpeedConnect'));

	//ShopifyPOS
	Router::connect('/getshopifypos', array('controller' => 'users', 'action' => 'shopifypos'));
	Router::connect('/shopifypos/shop/update/:id', array('plugin' => 'Shopifypos', 'controller' => 'ShopifyPos', 'action' => 'shop'));
	Router::connect('/shopifypos/shop/map', array('plugin' => 'Shopifypos', 'controller' => 'ShopifyPos', 'action' => 'mapVarientIds'));

	//Quickbook POS
	Router::connect('/qwc', array('plugin' => 'Quickbook', 'controller' => 'Quickbook', 'action' => 'qwc'));
	Router::connect('/QuickbookSync', array('plugin' => 'Quickbook', 'controller' => 'Quickbook', 'action' => 'getQuickbookUsers'));
	Router::connect('/downloadFile', array('plugin' => 'Quickbook', 'controller' => 'Quickbook', 'action' => 'getQwcFile'));

	//Vend POS
	Router::connect('/getVendpos/:retailer_id', array('controller' => 'users', 'action' => 'vendPosList'), array('retailer_id' => Router::ID, 'pass' => array('retailer_id')));
	Router::connect('/vendpos/connect/:retailer_id', array('controller' => 'users', 'action' => 'vendPosConnect'), array('retailer_id' => Router::ID, 'pass' => array('retailer_id')));
	Router::connect('/vendpos/connect', array('controller' => 'users', 'action' => 'vendPosConnect'));

	//Square POS
	Router::connect('/getSquare/:retailer_id', array('controller' => 'users', 'action' => 'squarePosList'), array('retailer_id' => Router::ID, 'pass' => array('retailer_id')));
	Router::connect('/square/connect/:retailer_id', array('controller' => 'users', 'action' => 'squareConnect'), array('retailer_id' => Router::ID, 'pass' => array('retailer_id')));
	Router::connect('/square/connect', array('controller' => 'users', 'action' => 'squareConnect'));

	//Shopify Ecomm
	Router::connect('/shopifyUpdate', array('controller' => 'ws', 'action' => 'shopifyUpdate'));

	//PDF
	Router::connect('/viewPdf/:id/:type', array('controller' => 'invoice_pdf', 'action' => 'viewPdf'), array('id' => Router::ID, 'pass' => array('id', 'type')));
	Router::connect('/viewPdf/:id/:type/:file', array('controller' => 'invoice_pdf', 'action' => 'viewPdf'), array('id' => Router::ID, 'pass' => array('id', 'type')));
	Router::connect('/generateInvoice/:id/:type', array('controller' => 'invoice_pdf', 'action' => 'generateInvoice'), array('id' => Router::ID, 'pass' => array('id', 'type')));
	Router::connect('/generateInvoice/:id/:type/:file', array('controller' => 'invoice_pdf', 'action' => 'generateInvoice'), array('id' => Router::ID, 'pass' => array('id', 'type')));

	//HELP
	Router::connect('/helps', array('controller' => 'helps', 'action' => 'helps_index'));

	//REPORTS
	Router::connect('/reports', array('controller' => 'reports', 'action' => 'index'));
	Router::connect('/reports/view/:name', array('controller' => 'reports', 'action' => 'view'));
	Router::connect('/reports/list/:name', array('controller' => 'reports', 'action' => 'listreport'));
	Router::connect('/reports/export/abandonedcarts', array('controller' => 'reports', 'action' => 'export_abandonedcarts'));
	Router::connect('/:token/abandoncart', array('controller' => 'reports', 'action' => 'ajax_abandonedcart'));

	Router::mapResources('ws');
	Router::parseExtensions('json');

/**
 * Load all plugin routes. See the CakePlugin documentation on
 * how to customize the loading of plugin routes.
 */
	CakePlugin::routes();

	// Connect general pages and base path last
	Router::connect('/:url', array('controller' => 'pages', 'action' => 'view'));
	Router::connect('/', array('controller' => 'dashboards'));

/**
 * Load the CakePHP default routes. Only remove this if you do not want to use
 * the built-in default routes.
 */
	require CAKE . 'Config' . DS . 'routes.php';
