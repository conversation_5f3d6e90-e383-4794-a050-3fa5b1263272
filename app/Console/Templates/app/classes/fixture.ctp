<?php
/**
 * Fixture Template file
 *
 * Fixture Template used when baking fixtures with bake
 *
 * @var string $model
 * @var string $table
 * @var string $schema
 * @var string $records
 * @var string $import
 * @var string $fields
 *
 * @see FixtureTask::bake
 */
?>
<?= "<?php\n" ?>
App::uses('TableCopyTestFixture', 'TestSuite/Fixture');

class <?= $model ?>Fixture extends TableCopyTestFixture
{
<?php if ($table) { ?>
    public $table = '<?= $table ?>';
<?php } ?>
}
