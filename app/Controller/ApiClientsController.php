<?php
App::uses('App<PERSON><PERSON>roller', 'Controller');

/**
 * ApiClients Controller.
 *
 * @property ApiClient $ApiClient
 * @property PaginatorComponent $Paginator
 */
class ApiClientsController extends AppController
{
    public $components = array(
        'Paginator' => [
            'paramType' => 'querystring',
            'recursive' => -1,
            'fields' => ['ApiClient.client_id', 'ApiClient.name', 'ApiClient.created_at'],
            'order' => ['ApiClient.created_at' => 'DESC'],
            'limit' => 50,
        ]
    );

    public function isAuthorized()
    {
        return (
            in_array($this->Auth->user('user_type'), [User::TYPE_MANUFACTURER, User::TYPE_RETAILER], true) &&
            $this->Permissions->userHasPermission($this->Auth->user(), Permissions::NAME_API_SETTINGS) &&
            parent::isAuthorized()
        );
    }

    public function index()
    {
        $this->set('clients', $this->Paginator->paginate('ApiClient', ['ApiClient.user_id' => $this->Auth->user('id')]));
    }

    public function add()
    {
        if ($this->request->is('post')) {
            $data = [
                'user_id' => $this->Auth->user('id'),
                'name' => $this->request->data['ApiClient']['name'],
            ];
            $this->ApiClient->create();
            if ($this->ApiClient->save($data)) {
                return $this->_successResponse(__('The api client has been saved.'), ['controller' => 'api_clients', 'action' => 'edit', 'client_id' => $this->ApiClient->id]);
            } else {
                $this->setFlash(__('The api client could not be saved. Please, try again.'), 'error');
            }
        }
        return $this->render('form');
    }

    public function edit($clientId = null)
    {
        if (!$this->ApiClient->exists(['ApiClient.client_id' => $clientId])) {
            throw new NotFoundException(__('Invalid api client'));
        }
        if ($this->request->is(['post', 'put'])) {
            $this->request->data['ApiClient']['user_id'] = $this->Auth->user('id');

            if ($this->ApiClient->save($this->request->data)) {
                return $this->_successResponse(__('The api client has been saved.'));
            } else {
                $this->setFlash(__('The api client could not be saved. Please, try again.'), 'error');
            }
        } else {
            $this->request->data = $this->ApiClient->findByClientId($clientId);
        }
        $this->set('title', $this->ApiClient->field('name', ['client_id' => $clientId]));
        return $this->render('form');
    }

    public function delete($clientId = null)
    {
        if (!$this->ApiClient->exists(['ApiClient.client_id' => $clientId])) {
            throw new NotFoundException(__('Invalid api client'));
        }
        $this->request->allowMethod('post', 'delete');
        if (!$this->ApiClient->delete($clientId)) {
            $message = __('The api client could not be deleted. Please, try again.');
            $this->_exceptionResponse(new InternalErrorException($message), $message, true);
        }
        return $this->_successResponse(__('The api client has been deleted.'));
    }
}
