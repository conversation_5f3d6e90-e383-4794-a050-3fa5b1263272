<?php
/**
 * Application level Controller
 *
 * This file is application-wide controller file. You can put all
 * application-wide controller-related methods here.
 *
 * PHP 5
 *
 * CakePHP(tm) : Rapid Development Framework (http://cakephp.org)
 * Copyright (c) Cake Software Foundation, Inc. (http://cakefoundation.org)
 *
 * Licensed under The MIT License
 * For full copyright and license information, please see the LICENSE.txt
 * Redistributions of files must retain the above copyright notice.
 *
 * @copyright     Copyright (c) Cake Software Foundation, Inc. (http://cakefoundation.org)
 * @link          http://cakephp.org CakePHP(tm) Project
 * @package       app.Controller
 * @since         CakePHP(tm) v 0.2.9
 * @license       http://www.opensource.org/licenses/mit-license.php MIT License
 */

use ShipEarlyApp\Lib\Globals\AppGlobalMethodsTrait;
use ShipEarlyApp\Lib\Utility\SupportedLanguages;

App::uses('ShimController', 'Shim.Controller');
App::uses('User', 'Model');
App::uses('SubdomainRoute', 'Routing/Route');
App::uses('AppControllerLogResourceUsageTrait', 'Utility');
App::uses('Hash', 'Utility');
App::uses('CakeTime', 'Utility');
App::uses('OrderType', 'Utility');
App::uses('I18n', 'I18n');

/**
 * Application Controller
 *
 * Add your application-wide methods in the class below, your controllers
 * will inherit them.
 *
 * @package        app.Controller
 * @link        http://book.cakephp.org/2.0/en/controllers.html#the-app-controller
 *
 * @property AuthComponent $Auth
 * @property CookieComponent $Cookie
 * @property EmailComponent $Email
 * @property FlashComponent $Flash
 * @property NotificationLogicComponent $NotificationLogic
 * @property PermissionsComponent $Permissions
 * @property RequestHandlerComponent $RequestHandler
 * @property SessionComponent $Session
 * @property TranslateComponent $Translate
 *
 * @property B2bCart $B2bCart
 * @property Btask $Btask
 * @property Configuration $Configuration
 * @property Country $Country
 * @property Cron $Cron
 * @property ManufacturerRetailer $ManufacturerRetailer
 * @property Notification $Notification
 * @property Order $Order
 * @property Product $Product
 * @property RetailStaffLocation $RetailStaffLocation
 * @property State $State
 * @property User $User
 * @property UserCategories $UserCategories
 * @property UserSubdomain $UserSubdomain
 * @property WatchProduct $WatchProduct
 */
class AppController extends ShimController
{
    use AppGlobalMethodsTrait, AppControllerLogResourceUsageTrait;

    public $helpers = [
        'Html' => ['className' => 'AppHtml', 'configFile' => 'html5_tags.php'],
        'Form' => ['className' => 'AppForm'],
        'Url' => ['className' => 'Shim.UrlShim'],
    ];

    public $components = [
        'Shim.Shim',
        'Auth',
        'Cookie',
        'Email',
        'Flash',
        'NotificationLogic',
        'Permissions',
        'RequestHandler' => [
            'className' => 'Shim.RequestHandlerShim',
            'viewClassMap' => [
                'json' => 'Shim.JsonShim',
                'xml' => 'Xml',
            ],
        ],
        'Session',
        'Translate',
    ];

    public $uses = [
        'B2bCart',
        'Btask',
        'Configuration',
        'Country',
        'Cron',
        'ManufacturerRetailer',
        'Notification',
        'Order',
        'Product',
        'RetailStaffLocation',
        'State',
        'User',
        'UserCategories',
        'UserSubdomain',
        'WatchProduct',
    ];

    /**
     * The name of the View class this controller sends output to.
     *
     * @var string
     */
    public $viewClass = 'App';

    /**
     * The current user.
     *
     * @var array
     * @see AuthComponent::user
     */
    public $shipearly_user = [];

    public function __construct(CakeRequest $request = null, CakeResponse $response = null)
    {
        parent::__construct($request, $response);
        foreach (App::objects('Model') as $model) {
            App::uses($model, 'Model');
        }
    }

    /**
     * Callback for checking user authorization.
     *
     * @return bool Return a boolean to indicate whether or not the user is authorized.
     * @see AuthComponent::isAuthorized
     * @see ControllerAuthorize::authorize
     */
    public function isAuthorized()
    {
        return true;
    }

    /**
     *
     */
    public function beforeFilter()
    {
        $this->_defineConfigurations();

        $this->Auth->authorize = 'Controller';
        if ($this->request->is('ajax')) {
            $this->Auth->unauthorizedRedirect = false;
        }

        if ($this->request->param('prefix') === 'admin') {
            $this->_clearCache();

            $this->Auth->authenticate = $this->adminAuthenticationSettings();
            $this->Auth->loginAction = ['controller' => 'users', 'action' => 'admin_login', 'prefix' => 'admin', 'plugin' => null];
            $this->Auth->loginRedirect = ['controller' => 'users', 'action' => 'Manufacturer', 'prefix' => 'admin', 'plugin' => null];
        } else {
            $this->Auth->authenticate = [
                'Form' => [
                    'userModel' => 'User',
                    'fields' => ['username' => 'email_address', 'password' => 'password'],
                    //'scope' => ['User.status' => 'Active'],
                    'passwordHasher' => User::PASSWORD_HASHER_OPTIONS,
                    'recursive' => -1,
                ],
            ];
            $this->Auth->loginAction = ['controller' => 'users', 'action' => 'login', 'plugin' => null];
            $this->Auth->loginRedirect = ['controller' => 'dashboards', 'action' => 'index', 'plugin' => null];
            $this->Auth->authError = 'You need permissions to access this page';

            $this->_rememberMe();
            if ($this->Auth->user()) {
                $this->syncUserSession($this->Auth->user('id'));
                $this->defineLanguage();
                $this->_headerNotifications();
                $this->_checkCron();
                $this->_userValidation();
            }
        }
    }

    public function beforeRender()
    {
        parent::beforeRender();
        if ($this->request->param('prefix') !== 'admin') {
            // If reverted in the controller action; resolve again so that views
            // and elements must revert to act on the child user
            $this->setUserSession(User::resolveAuthParent($this->Auth->user()));

            $this->set('shipearly_logo_url', defined('SHIPEARLY_LOGO_URL') ? trim(SHIPEARLY_LOGO_URL) : '');

            $whitelabelUserId = (strtolower($this->request->param('plugin')) === 'shopify')
                ? (int)$this->Session->read('Shopify.User.User.id')
                : (int)$this->Auth->user('id');
            $this->set('subdomain_settings', $this->UserSubdomain->findWhitelabelSettings($whitelabelUserId));
        }
        $this->set('lang', $this->Translate->getHtmlLang((string)Configure::read('Config.language')));
    }

    public function afterFilter()
    {
        $this->setUserSession(User::revertAuthParent($this->Auth->user()));
        parent::afterFilter();

        $this->logResourceUsage('timing');
    }

    public function beforeRedirect($url, $status = null, $exit = true)
    {
        $this->setUserSession(User::revertAuthParent($this->Auth->user()));
        parent::beforeRedirect($url, $status, $exit);

        $this->logResourceUsage('timing');
    }

    public function referer($default = null, $local = false)
    {
        return $this->request->query('referer') ?: parent::referer($default, $local);
    }

    /**
     * Default Configuration values from Configuration table
     */
    public function _defineConfigurations()
    {
        Security::setHash('md5');

        // Set 'Config.language' before any Model TranslateBehavior callbacks
        $this->defineLanguage();
        $this->Configuration->bootstrap();

        $this->Cookie->name = APP_NAME;
        $this->Cookie->domain = '.' . SubdomainRoute::parseDomain(SubdomainRoute::getHost());
        $this->Cookie->secure = true;
        $this->Cookie->httpOnly = true;
    }

    /**
     * set the configuration for the requests language
     * @return void 
     * @throws InvalidArgumentException 
     */
    public function defineLanguage()
    {
        Configure::write('Config.language', $this->Translate->getPreferredLanguage());
    }

    /**
     * Empty shipearly cache.
     *
     * @deprecated since Version2.10. Call `APP/Console/cake cache clear_all` instead.
     */
    public function _clearCache()
    {
        if (Configure::read('debug') > 0 && isset($this->request->query['emptycache'])) {
            deprecationWarning("Version2.10 {$this->request->here()} is deprecated. Call `APP/Console/cake cache clear_all` instead.");

            // clear Cache::write() items
            $engines = Cache::configured();
            foreach ($engines as $engine) {
                Cache::clear(false, $engine);
            }
            // clear core cache
            $cachePaths = array('views', 'persistent', 'models');
            foreach ($cachePaths as $config) {
                clearCache(null, $config);
            }
            $this->setFlash("Cache cleared", 'info');
        }
    }

    protected function adminAuthenticationSettings(): array
    {
        return [
            'Form' => [
                'userModel' => 'Administrator',
                'fields' => ['username' => 'username', 'password' => 'password'],
                'passwordHasher' => User::PASSWORD_HASHER_OPTIONS,
                'recursive' => -1,
            ],
        ];
    }

    /**
     * Remember me feature
     */
    public function _rememberMe()
    {
        if (!$this->Auth->user()) {
            $cookie = $this->Cookie->read('remember_me');
            if (isset($cookie['email_address']) && isset($cookie['password'])) {
                $user = $this->User->find('first', [
                    'recursive' => -1,
                    'conditions' => [
                        'User.email_address' => $cookie['email_address'],
                        'User.password' => User::passwordHasher()->hash($cookie['password'])
                    ],
                    // Retrieve the bare minimum knowing that `syncUserSession` will be called after this
                    'fields' => ['id'],
                ]);

                if ($user && !$this->Auth->login($user['User'])) {
                    $this->redirect('/users/logout'); // destroy session & cookie
                }
            }
        }
    }

    /**
     * Read header notification counts from database
     */
    public function _headerNotifications()
    {
        $userId = (int)$this->Auth->user('id');
        $userType = (string)$this->Auth->user('user_type');

        $this->set('watch_count', $this->WatchProduct->getWatchProductIdsCount($this->_watchCondition()));
        $this->set('Notification_count', $this->Notification->getHeaderNotificationCount($userId));
        $this->set('Message_count', $this->Notification->getHeaderMessageCount($userId));

        $has_b2b_cart_permission = $this->Permissions->userHasB2bCartPermission($this->Auth->user());
        $this->set('has_b2b_cart_permission', $has_b2b_cart_permission);
        $this->set('b2b_cart_count', $has_b2b_cart_permission ? $this->B2bCart->countAllItems($userId, $userType) : 0);

        //Get Retailer count of pending retailers
        $this->set('retailer_status_total', $this->ManufacturerRetailer->countRetailerStatus($userId));

        //Purchase Order totals
        $this->set('purchase_order_totals', $this->Order->countPurchaseOrders($userId, $userType));

        //Brand Order totals
        $this->set('consumer_order_totals', $this->Order->countConsumerOrders($userId, $userType));
    }

    /**
     * Condition to retrieve watch product count
     * @return array
     */
    public function _watchCondition()
    {
        $userId = $this->Auth->user('id');
        $userType = $this->Auth->user('user_type');
        return ($userType == 'Manufacturer')
            ? array('WatchProduct.user_id' => $userId)
            : array('WatchProduct.retailer_id' => $userId);
    }

    /**
     * Flash message to manufactures about the initial Synchronization status.
     */
    public function _checkCron()
    {
        if (!$this->request->is('ajax')) {
            $this->set('showSyncBar', (
                $this->Auth->user('user_type') === 'Manufacturer' &&
                $this->Cron->isProcessing($this->Auth->user('id'))
            ));
        }
    }

    /**
     * can only view selected page listed in $allowedPages array Who are not complete their profiles.
     */
    public function _userValidation()
    {
        $allowedPages = [
            'profile_setup',
            'logout',
            'view',
            'profile',
            'editperson',
            'account_setting',
            'configuration',
            'shipment_setting',
            'shipment_dealer_options',
            'getheadernot',
            'ajax_popup',
            'change_password',
            'helps_index',
            'acc_subscription',
            'stripeconnect',
            'subscription',
            'lightSpeedList',
            'getLightspeedTaxRate',
            'storehours',
            'contact',
            'adddealer',
            'removedealer',
            'shopifypos',
            'lightSpeedConnect',
            'getAccountId',
            'getQwcFile',
            'vendPosConnect',
            'vendPosList',
            'squarePosList',
            'squareConnect',
            'getstates',
        ];
        if (in_array($this->request->param('action'), $allowedPages, true)) {
            return;
        }

        if ($this->shipearly_user['User']['setup_status'] != 1) {
            $this->redirect('/profile/setup');
        }

        if ($this->shipearly_user['User']['status'] === 'Suspend') {
            $this->redirect('/shipearly/subscription');
        }
    }

    /**
     * Just format the input from user profile page
     * @param $user
     * @deprecated Use User::formatAddress instead.
     */
    public function formatAddress(&$user)
    {
        $user = $this->User->formatAddress($user);
    }

    /**
     * Synchronize the user data with session as well on the template variable
     * @param $userId
     */
    public function syncUserSession($userId)
    {
        // If about to refresh the auth parent user then refresh and resolve the child user
        $authUser = $this->Auth->user();
        if (!empty($authUser['id']) && $authUser['id'] == $userId) {
            $userId = User::revertAuthParent($authUser)['id'];
        }

        $user = $this->User->findAuthUser($userId);
        if (empty($user['id'])) {
            $this->Auth->logout();

            throw new NotFoundException(json_encode(['User' => ['id' => $userId]]));
        }

        $this->setUserSession(User::resolveAuthParent($user));
    }

    /**
     * Assigns an auth user to the session and global view variable.
     *
     * @param array|null $authUser User fields after resolving the alias key.
     * @see AuthComponent::user
     */
    public function setUserSession(?array $authUser)
    {
        $this->Session->write('Auth.User', $authUser);
        $this->shipearly_user = !empty($authUser) ? ['User' => $authUser] : [];
        $this->set('shipearly_user', $this->shipearly_user);
    }

    /**
     * Common function to enqueue emails from shipearly app.
     *
     * @param array $mail_data EmailTemplate model.
     * @param string[] $variable_arr
     * @param string|string[] $to_email
     * @param string|string[] $cc_mail
     * @param string|string[] $bcc_mail
     * @param string[] $attachments
     * @param string $pdfUrl
     * @return bool
     * @see NotificationLogicComponent::sendEmail
     * @deprecated Use NotificationLogicComponent::sendEmail instead.
     */
    public function sendEmail($mail_data, $variable_arr, $to_email, $cc_mail = array(), $bcc_mail = array(), $attachments = array(), $pdfUrl = '')
    {
        return $this->NotificationLogic->sendEmail($mail_data, $variable_arr, $to_email, $cc_mail, $bcc_mail, $attachments, $pdfUrl);
    }

    /**
     * @param array $mailQueue
     * @return bool
     */
    protected function _sendQueuedEmail($mailQueue)
    {
        $adminEmail = MailQueue::FROM_ADMIN;

        $from_email = $mailQueue['MailQueue']['fromemail'];
        $to_email = $mailQueue['MailQueue']['to'];
        $cc_mail = $mailQueue['MailQueue']['cc'];
        $bcc_mail = $mailQueue['MailQueue']['bcc'];
        $logo = $mailQueue['MailQueue']['logo'];
        $email_subject = $mailQueue['MailQueue']['subject'];
        $email_content = $mailQueue['MailQueue']['content'];
        $attachments = $mailQueue['MailQueue']['attachments'];
        if (!empty($mailQueue['MailQueue']['generateUrl'])) {
            try {
                $this->requestAction($mailQueue['MailQueue']['generateUrl'], ['return']);
            } catch (Exception $e) {
                CakeLog::warning($e, 'email');
            }
        }

        $this->Email->reset();

        $this->Email->delivery = EMAIL_TRANSPORT_CLASS;

        if ($this->Email->delivery === 'smtp') {
            $this->Email->smtpOptions = [
                'host' => EMAIL_TRANSPORT_SMTP_HOST,
                'port' => EMAIL_TRANSPORT_SMTP_PORT,
                'username' => EMAIL_TRANSPORT_SMTP_USER,
                'password' => EMAIL_TRANSPORT_SMTP_PASS,
            ];
        }

        $this->Email->to = $to_email;
        $this->Email->cc = $cc_mail;
        $this->Email->bcc = $bcc_mail;

        if ($this->Email->delivery !== 'debug') {
            $this->Email->to = ['<EMAIL>'];
            $this->Email->cc = [];
            $this->Email->bcc = [];
        }

        $this->Email->subject = strip_tags($email_subject);

        $this->Email->from = preg_replace('/<[^>]+>/', '<' . SUPPORT_EMAIL . '>', $from_email);
        $this->Email->replyTo = $from_email;

        $this->Email->template = ($from_email == $adminEmail)
            ? 'email_template'
            : 'email_template_without_header_and_footer';
        $this->Email->sendAs = 'html';
        if (!empty($attachments)) {
            $this->Email->attachments = array_map(function($attachment) {
                return str_replace(BASE_PATH, WWW_ROOT, $attachment);
            }, $attachments);
        }
        $this->set('mail_data', $email_content);
        $this->set('logo', $logo);
        $this->set('skipNewlineConversion', EmailTemplate::getSkipNewlineConversion($email_content));

        $email = $this->Email->send();
        CakeLog::debug(__METHOD__ . ', line ' . __LINE__ . ' - ' . 'Email sent ' . json_encode($email), 'email');

        if ($this->Email->delivery === 'debug') {
            $emailDumpPath = (MAIN_BASE_DIR_PATH . 'tmp/emaildumps/');
            if (!is_dir($emailDumpPath)) {
                mkdir($emailDumpPath, 0777, true);
            }
            $toEmails = implode('_', $to_email);
            file_put_contents($emailDumpPath . "lastEmailTo_{$toEmails}.log", json_encode($email));
            file_put_contents($emailDumpPath . "lastEmailTo_{$toEmails}.html", $this->Email->htmlMessage);
        }

        return (bool)$email;
    }

    /**
     * Get lat & log using the given address
     *
     * @param string $street Line 1 of the street address. Line 2 should not be included.
     * @param string $city
     * @param string $zip
     * @param string|int $stateName
     * @param string|int $countryCode Country ISO 3166-1 alpha-2 code or full name.
     * @param int $level
     * @return array{lat: float, lng: float}|null Geocode array with keys 'lat' and 'lng', or `null` on failure
     * @see findGeocode()
     * @deprecated Use findGeocode() instead.
     */
    function _getLnt($street, $city, $zip, $stateName, $countryCode, int $level = 1): ?array
    {
        $originalCountryStateArgs = compact('stateName', 'countryCode');

        if (is_numeric($stateName)) {
            $stateName = $this->State->getStateName($stateName, $countryCode);
        }
        if (is_numeric($countryCode)) {
            $countryCode = $this->Country->getCountryCode((int)$countryCode);
        }

        $countryStateArgs = compact('stateName', 'countryCode');
        $deprecatedCountryStateArgs = array_diff($originalCountryStateArgs, $countryStateArgs);
        if ($deprecatedCountryStateArgs) {
            CakeLog::warning(sprintf(
                "Arguments %s should be provided as %s.\nStack Trace:\n%s",
                json_encode($deprecatedCountryStateArgs),
                json_encode(array_intersect_key($countryStateArgs, $deprecatedCountryStateArgs)),
                (new Exception())->getTraceAsString()
            ));
        }

        return findGeocode((string)$street, (string)$city, (string)$zip, (string)$stateName, (string)$countryCode, $level);
    }

    /**
     * Custom log with WebService as the log level.
     *
     * @param mixed $msg
     * @return bool
     * @deprecated Use CakeLog::debug configured with AppFileLog instead.
     */
    public function _webServiceLog($msg)
    {
        if (!is_string($msg)) {
            $msg = json_encode($msg);
        }

        return CakeLog::debug($msg, 'webService');
    }

    /**
     * Custom log with WebhookService as the log level.
     *
     * @param mixed $msg
     * @return bool
     * @deprecated Use CakeLog::debug configured with AppFileLog instead.
     */
    public function _webhookServiceLog($msg)
    {
        return CakeLog::debug($msg, 'webhookService');
    }

    /**
     * @param float $startTime
     * @param string $message
     * @param int|string $level
     * @param string|string[] $scope
     * @return float The new $startTime for the next call.
     */
    public function _logDeltaTime($startTime, $message, $level = 'timing', $scope = [])
    {
        $endTime = microtime(true);
        $delta = number_format($endTime - $startTime, 6);

        CakeLog::write($level, json_encode(compact('delta', 'message')), $scope);

        return $endTime;
    }

    /**
     * Read the current flash message by type without removing it from the session.
     *
     * @param string $type enum('success', 'error', 'info').
     * @return string Message
     * @see SessionHelper::flash
     */
    public function getFlash($type = 'success')
    {
        // Combine all flash messages to emulate CakePHP 2.7
        return implode('', array_column((array)$this->Session->read('Message.' . $type), 'message'));
    }

    /**
     * Set a flash message to display in display_message.ctp.
     *
     * @param string $message
     * @param string $type enum('success', 'error', 'info').
     * @see SessionComponent::setFlash
     */
    public function setFlash($message, $type = 'success')
    {
        // Overwrite all flash messages to emulate CakePHP 2.7
        $this->Session->write('Message.' . $type, [['message' => $message, 'element' => false, 'params' => []]]);
    }

    /**
     * To get current currency value for manufactures report calculation
     * @param $amount
     * @param $from_Currency
     * @param $to_Currency
     * @return mixed
     */
    public function currencyConversion($amount, $from_Currency, $to_Currency)
    {
        if (!empty($from_Currency) && $from_Currency === $to_Currency) {
            return $amount;
        }

        // Both of these APIs are dead. We need to evaluate if this is needed any longer and remove it or fix it.
        $url = "https://www.google.com/finance/converter" . "?a=$amount&from=$from_Currency&to=$to_Currency";
        $content = $this->cUrlResponse($url);
        if (preg_match("/<span class=bld>/", $content)) {
            $get = explode("<span class=bld>", $content);
            $get = explode("</span>", $get[1]);
            return floatval(substr($get[0], 0, -4));
        } else {
            return $this->yahooCurrencyApi($amount, $from_Currency, $to_Currency);
        }
    }

    /**
     * Just return the curl response for the given URL.
     * @param $url
     * @return mixed
     */
    public function cUrlResponse($url)
    {
        $ch = curl_init();
        // Set query data here with the URL
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        $content = curl_exec($ch);
        curl_close($ch);
        return $content;
    }

    /**
     * Alternative for google currency conversion api.
     * @param $from_Currency
     * @param $to_Currency
     * @return mixed
     */
    public function yahooCurrencyApi($from_Currency, $to_Currency)
    {
        $currency = $from_Currency . $to_Currency;
        $url = "http://query.yahooapis.com/v1/public/yql?q=" . urlencode("select * from yahoo.finance.xchange where pair in ('" . $currency . "')") . "&env=store://datatables.org/alltableswithkeys&format=json";
        $content = $this->cUrlResponse($url);
        $result = json_decode($content);
        if (isset($result->query->results->rate->Rate)) {
            return $result->query->results->rate->Rate;
        }
    }

    /**
     * Format the order link using order details
     * @param $orderID
     * @param $id
     * @return string
     * @see NotificationLogicComponent::makeOrderLink
     * @deprecated Use NotificationLogicComponent::makeOrderLink instead.
     */
    public function makeOrderLink($orderID, $id)
    {
        return $this->NotificationLogic->makeOrderLink($orderID, $id);
    }

    /**
     * Set country, state, and timezone view variables.
     *
     * @param int $countryId
     */
    public function getCountryState($countryId)
    {
        $this->set('country', $this->Country->getCountryList());
        $this->set('states', $this->State->getStateList($countryId));
        $this->set('timezoneOptions', $this->getTimezoneOptions($countryId));
    }


    /**
     * Set language options and selected language view vars.
     * Prefering language set in authUser and falling back to browser configured
     *
     */
    public function getLanguages()
    {
        $this->set('languageOptions', SupportedLanguages::getSelfTranslatedOptions());
        $this->set('selectedLanguage', $this->Translate->getHtmlLang((string)Configure::read('Config.language')));
    }

    public function getTimezoneOptions($countryId = null): array
    {
        $timeFormat = 'T g:i A ' . DATE_FORMAT;
        if ($countryId) {
            $countryCode = $this->Country->getCountryCode($countryId);
            $timezones = array_reduce(
                CakeTime::listTimezones(DateTimeZone::PER_COUNTRY, $countryCode, ['group' => true]),
                function($map, $tzGroup) { return $map + $tzGroup; },
                array()
            );
            $timezones = array_map(function($region) { return str_replace('_', ' ', $region); }, $timezones);
        } else {
            $timezones = CakeTime::listTimezones(null, null, ['group' => false]);
        }
        $timezoneOptions = array();
        $dateTime = new DateTime();
        foreach ($timezones as $timezone => $region) {
            $formattedTime = $dateTime->setTimezone(new DateTimeZone($timezone))->format($timeFormat);
            $timezoneOptions[$timezone] = $region . ' - ' . $formattedTime;
        }
        return $timezoneOptions;
    }

    /**
     * Format the customer address based on the available input
     * @param array $order
     * @return string
     */
    public function _formatOrderCustomerAddress(array $order): string
    {
        $customerName = $order['Order']['customer_name'];
        if ($order['Order']['order_type'] === OrderType::WHOLESALE && $order['Order']['b2b_ship_to_user_id']) {
            $customerUrl = Router::url(['controller' => 'users', 'action' => 'contact', 'id' => $order['Order']['b2b_ship_to_user_id']]);
            $customerName = "<a href=\"{$customerUrl}\" target=\"_blank\">{$customerName}</a>";
        } elseif ($this->Auth->user('user_type') !== User::TYPE_MANUFACTURER) {
            $customerUrl = Router::url(['controller' => 'orders', 'action' => 'message_customer', $order['Order']['id']]);
            $customerName = "<a href=\"{$customerUrl}\" class=\"ajax-message-customer\">{$customerName}</a>";
        } elseif ($order['Order']['customerID']) {
            $customerUrl = BASE_PATH . "customer/{$order['Order']['customerID']}/orders";
            $customerName = "<a href=\"{$customerUrl}\" target=\"_blank\">{$customerName}</a>";
        }

        $address = $this->User->addressDisplayFormat([
            'company_name' => $customerName,
            'address' => $order['Order']['shipping_address1'],
            'address1' => $order['Order']['shipping_address1'],
            'address2' => $order['Order']['shipping_address2'],
            'city' => $order['Order']['shipping_city'],
            'zipcode' => $order['Order']['shipping_zipcode'],
            'state_id' => $order['Order']['shipping_state'],
            'country_id' => $order['Order']['shipping_country'],
            'telephone' => $order['Order']['shipping_telephone'],
        ]);
        if ($order['Order']['latitude'] && $order['Order']['longitude']) {
            $mapUrl = "https://www.google.com/maps/?q={$order['Order']['latitude']},{$order['Order']['longitude']}";
            $address .= '<br />' . "<a href=\"{$mapUrl}\" target=\"_blank\">" . __('View Map') . "</a>";
        }

        return $address;
    }

    /**
     * Format the address field on user table.
     * @param $address1
     * @param $address2
     * @return string
     * @deprecated Use User::getCombinedAddressField instead.
     */
    public function setFormatAddress($address1, $address2)
    {
        return $this->User->getCombinedAddressField($address1, $address2);
    }

    /**
     * Format the address based on the available input
     * @param $user
     * @return string
     * @deprecated Use User::addressDisplayFormat instead.
     */
    public function addressDisplayFormat($user)
    {
        return $this->User->addressDisplayFormat($user);
    }

    /**
     * Format the customer address based on the available input
     * @param string $companyname
     * @param string $address1
     * @param string $address2
     * @param string $city
     * @param string $state
     * @param string $country
     * @param string $zipcode
     * @param string $telephone
     * @return string
     * @see NotificationLogicComponent::emailAddressDisplayFormat
     * @deprecated Use NotificationLogicComponent::emailAddressDisplayFormat instead.
     */
    public function emailAddressDisplayFormat($companyname = '', $address1 = '', $address2 = '', $city = '', $state = '', $country = '', $zipcode = '', $telephone = '')
    {
        return $this->NotificationLogic->emailAddressDisplayFormat($companyname, $address1, $address2, $city, $state, $country, $zipcode, $telephone);
    }

    /**
     * Format the input address.
     *
     * @param array $user
     * @return array
     * @deprecated Use User::formatAddress instead.
     */
    public function emailFormatAddress($user)
    {
        return $this->User->formatAddress($user);
    }

    /**
     * Formating order id with Shipearly Format
     * @param $orderID
     * @param $prefix
     * @return string
     * @deprecated Replaced by $this->Order->formatOrderId
     */
    public function formatOrderId($orderID, $prefix = true)
    {
        return $this->Order->formatOrderId($orderID, $prefix);
    }

    public function getAuthUserIds()
    {
        $authUser = $this->User->revertAuthParent($this->Auth->user());
        if($authUser['user_type'] === User::TYPE_STAFF){
            return $this->RetailStaffLocation->getLocationsForId($authUser['id']);
        }

        return $this->Auth->user('id');
    }

    public function isRetailStaffUser()
    {
        return $this->User->revertAuthParent($this->Auth->user())['user_type'] === User::TYPE_STAFF;
    }

    protected function _listValidationKeysByErrorMessage(array $validationErrors): array
    {
        $keysByError = [];
        foreach ($validationErrors as $key => $fieldErrors) {
            foreach (array_unique(Hash::flatten($fieldErrors)) as $error) {
                $keysByError[$error][] = $key;
            }
        }

        return $keysByError;
    }

    /**
     * @param ForbiddenException $e
     * @param array|null|string $redirectUrl A string or array-based URL. Defaults to the referring URL.
     * @param bool|string $log Whether or not to log the exception or a provided log message.
     * @param null|string $flashMessage
     * @return CakeResponse|null
     */
    protected function _permissionDeniedResponse(ForbiddenException $e, $redirectUrl = null, $log = true, $flashMessage = null)
    {
        if ($flashMessage === null) {
            // Display no message by default to match the current behaviour of Controller::isAuthorized
            // otherwise, the value of $this->Auth->authError would be appropriate
            $flashMessage = $this->getFlash('error') ?: '';
        }

        return $this->_exceptionResponse($e, $flashMessage, $log, $redirectUrl);
    }

    /**
     * @param array $validationErrors Multi-dimensional array of error messages from Model::$validationErrors.
     * @param bool|string $log Whether or not to log the exception or a provided log message.
     * @param array|null|string $redirectUrl A string or array-based URL. Defaults to the referring URL.
     * @return CakeResponse|null
     */
    protected function _validationErrorFlashResponse(array $validationErrors, $log = false, $redirectUrl = null)
    {
        $validationErrorFlash = implode('<br />', array_unique(Hash::flatten($validationErrors)));

        return $this->_exceptionResponse(new BadRequestException(json_encode($validationErrors)), $validationErrorFlash ?: null, $log, $redirectUrl);
    }

    /**
     * @param null|string $flashMessage
     * @param array|null|string $redirectUrl A string or array-based URL. Defaults to the referring URL.
     * @return CakeResponse|null
     */
    protected function _successResponse($flashMessage = null, $redirectUrl = null)
    {
        $flashMessage = $this->_buildFlashMessage('success', $flashMessage);

        if ($this->request->is('ajax')) {
            if (empty($this->response->body())) {
                // Arg for the JS displayFlash method
                $this->response->body(json_encode(['success' => h($flashMessage ?: true)]));
            }
            return $this->response;
        }

        if (!empty($flashMessage)) {
            $this->setFlash($flashMessage, 'success');
        }

        if ($redirectUrl === null) {
            $redirectUrl = $this->referer(null, true);
        }
        return $this->redirect($redirectUrl);
    }

    /**
     * @param HttpException|null $e
     * @param null|string $flashMessage
     * @param bool|string $log
     * @param array|null|string $redirectUrl A string or array-based URL. Defaults to the referring URL.
     * @return CakeResponse|null
     */
    //TODO leverage an extension of CakePHP's ExceptionRenderer class
    protected function _exceptionResponse(HttpException $e = null, $flashMessage = null, $log = false, $redirectUrl = null)
    {
        if (empty($e)) {
            $e = new InternalErrorException();
        }

        if ($log === true) {
            $log = $e;
        }
        if (!empty($log)) {
            CakeLog::error($log);
        }

        $flashMessage = $this->_buildFlashMessage('error', $flashMessage);

        if ($this->request->is('ajax')) {
            $code = $this->response->statusCode();
            if ($code < 400 || !$this->response->httpCodes($code)) {
                $code = $e->getCode();
                if ($code < 400 || !$this->response->httpCodes($code)) {
                    $code = 500;
                }
                $this->response->statusCode($code);
            }
            if (!$this->response->body()) {
                $this->response->body(json_encode([
                    // Arg for the JS displayFlash method
                    'error' => h($flashMessage ?: true),
                    // Body based on ExceptionRenderer::error500
                    'name' => h(current($this->response->httpCodes($code))),
                    'message' => h($flashMessage),
                    'url' => h($this->request->here()),
                ]));
            }
            return $this->response;
        }

        if (!empty($flashMessage)) {
            $this->setFlash($flashMessage, 'error');
        }

        if ($redirectUrl === null) {
            $redirectUrl = $this->referer(null, true);
        }
        return $this->redirect($redirectUrl);
    }

    /**
     * @param string $type enum('success', 'error', 'info').
     * @param string|null $flashMessage
     * @return mixed|string
     */
    protected function _buildFlashMessage(string $type, ?string $flashMessage = null)
    {
        if ($flashMessage === null) {
            $flashMessage = $this->getFlash($type);
            if (!$flashMessage) {
                if ($type === 'success') {
                    $flashMessage = __('Update successful');
                } elseif ($type === 'error') {
                    $flashMessage = __('An error occurred. Please, try again.');
                }
            }
        }

        return $this->_appendDebugOutput($flashMessage);
    }

    /**
     * Append any generated CakePHP debug outputs to the provided message string.
     *
     * Non-string messages are coerced if and only if there is a debug message to append.
     *
     * @param mixed|string $message
     * @return mixed|string The message as a string with debugs appended, or the original input if no debugs exist.
     */
    protected function _appendDebugOutput($message = '')
    {
        if (Configure::read('debug') > 0) {
            $debug = ob_get_contents();
            if (!empty($debug)) {
                return strval($message) . $debug;
            }
        }
        return $message;
    }


    protected function _ajaxSuccessResponse($message)
    {
        $response = [
            'status' => 'success',
            'message' => $message
        ];
        $this->set($response);
        $this->set('_serialize', array_keys($response));
    }
    protected function _ajaxErrorResponse($message = null)
    {
        if($message === null){
            $message = 'An error occurred.';
        }
        $response = [
            'status' => 'error',
            'message' => $message
        ];

        $this->set($response);
        $this->set('_serialize', array_keys($response));
    }

}
