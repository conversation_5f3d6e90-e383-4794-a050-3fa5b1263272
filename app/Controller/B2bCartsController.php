<?php

use ShipEarlyApp\Lib\Integrations\Klaviyo\KlaviyoFactory;
use ShipEarlyApp\Lib\Utility\UserSiteType;

App::uses('AppController', 'Controller');
App::uses('UserFriendlyException', 'Error');
App::uses('B2bCart', 'Model');
App::uses('B2bCartType', 'Utility');
App::uses('OrderPaymentMethod', 'Utility');

/**
 * B2bCarts Controller
 *
 * @property NotificationLogicComponent $NotificationLogic
 * @property OrderPlacerComponent $OrderPlacer
 * @property ShippingCalculatorComponent $ShippingCalculator
 * @property ShopifyComponent $Shopify
 * @property StripeComponent $Stripe
 * @property WoocommerceComponent $Woocommerce
 *
 * @property B2bCart $B2bCart
 * @property B2bCartProduct $B2bCartProduct
 * @property B2bShippingZone $B2bShippingZone
 * @property Country $Country
 * @property CreditTerm $CreditTerm
 * @property DealerOrder $DealerOrder
 * @property DealerOrderProduct $DealerOrderProduct
 * @property Discount $Discount
 * @property EmailTemplate $EmailTemplate
 * @property ManufacturerRetailer $ManufacturerRetailer
 * @property Notification $Notification
 * @property Order $Order
 * @property OrderProduct $OrderProduct
 * @property Product $Product
 * @property LegacyRetailerCredit $LegacyRetailerCredit
 * @property RetailerCredit $RetailerCredit
 * @property RetailerCreditTerm $RetailerCreditTerm
 * @property State $State
 * @property StripeUser $StripeUser
 * @property UploadComponent $Upload
 * @property User $User
 * @property WarehouseProductReservation $WarehouseProductReservation
 */
class B2bCartsController extends AppController
{
    public $components = [
        'NotificationLogic',
        'OrderPlacer',
        'ShippingCalculator',
        'Shopify.Shopify',
        'Stripe.Stripe',
        'Woocommerce.Woocommerce',
        'Upload',
    ];

    public $uses = [
        'B2bCart',
        'B2bCartProduct',
        'B2bShippingZone',
        'Country',
        'CreditTerm',
        'DealerOrder',
        'DealerOrderProduct',
        'Discount',
        'EmailTemplate',
        'ManufacturerRetailer',
        'Notification',
        'Order',
        'OrderProduct',
        'Product',
        'LegacyRetailerCredit',
        'RetailerCredit',
        'RetailerCreditTerm',
        'State',
        'StripeUser',
        'User',
        'WarehouseProductReservation',
    ];

    public function isAuthorized()
    {
        if (!parent::isAuthorized()) {
            return false;
        }

        return (
            ($this->Permissions->userHasB2bCartPermission($this->Auth->user()))
            ||in_array($this->request->param('action'), ['index', 'view'])
        );
    }

    public function index()
    {
        $userType = (string)$this->Auth->user('user_type');

        $retailerFilter = $this->request->query('retailerFilter');
        if($this->isRetailStaffUser()){
            $retailerFilter = $retailerFilter ?? $this->getAuthUserIds();
        }
        $b2bCartTypeFilter = $this->request->query('b2bCartTypeFilter');
        $calculateShipping = $this->getDefaultCalculateShippingFunction();
        $b2bCarts = $this->B2bCart->findIndex((int)$this->Auth->user('id'), $calculateShipping, $userType, null, $retailerFilter, $b2bCartTypeFilter);
        //when we only have one cart as a result of a filter we do not want to redirect.
        if (!($retailerFilter || $b2bCartTypeFilter) && count($b2bCarts) === 1) {
            $b2bCart = current($b2bCarts);
            $this->redirect(['action' => 'view', 'id' => $b2bCart['B2bCart']['id']]);
        }
        $this->set('b2bCarts', $b2bCarts);
        $this->set('userAlias', $this->_userAlias($userType));
        $this->set('title_for_layout', __('Draft Orders'));
        $this->set('can_edit', $this->_canEdit($this->Auth->user()));
        $retailerOptions = in_array($userType, [User::TYPE_SALES_REP, User::TYPE_MANUFACTURER]) ? $this->B2bCart->getRetailersWithCarts($this->Auth->user('id'), $userType) : [];
        $this->set('retailerOptions', $retailerOptions);
        $this->set('b2bCartTypeOptions', array_combine(B2bCartType::getB2bCartTypes(), B2bCartType::getB2bCartTypes()));
    }

    public function view($id = null)
    {
        $this->_validateCartId($id);

        $userId = (int)$this->Auth->user('id');
        $userType = (string)$this->Auth->user('user_type');
        $userAlias = $this->_userAlias($userType);

        $calculateShipping = $this->getDefaultCalculateShippingFunction();
        $b2bCart = $this->B2bCart->findView($id, $calculateShipping);

        $brandId = (int)$b2bCart['B2bCart']['user_id'];
        $retailerId = (int)$b2bCart['B2bCart']['retailer_id'];
        $branchId = (int)$b2bCart['B2bCart']['branch_id'];
        $is_brand = ($userId === $brandId);

        $productsUrl = $this->_getCatalogueUrl($userId, $userType, $id, $brandId, $branchId);
        $shipToUserById = $this->User->findAllB2bShipToUsersById($brandId, $retailerId);

        $orderTypeText = B2bCartType::getOrderTypeLabel($b2bCart['B2bCart']['order_type']);

        $storeConnection = $this->ManufacturerRetailer->getStoreConnection($brandId, $retailerId, [
            'pricingtierid',
        ]);

        $this->set('userAlias', $userAlias);
        $this->set('title_for_layout', __($b2bCart[$userAlias]['company_name'] . ' ' . $orderTypeText));
        $this->set('b2bCart', $b2bCart);
        $this->set('productsUrl', $productsUrl);
        $this->set('branchOptions', array_column($shipToUserById, 'company_name', 'id'));
        $this->set('branchAddresses', array_map([$this->User, 'addressDisplayFormat'], $shipToUserById));
        $this->set('country', $this->Country->getCountryList());
        $this->set('states', $this->State->getStateList($b2bCart['B2bCart']['country_id'] ?? $b2bCart['B2bShipToAddress']['country_id']));
        $this->set('remainingCredit', $this->LegacyRetailerCredit->fetchRemainingBalance($brandId, $retailerId));
        $canEdit = $this->_canEdit($this->Auth->user());
        $this->set('can_edit', $canEdit);
        $this->set('brandEdit', ($is_brand && $canEdit));
        $this->set('minimumRequired', (!$is_brand && $b2bCart['B2bCart']['minimum_subtotal'] > 0));
        $this->set('accountId', $b2bCart['ManufacturerRetailerBranch']['external_retailer_account'] ?? $b2bCart['ManufacturerRetailer']['external_retailer_account'] ?? '');
        $this->set('shippingRateOptions', $this->ShippingCalculator->findB2bCartRateOptions($b2bCart));
        $this->set('shippingData', $this->B2bShippingZone->getFreeShippingBarData($brandId, $storeConnection['pricingtierid']));
    }

    public function ajax_calculate_totals($id = null)
    {
        $this->autoRender = false;
        $this->_validateCartId($id);
        $this->request->allowMethod('post', 'put');

        $b2bCartProducts = $this->_filterB2bCartProductsForEditing($id, $this->request->data('B2bCartProduct'));
        if (empty($b2bCartProducts)) {
            return $this->_exceptionResponse(
                new BadRequestException(json_encode(['data' => $this->request->data]))
            );
        }

        $calculateShipping = function($b2bCart) {
            $manualShipping = $this->request->data('B2bCart.shipping_amount');
            if (is_numeric($manualShipping)) {
                return $manualShipping;
            }
            return $this->getDefaultCalculateShippingFunction()($b2bCart);
        };

        $totalQuantity = 0;
        foreach ($b2bCartProducts as $product) {
            $totalQuantity += (int)$product['quantity'];
        }
        $totalQtyWithLabel = __n('%d item', '%d items', $totalQuantity, $totalQuantity);

        $discount = (float)$this->request->data('B2bCart.manual_discount');
        $b2bCart = $this->B2bCart->calculateTotals((int)$id, $calculateShipping, $discount, $b2bCartProducts);
        $b2bCart = array_intersect_key($b2bCart, array_flip(['B2bCart', 'B2bCartProduct']));
        $b2bCart['totalQtyWithLabel'] = $totalQtyWithLabel;

        $this->response->body(json_encode($b2bCart));
        return $this->response;
    }

    public function save_ship_to_address($id = null): ?CakeResponse
    {
        return $this->ajax_save_field((int)$id, fn(B2bCart $B2bCart, int $id): bool => $B2bCart->editAddress(
            $id,
            $this->request->data('B2bCart.ship_to_user_id')
                ?: $this->request->data('B2bShipToAddress')
        ));
    }

    public function save_requested_ship_date($id = null): ?CakeResponse
    {
        return $this->ajax_save_field((int)$id, fn(B2bCart $B2bCart, int $id): bool => (bool)$B2bCart->save([
            'id' => $id,
            'requested_ship_date' => format_from_datepicker($this->request->data['B2bCart']['requested_ship_date']),
        ]));
    }

    public function save_purchase_order_number($id = null): ?CakeResponse
    {
        return $this->ajax_save_field((int)$id, fn(B2bCart $B2bCart, int $id): bool => (bool)$B2bCart->save([
            'id' => $id,
            'purchase_order_number' => $this->request->data['B2bCart']['purchase_order_number'],
        ]));
    }

    public function save_notes($id = null): ?CakeResponse
    {
        return $this->ajax_save_field((int)$id, fn(B2bCart $B2bCart, int $id): bool => (bool)$B2bCart->save([
            'id' => $id,
            'notes' => $this->request->data['B2bCart']['notes'],
        ]));
    }

    /**
     * General handler for AJAX endpoints requested by the front end method `ajax_save_field()`.
     *
     * @param int $id
     * @param callable(B2bCart $B2bCart, int $id): bool $saveField
     * @return CakeResponse|null
     */
    private function ajax_save_field(int $id, callable $saveField): ?CakeResponse
    {
        $this->_validateCartId($id);
        $this->request->allowMethod('post', 'put');

        if (!$saveField($this->B2bCart, $id)) {
            return $this->_validationErrorFlashResponse($this->B2bCart->validationErrors);
        }

        $userId = (int)$this->B2bCart->field('user_id', ["{$this->B2bCart->alias}.id" => $id]);
        try {
            $klaviyo = KlaviyoFactory::new($this->User, $userId);
            $klaviyo->trackB2bCartUpdated($this->B2bCart, $this->User, $id, $this->getDefaultCalculateShippingFunction());
        } catch (\GuzzleHttp\Exception\GuzzleException|Exception $e) {
            CakeLog::warning($e, ['klaviyo']);
        }

        return $this->_successResponse();
    }

    public function payment_method($id = null)
    {
        $this->_validateCartId($id);
        $b2bCartProducts = $this->_filterB2bCartProductsForEditing($id, $this->request->data('B2bCartProduct'));
        $this->request->allowMethod('post', 'put');

        if (empty($b2bCartProducts)) {
            return $this->_exceptionResponse(
                new BadRequestException(json_encode(['data' => $this->request->data]))
            );
        }

        // save products to update saved quantity
        if (!$this->B2bCart->editProducts($id, $b2bCartProducts)) {
            return $this->_exceptionResponse(
                new InternalErrorException(json_encode(['errors' => $this->B2bCart->validationErrors])),
                'The cart could not be modified. Please, try again.', // TODO more accurate error message
                true
            );
        }

        $calculateShipping = function($b2bCart) {
            $manualShipping = $this->request->data('B2bCart.shipping_amount');
            if (is_numeric($manualShipping)) {
                return $manualShipping;
            }
            return $this->getDefaultCalculateShippingFunction()($b2bCart);
        };
        $discount = (float)$this->request->data('B2bCart.manual_discount');

        $b2bCart = $this->B2bCart->findView($id, $calculateShipping, $discount);
        $brandId = (int)$b2bCart['B2bCart']['user_id'];
        $retailerId = (int)$b2bCart['B2bCart']['retailer_id'];

        $addressOption = $this->request->data('B2bCart.ship_to_user_id') ?: null;
        $b2bShipToAddress = $this->request->data('B2bShipToAddress');
        $address = $this->_getB2bOrderAddress($brandId, $addressOption, $b2bShipToAddress);

        $paymentMethodOptions = [];

        $stripeAccount = $this->StripeUser->getAccountId($brandId);

        $customerSessionClientSecret = null;
        if ($b2bCart['ManufacturerRetailer']['enable_b2b_credit_card']) {
            try {
                $customerSession = $this->_initStripeCustomerSession($brandId, (array)$b2bCart['Retailer'], $address, $stripeAccount);
                $customerSessionClientSecret = $customerSession->client_secret;
            } catch (\Stripe\Exception\ApiErrorException $e) {
                CakeLog::warning($e);
            }

            $paymentMethodOptions[OrderPaymentMethod::STRIPE] = OrderPaymentMethod::getLabel(OrderPaymentMethod::STRIPE);
        }

        $creditTermOptions = $this->getCreditTermOptions($id, $brandId, $retailerId, $b2bCart);
        if ($creditTermOptions) {
            $availableCredit = $this->RetailerCredit->fetchAvailableCredit($brandId, $retailerId);
            $paymentMethodOptions[OrderPaymentMethod::CREDIT] = OrderPaymentMethod::getLabel(OrderPaymentMethod::CREDIT) . " " . __('(%s Remaining)', $availableCredit);
        }

        if ($b2bCart['ManufacturerRetailer']['enable_b2b_external_payment']) {
            $paymentMethodOptions[OrderPaymentMethod::EXTERNAL] = OrderPaymentMethod::getLabel(OrderPaymentMethod::EXTERNAL);
        }

        $this->set('title_for_layout', __('Please select a payment method'));
        $this->set('b2bCart', $b2bCart);
        $this->set('address', $address);
        $this->set('paymentMethodOptions', $paymentMethodOptions);
        $this->set('stripeAccount', $stripeAccount);
        $this->set('customerSessionClientSecret', $customerSessionClientSecret);
        $this->set('creditTermOptions', $creditTermOptions);
    }

    /**
     * Endpoint for adding products to cart.
     *
     * @param int|null $branchId
     * @return CakeResponse|null
     */
    public function add($branchId = null)
    {
        $this->request->allowMethod('post');

        $userId = (int)$this->Auth->user('id');
        $userType = (string)$this->Auth->user('user_type');

        $brandId = $this->request->query('brandId');
        $cartId = $this->request->query('cartId');
        $discountId = $this->request->query('discountId');

        $existing = $this->B2bCart->findForAddToCart($cartId);
        if (!empty($existing['B2bCart']['id'])) {
            $brandId = $existing['B2bCart']['user_id'];
            $branchId = $existing['B2bCart']['branch_id'];
            $discountId = $existing['B2bCart']['discount_id'];
        } else {
            $cartId = null;
        }
        $createCart = !(bool)$cartId;

        try {
            $branchId = $this->_validateBranchIdForAdding($branchId);
            $brandId = $this->_validateBrandIdForAdding($brandId, $branchId);
        } catch (HttpException $e) {
            $messageArray = (array)json_decode($e->getMessage(), true);

            return $this->_exceptionResponse($e, $messageArray['message'] ?? null, true);
        }

        $retailerId = $this->User->getMainRetailerId($branchId);

        $b2bCartProducts = $this->_filterB2bCartProductsForAdding($this->request->data('B2bCartProduct'));
        if (empty($b2bCartProducts)) {
            $message = 'Unable to add to cart: no valid products selected';
            return $this->_exceptionResponse(
                new BadRequestException(json_encode(['message' => $message, 'data' => $this->request->data])),
                $message,
                true
            );
        }

        $b2bCartProducts = $this->_setDefaultProductWarehouse($brandId, $retailerId, $b2bCartProducts);
        if (
            array_has_any($b2bCartProducts, fn(array $item): bool => !$item['warehouse_id'])
            && $this->User->field('site_type', ['id' => $brandId]) === UserSiteType::SHOPIFY
        ) {
            $message = 'Unable to add to cart: brand has no default warehouse';
            $users = Hash::combine(
                $this->User->findAllById(
                    [$brandId, $retailerId],
                    ['id', 'email_address', 'user_type'],
                    'user_type', null, 1, -1
                ),
                '{n}.User.user_type', '{n}.User'
            );
            return $this->_exceptionResponse(
                new InternalErrorException(json_encode(compact('message', 'users'))),
                ($message . '. Please select a specific brand and warehouse then try again.'),
                true
            );
        }

        if($createCart){
            $cartId = $this->B2bCart->createNewCart($brandId, $branchId, null, $branchId, $discountId)['id'];
        }

        if (!$this->B2bCartProduct->saveManyToCart($cartId, $b2bCartProducts)) {
            $presentableErrors = array_unique(array_filter(Hash::flatten($this->B2bCartProduct->validationErrors)));
            $log = json_encode([
                'errors' => $this->B2bCartProduct->validationErrors,
                'data' => $this->B2bCartProduct->data,
                'Auth' => User::extractAuthUserLogFields($this->Auth->user()),
            ]);
            $message = implode(', ', $presentableErrors);

            return $this->_exceptionResponse(
                new BadRequestException($log),
                $message ? ('Unable to add to cart: ' . $message) : null,
                true
            );
        }

        try {
            $klaviyo = KlaviyoFactory::new($this->User, $brandId);
            $calculateShipping = $this->getDefaultCalculateShippingFunction();
            if ($createCart) {
                $klaviyo->trackB2bCartCreated($this->B2bCart, $this->User, (int)$cartId, $calculateShipping);
            } else {
                $klaviyo->trackB2bCartUpdated($this->B2bCart, $this->User, (int)$cartId, $calculateShipping);
            }
        } catch (\GuzzleHttp\Exception\GuzzleException|Exception $e) {
            CakeLog::warning($e, ['klaviyo']);
        }

        if ($createCart && $userType === User::TYPE_RETAILER) {
            $this->NotificationLogic->newB2bCartSalesRepNotification($brandId, $retailerId);
        }

        $b2bCart = $this->B2bCart->findForB2bCatalogue($cartId, $b2bCartProducts);

        $cartLink = '<a href="' . h(Router::url(['controller' => 'b2b_carts', 'action' => 'view', 'id' => $b2bCart['B2bCart']['id']])) . '" class="no-of-retails"><span>View Cart</span></a>';
        $flashMessage = 'Product added to draft order. ' . $cartLink;

        $refreshUrl = Router::url($this->_getCatalogueUrl($userId, $userType, $cartId, $brandId, $branchId, null, $discountId));

        if ($this->request->is('ajax')) {
            $this->response->body(json_encode($b2bCart + [
                'count' => $this->B2bCart->countAllItems($userId, $userType),
                'addUrl' => Router::url(['controller' => 'b2b_carts', 'action' => 'add', 'retailer_id' => $branchId, '?' => ['brandId' => $brandId, 'cartId' => $cartId, 'discountId' => $discountId]]),
                'refreshUrl' => $refreshUrl,
                'cartId' => $cartId,
            ]));
        }

        return $this->_successResponse($flashMessage, $refreshUrl);
    }

    private function _setDefaultProductWarehouse($brandId, $retailerId, $b2bCartProducts)
    {
        if (!array_has_any($b2bCartProducts, function($item) { return !$item['warehouse_id']; })) {
            return $b2bCartProducts;
        }

        $defaultWarehouse = $this->ManufacturerRetailer->findDefaultWarehouseId($brandId, $retailerId);

        return array_map(function($item) use ($defaultWarehouse) {
            if (!$item['warehouse_id']) {
                $item['warehouse_id'] = $defaultWarehouse;
            }
            return $item;
        }, $b2bCartProducts);
    }

    public function edit($id = null)
    {
        $this->_validateCartId($id);
        $this->request->allowMethod('post', 'put');

        if($this->request->data('B2bCart.ship_to_user_id'))
        {
            $addressData = $this->request->data('B2bCart.ship_to_user_id');
        } else {
            $addressData = $this->request->data('B2bShipToAddress');
        }

        $b2bCartProducts = $this->_filterB2bCartProductsForEditing($id, $this->request->data('B2bCartProduct'));
        if (empty($b2bCartProducts)) {
            return $this->_exceptionResponse(
                new BadRequestException(json_encode(['data' => $this->request->data])),
                'The cart could not be modified. Cart products are missing.',
                true
            );
        }

        if (!$this->B2bCart->editProducts($id, $b2bCartProducts)) {
            return $this->_exceptionResponse(
                new InternalErrorException(json_encode(['errors' => $this->B2bCart->validationErrors])),
                'The cart could not be modified. Please, try again.',
                true
            );
        }

        if(!$this->B2bCart->editAddress($id, $addressData))
        {
            return $this->_exceptionResponse(
                new InternalErrorException(json_encode(['errors' => $this->B2bCart->validationErrors])),
                'The cart could not be modified. Please, try again.',
                true
            );
        }

        $userId = (int)$this->B2bCart->field('user_id', ["{$this->B2bCart->alias}.id" => $id]);
        try {
            $klaviyo = KlaviyoFactory::new($this->User, $userId);
            $klaviyo->trackB2bCartUpdated($this->B2bCart, $this->User, (int)$id, $this->getDefaultCalculateShippingFunction());
        } catch (\GuzzleHttp\Exception\GuzzleException|Exception $e) {
            CakeLog::warning($e, ['klaviyo']);
        }

        return $this->_successResponse(__('The cart has been updated.'));
    }

    public function delete($id = null)
    {
        $this->_validateCartId($id);
        $this->request->allowMethod('post', 'delete');

        if (!$this->B2bCart->delete($id)) {
            return $this->_exceptionResponse(
                new InternalErrorException(__('Failed to delete b2b cart')),
                'The b2b cart could not be deleted. Please, try again.',
                true
            );
        }

        return $this->_successResponse(__('The b2b cart has been deleted.'), ['controller' => 'b2b_carts', 'action' => 'index']);
    }

    public function delete_product($id = null)
    {
        if (!$this->B2bCartProduct->exists(['B2bCartProduct.id' => $id])) {
            throw new NotFoundException(__('Invalid b2b cart product'));
        }
        $b2bCart = $this->B2bCartProduct->find('first', [
            'contain' => [
                'B2bCart' => [
                    'fields' => ['id', 'user_id'],
                ],
            ],
            'conditions' => [
                "{$this->B2bCartProduct->alias}.id" => $id
            ],
            'fields' => [
                "{$this->B2bCartProduct->alias}.id",
                "{$this->B2bCart->alias}.id",
                "{$this->B2bCart->alias}.user_id"
            ]
        ]
        );
        $this->_validateCartId($b2bCart['B2bCart']['id']);
        $this->request->allowMethod('post', 'delete');

        if (!$this->B2bCartProduct->delete($id)) {
            return $this->_exceptionResponse(
                new InternalErrorException(__('Failed to delete b2b cart product')),
                'The product could not be removed from the cart. Please, try again.',
                true
            );
        }

        try {
            $klaviyo = KlaviyoFactory::new($this->User, (int)$b2bCart['B2bCart']['user_id']);
            $klaviyo->trackB2bCartUpdated($this->B2bCart, $this->User, (int)$b2bCart['B2bCart']['id'], $this->getDefaultCalculateShippingFunction());
        } catch (\GuzzleHttp\Exception\GuzzleException|Exception $e) {
            CakeLog::warning($e, ['klaviyo']);
        }

        return $this->_successResponse(__('The product has been removed from cart.'));
    }

    public function place_order($id = null)
    {
        $this->_validateCartId($id);

        $isPreorderRequest = ($this->request->param('filter') === 'preorder');

        $this->request->allowMethod(($isPreorderRequest) ? ['post'] : ['get', 'post']);
        if ($this->request->is('get')) {
            $this->request->data = $this->_handlePlaceOrderRedirect($id, $this->request->query);
        }

        $b2bCartProducts = $this->_filterB2bCartProductsForEditing($id, $this->request->data('B2bCartProduct'));
        if (empty($b2bCartProducts)) {
            return $this->_exceptionResponse(
                new BadRequestException(json_encode(['data' => $this->request->data])),
                'The order could not be created. Cart products are missing.',
                true
            );
        }

        if (!$this->B2bCart->editProducts($id, $b2bCartProducts)) {
            return $this->_exceptionResponse(
                new InternalErrorException(json_encode(['errors' => $this->B2bCart->validationErrors])),
                'The cart could not be updated. Please, try again.',
                true
            );
        }

        $userId = (int)User::revertAuthParent($this->Auth->user())['id'];

        $calculateShipping = function($b2bCart) {
            $manualShipping = $this->request->data('B2bCart.shipping_amount');
            if (is_numeric($manualShipping)) {
                return $manualShipping;
            }
            return $this->getDefaultCalculateShippingFunction()($b2bCart);
        };
        $discount = $this->request->data('B2bCart.manual_discount');
        $b2bCart = $this->B2bCart->findView($id, $calculateShipping, $discount);

        $brandId = (int)$b2bCart['B2bCart']['user_id'];
        $userIsCartBrand = ($userId === $brandId);

        if (!$userIsCartBrand && $b2bCart['B2bCart']['product_total_price'] < $b2bCart['B2bCart']['minimum_subtotal']) {
            $errorMessage = "Your subtotal of '{$b2bCart['B2bCart']['product_total_price']}' does not meet the minimum of '{$b2bCart['ManufacturerRetailer']['b2b_minimum']}'.";
            return $this->_exceptionResponse(new BadRequestException($errorMessage), $errorMessage);
        }

        $currencyCode = (string)$b2bCart['ManufacturerRetailerBranch']['PricingTier']['currencytype'];
        $totalPrice = (float)$b2bCart['B2bCart']['total_price'];

        $addressOption = $this->request->data('B2bCart.ship_to_user_id') ?: null;
        $b2bShipToAddress = $this->request->data('B2bShipToAddress');
        $shipDate = $this->request->data('B2bCart.requested_ship_date');
        $paymentMethod = ($totalPrice > 0)
            ? $this->request->data('B2bCart.payment_method')
            : OrderPaymentMethod::EXTERNAL;
        $setupId = $this->request->data('B2bCart.setup_intent');
        $purchaseOrderNumber = $this->request->data('B2bCart.purchase_order_number');
        $serialNumber = (string)$this->request->data('B2bCart.serial_number') ?: null;
        $warrantyImages = [];
        if (!empty($this->request->data('B2bCart.warranty_image'))) {
            $files = (array)$this->request->data['B2bCart']['warranty_image'];
            foreach ($files as $file) {
                if($file['error'] === UPLOAD_ERR_NO_FILE) {
                    continue;
                }
                $sanitizedFileName = !empty($file['name']) ? preg_replace('/[^a-zA-Z0-9-_\.]/', '_', basename($file['name'])) : null;
                if (!empty($sanitizedFileName)) {
                    $warrantyImages[] = $this->Upload->uploadToUserHash(
                        $file,
                        $this->Auth->user('uuid'),
                        'warranty_images',
                        $sanitizedFileName
                    );
                } else {
                    throw new InternalErrorException(json_encode([
                        'message' => 'Invalid file name for warranty image.',
                    ]));
                }
            }
            $warrantyImages = array_values(array_unique($warrantyImages));
        }
        $notes = $this->request->data('B2bCart.notes');
        $creditTerm = ($paymentMethod === OrderPaymentMethod::CREDIT)
            ? $this->request->data('B2bCart.credit_term_id')
            : null;
        $placeEcommerceOrder = $this->request->data('B2bCart.place_ecommerce_order');
        $reserveInventory = ($b2bCart['B2bCart']['order_type'] !== B2bCartType::BOOKING);
        $shippingName = $this->request->data('B2bCart.shipping_custom_name') ?: null;

        $address = $this->_getB2bOrderAddress($brandId, $addressOption, $b2bShipToAddress);

        if ($isPreorderRequest) {
            return $this->_handlePreorderRequest($b2bCart, $paymentMethod, $address, $this->request->data);
        }

        try {
            $paymentMethodDetails = $this->OrderPlacer->reservePurchaseOrderPayment($b2bCart, $paymentMethod, $setupId);
        } catch (UserFriendlyException $e) {
            return $this->_exceptionResponse(new BadRequestException($e->getMessage()), $e->getMessage());
        } catch (Exception $e) {
            $httpException = !($e instanceof HttpException) ? new InternalErrorException(sprintf('[%s] %s', get_class($e), $e->getMessage())) : $e;

            return $this->_exceptionResponse($httpException, __('There was an error processing your payment. Please, try again.'), $e);
        }

        $currencyConversion = $this->currencyConversion(1, $b2bCart['User']['currency_code'], $currencyCode);

        $newOrderId = $this->Order->createFromB2bCartView(
            $userId,
            $b2bCart,
            $address,
            $shipDate,
            $purchaseOrderNumber,
            $serialNumber,
            $warrantyImages,
            $notes,
            $paymentMethod,
            $paymentMethodDetails,
            $creditTerm,
            $currencyConversion
        );
        if (!$newOrderId) {
            return $this->_exceptionResponse(
                new InternalErrorException(json_encode(['errors' => $this->Order->validationErrors, 'data' => $this->Order->data])),
                'The order could not be created. Please, try again.',
                true
            );
        }

        $this->_closeB2bCart($id);

        $orderID = $this->Order->field('orderID', ['id' => $newOrderId]);

        $user = [
            'User' => [
                'company_name' => $b2bCart['Retailer']['company_name'],
                'id' => $b2bCart['Retailer']['id'],
            ]
        ];

        /** @var ShipearlyHelper $Helper */
        $Helper = (new View($this))->loadHelper('Shipearly');

        $retailerLink = $Helper->makeInlineContactLink($user);
        $orderLink = $Helper->orderLink(
            $newOrderId,
            $orderID,
            true,
        );

        $this->NotificationLogic->sendOrderNotifications(
            $retailerLink . ' placed purchase order ' . $orderLink,
            Notification::TYPE_PURCHASE_ORDER,
            $newOrderId
        );

        $successMessage = __('Purchase Order %s has been created.', $orderID);
        $redirectUrl = ['controller' => 'b2b_carts', 'action' => 'index'];

        // Prepend the success flash message with the Browser Pixel JS snippet.
        $successMessage = $this->_facebookPixelSnippet($b2bCart, $orderID) . $successMessage;

        $this->Order->trackFbpixelPurchaseEvent($newOrderId);
        try {
            $klaviyo = KlaviyoFactory::new($this->User, $brandId);
            $klaviyo->trackB2bOrderPlacedEvent($this->Order, $this->User, $newOrderId);
        } catch (\GuzzleHttp\Exception\GuzzleException|Exception $e) {
            CakeLog::warning($e, ['klaviyo']);
        }

        if ($reserveInventory) {
            $this->WarehouseProductReservation->reserveLineItemSet($newOrderId, $b2bCart['B2bCartProduct']);
        }

        // this shortcircuits the purchase order stage so to create as purchase order we skip this step.
        if ($userIsCartBrand && $placeEcommerceOrder) {
            $errorMessage = '';
            try {
                $this->OrderPlacer->chargePurchaseOrder($newOrderId, $totalPrice);
                if (!$this->DealerOrder->createFromB2bCartView($newOrderId, $b2bCart, $shippingName)) {
                    throw new InternalErrorException('Failed to create DealerOrder ' . json_encode(['errors' => $this->DealerOrder->validationErrors, 'data' => $this->DealerOrder->data]));
                }
                $dealerOrderId = $this->DealerOrder->id;

                if ($reserveInventory) {
                    $this->WarehouseProductReservation->fulfill($newOrderId);
                }

                $successMessage .= '<br />' . "Wholesale pricing confirmed for Purchase Order {$orderID}.";
                $this->NotificationLogic->sendPricingConfirmationEmail($newOrderId);

                if (!$this->OrderPlacer->createEcommerceDealerOrder($dealerOrderId)) {
                    $logMessage = __('Failed placing purchase order %s in the ecommerce platform.', $orderID);

                    $errorMessage = $logMessage . $this->OrderPlacer->buildCreateEcommerceDealerOrderRetryLink($dealerOrderId);

                    throw new InternalErrorException($logMessage);
                } else {
                    $successMessage .= '<br />' . "Purchase Order {$orderID} placed in the ecommerce platform.";
                }
            } catch (HttpException $e) {
                $this->setFlash($successMessage, 'success');
                if (empty($errorMessage)) {
                    $errorMessage = "Failed to confirm pricing for purchase order {$orderID}. " . ($this->getFlash('error') ?: 'Please try again.');
                }
                return $this->_exceptionResponse($e, $errorMessage, true, $redirectUrl);
            }
        } else {
            if ($paymentMethod === OrderPaymentMethod::STRIPE) {
                try {
                    $this->Stripe->updateUnconfirmedPayment($paymentMethodDetails->stripe_account, $paymentMethodDetails->transactionID, [
                        'description' => $orderID,
                        'metadata' => ['orderId' => $newOrderId],
                    ]);
                } catch (\Stripe\Exception\ApiErrorException $e) {
                    CakeLog::warning($e);
                }
            }
            if (!$userIsCartBrand) {
                $this->NotificationLogic->sendNewPurchaseOrderBrandNotification($newOrderId);
            }
        }

        $this->_successResponse($successMessage, $redirectUrl);
    }

    public function reorder()
    {
        $this->autoRender = false;
        $this->response->type('json');
        $this->request->allowMethod('post');

        if (!$this->_canEdit($this->Auth->user())) {
            $this->response->body(json_encode([
                'success' => false,
                'message' => 'User cannot reorder this order.'
            ]));
            return $this->response;
        }

        $orderId = $this->request->data('order_id');

        $this->Order->bindModel([
            'hasMany' => ['OrderProduct'],
        ], false);
        $order = $this->Order->record($orderId, [
            'contain' => [
                'OrderProduct' => [
                    'fields' => ['id', 'product_id', 'quantity', 'warehouse_id', 'inventory_transfer_id'],
                ],
            ],
            'conditions' => [
                'Order.order_type' => OrderType::WHOLESALE,
            ],
            'fields' => ['id', 'user_id', 'retailer_id', 'branch_id', 'discount_id', 'subType'],
        ]);
        $this->Order->unbindModel([
            'hasMany' => ['OrderProduct'],
        ], false);

        if (empty($order)) {
            $this->response->body(json_encode([
                'success' => false,
                'message' => 'Original order not found.'
            ]));
            return $this->response;
        }

        $retailerId = $order['Order']['retailer_id'];
        $masterRetailerId = $this->User->getMainRetailerId($retailerId);

        $newCartData = [
            'B2bCart' => [
                'user_id' => $order['Order']['user_id'],
                'retailer_id' => $masterRetailerId,
                'branch_id' => $retailerId,
                'ship_to_user_id' => $retailerId,
                'discount_id'=> $order['Order']['discount_id'],
                'order_type' => in_array($order['Order']['subType'], B2bCartType::getB2bCartTypes(), true)
                    ? $order['Order']['subType']
                    : B2bCartType::REGULAR,
            ],
            'B2bCartProduct' => array_values(array_filter(array_map(
                fn(array $item): ?array =>
                    (!empty($item['product_id']) && !empty($item['quantity'])) ? [
                        'product_id' => $item['product_id'],
                        'quantity' => $item['quantity'],
                        'warehouse_id'=> $item['warehouse_id'] ?? null,
                        'inventory_transfer_id' => $item['inventory_transfer_id'] ?? null,
                    ] : null,
                $order['OrderProduct']
            ))),
        ];

        $this->B2bCart->create();
        if (!$this->B2bCart->saveAssociated($newCartData)) {
            $this->response->body(json_encode([
                'success' => false,
                'message' => 'Failed to create new cart with products.'
            ]));
            return $this->response;
        }

        $this->response->body(json_encode([
            'success' => true,
            'message' => 'Reorder successful.',
            'cart_id' => $this->B2bCart->id
        ]));
        return $this->response;
    }

    /**
     * Retrieves and validates preorder request data when returning from a payment confirmation redirect.
     *
     * @param int $id B2bCart ID
     * @param array $query Request query parameters
     * @return array Preorder request data
     * @throws NotFoundException If the preorder request data is missing
     * @throws BadRequestException If the preorder request data is invalid
     */
    protected function _handlePlaceOrderRedirect(int $id, array $query): array
    {
        $data = $this->_getPreorderData($id);
        if (!$data) {
            throw new NotFoundException('Missing preorder request data');
        }

        $querySetupId = (string)($query['setup_intent'] ?? '') ?: null;
        $sessionSetupId = (string)($data['B2bCart']['setup_intent'] ?? '') ?: null;
        if ($querySetupId !== $sessionSetupId) {
            throw new BadRequestException(json_encode([
                'message' => 'The query parameter setup_intent does not match the request data setup_intent.',
                'query' => $this->request->query,
                'data' => $data,
            ]));
        }

        // None of our supported Stripe payment methods are expected to require a payment confirmation redirect
        // but the Stripe payment element can always add payment methods without our knowledge that do require one.
        triggerWarning(json_encode(['message' => 'Handling an unexpected payment confirmation redirect using session data.', 'data' => $data]));

        return $data;
    }

    /**
     * Persists preorder request data in case payment confirmation requires a redirect.
     *
     * Initializes Stripe intents and responds with a client secret if Stripe is the selected order payment method.
     *
     * @param array $b2bCart
     * @param string $paymentMethod
     * @param array $address
     * @param array $data
     * @return CakeResponse|null
     */
    protected function _handlePreorderRequest(array $b2bCart, string $paymentMethod, array $address, array $data): ?CakeResponse
    {
        $id = (int)$b2bCart['B2bCart']['id'];

        $setupId = null;
        $clientSecret = null;
        if ($paymentMethod === OrderPaymentMethod::STRIPE) {
            $setup = $this->_initStripeSetupIntent($b2bCart, $address);

            $setupId = $setup->id;
            $clientSecret = $setup->client_secret;
        }

        // Assign form input that would be assigned after a non-redirecting confirmation.
        $data['B2bCart']['setup_intent'] = (string)$setupId;

        $this->_setPreorderData($id, $data);

        $response = [
            'client_secret' => $clientSecret,
        ];
        $this->set($response);
        $this->set('_serialize', array_keys($response));

        return null;
    }

    /**
     * @param int $brandId
     * @param array $retailer
     * @param array $address
     * @param string|null $stripeAccount
     * @return \Stripe\CustomerSession
     * @throws \Stripe\Exception\ApiErrorException
     * @see https://docs.stripe.com/api/customer_sessions/create?lang=php
     */
    protected function _initStripeCustomerSession(int $brandId, array $retailer, array $address, ?string $stripeAccount): \Stripe\CustomerSession
    {
        $stripeCustomerId = $this->Stripe->createB2bCustomerIfNotExists($brandId, $retailer, $address, $stripeAccount);

        return $this->Stripe->client->customerSessions->create([
            'customer' => $stripeCustomerId,
            'components' => [
                'payment_element' => [
                    'enabled' => true,
                    'features' => [
                        // Explicitly saved payment methods are marked as 'always' redisplay.
                        'payment_method_allow_redisplay_filters' => ['always'],
                        'payment_method_redisplay' => 'enabled',
                        'payment_method_save' => 'enabled',
                        'payment_method_save_usage' => 'off_session',
                        // Allowing payment method removal impacts any subscriptions depending on that payment method.
                        // This is not a concern for B2B Stripe Customers as they are not expected to have subscriptions.
                        'payment_method_remove' => 'enabled',
                    ],
                ],
            ],
        ], ['stripe_account' => $stripeAccount]);
    }

    /**
     * @param array $b2bCart
     * @param array $address
     * @return \Stripe\SetupIntent
     * @throws \Stripe\Exception\ApiErrorException
     */
    protected function _initStripeSetupIntent(array $b2bCart, array $address): \Stripe\SetupIntent
    {
        $id = (int)$b2bCart['B2bCart']['id'];
        $brandId = (int)$b2bCart['B2bCart']['user_id'];
        $currency = (string)$b2bCart['ManufacturerRetailerBranch']['PricingTier']['currencytype'];
        $amount = (float)$b2bCart['B2bCart']['total_price'];

        $setupAccount = $this->StripeUser->getAccountId($brandId);
        $paymentAccount = $setupAccount;

        $stripeCustomerId = $this->Stripe->initB2bCustomer($brandId, (array)$b2bCart['Retailer'], $address, $setupAccount);

        $paymentId = $this->Session->read("B2bCart.{$id}.paymentId") ?: null;
        $payment = $this->Stripe->savePaymentIntent($paymentId, $amount, $currency, [
            'description' => trim("Purchase Order {$b2bCart['B2bCart']['purchase_order_number']}"),
            'metadata' => [],
            'payment_method_types' => ['card'],
            'shipping' => [
                'address' => [
                    'line1' => $address['address1'],
                    'line2' => $address['address2'] ?: null,
                    'city' => $address['city'],
                    'state' => $address['state_name'],
                    'country' => $address['country_code'],
                    'postal_code' => $address['zipcode'],
                ],
                'name' => $address['company_name'],
                'phone' => $address['telephone'],
            ],
        ], ['stripe_account' => $paymentAccount]);
        $paymentId = $payment->id;
        $this->Session->write("B2bCart.{$id}.paymentId", $paymentId);

        $setupId = (string)$this->Session->read("B2bCart.{$id}.setupId") ?: null;
        $setup = $this->Stripe->saveCardSetupIntent($setupId, $setupAccount, $stripeCustomerId, $paymentId, $paymentAccount);
        $setupId = $setup->id;
        $this->Session->write("B2bCart.{$id}.setupId", $setupId);

        return $setup;
    }

    /**
     * Persists preorder request data in case payment confirmation requires a redirect.
     *
     * @param int $id B2bCart ID
     * @param array $data Preorder request data
     * @return bool Success
     */
    protected function _setPreorderData(int $id, array $data): bool
    {
        // Storing preorder data in the session is not perfectly reliable but it is simpler than managing DB records
        // that are not expected to be required.
        // TODO Store preorder data in the database if it becomes necessary to properly support confirmation redirects.
        return $this->Session->write("B2bCart.{$id}.preorder", $data);
    }

    /**
     * Retrieves preorder request data when returning from a payment/setup confirmation redirect.
     *
     * @param int $id B2bCart ID
     * @return array Preorder request data
     * @throws BadRequestException If the preorder request data is missing or invalid
     */
    protected function _getPreorderData(int $id): array
    {
        // TODO Store preorder data in the database if it becomes necessary to properly support confirmation redirects.
        return (array)($this->Session->read("B2bCart.{$id}.preorder") ?: []);
    }

    /**
     * Clean up B2bCart data after completing an order.
     *
     * @param int $id B2bCart ID
     * @return bool Success
     */
    protected function _closeB2bCart(int $id): bool
    {
        $this->Session->delete("B2bCart.{$id}");

        return $this->B2bCart->delete($id);
    }

    public function orderTypes()
    {
        $brandId = $this->request->query['brandId'];
        $retailerId = $this->request->query['retailerId'];
        $locationId = $this->request->query['locationId'];
        $currencyCode = $this->User->field('currency_code', ["{$this->User->alias}.id" => $brandId]);
        $this->set('orderTypes', $this->_getOrderTypes($locationId, $brandId));
        $this->set('retailerId', $retailerId);
        $this->set('locationId', $locationId);
        $this->set('brandId', $brandId);
        $this->set('currencyCode', $currencyCode);

        $this->layout = 'ajax';
        $this->render('/Elements/B2bCarts/b2b_order_type_table');
    }

    public function createCart()
    {
        if ($this->request->is('POST')) {
            $this->_createCartPost();
        } elseif ($this->request->is('GET')) {
            $this->_createCartGet();
        }
    }

    public function existingCarts()
    {
        $userId = $this->Auth->user('id');
        $userType = $this->Auth->user('user_type');
        $brandId = $this->request->query['brandId'];
        $retailerId = $this->request->query['retailerId'];
        $locationId = $this->request->query['locationId'];
        $calculateShipping = $this->getDefaultCalculateShippingFunction();
        $b2bCarts = $this->B2bCart->findIndex($userId, $calculateShipping, $userType, $brandId, $locationId);

        $b2bCarts = array_map(function ($cart) use ($userId, $userType) {
            $cart['productsUrl'] = $this->_getCatalogueUrl($userId, $userType, $cart['B2bCart']['id'], $cart['B2bCart']['user_id'], $cart['B2bCart']['branch_id']);
            return $cart;
        }, $b2bCarts);
        $this->set('b2bCarts', $b2bCarts);
        $this->layout = 'ajax';
        $this->render('/Elements/B2bCarts/existing_cart_table');
    }

    public function getSalesRepRetailersForBrand(){
        $this->autoRender = false;

        $salesRepId = $this->Auth->user('id');
        $brandId = $this->request->query['brandId'];
        $retailers = $this->User->listSalesRepStoreNames($salesRepId, $brandId);

        $retailers = array_map(function($retailer, $retailerId) {
            return [
                'retailer' => $retailer, 
                'retailerId' => $retailerId
                ];
        }, $retailers, array_keys($retailers));

        $this->response->body(json_encode(['data' => $retailers]));
        return $this->response;
    }

    public function getRetailerLocations()
    {
        $this->autoRender = false;

        $userId = $this->Auth->user('id');
        $userType = $this->Auth->user('user_type');
        $retailerId = $this->request->query('retailerId');
        $brandId = $this->request->query('brandId');
        if($userType !== User::TYPE_SALES_REP) {
            $locations = $this->User->listRetailerLocationsForBrand($brandId, $retailerId);
        } else {
            $locations = $this->User->listSalesRepStoreNames($userId, $brandId, $retailerId);
        }
        if($this->isRetailStaffUser()){
            $locations = array_intersect_key($locations, array_flip($this->getAuthUserIds()));
        }

        $locations = array_map(function($location, $locationId) {
            return [
                'location' => $location, 
                'locationId' => $locationId
                ];
        }, $locations, array_keys($locations));

        $this->response->body(json_encode(['data' => $locations]));
        return $this->response;
    }

    public function getForB2bCatalogue($cartId = null)
    {
        $this->autoRender = false;
        $b2bCart = $this->B2bCart->findForB2bCatalogue($cartId);
        $this->response->body(json_encode($b2bCart));
    }

    private function _createCartPost()
    {
        $orderType = $this->request->data('orderType');
        $brandId = $this->request->data('brandId');
        $locationId = $this->request->data('locationId');
        $discountId = $this->request->data('discountId');
        $discountId = !empty($discountId) ? (int)$discountId : null;
        $this->_getProductsRedirect($brandId, $locationId, $discountId, $orderType);
    }
    private function _brandCreateCartGet()
    {
        $brandId = $this->Auth->user('id');
        $retailers = $this->User->listConnectedRetailerNames($brandId);
        $this->set('retailers', $retailers);
        $this->set('brandId',$brandId);
    }

    private function _retailerCreateCartGet()
    {
        $retailerId = $this->User->getMainRetailerId((int)$this->Auth->user('id'));
        $this->set('brands', $this->User->listConnectedBrandNames($retailerId));
        $this->set('retailerId', $retailerId);
    }

    private function _salesRepCreateCartGet()
    {
        $this->set('retailers', []);
        $this->set('brands', $this->User->listSalesRepBrandNames((int)$this->Auth->user('id')));
    }

    private function _createCartGet()
    {
        $userType = $this->Auth->user('user_type');
        $this->set('userType', $userType);

        $locationId = $this->request->query('locationId');
        $this->set('locationId', $locationId ?? null);
        $this->set('retailerId', $this->User->getMainRetailerId($locationId) ?: null);
        $this->set('brandId', $this->request->query('brandId'));

        //TODO: handle retail staff if they can create orders
        if ($userType === User::TYPE_MANUFACTURER) {
            $this->_brandCreateCartGet();
        } else if ($userType === User::TYPE_SALES_REP) {
            $this->_salesRepCreateCartGet();
        } else if ($userType === User::TYPE_RETAILER) {
            $this->_retailerCreateCartGet();
        }
    }

    private function _getOrderTypes(int $retailerId, int $brandId)
    {
        $defaultRow = ['Discount' => null, 'type' => B2bCartType::REGULAR, 'description' => ''];
        $discounts = $this->Discount->getAvailableB2bDiscounts($retailerId, $brandId);

        $hasRegularDiscount = false;
        foreach ($discounts as $discount) {
            if (($discount['Discount']['b2b_discount_type'] ?? '') === B2bCartType::REGULAR) {
                $hasRegularDiscount = true;
                break;
            }
        }

        $discountRows = array_map(function($discount) {
            return [
                'Discount' => $discount['Discount'],
                'type' => $discount['Discount']['b2b_discount_type'],
                'description' => $discount['Discount']['description']
            ];
        }, $discounts);

        return $hasRegularDiscount
            ? $discountRows
            : array_merge([$defaultRow], $discountRows);
    }

    private function _getProductsRedirect(int $brandId, int $branchId, ?int $discountId, string $orderType)
    {
        $userId = $this->Auth->user('id');
        $userType = $this->Auth->user('user_type');
        $productsUrl = $this->_getCatalogueUrl(
            $userId,
            $userType,
            $cart['id'] ?? null,
            $cart['user_id'] ?? $brandId,
            $cart['branch_id'] ?? $branchId,
            $orderType,
            $discountId
        );
        $this->redirect($productsUrl);
    }

    /**
     * @param int $brandId
     * @param int|null $addressOption
     * @param null|string[] $b2bShipToAddress
     * @return array
     */
    private function _getB2bOrderAddress(int $brandId, ?int $addressOption, ?array $b2bShipToAddress): array
    {
        $b2bOrderAddress = ($addressOption)
            ? $this->User->findB2bShipToUserOrderAddress($brandId, $addressOption)
            : $this->_findB2bCustomOrderAddress($b2bShipToAddress);

        if (!$b2bOrderAddress['latitude'] || !$b2bOrderAddress['longitude']) {
            $geopoints = findGeocode(
                $b2bOrderAddress['address1'],
                $b2bOrderAddress['city'],
                $b2bOrderAddress['zipcode'],
                $b2bOrderAddress['state_name'],
                $b2bOrderAddress['country_name']
            );
            $b2bOrderAddress = array_merge($b2bOrderAddress, [
                'latitude' => $geopoints['lat'] ?? null,
                'longitude' => $geopoints['lng'] ?? null,
            ]);
        }

        return $b2bOrderAddress;
    }

    private function _findB2bCustomOrderAddress(array $b2bShipToAddress): array
    {
        $state = $this->State->findWithCountryName($b2bShipToAddress['state_id']);

        return array_merge($b2bShipToAddress, [
            'b2b_ship_to_user_id' => null,
            'country_code' => $state['State']['country_code'],
            'country_name' => $state['State']['country_name'],
            'state_code' => $state['State']['state_code'],
            'state_name' => $state['State']['state_name'],
            'latitude' => null,
            'longitude' => null,
        ]);
    }

    private function _facebookPixelSnippet(array $b2bCart, string $eventID): string
    {
        $fbpixelId = (string)$b2bCart['User']['fbpixelId'];
        if ($fbpixelId) {
            $customData = [
                'value' => (float)$b2bCart['B2bCart']['total_price'],
                'currency' => strtolower($b2bCart['B2bCart']['currency_code']),
            ];
            $js_fbpixelId = json_encode($fbpixelId);
            $js_customData = json_encode($customData);
            $js_eventID = json_encode(['eventID' => $eventID]);
            $noscriptQuery = http_build_query([
                'id' => $fbpixelId,
                'ev' => 'Purchase',
                'cd' => $customData,
                'eid' => $eventID,
                'noscript' => 1,
            ]);

            $fbpixelCode = <<<HTML
<script type="text/javascript">
!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?
n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;
n.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;
t.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)
}(window,document,'script','https://connect.facebook.net/en_US/fbevents.js');
  fbq('init', {$js_fbpixelId});
  fbq('track', 'Purchase', {$js_customData}, {$js_eventID});
</script>
<noscript><img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?{$noscriptQuery}" /></noscript>
HTML;
        } else {
            $fbpixelCode = "\t<!-- Missing Facebook Pixel ID -->";
        }
        return <<<HTML

<!-- Facebook Pixel Code -->
{$fbpixelCode}
<!-- / Facebook Pixel Code -->

HTML;
    }

    /**
     * @param int|string $id
     */
    private function _validateCartId($id)
    {
        if (!$this->B2bCart->exists(['B2bCart.id' => $id])) {
            if ($this->request->is('get')) {
                $this->setFlash(__('That cart no longer exists or was emptied.'), 'error');
                $this->redirect(array('action' => 'index'));
            }
            throw new NotFoundException(json_encode(['B2bCart' => ['id' => $id]]));
        }
        if (!$this->B2bCart->validUser($id, (int)$this->Auth->user('id'), (string)$this->Auth->user('user_type'))) {
            throw new ForbiddenException(json_encode(['User' => array_intersect_key($this->Auth->user(), array_flip(['id', 'Branch', 'email_address', 'user_type']))]));
        }
    }

    /**
     * @param int|string|null $branchId
     * @return int $branchId
     * @throws HttpException
     */
    private function _validateBranchIdForAdding($branchId): int
    {
        $userId = (int)$this->Auth->user('id');
        $userType = (string)$this->Auth->user('user_type');

        if ($userType === User::TYPE_RETAILER) {
            $branchId = $branchId ?: $userId;
        } else {
            if (!$this->User->isUserType((int)$branchId, User::TYPE_RETAILER)) {
                throw new NotFoundException(json_encode([
                    'message' => 'Unable to add to cart: location not found',
                    'retailer_id' => $branchId,
                    'Auth' => User::extractAuthUserLogFields($this->Auth->user()),
                ]));
            }
        }

        if (!$this->User->hasB2bRetailerPermission($userId, $branchId)) {
            throw new ForbiddenException(json_encode([
                'message' => 'Unable to add to cart: location does not have permission',
                'retailer_id' => $branchId,
                'Auth' => User::extractAuthUserLogFields($this->Auth->user()),
            ]));
        }

        return (int)$branchId;
    }

    /**
     * @param int|string|null $brandId
     * @param int|string|null $branchId
     * @return int $brandId
     * @throws HttpException
     */
    private function _validateBrandIdForAdding($brandId, $branchId): int
    {
        $userId = (int)$this->Auth->user('id');
        $userType = (string)$this->Auth->user('user_type');

        if ($userType === User::TYPE_MANUFACTURER) {
            $brandId = $userId;
        } else {
            if (!$this->User->isUserType((int)$brandId, User::TYPE_MANUFACTURER)) {
                throw new NotFoundException(json_encode([
                    'message' => 'Unable to add to cart: brand not found',
                    'brand_id' => $brandId,
                    'Auth' => User::extractAuthUserLogFields($this->Auth->user()),
                ]));
            }
            if (!$this->User->hasB2bRetailerPermission($brandId, $branchId)) {
                throw new ForbiddenException(json_encode([
                    'message' => 'Unable to add to cart: brand does not have permission',
                    'brand_id' => $brandId,
                    'retailer_id' => $branchId,
                    'Auth' => User::extractAuthUserLogFields($this->Auth->user()),
                ]));
            }
        }

        return (int)$brandId;
    }

    /**
     * @param array $b2bCartProducts
     * @return array
     */
    private function _filterB2bCartProductsForAdding($b2bCartProducts)
    {
        return array_values(
            array_filter((array)$b2bCartProducts, function($item) {
                return is_array($item) && key_exists('quantity', $item);
            })
        );
    }

    /**
     * @param int|string $b2bCartId
     * @param array $b2bCartProducts
     * @return array
     */
    private function _filterB2bCartProductsForEditing($b2bCartId, $b2bCartProducts)
    {
        return array_values(
            array_filter((array)$b2bCartProducts, function($item) use ($b2bCartId) {
                return (
                    !empty($b2bCartId)
                    && !empty($item['id'])
                    && $this->B2bCartProduct->exists(['B2bCartProduct.id' => $item['id'], 'B2bCartProduct.b2b_cart_id' => $b2bCartId])
                );
            })
        );
    }

    /**
     * @param string $userType
     * @return string
     */
    private function _userAlias(string $userType)
    {
        return in_array($userType, [User::TYPE_MANUFACTURER, User::TYPE_SALES_REP], true) ? 'Retailer' : 'User';
    }

    /**
     * @param array $authUser
     * @return bool
     */
    private function _canEdit(array $authUser)
    {
        return (
            in_array($authUser['user_type'], [User::TYPE_RETAILER, User::TYPE_MANUFACTURER, User::TYPE_SALES_REP], true) &&
            $this->Permissions->userHasPermission($authUser, Permissions::NAME_ORDERS, Permissions::LEVEL_EDIT)
        );
    }

    private function _getCatalogueUrl(int $userId, string $userType, ?int $cartId, int $brandId, int $branchId, ?string $orderType = null, ?int $discountId = null)
    {
        $productsUrlQuery = [
            'user_id' => $brandId,
            'cartId' => $cartId,
            'discountId' => $discountId,
            'orderType' => $orderType,
        ];
        if ($userId === $brandId) {
            unset($productsUrlQuery['user_id']);
        }

        return ['controller' => 'products', 'action' => 'index_retailer', 'retailer_id' => $branchId, '?' => $productsUrlQuery];
    }

    protected function getCreditTermOptions($b2bCartId, $userId, $retailerId, $b2bCart)
    {
        if ($b2bCart['B2bCart']['total_discount'] > 0.00) {
            $creditTermOptions = $this->B2bCart->getCreditTermOptions($b2bCartId);
            if ($creditTermOptions) {
                return $creditTermOptions;
            }
        }

        $creditTermOptions = $this->RetailerCreditTerm->findCreditTermOptions($userId, $retailerId);
        if (empty($creditTermOptions)) {
            return [];
        }

        $creditTermIds = array_keys($creditTermOptions);
        $conditionTypeAndValue = $this->CreditTerm->findCreditTermConditions($creditTermIds);
        $validCreditTermOptions = [];
        $productTotalPrice = $b2bCart['B2bCart']['product_total_price'];
        $totalQuantity = 0;
        $productTypeTotals = [];

        foreach ($b2bCart['B2bCartProduct'] as $product) {
            $productType = $product['Product']['product_type'];
            $quantity = (int)$product['quantity'];
            $price = (float)$product['dealer_price'];
            if (!isset($productTypeTotals[$productType])) {
                $productTypeTotals[$productType] = ['quantity' => 0, 'value' => 0.00];
            }
            $productTypeTotals[$productType]['quantity'] += $quantity;
            $productTypeTotals[$productType]['value'] += $quantity * $price;
            $totalQuantity += $quantity;
        }

        foreach ($conditionTypeAndValue as $creditTermId => $creditTermData) {
            if (empty($creditTermData['condition_type'])) {
                if (isset($creditTermOptions[$creditTermId])) {
                    $validCreditTermOptions[$creditTermId] = $creditTermOptions[$creditTermId];
                }
                continue;
            }
            if (empty($creditTermData['product_types'])) {
                $conditionValue = (float) $creditTermData['condition_value'];
                if ($creditTermData['condition_type'] === 'Value') {
                    if ($productTotalPrice >= $conditionValue) {
                        $validCreditTermOptions[$creditTermId] = $creditTermOptions[$creditTermId];
                    }
                } elseif ($creditTermData['condition_type'] === 'QTY') {
                    if ($totalQuantity >= $conditionValue) {
                        $validCreditTermOptions[$creditTermId] = $creditTermOptions[$creditTermId];
                    }
                }
            } else {
                $validProductTypeMatch = false;
                $totalProductTypeQuantity = 0;
                $totalProductTypeValue = 0.00;
                foreach ($creditTermData['product_types'] as $productType) {
                    if (isset($productTypeTotals[$productType])) {
                        $validProductTypeMatch = true;
                        $totalProductTypeQuantity += $productTypeTotals[$productType]['quantity'];
                        $totalProductTypeValue += $productTypeTotals[$productType]['value'];
                    }
                }
                if ($validProductTypeMatch) {
                    $conditionValue = (float) $creditTermData['condition_value'];
                    if ($creditTermData['condition_type'] === 'Value') {
                        if ($totalProductTypeValue >= $conditionValue) {
                            $validCreditTermOptions[$creditTermId] = $creditTermOptions[$creditTermId];
                        }
                    } elseif ($creditTermData['condition_type'] === 'QTY') {
                        if ($totalProductTypeQuantity >= $conditionValue) {
                            $validCreditTermOptions[$creditTermId] = $creditTermOptions[$creditTermId];
                        }
                    }
                }
            }
        }

        return $validCreditTermOptions;
    }

    protected function getDefaultCalculateShippingFunction(): Closure
    {
        return fn($b2bCart) => $this->ShippingCalculator->calculateB2bCartShipping((array)$b2bCart);
    }
}
