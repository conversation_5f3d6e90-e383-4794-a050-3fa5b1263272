<?php
App::uses('AppComponent', 'Controller/Component');
App::uses('AppModelValidationsTrait', 'Model/Behavior/Trait');
App::uses('Validation', 'Utility');

/**
 * Class AddressValidationComponent.
 *
 * @property UspsComponent $Usps
 *
 * @property Country $Country
 * @property OrderAddress $OrderAddress
 * @property State $State
 */
class AddressValidatorComponent extends AppComponent
{
    public $components = [
        'Usps',
    ];

    public $uses = [
        'Country',
        'OrderAddress',
        'State',
    ];

    public function validate_address(array $data)
    {
        $log_request = json_encode(['request' => ['url' => $this->controller->request->here(), 'data' => $data]]);
        CakeLog::debug($log_request);

        $address = $this->_requestToOrderAddress($data);

        $fieldErrors = [];
        if (!$this->OrderAddress->validateEcommerceForm((string)$address['country_code'], $address, ['fieldList' => array_keys($address)])) {
            $fieldErrors = $this->_orderAddressErrorsToFieldErrors($this->OrderAddress->validationErrors);
        }

        $missingFields = array_keys($fieldErrors, "Can't be blank", true);
        if ($missingFields) {
            $errorMessage = "Missing required fields: " . implode(', ', $missingFields);
            CakeLog::error($errorMessage . ' ' . $log_request);
            return [
                'status' => 'ERROR',
                'message' => $errorMessage,
                'fieldErrors' => array_fill_keys($missingFields, "Can't be blank"),
            ];
        }
        if ($fieldErrors) {
            $errorMessage = json_encode($fieldErrors);
            CakeLog::warning($errorMessage . ' ' . $log_request);
            return [
                'status' => 'INVALID',
                'message' => $errorMessage,
                'fieldErrors' => $fieldErrors,
            ];
        }

        $response = [
            'status' => 'VALID',
            'message' => 'Address is valid',
            'fieldErrors' => json_decode('{}'),
        ];
        if ($this->Usps->validationIsAllowed($address['country_code'])) {
            $response = $this->Usps->validateAddress(
                $address['address1'],
                $address['city'],
                $address['country_code'],
                $address['state_code'],
                $address['zipcode']
            );
        }
        if (in_array($response['status'], ['INVALID', 'ERROR'])) {
            CakeLog::warning($response['message'] . ' ' . $log_request);
        }
        return $response;
    }

    public function validate_email(?string $email_address): array
    {
        $log_request = json_encode(['request' => ['url' => $this->controller->request->here(), 'data' => compact('email_address')]]);
        CakeLog::debug($log_request);

        if (!Validation::notBlank($email_address)) {
            $missingFields = ['email_address'];
            $errorMessage = "Missing required fields: " . implode(', ', $missingFields);
            CakeLog::error($errorMessage . ' ' . $log_request);

            return [
                'status' => 'ERROR',
                'message' => $errorMessage,
                'fieldErrors' => array_fill_keys($missingFields, "Can't be blank"),
            ];
        }

        if (!AppModelValidationsTrait::email($email_address, true)) {
            $fieldErrors = ['email_address' => "Please enter a valid email address"];
            $errorMessage = json_encode($fieldErrors);
            CakeLog::warning($errorMessage . ' ' . $log_request);

            return [
                'status' => 'INVALID',
                'message' => $errorMessage,
                'fieldErrors' => $fieldErrors,
            ];
        }

        return [
            'status' => 'VALID',
            'message' => 'Email address is valid',
            'fieldErrors' => json_decode('{}'),
        ];
    }

    private function _requestToOrderAddress(array $data): array
    {
        $fullname = $data['fullname'] ?? '';
        if ($fullname) {
            list($first_name, $last_name) = (strpos($fullname, ' ') !== false)
                ? explode(' ', $fullname, 2)
                : ['', $fullname];
            $data += compact('first_name', 'last_name');
        }

        if (is_numeric($data['country_code'] ?? '')) {
            $data += ['country_id' => $data['country_code']];
            unset($data['country_code']);
        }
        if (is_numeric($data['state_code'] ?? '')) {
            $data += ['state_id' => $data['state_code']];
            unset($data['state_code']);
        }

        $countryCode = !empty($data['country_id'])
            ? $this->Country->getCountryCode($data['country_id'])
            : $data['country_code'] ?? '';
        $stateCode = !empty($data['state_id'])
            ? $this->State->getStateCode($data['state_id'])
            : $data['state_code'] ?? '';
        $state = $this->State->findStateAndCountryByVagueName($countryCode, $stateCode);

        return [
            'email_address' => $data['email_address'] ?? '',
            'first_name' => $data['first_name'] ?? '',
            'last_name' => $data['last_name'] ?? '',
            'company_name' => $data['company_name'] ?? $data['company'] ?? '',
            'address1' => $data['address1'] ?? $data['address'] ?? '',
            'address2' => $data['address2'] ?? '',
            'city' => $data['city'] ?? '',
            'country_id' => $state['Country']['id'] ?? null,
            'country_code' => $state['Country']['country_code'] ?? '',
            'state_id' => $state['State']['id'] ?? null,
            'state_code' => $state['State']['state_code'] ?? '',
            'zipcode' => $data['zipcode'] ?? $data['postal_code'] ?? '',
            'telephone' => $data['telephone'] ?? $data['phone'] ?? '',
        ];
    }

    private function _orderAddressErrorsToFieldErrors(array $validationErrors): array
    {
        // Use the shorter notBlank message seen on eCommerce front ends
        array_walk_recursive($validationErrors, function(&$error) {
            if ($error === 'This field cannot be left blank') {
                $error = "Can't be blank";
            }
        });

        $responseFieldMap = [
            'first_name' => 'fullname',
            'last_name' => 'fullname',
            'company_name' => 'company',
            'address1' => 'address',
            'zipcode' => 'postal_code',
            'telephone' => 'phone',
        ];
        $fieldErrors = [];
        foreach ($validationErrors as $field => $errors) {
            $fieldErrors[($responseFieldMap[$field] ?? $field)] = $errors;
        }

        return array_map('current', $fieldErrors);
    }
}
