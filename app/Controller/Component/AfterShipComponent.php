<?php
App::uses('Controller', 'Controller');
App::uses('Component', 'Controller');

use AfterShip\AfterShipException;
use AfterShip\Trackings;
use AfterShip\Couriers;
use AfterShip\LastCheckPoint;
use AfterShip\Notifications;

/**
 * Class AfterShipComponent.
 *
 * @see https://github.com/AfterShip/aftership-sdk-php
 */
class AfterShipComponent extends Component
{
    /**
     * @var string
     */
    private $api_key;

    // Access with getters for lazy loading
    private $_couriers;
    private $_trackings;
    private $_last_check_point;
    private $_notifications;

    public function startup(Controller $controller)
    {
        parent::startup($controller);
        $this->api_key = AFTERSHIP_KEY;
    }

    /**
     * @return Couriers
     * @throws AfterShipException
     */
    private function couriers(): Couriers
    {
        $this->_couriers = $this->_couriers ?: new Couriers($this->api_key);
        return $this->_couriers;
    }

    /**
     * @return Trackings
     * @throws AfterShipException
     */
    private function trackings(): Trackings
    {
        $this->_trackings = $this->_trackings ?: new Trackings($this->api_key);
        return $this->_trackings;
    }

    /**
     * @return LastCheckPoint
     * @throws AfterShipException
     */
    private function last_check_point(): LastCheckPoint
    {
        $this->_last_check_point = $this->_last_check_point ?: new LastCheckPoint($this->api_key);
        return $this->_last_check_point;
    }

    /**
     * @return Notifications
     * @throws AfterShipException
     */
    private function notifications(): Notifications
    {
        $this->_notifications = $this->_notifications ?: new Notifications($this->api_key);
        return $this->_notifications;
    }

    /**
     * Return a list of all user activated couriers.
     *
     * @return array
     * @throws AfterShipException
     * @see https://developers.aftership.com/reference/get-couriers
     */
    public function getAllCouriers()
    {
        return $this->_data($this->couriers()->get());
    }

    /**
     * Return a list of matched couriers based on tracking number format and selected couriers or a list of couriers.
     *
     * @param string $trackingNumber
     * @param array $params
     * @return array
     * @throws AfterShipException
     * @see https://developers.aftership.com/reference/post-couriers-detect
     */
    public function detectCouriers($trackingNumber, array $params = array())
    {
        return $this->_data($this->couriers()->detect($trackingNumber, $params));
    }

    /**
     * Create a tracking.
     *
     * @param string $trackingNumber
     * @param array $params
     * @return array
     * @throws AfterShipException
     * @see https://developers.aftership.com/reference/post-trackings
     */
    public function createTracking($trackingNumber, $params = array())
    {
        return $this->_trackingResponse($this->_data($this->trackings()->create($trackingNumber, $params)));
    }

    /**
     * Delete a tracking.
     *
     * @param string $slug
     * @param string $tracking_number
     * @return array
     * @throws AfterShipException
     * @see https://developers.aftership.com/reference/delete-trackings-slug-tracking_number
     */
    public function deleteTracking($slug, $tracking_number)
    {
        return $this->_data($this->trackings()->delete($slug, $tracking_number));
    }

    /**
     * Get tracking results of a single tracking.
     *
     * @param string $slug
     * @param string $trackingNumber
     * @return array
     * @throws AfterShipException
     * @see https://developers.aftership.com/reference/get-trackings-slug-tracking_number
     */
    public function getTracking($slug, $trackingNumber)
    {
        return $this->_trackingResponse($this->_data($this->trackings()->get($slug, $trackingNumber)));
    }

    /**
     * Return the tracking information of the last checkpoint of a single tracking.
     *
     * @param string $slug
     * @param string $tracking_number
     * @return array
     * @throws AfterShipException
     * @see https://developers.aftership.com/reference/get-last_checkpoint
     */
    public function getLastCheckpoint($slug, $tracking_number)
    {
        return $this->_data($this->last_check_point()->get($slug, $tracking_number));
    }

    /**
     * Check if couriers exist based on tracking number format.
     *
     * @param string $trackingNumber
     * @param string|string[] $slug
     * @return bool
     * @throws AfterShipException
     * @see AfterShipComponent::detectCouriers
     */
    public function hasCouriers($trackingNumber, $slug = array())
    {
        return (bool)Hash::get($this->detectCouriers($trackingNumber, ['slug' => $slug]), 'couriers');
    }

    /**
     * @param int $orderId
     * @param string $trackingNumber
     * @param string $slug
     * @return array
     * @throws AfterShipException
     */
    public function trackOrder($orderId, $trackingNumber, $slug)
    {
        /** @var Order $Order */
        $Order = ClassRegistry::init('Order');
        $Order->addAssociations([
            'belongsTo' => [
                'Retailer' => ['className' => 'User'],
            ],
            'hasOne' => [
                'Contact' => [
                    'foreignKey' => false,
                    'conditions' => [
                        'Contact.user_id' => $Order->identifier('Order.retailer_id'),
                        'Contact.type' => 'company',
                        'Contact.contact_medium' => 'telephone',
                    ],
                ],
            ],
        ]);
        $order = $Order->record($orderId, [
            'contain' => [
                'Retailer' => ['fields' => ['id', 'email_address', 'company_name']],
                'Contact' => ['fields' => ['id', 'value']],
            ],
            'fields' => ['id', 'orderID', 'order_type', 'shipping_telephone', 'customerEmail', 'customer_name'],
        ]);
        $Order->unbindModel([
            'belongsTo' => ['Retailer'],
            'hasOne' => ['Contact'],
        ], false);

        $sms = [$order['Contact']['value']];
        $emails = [$order['Retailer']['email_address']];
        $aftershipCustomerName = $order['Retailer']['company_name'];

        if ($order['Order']['order_type'] == Order::TYPE_SHIP_FROM_STORE) {
            $sms = [$order['Order']['shipping_telephone']];
            $emails = [$order['Order']['customerEmail']];
            $aftershipCustomerName = $order['Order']['customer_name'];
        }

        try {
            return $this->createTracking($trackingNumber, array(
                'slug' => $slug,
                'smses' => $sms,
                'emails' => $emails,
                'order_id' => $order['Order']['orderID'],
                'customer_name' => $aftershipCustomerName,
            ));
        } catch (AfterShipException $e) {
            if ($e->getMessage() !== 'BadRequest: 4003 - Tracking already exists.') {
                throw $e;
            }
            return $this->getTracking($slug, $trackingNumber);
        }
    }

    private function _trackingResponse($response): array
    {
        $tracking = $response['tracking'];

        $checkpoints = $tracking['checkpoints'];
        $checkpoint = end($checkpoints);

        $tracking['LastCheckPoint'] = $checkpoint['city'] ?? null;

        return $tracking;
    }

    /**
     * Extract the data field from an API response.
     *
     * @param array $response
     * @return array
     */
    private function _data(array $response): array
    {
        return Hash::get($response, 'data');
    }

}
