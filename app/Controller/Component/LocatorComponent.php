<?php

use ShipEarlyApp\Lib\Geolocation\Geolocation;

App::uses('AppComponent', 'Controller/Component');
App::uses('LocatorException', 'Error');
App::uses('DeliveryOptions', 'Utility');

/**
 * Locator Component.
 *
 * @property EcommerceView $EcommerceView
 * @property Store $Store
 * @property User $User
 */
class LocatorComponent extends AppComponent
{
    public $uses = [
        'EcommerceView',
        'Store',
        'User',
    ];

    /**
     * @var int|null
     */
    public $ecommerceViewId;

    public function locateDealers(int $brandId, Geolocation $location, ?int $limit, array $additionalArgs = []): array
    {
        $this->ecommerceViewId = null;

        $deliveryOptions = array_filter([DeliveryOptions::denormalize($additionalArgs['requestedDeliveryOption'] ?? null)]);
        $retailerIds = array_filter((array)($additionalArgs['requestedRetailerId'] ?? null));
        $includeNonStripeRetailers = (bool)($additionalArgs['include_non_stripe_retailers'] ?? false);
        $allowCommission = (bool)($additionalArgs['allow_commission'] ?? false);
        $page = (int)($additionalArgs['page'] ?? 1);

        $items = (array)($additionalArgs['items'] ?? []);
        $inStock = isset($additionalArgs['in_stock']) ? (bool)$additionalArgs['in_stock'] : null;

        $conditions = [];
        if ($retailerIds) {
            $conditions['User.id'] = $retailerIds;
        }
        if (!$allowCommission) {
            $conditions['ManufacturerRetailer.is_commission_tier'] = false;
        }
        if (!$includeNonStripeRetailers) {
            $conditions[] = [
                'OR' => [
                    'User.stripe_account !=' => null,
                    'ManufacturerRetailer.is_commission_tier' => true,
                ],
            ];
        }

        $fields = array_merge([
            'User.id',
            'User.distance',
            'User.distance_unit',
            'User.distance_miles',
            'User.stripe_account',
            'User.timezone',
            'ManufacturerRetailer.is_commission_tier',
        ], DeliveryOptions::getRetailOptions());

        if ($items) {
            $resultList = $this->User->findAllRetailersSellingItemsToLocation($brandId, $location, $items, $inStock, $deliveryOptions, compact('conditions', 'fields', 'limit', 'page'));
        } else {
            $resultList = $this->User->findAllRetailersSellingToLocation($brandId, $location, $deliveryOptions, compact('conditions', 'fields', 'limit', 'page'));

            $variantsInStockByRetailerId = Hash::combine(
                $this->Store->query($this->Store->productCountQuery([
                    'Product.user_id' => $brandId,
                    'Store.storeId' => Hash::extract($resultList, '{n}.User.id'),
                ])),
                '{n}.Store.retailer_id',
                '{n}.0.count'
            );
            $resultList = array_map(function(array $retailer) use ($variantsInStockByRetailerId): array {
                $retailer['User']['in_stock'] = (bool)($retailer['User']['in_stock'] ?? $variantsInStockByRetailerId[$retailer['User']['id']] ?? false);

                return $retailer;
            }, $resultList);
        }
        $retailers = array_column($resultList, 'User');

        if (array_key_exists('ecommerceViewId', $additionalArgs)) {
            $ecommerceViewId = (int)$additionalArgs['ecommerceViewId'] ?: null;
            if ($ecommerceViewId && $this->EcommerceView->exists(['EcommerceView.id' => $ecommerceViewId, 'EcommerceView.user_id' => $brandId])) {
                // By default, reflect the passed ecommerceViewId to the client so that it may be persisted.
                $this->ecommerceViewId = $ecommerceViewId;
            }
            if ($items) {
                // Product locator requests attempt to update the record
                try {
                    $ecommerceViewId = $this->EcommerceView->saveViewFromLocatorComponent($brandId, $items, $retailers, $ecommerceViewId);
                    if (!$ecommerceViewId) {
                        throw new BadRequestException(json_encode(['errors' => $this->EcommerceView->validationErrors, 'data' => $this->EcommerceView->data]));
                    }
                    $this->ecommerceViewId = $ecommerceViewId;
                } catch (Exception $e) {
                    CakeLog::warning($e);
                }
            }
        }

        return array_map(function($retailer) {
            return [
                'id' => $retailer['User']['id'],
                'distance' => $retailer['User']['distance'],
                'distance_unit' => $retailer['User']['distance_unit'],
                'in_stock' => $retailer['User']['in_stock'],
                'methods' => ($retailer['User']['stripe_account'] || $retailer['ManufacturerRetailer']['is_commission_tier'])
                    ? array_values(array_filter(DeliveryOptions::getRetailOptions(), function($method) use ($retailer) {
                        return $retailer['User'][$method];
                    }))
                    : [],
                'timezone' => $retailer['User']['timezone'],
            ];
        }, Hash::combine($resultList, '{n}.User.id', '{n}'));
    }
}
