<?php
App::uses('Component', 'Controller');
App::uses('<PERSON>ache', 'Cache');

/**
 * Class LockComponent.
 */
class LockComponent extends Component
{
    /**
     * @var CakeRequest
     */
    public $request;

    protected $_cacheConfig = 'short'; //'default';
    protected $_keyPrefix = 'Lock';

    public function initialize(Controller $controller)
    {
        parent::initialize($controller);
        $this->request = $controller->request;
    }

    /**
     * Globally locks a script under the given key.
     *
     * WARNING: Only use on scripts where only one caller is expected for the server such as most cronjobs.
     *
     * @param string $key Unique key representing a locked resource
     * @param callable $callback
     * @param array $args
     * @param bool $exit If true, exit() will be called in place of a ConcurrencyException
     * @return mixed The callback return value.
     * @throws ConcurrencyException Thrown if the resource key is already locked and $exit is false.
     */
    public function applyGlobalLock($key, callable $callback, $args = array(), $exit = true)
    {
        $this->_setGlobalLock($key, $exit);
        try {
            return call_user_func_array($callback, $args);
        } finally {
            $this->_removeGlobalLock($key);
        }
    }

    /**
     * Creates a global locks with the given key.
     *
     * WARNING: Only use on scripts where only one caller is expected for the server such as most cronjobs.
     * LockComponent::removeGlobalLock must be called with the same key when finished with the resource.
     *
     * @param string $key Unique key representing a locked resource
     * @param bool $exit If true, exit() will be called instead of throwing a ConcurrencyException
     * @return bool Success
     * @throws ConcurrencyException Thrown if the resource key is already locked and $exit is false.
     */
    protected function _setGlobalLock($key, $exit = false)
    {
        if (Cache::read($this->_key($key), $this->_cacheConfig)) {
            $e = new ConcurrencyException("[{$this->request->clientIp()}] The resource '{$key}' is already in use.");
            if ($exit) {
                CakeLog::error(strval($e));
                $this->_stop($e->getMessage() . PHP_EOL);
            }
            throw $e;
        }
        return Cache::write($this->_key($key), true, $this->_cacheConfig);
    }

    /**
     * @param string $key
     * @return bool Success
     */
    protected function _removeGlobalLock($key)
    {
        return Cache::delete($this->_key($key), $this->_cacheConfig);
    }

    /**
     * @param string $key
     * @return string
     */
    protected function _key($key)
    {
        return ($this->_keyPrefix) ? $this->_keyPrefix . '.' . $key : $key;
    }

}

/**
 * Class ConcurrencyException.
 *
 * Thrown when an attempt is made access a locked resource.
 */
class ConcurrencyException extends RuntimeException {}
