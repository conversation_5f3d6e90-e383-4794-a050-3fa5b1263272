<?php
App::uses('AppComponent', 'Controller/Component');
App::uses('OrderType', 'Utility');

/**
 * Class NotificationLogicComponent
 *
 * @property OrdersController|CronsController|WsController|ShopifyController|WoocommerceController $controller
 *
 * @property CurrencyComponent $Currency
 *
 * @property Country $Country
 * @property Courier $Courier
 * @property EmailTemplate $EmailTemplate
 * @property Fulfillment $Fulfillment
 * @property MailQueue $MailQueue
 * @property ManufacturerRetailer $ManufacturerRetailer
 * @property Notification $Notification
 * @property Order $Order
 * @property Product $Product
 * @property State $State
 * @property User $User
 */
class NotificationLogicComponent extends AppComponent
{
    public $components = [
        'Currency',
    ];

    public $uses = [
        'Country',
        'Courier',
        'EmailTemplate',
        'Fulfillment',
        'MailQueue',
        'ManufacturerRetailer',
        'Notification',
        'Order',
        'Product',
        'State',
        'User',
    ];

    /**
     * Common Order Notification function for shipearly order.
     * Common for Instore, Ship from store and Non stock orders.
     *
     * @param string $shippingMethod
     * @param int $orderId
     * @param array $retailer
     * @param array $user
     * @param array $order
     * @param string $shipping_method_subtype set to 'localdelivery' for Local Delivery case
     */
    public function handleNotification($shippingMethod, $orderId, $retailer, $user, $order, $shipping_method_subtype = '')
    {
        $this->_sendEcommerceOrderNotifications($orderId, $shippingMethod, $shipping_method_subtype);
        $this->_sendEcommerceOrderEmails($shippingMethod, $orderId, $retailer, $user, $order, $shipping_method_subtype);
    }

    public function handleShipToStoreOnlyNotification($orderId, $order, $shippingMethod, $shipping_method_subtype = '')
    {
        $this->_sendEcommerceOrderNotifications($orderId, $shippingMethod, $shipping_method_subtype);
        $this->_sendShipToStoreOnlyBrandEmail($orderId);

        $retailer = $this->User->findForEcommerceNotifications($order['retailer_id']);
        $user = Hash::get($this->User->record($order['user_id']), 'User');

        $this->_sendEcommerceOrderEmails($shippingMethod, $orderId, $retailer, $user, $order, $shipping_method_subtype, true);
    }

    /**
     * @param int $orderId
     * @return void
     * @throws Exception
     */
    public function handleSellDirectNotifications(int $orderId)
    {
        $this->Order->addAssociations([
            'belongsTo' => [
                'User',
                'Distributor' => ['className' => 'User'],
            ],
        ]);
        $order = (array)$this->Order->get($orderId, [
            'contain' => [
                'User' => ['fields' => ['id', 'email_address', 'company_name', 'timezone']],
                'Distributor' => ['fields' => ['id', 'email_address', 'company_name', 'timezone']],
            ],
            'fields' => [
                'id',
                'user_id',
                'customerEmail',
                'orderID',
                'shipping_address1',
                'shipping_address2',
                'shipping_city',
                'shipping_state',
                'shipping_country',
                'shipping_zipcode',
                'shipping_telephone',
                'created_at',
                'customer_name',
            ],
        ]);
        $this->Order->unbindModel([
            'belongsTo' => [
                'User',
                'Distributor',
            ],
        ], false);

        $variables = [
            '{site_name}' => SITE_NAME,
            '{user_name}' => $order['User']['company_name'],
            '{order_id}' => $order['Order']['orderID'],
            '{order_url}' => $this->_getOrderUrl($orderId),
            '{order_link}' => $this->makeOrderLink($order['Order']['orderID'], $orderId),
            '{order_view_button}' => $this->buildViewOrderButtonHtml($orderId),
            '{order_date}' => $this->_formatDate($order['Order']['created_at'], $order['User']['timezone']),
            '{order_details}' => $this->_getOrderDetails($orderId),
            '{customer_email}' => $order['Order']['customerEmail'],
            '{customer_name}' => $order['Order']['customer_name'],
            '{customer_telephone}' => $order['Order']['shipping_telephone'],
            '{customer_address}' => $this->emailAddressDisplayFormat(
                $order['Order']['customer_name'],
                $order['Order']['shipping_address1'],
                $order['Order']['shipping_address2'],
                $order['Order']['shipping_city'],
                $order['Order']['shipping_state'],
                $order['Order']['shipping_country'],
                $order['Order']['shipping_zipcode']
            ),
        ];

        $userId = (int)$order['Order']['user_id'];
        $emailTemplateArr = $this->EmailTemplate->getEmailTemplate('Direct Order Sale', $userId);
        $attachments = array_filter([$emailTemplateArr['EmailTemplate']['attachment']]);

        foreach ([$order['User'], $order['Distributor']] as $recipient) {
            if (!$recipient['id']) {
                continue;
            }

            $this->Notification->createForSellDirect($userId, $recipient['id'], $orderId);

            $variables = array_merge($variables, [
                '{user_name}' => $recipient['company_name'],
                '{order_date}' => $this->_formatDate($order['Order']['created_at'], $recipient['timezone']),
            ]);

            $this->sendEmail($emailTemplateArr, $variables, $recipient['email_address'], '', '', $attachments);
        }
    }

    protected function _sendEcommerceOrderNotifications($orderId, $shippingMethod, $shipping_method_subtype)
    {
        $notificationType = '';
        $msg = '';
        if ($shippingMethod == 'shipearly_shipearly') {
            $notificationType = Notification::TYPE_INSTORE_ORDER;
            $msg = 'A customer has placed an order on an in-store pick up';
            if ($shipping_method_subtype == 'localdelivery') {
                $notificationType = Notification::TYPE_LOCAL_DELIVERY_ORDER;
                $msg = 'A customer has placed an order for local delivery';
            }
        } elseif ($shippingMethod == 'shipfromstore_shipfromstore') {
            $notificationType = Notification::TYPE_SHIP_FROM_STORE_ORDER;
            $msg = 'A customer has placed an order to be shipped from store';
        }
        if (!empty($notificationType) && !empty($msg)) {
            $this->sendOrderNotifications($msg, $notificationType, $orderId);
        }
    }

    /**
     * Send order notifications for all involved users.
     *
     * Notifications appear in the header notification dropdown and are used
     * for populating the receiver's Dashboard.
     *
     * @param string $msg
     * @param string $notificationType
     * @param int $orderId
     */
    public function sendOrderNotifications($msg, $notificationType, $orderId)
    {
        $this->Order->addAssociations([
            'belongsTo' => ['Retailer' => ['className' => 'User']],
            'hasMany' => ['OrderSalesRep'],
        ]);
        $order = $this->Order->record($orderId, [
            'contain' => [
                'Retailer' => ['fields' => ['permission', 'Branch']],
                'OrderSalesRep' => ['fields' => ['id', 'order_id', 'sales_rep_id']],
            ],
            'fields' => ['id', 'user_id', 'retailer_id', 'store_associate_id', 'distributor_id'],
        ]);

        $brandId = $order['Order']['user_id'];
        $salesRepIds = array_column($order['OrderSalesRep'], 'sales_rep_id');
        $distributorId = $order['Order']['distributor_id'];
        $retailerId = $order['Order']['retailer_id'];
        $masterId = $order['Retailer']['Branch'];
        $associateId = $order['Order']['store_associate_id'];

        $this->Notification->createForBrandOrder($brandId, $orderId, $msg, $notificationType);
        foreach ($salesRepIds as $salesRepId) {
            $this->Notification->createForSalesRepOrder($salesRepId, $brandId, $orderId, $msg, $notificationType);
        }
        if (!empty($distributorId)) {
            $this->Notification->createForSalesRepOrder($distributorId, $brandId, $orderId, $msg, $notificationType);
        }
        $this->Notification->createForRetailerOrder($retailerId, $brandId, $orderId, $msg, $notificationType);
        if (!empty($masterId)) {
            $this->Notification->createForRetailerOrder($masterId, $brandId, $orderId, $msg, $notificationType);
        }
        if (!empty($associateId)) {
            $this->Notification->createForStoreAssociateOrder($associateId, $brandId, $orderId, $msg, $notificationType);
        }
    }

    public function sendPricingConfirmationEmail(int $orderId): bool
    {
        $order = $this->Order->record($orderId, [
            'fields' => ['id', 'orderID', 'user_id', 'retailer_id', 'order_type', 'payment_method'],
        ]);

        if ($order['Order']['order_type'] !== OrderType::WHOLESALE) {
            return true;
        }

        $brandId = $order['Order']['user_id'];
        $retailerId = $order['Order']['retailer_id'];
        $users = Hash::combine((array)$this->User->find('all', [
            'recursive' => -1,
            'conditions' => ['User.id' => [$brandId, $retailerId]],
            'fields' => ['id', 'company_name', 'email_address', 'avatar', 'language_code'],
        ]), '{n}.User.id', '{n}');
        $brandData = (array)$users[$brandId];
        $retailerData = (array)$users[$retailerId];

        $languageCode = (string)$retailerData['User']['language_code'] ?: null;

        $emailTemplateArr = $this->EmailTemplate->getEmailTemplate('Purchase Order Confirmation', $brandId, $languageCode);
        $emailTemplateArr['EmailTemplate']['fromEmail'] = "{$brandData['User']['company_name']}<{$brandData['User']['email_address']}>";
        $emailTemplateArr['EmailTemplate']['logo'] = $brandData['User']['avatar'];
        $pdfPath = array_filter([$emailTemplateArr['EmailTemplate']['attachment']]);
        $pdfUrl = '';
        if (empty($pdfPath)) {
            $pdfPath = [get_order_invoice_pdf_filepath($orderId)];
            $pdfUrl = $this->_invoicePdfUrl($orderId, 'b2b', $languageCode);
        }

        $variables = [];
        $variables['{brand_name}'] = $brandData['User']['company_name'];
        $variables['{retailer_name}'] = $retailerData['User']['company_name'];
        $variables['{order_id}'] = $order['Order']['orderID'];
        $variables['{order_summary}'] = $this->_getOrderDetails($order['Order']['id']);
        $variables['{site_name}'] = SITE_NAME;
        $variables['{payment_method}'] = OrderPaymentMethod::getLabel((string)$order['Order']['payment_method']);

        return $this->sendEmail($emailTemplateArr, $variables, $retailerData['User']['email_address'], '', '', $pdfPath, $pdfUrl);
    }

    protected function _sendShipToStoreOnlyBrandEmail($orderId)
    {
        $order = $this->_getDealerOrderEmailOrder($orderId);

        $brand = $order['User'];
        $retailer = $order['Retailer'];
        $salesReps = $order['SalesRep'];

        $variables = $this->_getDealerOrderEmailVariables($order, $brand, $retailer);

        $this->_sendDealerOrderBrandEmail($variables, $brand, $salesReps);
    }

    protected function _sendEcommerceOrderEmails($shippingMethod, $orderId, $retailer, $user, $order, $shipping_method_subtype = '', $shipToStoreOnly = false)
    {
        $orderId = (int)$orderId;
        $orderID = $order['orderID'];
        $customerName = $order['customer_name'];
        $secret_code = $order['secretcode'];
        $customerEmail = $order['customerEmail'];
        $subType = (string)$order['subType'] ?: null;

        $recipients = $this->User->listRetailerEmailRecipientsByEmailField($retailer['User']['id']);
        $variables = $this->formatVariables($order, $orderID, $orderId, $customerName, $retailer, $secret_code, $user, $shipping_method_subtype);

        if ($shippingMethod == 'shipearly_shipearly') {
            $variables['{order_type}'] = 'In Store Pickup';
            if ($shipping_method_subtype == 'localdelivery') {
                $variables['{order_type}'] = 'Local Delivery';
            }
            $this->InStoreCustomer($variables, $customerEmail, $orderId, $subType, $shipping_method_subtype);

            $subType = ($order['is_commission_retailer']) ? 'commission' : $subType;
            if ($shipToStoreOnly) {
                // Send the 'Commission Order Retailer' email and suppress the 'Ship to Store Sales Rep' email
                $subType = 'commission';
            }

            $this->inStoreRetailerEmail($variables, $recipients, $orderId, $subType, $shipping_method_subtype);
            $this->inStoreSalesRepEmail($variables, $orderId, $subType, $shipToStoreOnly);
        } elseif ($shippingMethod == 'shipfromstore_shipfromstore') {
            $variables['{order_type}'] = 'Ship from Store';
            $this->shipCustomer($variables, $customerEmail, $user, $orderId);

            $subType = ($order['is_commission_retailer']) ? 'commission' : $subType;

            $this->shipRetailerEmail($variables, $recipients, $orderId, $subType, $user, $shipToStoreOnly);
            $this->shipFromStoreSalesRepEmail($variables, $orderId, $subType, $shipToStoreOnly);
        }
    }

    /**
     * Sub function format input variable for Notification system
     * @param $order
     * @param $orderID
     * @param $orderId
     * @param $customerName
     * @param $retailer
     * @param $randomString
     * @return array
     */
    public function formatVariables($order, $orderID, $orderId, $customerName, $retailer, $randomString, $user, $shipping_method_ship_subtype = '')
    {
        $variables = [];
        $variables['{site_name}'] = SITE_NAME;
        $variables['{order_id}'] = $this->makeOrderLink($orderID, $orderId);
        $variables['{order_details}'] = $this->_getOrderDetails($orderId);

        $variables['{order_type}'] = $order['order_type'];
        if ($order['order_type'] == 'retailer_order') {
            $variables['{order_type}'] = 'Retailer Order';
        } elseif ($order['order_type'] == 'In_store' && $order['subType'] == 'stock') {
            $variables['{order_type}'] = 'In-Store Pickup';
            if ($shipping_method_ship_subtype == 'localdelivery') {
                $variables['{order_type}'] = 'Local Delivery';
            }
        } elseif ($order['order_type'] == 'In_store' && $order['subType'] == 'nonstock') {
            $variables['{order_type}'] = 'Ship to Store';
            if ($shipping_method_ship_subtype == 'localdelivery') {
                $variables['{order_type}'] = 'Ship to Store with Local Delivery';
            }
        } elseif ($order['order_type'] == 'Ship_store') {
            $variables['{order_type}'] = 'Ship From Store';
        }

        $variables['{customer_email}'] = $order['customer_email'];
        $variables['{customer_telephone}'] = $order['shipping_telephone'];
        //$variables['{customer_name}'] = $order['customer_firstname'] . " " . $order['customer_lastname'];
        //$variables['{customer_address}'] = $order['shipping_address']['street'] . ", <br/>" . $order['shipping_address']['city'];
        $variables['{customer_address}'] = $this->emailAddressDisplayFormat($customerName, $order['shipping_address']['street1'], $order['shipping_address']['street2'], $order['shipping_address']['city'], $order['shipping_address']['state'], $order['shipping_address']['country'], $order['shipping_address']['zipcode'], '');
        // $variables['{customer_phone}'] = $order['shipping_telephone'];
        $variables['{customer_name}'] = $customerName;
        $variables['{user_name}'] = $variables['{customer_name}'];
        $variables['{retailer_name}'] = $retailer['User']['company_name'];
        $variables['{company_name}'] = $variables['{retailer_name}'];
        $variables['{store_timing}'] = $retailer['User']['store_timing'];
        $variables['{retailer_email}'] = !empty($retailer['Contactperson']['email']) ? $retailer['Contactperson']['email'] : $retailer['User']['email_address'];
        $variables['{retailer_from_email}'] = "{$variables['{retailer_name}']}<{$variables['{retailer_email}']}>";
        $variables['{retailer_telephone}'] = $retailer['Contact']['value'];
        //$variables['{address}'] = $variables['{retailer_address}'] = $retailer['User']['address'];
        $variables['{retailer_address}'] = $this->emailAddressDisplayFormat($retailer['User']['company_name'], $retailer['User']['address'], '', $retailer['User']['city'], $retailer['State']['state_name'], $retailer['Country']['country_name'], $retailer['User']['zipcode'], '');
        $variables['{retailer_city}'] = $retailer['User']['city'];
        $variables['{city}'] = $variables['{retailer_city}'];
        $variables['{retailer_zipcode}'] = $retailer['User']['zipcode'];
        $variables['{zipcode}'] = $variables['{retailer_zipcode}'];
        $variables['{telephone}'] = $variables['{retailer_telephone}'];
        $variables['{retailer_state}'] = $retailer['State']['state_name'];
        $variables['{state}'] = $variables['{retailer_state}'];
        $variables['{retailer_country}'] = $retailer['Country']['country_name'];
        $variables['{country}'] = $variables['{retailer_country}'];
        $variables['{secret_code}'] = $randomString;
        $variables['{brand_name}'] = $user['company_name'];
        $variables['{brand_from_email}'] = "{$variables['{brand_name}']}<{$user['email_address']}>";
        $variables['{brand_company_logo}'] = $user['avatar'];

        return $variables;
    }

    /**
     * Get the Customer Preferred Language for the given order
     * @param int $orderId
     */
    protected function _getCustomerLanguage($orderId)
    {
        $this->Order->bindModel(['belongsTo' => [
            'Customer' => ['foreignKey' => 'customerID'],
        ]], false);
        $order = (array)$this->Order->get($orderId, [
            'contain' => [
                'Customer' => ['fields' => ['id', 'preferred_language']],
            ],
            'fields' => ['id', 'user_id', 'orderID'],
        ]);
        $this->Order->unbindModel(['belongsTo' => [
            'Customer',
        ]], false);

        return $order;
    }

    /**
     * Send email to instore customer with secret code.
     * @param array $variables
     * @param string $customerEmail
     * @param int $orderId
     * @param string $subType set to 'stock' or 'nonstock'
     * @param string $shipping_method_subtype set to 'localdelivery' for Local Delivery case
     */
    public function InStoreCustomer($variables, $customerEmail, $orderId, $subType, $shipping_method_subtype = '')
    {
        $templatePrefix = '';
        if ($shipping_method_subtype == 'localdelivery') {
            $templatePrefix = 'Local Delivery, ';
        }

        $order = $this->_getCustomerLanguage($orderId);

        $templateName = ($subType == 'nonstock')
            ? $templatePrefix . 'Nonstock Instore Code'
            : $templatePrefix . 'Instore Code';
        $userId = (int)$order['Order']['user_id'];

        $languageCode = (string)$order['Customer']['preferred_language'] ?: null;

        $emailTemplateArr = $this->EmailTemplate->getEmailTemplate($templateName, $userId, $languageCode);
        $emailTemplateArr['EmailTemplate']['fromEmail'] = $variables['{retailer_from_email}'];
        $emailTemplateArr['EmailTemplate']['logo'] = $variables['{brand_company_logo}'];

        $pdfPath = array_filter([$emailTemplateArr['EmailTemplate']['attachment']]);
        $pdfUrl = '';
        if (empty($pdfPath)) {
            $pdfPath = [get_order_invoice_pdf_filepath($orderId)];
            $pdfUrl = $this->_invoicePdfUrl((int)$orderId, 'b2c', $languageCode);
        }

        $this->sendEmail($emailTemplateArr, $variables, trim($customerEmail), '', '', $pdfPath, $pdfUrl);
    }

    /**
     * Send email for instore retailers.
     *
     * @param array $variables
     * @param array $recipients
     * @param int $orderId
     * @param string $subType set to 'stock' or 'nonstock'
     * @param string $shipping_method_subtype set to 'localdelivery' for Local Delivery case
     */
    public function inStoreRetailerEmail($variables, $recipients, $orderId, $subType, $shipping_method_subtype = '')
    {
        $templatePrefix = '';
        if ($shipping_method_subtype == 'localdelivery') {
            $templatePrefix = 'Local Delivery, ';
        }

        $primaryRecipient = current($recipients['to']);
        $variables['{user_name}'] = $primaryRecipient['company_name'];

        $this->Order->addAssociations([
            'belongsTo' => [
                'Retailer' => ['className' => 'User'],
            ],
        ]);

        $order = (array)$this->Order->get($orderId, [
            'contain' => [
                'Retailer' => ['fields' => ['id', 'language_code']],
            ],
            'fields' => ['id', 'user_id', 'orderID'],
        ]);

        $this->Order->unbindModel([
            'belongsTo' => [
                'Retailer',
            ],
        ], false);

        if ($subType == 'nonstock') {
            $templateName = $templatePrefix . 'Ship to Store Retailer';

            $orderID = $order['Order']['orderID'];
            $variables['{order_id}'] = '<a href="' . BASE_PATH . "orders\" title=\"{$orderID}\">{$orderID}</a>";
        } else {
            $templateName = $templatePrefix . 'Instore Retailer';
            if ($subType == 'commission') {
                $templateName = 'Commission Order Retailer';
            }
        }
        $emailTemplateArr = $this->EmailTemplate->getEmailTemplate($templateName, $order['Order']['user_id'], $order['Retailer']['language_code']);

        $pdfPath = array_filter([$emailTemplateArr['EmailTemplate']['attachment']]);
        $pdfUrl = '';

        $this->sendEmail($emailTemplateArr, $variables, array_keys($recipients['to']), array_keys($recipients['cc']), array_keys($recipients['bcc']), $pdfPath, $pdfUrl);
    }

    protected function inStoreSalesRepEmail(array $variables, int $orderId, ?string $subType, bool $shipToStoreOnly): bool
    {
        if ($shipToStoreOnly || !in_array($subType, ['nonstock', 'commission'], true)) {
            return true;
        }

        $this->Order->addAssociations([
            'belongsTo' => [
                'Distributor' => ['className' => 'User'],
            ],
            'belongsToMany' => [
                'SalesRep' => [
                    'className' => 'User',
                    'with' => 'OrderSalesRep',
                    'associationForeignKey' => 'sales_rep_id',
                    'unique' => 'keepExisting',
                ],
            ],
        ]);
        /** @var User $Distributor */
        $Distributor = $this->Order->Distributor;
        $Distributor->hasOne('Contactpersons');
        /** @var User $SalesRep */
        $SalesRep = $this->Order->SalesRep;
        $SalesRep->hasOne('Contactpersons');

        $order = $this->Order->record($orderId, [
            'contain' => [
                'Distributor' => [
                    'fields' => ['id', 'email_address'],
                    'Contactpersons' => ['fields' => ['firstname', 'lastname']],
                ],
                'SalesRep' => [
                    'with' => ['OrderSalesRep' => []],
                    'fields' => ['id', 'email_address'],
                    'Contactpersons' => ['fields' => ['firstname', 'lastname']],
                ],
            ],
            'fields' => ['id', 'user_id', 'orderID'],
        ]);

        $SalesRep->unbindModel(['hasOne' => ['Contactpersons']], false);
        $Distributor->unbindModel(['hasOne' => ['Contactpersons']], false);
        $this->Order->unbindModel([
            'belongsTo' => ['Distributor'],
            'hasAndBelongsToMany' => ['SalesRep'],
        ], false);

        if (!empty($order['Distributor']['id'])) {
            $order['SalesRep'][] = $order['Distributor'];
        }
        if (empty($order['SalesRep'])) {
            return true;
        }

        $brandId = $order['Order']['user_id'];
        $orderID = $order['Order']['orderID'];
        $salesReps = $order['SalesRep'];

        $emailTemplateArr = $this->EmailTemplate->getEmailTemplate('Ship to Store Sales Rep', $brandId);
        $pdfPath = array_filter([$emailTemplateArr['EmailTemplate']['attachment']]);
        $pdfUrl = '';
        $variables = array_merge($variables, [
            '{order_id}' => '<a href="' . BASE_PATH . "orders\" title=\"{$orderID}\">{$orderID}</a>",
        ]);
        foreach ($salesReps as $salesRep) {
            $fullname = trim($salesRep['Contactpersons']['firstname'] . ' ' . $salesRep['Contactpersons']['lastname']);
            $salesRepVariables = array_merge($variables, [
                '{sales_rep_name}' => $fullname,
                '{user_name}' => $fullname,
            ]);
            $this->sendEmail($emailTemplateArr, $salesRepVariables, $salesRep['email_address'], '', '', $pdfPath, $pdfUrl);
        }

        return true;
    }

    /**
     * Send Ship from store email to end customer
     *
     * @param array $variables
     * @param string $customerEmail
     * @param array $user
     * @param int $orderId
     */
    public function shipCustomer($variables, $customerEmail, $user, $orderId)
    {
        $variables['{company_name}'] = $user['company_name'];

        $order = $this->_getCustomerLanguage($orderId);

        $languageCode = (string)$order['Customer']['preferred_language'] ?: null;

        $emailTemplateArr = $this->EmailTemplate->getEmailTemplate('Shipfromstore Customer', $user['id'], $languageCode);
        $emailTemplateArr['EmailTemplate']['fromEmail'] = $variables['{retailer_from_email}'];
        $emailTemplateArr['EmailTemplate']['logo'] = $variables['{brand_company_logo}'];

        $pdfPath = array_filter([$emailTemplateArr['EmailTemplate']['attachment']]);
        $pdfUrl = '';
        if (empty($pdfPath)) {
            $pdfPath = [get_order_invoice_pdf_filepath($orderId)];
            $pdfUrl = $this->_invoicePdfUrl((int)$orderId, 'b2c', $languageCode);
        }

        $this->sendEmail($emailTemplateArr, $variables, trim($customerEmail), '', '', $pdfPath, $pdfUrl);
    }

    /**
     * Send ship from store email to retailer.
     *
     * @param array $variables
     * @param array $recipients
     * @param string $subType
     * @return bool
     */
    public function shipRetailerEmail($variables, $recipients, $orderId, $subType, $brand, $shipToStoreOnly)
    {
        if ($shipToStoreOnly) {
            $templateName = 'Non-Stocking Ship from Store';
        } elseif ($subType === 'nonstock') {
            $templateName = 'Shipfromstore Retailer';
        } elseif ($subType === 'commission') {
            $templateName = 'Ship from Store Initial Order Commission Retailer';
        } else {
            $templateName = 'Shipfromstore In-Stock Only Retailer';
        }

        $order = (array)$this->Order->get($orderId, [
            'contain' => [
                'Retailer' => ['fields' => ['id', 'language_code']],
            ],
            'fields' => ['id', 'user_id', 'orderID'],
        ]);

        $emailTemplateArr = $this->EmailTemplate->getEmailTemplate($templateName, $brand['id'], $order['Retailer']['language_code']);

        $primaryRecipient = current($recipients['to']);
        $variables['{user_name}'] = $primaryRecipient['company_name'];

        return $this->sendEmail($emailTemplateArr, $variables, array_keys($recipients['to']), array_keys($recipients['cc']), array_keys($recipients['bcc']));
    }

    protected function shipFromStoreSalesRepEmail(array $variables, int $orderId, ?string $subType, bool $shipToStoreOnly)
    {
        //TODO Create a new email template
        $this->inStoreSalesRepEmail($variables, $orderId, $subType, $shipToStoreOnly);
    }

    /**
     * Send In store secret code reset mail to customer
     * @param $code
     * @param $order
     * @return bool
     */
    public function codeResetMail($code, $order, $schedule_link = '')
    {
        $brandData = $this->User->record($order['Order']['user_id'], ['fields' => ['company_name', 'email_address', 'avatar']]);

        $orderCustomer = $this->_getCustomerLanguage($order['Order']['id']);

        $this->User->addAssociations([
            'belongsTo' => ['State', 'Country'],
            'hasOne' => ['Contact' => ['conditions' => ['Contact.type' => 'company', 'Contact.contact_medium' => 'telephone']]],
        ]);
        $retailerData = $this->User->record($order['Order']['retailer_id'], [
            'contain' => [
                'State' => ['fields' => ['state_name']],
                'Country' => ['fields' => ['country_name']],
                'Contact' => ['fields' => ['value']],
            ],
            'fields' => ['company_name', 'email_address', 'address', 'city', 'zipcode', 'language_code'],
        ]);
        $this->User->unbindModel([
            'belongsTo' => ['State', 'Country'],
            'hasOne' => ['Contact'],
        ], false);

        $templateName = empty($schedule_link) ? 'Secret Code Reset' : 'In-Store Pickup with Scheduling';
        if ($order['Order']['order_type'] == 'Local_delivery') {
            $templateName = empty($schedule_link) ? 'Local Delivery, Secret Code Reset' : 'Local Delivery with Scheduling';
        }

        $emailTemplateArr = $this->EmailTemplate->getEmailTemplate($templateName, $order['Order']['user_id'], $orderCustomer['Customer']['preferred_language']);
        $emailTemplateArr['EmailTemplate']['fromEmail'] = "{$retailerData['User']['company_name']}<{$retailerData['User']['email_address']}>";
        $emailTemplateArr['EmailTemplate']['logo'] = $brandData['User']['avatar'];
        $attachments = array_filter([$emailTemplateArr['EmailTemplate']['attachment']]);

        $variables = [];
        $variables['{brand_name}'] = $brandData['User']['company_name'];
        $variables['{retailer_name}'] = $retailerData['User']['company_name'];
        $variables['{retailer_address}'] = $this->emailAddressDisplayFormat($retailerData['User']['company_name'], $retailerData['User']['address'], '', $retailerData['User']['city'], $retailerData['State']['state_name'], $retailerData['Country']['country_name'], $retailerData['User']['zipcode'], '');
        $variables['{retailer_telephone}'] = $retailerData['Contact']['value'];
        $variables['{customer_name}'] = $order['Order']['customer_name'];
        $variables['{order_id}'] = $order['Order']['orderID'];
        $variables['{order_details}'] = $this->_getOrderDetails($order['Order']['id']);
        $variables['{secret_code}'] = "<b>$code</b>";
        $variables['{site_name}'] = SITE_NAME;
        $variables['{scheduling_link}'] = $schedule_link;

        return $this->sendEmail($emailTemplateArr, $variables, $order['Order']['customerEmail'], '', '', $attachments);
    }

    public function orderFulfillmentEmail($fulfillmentId)
    {
        $fulfillment = $this->Fulfillment->findForEmail($fulfillmentId);

        $order = $this->Order->findForFulfillmentEmail($fulfillment['Fulfillment']['order_id']);
        if (empty($order['Order']['id'])) {
            return;
        }

        $variables = array_merge(
            [
                '{site_name}' => SITE_NAME,
                '{order_id}' => $order['Order']['orderID'],
                '{brand_name}' => $order['User']['company_name'],
                '{customer_name}' => $order['Order']['customer_name'],
            ],
            $this->_getFulfillmentEmailVariables($fulfillment)
        );

        $emailTemplate = $this->EmailTemplate->getEmailTemplate('Direct Order Fulfillment', $order['Order']['user_id'], $order['Customer']['preferred_language']);
        $emailTemplate['EmailTemplate']['fromEmail'] = "{$order['User']['company_name']}<{$order['User']['email_address']}>";
        $emailTemplate['EmailTemplate']['logo'] = $order['User']['avatar'];

        $this->sendEmail($emailTemplate, $variables, $order['Order']['customerEmail']);
    }

    /**
     * @param int|string $fulfillmentId
     * @see NotificationLogicComponent::sendNonStockEmailsForOrder
     */
    public function dealerOrderFulfillmentEmail($fulfillmentId)
    {
        $fulfillment = $this->Fulfillment->findForEmail($fulfillmentId);

        $orderId = $fulfillment['DealerOrder']['order_id'];

        $order = $this->_getNonStockEmailOrderInfo($orderId);
        if (!in_array($order['Order']['order_type'], [OrderType::IN_STORE_PICKUP, OrderType::LOCAL_DELIVERY, OrderType::SHIP_FROM_STORE], true)) {
            //TODO what about wholesale?
            return;
        }

        $variables = array_merge(
            $this->_getNonStockEmailVariables($order),
            $this->_getFulfillmentEmailVariables($fulfillment)
        );

        if ($order['Order']['order_type'] === OrderType::SHIP_FROM_STORE) {
            $this->_sendOrderConsumerEmail('Shipfromstore Tracking Customer', $order, $variables);

            return;
        }

        $hasTracking = ($fulfillment['Fulfillment']['tracking_number'] || $fulfillment['Fulfillment']['tracking_url']);

        $templateName = 'Dealer Order Customer Notifications';
        if (!$hasTracking) {
            $templateName .= ' Without Track';
        }

        $this->_sendOrderConsumerEmail($templateName, $order, $variables);
        $this->_sendOrderRetailerEmail('Order Acceptance retailer', $order, $variables);
    }

    private function _getFulfillmentEmailVariables(array $fulfillment): array
    {
        $courierName = $fulfillment['Courier']['name'];
        $trackingNumber = $fulfillment['Fulfillment']['tracking_number'];
        $trackingUrl = $fulfillment['Fulfillment']['tracking_url'];
        $fulfillmentItemsTable = $this->_buildFulfillmentItemsTable($fulfillment['FulfillmentProduct']);

        $trackingLink = (string)($trackingNumber ?: $trackingUrl);
        if ($trackingUrl) {
            $trackingLink = "<a href=\"{$trackingUrl}\" target=\"_blank\">{$trackingLink}</a>";
        }

        return [
            '{fulfillment_tracking_company}' => $courierName,
            '{fulfillment_tracking_number}' => $trackingNumber,
            '{fulfillment_tracking_url}' => $trackingUrl,
            '{fulfillment_tracking_link}' => $trackingLink,
            '{fulfillment_items_table}' => $fulfillmentItemsTable,
            // Override legacy order vars with equivalent values
            '{order_details}' => $fulfillmentItemsTable,
            '{order_courier}' => ($courierName) ? "Courier name: <b>{$courierName}</b>" : '',
            '{order_tracking_no}' => ($trackingLink) ? "Tracking number: <b>{$trackingLink}</b>" : '',
        ];
    }

    private function _buildFulfillmentItemsTable(array $fulfillmentProducts): string
    {
        $itemSummary = <<<HTML
<table>
    <tr>
        <th style="padding: 5px;">Part #</th>
        <th style="padding: 5px;">Product Name</th>
        <th style="padding: 5px;">Quantity</th>
    </tr>
HTML;
        foreach ($fulfillmentProducts as $item) {
            $product = $item['OrderProduct']['Product'] ?? $item['DealerOrderProduct']['Product'];
            $itemSummary .= <<<HTML
    <tr>
        <td style="padding: 5px;">{$product['product_sku']}</td>
        <td style="padding: 5px;">{$product['product_title']}</td>
        <td style="padding: 5px;">{$item['quantity']}</td>
    </tr>
HTML;
        }
        $itemSummary .= <<<HTML
</table>
HTML;

        return trim(preg_replace('/\s+/', ' ', $itemSummary));
    }

    /**
     * Order Refund notification email
     *
     * @param array $order Order Details
     * @param float $amount Refund Amount
     * @param string $authUserType
     * @return bool
     */
    public function orderRefundEmail($order, $amount, $authUserType, $refundId)
    {
        if (Order::filterOrderType($order['Order']['order_type']) === Order::TYPE_SELL_DIRECT) {
            return $this->sendDirectOrderRefundEmailToConsumer($order, $amount);
        }

        $success = true;
        if ($authUserType === User::TYPE_MANUFACTURER) {
            $success = $this->sendOrderRefundEmailToRetailer($order, $amount, $refundId);
        }

        return $this->sendOrderRefundEmailToConsumer($order, $amount) && $success;
    }

    private function sendDirectOrderRefundEmailToConsumer(array $order, $amount): bool
    {
        $brandId = $order['Order']['user_id'];
        $brandData = $this->User->record($brandId, ['fields' => ['company_name', 'email_address', 'avatar']]);

        $orderCustomer = $this->_getCustomerLanguage($order['Order']['id']);

        $variables = $this->_getOrderRefundEmailVariables($order, $brandData, $brandData, $amount);

        $emailTemplateArr = $this->EmailTemplate->getEmailTemplate('Direct Order Refund', $brandId, $orderCustomer['Customer']['preferred_language']);
        $emailTemplateArr['EmailTemplate']['fromEmail'] = "{$brandData['User']['company_name']}<{$brandData['User']['email_address']}>";
        $emailTemplateArr['EmailTemplate']['logo'] = $brandData['User']['avatar'];

        return $this->sendEmail($emailTemplateArr, $variables, $order['Order']['customerEmail']);
    }

    private function sendOrderRefundEmailToRetailer(array $order, $amount, $refundId): bool
    {
        $brandId = $order['Order']['user_id'];
        $retailerId = $order['Order']['retailer_id'];
        $users = Hash::combine((array)$this->User->find('all', [
            'recursive' => -1,
            'conditions' => ['User.id' => [$brandId, $retailerId]],
            'fields' => ['id', 'company_name', 'email_address', 'avatar', 'language_code'],
        ]), '{n}.User.id', '{n}');
        $brandData = (array)$users[$brandId];
        $retailerData = (array)$users[$retailerId];

        /** @var OrderRefund $OrderRefund */
        $OrderRefund = ClassRegistry::init('OrderRefund');

        $orderRefundReason = $OrderRefund->find('first', [
            'conditions' => ['OrderRefund.id' => $refundId],
            'fields' => ['id', 'reason'],
        ]);

        $refundReason = !empty($orderRefundReason) ? $orderRefundReason['OrderRefund']['reason'] : '';
        $variables = $this->_getOrderRefundEmailVariables($order, $brandData, $retailerData, $amount, $refundReason);

        $emailTemplateArr = $this->EmailTemplate->getEmailTemplate('Brand Refund', null, $retailerData['User']['language_code']);
        $recipients = $this->User->listRetailerEmailRecipientsByEmailField($retailerId);

        return $this->sendEmail($emailTemplateArr, $variables, array_keys($recipients['to']), array_keys($recipients['cc']), array_keys($recipients['bcc']));
    }

    private function sendOrderRefundEmailToConsumer(array $order, $amount): bool
    {
        $brandId = $order['Order']['user_id'];
        $retailerId = $order['Order']['retailer_id'];
        $users = Hash::combine((array)$this->User->find('all', [
            'recursive' => -1,
            'conditions' => ['User.id' => [$brandId, $retailerId]],
            'fields' => ['id', 'company_name', 'email_address', 'avatar'],
        ]), '{n}.User.id', '{n}');
        $brandData = (array)$users[$brandId];
        $retailerData = (array)$users[$retailerId];

        $orderCustomer = $this->_getCustomerLanguage($order['Order']['id']);

        $variables = $this->_getOrderRefundEmailVariables($order, $brandData, $retailerData, $amount);

        // Email notification from retailer to consumer
        $emailTemplateArr = $this->EmailTemplate->getEmailTemplate('Order Refund', $brandId, $orderCustomer['Customer']['preferred_language']);
        $emailTemplateArr['EmailTemplate']['fromEmail'] = "{$retailerData['User']['company_name']}<{$retailerData['User']['email_address']}>";
        $emailTemplateArr['EmailTemplate']['logo'] = $brandData['User']['avatar'];

        return $this->sendEmail($emailTemplateArr, $variables, $order['Order']['customerEmail']);
    }

    private function _getOrderRefundEmailVariables($order, $brandData, $retailerData, $refundAmount, $refundReason = null): array
    {
        return [
            '{site_name}' => SITE_NAME,
            '{reason_for_refund}' => $refundReason,
            '{brand_name}' => $brandData['User']['company_name'],
            '{retailer_name}' => $retailerData['User']['company_name'],
            '{customer_name}' => $order['Order']['customer_name'],
            '{order_id}' => $order['Order']['orderID'],
            '{amount}' => number_format($refundAmount, 2),
        ];
    }

    /**
     * Notification email sent to Retailer from Brand to indicate an order has been edited.
     * @param array $order
     * @return boolean
     */
    public function sendOrderEditEmail($order)
    {
        $brandId = $order['Order']['user_id'];
        $retailerId = $order['Order']['retailer_id'];
        $users = Hash::combine((array)$this->User->find('all', [
            'recursive' => -1,
            'conditions' => ['User.id' => [$brandId, $retailerId]],
            'fields' => ['id', 'company_name', 'email_address', 'avatar', 'language_code'],
        ]), '{n}.User.id', '{n}');
        $brandData = (array)$users[$brandId];
        $retailerData = (array)$users[$retailerId];

        $emailTemplateArr = $this->EmailTemplate->getEmailTemplate('Order Edit', null, $retailerData['User']['language_code']);
        $emailTemplateArr['EmailTemplate']['fromEmail'] = "{$brandData['User']['company_name']}<{$brandData['User']['email_address']}>";
        $emailTemplateArr['EmailTemplate']['logo'] = $brandData['User']['avatar'];

        $variables = [];
        $variables['{brand_name}'] = $brandData['User']['company_name'];
        $variables['{retailer_name}'] = $retailerData['User']['company_name'];
        $variables['{order_id}'] = $order['Order']['orderID'];
        $variables['{order_summary}'] = $this->_getOrderDetails($order['Order']['id']);
        $variables['{site_name}'] = SITE_NAME;

        return $this->sendEmail($emailTemplateArr, $variables, $retailerData['User']['email_address']);
    }

    /**
     * Fetch data used by sendOrderCancellationEmail so that it can be stored
     * in a variable before the original record is possibly deleted.
     *
     * @param int $orderId
     * @return array
     * @see NotificationLogicComponent::sendOrderCancellationEmail
     */
    public function findOrderCancellationEmailData($orderId)
    {
        $order = $this->_getNonStockEmailOrderInfo($orderId);
        if (empty($order)) {
            return $order;
        }
        $variables = $this->_getNonStockEmailVariables($order);

        return $order + ['Variables' => $variables];
    }

    /**
     * @param array $orderCancellationEmailData Expects the result of findOrderCancellationEmailData.
     * @return bool Success
     * @see NotificationLogicComponent::findOrderCancellationEmailData
     */
    public function sendOrderCancellationEmail(array $orderCancellationEmailData)
    {
        $variables = $orderCancellationEmailData['Variables'];
        unset($orderCancellationEmailData['Variables']);
        $order = $orderCancellationEmailData;

        if ($order['Order']['order_type'] !== OrderType::WHOLESALE) {
            return true;
        }

        $brandId = $order['Order']['user_id'];
        $retailerId = $order['Order']['retailer_id'];
        $users = Hash::combine((array)$this->User->find('all', [
            'recursive' => -1,
            'conditions' => ['User.id' => [$brandId, $retailerId]],
            'fields' => ['id', 'company_name', 'email_address', 'avatar', 'language_code'],
        ]), '{n}.User.id', '{n}');
        $brandData = (array)$users[$brandId];
        $retailerData = (array)$users[$retailerId];

        $emailTemplateArr = $this->EmailTemplate->getEmailTemplate('Purchase Order Cancellation', null, $retailerData['User']['language_code']);
        $emailTemplateArr['EmailTemplate']['fromEmail'] = "{$brandData['User']['company_name']}<{$brandData['User']['email_address']}>";
        $emailTemplateArr['EmailTemplate']['logo'] = $brandData['User']['avatar'];

        return $this->sendEmail($emailTemplateArr, $variables, $order['Retailer']['email_address']);
    }

    /**
     * Send out emails when a Need to Confirm order is marked as in stock.
     * @param int $orderId
     * @return boolean
     */
    public function sendInStockEmailsForOrder($orderId)
    {
        $order = $this->_getNonStockEmailOrderInfo($orderId);
        $variables = $this->_getNonStockEmailVariables($order);

        switch ($order['Order']['order_type']) {
        case 'In_store':
        case 'Local_delivery':
            $this->_sendOrderConsumerEmail('NonStock Customer', $order, $variables);
            $this->_sendOrderRetailerEmail('Order Acceptance retailer', $order, $variables);

            break;
        case 'Ship_store':
            $this->_sendOrderRetailerEmail('Shipfromstore InStock Retailer', $order, $variables);

            break;
        default:
        }

        return true;
    }

    /**
     * Send out emails when an order is ready for pickup or delivery.
     *
     * @param int $orderId
     * @param string|null $trackingUrl
     * @return boolean
     */
    public function sendNonStockEmailsForOrder(int $orderId, ?string $trackingUrl = null)
    {
        $order = $this->_getNonStockEmailOrderInfo($orderId);
        $variables = $this->_getNonStockEmailVariables($order, $trackingUrl);

        switch ($order['Order']['order_type']) {
        case 'In_store':
        case 'Local_delivery':
            $templateName = 'Dealer Order Customer Notifications';
            if (!$order['Order']['courier'] || $order['Order']['trackingno'] == '') {
                $templateName .= ' Without Track';
            }
            $this->_sendOrderConsumerEmail($templateName, $order, $variables);
            $this->_sendOrderRetailerEmail('Order Acceptance retailer', $order, $variables);

            break;
        case 'Ship_store':
            break;
        default:
        }

        return true;
    }

    /**
     * Send out emails when an order is shipped with a courier.
     *
     * @param int $orderId
     * @param string|null $trackingUrl
     * @param bool $brandShipment true if the order is being shipped from a brand
     * @return boolean
     */
    public function sendShipmentTrackingEmailsForOrder(int $orderId, ?string $trackingUrl, bool $brandShipment)
    {
        $order = $this->_getNonStockEmailOrderInfo($orderId);
        $variables = $this->_getNonStockEmailVariables($order, $trackingUrl);

        switch ($order['Order']['order_type']) {
        case 'In_store':
        case 'Local_delivery':
            break;
        case 'Ship_store':
            if ($brandShipment && $this->Order->isDoubleShipOrder($orderId)) {
                $this->_sendOrderConsumerEmail('Shipfromstore Double Ship Tracking Customer', $order, $variables);
            } else {
                $this->_sendOrderConsumerEmail('Shipfromstore Tracking Customer', $order, $variables);
            }

            break;
        default:
        }

        return true;
    }

    /**
     * Send out emails when the courier has delivered the order.
     * @param int $orderId
     * @return boolean
     */
    public function sendNonStockCourierDeliveredEmail($orderId)
    {
        $orderInfo = $this->_getNonStockEmailOrderInfo($orderId);
        $variables = $this->_getNonStockEmailVariables($orderInfo);

        switch ($orderInfo['Order']['order_type']) {
        case 'In_store':
        case 'Local_delivery':
            $this->_sendOrderConsumerEmail('NonStock Customer', $orderInfo, $variables);

            break;
        case 'Ship_store':
            break;
        default:
        }

        return true;
    }

    protected function _getNonStockEmailOrderInfo($orderId): array
    {
        /** @var User $Retailer */
        $Retailer = ClassRegistry::init(['class' => 'User', 'alias' => 'Retailer']);

        $Retailer->addAssociations([
            'hasOne' => [
                'ContactPerson' => [
                    'className' => 'Contactpersons',
                    'foreignKey' => 'user_id',
                ],
                'ContactTelephone' => [
                    'className' => 'Contact',
                    'foreignKey' => 'user_id',
                    'conditions' => [
                        'ContactTelephone.type' => 'company',
                        'ContactTelephone.contact_medium' => 'telephone',
                    ],
                ],
                'Order' => [
                    'foreignKey' => 'retailer_id',
                ],
                'Brand' => [
                    'className' => 'User',
                    'foreignKey' => false,
                    'conditions' => ['Order.user_id = Brand.id'],
                ],
                'Customer' => [
                    'foreignKey' => false,
                    'conditions' => ['Order.customerID = Customer.id'],
                ],
            ],
        ]);
        $originalVirtualFields = $Retailer->virtualFields;

        $Retailer->virtualFields += $this->Order->virtualFields;

        $orderInfo = (array)$Retailer->find('first', [
            'contain' => [
                'ContactPerson' => ['fields' => ['email']],
                'ContactTelephone' => ['fields' => ['value']],
                'Order' => [
                    'fields' => [
                        'id',
                        'user_id',
                        'retailer_id',
                        'orderID',
                        'order_status',
                        'order_type',
                        'customerEmail',
                        'secretcode',
                        'dealer_qty_ordered',
                        'courier',
                        'trackingno',
                        'shipping_address1',
                        'shipping_address2',
                        'shipping_city',
                        'shipping_statecode',
                        'shipping_countrycode',
                        'shipping_zipcode',
                        'shipping_telephone',
                    ],
                ],
                'Brand' => ['fields' => ['id', 'email_address', 'company_name', 'avatar']],
                'Customer' => ['fields' => ['id', 'preferred_language']],
            ],
            'conditions' => ['Order.id' => $orderId],
            'fields' => [
                'id',
                'email_address',
                'company_name',
                'store_timing',
                'address',
                'city',
                'state_id',
                'country_id',
                'zipcode',
                'customer_name',
                'language_code',
            ],
        ]);

        $Retailer->virtualFields = $originalVirtualFields;
        $Retailer->unbindModel(['hasOne' => ['ContactPerson', 'ContactTelephone', 'Order', 'Brand', 'Customer']], false);

        if (empty($orderInfo['Retailer']['id'])) {
            return [];
        }

        $orderInfo['Order']['is_dealerorder'] = !empty($orderInfo['Order']['dealer_qty_ordered']);

        $orderInfo['Order']['customer_name'] = $orderInfo['Retailer']['customer_name'];
        unset($orderInfo['Retailer']['customer_name']);

        if (empty($orderInfo['ContactPerson']['email'])) {
            $orderInfo['ContactPerson']['email'] = $orderInfo['Retailer']['email_address'];
        }

        return $orderInfo;
    }

    protected function _getNonStockEmailVariables(array $orderInfo, ?string $trackingUrl = null): array
    {
        $order = $orderInfo['Order'];
        $brand = $orderInfo['Brand'];
        $retailer = $orderInfo['Retailer'];
        $retailer_telephone = $orderInfo['ContactTelephone']['value'];

        $customer_name = $order['customer_name'];
        $timing = json_decode($this->User->getStoreTiming($retailer['store_timing'], $retailer['id']), true);

        $trackingLink = (string)($order['trackingno'] ?: $trackingUrl);
        if ($trackingUrl) {
            $trackingLink = "<a href=\"{$trackingUrl}\" target=\"_blank\">{$trackingLink}</a>";
        }

        $courierName = '';
        $trackingNumber = '';
        if ($order['courier'] && $order['trackingno'] != '') {
            $courierName = "Courier name: <b>{$this->Courier->field('name', ['id' => $order['courier']])}</b>";
            $trackingNumber = "Tracking number: <b>{$trackingLink}</b>";
        }

        return [
            '{site_name}' => SITE_NAME,
            '{order_id}' => $order['orderID'],
            '{order_details}' => $this->_getOrderDetails($order['id']),
            '{order_courier}' => $courierName,
            '{order_tracking_no}' => $trackingNumber,
            '{customer_name}' => $customer_name,
            '{customer_address}' => $this->emailAddressDisplayFormat($customer_name, $order['shipping_address1'], $order['shipping_address2'], $order['shipping_city'], $order['shipping_statecode'], $order['shipping_countrycode'], $order['shipping_zipcode']),
            '{brand_name}' => $brand['company_name'],
            '{brand_name_link}' => '<a href="' . BASE_PATH . "contact/{$brand['id']}\">{$brand['company_name']}</a>",
            '{retailer_name}' => $retailer['company_name'],
            '{retailer_name_link}' => '<a href="' . BASE_PATH . "contact/{$retailer['id']}\">{$retailer['company_name']}</a>",
            '{retailer_address}' => $this->emailAddressDisplayFormat($retailer['company_name'], $retailer['address'], ' ', $retailer['city'], $retailer['state_id'], $retailer['country_id'], $retailer['zipcode']),
            '{retailer_email}' => $retailer['email_address'],
            '{retailer_telephone}' => $retailer_telephone,
            '{code}' => $order['secretcode'],
            '{store_timing}' => $timing['currentTime'],
        ];
    }

    protected function _sendOrderConsumerEmail($templateName, $order, $variables)
    {
        $templatePrefix = ($order['Order']['order_type'] == 'Local_delivery') ? 'Local Delivery, ' : '';

        $emailTemplate = $this->EmailTemplate->getEmailTemplate($templatePrefix . $templateName, $order['Order']['user_id'], $order['Customer']['preferred_language']);
        $emailTemplate['EmailTemplate']['fromEmail'] = "{$order['Retailer']['company_name']} <{$order['ContactPerson']['email']}>";
        $emailTemplate['EmailTemplate']['logo'] = $order['Brand']['avatar'];

        $pdfPath = array_filter([$emailTemplate['EmailTemplate']['attachment']]);
        $pdfUrl = '';

        return $this->sendEmail($emailTemplate, $variables, $order['Order']['customerEmail'], '', '', $pdfPath, $pdfUrl);
    }

    protected function _sendOrderRetailerEmail($templateName, $order, $variables)
    {
        $emailTemplate = $this->EmailTemplate->getEmailTemplate($templateName, null, $order['Retailer']['language_code']);
        if ($order['Order']['order_type'] === Order::TYPE_WHOLESALE) {
            return $this->sendEmail($emailTemplate, $variables, $order['Retailer']['email_address']);
        }

        $recipients = $this->User->listRetailerEmailRecipientsByEmailField($order['Order']['retailer_id']);

        return $this->sendEmail($emailTemplate, $variables, array_keys($recipients['to']), array_keys($recipients['cc']), array_keys($recipients['bcc']));
    }

    private function _invoicePdfUrl(int $orderId, string $type, ?string $lang): string
    {
        return (string)Router::url([
            'plugin' => null,
            'controller' => 'invoice_pdf', 'action' => 'viewPdf', 'id' => $orderId, 'type' => $type,
            '?' => ($lang ? ['lang' => $lang] : []),
        ]);
    }

    /**
     * Send Non stock order notification mail to retailer
     */
    public function sendNonStockOrderReminders()
    {
        $orders = $this->Order->findAllForNonStockOrderReminders();
        foreach ($orders as $order) {
            $orderId = $order['Order']['id'];
            $userId = $order['Order']['user_id'];
            $retailerId = $order['Order']['retailer_id'];
            $orderID = $order['Order']['orderID'];
            $type = Notification::TYPE_NONSTOCK_ORDER_REMINDER;

            $existing = $this->Notification->getEmailNotifications($retailerId, $type, $orderId);
            if ($existing == 0) {
                $notification_msg = __("Confirm inventory for order %s within the next 12 hrs or a dealer order will be placed on your behalf.", $orderID);
                $this->Notification->createForRetailerOrder($retailerId, $userId, $orderId, $notification_msg, $type);

                $variables = [
                    '{retailer_name}' => $order['Retailer']['company_name'],
                    '{order_id}' => $orderID,
                    '{order_details}' => $this->_getOrderDetails($orderId),
                    '{site_name}' => SITE_NAME,
                ];
                $emailTemplateArr = $this->EmailTemplate->getEmailTemplate('Non stock order notification', null, $order['Retailer']['language_code']);
                $recipients = $this->User->listRetailerEmailRecipientsByEmailField($retailerId);
                $this->sendEmail($emailTemplateArr, $variables, array_keys($recipients['to']), array_keys($recipients['cc']), array_keys($recipients['bcc']));
            }
        }
    }

    /**
     * Formating the order details of given order id
     *
     * @param $orderId
     * @return string
     */
    protected function _getOrderDetails($orderId)
    {
        $this->Order->hasMany('OrderProduct');
        /** @var OrderProduct $OrderProduct */
        $OrderProduct = $this->Order->OrderProduct;
        $OrderProduct->belongsTo('Product');

        $order = $this->Order->find('first', [
            'contain' => [
                'OrderProduct' => [
                    'fields' => ['id', 'order_id', 'product_id', 'quantity', 'total_price'],
                    'Product' => [
                        'fields' => ['id', 'product_sku', 'product_title', 'product_image'],
                    ],
                ],
            ],
            'conditions' => ['Order.id' => $orderId],
            'fields' => ['Order.id', 'Order.currency_code', 'Order.total_discount', 'Order.total_tax', 'Order.shipping_amount'],
        ]);

        $OrderProduct->unbindModel(['belongsTo' => ['Product']], false);
        $this->Order->unbindModel(['hasMany' => ['OrderProduct']], false);

        $subtotal = 0;
        foreach ($order['OrderProduct'] as $item) {
            $subtotal += $item['total_price'];
        }

        $subtotalAfterDiscount = format_number($subtotal - $order['Order']['total_discount'], 2);
        $total = $subtotalAfterDiscount + $order['Order']['shipping_amount'] + $order['Order']['total_tax'];
        $total_format = $order['Order']['currency_code'] . ' ' . number_format($total, 2);
        $orderSummaryLabel = __('Order Summary');
        $orderSubtotalLabel = __('Subtotal');
        $orderShippingLabel = __('Shipping');
        $orderTaxLabel = __('Tax');
        $orderTotalLabel = __('Total');

        $itemSummary = <<<HTML
            <table class="order-container" cellpadding="0" cellspacing="0" style="font-family: Helvetica Neue, Helvetica, Arial, sans-serif; width: 100%; max-width: 600px; margin: auto; border-collapse: collapse;">
                <tr>
                    <td style="font-weight: bold; font-size: 20px; color: #3b3a3a; padding-bottom: 20px;">{$orderSummaryLabel}</td>
                </tr>
HTML;
        foreach ($order['OrderProduct'] as $item) {
            $itemSummary .= <<<HTML
                <tr>
                    <td style="border-bottom: 1px solid #ccc; padding-bottom: 10px; font-weight: bold;">
                        <table width="100%" cellspacing="0" cellpadding="0" border="0">
                            <tr>
                                <td valign="top" width="60">
                                    <img src="{$item['Product']['product_image']}" width="40" style="max-height: 30px; width: 40px;" alt="Thumbnail" />
                                </td>
                                <td valign="top">
                                    {$item['Product']['product_title']} x {$item['quantity']}
                                    <br />
                                    <span style="font-size: 80%; color: #666;">
                                        {$item['Product']['product_sku']}
                                    </span>
                                </td>
                                <td align="right" valign="top">
                                    {$item['total_price']}
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
    HTML;
        }
        $itemSummary .= <<<HTML
                <tr>
                    <td style="text-align: right; border-bottom: 1px solid #ccc; padding-top: 10px; padding-bottom: 10px;">
                        <table width="100%" cellspacing="0" cellpadding="0" border="0">
                            <tr>
                                <td style="text-align: right; font-size: 18px; width: 50%;">{$orderSubtotalLabel}</td>
                                <td style="text-align: right; font-weight: bold;">{$subtotalAfterDiscount}</td>
                            </tr>
                            <tr>
                                <td style="text-align: right; font-size: 18px; width: 50%;">{$orderShippingLabel}</td>
                                <td style="text-align: right; font-weight: bold;">{$order['Order']['shipping_amount']}</td>
                            </tr>
                            <tr>
                                <td style="text-align: right; font-size: 18px; width: 50%;">{$orderTaxLabel}</td>
                                <td style="text-align: right; font-weight: bold;">{$order['Order']['total_tax']}</td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td style="text-align: right; padding-top: 10px; padding-bottom: 10px;">
                        <table width="100%" cellspacing="0" cellpadding="0" border="0">
                            <tr>
                                <td style="text-align: right; font-size: 18px; width: 50%;">{$orderTotalLabel}</td>
                                <td style="text-align: right; font-weight: bold; white-space: nowrap">{$total_format}</td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
HTML;

        return trim(preg_replace('/\s+/', ' ', $itemSummary));
    }

    /**
     * Send abandon cart notification mail to customer
     *
     * @param array $abandonCart AbandonCart record
     * @return bool
     */
    public function abandonCartNotificationMail(array $abandonCart)
    {
        $address = json_decode($abandonCart['AbandonCart']['address'], true);
        $address = $address['address'];

        $cart = json_decode($abandonCart['AbandonCart']['cart'], true);

        $token = $abandonCart['AbandonCart']['abandon_cart_token'];
        $subdomain = $abandonCart['UserSubdomain']['subdomain'];
        $cart_url = Router::url(['plugin' => 'Shopify', 'controller' => 'Shopify', 'action' => 'paymentPage', 'abandonid' => $token, 'subdomain' => $subdomain], true);

        $variables = [
            '{customer_name}' => trim($address['First_name'] . ' ' . $address['Last_name']),
            '{shop_name}' => $abandonCart['User']['company_name'],
            '{product_details}' => $this->_abandonCartProductDetails($cart, $abandonCart['User']['currency_code']),
            '{url}' => "<a href=\"{$cart_url}\">here</a>",
        ];
        $legacyVariables = [
            '{abandon_cart_subject}' => $abandonCart['User']['brand_abandon_cart_subject'] ?: 'Complete Your Purchase',
            '{abandon_cart_content}' => $abandonCart['User']['brand_abandon_cart_message'] ?: '',
        ];
        $variables += $legacyVariables;

        $emailTemplateArr = $this->EmailTemplate->getEmailTemplate('Abandon cart customer notification', $abandonCart['User']['id'], $abandonCart['AbandonCart']['preferred_language']);
        $emailTemplateArr['EmailTemplate']['fromEmail'] = "{$abandonCart['User']['company_name']} <{$abandonCart['User']['email_address']}>";
        $emailTemplateArr['EmailTemplate']['logo'] = $abandonCart['User']['avatar'];

        return $this->sendEmail($emailTemplateArr, $variables, $abandonCart['AbandonCart']['email_address']);
    }

    private function _abandonCartProductDetails($cart, $currencyCode)
    {
        $itemSummary = <<<HTML
<table class="test" border="1">
    <tr>
        <th style="padding: 5px;">Product Name</th>
        <th style="padding: 5px;">Quantity</th>
        <th style="padding: 5px;">Price</th>
    </tr>
HTML;
        foreach ($cart['items'] as $item) {
            $itemSummary .= <<<HTML
    <tr>
        <td style="padding: 5px;">{$item['title']}</td>
        <td style="padding: 5px;">{$item['quantity']}</td>
        <td style="padding: 5px;">{$this->Currency->formatCurrency($item['line_price'] / 100, $currencyCode)}</td>
    </tr>
HTML;
        }
        $itemSummary .= <<<HTML
</table>
HTML;

        return trim(preg_replace('/\s+/', ' ', $itemSummary));
    }

    /**
     * @param int|string $brandId
     * @param int|string $retailerId
     */
    public function newB2bCartSalesRepNotification($brandId, $retailerId)
    {
        $this->ManufacturerRetailer->addAssociations([
            'belongsTo' => [
                'User',
                'Retailer' => ['className' => 'User'],
            ],
            'belongsToMany' => [
                'SalesRep' => [
                    'className' => 'User',
                    'with' => 'ManufacturerRetailerSalesRep',
                    'associationForeignKey' => 'sales_rep_id',
                    'unique' => 'keepExisting',
                ],
            ],
        ]);
        $recipient = $this->ManufacturerRetailer->find('first', [
            'contain' => [
                'User' => ['fields' => ['id', 'company_name']],
                'Retailer' => ['fields' => ['id', 'company_name', 'language_code']],
                'SalesRep' => [
                    'with' => ['ManufacturerRetailerSalesRep' => []],
                    'fields' => ['id', 'company_name', 'email_address'],
                ],
            ],
            'conditions' => [
                'ManufacturerRetailer.user_id' => $brandId,
                'ManufacturerRetailer.retailer_id' => $retailerId,
            ],
            'fields' => ['id'],
        ]);
        $this->ManufacturerRetailer->unbindModel([
            'belongsTo' => ['User', 'Retailer'],
            'hasAndBelongsToMany' => ['SalesRep'],
        ], false);

        if (empty($recipient['SalesRep'])) {
            return;
        }

        $variables = [
            '{site_name}' => SITE_NAME,
            '{brand_name}' => $recipient['User']['company_name'],
            '{retailer_name}' => $recipient['Retailer']['company_name'],
        ];

        $emailTemplate = $this->EmailTemplate->getEmailTemplate('Sales Rep B2B Initiation');
        foreach ($recipient['SalesRep'] as $salesRep) {
            $salesRepVariables = array_merge($variables, [
                '{sales_rep_name}' => $salesRep['company_name'],
            ]);
            $this->sendEmail($emailTemplate, $salesRepVariables, $salesRep['email_address']);
        }
    }

    /**
     * Dealer order notification to retailer and brand.
     *
     * @param int $orderId
     * @param string $type One of ('new', 'auto')
     * @return bool
     */
    public function newDealerOrderNotification($orderId, $type = 'new')
    {
        $order = $this->_getDealerOrderEmailOrder($orderId);

        $brand = $order['User'];
        $retailer = $order['Retailer'];
        $salesReps = $order['SalesRep'];

        $variables = $this->_getDealerOrderEmailVariables($order, $brand, $retailer);

        if ($order['Order']['order_type'] === OrderType::WHOLESALE) {
            return $this->sendNewPurchaseOrderBrandNotification($orderId);
        }

        // Mail to Manufacturer
        $this->_sendDealerOrderBrandEmail($variables, $brand, $salesReps, $type);

        // Mail to Retailer
        $retailerEmailTemplateArr = ($order['Order']['order_type'] == Order::TYPE_SHIP_FROM_STORE)
            ? $this->EmailTemplate->getEmailTemplate('Shipfromstore Dealer Order Retailer', null, $retailer['language_code'])
            : $this->EmailTemplate->getEmailTemplate('Dealer order Retailer Notifications', null, $retailer['language_code']);
        $recipients = $this->User->listRetailerEmailRecipientsByEmailField($retailer['id']);
        $this->sendEmail($retailerEmailTemplateArr, $variables, array_keys($recipients['to']), array_keys($recipients['cc']), array_keys($recipients['bcc']));

        $brandId = $order['Order']['user_id'];
        $retailerId = $order['Order']['retailer_id'];
        $orderId = $order['Order']['id'];
        $notificationType = Notification::TYPE_DEALER_ORDER;

        // Notifications to Manufacturer
        $brandMessage = "{$variables['{retailer_name_link}']} placed a dealer order {$order['Order']['orderID']}";
        $this->Notification->createNotification($retailerId, $brandId, $notificationType, $orderId, $brandMessage);

        // Notifications to Retailer
        $retailerMessage = "A dealer order {$order['Order']['orderID']} placed to {$variables['{brand_name_link}']}";
        $this->Notification->createForRetailerOrder($retailerId, $brandId, $orderId, $retailerMessage, $notificationType);

        return true;
    }

    /**
     * Dealer order notification to retailer and brand.
     *
     * @param int|string $orderId
     * @return bool
     */
    public function sendNewPurchaseOrderBrandNotification($orderId): bool
    {
        $order = $this->_getDealerOrderEmailOrder($orderId);

        $brand = $order['User'];
        $retailer = $order['Retailer'];

        $variables = $this->_getDealerOrderEmailVariables($order, $brand, $retailer);

        $emailTemplateArr = $this->EmailTemplate->getEmailTemplate('Purchase Order');

        return $this->sendEmail($emailTemplateArr, $variables, $brand['email_address']);
    }

    private function _getDealerOrderEmailOrder($orderId): array
    {
        $this->Order->addAssociations([
            'belongsTo' => [
                'User',
                'Retailer' => ['className' => 'User'],
                'Distributor' => ['className' => 'User'],
            ],
            'hasMany' => [
                'OrderProduct',
            ],
            'belongsToMany' => [
                'SalesRep' => [
                    'className' => 'User',
                    'with' => 'OrderSalesRep',
                    'associationForeignKey' => 'sales_rep_id',
                    'unique' => 'keepExisting',
                ],
            ],
        ]);
        /** @var User $Retailer */
        $Retailer = $this->Order->Retailer;
        $Retailer->hasOne('Contact', [
            'conditions' => ['Contact.type' => 'company', 'Contact.contact_medium' => 'telephone'],
        ]);
        $order = $this->Order->record($orderId, [
            'contain' => [
                'User' => ['fields' => ['id', 'email_address', 'company_name']],
                'Retailer' => [
                    'fields' => ['id', 'email_address', 'company_name', 'address', 'city', 'state_id', 'country_id', 'zipcode', 'language_code'],
                    'Contact' => ['fields' => ['id', 'value']],
                ],
                'Distributor' => ['fields' => ['id', 'email_address', 'company_name']],
                'OrderProduct' => ['fields' => ['product_id']],
                'SalesRep' => [
                    'with' => ['OrderSalesRep' => []],
                    'fields' => ['id', 'email_address', 'company_name'],
                ],
            ],
            'fields' => ['id', 'user_id', 'retailer_id', 'orderID', 'order_type', 'dealer_qty_ordered', 'customer_name'],
        ]);
        $Retailer->unbindModel(['hasOne' => ['Contact']], false);
        $this->Order->unbindModel([
            'belongsTo' => ['User', 'Retailer', 'Distributor'],
            'hasMany' => ['OrderProduct'],
            'hasAndBelongsToMany' => ['SalesRep'],
        ], false);

        if (empty($order['Order']['id'])) {
            return [];
        }

        $order['Order']['dealer_qty_ordered'] = json_decode($order['Order']['dealer_qty_ordered'], true);

        if (!empty($order['Distributor']['id'])) {
            $order['SalesRep'][] = $order['Distributor'];
        }

        return $order;
    }

    private function _getDealerOrderEmailVariables($order, $brand, $retailer)
    {
        $dealerProducts = !empty($order['Order']['dealer_qty_ordered']['products'])
            ? $order['Order']['dealer_qty_ordered']['products']
            : [];
        $productIds = array_unique(
            array_merge(
                array_keys($dealerProducts),
                array_values(array_column($order['OrderProduct'], 'product_id'))
            )
        );
        $products = $this->Product->find('all', [
            'recursive' => -1,
            'conditions' => ['Product.id' => $productIds],
            'fields' => ['id', 'product_sku', 'product_title', 'product_upc'],
        ]);
        $items = array_map(function($product) use ($dealerProducts) {
            $productId = $product['Product']['id'];
            $product['quantity'] = !empty($dealerProducts[$productId]['quantity'])
                ? (int)$dealerProducts[$productId]['quantity']
                : 'In Stock';

            return $product;
        }, $products);

        $itemSummary = <<<HTML
<table border="1">
    <thead>
        <tr>
            <th style="padding: 5px;">Part #</th>
            <th style="padding: 5px;">Product Name</th>
            <th style="padding: 5px; text-align: center;">UPC</th>
            <th style="padding: 5px;">Quantity</th>
        </tr>
    </thead>
    <tbody>
HTML;
        foreach ($items as $item) {
            $itemSummary .= <<<HTML
        <tr>
            <td style="padding: 5px;">{$item['Product']['product_sku']}</td>
            <td style="padding: 5px;">{$item['Product']['product_title']}</td>
            <td style="padding: 5px;">{$item['Product']['product_upc']}</td>
            <td style="padding: 5px; text-align: center;">{$item['quantity']}</td>
        </tr>
HTML;
        }
        $itemSummary .= <<<HTML
    </tbody>
</table>
HTML;
        $itemSummary = trim(preg_replace('/\s+/', ' ', $itemSummary));

        return [
            '{site_name}' => SITE_NAME,
            '{order_id}' => $order['Order']['orderID'],
            '{order_details}' => $itemSummary,
            '{customer_name}' => $order['Order']['customer_name'],
            '{brand_name}' => $brand['company_name'],
            '{brand_name_link}' => '<a href="' . BASE_PATH . "contact/{$brand['id']}\">{$brand['company_name']}</a>",
            '{retailer_name}' => $retailer['company_name'],
            '{retailer_name_link}' => '<a href="' . BASE_PATH . "contact/{$retailer['id']}\">{$retailer['company_name']}</a>",
            '{retailer_address}' => $this->emailAddressDisplayFormat(
                $retailer['company_name'], $retailer['address'], '', $retailer['city'], $retailer['state_id'], $retailer['country_id'], $retailer['zipcode']
            ),
            '{retailer_email}' => $retailer['email_address'],
            '{retailer_telephone}' => $retailer['Contact']['value'],
        ];
    }

    private function _sendDealerOrderBrandEmail($variables, $brand, $salesReps, $type = 'new')
    {
        $emailTemplateArr = ($type == 'new')
            ? $this->EmailTemplate->getEmailTemplate('Dealer Order')
            : $this->EmailTemplate->getEmailTemplate('24 Hr Dealer Order Notification');
        $this->sendEmail($emailTemplateArr, $variables, $brand['email_address']);
        foreach ($salesReps as $salesRep) {
            $salesRepVariables = array_merge($variables, [
                '{brand_name}' => $salesRep['company_name'],
                '{brand_name_link}' => '<a href="' . BASE_PATH . "contact/{$salesRep['id']}\">{$salesRep['company_name']}</a>",
            ]);
            $this->sendEmail($emailTemplateArr, $salesRepVariables, $salesRep['email_address']);
        }
    }

    /**
     * Common function to enqueue emails from shipearly app.
     *
     * @param array $mail_data EmailTemplate model.
     * @param string[] $variable_arr
     * @param string|string[] $to_email
     * @param string|string[] $cc_mail
     * @param string|string[] $bcc_mail
     * @param string[] $attachments
     * @param string $pdfUrl
     * @return bool
     */
    public function sendEmail($mail_data, $variable_arr, $to_email, $cc_mail = [], $bcc_mail = [], $attachments = [], $pdfUrl = ''): bool
    {
        try {
            $to_email = array_values(array_filter((array)$to_email));
            $cc_mail = array_values(array_filter((array)$cc_mail));
            $bcc_mail = array_values(array_filter((array)$bcc_mail));

            foreach ($variable_arr as $key => $value) {
                $mail_data['EmailTemplate']['content'] = str_replace($key, $value, $mail_data['EmailTemplate']['content']);
                $mail_data['EmailTemplate']['subject'] = str_replace($key, $value, $mail_data['EmailTemplate']['subject']);
            }
            if ($mail_data['EmailTemplate']['bcc_admin']) {
                $bcc_mail = array_merge($bcc_mail, [SUPPORT_EMAIL]);
            }

            $response = $this->MailQueue->queueEmail($mail_data, $to_email, $cc_mail, $bcc_mail, $attachments, $pdfUrl);
            if (!$response) {
                throw new InternalErrorException(json_encode(['errors' => $this->MailQueue->validationErrors]));
            }
            CakeLog::info('Email saved to MailQueue ' . json_encode($response), 'email');

            return true;
        } catch (Exception $e) {
            CakeLog::error('Failed to save MailQueue ' . json_encode($this->MailQueue->data), 'email');
            CakeLog::error($e, 'email');

            return false;
        }
    }

    /**
     * Format the order link using order details.
     *
     * @param string $orderID
     * @param int $id
     * @return string
     */
    public function makeOrderLink($orderID, $id): string
    {
        return sprintf('<a href="%s" title="%s" target="_blank">%s</a>', $this->_getOrderUrl((int)$id), $orderID, $orderID);
    }

    public function buildViewOrderButtonHtml(int $id): string
    {
        $url = $this->_getOrderUrl($id);
        $text = __('View your order');

        // Template based on the button found in Shopify order emails.
        // Consider applying brand whitelabel settings.
        $fontFamily = h('-apple-system,BlinkMacSystemFont,"Segoe UI","Roboto","Oxygen","Ubuntu","Cantarell","Fira Sans","Droid Sans","Helvetica Neue",sans-serif');
        $bgcolor = h('#1990c6');
        $template = <<<HTML
<table style="border-spacing:0;border-collapse:collapse;">
    <tbody>
        <tr>
            <td style="font-family:{$fontFamily};border-radius:4px" align="center" bgcolor="{$bgcolor}">
                <a href="{$url}" style="font-size:16px;text-decoration:none;display:block;color:#fff;padding:20px 25px" target="_blank">{$text}</a>
            </td>
        </tr>
    </tbody>
</table>
HTML;

        return trim(preg_replace('/\s+/', ' ', $template));
    }

    private function _getOrderUrl(int $id): string
    {
        return h(Router::url(['controller' => 'orders', 'action' => 'invoice', 'id' => $id], true));
    }

    private function _formatDate(?string $datetime, ?string $timezone = null): ?string
    {
        return format_datetime_from(DATETIME_FORMAT_SQL, $datetime, DATE_FORMAT, $timezone);
    }

    /**
     * Format the customer address based on the available input
     * @param string $companyname
     * @param string $address1
     * @param string $address2
     * @param string $city
     * @param string $state
     * @param string $country
     * @param string $zipcode
     * @param string $telephone
     * @return string
     */
    public function emailAddressDisplayFormat($companyname = '', $address1 = '', $address2 = '', $city = '', $state = '', $country = '', $zipcode = '', $telephone = '')
    {
        $address = '';
        if (trim($companyname) != '') {
            $address .= '<b>' . $companyname . '</b><br/>';
        }
        if (trim($address1) != '') {
            $tempAddress = $this->User->splitAddressField($address1);
            if (isset($tempAddress[0])) {
                $address .= $tempAddress[0] . '<br/>';
            }
            if (isset($tempAddress[1]) && (trim($tempAddress[1]) != '')) {
                $address .= $tempAddress[1] . '<br/>';
            }
        }
        if (trim($address2) != '') {
            $address .= $address2 . '<br/>';
        }
        if (trim($city) != '') {
            $address .= $city . ', ';
        }
        if (trim($state) != '') {
            if (is_numeric($state)) {
                $address .= $this->State->getStateName($state, $country) . ',<br/>';
            } else {
                $address .= $state . '<br/>';
            }
        }
        if (trim($country) != '') {
            if (is_numeric($country)) {
                $address .= $this->Country->getCountryName($country) . ', ';
            } elseif (strlen($country) == 2) {
                $address .= $this->Country->getCountryNameByCode($country) . ', ';
            } else {
                $address .= $country . ', ';
            }
        }
        if (trim($zipcode) != '') {
            $address .= $zipcode . '<br/>';
        }
        if (trim($telephone) != '') {
            $address .= 'PHONE : ' . $telephone . '<br/>';
        }

        return $address;
    }
}
