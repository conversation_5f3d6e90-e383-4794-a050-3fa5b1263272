<?php
App::uses('AppComponent', 'Controller/Component');
App::uses('OrderPaymentMethod', 'Utility');
App::uses('OrderPaymentStatus', 'Utility');
App::uses('OrderStatus', 'Utility');

/**
 * Class OrderLogicComponent.
 *
 * @property OrdersController|OrderCommentsController|ProductsController $controller
 *
 * @property StripeComponent $Stripe
 *
 * @property ManufacturerRetailer $ManufacturerRetailer
 * @property Order $Order
 * @property OrderProduct $OrderProduct
 * @property OrderRefund $OrderRefund
 * @property ProductTier $ProductTier
 * @property StripeUser $StripeUser
 * @property User $User
 * @property WarehouseProductReservation $WarehouseProductReservation
 */
class OrderLogicComponent extends AppComponent
{
    public $components = [
        'Stripe.Stripe'
    ];

    public $uses = [
        'ManufacturerRetailer',
        'Order',
        'OrderProduct',
        'OrderRefund',
        'ProductTier',
        'StripeUser',
        'User',
        'WarehouseProductReservation',
    ];

    /**
     * Check if the current Auth user is in any way associated with the given order.
     *
     * @param int $orderId
     * @return array Log data containing (User)'Auth' and 'Order' models with fields intended for diagnostic logging.
     * @throws NotFoundException Thrown if the order does not exist. Message contains json log data.
     * @throws ForbiddenException Thrown if the order does not belong to the current Auth user. Message contains json log data.
     */
    public function isAuthorizedForOrder($orderId)
    {
        $authFields = [
            'id',
            'parent_brand_id',
            'Branch',
            'user_type',
            'email_address',
            'company_name',
        ];
        $log = ['Auth' => array_intersect_key(AuthComponent::user(), array_flip($authFields))];

        $this->Order->hasMany('OrderSalesRep');
        $log += $this->Order->record($orderId, [
            'contain' => [
                'OrderSalesRep' => ['fields' => ['id', 'order_id', 'sales_rep_id']],
            ],
            'fields' => [
                'id',
                'orderID',
                'source_order_name',
                'user_id',
                'retailer_id',
                'branch_id',
                'is_commission_retailer',
                'store_associate_id',
                'distributor_id',
                'created_by_user_id',
                'order_type',
                'order_status',
                'is_dealerorder',
            ],
        ]);
        $this->Order->unbindModel(['hasMany' => ['OrderSalesRep']], false);

        if (empty($log['Order']['id'])) {
            throw new NotFoundException('Order not found where id=' . json_encode($orderId));
        }

        $assocIds = array_intersect_key($log['Order'], array_flip([
            'user_id',
            'retailer_id',
            'branch_id',
            'store_associate_id',
            'distributor_id',
        ]));
        $authRetailerId = ($log['Auth']['user_type'] === User::TYPE_STAFF)
            ? $log['Auth']['Branch']
            : $log['Auth']['id'];

        if (
            !in_array($log['Auth']['id'], $assocIds) &&
            !in_array($log['Auth']['id'], array_column($log['OrderSalesRep'], 'sales_rep_id')) &&
            $log['Auth']['parent_brand_id'] != $log['Order']['user_id'] &&
            !in_array($log['Order']['retailer_id'], $this->User->listRetailerStoreIds($authRetailerId)) &&
            !$this->ManufacturerRetailer->countSalesRepUserRetailerNames($log['Auth']['id'], [
                'ManufacturerRetailer.user_id' => $log['Order']['user_id'],
                'ManufacturerRetailer.retailer_id' => ($log['Order']['branch_id'] ?? $log['Order']['retailer_id']),
            ])
        ) {
            throw new ForbiddenException(json_encode($log));
        }

        return $log;
    }

    /**
     * Calculate shipearly fees using revenueModel
     *
     * @param $total_price
     * @param string $type Either 'brand' or 'retailer'
     * @param float|null $revenueModel
     * @param float|null $default_fee
     * @param float|null $maximum_fee
     * @return float
     * @deprecated Use StripeComponent::calculateFees() instead.
     */
    public function CalculateFees($total_price, string $type, $revenueModel = null, $default_fee = null, $maximum_fee = null): float
    {
        $revenueModel = isset($revenueModel) ? (float)$revenueModel : null;
        $default_fee = isset($default_fee) ? (float)$default_fee : null;
        $maximum_fee = isset($maximum_fee) ? (float)$maximum_fee : null;

        return $this->Stripe->calculateFees((float)$total_price, $type, $revenueModel, $default_fee, $maximum_fee);
    }

    /**
     * Check the permission level between store and main retailer
     * @param $order
     */
    public function checkViewPermission($order)
    {
        if ($order['Order']['order_type'] == 'retailer_order') {
            if (!in_array($this->controller->shipearly_user['User']['id'], array($order['Order']['user_id'], $order['Order']['customerID']))) {
                $this->controller->redirect('/');
            }
        } elseif ($order['Order']['order_type'] == 'In_store' || $order['Order']['order_type'] == 'Ship_store') {
            if (!in_array($this->controller->shipearly_user['User']['id'], array($order['Order']['user_id'], $order['Order']['retailer_id']))) {
                $this->controller->redirect('/');
            }
        }
    }

    /**
     * Top selling products near your store location (old function for backup)
     * @param $selectCat
     * @param $latitude
     * @param $longitude
     * @param $offset
     * @param int $limit
     * @param int $radius
     * @param string $filter
     * @return mixed
     */
    public function topSellingProductsNearYou($selectCat, $latitude, $longitude, $offset, $limit = 10, $radius = 10, $filter = 'month')
    {
        if (empty($selectCat)) {
            return [];
        }

        $fRadius = (float)$radius;

        if (is_array($selectCat)) {
            $selectCatList = implode(',', array_map('intval', $selectCat));
        } else {
            $selectCatList = implode(',', array_map('intval', explode(',', $selectCat)));
        }

        $monthCondition = ($filter == 'month') ? 'Order.created_at BETWEEN NOW() - INTERVAL 30 DAY AND NOW()' : '1=1';

        $distanceCondition = "SQRT(POWER(({$latitude} - Order.latitude) * 110.7, 2) + POWER(({$longitude} - Order.longitude) * 75.6, 2)) * 0.621371192 <= {$fRadius}";

        $query = $this->controller->Product->find('all', [
            'recursive' => -1,
            'fields' => [
                'Product.id',
                'COUNT(OrderProduct.product_id) AS sort',
                'Product.productID',
                'Product.product_title',
                'Product.product_description',
                'Product.product_price',
                'Product.product_image',
                'Product.uuid',
                'Product.product_sku',
                'Product.invoice_amount',
                'Product.min_shipping',
                'Product.user_id',
            ],
            'joins' => [
                [
                    'table' => 'order_products',
                    'alias' => 'OrderProduct',
                    'type' => 'INNER',
                    'conditions' => [
                        'OrderProduct.product_id = Product.id',
                        'OrderProduct.status !=' => 'Cancelled'
                    ]
                ],
                [
                    'table' => 'orders',
                    'alias' => 'Order',
                    'type' => 'INNER',
                    'conditions' => [
                        'OrderProduct.order_id = Order.id'
                    ]
                ],
                [
                    'table' => 'product_categories',
                    'alias' => 'ProductCategories',
                    'type' => 'INNER',
                    'conditions' => [
                        'ProductCategories.product_id = Product.id'
                    ]
                ]
            ],
            'conditions' => [
                'Product.deleted' => '0',
                'Product.product_status' => 'Enabled',
                'ProductCategories.cat_id IN (' . $selectCatList . ')',
                $monthCondition,
                $distanceCondition
            ],
            'group' => 'Product.id',
            'order' => ['sort DESC'],
            'limit' => $limit,
            'offset' => $offset
        ]);
        return $query;
    }

    /**
     * Top selling products count near your store location (old function for backup)
     * @param $selectCat
     * @param $latitude
     * @param $longitude
     * @param int $radius
     * @return mixed
     */
    public function topSellingProductsCountNearYou($selectCat, $latitude, $longitude, $radius = 10)
    {
        if (empty($selectCat)) {
            return 0;
        }

        $fRadius = (float)($radius);
        $sXprDistance = getXprDistance($latitude, $longitude, 'Order');
        $query = "SELECT COUNT(DISTINCT `Product`.`id`) as productCount FROM `ship_products` AS `Product` INNER JOIN `ship_order_products` AS `OrderProduct` ON (`OrderProduct`.`product_id` = `Product`.`id` AND `OrderProduct`.`status` != 'Cancelled') INNER JOIN `ship_orders` AS `Order` ON (`OrderProduct`.`order_id` = `Order`.`id`) INNER JOIN `ship_product_categories` AS `ProductCategories` ON (`ProductCategories`.`product_id` = `Product`.`id`) WHERE `deleted` = '0' AND `product_status` = 'Enabled' AND `ProductCategories`.`cat_id` IN ($selectCat) AND `Order`.`created_at` BETWEEN NOW() -INTERVAL 30 DAY AND NOW() AND $sXprDistance <= '$fRadius' ORDER BY `published_at` DESC";
        $productCount = $this->controller->Product->query($query);
        return $productCount[0][0]['productCount'];
    }

    /**
     * Instore secret code generate on code reset
     * @return string
     */
    function generateCode()
    {
        // Ambiguous characters not included: 0, O, 1, l, I, 8, and B
        $characters = '2345679ACDEFGHJKLMNPQRSTUVWXYZ';
        $randomString = '';
        for ($i = 0; $i < 6; $i++) {
            $randomString .= $characters[rand(0, strlen($characters) - 1)];
        }
        return $randomString;
    }

    /**
     * Get Retailer rating based on successful order delivery
     * @param $id
     * @return float|int
     */
    public function getRetailerRating($id)
    {
        $rating = 0;
        $totalOrder = $this->getRetailerOrdersCount($id);
        $deliveredOrders = $this->controller->Order->find('first', array(
            'recursive' => -1,
            'conditions' => array(
                'order_status' => 'Delivered',
                'retailer_id' => $id,
            ),
            'fields' => array('COUNT(id) as Delivered'),
            'group' => 'retailer_id',
        ));
        if(!empty($deliveredOrders)) {
            $rating = round(($deliveredOrders[0]['Delivered'] / $totalOrder), 2) * 100;
        }
        return $rating;
    }

    /**
     * Get Retailer Total orders count
     * @param $id
     * @return float|int
     */
    public function getRetailerOrdersCount($id)
    {
        $totalOrdersCount = 0;
        $allOrders = $this->controller->Order->find('first', array(
            'recursive' => -1,
            'conditions' => array('retailer_id' => $id),
            'fields' => array('COUNT(id) as total'),
        ));
        if (isset($allOrders[0]['total']) && $allOrders[0]['total'] != 0) {
            $totalOrdersCount = $allOrders[0]['total'];
        }
        return $totalOrdersCount;
    }

    /**
     * Common function triggered when a dealer order is placed.
     *
     * @param int $orderId
     * @param array $dealer_qty_ordered will set ['products'][$productId]['quantity']
     *   to provided values except on ship from store orders
     * @return bool
     */
    public function dealerOrderEvent($orderId, $dealer_qty_ordered = []): bool
    {
        $order = $this->Order->record($orderId, [
            'fields' => [
                'id',
                'user_id',
                'retailer_id',
                'branch_id',
                'b2b_ship_to_user_id',
                'order_type',
                'order_status',
                'shipping_statecode',
                'shipping_countrycode',
            ],
        ]);
        if ($order['Order']['order_status'] !== OrderStatus::NEED_TO_CONFIRM) {
            return false;
        }

        $success = (bool)$this->Order->save([
            'id' => $orderId,
            'order_status' => OrderStatus::DEALER_ORDER,
            'dealer_qty_ordered' => json_encode($this->ProductTier->calcNewDealerOrderPricing($order, (array)$dealer_qty_ordered)),
        ]);

        $this->reserveNewDealerOrderInventory($orderId);

        return $success;
    }

    public function reserveNewDealerOrderInventory($orderId)
    {
        $dealer_qty_ordered = json_decode($this->Order->field('dealer_qty_ordered', ['id' => $orderId]), true);

        $productQuantities = Hash::combine(
            $this->OrderProduct->findAllWithDerivedQuantities($orderId),
            '{n}.OrderProduct.product_id',
            '{n}.OrderProduct'
        );

        return $this->WarehouseProductReservation->reserveLineItemSet($orderId, array_map(
            function($productId) use ($dealer_qty_ordered, $productQuantities) {
                $product = $dealer_qty_ordered['products'][$productId];
                $orderProduct = $productQuantities[$productId] ?? [];

                return [
                    'product_id' => $productId,
                    'warehouse_id' => $product['warehouse_id'] ?? $orderProduct['warehouse_id'] ?? null,
                    'inventory_transfer_id' => $product['inventory_transfer_id'] ?? $orderProduct['inventory_transfer_id'] ?? null,
                    'quantity' => max($product['quantity'] - ($orderProduct['refunded_quantity'] ?? 0), 0),
                ];
            },
            array_keys(($dealer_qty_ordered['products'] ?? []))
        ));
    }

    /**
     * Get Retailer count
     * @param $id
     * @return int
     */
    public function getRetailersCount($id)
    {
        $retailerCount = 0;
        /** @var ManufacturerRetailer $retailerModel */
        $retailerModel = ClassRegistry::init('ManufacturerRetailer');
        $retailers = $retailerModel->find('first', array(
            'recursive' => -1,
            'conditions' => array(
                'user_id' => $id,
                'status' => ManufacturerRetailer::STATUS_CONNECTED,
            ),
            'fields' => array('COUNT(id) as total'),
        ));
        if (isset($retailers[0]['total']) && $retailers[0]['total'] != 0) {
            $retailerCount = $retailers[0]['total'];
        }
        return $retailerCount;
    }

    /**
     * @param int $newOrderId
     * @return bool
     */
    public function updateChargeForNewOrder(int $newOrderId): bool
    {
        try {
            $newOrder = $this->Order->record($newOrderId, [
                'fields' => [
                    'id',
                    'user_id',
                    'retailer_id',
                    'is_commission_retailer',
                    'order_type',
                    'orderID',
                    'orderNO',
                    'source_order_name',
                    'payment_method',
                    'payment_status',
                    'total_price',
                    'total_discount',
                    'transactionID',
                    'stripe_account',
                    'shipearlyFees',
                ],
            ]);
            if (empty($newOrder['Order']['id'])) {
                throw new NotFoundException(__('Order not found where id=%s', json_encode($newOrderId)));
            }
            if (!$newOrder['Order']['transactionID'] || $newOrder['Order']['payment_method'] !== OrderPaymentMethod::STRIPE) {
                throw new BadRequestException(__('Invalid Stripe charge for order: %s', json_encode($newOrder['Order'])));
            }

            $stripe_account = $this->StripeUser->getOrderAccountId($newOrderId, $newOrder['Order']);

            $descriptionID = $this->Order->getSourceOrderName($newOrder) ?: $newOrder['Order']['orderID'];
            $charge = $this->Stripe->updateCharge($stripe_account, $newOrder['Order']['transactionID'], [
                'description' => "Order {$descriptionID}",
            ]);

            $success = $this->Order->save([
                'id' => $newOrderId,
                'balance_transaction_id' => $charge->balance_transaction,
                'stripe_fees' => !empty($charge->balance_transaction)
                    ? $this->Stripe->retrieveStripeFees($stripe_account, $charge)
                    : $this->Stripe->estimateStripeFees($newOrder['Order']['total_price'] - $newOrder['Order']['total_discount']),
                'risk_level' => $charge->outcome->risk_level ?? Order::RISK_LEVEL_NOT_ASSESSED,
            ]);
            if (!$success) {
                throw new InternalErrorException(json_encode(['message' => 'Failed to save charge update', 'errors' => $this->Order->validationErrors, 'data' => $this->Order->data]));
            }

            if ($newOrder['Order']['payment_status'] == OrderPaymentStatus::PAID && !$charge->captured) {
                $success = $this->captureStripeCharge($newOrderId, $newOrder['Order']);
                if (!$success) {
                    throw new InternalErrorException(json_encode(['message' => 'Failed to save charge capture', 'errors' => $this->Order->validationErrors, 'data' => $this->Order->data]));
                }
            }

            return (bool)$success;
        } catch (Exception $e) {
            CakeLog::error($e);

            return false;
        }
    }

    /**
     * @param int $orderId
     * @param array $orderFields Optionally pass in values from a prefetched order record.
     * @return bool Success
     * @throws HttpException
     * @throws \Stripe\Exception\ApiErrorException
     */
    public function captureStripeCharge(int $orderId, array $orderFields = []): bool
    {
        $fieldNames = [
            'id',
            'user_id',
            'retailer_id',
            'is_commission_retailer',
            'order_type',
            'payment_method',
            'total_price',
            'total_discount',
            'transactionID',
            'stripe_account',
            'shipearlyFees',
        ];
        $orderFields = $this->Order->resolveProvidedFields($orderId, $orderFields, $fieldNames);
        if (empty($orderFields['id'])) {
            throw new NotFoundException(__('Order not found where id=%s', $orderId));
        }
        if (!$orderFields['transactionID'] || $orderFields['payment_method'] !== OrderPaymentMethod::STRIPE) {
            throw new BadRequestException(__('Invalid Stripe charge for order: %s', json_encode($orderFields)));
        }

        try {
            $refund = $this->OrderRefund->getTotalsByOrder($orderId);
            $amount = $orderFields['total_price'] - $orderFields['total_discount'] - $refund['OrderRefund']['total_amount'];
            $application_fee = $orderFields['shipearlyFees'] - $refund['OrderRefund']['total_fees'];

            $stripe_account = $this->StripeUser->getOrderAccountId($orderId, $orderFields);

            $charge = $this->Stripe->captureCharge($stripe_account, $orderFields['transactionID'], [
                'amount' => (int)round($amount * 100),
                'application_fee_amount' => (int)round($application_fee * 100),
            ]);

            return (bool)$this->Order->save([
                'id' => $orderId,
                'payment_status' => OrderPaymentStatus::PAID,
                'payment_captured_at' => $this->date(),
                'balance_transaction_id' => $charge->balance_transaction,
                'stripe_fees' => !empty($charge->balance_transaction)
                    ? $this->Stripe->retrieveStripeFees($stripe_account, $charge)
                    : $this->Stripe->estimateStripeFees($amount),
            ]);
        } catch (\Stripe\Exception\ApiErrorException $e) {
            CakeLog::error('[' . get_class($e) . '] ' . json_encode($e->getJsonBody()));

            throw $e;
        }
    }

    /**
     * Validates a refund request before processing it in Stripe. On success, returns
     * the request data to be saved but does not perform the saving action.
     * @param array $request containing 'OrderRefund' and 'OrderRefundProduct' data
     * to be processed as well as the target order id under in the field 'orderID'
     * @return string JSON containing 'status' and 'message'
     */
    public function orderRefund(array $request)
    {
        CakeLog::debug(json_encode($request));

        $orderId = (int)$request['orderID'];

        $orderInfo = $this->Order->record($orderId);
        if (empty($orderInfo['Order']['id'])) {
            CakeLog::error('Order not found where ' . json_encode(compact('orderId')));
            return json_encode(['status' => 'error', 'message' => 'Order not found']);
        }

        if (empty($orderInfo['Order']['transactionID'])) {
            CakeLog::error('Stripe charge not found for order ' . json_encode($orderInfo['Order']));
            return json_encode(['status' => 'error', 'message' => 'Stripe charge not found for order']);
        }
        $charge_id = $orderInfo['Order']['transactionID'];

        // Filter user input
        foreach (['restocking_fees', 'shipping_portion', 'tax_portion', 'amount'] as $fieldName) {
            $request['OrderRefund'][$fieldName] = round($request['OrderRefund'][$fieldName], 2);
        }

        if ($request['OrderRefund']['shipping_portion'] < 0) {
            return json_encode(['status' => 'error', 'message' => 'Shipping Refund cannot be negative']);
        }
        if ($request['OrderRefund']['restocking_fees'] < 0) {
            return json_encode(['status' => 'error', 'message' => 'Restocking Fees cannot be negative']);
        }
        if ($request['OrderRefund']['amount'] <= 0) {
            return json_encode(['status' => 'error', 'message' => 'Please provide a valid refund amount']);
        }

        // Refund product quantity validations
        $orderProductQuantities = array_filter(Hash::combine(
            $this->OrderProduct->findAllWithDerivedQuantities($orderId),
            '{n}.OrderProduct.id',
            '{n}.OrderProduct.remaining_quantity'
        ));

        $request['OrderRefundProduct'] = array_filter($request['OrderRefundProduct'], function($orderRefundProduct) use ($orderProductQuantities) {
            return array_key_exists($orderRefundProduct['order_product_id'], $orderProductQuantities)
                   && $orderRefundProduct['quantity'] != 0;
        });
        foreach ($request['OrderRefundProduct'] as $orderRefundProduct) {
            $maxQuantity = $orderProductQuantities[ $orderRefundProduct['order_product_id'] ];
            if ($orderRefundProduct['quantity'] > $maxQuantity) {
                return json_encode(['status' => 'error', 'message' => 'Refund quantity exceeds the remaining quantity']);
            }
            if ($orderRefundProduct['quantity'] < 0) {
                return json_encode(['status' => 'error', 'message' => 'Refund quantity cannot be negative']);
            }
        }

        $nextOrderProductQuantities = $orderProductQuantities;
        foreach ($request['OrderRefundProduct'] as $orderRefundProduct) {
            $nextOrderProductQuantities[ $orderRefundProduct['order_product_id'] ] -= $orderRefundProduct['quantity'];
        }

        // Fetch order details
        $refundTotals = $this->OrderRefund->getTotalsByOrder($orderId);

        $orderTotal = round($orderInfo['Order']['total_price'] - $orderInfo['Order']['total_discount'], 2);

        $nextTotalAmount = round($refundTotals['OrderRefund']['total_amount'] + $request['OrderRefund']['amount'], 2);
        $nextTotalShippingPortion = round($refundTotals['OrderRefund']['total_shipping_portion'] + $request['OrderRefund']['shipping_portion'], 2);

        // Figure out application fee refund amount
        $prevFeeRefundTotal = $refundTotals['OrderRefund']['total_fees'];
        $nextFeeRefundTotal = round($orderInfo['Order']['shipearlyFees'] * $nextTotalAmount / $orderTotal, 2);
        $feeRefundAmount = round($nextFeeRefundTotal - $prevFeeRefundTotal, 2);

        if ($nextTotalAmount > $orderTotal) {
            return json_encode(['status' => 'error', 'message' => 'The refund amount exceeds the remaining balance of the order']);
        }
        if ($nextTotalShippingPortion > $orderInfo['Order']['shipping_amount']) {
            return json_encode(['status' => 'error', 'message' => 'The shipping refund value exceeds the remaining shipping available']);
        }

        if ($orderInfo['Order']['payment_method'] !== OrderPaymentMethod::STRIPE) {
            CakeLog::error("Unknown payment method '{$orderInfo['Order']['payment_method']}' for order '{$orderInfo['Order']['orderID']}'.");
            return json_encode(['status' => 'error', 'message' => 'There was an issue processing the refund']);
        }

        $stripe_account = $this->StripeUser->getOrderAccountId($orderId, $orderInfo['Order']);

        // Refund Customer
        try {
            $refund_amount = $request['OrderRefund']['amount'];
            if ($nextTotalAmount == $orderTotal) {
                // Full refund even if the charge is uncaptured
                $refund_amount = null;
            }
            $charge = $this->Stripe->refundCharge($stripe_account, $charge_id, $refund_amount);
        } catch (Exception $e) {
            CakeLog::error("Failed to refund order '{$orderInfo['Order']['orderID']}' " . strval($e));
            return json_encode(['status' => 'error', 'message' => 'There was an issue processing the refund']);
        }

        $stripeFees = !empty($charge->balance_transaction)
            ? $this->Stripe->retrieveStripeFees($stripe_account, $charge)
            : $this->Stripe->estimateStripeFees($orderTotal - $nextTotalAmount);

        if (!empty($charge->application_fee)) {
            try {
                if ($charge->captured && $orderInfo['Order']['payment_status'] != OrderPaymentStatus::PAID) {
                    $stripeFeeRefund = $orderInfo['Order']['stripe_fees'] - $stripeFees;
                    $retailerFeeRefund = $request['OrderRefund']['amount'] - $stripeFeeRefund - $feeRefundAmount;
                    CakeLog::debug("Refunding {$retailerFeeRefund} from held splitpayment balance");
                    $this->Stripe->refundFees($charge->application_fee, $retailerFeeRefund);
                }
                CakeLog::debug("Refunding {$feeRefundAmount} from shipearly fees");
                $this->Stripe->refundFees($charge->application_fee, $feeRefundAmount);
            } catch (Exception $e) {
                CakeLog::error("Failed to refund fees for order '{$orderInfo['Order']['orderID']}' " . strval($e));
                return json_encode(array('status' => 'error', 'message' => 'There was an issue processing the refund'));
            }
        }

        // Format data to be saved
        $request['OrderRefund']['order_id'] = $orderId;
        $request['OrderRefund']['fees'] = $feeRefundAmount;
        $request['OrderRefund']['transaction_id'] = $charge_id;

        $request['Order'] = [
            'id' => $orderId,
            'stripe_fees' => $stripeFees,
            'order_status' => $orderInfo['Order']['order_status'],
            'payment_status' => $orderInfo['Order']['payment_status'],
        ];
        if (
            $nextTotalAmount == $orderTotal
            || !array_has_any($nextOrderProductQuantities, fn($quantity) => $quantity > 0)
        ) {
            if ($orderInfo['Order']['payment_status'] != OrderPaymentStatus::PAID) {
                $request['Order']['order_status'] = OrderStatus::VOIDED;
                $request['Order']['payment_status'] = OrderPaymentStatus::VOIDED;
            } else {
                $request['Order']['order_status'] = OrderStatus::REFUNDED;
            }
        }

        return json_encode(['status' => 'ok', 'message' => ['orderInfo' => $orderInfo, 'request' => $request]]);
    }

}
