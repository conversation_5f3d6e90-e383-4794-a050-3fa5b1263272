<?php

use ShipEarlyApp\Lib\Stripe\StripePaymentMethodDetails;
use ShipEarlyApp\Lib\Utility\UserSiteType;

App::uses('AppComponent', 'Controller/Component');
App::uses('Order', 'Model');
App::uses('OrderPaymentMethod', 'Utility');
App::uses('OrderPaymentStatus', 'Utility');
App::uses('OrderType', 'Utility');

/**
 * Class OrderPlacerComponent.
 *
 * Contains functions extracted from common procedures during payment on Ecommerce platforms.
 *
 * @property StripeComponent $Stripe
 * @property ShopifyComponent $Shopify
 * @property ShopifyWebhookHandlerComponent $ShopifyWebhookHandler
 * @property ShopifyPOSComponent $ShopifyPOS
 * @property QuickbookComponent $Quickbook
 * @property VendPOSComponent $VendPOS
 * @property WoocommerceComponent $Woocommerce
 * @property SquarePosComponent $SquarePos
 * @property LightspeedComponent $Lightspeed
 *
 * @property Btask $Btask
 * @property Customer $Customer
 * @property DealerOrder $DealerOrder
 * @property ManufacturerRetailer $ManufacturerRetailer
 * @property Order $Order
 * @property ProductStateFee $ProductStateFee
 * @property RetailerCredit $RetailerCredit
 * @property StripeUser $StripeUser
 * @property User $User
 * @property WarehouseProduct $WarehouseProduct
 *
 * @property WsController|ShopifyController|WoocommerceController|B2bCartsController|OrdersController $controller
 */
class OrderPlacerComponent extends AppComponent
{
    public $components = [
        'Stripe.Stripe',
        'Shopify.Shopify',
        'Shopify.ShopifyWebhookHandler',
        'Shopifypos.ShopifyPOS',
        'Quickbook.Quickbook',
        'Vendpos.VendPOS',
        'Woocommerce.Woocommerce',
        'SquarePos',
        'Lightspeed',
    ];

    public $uses = [
        'Btask',
        'Customer',
        'DealerOrder',
        'ManufacturerRetailer',
        'Order',
        'ProductStateFee',
        'RetailerCredit',
        'StripeUser',
        'User',
        'WarehouseProduct',
    ];

    /**
     * @param string[] $address
     * @param string $email
     * @return string[]
     */
    public function formatAddress($address, $email)
    {
        $street = $address['address'];
        if (!empty($address['address2'])) {
            $street .= " " . $address['address2'];
        }
        return array(
            'email' => $email,
            'firstname' => $address['First_name'],
            'lastname' => $address['Last_name'],
            'company' => $address['company'],
            'street' => $street,
            'city' => $address['city'],
            'region' => $address['regionName'],
            'postcode' => $address['PostalCode'],
            'country_id' => $address['countryName'],
            'telephone' => $address['phone'],
        );
    }

    /**
     * @param array $b2bCart
     * @param string $paymentMethod
     * @param string|null $setupId
     * @return StripePaymentMethodDetails|null
     * @throws UserFriendlyException
     * @throws \Stripe\Exception\ApiErrorException
     * @throws HttpException
     */
    public function reservePurchaseOrderPayment(array $b2bCart, string $paymentMethod, ?string $setupId): ?StripePaymentMethodDetails
    {
        $brandId = (int)$b2bCart['B2bCart']['user_id'];
        $retailerId = (int)$b2bCart['B2bCart']['retailer_id'];
        $amount = (float)$b2bCart['B2bCart']['total_price'];

        if ($paymentMethod === OrderPaymentMethod::STRIPE) {
            $setupAccount = $this->StripeUser->getAccountId($brandId);
            $paymentAccount = $setupAccount;

            list($payment, ) = $this->Stripe->cloneCardSetupToPayments($setupId, $setupAccount);

            return $this->Stripe->getPaymentMethodDetails($payment, $paymentAccount);
        } elseif ($paymentMethod === OrderPaymentMethod::CREDIT) {
            if (!$this->RetailerCredit->hasAvailableCredit($amount, $brandId, $retailerId)) {
                $remainingCredit = $this->RetailerCredit->fetchAvailableCredit($brandId, $retailerId);

                throw new UserFriendlyException("Your total of '{$amount}' exceeds your remaining account balance of '{$remainingCredit}'.");
            }
        } elseif ($paymentMethod === OrderPaymentMethod::EXTERNAL) {
            // Do nothing; payment is reserved externally
        } else {
            throw new BadRequestException("Unknown payment method '{$paymentMethod}'.");
        }

        return null;
    }

    /**
     * Create and save a new payment to a new purchase order split from the original purchase order.
     *
     * @param int $originalOrderId
     * @param int $splitOrderId
     * @return bool Success. False if a 2nd payment should have been created but was not.
     * @throws HttpException
     * @throws \Stripe\Exception\ApiErrorException Thrown upon failing to update the original payment
     */
    public function splitUnconfirmedPurchaseOrderPayment(int $originalOrderId, int $splitOrderId): bool
    {
        $this->Order->bindModel(['hasOne' => ['OrderFulfillmentGroup']], false);
        $orderById = Hash::combine(
            (array)$this->Order->find('all', [
                'contain' => [
                    'OrderFulfillmentGroup' => ['fields' => ['groupID']],
                ],
                'conditions' => [
                    'Order.id' => [$originalOrderId, $splitOrderId],
                ],
                'fields' => [
                    'id',
                    'user_id',
                    'retailer_id',
                    'distributor_id',
                    'orderID',
                    'order_type',
                    'is_commission_retailer',
                    'payment_method',
                    'total_price',
                    'total_discount',
                    'currency_code',
                    'transactionID',
                    'stripe_account',
                ],
            ]),
            '{n}.Order.id',
            '{n}'
        );
        $this->Order->unbindModel(['hasOne' => ['OrderFulfillmentGroup']], false);

        $originalOrder = (array)$orderById[$originalOrderId];
        if (!$originalOrder) {
            throw new InternalErrorException('Original order not found for id=' . json_encode($originalOrderId));
        }
        $splitOrder = (array)$orderById[$splitOrderId];
        if (!$splitOrder) {
            throw new InternalErrorException('Split order not found for id=' . json_encode($splitOrderId));
        }
        if (!$splitOrder['OrderFulfillmentGroup']['groupID'] || $splitOrder['OrderFulfillmentGroup']['groupID'] !== $originalOrder['OrderFulfillmentGroup']['groupID']) {
            throw new InternalErrorException(json_encode([
                'message' => 'Orders do not belong to the same group',
                'splitOrder' => $splitOrder,
                'originalOrder' => $originalOrder,
            ]));
        }

        $originalOrderID = (string)$originalOrder['Order']['orderID'];
        $paymentMethod = (string)$originalOrder['Order']['payment_method'];

        if ($paymentMethod === OrderPaymentMethod::STRIPE) {
            $originalPaymentId = (string)$originalOrder['Order']['transactionID'];
            if (!$originalPaymentId) {
                CakeLog::info("Skipping split payment for legacy purchase order '{$originalOrderID}'");

                return true;
            }

            $stripe_account = $this->StripeUser->getOrderAccountId($originalOrderId, $originalOrder['Order']);
            if (!$stripe_account) {
                throw new InternalErrorException(json_encode([
                    'message' => 'No Stripe account found for brand',
                    'User' => $this->User->record($originalOrder['Order']['user_id'], ['fields' => ['id', 'email_address', 'company_name']])['User'],
                ]));
            }

            $originalPayment = $this->Stripe->updateUnconfirmedPayment($stripe_account, $originalPaymentId, [
                'amount' => (int)round(((float)$originalOrder['Order']['total_price'] - (float)$originalOrder['Order']['total_discount']) * 100),
                'currency' => strtolower((string)$originalOrder['Order']['currency_code']),
                'description' => $originalOrderID,
            ]);

            try {
                $this->_createSplitOrderPayment($splitOrder, $originalPayment, $stripe_account);
            } catch (\Stripe\Exception\ApiErrorException $e) {
                CakeLog::error($e);

                return false;
            }
        }

        return true;
    }

    /**
     * @param array $splitOrder
     * @param \Stripe\PaymentIntent $originalPayment
     * @param string $stripe_account
     * @return void
     * @throws \Stripe\Exception\ApiErrorException
     */
    protected function _createSplitOrderPayment(array $splitOrder, \Stripe\PaymentIntent $originalPayment, string $stripe_account): void
    {
        $splitOrderId = (int)$splitOrder['Order']['id'];

        $copiedParams = array_intersect_key($originalPayment->jsonSerialize(), array_flip([
            'capture_method',
            'customer',
            'description',
            'metadata',
            'payment_method',
            'shipping',
        ]));
        // Payment intents are strict about setting an empty shipping param
        if (empty($copiedParams['shipping'])) {
            unset($copiedParams['shipping']);
        }
        // Same for payment_method but it being empty is a cause for concern.
        if (!$copiedParams['payment_method']) {
            unset($copiedParams['payment_method']);
            CakeLog::warning(json_encode([
                'message' => 'The split payment payment_method is empty. The original payment may have been declined.',
                'orderID' => $splitOrder['Order']['orderID'],
                'original_payment_intent' => $originalPayment->id,
            ]));
        }

        $copiedParams['description'] = (string)$splitOrder['Order']['orderID'];
        $copiedParams['metadata']['orderId'] = $splitOrderId;

        $splitPayment = $this->Stripe->savePaymentIntent(
            null,
            (float)$splitOrder['Order']['total_price'] - (float)$splitOrder['Order']['total_discount'],
            (string)$splitOrder['Order']['currency_code'],
            $copiedParams,
            ['stripe_account' => $stripe_account]
        );
        $splitPaymentDetails = $this->Stripe->getPaymentMethodDetails($splitPayment, $stripe_account);

        if (!$this->Order->save(['id' => $splitOrderId] + $splitPaymentDetails->toArray())) {
            throw new InternalErrorException(json_encode([
                'message' => 'Failed to update payment for split purchase order',
                'errors' => $this->Order->validationErrors,
                'data' => $this->Order->data,
            ]));
        }
    }

    /**
     * @param int $orderId
     * @param float $amount
     * @param bool $bypassCreditCheck
     * @return void
     * @throws Exception
     */
    public function chargePurchaseOrder(int $orderId, float $amount, bool $bypassCreditCheck = false): void
    {
        $order = $this->Order->get($orderId, [
            'fields' => [
                'id',
                'user_id',
                'retailer_id',
                'distributor_id',
                'orderID',
                'order_type',
                'is_commission_retailer',
                'payment_method',
                'currency_code',
                'transactionID',
                'stripe_account',
            ],
        ]);

        $orderID = (string)$order['Order']['orderID'];
        $brandId = (int)$order['Order']['user_id'];
        $retailerId = (int)$order['Order']['retailer_id'];
        $paymentMethod = (string)$order['Order']['payment_method'];

        if ($paymentMethod === OrderPaymentMethod::STRIPE) {
            $stripe_account = $this->StripeUser->getOrderAccountId($orderId, $order['Order']);
            if (!$stripe_account) {
                throw new InternalErrorException(json_encode([
                    'message' => 'No Stripe account found for brand',
                    'User' => $this->User->record($brandId, ['fields' => ['id', 'email_address', 'company_name']])['User'],
                ]));
            }

            $paymentId = (string)$order['Order']['transactionID'] ?: null;
            $currencyCode = (string)$order['Order']['currency_code'];

            try {
                $paymentParams = [
                    'description' => $orderID,
                    'metadata' => ['orderId' => $orderId],
                    'capture_method' => 'manual',
                ];
                $paymentDetails = ($paymentId)
                    ? $this->_chargeStripePurchaseOrder($paymentId, $amount, $currencyCode, $paymentParams, $stripe_account)
                    : $this->_chargeStripeLegacyPurchaseOrder($orderId, $amount, $currencyCode, $paymentParams, $stripe_account);
                $paymentId = $paymentDetails->transactionID;
                $stripe_account = $paymentDetails->stripe_account;

                /** @see OrderLogicComponent::updateChargeForNewOrder() */
                $charge = $this->Stripe->retrieveCharge($stripe_account, $paymentId, ['expand' => ['balance_transaction']]);
                $chargeDetails = [
                    'balance_transaction_id' => $charge->balance_transaction->id ?? null,
                    'stripe_fees' => !empty($charge->balance_transaction)
                        ? $this->Stripe->retrieveStripeFees($stripe_account, $charge)
                        : $this->Stripe->estimateStripeFees($amount),
                    'risk_level' => $charge->outcome->risk_level ?? Order::RISK_LEVEL_NOT_ASSESSED,
                ];

                if (!$this->Order->save(['id' => $orderId] + $paymentDetails->toArray() + $chargeDetails)) {
                    throw new InternalErrorException(json_encode([
                        'message' => 'Failed to update charged purchase order',
                        'errors' => $this->Order->validationErrors,
                        'data' => $this->Order->data,
                    ]));
                }

                return;
            } catch (\Stripe\Exception\ApiErrorException $e) {
                CakeLog::error($e);
                if ($e instanceof \Stripe\Exception\CardException) {
                    $this->controller->setFlash($e->getMessage(), 'error');
                }

                throw new InternalErrorException('[' . get_class($e) . '] ' . json_encode($e->getJsonBody()));
            }
        } elseif ($paymentMethod === OrderPaymentMethod::CREDIT) {
            // Credit orders are charged on fulfillment But we'll check that they have the available credit for this order here.
            if (!$bypassCreditCheck && !$this->RetailerCredit->hasAvailableCredit($amount, $brandId, $retailerId)) {
                $availableCredit = $this->RetailerCredit->fetchAvailableCredit($brandId, $retailerId);
                $errorMessage = "Your total of '{$amount}' exceeds your remaining account balance of '{$availableCredit}'.";
                $this->controller->setFlash($errorMessage, 'error');
                throw new InternalErrorException(json_encode(['errors' => $errorMessage]));
            }
            return;
        } elseif ($paymentMethod === OrderPaymentMethod::EXTERNAL) {
            return;
        } else {
            throw new InternalErrorException("Unknown payment method '{$paymentMethod}' for order '{$orderID}'.");
        }
    }

    /**
     * @param string $stripePaymentId
     * @param float $amount
     * @param string $currencyCode
     * @param array $paymentParams
     * @param string $stripe_account
     * @return StripePaymentMethodDetails
     * @throws \Stripe\Exception\ApiErrorException
     */
    protected function _chargeStripePurchaseOrder(string $stripePaymentId, float $amount, string $currencyCode, array $paymentParams, string $stripe_account): StripePaymentMethodDetails
    {
        $paymentParams = array_merge($paymentParams, [
            'amount' => (int)round($amount * 100),
            'currency' => strtolower($currencyCode),
        ]);
        $confirmParams = [
            'on_error_reattach_payment_method' => true,
        ];

        $payment = $this->Stripe->confirmPaymentIfNotConfirmed($stripe_account, $stripePaymentId, $paymentParams, $confirmParams);

        return $this->Stripe->getPaymentMethodDetails($payment, $stripe_account);
    }

    /**
     * @param int $orderId
     * @param float $amount
     * @param string $currencyCode
     * @param array $paymentParams
     * @param string $stripe_account
     * @return StripePaymentMethodDetails
     * @throws \Stripe\Exception\ApiErrorException
     * @throws \Stripe\Exception\CardException
     */
    protected function _chargeStripeLegacyPurchaseOrder(int $orderId, float $amount, string $currencyCode, array $paymentParams, string $stripe_account): StripePaymentMethodDetails
    {
        $this->Order->bindModel(['belongsTo' => ['State' => ['foreignKey' => 'shipping_statecode']]], false);
        $orderAddressFields = $this->Order->get($orderId, [
            'contain' => [
                'State' => ['fields' => ['id', 'state_name']],
            ],
            'fields' => [
                'id',
                'retailer_id',
                'orderID',
                'shipping_company_name',
                'shipping_address1',
                'shipping_address2',
                'shipping_city',
                'shipping_state',
                'shipping_country',
                'shipping_zipcode',
                'shipping_telephone',
            ],
        ]);
        $this->Order->unbindModel(['belongsTo' => ['State']], false);

        $retailerId = (string)$orderAddressFields['Order']['retailer_id'];
        $orderID = (string)$orderAddressFields['Order']['orderID'];
        deprecationWarning("Charging legacy purchase order '{$orderID}' created without a PaymentIntent", 0);

        $stripeCustomerId = $this->StripeUser->getB2bCustomerId($retailerId);
        if (!$stripeCustomerId) {
            throw new InternalErrorException(json_encode([
                'message' => 'No Stripe customer found for retailer',
                'User' => $this->User->record($retailerId, ['fields' => ['id', 'email_address', 'company_name']])['User'],
            ]));
        }

        try {
            $paymentParams += [
                'payment_method_types' => ['card'],
                'shipping' => [
                    'address' => [
                        'line1' => $orderAddressFields['Order']['shipping_address1'],
                        'line2' => $orderAddressFields['Order']['shipping_address2'] ?: null,
                        'city' => $orderAddressFields['Order']['shipping_city'],
                        'state' => $orderAddressFields['State']['state_name'] ?: $orderAddressFields['Order']['shipping_state'],
                        'country' => $orderAddressFields['Order']['shipping_country'],
                        'postal_code' => $orderAddressFields['Order']['shipping_zipcode'],
                    ],
                    'name' => $orderAddressFields['Order']['shipping_company_name'],
                    'phone' => $orderAddressFields['Order']['shipping_telephone'],
                ],
                'confirm' => true,
                'error_on_requires_action' => true,
                'off_session' => true,
            ];
            $payment = $this->Stripe->createPlatformCustomerPayment($amount, $currencyCode, $stripeCustomerId, $paymentParams, ['stripe_account' => $stripe_account]);

            return $this->Stripe->getPaymentMethodDetails($payment, $stripe_account);
        } catch (\Stripe\Exception\ApiErrorException $e) {
            CakeLog::error($e);
        }

        CakeLog::warning("Failed to create PaymentIntent for a legacy purchase order '{$orderID}'. Falling back to creating a Charge.");
        $charge = $this->Stripe->chargePlatformCustomer($stripe_account, $amount, $currencyCode, $stripeCustomerId, [
            'description' => $paymentParams['description'] ?? $orderID,
            'metadata' => ['orderId' => $orderId],
            'capture' => (($paymentParams['capture_method'] ?? null) !== 'manual'),
        ]);

        return $this->Stripe->getPaymentMethodDetailsFromCharge($charge, $stripe_account);
    }

    /**
     * @param int $retailerId
     * @param array $order
     * @param array $log
     * @param array $shipping
     * @param array $billing
     * @param string $shippingMethod
     * @return void
     */
    public function createPosOrder($retailerId, $order, $log, $shipping, $billing, $shippingMethod): void
    {
        if (isset($order['Order'])) {
            $order = $order['Order'];
        }

        $retailer = $this->User->findForPosApi($retailerId)['User'];
        if (!$retailer['enable_creating_pos_orders']) {
            return;
        }
        $hasBlankUpcs = false;
        foreach ($log['product'] as $product) {
            if(empty($product['upc']) && !($product['isFeeProduct'] ?? false)){
                $hasBlankUpcs = true;
                break;
            }
        }
        $hasActiveInventory = $retailer['inventory_type'] === 'other' || (
            $retailer['inventory_password'] &&
            ($retailer['inventory_apiuser'] || $retailer['inventory_type'] === 'shopify_pos') &&
            ($retailer['vend_refresh_access_token'] || !in_array($retailer['inventory_type'], ['lightspeed_cloud', 'vend_pos'], true))
        );
        if ($log['velofix'] || $order['is_commission_retailer'] || $hasBlankUpcs || !$hasActiveInventory) {
            return;
        }
        if (
            // Lightspeed is the only (so far) POS integration to handle ship to store orders or orders with ancillary fees.
            $retailer['inventory_type'] !== 'lightspeed_cloud'
            && (
                $order['subType'] === OrderType::SUB_TYPE_NONSTOCK
                || $this->ProductStateFee->exists(['ProductStateFee.fee_product_id' => array_filter(array_column($log['product'], 'id'))])
            )
        ) {
            return;
        }


        $log['taxamt'] -= $log['shippingTax'];

        $shipTo = ($shippingMethod == 'shipfromstore_shipfromstore');

        try {
            if ($retailer['inventory_type'] == 'lightspeed_cloud') {
                $this->Lightspeed->createSale($retailer['inventory_apiuser'], $retailer['inventory_password'], $retailer['Inventory_Reg_ID'], $retailer['Inventory_Emp_ID'], $retailer['Inventory_Store_ID'], $log['product'], (object)$billing, $retailer['id'], $order['currency_code'], $order['total_price'] - $order['total_discount'], $order['shipping_amount'], $shipTo, (object)$shipping, $log['shippingTax'], $log['taxamtdiscount'], $order['orderID'], $retailer['vend_refresh_access_token'], $retailer['vend_access_token_expires']);
            } elseif ($retailer['inventory_type'] == 'shopify_pos') {
                $this->ShopifyPOS->createShopifyPosOrder($retailer['inventory_apiuser'], $retailer['inventory_password'], $retailer['shop_url'], $log['product'], (object)$billing, $retailer['id'], $order['currency_code'], $order['total_price'], $order['shipping_amount'], $shipTo, (object)$shipping, $log['shippingTax'], $log['taxamt'], $log['tax_included'], $order['orderID']);
            } elseif ($retailer['inventory_type'] == 'quickbook_pos') {
                $this->Quickbook->createQuickbookOrder($order['id'], $retailer['inventory_apiuser'], json_encode($log['product']), $retailer['id']);
            } elseif ($retailer['inventory_type'] == 'vend_pos') {
                $this->VendPOS->makeLayaway($retailer['inventory_apiuser'], $retailer['inventory_password'], $retailer['Inventory_Reg_ID'], $retailer['Inventory_Emp_ID'], $retailer['Inventory_Store_ID'], $log['product'], (object)$billing, $retailer['id'], $order['currency_code'], $order['total_price'], $order['shipping_amount'], $shipTo, (object)$shipping, $log['shippingTax'], $log['taxamt'], $order['orderID']);
            } elseif ($retailer['inventory_type'] == 'square') {
                $this->SquarePos->createCustomerFromEcommerce($retailer['inventory_password'], (object)$shipping, $order);
            }
        } catch (Exception $e) {
            CakeLog::error(strval($e));
        }
    }

    /**
     * @param int $orderId
     * @param bool $queueOnFailure
     * @return bool
     * @throws Exception
     */
    public function createEcommerceConsumerOrder(int $orderId, bool $queueOnFailure = true): bool
    {
        $order = $this->Order->findForEcommerceConsumerOrder($orderId);
        if (empty($order['Order']['id'])) {
            throw new NotFoundException('Order not found for id=' . json_encode($orderId));
        }
        if (
            OrderType::filterOrderType($order['Order']['order_type']) !== OrderType::SELL_DIRECT &&
            !$order['Order']['is_commission_retailer']
        ) {
            throw new BadRequestException('Invalid order type: ' . json_encode($order['Order']['order_type']));
        }

        $userId = (int)$order['Order']['user_id'];
        $retailer = $this->User->findEcommerceConsumerOrderRetailer($userId, $order['Order']['retailer_id']);

        $userLog = mask_secret_fields($order['User'], ['api_key', 'secret_key']);

        switch ($order['User']['site_type']) {
            case UserSiteType::SHIPEARLY:
                // Do nothing
                return true;
            case UserSiteType::SHOPIFY:
                $api_order = [];
                try {
                    $api_order = $this->Shopify->createOrderFromConsumerOrder($order, $retailer);
                } catch (ShopifyApiException $e) {
                    if ($queueOnFailure) {
                        CakeLog::warning($e);
                        $this->Btask->queueCreateEcommerceConsumerOrder($orderId);
                    } else {
                        CakeLog::error($e);
                    }
                }
                if (!$api_order) {
                    return false;
                }

                $save = [
                    'id' => $orderId,
                    'source_id' => $api_order['id'],
                    'orderNO' => $api_order['order_number'],
                    'source_order_name' => $api_order['name'],
                ];
                if (
                    isset($api_order['customer']['email']) &&
                    $this->Customer->syncShopifyCustomer($userId, $api_order['customer'])
                ) {
                    $save += ['customerID' => $this->Customer->id];
                }
                $success = (bool)$this->Order->save($save);

                if ($success) {
                    //FIXME Remove this step after resolving differences from the initial order record
                    $this->ShopifyWebhookHandler->syncOrder($userId, $api_order);
                }

                return $success;
            case UserSiteType::WOOCOMMERCE:
            case UserSiteType::MAGENTO:
                triggerWarning(json_encode(['message' => 'Unsupported site_type', 'User' => $userLog]));

                return false;
            default:
                throw new BadRequestException(json_encode(['message' => 'Unknown site_type', 'User' => $userLog]));
        }
    }

    /**
     * @param int $dealerOrderId
     * @return bool
     * @throws Exception
     */
    public function createEcommerceDealerOrder($dealerOrderId): bool
    {
        //TODO: this needs to push discount code up to shopify.
        // need to consider the code discount as well as additional discount given at checkoiut by brand
        // possibly send those as different discount codes
        // currently whole discount shows up as CUSTOM
        if (!$this->DealerOrder->exists(['DealerOrder.id' => $dealerOrderId])) {
            throw new NotFoundException('Dealer order not found where ' . json_encode(['id' => $dealerOrderId]));
        }

        $dealerOrder = $this->DealerOrder->findForEcommerceDealerOrder($dealerOrderId);

        if ($dealerOrder['DealerOrder']['source_id']) {
            CakeLog::warning("The dealer order is already linked to /orders/{$dealerOrder['DealerOrder']['source_id']}");

            return true;
        }

        $dealerOrder['DealerOrderProduct'] = $this->WarehouseProduct->addWarehouseProductsToLineItems((array)$dealerOrder['DealerOrderProduct'], (int)$dealerOrder['Order']['id']);
        $retailer = $this->User->findEcommerceDealerOrderRetailer($dealerOrder);
        $shippingName = $dealerOrder['DealerOrder']['shipping_name'];

        $userLog = mask_secret_fields($dealerOrder['User'], ['api_key', 'secret_key']);

        switch ($dealerOrder['User']['site_type']) {
            case UserSiteType::SHIPEARLY:
                // Do nothing
                return true;
            case UserSiteType::SHOPIFY:
                $response = $this->Shopify->createOrderFromDealerOrder($dealerOrder, $retailer, $shippingName);
                if (!$response) {
                    return false;
                }

                return (bool)$this->DealerOrder->save([
                    'id' => $dealerOrderId,
                    'source_id' => $response['id'],
                    'orderNO' => $response['order_number'],
                    'source_order_name' => $response['name'],
                ]);
            case UserSiteType::WOOCOMMERCE:
                $response = $this->Woocommerce->createOrderFromDealerOrder($dealerOrder, $retailer, $shippingName);
                if (!$response) {
                    return false;
                }

                return (bool)$this->DealerOrder->save([
                    'id' => $dealerOrderId,
                    'source_id' => $response->id,
                    'orderNO' => $response->number,
                ]);
            case UserSiteType::MAGENTO:
                triggerWarning(json_encode(['message' => 'Unsupported site_type', 'User' => $userLog]));

                return false;
            default:
                throw new BadRequestException(json_encode(['message' => 'Unknown site_type', 'User' => $userLog]));
        }
    }

    public function captureConsumerOrderPayment($consumerOrderId): bool
    {
        $consumerOrder = $this->Order->findForEcommerceConsumerOrder($consumerOrderId);

        $userLog = mask_secret_fields($consumerOrder['User'], ['api_key', 'secret_key']);

        switch ($consumerOrder['User']['site_type']) {
            case UserSiteType::SHIPEARLY:
                // Do nothing
                return true;
            case UserSiteType::SHOPIFY:
                return (bool)$this->Shopify->captureEcommercePaymentApi($consumerOrder);
            case UserSiteType::WOOCOMMERCE:
                return (bool)$this->Woocommerce->captureEcommercePaymentApi($consumerOrder);
            case UserSiteType::MAGENTO:
                triggerWarning(json_encode(['message' => 'Unsupported site_type', 'User' => $userLog]));

                return false;
            default:
                throw new BadRequestException(json_encode(['message' => 'Unknown site_type', 'User' => $userLog]));
        }
    }

    public function buildCreateEcommerceDealerOrderRetryLink($dealerOrderId): string
    {
        App::uses('AppView', 'View');
        $view = new AppView($this->controller);

        $view->set('dealerOrderId', $dealerOrderId);

        return (string)$view->render('/Elements/Orders/create_ecommerce_dealer_order_retry_link', false);
    }
}
