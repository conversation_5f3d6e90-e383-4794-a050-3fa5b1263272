<?php
App::uses('Component', 'Controller');

/**
 * Class ShippoComponent
 */
class ShippoComponent extends Component
{
    public function startup(Controller $controller)
    {
        parent::startup($controller);
        Shippo::setApiKey(SHIPPO_PRIVATE_AUTH_TOKEN);
        Shippo::setApiVersion(SHIPPO_API_VERSION);
    }

    /**
     * List all carrier accounts.
     *
     * @param array $params Pagination: 'results', 'page'. Filters: 'carrier', 'account_id'.
     * @return Shippo_CarrierAccount[]|null An array of account objects with ArrayAccess or Null on failure.
     * @link https://goshippo.com/docs/reference/php#carrier-accounts-list
     */
    public function listCarrierAccounts($params = array())
    {
        try {
            $params += array('results' => 30);
            $response = Shippo_CarrierAccount::all($params);
            return array_values($response['results']);
        } catch (Exception $e) {
            CakeLog::error(strval($e));
            return null;
        }
    }

    /**
     * Search carrier accounts for an existing account either matching the given id or provided by Shippo.
     *
     * @param string $account_id
     * @param array $params Args for ShippoComponent::listCarrierAccounts
     * @return Shippo_CarrierAccount|null
     * @see ShippoComponent::listCarrierAccounts
     */
    public function findCarrierAccountByAccountId($account_id, $params = array())
    {
        $shippoAccounts = array_filter(
            $this->listCarrierAccounts($params),
            function($account) use ($account_id) {
                return ($account['account_id'] === $account_id || $account['is_shippo_account']);
            }
        );

        foreach ($shippoAccounts as $account) {
            if ($account['account_id'] === $account_id) {
                return $account;
            }
        }

        foreach ($shippoAccounts as $account) {
            if ($account['is_shippo_account']) {
                return $account;
            }
        }

        return null;
    }

    /**
     * Create a new carrier account.
     *
     * @param string $carrier_id Token representing a Shippo carrier.
     * @param string[] $carrier_parameters Argument values that vary based on the carrier.
     * @return Shippo_CarrierAccount|null
     * @see https://goshippo.com/docs/reference/php#carrier-accounts-create
     * @see https://goshippo.com/docs/reference/php#carriers
     */
    public function createCarrierAccount($carrier_id, $carrier_parameters)
    {
        try {
            $account = $this->_getCarrierAccountParams($carrier_id, $carrier_parameters);

            $response = Shippo_CarrierAccount::create($account);
            CakeLog::debug(json_encode(['carrier_account' => $response->__toArray(true)]));

            return $response;
        } catch (Exception $e) {
            CakeLog::error(strval($e));
            return null;
        }
    }

    /**
     * Update an existing carrier account.
     *
     * @param string $object_id
     * @param string $carrier_id
     * @param string[] $carrier_parameters
     * @return Shippo_CarrierAccount|null
     * @see https://goshippo.com/docs/reference/php#carrier-accounts-update
     * @see https://goshippo.com/docs/reference/php#carriers
     */
    public function updateCarrierAccount($object_id, $carrier_id, $carrier_parameters)
    {
        try {
            $account = $this->_getCarrierAccountParams($carrier_id, $carrier_parameters);

            $response = Shippo_CarrierAccount::update($object_id, $account);
            CakeLog::debug(json_encode(['carrier_account' => $response->__toArray(true)]));

            return $response;
        } catch (Exception $e) {
            CakeLog::error(strval($e));
            return null;
        }
    }

    /**
     * Create a new shipment between addresses and returns its valid carrier rates.
     *
     * @param string[] $address_from
     * @param string[] $address_to
     * @param string[] $parcel
     * @param string[] $carrier_accounts
     * @return array
     * @throws Shippo_Error
     * @see https://goshippo.com/docs/reference/php#shipments-create
     */
    public function getCarrierRates(array $address_from, array $address_to, array $parcel, array $carrier_accounts): array
    {
        $params = [
            'address_from' => $address_from,
            'address_to' => $address_to,
            'parcels' => [
                $parcel,
            ],
            'carrier_accounts' => $carrier_accounts,
            'async' => false,
        ];
        CakeLog::debug(json_encode(compact('params')));

        $shipment = Shippo_Shipment::create($params)->__toArray(true);
        CakeLog::debug(json_encode(compact('shipment')));

        if (empty($shipment['rates']) && !empty($shipment['messages'])) {
            $jsonBody = (array)$shipment['messages'];
            $httpBody = (string)json_encode($jsonBody);

            $messages = array_filter(array_map(function($message) {
                $source = $message['source'] ?? '';
                $text = $message['text'] ?? '';

                return trim($source . ' ' . $text);
            }, $jsonBody));
            $message = implode(', ', $messages);

            throw new Shippo_Error($message ?: $httpBody, 400, $httpBody, $jsonBody);
        }

        return (array)$shipment['rates'];
    }

    /**
     * Create the request for saving a carrier account.
     *
     * Assigns carrier parameters to their fields.
     *
     * @param string $carrier_id Token representing a Shippo carrier.
     * @param string[] $parameters Argument values that vary based on the carrier.
     * @return array
     * @throws Exception $carrier_id is not recognized.
     * @see https://goshippo.com/docs/reference/php#carriers
     * @see https://goshippo.com/docs/carriers
     */
    protected function _getCarrierAccountParams($carrier_id, $parameters)
    {
        $account = array(
            'carrier' => $carrier_id,
            'account_id' => $parameters['parameter1'],
            'parameters' => array(),
            'test' => false,
            'active' => true,
        );

        switch ($carrier_id) {
        case 'fedex':
            $account['parameters'] = array(
                'meter' => $parameters['parameter2'],
                'smartpost_id' => $parameters['parameter3'],
            );
            break;
        case 'dhl_express':
            $account['parameters'] = array();
            break;
        case 'dhl_ecommerce' :
            $account['parameters'] = array(
                'username' => $parameters['parameter2'],
                'password' => $parameters['parameter3']
            );
            break;
        case 'ups':
            $account['parameters'] = array(
                'account_number' => $parameters['parameter2'],
                'password' => $parameters['parameter3'],
            );
            break;
        case 'purolator':
            $account['parameters'] = array(
                'production_key' => $parameters['parameter2'],
                'production_key_password' => $parameters['parameter3'],
            );
            break;
        case 'usps':
            $account['parameters'] = array();
            break;
        default:
            throw new Exception('Unknown carrier id ' . json_encode(compact('carrier_id', 'parameters')));
        }

        CakeLog::debug(json_encode($account));

        return $account;
    }

}
