<?php

use ShipEarlyApp\Lib\Utility\UserSiteType;

App::uses('AppComponent', 'Controller/Component');
App::uses('BrandSetupConditions', 'Controller/Component/UserLogic');
App::uses('BranchSetupConditions', 'Controller/Component/UserLogic');
App::uses('RetailerSetupConditions', 'Controller/Component/UserLogic');
App::uses('User', 'Model');

/**
 * Class UserLogicComponent.
 *
 * @property UploadComponent $Upload
 *
 * @property Btask $Btask
 * @property Contactpersons $Contactpersons
 * @property EmailTemplate $EmailTemplate
 * @property ManufacturerRetailer $ManufacturerRetailer
 * @property Product $Product
 * @property ProductCategories $ProductCategories
 * @property ProductRetailer $ProductRetailer
 * @property StripeUser $StripeUser
 * @property User $User
 * @property UserCategories $UserCategories
 *
 * @property BranchsController|BrandStaffController|DashboardsController|PagesController|RetailersController|SalesRepsController|StaffController|UsersController $controller
 */
class UserLogicComponent extends AppComponent
{
    public $components = [
        'Upload'
    ];

    public $uses = [
        'Btask',
        'Contactpersons',
        'EmailTemplate',
        'ManufacturerRetailer',
        'Product',
        'ProductCategories',
        'ProductRetailer',
        'StripeUser',
        'User',
        'UserCategories',
    ];

    /**
     * Update user area of interest (add and remove categories)
     *
     * @param string[] $existingCat
     * @param string[] $postCat
     * @param int $userId
     * @param string $userType
     * @param int $Branch
     * @param bool $updateSession
     */
    public function UpdateUserCat($existingCat, $postCat, $userId, $userType, $Branch, $updateSession)
    {
        $userIds = [(int)$userId];
        if ($userType === User::TYPE_RETAILER && !$Branch) {
            $userIds = array_merge($userIds, $this->User->listBranchIds($userId));
        }

        $unset = array_diff($existingCat, $postCat);
        if ($unset) {
            $this->UserCategories->deleteAllCat($unset, $userIds);

            if ($userType === User::TYPE_MANUFACTURER) {
                $products = $this->ProductCategories->getUserProducts($userId, $unset);
                if ($products) {
                    $this->Product->setIncomplete($products);
                    $this->ProductCategories->unsetCatProducts($products);

                    if ($updateSession) {
                        $this->controller->setFlash('Your existing products in unselected categories are moved to Incomplete status.', 'error');
                    }
                }
            }
        }

        $catCount = $this->UserCategories->getUserCatCount($userId);
        $newCat = array_diff($postCat, $existingCat);
        if ($newCat && ($catCount + count($newCat) <= 3 || $userType === User::TYPE_RETAILER)) {
            $this->UserCategories->createCat($userIds, $userType, $newCat);
        }

        $this->ProductRetailer->deleteRetailerAssoc($userId);

        if ($userType === User::TYPE_RETAILER && !$Branch) {
            $brandIds = $this->ManufacturerRetailer->find('list', [
                'recursive' => -1,
                'conditions' => ['retailer_id' => $userId],
                'fields' => ['user_id', 'user_id'],
            ]);
            foreach ($brandIds as $brandId) {
                $this->productAssociation($brandId, $userId);
            }
        }

        if ($updateSession) {
            $this->controller->setFlash(__('Your categories of interest has been updated successfully'), 'success');
        }
    }

    /**
     * Product association with brand associated retailers.
     *
     * @param int $brandId
     * @param int $retailerId
     * @return bool
     */
    public function productAssociation($brandId, $retailerId)
    {
        $brandId = (int)$brandId;
        $retailerId = (int)$retailerId;
        $categoryIds = $this->UserCategories->findMatchingCategoryIds($brandId, $retailerId);
        $productIds = $this->ProductCategories->getBrandProductIds($brandId, $categoryIds);
        if ($this->ProductRetailer->saveRetailerAssoc($retailerId, $productIds)) {
            $this->Btask->queueStoreUpdate($retailerId);
        }

        return true;
    }

    /**
     * Store registration notification email to super admin
     * @param $data
     * @param $user
     * @return bool
     */
    public function newStoreNotification($data, $user)
    {
        //Store Registration email
        $emailTemplateArr = $this->EmailTemplate->getEmailTemplate('Store Registration');
        $variables = [];
        $variables['{user_name}'] = $data['User']['company_name'];
        $variables['{user_type}'] = $data['User']['user_type'];
        $variables['{site_name}'] = SITE_NAME;
        $variables['{Retailer}'] = '<a href="' . BASE_PATH . '/contact/' . $user['id'] . '">' . $user['company_name'] . '</a>';
        $this->controller->sendEmail($emailTemplateArr, $variables, ADMIN_EMAIL);

        return true;
    }

    /**
     * Send main retailer notification email to super admin
     * @param $data
     * @return bool
     */
    public function newRetailerNotification($data)
    {
        //Registration email
        $emailTemplateArr = $this->EmailTemplate->getEmailTemplate('Registration');
        $variables = [];
        $variables['{user_name}'] = $data['User']['company_name'];
        $variables['{user_type}'] = $data['User']['user_type'];
        $variables['{site_name}'] = SITE_NAME;
        $this->controller->sendEmail($emailTemplateArr, $variables, ADMIN_EMAIL);

        return true;
    }

    /**
     * Send an activation email to an approved user.
     *
     * Branches and SalesReps receive a temporary password.
     *
     * @param int $userId
     * @return bool
     */
    public function activationMail($userId)
    {
        $user = $this->controller->User->findById($userId, [
            'id',
            'uuid',
            'user_type',
            'Branch',
            'email_address',
            'company_name',
            'store_associate_pin',
            'language_code',
        ], null, -1);
        $user = $user['User'];

        $tempPassword = '';
        if (in_array($user['user_type'], [User::TYPE_SALES_REP, User::TYPE_BRAND_STAFF], true) || $user['Branch']) {
            $tempPassword = $this->getRandomPass();
            $this->controller->User->save(['id' => $userId, 'password' => User::passwordHasher()->hash($tempPassword)]);
        }

        $activationUrl = BASE_PATH . 'activate/' . $user['uuid'];
        if ($user['language_code']) {
            Configure::write('Config.language', $user['language_code']);
        }
        $activationUrlPlaceholder = __('Activate Account');
        $activateButtonHtml = <<<HTML
<div style="text-align: center;">
    <a href="$activationUrl" style="display: inline-block; background-color: #428bca; color: #fff; text-decoration: none; padding: 7px 10px; border-radius: 10px;">$activationUrlPlaceholder</a>
</div>
HTML;
        $variables = [
            '{user_name}' => $user['company_name'],
            '{activation_link}' => $activationUrl,
            '{activation_button}' => $activateButtonHtml,
            '{site_name}' => SITE_NAME,
            '{username}' => $user['email_address'],
            '{password}' => $tempPassword,
        ];

        if ($user['user_type'] === User::TYPE_STAFF) {
            $emailTemplateArr = $this->EmailTemplate->getEmailTemplate('Activate StoreAssociate', null, $user['language_code']);

            $variables['{pin}'] = $user['store_associate_pin'];
        } elseif (in_array($user['user_type'], [User::TYPE_SALES_REP, User::TYPE_BRAND_STAFF], true)) {
            $emailTemplateArr = $this->EmailTemplate->getEmailTemplate('Activate Sales Rep', null, $user['language_code']);

            /** @var Contactpersons $Contactpersons */
            $Contactpersons = ClassRegistry::init('Contactpersons');
            $variables['{user_name}'] = $Contactpersons->field('CONCAT(`firstname`, " ", `lastname`)', ['user_id' => $userId]);
        } elseif ($user['user_type'] === User::TYPE_RETAILER && $user['Branch'] != 0) {
            $emailTemplateArr = $this->EmailTemplate->getEmailTemplate('Activate Store', null, $user['language_code']);

            $variables['{retailer_name}'] = $this->makeContactLink($user['Branch'], true);
        } else {
            $emailTemplateArr = $this->EmailTemplate->getEmailTemplate('Activate Account', null, $user['language_code']);
        }

        // Legacy template variables
        if (isset($variables['{username}'])) {
            $variables['{userName}'] = $variables['{username}'];
        }
        if (isset($variables['{password}'])) {
            $variables['{passWord}'] = $variables['{password}'];
        }

        return $this->controller->sendEmail($emailTemplateArr, $variables, trim($user['email_address']));
    }

    /**
     * Sub function to generate contact link
     * @param $user
     * @param bool $fetch
     * @return string
     */
    public function makeContactLink($user, $fetch = false)
    {
        if ($fetch) {
            $user = $this->controller->User->record($user, ['fields' => ['id', 'company_name']]);
        }
        $link = "<a href='" . BASE_PATH . 'contact/' . $user['User']['id'] . "' title='" . $user['User']['company_name'] . "'>" . $user['User']['company_name'] . '</a>';

        return $link;
    }

    /**
     * Generate random string for instore secret code validation
     * @return string
     */
    public function getRandomPass()
    {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $randomString = '';
        for ($i = 0; $i < 6; $i++) {
            $randomString .= $characters[rand(0, strlen($characters) - 1)];
        }

        return $randomString;
    }

    /**
     * Upload company logo on my account page
     *
     * @param array $data
     * @return array Updated $data
     */
    public function uploadAvatar(array $data)
    {
        if (isset($data['User']['avatar'])) {
            $avatarBasePath = $this->Upload->basePath . 'files/users/';

            $oldAvatar = AuthComponent::user('avatar');
            $oldFileUrl = !empty($oldAvatar)
                ? $avatarBasePath . $oldAvatar
                : '';

            $newFileUrl = $this->replaceUserImage(
                $oldFileUrl,
                (array)$data['User']['avatar'],
                (string)AuthComponent::user('uuid'),
                'avatar',
                ['type' => 'resizemin', 'size' => [1, 80]]
            );

            if ($newFileUrl) {
                $data['User']['avatar'] = str_replace($avatarBasePath, '', $newFileUrl);
            } else {
                unset($data['User']['avatar']);
            }
        }

        return $data;
    }

    /**
     * Upload a user image to webroot/files/users/ and remove an existing one.
     * Returns the new image URL which will be different from the original.
     *
     * @param string|null $oldFileUrl Absolute URL of the image to be replaced
     * @param array $newFileData request data from file input form element
     * @param string $uuid
     * @param string $path
     * @param array|null $uploadRules optional parameters for modifying the image
     * @return string|null Absolute URL of the uploaded file on success, null on failure
     */
    public function replaceUserImage(?string $oldFileUrl, array $newFileData, string $uuid, string $path, ?array $uploadRules = null): ?string
    {
        if ($newFileData['error'] === UPLOAD_ERR_NO_FILE) {
            return null;
        }

        try {
            return $this->Upload->replaceFileInUserHash(
                $oldFileUrl,
                $newFileData,
                $uuid,
                $path,
                $newFileData['name'],
                $uploadRules,
                ['jpg', 'jpeg', 'gif', 'png']
            );
        } catch (Exception $e) {
            CakeLog::error($e);

            return null;
        }
    }

    /**
     * Get the geo location based on given address
     *
     * @param array $data
     * @return array Updated $data
     */
    public function updateGeoLocation(array $data)
    {
        $geopoints = $this->controller->_getLnt(
            trim($data['User']['address']),
            trim($data['User']['city']),
            trim($data['User']['zipcode']),
            $data['User']['state_id'],
            $data['User']['country_id']
        );
        $data['User']['latitude'] = $geopoints['lat'];
        $data['User']['longitude'] = $geopoints['lng'];

        return $data;
    }

    /**
     * Send Mail to the Brand/Retailer on registration
     * @param $data
     */
    public function _sendRegistrationMail($data, $brand = null)
    {
        //Registration email
        $emailTemplateArr = $this->EmailTemplate->getEmailTemplate('Registration Mail', $brand['User']['id'] ?? null, $data['User']['language_code']);

        if (!empty($brand['User']['id'])) {
            $emailTemplateArr['EmailTemplate']['fromEmail'] = "{$brand['User']['company_name']}<{$brand['User']['email_address']}>";
            $emailTemplateArr['EmailTemplate']['logo'] = $brand['User']['avatar'];
        }

        $variables = [];
        $variables['{user_name}'] = $data['User']['company_name'];
        $variables['{user_type}'] = $data['User']['user_type'];
        $variables['{site_name}'] = $brand['User']['company_name'] ?? SITE_NAME;
        $this->controller->sendEmail($emailTemplateArr, $variables, $data['User']['email_address']);

        return true;
    }

    public function findProfileSetupConditions($userId): ProfileSetupConditions
    {
        $user = $this->User->findById($userId, [
            'id',
            'user_type',
            'Branch',
            'status',
            'email_address',
            'company_name',
            'defaultTax',
            'site_type',
            'shop_url',
            'instore',
            'shipment',
            'store_timing',
            'inventory_type',
        ], null, -1);
        if (empty($user['User']['id'])) {
            throw new NotFoundException(json_encode(['message' => 'User not found', 'user_id' => $userId]));
        }

        $userType = $user['User']['user_type'];

        if ($userType === User::TYPE_MANUFACTURER) {
            $setupConditions = new BrandSetupConditions();
        } elseif ($userType === User::TYPE_RETAILER) {
            $setupConditions = ($user['User']['Branch']) ? new BranchSetupConditions() : new RetailerSetupConditions();
        } else {
            throw new BadRequestException(json_encode([
                'message' => 'Invalid user type',
                'User' => array_intersect_key($user['User'], array_flip(['id', 'user_type', 'Branch', 'status', 'email_address', 'company_name'])),
            ]));
        }

        $stripeUser = $this->StripeUser->getStripeUserFields($userId, [
            'id',
            'stripe_user_id',
            'stripe_cus_id',
            'charges_enabled',
        ]);

        return $setupConditions
            ->setContactPerson($this->Contactpersons->exists(['Contactpersons.user_id' => $userId]))
            ->setAreaOfInterest($this->UserCategories->exists(['UserCategories.user_id' => $userId]))
            ->setConfiguration(
                ($userType === User::TYPE_RETAILER)
                    ? ($user['User']['inventory_type'] && $user['User']['defaultTax'] !== null)
                    : ($user['User']['site_type'] && ($user['User']['shop_url'] || $user['User']['site_type'] === UserSiteType::SHIPEARLY))
            )
            ->setBankConnect(
                !empty($stripeUser['StripeUser']['stripe_user_id']) &&
                $stripeUser['StripeUser']['charges_enabled']
            )
            ->setSubscriptionPlan(
                !empty($stripeUser['StripeUser']['stripe_cus_id']) &&
                $user['User']['status'] !== 'Suspend'
            )
            ->setShipmentSetting($user['User']['instore'] || $user['User']['shipment'])
            ->setStoreTiming((bool)$user['User']['store_timing'])
            ->setBrandAssociation($this->ManufacturerRetailer->hasBrandAssociation((int)$userId));
    }

    public function promptToConnectBankIfNotConnected($userId)
    {
        if ($this->_showConnectBank($userId)){
            $button = '<a href="' . Router::url(['controller' => 'users', 'action' => 'payouts']) . '" class="btn btn-primary btn-xs" style="margin-top: -2px; margin-bottom: -1px;">' . __('Complete Now') . '</a>';
            $message = __('You are not eligible to receive sales or payouts until your Stripe account is activated.') . ' ' . $button;
            $this->controller->setFlash($message, 'info');
        }
    }

    private function _showConnectBank(int $userId) : bool
    {
        return !$this->StripeUser->hasConnectedUser($userId) && $this->User->showOnlineToOfflineOptions($userId);
    }
}
