<?php

/**
 * Class VelofixComponent
 *
 * @property AppController $controller
 * @property CurlComponent $Curl
 */
class VelofixComponent extends Component
{
    /**
     * Other Components this component uses.
     *
     * @var array
     */
    public $components = ['Shopify.Curl'];

    /**
     * @var string
     */
    public $apiUrl = 'https://velofixdirect-staging.herokuapp.com/api';
    // Alt endpoint
    //public $apiUrl = 'https://direct.velofix.com/api';

    public function initialize(Controller $controller)
    {
        $this->controller = $controller;
    }

    /**
     * List all franchises.
     *
     * @return array
     * @throws CurlException
     * @throws HttpException
     */
    public function getTerritories()
    {
        return $this->getTerritory('');
    }

    /**
     * Retrieve the covered terroritory of a specific franchise.
     *
     * @param string $id
     * @return array
     * @throws CurlException
     * @throws HttpException
     */
    public function getTerritory($id)
    {
        return $this->_sendHttpRequest('GET', '/territories/' . $id);
    }

    /**
     * Verifies coverage of a specific postal or zip code.
     *
     * @param string $zipcode either a zip code (90210) or postal code prefix (A1A)
     * @return array
     * @throws CurlException
     * @throws HttpException
     */
    public function getCoverageFromCode(string $zipcode): array
    {
        $coverage = $this->_sendHttpRequest('GET', '/coverage/' . $zipcode);

        return $this->_processCoverage($coverage);
    }

    /**
     * @param array $coverage
     * @return array
     */
    protected function _processCoverage(array $coverage): array
    {
        if (isset($coverage['territory']['contact']['address'])) {
            $address = $coverage['territory']['contact']['address'];
            // Sometimes the city field is given as "{city}, {state}"
            $address['city'] = preg_replace("/, {$address['state']}$/", '', $address['city']);
            $coverage['territory']['contact']['address'] = $address;
        }

        return $coverage;
    }

    /**
     * @param array $address
     * @return array
     * @throws CurlException
     * @throws HttpException
     */
    public function getCoverage(array $address): array
    {
        $zipcode = (string)$address['PostalCode'];
        if (strtolower($address['countryName']) == 'ca') {
            $zipcode = substr($zipcode, 0, 3);
        }

        return $this->getCoverageFromCode($zipcode);
    }

    /**
     * @param array $address
     * @return array
     * @throws CurlException
     * @throws HttpException
     */
    public function getCoveringFranchise(array $address): array
    {
        $coverage = $this->getCoverage($address);
        if ($coverage['coverage'] !== false && $coverage['active'] === true && isset($coverage['territory'])) {
            return (array)$coverage['territory'];
        }

        return [];
    }

    public function isAddressCovered(array $address): bool
    {
        try {
            return (bool)$this->getCoveringFranchise($address);
        } catch (Exception $e) {
            CakeLog::warning($e);

            return false;
        }
    }

    /**
     * @param string $method HTTP Method ('GET', 'POST', 'PUT', 'DELETE').
     * @param string $path Shopify API path after the store's domain.
     * @param array $params POST or PUT data being sent. Ignored for other HTTP methods.
     * @return array Response body
     * @throws CurlException
     * @throws HttpException
     */
    protected function _sendHttpRequest(string $method, string $path, array $params = []): array
    {
        $url = rtrim($this->apiUrl, '/') . '/' . ltrim($path, '/');

        $method = strtoupper($method);
        if (in_array($method, ['POST', 'PUT'])) {
            $query = [];
            $payload = json_encode($params);
            $request_headers = ['Content-Type: application/json; charset=utf-8'];
        } else {
            $query = $params;
            $payload = [];
            $request_headers = [];
        }

        list($response_body, $response_headers) = $this->Curl->HttpRequest($method, $url, $query, $payload, $request_headers);

        $httpStatusCode = (int)$response_headers['http_status_code'];
        if ($httpStatusCode < 200 || $httpStatusCode >= 300) {
            throw new HttpException((string)($response_headers['http_status_message'] ?? 'Unknown Error'), $httpStatusCode);
        }

        return (array)json_decode($response_body, true);
    }
}
