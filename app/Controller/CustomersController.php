<?php

use ShipEarlyApp\Lib\Utility\SupportedLanguages;

App::uses('App<PERSON>ontroller', 'Controller');

/**
 * Class CustomersController
 *
 * @property PhpExcelComponent $PhpExcel
 * 
 * @property TranslateComponent $Translate
 *
 * @property Customer $Customer
 */
class CustomersController extends AppController
{
    /**
     * @var string
     */
    public $name = 'Customers';
    /**
     * @var array
     */
    public $components = array('PhpExcel', 'Translate');
    /**
     * @var array
     */
    public $uses = array('Customer');

    /**
     * List all the customers with the sale count and total sale amount associated with current brand
     * @url: /customers
     */
    public function index()
    {
        $this->set('title_for_layout', 'Customers');

        $noRecords = 50;
        if (!empty($this->request->data['Customer']['noRecords'])) {
            $noRecords = $this->request->data['Customer']['noRecords'];
        }
        $this->set('noRecords', $noRecords);

        $page = 1;
        if (!empty($this->request->params['pageNo'])) {
            $page = $this->request->params['pageNo'];
        }

        /* Text Search */
        if (isset($this->request->query['customerSearch']) && !empty($this->request->query['customerSearch'])) {
            $this->request->data['customerSearch'] = $this->request->query['customerSearch'];
        }

        $customerSearch = '';
        if (isset($this->request->data['customerSearch']) && !empty($this->request->data['customerSearch'])) {
            $customerSearch = $this->request->data['customerSearch'];
        }

        $this->_CustomerLogic($this->Auth->user('id'), $noRecords, $page, $customerSearch);
    }

    /**
     * Return the table structure through ajax Response
     */
    public function ajax_index()
    {
        $this->layout = '';
        if ($this->request->is('ajax')) {
            $userId = $this->Auth->user('id');
            $noRecords = $this->request->data['noRecords'];
            $pageNumber = $this->request->data['pageNumber'];
            $customerSearch = $this->request->data['customerSearch'];
            $countCustomers = $this->request->data['countCustomers'];
            $sortField = $this->request->data['sortField'];
            $sortOrder = $this->request->data['sortOrder'];
            $order = $sortField . ' ' . $sortOrder;

            $this->_CustomerLogic($userId, $noRecords, $pageNumber, $customerSearch, $countCustomers, $order);
        }
    }

    public function export($customerSearch = '')
    {
        $this->autoRender = false;

        $userId = $this->Auth->user('id');
        $noRecords = $this->Customer->getCustomerListCount($userId, $customerSearch);
        $order = 'LastOrder.orderID DESC';
        $query = $this->Customer->_innerJoinFilter($userId, 0, $noRecords, $customerSearch, $order);

        if ($this->Customer->exists($query['conditions'])) {
            $this->PhpExcel->createWorksheet();
            $this->PhpExcel->setDefaultFont('Calibri', 12);

            $tableHeader = array(
                array('label' => __('First Name'), 'filter' => true),
                array('label' => __('Last Name'), 'filter' => true),
                array('label' => __('E-Mail'), 'filter' => true),
                array('label' => __('Marketing'), 'filter' => true),
                array('label' => __('Language'), 'filter' => true),
                array('label' => __('Street 1'), 'filter' => true),
                array('label' => __('Street 2'), 'filter' => true),
                array('label' => __('City'), 'filter' => true),
                array('label' => __('State'), 'filter' => true),
                array('label' => __('ZIP/Postal Code'), 'filter' => true),
                array('label' => __('Telephone'), 'filter' => true),
                array('label' => __('# of Orders'), 'filter' => true),
                array('label' => __('Last Order'), 'filter' => true),
                array('label' => __('Total Shipping'), 'filter' => true),
                array('label' => __('Total Taxes'), 'filter' => true),
                array('label' => __('Total Spent'), 'filter' => true),
            );
            $this->PhpExcel->addTableHeader($tableHeader, array('name' => 'Cambria', 'bold' => true));

            $this->Customer->streamPagedQuery($query, function(array $customer) {
                $language = SupportedLanguages::getName($customer['Customer']['preferred_language']) ?: $customer['Customer']['preferred_language'];
                $rowData = array(
                    $customer['Customer']['firstname'],
                    $customer['Customer']['lastname'],
                    $customer['Customer']['email_address'],
                    $customer['Customer']['accepts_marketing'] ? 'Yes' : 'No',
                    $language,
                    $customer['LastOrder']['shipping_address1'],
                    $customer['LastOrder']['shipping_address2'],
                    $customer['LastOrder']['shipping_city'],
                    $customer['LastOrder']['shipping_state'],
                    $customer['LastOrder']['shipping_zipcode'],
                    $customer['LastOrder']['shipping_telephone'],
                    $customer['Customer']['totalOrder'],
                    $customer['LastOrder']['orderID'],
                    !empty($customer['OrderTotals']['totalShipping']) ? $customer['OrderTotals']['totalShipping'] : 0,
                    !empty($customer['OrderTotals']['totalTaxes']) ? $customer['OrderTotals']['totalTaxes'] : 0,
                    !empty($customer['OrderTotals']['totalValue']) ? $customer['OrderTotals']['totalValue'] : 0,
                );
                $this->PhpExcel->addTableRow($rowData);
            });

            $this->PhpExcel->addTableFooter();
            $filename = "{$this->shipearly_user['User']['company_name']} Customer Export " . date('Y-m-d') . ".xlsx";
            $this->PhpExcel->render($filename);
        }
    }

    /**
     * Get all the customers with the sale count and total sale amount
     * @param $userId
     * @param $noRecords
     * @param $page
     * @param $customerSearch
     * @param string $count
     */
    public function _CustomerLogic($userId, $noRecords, $page, $customerSearch, $count = '', $order = 'LastOrder.orderID DESC')
    {
        if (empty($count)) {
            $countCustomers = $this->Customer->getCustomerListCount($userId, $customerSearch);
            $this->set('countCustomers', $countCustomers);
        } else {
            $countCustomers = $count;
        }

        $paging = paging($page, $noRecords, $countCustomers);
        $this->set('paging', $paging);

        $customers = $this->Customer->getCustomerList($userId, $paging['offset'], $noRecords, $customerSearch, $order);
        $this->set('customers', $customers);
    }
}
