<?php
App::uses('AppController', 'Controller');

/**
 * Class DashboardsController
 *
 * @property OrderLogicComponent $OrderLogic
 * @property UserLogicComponent $UserLogic
 * @property StripeComponent $Stripe
 *
 * @property Order $Order
 * @property ManufacturerRetailer $ManufacturerRetailer
 * @property Viewslog $Viewslog
 * @property Product $Product
 * @property Contactpersons $Contactpersons
 * @property StripeUser $StripeUser
 */
class DashboardsController extends AppController
{

    /**
     * @var string
     */
    public $name = 'Dashboards';
    /**
     * @var array
     */
    public $components = array('OrderLogic', 'UserLogic', 'Stripe.Stripe');
    /**
     * @var array
     */
    public $uses = array('Order', 'ManufacturerRetailer', 'Viewslog', 'Product', 'Contactpersons', 'StripeUser');

    /**
     * Common dashboard for Retailer and Brand
     * @url: /
     */
    public function index()
    {
        $this->set('title_for_layout', __('Dashboard'));

        $userId = $this->getAuthUserIds();
        $userType = (string)$this->Auth->user('user_type');

        $noRecords = !empty($this->request->data['Order']['noRecords'])
            ? $this->request->data['Order']['noRecords']
            : 10;

        $sortOrder = $this->request->data('sort_order') ?? 'created DESC';

        $Notification = array(
            'count' => $this->Notification->countUserActivities($userId, $userType),
            'list' => $this->Notification->findUserActivities($userId, $userType, $sortOrder, $noRecords),
            'noRecords' => $noRecords,
        );
        $this->set('Notification', $Notification);
        $this->set('has_b2b_cart_permission', $this->Permissions->userHasB2bCartPermission($this->Auth->user()));

        if ($userType == 'Manufacturer') {
            $products = $this->Product->getProductIds($userId);
            $first_views = $this->Viewslog->countFirstProductViews($products);
            $total_views = $this->Viewslog->countTotalProductViews($products);
            $this->set('first_views', $first_views);
            $this->set('total_views', $total_views);
            $this->set('return_views', $total_views - $first_views);

            $this->set('active_dealers', $this->ManufacturerRetailer->countMasterRetailers($userId, 1));
            $this->set('Inquiry', $this->ManufacturerRetailer->countMasterRetailers($userId, 2));
        } elseif ($userType == 'Retailer') {
            $this->set('no_of_manufacturers', $this->ManufacturerRetailer->countManufacturers($userId));

            $radius = 10;

            $page = !empty($this->request->params['pageNo']) ? $this->request->params['pageNo'] : 1;
            $noRecords = 3;
            $this->set('noRecords', $noRecords);

            $product_cat = $this->Auth->user('user_categories');
            $this->set('product_cat', $product_cat);

            $categoryIds = array_keys($product_cat);

            $sel = !empty($this->request->data['cattopfilter']['product_cat'])
                ? $this->request->data['cattopfilter']['product_cat']
                : $categoryIds;
            $this->set('selected', $sel);

            $count_topproducts = $this->OrderLogic->topSellingProductsCountNearYou(implode(',', $sel), $this->Auth->user('latitude'), $this->Auth->user('longitude'), $radius);
            $this->set('count_topproducts', $count_topproducts);

            $paging_top = paging($page, $noRecords, $count_topproducts);
            $this->set('paging_top', $paging_top);

            $top_products = $this->OrderLogic->topSellingProductsNearYou(implode(',', $sel), $this->Auth->user('latitude'), $this->Auth->user('longitude'), $paging_top['offset'], $noRecords, $radius);
            $this->set('top_products', $top_products);

            $selrec = !empty($this->request->data['catrecentfilter']['product_cat'])
                ? $this->request->data['catrecentfilter']['product_cat']
                : $categoryIds;
            $this->set('selrec', $selrec);

            $count_recentproducts = $this->Product->newlyAddedProductCount(implode(',', $selrec));
            $this->set('count_recentproducts', $count_recentproducts);

            $paging_recent = paging($page, $noRecords, $count_recentproducts);
            $this->set('paging_recent', $paging_recent);

            $recent_products = $this->Product->newlyAddedProduct(implode(',', $selrec), $noRecords, $paging_recent['offset']);
            $this->set('recent_products', $recent_products);

            $retailerId = $this->User->getMainRetailerId((int)$this->Auth->user('id'));
            $validB2bBrandIds = array_keys($this->User->listConnectedBrandNames($retailerId));
            $brandSubdomain = $this->UserSubdomain->findWhitelabelSettings();
            $brandCartPermission = (
                $this->viewVars['has_b2b_cart_permission']
                && in_array($brandSubdomain['user_id'], $validB2bBrandIds)
                && $this->User->fieldByConditions('enable_b2b_cart', ['User.id' => $brandSubdomain['user_id']])
            );
            $this->set('brandCartPermission', $brandCartPermission);

            $existingB2bCart = [];
            if ($brandCartPermission) {
                $existingB2bCart = (array)(current($this->B2bCart->findExistingCarts($userId, $userType, $brandSubdomain['user_id'])) ?: []);
            }
            $this->set('existingB2bCart', $existingB2bCart);

            $defaultLocationId = (int)$this->Auth->user('id');
            if ($brandCartPermission) {
                $locations = $this->User->listRetailerLocationsForBrand($brandSubdomain['user_id'], $retailerId);
                if ($this->isRetailStaffUser()) {
                    $locations = array_intersect_key($locations, array_flip($this->getAuthUserIds()));
                }
                if (!array_key_exists($defaultLocationId, $locations)) {
                    $defaultLocationId = (int)key($locations) ?: null;
                }
            }
            $this->set('defaultLocationId', $defaultLocationId);
        }
    }

    /**
     * latest activities list on dashboard (ajax_response)
     */
    public function ajax_act()
    {
        if (!$this->request->is('ajax')) {
            throw new BadRequestException('Not an ajax request');
        }

        $userId = $this->getAuthUserIds();
        $userType = (string)$this->Auth->user('user_type');

        $noRecords = !empty($this->request->data['noRecords'])
            ? $this->request->data['noRecords']
            : 10;

        $count_not = !empty($this->request->data['count_not'])
            ? $this->request->data['count_not']
            : $this->Notification->countUserActivities($userId, $userType);

        $pageNumber = !empty($this->request->data['pageNumber'])
            ? $this->request->data['pageNumber']
            : 1;

        $sortField = $this->request->data('sortField') ?: 'created';
        $sortOrder = $this->request->data('sortOrder') ?: 'DESC';
        $order = "{$sortField} {$sortOrder}";

        $paging = paging($pageNumber, $noRecords, $count_not);

        $this->set('orders', $this->Notification->findUserActivities($userId, $userType, $order, $noRecords, $paging['offset']));

        $this->render('/Elements/latest', false);
    }

    /**
     * Top selling product list slide on retailer dashboard
     */
    public function topSellingProduct()
    {
        $this->autoRender = false;
        $this->layout = '';
        if ($this->request->is('ajax')) {
            $paging_top = paging($this->request->data['pageNumber'], $this->request->data['noRecords'], $this->request->data['count_products']);
            $all_products = $this->OrderLogic->topSellingProductsNearYou($this->request->data['dashBoardcatId'], $this->Auth->user('latitude'), $this->Auth->user('longitude'), $paging_top['offset'], $this->request->data['noRecords'], 10);
            $this->set('all_products', $all_products);
            $this->render('/Products/ajax_index');
        }
    }

    /**
     * Recent product list slide on retailer dashboard
     */
    public function recentProduct()
    {
        $this->autoRender = false;
        $this->layout = '';
        if ($this->request->is('ajax')) {
            $paging_recent = paging($this->request->data['pageNumber'], $this->request->data['noRecords'], $this->request->data['count_products']);
            $all_products = $this->Product->newlyAddedProduct($this->request->data['dashBoardcatId'], $this->request->data['noRecords'], $paging_recent['offset']);
            $this->set('all_products', $all_products);
            $this->render('/Products/ajax_index');
        }
    }

    /**
     * Profile setup page
     * Block all the users who not update all the neccesary inputs
     */
    public function profile_setup()
    {
        $userId = $this->Auth->user('id');
        $userType = $this->Auth->user('user_type');

        if (in_array($userType, [User::TYPE_STAFF, User::TYPE_SALES_REP], true)) {
            $this->User->UpdateAccountSetup($userId);
            return $this->redirect('/');
        }

        $setupConditions = $this->UserLogic->findProfileSetupConditions($userId);

        // Differences from Admin view
        $setupConditions->setShowShipmentSetting($userType === User::TYPE_MANUFACTURER);
        $setupConditions->setShowBrandAssociation(false);

        if (
            $setupConditions->evaluateSetupStatus() &&
            $this->User->UpdateAccountSetup($userId)
        ) {
            if ($userType === User::TYPE_RETAILER && !$this->Auth->user('Branch')) {
                $this->UserLogic->promptToConnectBankIfNotConnected($userId);
            }
            $this->redirect('/');
        }

        $this->set('title_for_layout', 'Account Setup');
        $this->set('setupConditions', $setupConditions);
    }

}
