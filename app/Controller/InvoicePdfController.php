<?php
App::uses('App<PERSON><PERSON>roller', 'Controller');
App::uses('OrderType', 'Utility');
App::uses('Configure', 'Core');

use Knp\Snappy\Pdf;
use ShipEarlyApp\Lib\Utility\SupportedLanguages;

/**
 * Class InvoicePdfController
 *
 * @property B2bShipToAddress $B2bShipToAddress
 * @property Order $Order
 * @property OrderProduct $OrderProduct
 * @property Product $Product
 * @property User $User
 */
class InvoicePdfController extends AppController
{
    const PDF_TYPE_B2B = 'b2b';
    const PDF_TYPE_B2C = 'b2c';

    public $uses = ['B2bShipToAddress', 'Order', 'OrderProduct', 'Product', 'User'];

    public function beforeFilter()
    {
        parent::beforeFilter();
        $this->Auth->allow(['viewPdf', 'generateInvoice']);
    }

    public function defineLanguage()
    {
        parent::defineLanguage();

        if (isset($this->request->query['lang'])) {
            $locale = SupportedLanguages::toLocale($this->request->query['lang']);
            if (in_array($locale, SupportedLanguages::getLocalesSet(), true)) {
                Configure::write('Config.language', $locale);
            }
        }
    }

    /**
     * Generate pdf at the time of order creation.
     *
     * url: '/viewPdf/:id/:type'
     *
     * @param int $id
     * @param string $type One of 'b2b' or 'b2c'
     */
    public function viewPdf(int $id, string $type)
    {
        $this->request->params['file'] = $this->request->param('file') ?: basename(get_order_invoice_pdf_filepath($id));
        $file = (string)$this->request->params['file'];

        $pdf = new Pdf(get_wkhtmltopdf_bin());
        $targetFile = get_order_invoice_pdf_filepath_directory() . $file;

        try {
            $pdf->getOutput(Router::url([
                'controller' => 'invoice_pdf', 'action' => 'generateInvoice', 'id' => $id, 'type' => $type, 'file' => $file,
                '?' => $this->request->query,
            ], true), [
                'custom-header' => [
                    'Accept-Language' => CakeRequest::header('Accept-Language'),
                    'Cookie' => CakeRequest::header('Cookie'),
                ],
            ]);
        } catch (Exception $e) {
            CakeLog::alert($e);

            return $this->_exceptionResponse(null, 'PDF generation failed');
        }

        $directory = dirname($targetFile);
        if (!is_dir($directory)) {
            mkdir($directory, 0777, true);
        }

        $generatedFile = current($pdf->temporaryFiles);
        rename($generatedFile, $targetFile);

        $this->response->file($targetFile);

        return $this->response;
    }

    public function generateInvoice(int $id, string $type)
    {
        $this->request->params['file'] = $this->request->param('file') ?: basename(get_order_invoice_pdf_filepath($id));

        $order = $this->Order->findForInvoicePdf($id, $type === static::PDF_TYPE_B2B);

        if (!empty($order['DealerOrder']['id'])) {
            $viewOrder = ($order['DealerOrder'] + $order['Order']);
            $orderProducts = $order['DealerOrder']['DealerOrderProduct'];
            $fromUser = $order['Brand'];
        } else {
            $viewOrder = $order['Order'];
            $orderProducts = $order['OrderProduct'];
            $fromUser = ($order['Order']['subType'] !== OrderType::SUB_TYPE_STOCK) ? $order['Brand'] : $order['Retailer'];
        }

        /** @var UserSetting $UserSetting */
        $UserSetting = ClassRegistry::init('UserSetting');
        $termsAndConditions = $UserSetting->getTermsAndConditions($order['Order']['user_id']);

        /** @var ManufacturerRetailer $ManufacturerRetailer */
        $ManufacturerRetailer = ClassRegistry::init('ManufacturerRetailer');
        $vatNumber = $ManufacturerRetailer->getVatNumber($order['Order']['user_id'], $order['Order']['retailer_id']);

        $billToAddress = $this->getBillToAddress($order);
        $shipToAddress = $this->getShipToAddress($order);
        $fromAddress = $this->NotificationLogic->emailAddressDisplayFormat(
            $fromUser['company_name'],
            $fromUser['address1'],
            $fromUser['address2'],
            $fromUser['city'],
            $fromUser['state_id'],
            $fromUser['country_id'],
            $fromUser['zipcode'],
            $fromUser['Contact']['value']
        );

        $this->set('order', $viewOrder);
        $this->set('orderProducts', $orderProducts);
        $this->set('user', $fromUser);
        $this->set('billToAddress', $billToAddress);
        $this->set('shipToAddress', $shipToAddress);
        $this->set('fromAddress', $fromAddress);
        $this->set('creditTerm', $order['CreditTerm']);
        $this->set('termsAndConditions', $termsAndConditions);
        $this->set('vatNumber', $vatNumber);
    }

    protected function getBillToAddress(array $order): string
    {
        if (
            ($order['Order']['order_type'] !== OrderType::WHOLESALE && !empty($order['DealerOrder']['id']))
            || ($order['Order']['order_type'] === OrderType::WHOLESALE && empty($order['BillingAddress']['id']))
        ) {
            $billToAddress = $this->NotificationLogic->emailAddressDisplayFormat(
                $order['Retailer']['company_name'],
                $order['Retailer']['address1'],
                $order['Retailer']['address2'],
                $order['Retailer']['city'],
                $order['Retailer']['state_id'],
                $order['Retailer']['country_id'],
                $order['Retailer']['zipcode'],
                $order['Retailer']['Contact']['value']
            );
        } else {
            $billToAddress = $this->NotificationLogic->emailAddressDisplayFormat(
                "{$order['BillingAddress']['first_name']} {$order['BillingAddress']['last_name']}",
                $order['BillingAddress']['address1'],
                $order['BillingAddress']['address2'],
                $order['BillingAddress']['city'],
                $order['BillingAddress']['state_id'],
                $order['BillingAddress']['country_id'],
                $order['BillingAddress']['zipcode'],
                $order['BillingAddress']['telephone']
            );
        }

        return $billToAddress;
    }

    /**
     * @param array $order
     * @return string ship to address in html format
     * @throws CakeException
     */
    protected function getShipToAddress(array $order): string
    {
        if (
            ($order['Order']['order_type'] !== OrderType::WHOLESALE && !empty($order['DealerOrder']['id']))
            || $order['Order']['order_type'] === OrderType::IN_STORE_PICKUP
        ) {
            $address = $this->B2bShipToAddress->findResolvedRetailerAddress($order['Brand']['id'], $order['Retailer']['id']);
            $shipToAddress = $this->NotificationLogic->emailAddressDisplayFormat(
                $order['Retailer']['company_name'],
                $address['B2bShipToAddress']['address1'],
                $address['B2bShipToAddress']['address2'],
                $address['B2bShipToAddress']['city'],
                $address['B2bShipToAddress']['state_id'],
                $address['B2bShipToAddress']['country_id'],
                $address['B2bShipToAddress']['zipcode'],
                $address['B2bShipToAddress']['telephone']
            );
        } else {
            $shipToAddress = $this->NotificationLogic->emailAddressDisplayFormat(
                $order['Order']['customer_name'],
                $order['Order']['shipping_address1'],
                $order['Order']['shipping_address2'],
                $order['Order']['shipping_city'],
                $order['Order']['shipping_state'],
                $order['Order']['shipping_country'],
                $order['Order']['shipping_zipcode'],
                $order['Order']['shipping_telephone']
            );
        }

        return $shipToAddress;
    }
}
