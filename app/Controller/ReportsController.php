<?php

use ShipEarlyApp\Lib\Utility\SupportedLanguages;

App::uses('App<PERSON><PERSON>roller', 'Controller');
App::uses('CakeNumber', 'Utility');
App::uses('OrderStatus', 'Utility');
App::uses('Configure', 'Core');
App::uses('Shim', 'Shim.Lib');

/**
 * Class ReportsController
 *
 * @property PhpExcelComponent $PhpExcel
 *
 * @property AbandonCart $AbandonCart
 * @property DealerOrder $DealerOrder
 * @property EcommerceView $EcommerceView
 * @property EcommerceViewRetailer $EcommerceViewRetailer
 * @property ManufacturerRetailer $ManufacturerRetailer
 * @property ManufacturerSalesRep $ManufacturerSalesRep
 * @property Order $Order
 * @property TranslateComponent $Translate
 * @property UserCategories $UserCategories
 * @property Viewslog $Viewslog
 */
class ReportsController extends AppController
{

    /**
     * @var string
     */
    public $name = 'Reports';

    /**
     * @var array
     */
    public $components = array('PhpExcel', 'Translate');

    /**
     * @var array
     */
    public $uses = array('AbandonCart', 'DealerOrder', 'EcommerceView', 'EcommerceViewRetailer', 'ManufacturerRetailer', 'ManufacturerSalesRep', 'Order', 'UserCategories', 'Viewslog');

    /**
     * Reports tab menu
     */
    public function index()
    {
        $this->set('title_for_layout', __('Reports'));

        $userType = $this->Auth->user('user_type');
        if ($userType === User::TYPE_MANUFACTURER) {
        } elseif ($userType === User::TYPE_STAFF) {
            return $this->redirect(['controller' => 'reports', 'action' => 'dealerpayouts']);
        } elseif ($userType === User::TYPE_SALES_REP) {
            return $this->render('index_salesrep');
        } else {
            throw new ForbiddenException('Unknown user type ' . $userType);
        }
    }

    public function admin_index()
    {
        $this->set('title_for_layout', __('Reports'));
        $this->layout = 'admin';
    }

    public function listreport()
    {
        if ($this->request->isAjax()) {
            $this->autoRender = false;
        }
        $this->set('report_name', "Abandoned carts");

        $userId = $this->Auth->user('id');
        $date_range_start = !empty($this->request->data['daterange']['start']) ? $this->request->data['daterange']['start'] : date('Y-m-d', strtotime('1 MONTH AGO'));
        $date_range_end = !empty($this->request->data['daterange']['end']) ? $this->request->data['daterange']['end'] : date('Y-m-d');

        $carts = $this->AbandonCart->getAbandonedCarts($userId, $date_range_start, $date_range_end);

        $tablecolumns = [
            ["title" => "Checkout"],
            ["title" => "Date"],
            ["title" => "Placed By"],
            ["title" => "Language"],
            ["title" => "Email"],
            ["title" => "Total"],
            ["title" => "Status"],
        ];
        $tabledata = array_map(function(array $cart): array {
            return array(
                $cart["AbandonCart"]["abandon_cart_token"],
                $cart["0"]["created_at"],
                $cart["0"]["customer_name"],
                SupportedLanguages::getName($cart['AbandonCart']['preferred_language']) ?: $cart['AbandonCart']['preferred_language'],
                $cart["AbandonCart"]["email_address"],
                $this->_formatMoney($cart["AbandonCart"]["cart"]['total_price']),
                $cart["AbandonCart"]["status_name"],
            );
        }, $carts);
        $tablecolumndefs = array(
            "numClass" => "numericCol",
            "cols" => [4],
            "cols_datatype" => ["float"],
            "hyperlink_cols" => [0],
            "hyperlink_url" => ["{id}/abandoncart"],
            "hyperlinkvalues" => array_map(function($cart) { return $cart["AbandonCart"]["abandon_cart_token"]; }, $carts)
        );

        $this->set('ordersTableColumns', json_encode($tablecolumns));
        $this->set('ordersTableData', json_encode($tabledata));
        $this->set('ordersTableColumnDefs', json_encode($tablecolumndefs));

        if ($this->request->isAjax()) {
            $this->response->body(json_encode(['success' => 'success', 'ordersTableData' => $tabledata]));
            return $this->response;
        }
        $this->render('list');
    }

    public function export_abandonedcarts()
    {
        $this->autoRender = false;

        $userId = $this->Auth->user('id');
        $date_range_start = isset($this->request->query['daterange']['start']) ? $this->request->query['daterange']['start'] : '';
        $date_range_end = isset($this->request->query['daterange']['end']) ? $this->request->query['daterange']['end'] : '';
        $conditions = $this->AbandonCart->getExportConditions($userId, $date_range_start, $date_range_end);

        if ($this->AbandonCart->exists($conditions)) {
            $this->PhpExcel->createWorksheet();
            $this->PhpExcel->setDefaultFont('Calibri', 12);

            $tableHeader = array(
                array('label' => __('Checkout Code'), 'filter' => true),
                array('label' => __('Date'), 'filter' => true),
                array('label' => __('Customer Name'), 'filter' => true),
                array('label' => __('Language'), 'filter' => true),
                array('label' => __('Email'), 'filter' => true),
                array('label' => __('City'), 'filter' => true),
                array('label' => __('State/Province'), 'filter' => true),
                array('label' => __('Total')),
                array('label' => __('Status'), 'filter' => true),
                array('label' => __('Product Name'), 'filter' => true),
                array('label' => __('Product SKU'), 'filter' => true),
                array('label' => __('Unit Price'), 'filter' => true),
                array('label' => __('Quantity')),
                array('label' => __('Line Total'), 'filter' => true),
            );
            $this->PhpExcel->addTableHeader($tableHeader, array('name' => 'Cambria', 'bold' => true));

            $this->AbandonCart->streamExportData($conditions, function($cart) {
                foreach ($cart['AbandonCart']['cart']['items'] as $item) {
                    $preferredLanguage = $cart['AbandonCart']['preferred_language'];
				    $language = I18n::getInstance()->l10n->catalog($preferredLanguage)['language'] ?? $preferredLanguage;
                    $rowData = array(
                        $cart["AbandonCart"]["abandon_cart_token"],
                        $cart["0"]["created_at"],
                        $cart["0"]["customer_name"],
                        $language,
                        $cart["AbandonCart"]["email_address"],
                        $cart["AbandonCart"]["address"]["address"]["city"],
                        $cart["AbandonCart"]["address"]["address"]["regionName"],
                        $cart["AbandonCart"]["cart"]['total_price'],
                        $cart["AbandonCart"]["status_name"],
                        $item['title'],
                        $item['sku'],
                        $item['price'],
                        $item['quantity'],
                        $item['line_price'],
                    );
                    $this->PhpExcel->addTableRow($rowData);
                }
            });

            $this->PhpExcel->addTableFooter();
            $filename = "{$this->shipearly_user['User']['company_name']} Abandoned Cart Export " . date('Y-m-d') . ".xlsx";
            $this->PhpExcel->render($filename);
        }
    }

    public function ajax_abandonedcart()
    {
        $token = $this->request->param('token');
        $abandonedcart = $this->AbandonCart->find('first', array(
            'conditions' => array('AbandonCart.abandon_cart_token' => $token),
            'fields' => array(
                'AbandonCart.email_address',
                'AbandonCart.cart',
                'AbandonCart.address',
                'AbandonCart.created_at',
                'AbandonCart.status'
            )
        ));

        $cart = json_decode($abandonedcart["AbandonCart"]["cart"], true);
        $cart['total_price'] /= 100;
        $cart['items'] = array_map(function($item) {
            $item['price'] /= 100;
            $item['line_price'] /= 100;
            return $item;
        }, $cart['items']);

        $address = json_decode($abandonedcart["AbandonCart"]["address"], true);
        $address['address']['company_name'] = $address['address']['First_name'].' '.$address['address']['Last_name'];
        $address['address']['address1'] = $address['address']['address'];
        $address['address']['state_id'] = $address['address']['regionName'];
        $address['address']['country_id'] = $address['address']['countryName'];
        $address['address']['zipcode'] = $address['address']['PostalCode'];
        $address['address']['telephone'] = $address['address']['phone'];

        $this->set('token', $token);
        $this->set('email_address', $abandonedcart["AbandonCart"]["email_address"]);
        $this->set('customer_address', $this->addressDisplayFormat($address['address']));
        $this->set('cart', $cart);
        $this->set('currency_code', $address['users']['User']['currency_code']);
    }

    public function viewsbyproduct()
    {
        $userId = $this->Auth->user('id');
        if (!in_array($this->Auth->user('user_type'), [USER::TYPE_MANUFACTURER, USER::TYPE_SALES_REP])) {
            $this->redirect($this->referer());
        }

        $this->set('name', 'viewsbyproduct');
        $this->set('currentUrl', str_replace('+', ' ', $this->request->here()));
        $this->set('report_name', __('Views By Product'));
        $this->set('show_report_type_option', false);
        $this->set('show_report_currency_option', false);
        if ($this->Auth->user('user_type') === USER::TYPE_SALES_REP) {
            $storesList = Hash::combine(
                $this->ManufacturerRetailer->findAllSalesRepUserRetailerNames($userId, ['Retailer.company_name' => 'ASC']),
                '{n}.Retailer.id',
                '{n}.Retailer.company_name'
            );
            $brandList = $this->User->listSalesRepBrandNames($userId);
        } else {
            $storesList = $this->EcommerceViewRetailer->listViewedRetailerNames($userId);
            $brandList = [];
        }
        $this->set('single_store', true);
        $this->set('storesList', $storesList);
        $this->set('brandList', $brandList);
        $this->set('salesRepOptions', []);
        $this->set('currencyOptions', []);

        list($date_range_start, $date_range_end) = $this->_getDateRangeStartEnd($this->request);
        $stores = array_filter((array)$this->request->data('store_id'));

        if ($this->Auth->user('user_type') === USER::TYPE_SALES_REP) {
            $userIds = array_keys($brandList);
            $brandId = array_filter((array)$this->request->data('brand_list'));
            if ($brandId) {
                $userIds = array_intersect($userIds, $brandId);
            }
        } else{
            $userIds = [$userId];
        }
        $conditions = [
            'EcommerceView.user_id' => $userIds,
            'EcommerceViewRetailer.retailer_id' => $stores,
            'EcommerceView.created_at BETWEEN ? AND ?' => $this->EcommerceView->convertDateRange($date_range_start, $date_range_end),
        ];
        $reportData = $this->EcommerceView->reportRetailerViewsBy('product_title', $conditions);

        $chartOptions = [
            'title' => 'Total Views',
            'valueField' => 'views',
            'categoryField' => 'product',
        ];
        $chartData = array_map(fn(array $values): array => [
            'product' => '',
            'cat_label' => $values['EcommerceView']['product_title'],
            'views' => $values['EcommerceView']['views'],
        ], $reportData);

        $tablecolumns = [
            0 => ['title' => __('Category')],
            1 => ['title' => __('Product')],
            2 => ['title' => __('Views')],
            3 => ['title' => __('Average Distance'), 'average' => true],
        ];
        $tablecolumndefs = [
            'numClass' => 'numericCol',
            'cols' => [2, 3],
            'cols_datatype' => ['int', 'int'],
            'hyperlink_cols' => [],
            'hyperlink_url' => [],
        ];
        $tabledata = array_map(fn(array $values): array => [
            0 => $values['EcommerceView']['product_type'],
            1 => $values['EcommerceView']['product_title'],
            2 => $values['EcommerceView']['views'],
            3 => $values['EcommerceView']['average_distance'],
        ], $reportData);

        $response = $this->_salesReportResponse($chartOptions, $chartData, $tabledata, $tablecolumns, $tablecolumndefs);

        if ($this->request->is('ajax') && isset($response['json_response'])) {
            $this->response->body($response['json_response']);
            return $this->response;
        }
        $this->render('view');
    }

    public function viewsbyproductsku()
    {
        $userId = $this->Auth->user('id');
        if (!in_array($this->Auth->user('user_type'), [USER::TYPE_MANUFACTURER, USER::TYPE_SALES_REP])) {
            $this->redirect($this->referer());
        }

        $this->set('name', 'viewsbyproductsku');
        $this->set('currentUrl', str_replace('+', ' ', $this->request->here()));
        $this->set('report_name', __('Views By Product SKU'));
        $this->set('show_report_type_option', false);
        $this->set('show_report_currency_option', false);
        if ($this->Auth->user('user_type') === USER::TYPE_SALES_REP) {
            $storesList = Hash::combine(
                $this->ManufacturerRetailer->findAllSalesRepUserRetailerNames($userId, ['Retailer.company_name' => 'ASC']),
                '{n}.Retailer.id',
                '{n}.Retailer.company_name'
            );
            $brandList = $this->User->listSalesRepBrandNames($userId);
        } else {
            $storesList = $this->EcommerceViewRetailer->listViewedRetailerNames($userId);
            $brandList = [];
        }
        $this->set('single_store', true);
        $this->set('storesList', $storesList);
        $this->set('brandList', $brandList);
        $this->set('salesRepOptions', []);
        $this->set('currencyOptions', []);

        list($date_range_start, $date_range_end) = $this->_getDateRangeStartEnd($this->request);
        $stores = array_filter((array)$this->request->data('store_id'));

        if ($this->Auth->user('user_type') === USER::TYPE_SALES_REP) {
            $userIds = array_keys($brandList);
            $brandId = array_filter((array)$this->request->data('brand_list'));
            if ($brandId) {
                $userIds = array_intersect($userIds, $brandId);
            }
        } else{
            $userIds = [$userId];
        }
        $conditions = [
            'EcommerceView.user_id' => $userIds,
            'EcommerceViewRetailer.retailer_id' => $stores,
            'EcommerceView.created_at BETWEEN ? AND ?' => $this->EcommerceView->convertDateRange($date_range_start, $date_range_end),
        ];
        $reportData = $this->EcommerceView->reportRetailerViewsBy('product_sku', $conditions);

        $chartOptions = [
            'title' => 'Total Views',
            'valueField' => 'views',
            'categoryField' => 'product',
        ];
        $chartData = array_map(fn(array $values): array => [
            'product' => '',
            'cat_label' => $values['EcommerceView']['product_sku'],
            'views' => $values['EcommerceView']['views'],
        ], $reportData);

        $tablecolumns = [
            0 => ['title' => __('Category')],
            1 => ['title' => __('Variant Title')],
            2 => ['title' => __('Variant SKU')],
            3 => ['title' => __('Views')],
            4 => ['title' => __('Average Distance'), 'average' => true],
        ];
        $tablecolumndefs = [
            'numClass' => 'numericCol',
            'cols' => [3, 4],
            'cols_datatype' => ['int', 'int'],
            'hyperlink_cols' => [],
            'hyperlink_url' => [],
        ];
        $tabledata = array_map(fn(array $values): array => [
            0 => $values['EcommerceView']['product_type'],
            1 => $values['EcommerceView']['variant_title'],
            2 => $values['EcommerceView']['product_sku'],
            3 => $values['EcommerceView']['views'],
            4 => $values['EcommerceView']['average_distance'],
        ], $reportData);

        $response = $this->_salesReportResponse($chartOptions, $chartData, $tabledata, $tablecolumns, $tablecolumndefs);

        if ($this->request->is('ajax') && isset($response['json_response'])) {
            $this->response->body($response['json_response']);
            return $this->response;
        }
        $this->render('view');
    }

    public function view()
    {
        $report_type = $this->request->param('name');
        if (empty($report_type)) {
            $this->redirect(['controller' => 'reports', 'action' => 'index']);
            return null;
        }
        $this->set('name', $report_type);
        if ($this->Auth->user('user_type') == User::TYPE_SALES_REP) {
            $brandIds = array_keys($this->User->listSalesRepBrandNames($this->Auth->user('id')));
            $currencyOptions = $this->_findOrderCurrencyCodes($brandIds);
        } else {
            $currencyOptions = $this->_findOrderCurrencyCodes($this->Auth->user('id'));
        }
        $this->set('currencyOptions', $currencyOptions);
        $this->request->data('selectedCurrencyOption', $this->request->data('selectedCurrencyOption') ?: current($currencyOptions));
        $this->set('selectedCurrencyOption', $this->request->data('selectedCurrencyOption'));

        $currentUrl = str_replace('+', ' ', $this->request->here());
        $this->set('currentUrl', $currentUrl);

        // Populate user filter dropdowns
        if ($this->Auth->user('user_type') !== User::TYPE_SALES_REP) {
            $storesList = $this->Order->listStoreNames(['Order.user_id' => $this->Auth->user('id')]);
            $salesRepOptions = $this->ManufacturerSalesRep->listConnectedSalesReps(['Order.user_id' => $this->Auth->user('id')]);
            $brandList = [];
        } else {
            $storesList = Hash::combine(
                $this->ManufacturerRetailer->findAllSalesRepUserRetailerNames($this->Auth->user('id'), ['Retailer.company_name' => 'ASC']),
                '{n}.Retailer.id',
                '{n}.Retailer.company_name'
            );
            $salesRepOptions = [];
            $brandList = $this->User->listSalesRepBrandNames($this->Auth->user('id'));
        }

        if ($report_type == 'salesbymonth') {
            $this->set('storesList', $storesList);
            $this->set('salesRepOptions', $salesRepOptions);
            $this->set('brandList', $brandList);
            $this->set('show_report_type_option', true);
            $this->set('show_report_currency_option', true);
            if (isset($this->request->query['my'])) {
                $this->set('report_name', "Sales By Days");
                $response = $this->_salesByDaysInMonth($this->request->query['my'], $this->request->query['r']);
            } else {
                $this->set('report_name', "Sales By Month");
                $response = $this->_salesByMonthResults();
            }
        } elseif ($report_type == 'salesbylocation') {
            $this->set('storesList', $storesList);
            $this->set('salesRepOptions', $salesRepOptions);
            $this->set('report_name', "Sales By Location");
            $this->set('show_report_type_option', true);
            $this->set('show_report_currency_option', true);
            if (isset($this->request->query['country'])) {
                $response = $this->_salesByCountryResults($this->request->query['country'], $this->request->query['r']);
            } elseif (isset($this->request->query['state'])) {
                $response = $this->_salesByStateResults($this->request->query['state'], $this->request->query['r']);
            } else {
                $response = $this->_salesByLocationResults();
            }
        } elseif ($report_type == 'salesbyproduct') {
            $this->set('storesList', $storesList);
            $this->set('salesRepOptions', $salesRepOptions);
            $this->set('brandList', $brandList);
            $this->set('report_name', __('Sales By Product'));
            $this->set('show_report_type_option', true);
            $this->set('show_report_currency_option', true);
            $response = $this->_salesByProductResults();
        } elseif ($report_type == 'salesbyproductsku') {
            $this->set('storesList', $storesList);
            $this->set('salesRepOptions', $salesRepOptions);
            $this->set('brandList', $brandList);
            $this->set('report_name', __('Sales By Product SKU'));
            $this->set('show_report_type_option', true);
            $this->set('show_report_currency_option', true);
            $response = $this->_salesByProductSKUResults();
        } elseif ($report_type == 'salesbyordertype') {
            $this->set('storesList', $storesList);
            $this->set('salesRepOptions', $salesRepOptions);
            $this->set('report_name', "Sales By Order Type");
            if (!isset($this->request->data['reporttypeoption'])) {
                $this->request->data['reporttypeoption'] = "All";
            }
            $this->set('show_report_type_option', false);
            $this->set('show_report_currency_option', true);
            $response = $this->_salesByOrderTypeResults();
        } elseif ($report_type == 'salesbyretailerscount') {
            $this->set('report_name', "Retailers By Month");
            $this->set('salesRepOptions', $salesRepOptions);
            $this->set('show_report_type_option', false);
            $this->set('show_report_currency_option', true);
            $response = $this->_salesByRetailerMonthlyResults();
        } elseif ($report_type == 'salesbyretailers') {
            $this->set('report_name', "Sales By Retailers");
            $this->set('salesRepOptions', $salesRepOptions);
            $this->set('brandList', $brandList);
            $this->set('show_report_type_option', true);
            $this->set('show_report_currency_option', true);
            $response = $this->_salesByRetailerResults();
        }
        if ($this->request->isAjax() && isset($response['json_response'])) {
            $this->response->body($response['json_response']);
            return $this->response;
        }
    }

    /**
     * @param int|int[] $brandId
     * @return string[]
     */
    protected function _findOrderCurrencyCodes($brandId): array
    {
        $brandCurrency = $this->User->fieldByConditions('currency_code', ['id' => $brandId]);
        $brandCurrency = [$brandCurrency => $brandCurrency];

        $orderCurrencyCodes = $this->Order->find('list', [
            'recursive' => -1,
            'conditions' => ['Order.user_id' => $brandId],
            'fields' => ['currency_code', 'currency_code'],
            'group' => ['Order.currency_code'],
            'order' => ['Order.currency_code' => 'ASC'],
        ]);

        return $brandCurrency + $orderCurrencyCodes;
    }

    public function _salesByMonthResults()
    {
        $userId = $this->Auth->user('id');
        $userType = $this->Auth->user('user_type');
        list($date_range_start, $date_range_end) = $this->_getDateRangeStartEnd($this->request);
        $stores = $this->request->data('store_id');
        $salesRep = $this->request->data('sales_rep');
        $brandList = $this->request->data('brand_list');

        $report_type_option = !empty($this->request->data['reporttypeoption'])
            ? $this->request->data['reporttypeoption']
            : 'Direct';

        $conditions = $this->_getGeneralOrderConditions($report_type_option, $this->request->data('selectedCurrencyOption'));

        $tabledata = [];
        $wholesale_data = [];
        if($report_type_option == 'Wholesale' || $report_type_option == 'All') {
            $orderGraphDealer = $this->Order->getSalesByMonthForWholesale($userId, $date_range_start, $date_range_end, $stores, $salesRep, $brandList, $conditions, $userType);

            $tabledata = array_map(function($value) {
                return array(
                    $value[0]['month_n_y'],
                    $value[0]['month_c_y'],
                    $value[0]['no_of_order'],
                    $this->_formatMoney($value[0]['gross_sales']),
                    $this->_formatMoney($value[0]['total_discounts']),
                    $this->_formatMoney($value[0]['net_sales']),
                    $this->_formatMoney($value[0]['total_shipping']),
                    $this->_formatMoney($value[0]['total_taxes']),
                    $this->_formatMoney($value[0]['sales']),
                );
            }, $orderGraphDealer);

            $wholesale_data = $tabledata;
        }
        if($report_type_option != 'Wholesale') {
            $orderGraph = $this->Order->getSalesByMonth($userId, $date_range_start, $date_range_end, $stores,  $salesRep, $brandList, $conditions, $userType);

            $tabledata = array_map(function($value) {
                return array(
                    $value[0]['month_n_y'],
                    $value[0]['month_c_y'],
                    $value[0]['no_of_order'],
                    $this->_formatMoney($value[0]['gross_sales']),
                    $this->_formatMoney($value[0]['total_discounts']),
                    $this->_formatMoney($value[0]['net_sales']),
                    $this->_formatMoney($value[0]['total_shipping']),
                    $this->_formatMoney($value[0]['total_taxes']),
                    $this->_formatMoney($value[0]['sales']),
                );
            }, $orderGraph);
        }

        $monthInRange = $this->Order->getDateRange($date_range_start, $date_range_end);
        $tabledata = $this->_fillEmptyTableDates($tabledata, $monthInRange);

        if($report_type_option == 'All') {
            foreach ($tabledata as $key => $value) {
                $pos = array_search($value[0], array_column($wholesale_data, 1));
                if($pos > -1) {
                    $tabledata[$key][1] = $tabledata[$key][1] + $wholesale_data[$pos][2];
                    foreach (range(2, 7) as $i) {
                        $tabledata[$key][$i] = $this->_formatMoney($this->_unformatMoney($tabledata[$key][$i]) + $this->_unformatMoney($wholesale_data[$pos][$i + 1]));
                    }
                }
            }
        }

        $chartData = array_map(function($value) {
            list($month, $year) = explode(' ', $value[0]);
            return array(
                'month' => substr($month, 0, 3),
                'cat_label' => $value[0],
                'sales' => $this->_unformatMoney($value[2]),
            );
        }, $tabledata);

        $chartOptions = array(
            'title' => 'Total Sales',
            'valueField' => 'sales',
            'categoryField' => 'month',
        );
        $tablecolumns = array(
            ['title' => 'Month'],
            ['title' => 'Orders'],
            ['title' => 'Gross Sales'],
            ['title' => 'Discounts'],
            ['title' => 'Net Sales'],
            ['title' => 'Shipping'],
            ['title' => 'Tax'],
            ['title' => 'Total Sales'],
        );
        $tablecolumndefs = array(
            'numClass' => 'numericCol',
            'cols' => range(1, 7),
            'cols_datatype' => ['int'] + array_fill(1, 6, 'float'),
            'hyperlink_cols' => [0],
            'hyperlink_url' => ["reports/view/salesbymonth?r={$report_type_option}&my="],
        );

        return $this->_salesReportResponse($chartOptions, $chartData, $tabledata, $tablecolumns, $tablecolumndefs);
    }

    public function _salesByDaysInMonth($month_year, $reporttypeoption)
    {
        $userId = $this->Auth->user('id');

        if ($this->request->isAjax()) {
            list($date_range_start, $date_range_end) = $this->_getDateRangeStartEnd($this->request);
        } else {
            $date_range_start = date('Y-m-01', strtotime($month_year));
            $date_range_end = date('Y-m-t', strtotime($month_year));
        }

        $stores = $this->request->data('store_id');
        $salesRep = $this->request->data('sales_rep');

        $report_type_option = $this->request->isAjax()
            ? $this->request->data('reporttypeoption')
            : $reporttypeoption;
        if (empty($report_type_option)) {
            $report_type_option = 'Direct';
        }

        $conditions = $this->_getGeneralOrderConditions($report_type_option, $this->request->data('selectedCurrencyOption'));

        $this->set('start_date', $date_range_start);
        $this->set('end_date', $date_range_end);
        $this->set('report_type_option', $report_type_option);

        $tabledata = [];
        $wholesale_data = [];
        if($report_type_option == 'Wholesale' || $report_type_option == 'All') {
            $orderGraphDealer = $this->Order->getSalesByDayInMonthForWholesale($userId, $date_range_start, $date_range_end, $stores, $salesRep, $conditions);

            $tabledata = array_map(function($value) {
                return array(
                    $value[0]['day_m'],
                    $value[0]['day_m_y'],
                    $value[0]['no_of_order'],
                    $this->_formatMoney($value[0]['gross_sales']),
                    $this->_formatMoney($value[0]['total_discounts']),
                    $this->_formatMoney($value[0]['net_sales']),
                    $this->_formatMoney($value[0]['total_shipping']),
                    $this->_formatMoney($value[0]['total_taxes']),
                    $this->_formatMoney($value[0]['sales']),
                );
            }, $orderGraphDealer);

            $wholesale_data = $tabledata;
        }
        if($report_type_option != 'Wholesale') {
            $orderGraph = $this->Order->getSalesByDayInMonth($userId, $date_range_start, $date_range_end, $stores, $salesRep, $conditions);

            $tabledata = array_map(function($value) {
                return array(
                    $value[0]['day_m'],
                    $value[0]['day_m_y'],
                    $value[0]['no_of_order'],
                    $this->_formatMoney($value[0]['gross_sales']),
                    $this->_formatMoney($value[0]['total_discounts']),
                    $this->_formatMoney($value[0]['net_sales']),
                    $this->_formatMoney($value[0]['total_shipping']),
                    $this->_formatMoney($value[0]['total_taxes']),
                    $this->_formatMoney($value[0]['sales']),
                );
            }, $orderGraph);
        }
        $daysInRange = $this->Order->getDateRangeByDays($date_range_start, $date_range_end);
        $tabledata = $this->_fillEmptyTableDates($tabledata, $daysInRange);

        if($report_type_option == 'All') {
            foreach ($tabledata as $key => $value) {
                $pos = array_search($value[0], array_column($wholesale_data, 1));
                if($pos > -1) {
                    $tabledata[$key][1] = $tabledata[$key][1] + $wholesale_data[$pos][2];
                    foreach (range(2, 7) as $i) {
                        $tabledata[$key][$i] = $this->_formatMoney($this->_unformatMoney($tabledata[$key][$i]) + $this->_unformatMoney($wholesale_data[$pos][$i + 1]));
                    }
                }
            }
        }

        $chartData = array_map(function($value) {
            list($weekday, $day, $month, $year) = explode(' ', $value[0]);
            return array(
                'day' => (int)$day,
                'cat_label' => $value[0],
                'sales' => $this->_unformatMoney($value[2]),
            );
        }, $tabledata);

        $chartOptions = array(
            'title' => 'Total Sales',
            'valueField' => 'sales',
            'categoryField' => 'day',
        );
        $tablecolumns = array(
            ['title' => 'Day'],
            ['title' => 'Orders'],
            ['title' => 'Gross Sales'],
            ['title' => 'Discounts'],
            ['title' => 'Net Sales'],
            ['title' => 'Shipping'],
            ['title' => 'Tax'],
            ['title' => 'Total Sales'],
        );
        $tablecolumndefs = array(
            'numClass' => 'numericCol',
            'cols' => range(1, 7),
            'cols_datatype' => ['int'] + array_fill(1, 6, 'float'),
            'hyperlink_cols' => [],
            'hyperlink_url' => [],
        );

        return $this->_salesReportResponse($chartOptions, $chartData, $tabledata, $tablecolumns, $tablecolumndefs);
    }

    public function _salesByLocationResults()
    {
        $userId = $this->Auth->user('id');
        list($date_range_start, $date_range_end) = $this->_getDateRangeStartEnd($this->request);
        $stores = $this->request->data('store_id');
        $salesRep = $this->request->data('sales_rep');

        $report_type_option = !empty($this->request->data['reporttypeoption'])
            ? $this->request->data['reporttypeoption']
            : 'Direct';

        $conditions = $this->_getGeneralOrderConditions($report_type_option, $this->request->data('selectedCurrencyOption'));

        $chartData = [];
        $tabledata = [];
        $wholesale_data = [];
        if($report_type_option == 'Wholesale' || $report_type_option == 'All') {
            $orderGraphDealer = $this->Order->getSalesByLocationForWholesale($userId, $date_range_start, $date_range_end, $stores, $salesRep, $conditions);
            $chartData = array_map(function($values) {
                return array(
                    'country' => $values['Order']['billing_country'],
                    'cat_label' => $values['Order']['billing_country'],
                    'sales' => number_format($values[0]['sales'], 2, '.', ''),
                );
            }, $orderGraphDealer);
            $tabledata = array_map(function($values) {
                return array(
                    $values['Order']['billing_country'],
                    $values[0]['no_of_order'],
                    $this->_formatMoney($values[0]['gross_sales']),
                    $this->_formatMoney($values[0]['total_discounts']),
                    $this->_formatMoney($values[0]['net_sales']),
                    $this->_formatMoney($values[0]['total_shipping']),
                    $this->_formatMoney($values[0]['total_taxes']),
                    $this->_formatMoney($values[0]['sales']),
                );
            }, $orderGraphDealer);
            $wholesale_data = $tabledata;
        }
        if($report_type_option != 'Wholesale') {
            $orderGraph = $this->Order->getSalesByLocation($userId, $date_range_start, $date_range_end, $stores, $salesRep, $conditions);
            foreach ($orderGraph as $values) {
                array_push($chartData, [
                    'country' => $values['Order']['billing_country'],
                    'cat_label' => $values['Order']['billing_country'],
                    'sales' => number_format($values[0]['sales'], 2, '.', ''),
                ]);
            }
            $tabledata = array_map(function($values) {
                return array(
                    $values['Order']['billing_country'],
                    $values[0]['no_of_order'],
                    $this->_formatMoney($values[0]['gross_sales']),
                    $this->_formatMoney($values[0]['total_discounts']),
                    $this->_formatMoney($values[0]['net_sales']),
                    $this->_formatMoney($values[0]['total_shipping']),
                    $this->_formatMoney($values[0]['total_taxes']),
                    $this->_formatMoney($values[0]['sales']),
                );
            }, $orderGraph);
        }

        if($report_type_option == 'All') {
            foreach ($tabledata as $key => $value) {
                $pos = array_search($value[0], array_column($wholesale_data, 0));
                if($pos > -1) {
                    $tabledata[$key][1] = $tabledata[$key][1] + $wholesale_data[$pos][1];
                    foreach (range(2, 7) as $i) {
                        $tabledata[$key][$i] = $this->_formatMoney($this->_unformatMoney($tabledata[$key][$i]) + $this->_unformatMoney($wholesale_data[$pos][$i]));
                    }
                }
            }
            $chartData = array_map(function($value) {
                return array(
                    'country' => $value[0],
                    'cat_label' => $value[0],
                    'sales' => $this->_unformatMoney($value[7]),
                );
            }, $tabledata);
        }

        $chartOptions = array(
            'title' => 'Total Sales',
            'valueField' => 'sales',
            'categoryField' => 'country',
        );
        $tablecolumns = array(
            ['title' => 'Billing Country'],
            ['title' => 'Orders'],
            ['title' => 'Gross Sales'],
            ['title' => 'Discounts'],
            ['title' => 'Net Sales'],
            ['title' => 'Shipping'],
            ['title' => 'Tax'],
            ['title' => 'Total Sales'],
        );
        $tablecolumndefs = array(
            'numClass' => 'numericCol',
            'cols' => range(1, 7),
            'cols_datatype' => ['int'] + array_fill(1, 6, 'float'),
            'hyperlink_cols' => [0],
            'hyperlink_url' => ["reports/view/salesbylocation?r={$report_type_option}&country="],
        );

        return $this->_salesReportResponse($chartOptions, $chartData, $tabledata, $tablecolumns, $tablecolumndefs);
    }

    public function _salesByCountryResults($country_name, $reporttypeoption)
    {
        $userId = $this->Auth->user('id');
        list($date_range_start, $date_range_end) = $this->_getDateRangeStartEnd($this->request);
        $stores = $this->request->data('store_id');
        $salesRep = $this->request->data('sales_rep');

        $report_type_option = $this->request->isAjax()
            ? $this->request->data('reporttypeoption')
            : $reporttypeoption;
        if (empty($report_type_option)) {
            $report_type_option = 'Direct';
        }

        $conditions = $this->_getGeneralOrderConditions($report_type_option, $this->request->data('selectedCurrencyOption'));
        $conditions['Order.shipping_country'] = $country_name;

        $this->set('report_type_option', $report_type_option);

        $chartData = [];
        $tabledata = [];
        $wholesale_data = [];
        if($report_type_option == 'Wholesale' || $report_type_option == 'All') {
            $orderGraphDealer = $this->Order->getSalesByCountryForWholesale($userId, $date_range_start, $date_range_end, $stores, $salesRep, $conditions);
            $chartData = array_map(function($values) {
                return array(
                    'state' => '',
                    'cat_label' => $values['Order']['billing_state'],
                    'sales' => number_format($values[0]['sales'], 2, '.', ''),
                );
            }, $orderGraphDealer);
            $tabledata = array_map(function($values) {
                return array(
                    $values['Order']['billing_state'],
                    $values[0]['no_of_order'],
                    $this->_formatMoney($values[0]['gross_sales']),
                    $this->_formatMoney($values[0]['total_discounts']),
                    $this->_formatMoney($values[0]['net_sales']),
                    $this->_formatMoney($values[0]['total_shipping']),
                    $this->_formatMoney($values[0]['total_taxes']),
                    $this->_formatMoney($values[0]['sales']),
                );
            }, $orderGraphDealer);
            $wholesale_data = $tabledata;
        }

        if($report_type_option != 'Wholesale') {
            $orderGraph = $this->Order->getSalesByCountry($userId, $date_range_start, $date_range_end, $stores, $salesRep, $conditions);
            foreach ($orderGraph as $values) {
                array_push($chartData, [
                    'state' => '',
                    'cat_label' => $values['Order']['billing_state'],
                    'sales' => number_format($values[0]['sales'], 2, '.', ''),
                ]);
            }
            $tabledata = array_map(function($values) {
                return array(
                    $values['Order']['billing_state'],
                    $values[0]['no_of_order'],
                    $this->_formatMoney($values[0]['gross_sales']),
                    $this->_formatMoney($values[0]['total_discounts']),
                    $this->_formatMoney($values[0]['net_sales']),
                    $this->_formatMoney($values[0]['total_shipping']),
                    $this->_formatMoney($values[0]['total_taxes']),
                    $this->_formatMoney($values[0]['sales']),
                );
            }, $orderGraph);
        }

        if($report_type_option == 'All') {
            foreach ($tabledata as $key => $value) {
                $pos = array_search($value[0], array_column($wholesale_data, 0));
                if($pos > -1) {
                    $tabledata[$key][1] = $tabledata[$key][1] + $wholesale_data[$pos][1];
                    foreach (range(2, 7) as $i) {
                        $tabledata[$key][$i] = $this->_formatMoney($this->_unformatMoney($tabledata[$key][$i]) + $this->_unformatMoney($wholesale_data[$pos][$i]));
                    }
                }
            }
            $chartData = array_map(function($value) {
                return array(
                    'state' => '',
                    'cat_label' => $value[0],
                    'sales' => $this->_unformatMoney($value[7]),
                );
            }, $tabledata);
        }

        $chartOptions = array(
            'title' => 'Total Sales',
            'valueField' => 'sales',
            'categoryField' => 'state',
        );
        $tablecolumns = array(
            ['title' => 'Billing State'],
            ['title' => 'Orders'],
            ['title' => 'Gross Sales'],
            ['title' => 'Discounts'],
            ['title' => 'Net Sales'],
            ['title' => 'Shipping'],
            ['title' => 'Tax'],
            ['title' => 'Total Sales'],
        );
        $tablecolumndefs = array(
            'numClass' => 'numericCol',
            'cols' => range(1, 7),
            'cols_datatype' => ['int'] + array_fill(1, 6, 'float'),
            'hyperlink_cols' => [0],
            'hyperlink_url' => ["reports/view/salesbylocation?r={$report_type_option}&state="],
        );

        return $this->_salesReportResponse($chartOptions, $chartData, $tabledata, $tablecolumns, $tablecolumndefs);
    }

    public function _salesByStateResults($state_name, $reporttypeoption)
    {
        $userId = $this->Auth->user('id');
        list($date_range_start, $date_range_end) = $this->_getDateRangeStartEnd($this->request);
        $stores = $this->request->data('store_id');
        $salesRep = $this->request->data('sales_rep');

        $report_type_option = $this->request->isAjax()
            ? $this->request->data('reporttypeoption')
            : $reporttypeoption;
        if (empty($report_type_option)) {
            $report_type_option = 'Direct';
        }

        $conditions = $this->_getGeneralOrderConditions($report_type_option, $this->request->data('selectedCurrencyOption'));
        $conditions['Order.shipping_state'] = $state_name;

        $this->set('report_type_option', $report_type_option);

        $chartData = [];
        $tabledata = [];
        $wholesale_data = [];
        if($report_type_option == 'Wholesale' || $report_type_option == 'All') {
            $orderGraphDealer = $this->Order->getSalesByStateForWholesale($userId, $date_range_start, $date_range_end, $stores, $salesRep, $conditions);
            $chartData = array_map(function($values) {
                return array(
                    'city' => '',
                    'cat_label' => $values['Order']['billing_city'],
                    'sales' => number_format($values[0]['sales'], 2, '.', ''),
                );
            }, $orderGraphDealer);
            $tabledata = array_map(function($values) {
                return array(
                    $values['Order']['billing_city'],
                    $values[0]['no_of_order'],
                    $this->_formatMoney($values[0]['gross_sales']),
                    $this->_formatMoney($values[0]['total_discounts']),
                    $this->_formatMoney($values[0]['net_sales']),
                    $this->_formatMoney($values[0]['total_shipping']),
                    $this->_formatMoney($values[0]['total_taxes']),
                    $this->_formatMoney($values[0]['sales']),
                );
            }, $orderGraphDealer);
            $wholesale_data = $tabledata;
        }

        if($report_type_option != 'Wholesale') {
            $orderGraph = $this->Order->getSalesByState($userId, $date_range_start, $date_range_end, $stores, $salesRep, $conditions);
            foreach ($orderGraph as $values) {
                array_push($chartData, [
                    'city' => '',
                    'cat_label' => $values['Order']['billing_city'],
                    'sales' => number_format($values[0]['sales'], 2, '.', ''),
                ]);
            }
            $tabledata = array_map(function($values) {
                return array(
                    $values['Order']['billing_city'],
                    $values[0]['no_of_order'],
                    $this->_formatMoney($values[0]['gross_sales']),
                    $this->_formatMoney($values[0]['total_discounts']),
                    $this->_formatMoney($values[0]['net_sales']),
                    $this->_formatMoney($values[0]['total_shipping']),
                    $this->_formatMoney($values[0]['total_taxes']),
                    $this->_formatMoney($values[0]['sales']),
                );
            }, $orderGraph);
        }

        if($report_type_option == 'All') {
            foreach ($tabledata as $key => $value) {
                $pos = array_search($value[0], array_column($wholesale_data, 0));
                if($pos > -1) {
                    $tabledata[$key][1] = $tabledata[$key][1] + $wholesale_data[$pos][1];
                    foreach (range(2, 7) as $i) {
                        $tabledata[$key][$i] = $this->_formatMoney($this->_unformatMoney($tabledata[$key][$i]) + $this->_unformatMoney($wholesale_data[$pos][$i]));
                    }
                }
            }
            $chartData = array_map(function($value) {
                return array(
                    'city' => '',
                    'cat_label' => $value[0],
                    'sales' => $this->_unformatMoney($value[7]),
                );
            }, $tabledata);
        }

        $chartOptions = array(
            'title' => 'Total Sales',
            'valueField' => 'sales',
            'categoryField' => 'city',
        );
        $tablecolumns = array(
            ['title' => 'Billing City'],
            ['title' => 'Orders'],
            ['title' => 'Gross Sales'],
            ['title' => 'Discounts'],
            ['title' => 'Net Sales'],
            ['title' => 'Shipping'],
            ['title' => 'Tax'],
            ['title' => 'Total Sales'],
        );
        $tablecolumndefs = array(
            'numClass' => 'numericCol',
            'cols' => range(1, 7),
            'cols_datatype' => ['int'] + array_fill(1, 6, 'float'),
            'hyperlink_cols' => [],
            'hyperlink_url' => [],
        );

        return $this->_salesReportResponse($chartOptions, $chartData, $tabledata, $tablecolumns, $tablecolumndefs);
    }

    public function _salesByProductResults()
    {
        $userId = $this->Auth->user('id');
        $userType = $this->Auth->user('user_type');
        list($date_range_start, $date_range_end) = $this->_getDateRangeStartEnd($this->request);
        $stores = $this->request->data('store_id');
        $salesRep = $this->request->data('sales_rep');
        $brandList = $this->request->data('brand_list');

        $report_type_option = !empty($this->request->data['reporttypeoption'])
            ? $this->request->data['reporttypeoption']
            : 'Direct';

        $conditions = $this->_getGeneralOrderConditions($report_type_option, $this->request->data('selectedCurrencyOption'));

        $chartData = [];
        $tabledata = [];
        $wholesale_data = [];
        if ($report_type_option == 'Wholesale' || $report_type_option == 'All') {
            $orderGraphDealer = $this->Order->getSalesByProductTitleForWholesale($userId, $date_range_start, $date_range_end, $stores, $salesRep, $brandList, $conditions, $userType);
            $chartData = array_map(function($values) {
                return array(
                    'product' => '',
                    'cat_label' => $values[0]['source_product_title'],
                    'sales' => number_format($values[0]['gross_sales'], 2, '.', ''),
                );
            }, $orderGraphDealer);
            $tabledata = array_map(function($values) {
                return array(
                    $this->_formatProductTypeField($values['Product']['product_type']),
                    $values[0]['source_product_title'],
                    $values[0]['no_of_order'],
                    $this->_formatMoney($values[0]['gross_sales']),
                    $this->_formatMoney($values[0]['total_discounts']),
                    $this->_formatMoney($values[0]['net_sales']),
                    $this->_formatMoney($values[0]['total_taxes']),
                    $this->_formatMoney($values[0]['sales']),
                );
            }, $orderGraphDealer);
            $wholesale_data = $tabledata;
        }

        if ($report_type_option != 'Wholesale') {
            $orderGraph = $this->Order->getSalesByProductTitle($userId, $date_range_start, $date_range_end, $stores, $salesRep, $brandList, $conditions, $userType);
            foreach ($orderGraph as $values) {
                array_push($chartData, array(
                    'product' => '',
                    'cat_label' => $values[0]['source_product_title'],
                    'sales' => number_format($values[0]['gross_sales'], 2, '.', ''),
                ));
            }
            $tabledata = array_map(function($values) {
                return array(
                    $this->_formatProductTypeField($values['Product']['product_type']),
                    $values[0]['source_product_title'],
                    $values[0]['no_of_order'],
                    $this->_formatMoney($values[0]['gross_sales']),
                    $this->_formatMoney($values[0]['total_discounts']),
                    $this->_formatMoney($values[0]['net_sales']),
                    $this->_formatMoney($values[0]['total_taxes']),
                    $this->_formatMoney($values[0]['sales']),
                );
            }, $orderGraph);
        }
        if ($report_type_option == 'All') {
            $tabledata = array_map(function($value) use ($wholesale_data) {
                $pos = array_search($value[0], array_column($wholesale_data, 0));
                if ($pos > -1) {
                    $wholesale_value = $wholesale_data[$pos];
                    $value[2] = $value[2] + $wholesale_value[2];
                    foreach (range(3, 7) as $i) {
                        $value[$i] = $this->_formatMoney($this->_unformatMoney($value[$i]) + $this->_unformatMoney($wholesale_value[$i]));
                    }
                }
                return $value;
            }, $tabledata);
            $chartData = array_map(function($value) {
                return array(
                    'product' => '',
                    'cat_label' => $value[1],
                    'sales' => $this->_unformatMoney($value[3]),
                );
            }, $tabledata);
        }

        if ($report_type_option == 'Wholesale' || $report_type_option == 'All') {
            $chartData = $this->_chartDescSort($chartData);
        }

        $chartOptions = array(
            'title' => __('Total Sales'),
            'valueField' => 'sales',
            'categoryField' => 'product',
        );
        $tablecolumns = array(
            ['title' => __('Category')],
            ['title' => __('Product')],
            ['title' => __('Net Quantity')],
            ['title' => __('Gross Sales')],
            ['title' => __('Discounts')],
            ['title' => __('Net Sales')],
            ['title' => __('Tax')],
            ['title' => __('Total Sales')],
        );
        $tablecolumndefs = array(
            'numClass' => 'numericCol',
            'cols' => range(2, 7),
            'cols_datatype' => ['int'] + array_fill(1, 5, 'float'),
            'hyperlink_cols' => [],
            'hyperlink_url' => [],
        );

        return $this->_salesReportResponse($chartOptions, $chartData, $tabledata, $tablecolumns, $tablecolumndefs);
    }

    public function _salesByProductSKUResults()
    {
        $userId = $this->Auth->user('id');
        $userType = $this->Auth->user('user_type');
        list($date_range_start, $date_range_end) = $this->_getDateRangeStartEnd($this->request);
        $stores = $this->request->data('store_id');
        $salesRep = $this->request->data('sales_rep');
        $brandList = $this->request->data('brand_list');

        $report_type_option = !empty($this->request->data['reporttypeoption'])
            ? $this->request->data['reporttypeoption']
            : 'Direct';

        $conditions = $this->_getGeneralOrderConditions($report_type_option, $this->request->data('selectedCurrencyOption'));

        $chartData = [];
        $tabledata = [];
        $wholesale_data = [];
        if ($report_type_option == 'Wholesale' || $report_type_option == 'All') {
            $orderGraphDealer = $this->Order->getSalesByProductSkuForWholesale($userId, $date_range_start, $date_range_end, $stores, $salesRep, $brandList, $conditions, $userType);
            $chartData = array_map(function($values) {
                return array(
                    'product' => '',
                    'cat_label' => $values['Product']['product_title'],
                    'sales' => number_format($values[0]['gross_sales'], 2, '.', ''),
                );
            }, $orderGraphDealer);
            $tabledata = array_map(function($values) {
                return array(
                    $this->_formatProductTypeField($values['Product']['product_type']),
                    $values['Product']['product_title'],
                    $values['Product']['product_sku'],
                    $values[0]['no_of_order'],
                    $this->_formatMoney($values[0]['gross_sales']),
                    $this->_formatMoney($values[0]['total_discounts']),
                    $this->_formatMoney($values[0]['net_sales']),
                    $this->_formatMoney($values[0]['total_taxes']),
                    $this->_formatMoney($values[0]['sales']),
                );
            }, $orderGraphDealer);
            $wholesale_data = $tabledata;
        }

        if ($report_type_option != 'Wholesale') {
            $orderGraph = $this->Order->getSalesByProductSKU($userId, $date_range_start, $date_range_end, $stores, $salesRep, $brandList, $conditions, $userType);
            foreach ($orderGraph as $values) {
                array_push($chartData, array(
                    'product' => '',
                    'cat_label' => $values['Product']['product_title'],
                    'sales' => number_format($values[0]['gross_sales'], 2, '.', ''),
                ));
            }
            $tabledata = array_map(function($values) {
                return array(
                    $this->_formatProductTypeField($values['Product']['product_type']),
                    $values['Product']['product_title'],
                    $values['Product']['product_sku'],
                    $values[0]['no_of_order'],
                    $this->_formatMoney($values[0]['gross_sales']),
                    $this->_formatMoney($values[0]['total_discounts']),
                    $this->_formatMoney($values[0]['net_sales']),
                    $this->_formatMoney($values[0]['total_taxes']),
                    $this->_formatMoney($values[0]['sales']),
                );
            }, $orderGraph);
        }

        if ($report_type_option == 'All') {
            $tabledata = array_map(function($value) use ($wholesale_data) {
                $pos = array_search($value[0], array_column($wholesale_data, 0));
                if ($pos > -1) {
                    $wholesale_value = $wholesale_data[$pos];
                    $value[3] = $value[3] + $wholesale_value[3];
                    foreach (range(4, 8) as $i) {
                        $value[$i] = $this->_formatMoney($this->_unformatMoney($value[$i]) + $this->_unformatMoney($wholesale_value[$i]));
                    }
                }
                return $value;
            }, $tabledata);
            $chartData = array_map(function($value) {
                return array(
                    'product' => '',
                    'cat_label' => $value[1],
                    'sales' => $this->_unformatMoney($value[4]),
                );
            }, $tabledata);
        }

        if ($report_type_option == 'Wholesale' || $report_type_option == 'All') {
            $chartData = $this->_chartDescSort($chartData);
        }

        $chartOptions = array(
            'title' => __('Total Sales'),
            'valueField' => 'sales',
            'categoryField' => 'product',
        );
        $tablecolumns = array(
            ['title' => __('Category')],
            ['title' => __('Variant Title')],
            ['title' => __('Variant SKU')],
            ['title' => __('Net Quantity')],
            ['title' => __('Gross Sales')],
            ['title' => __('Discounts')],
            ['title' => __('Net Sales')],
            ['title' => __('Tax')],
            ['title' => __('Total Sales')],
        );
        $tablecolumndefs = array(
            'numClass' => 'numericCol',
            'cols' => range(3, 8),
            'cols_datatype' => ['int'] + array_fill(1, 5, 'float'),
            'hyperlink_cols' => [],
            'hyperlink_url' => [],
        );

        return $this->_salesReportResponse($chartOptions, $chartData, $tabledata, $tablecolumns, $tablecolumndefs);
    }

    public function _salesByOrderTypeResults()
    {
        $userId = $this->Auth->user('id');
        list($date_range_start, $date_range_end) = $this->_getDateRangeStartEnd($this->request);
        $stores = $this->request->data('store_id');
        $salesRep = $this->request->data('sales_rep');

        $conditions = $this->_getCurrencyOrderConditions($this->request->data('selectedCurrencyOption'));
        $orderGraph = $this->Order->getSalesByOrderType($userId, $date_range_start, $date_range_end, $stores, $salesRep, $conditions);

        $tablecolumns = array(
            ['title' => 'Channel Name'],
            ['title' => 'Order Count'],
            ['title' => 'Gross Sales'],
            ['title' => 'Discounts'],
            ['title' => 'Net Sales'],
            ['title' => 'Shipping'],
            ['title' => 'Taxes'],
            ['title' => 'Total Sales'],
        );
        $tablecolumndefs = array(
            'numClass' => 'numericCol',
            'cols' => range(1, 7),
            'cols_datatype' => ['int'] + array_fill(1, 6, 'float'),
            'hyperlink_cols' => [],
            'hyperlink_url' => [],
        );
        $tabledata = array_map(function($values) {
            return array(
                $values[0]['fulfill_type'],
                $values[0]['no_of_order'],
                $this->_formatMoney($values[0]['gross_sales']),
                $this->_formatMoney($values[0]['total_discounts']),
                $this->_formatMoney($values[0]['net_sales']),
                $this->_formatMoney($values[0]['total_shipping']),
                $this->_formatMoney($values[0]['total_taxes']),
                $this->_formatMoney($values[0]['sales']),
            );
        }, $orderGraph);

        $chartOptions = array(
            'title' => 'Total Sales',
            'valueField' => 'sales',
            'categoryField' => 'ordertype',
        );
        $chartData = array_map(function($values) {
            return array(
                'ordertype' => $values[0]['fulfill_type'],
                'cat_label' => $values[0]['fulfill_type'],
                'sales' => number_format($values[0]['sales'], 2, '.', ''),
            );
        }, $orderGraph);

        return $this->_salesReportResponse($chartOptions, $chartData, $tabledata, $tablecolumns, $tablecolumndefs);
    }

    public function _salesByRetailerMonthlyResults()
    {
        $userId = $this->Auth->user('id');
        list($date_range_start, $date_range_end) = $this->_getDateRangeStartEnd($this->request);
        $salesRep = $this->request->data('sales_rep');

        $conditions = $this->_getGeneralOrderConditions('All', $this->request->data('selectedCurrencyOption'));

        $orderGraph = $this->Order->getSalesByMonthRetailer($userId, $date_range_start, $date_range_end, $salesRep, $conditions);

        $tablecolumns = array(
            ['title' => 'Month'],
            ['title' => 'Retailer Count'],
            ['title' => 'Gross Sales'],
            ['title' => 'Discounts'],
            ['title' => 'Net Sales'],
            ['title' => 'Shipping'],
            ['title' => 'Tax'],
            ['title' => 'Total Sales'],
        );
        $tablecolumndefs = array(
            'numClass' => 'numericCol',
            'cols' => range(1, 7),
            'cols_datatype' => ['int'] + array_fill(1, 6, 'float'),
            'hyperlink_cols' => [],
            'hyperlink_url' => [],
        );
        $tabledata = array_map(function($values) {
            return array(
                $values[0]['month_n_y'],
                $values[0]['month_c_y'],
                $values[0]['no_of_order'],
                $this->_formatMoney($values[0]['gross_sales']),
                $this->_formatMoney($values[0]['total_discounts']),
                $this->_formatMoney($values[0]['net_sales']),
                $this->_formatMoney($values[0]['total_shipping']),
                $this->_formatMoney($values[0]['total_taxes']),
                $this->_formatMoney($values[0]['sales']),
            );
        }, $orderGraph);

        $monthInRange = $this->Order->getDateRange($date_range_start, $date_range_end);
        $tabledata = $this->_fillEmptyTableDates($tabledata, $monthInRange);

        $chartOptions = array(
            'title' => 'Retailer Count',
            'valueField' => 'count',
            'categoryField' => 'month',
        );
        $chartData = array_map(function($value) {
            return array(
                'month' => substr((explode(' ', $value[0])[0]), 0, 3),
                'cat_label' => $value[0],
                'count' => $value[1],
            );
        }, $tabledata);

        return $this->_salesReportResponse($chartOptions, $chartData, $tabledata, $tablecolumns, $tablecolumndefs);
    }

    public function _salesByRetailerResults()
    {
        $userId = $this->Auth->user('id');
        $userType = $this->Auth->user('user_type');
        list($date_range_start, $date_range_end) = $this->_getDateRangeStartEnd($this->request);
        $salesRep = $this->request->data('sales_rep');
        $brandList = $this->request->data('brand_list');

        $report_type_option = !empty($this->request->data['reporttypeoption'])
            ? $this->request->data['reporttypeoption']
            : 'Retail';

        $conditions = $this->_getGeneralOrderConditions($report_type_option, $this->request->data('selectedCurrencyOption'));

        $tabledata = [];
        if ($report_type_option == 'Wholesale') {
            $orderGraph = $this->Order->getSalesByRetailerForWholesale($userId, $date_range_start, $date_range_end, $salesRep, $brandList, $conditions, $userType);
        } else {
            $orderGraph = $this->Order->getSalesByRetailer($userId, $date_range_start, $date_range_end, $salesRep, $brandList, $conditions, $userType);
        }

        $tablecolumns = [
            ['title' => 'Retailer'],
            ['title' => 'Orders'],
            ['title' => 'Gross Sales'],
            ['title' => 'Discounts'],
            ['title' => 'Net Sales'],
            ['title' => 'Shipping'],
            ['title' => 'Tax'],
            ['title' => 'Total Sales'],
        ];
        $tablecolumndefs = [
            'numClass' => 'numericCol',
            'cols' => range(1, 7),
            'cols_datatype' => ['int'] + array_fill(1, 6, 'float'),
            'hyperlink_cols' => [],
            'hyperlink_url' => [],
        ];
        $tabledata = array_map(function($values) {
            return [
                $values['User']['retailer_name'],
                $values[0]['no_of_order'],
                $this->_formatMoney($values[0]['gross_sales']),
                $this->_formatMoney($values[0]['total_discounts']),
                $this->_formatMoney($values[0]['net_sales']),
                $this->_formatMoney($values[0]['total_shipping']),
                $this->_formatMoney($values[0]['total_taxes']),
                $this->_formatMoney($values[0]['sales']),
            ];
        }, $orderGraph);

        $chartOptions = [
            'title' => 'Total Sales',
            'valueField' => 'sales',
            'categoryField' => 'retailers',
        ];
        $chartData = array_map(function($values) {
            return [
                'retailers' => '',
                'cat_label' => $values['User']['retailer_name'],
                'sales' => number_format($values[0]['sales'], 2, '.', ''),
            ];
        }, $orderGraph);

        return $this->_salesReportResponse($chartOptions, $chartData, $tabledata, $tablecolumns, $tablecolumndefs);
    }

    /**
     * @param CakeRequest $request
     * @return array
     */
    protected function _getDateRangeStartEnd(CakeRequest $request)
    {
        if ($request->isAjax()) {
            $date_range_start = $request->data['daterange']['start'];
            $date_range_end = $request->data['daterange']['end'];
        } else {
            $date_range_start = date('Y-m-d', strtotime('-1 years'));
            $date_range_end = date('Y-m-d');
        }
        return array($date_range_start, $date_range_end);
    }

    /**
     * @param $chartOptions
     * @param $chartData
     * @param $tabledata
     * @param $tablecolumns
     * @param $tablecolumndefs
     * @return array
     */
    protected function _salesReportResponse($chartOptions, $chartData, $tabledata, $tablecolumns, $tablecolumndefs)
    {
        $this->set('ordersChartOptions', $chartOptions);
        $this->set('ordersChartData', json_encode($chartData));
        $this->set('ordersTableData', json_encode($tabledata));
        $this->set('ordersTableColumns', json_encode($tablecolumns));
        $this->set('ordersTableColumnDefs', json_encode($tablecolumndefs));

        if ($this->request->isAjax()) {
            return array(
                'json_response' => json_encode(array(
                    'success' => 'success',
                    'ordersChartOptions' => $chartOptions,
                    'ordersChartData' => $chartData,
                    'ordersTableData' => $tabledata,
                    'ordersTableColumns' => $tablecolumns,
                    'ordersTableColumnDefs' => $tablecolumndefs,
                    'data' => $this->request->data['daterange'],
                    'report_type_option' => $this->request->data['reporttypeoption'],
                )),
            );
        }

        return array();
    }

    protected function _formatProductTypeField($product_type, $limit = 3)
    {
        $categoryNames = json_decode($product_type);
        if (!is_array($categoryNames)) {
            $categoryNames = array($product_type);
        }
        return $this->_formatArrayToCommaList($categoryNames, $limit);
    }

    protected function _formatArrayToCommaList(array $array, $limit = 3)
    {
        $listEnd = array();
        if (count($array) > $limit) {
            $listEnd = array('...');
        }
        array_splice($array, $limit, count($array), $listEnd);
        return implode(', ', $array);
    }

    protected function _fillEmptyTableDates($tabledata, $monthInRange) {
        $filledMonths = array_column($tabledata, 0);
        $emptyMonths = array_diff($monthInRange[0], $filledMonths);

        foreach ($emptyMonths as $i => $value) {
            array_push($tabledata, [$value, $monthInRange[1][$i], 0, '0.00', '0.00', '0.00', '0.00', '0.00', '0.00']);
        }
        usort($tabledata, function($a, $b) {
            return $a[0] - $b[0];
        });

        //Repopulate the Data
        return array_map(function($value) {
            array_shift($value);
            return $value;
        }, $tabledata);
    }

    protected function _chartDescSort($data)
    {
        usort($data, function ($item1, $item2) {
            if ($item1['sales'] == $item2['sales']) return 0;
            return ($item1['sales'] < $item2['sales']) ? 1 : -1;
        });
        return $data; 
    }

    /**
     * @param float $number
     * @return string
     */
    protected function _formatMoney($number)
    {
        return CakeNumber::currency(number_format($number, 2, '.', ''), '');
    }

    protected function _unformatMoney($data) {
        return preg_replace('/([^0-9\\.])/i', '', $data);
    }

    protected function _getReportTypeOrderConditions($report_type_option = 'Direct')
    {
        if (empty($report_type_option)) {
            $report_type_option = 'Direct';
        }

        $directConditions = [
            'Order.order_type' => [Order::TYPE_SELL_DIRECT, Order::TYPE_SHIPEARLY, ''],
        ];
        $retailConditions = [
            'Order.order_type' => [Order::TYPE_IN_STORE_PICKUP, Order::TYPE_SHIP_FROM_STORE, Order::TYPE_LOCAL_DELIVERY],
            'Order.order_status !=' => [
                Order::STATUS_NEED_TO_CONFIRM,
                Order::STATUS_PURCHASE_ORDER,
            ],
        ];

        switch ($report_type_option) {
        case 'Direct':
            return $directConditions;
        case 'Retail':
            return $retailConditions;
        case 'Combined':
            return array_merge_recursive($directConditions, $retailConditions);
        case 'Wholesale':
            return array_merge_recursive($retailConditions, array(
                'Order.order_type' => [Order::TYPE_WHOLESALE],
                'COALESCE(`Order`.`dealer_qty_ordered`, "") !=' => "",
            ));
        case 'All':
            // Combined with Wholesale after fetching
            return array_merge_recursive($directConditions, $retailConditions);
        default:
            return null;
        }
    }

    protected function _getCurrencyOrderConditions($currency)
    {
        return ['Order.currency_code' => $currency];
    }

    protected function _getGeneralOrderConditions($report_type_option, $currency)
    {
        return $this->_getCurrencyOrderConditions($currency) + $this->_getReportTypeOrderConditions($report_type_option);
    }


    public function prospects()
    {
        $userId = (int)$this->Auth->user('id');
        $userType = (string)$this->Auth->user('user_type');

        if (!in_array($userType, ['Manufacturer', 'SalesRep'])) {
            $this->redirect($this->referer());
        }

        $this->request->query = array_filter($this->request->query);
        $query = $this->request->query + array(
            'show' => 10,
            'page' => 1,
            'sort' => 'Viewslog.created',
            'order' => 'DESC',
            'brand' => null,
            'store' => null,
        );

        $brandId = ($userType === 'Manufacturer') ? $userId : $query['brand'];
        $storeId = $query['store'];

        $sort = $query['sort'];
        $order = $query['order'];

        $pageNum = $query['page'];
        $rowsPerPage = $query['show'];
        $totalRows = $this->Viewslog->countProspects($userId, $brandId, $storeId, $userType);

        $paging = paging($pageNum, $rowsPerPage, $totalRows);

        $this->set(compact('brandId', 'storeId', 'sort', 'order', 'totalRows', 'paging'));

        $this->set('title_for_layout', 'Prospects Report');
        $this->set('brandsList', $this->Viewslog->listProspectBrands($userId, $userType));
        $this->set('storesList', $this->Viewslog->listProspectRetailers($userId, $userType));
        $this->set('viewslogs', $this->Viewslog->reportProspects(
            $userId,
            trim($sort . ' ' . $order),
            $rowsPerPage,
            $paging['offset'],
            $brandId,
            $storeId,
            $userType
        ));

        if ($this->request->is('ajax')) {
            $this->render('/Elements/Reports/ajax_prospects');
        }
    }

    public function splitpayments()
    {
        $userType = $this->Auth->user('user_type');
        if (!in_array($userType, [User::TYPE_MANUFACTURER, User::TYPE_SALES_REP], true)) {
            // Redirecting to referer or /reports may cause a loop
            return $this->redirect('/');
        }
        $this->_splitpayments($userType, $this->Auth->user('id'));
    }

    public function admin_splitpayments($userType = null, $userId = null)
    {
        if (!in_array($userType, [null, User::TYPE_MANUFACTURER, User::TYPE_SALES_REP], true)) {
            return $this->redirect($this->referer());
        }
        if ($this->request->is('post') && isset($this->request->data['Report'])) {
            $this->DealerOrder->saveSplitpaymentReportPayments($this->request->data['Report']);
        }
        $this->_splitpayments($userType, $userId);
        $this->render('splitpayments', 'admin');
    }

    private function _splitpayments($userType, $userId)
    {
        $reportCount = $this->DealerOrder->splitpaymentReportSummariesCount($userType, $userId);
        $this->set('title_for_layout', 'Dealer Payments');
        $this->set('userType', $userType);
        $this->set('userId', $userId);
        $this->set('rowsPerPage', 10);
        $this->set('totalRows', $reportCount);
    }

    public function ajax_splitpayments()
    {
        $userType = $this->Auth->user('user_type');
        if (!in_array($userType, [User::TYPE_MANUFACTURER, User::TYPE_SALES_REP], true)) {
            return $this->redirect($this->referer());
        }
        $this->_ajax_splitpayments($userType, $this->Auth->user('id'));
    }

    public function admin_ajax_splitpayments($userType = null, $brandId = null)
    {
        if (!in_array($userType, [null, User::TYPE_MANUFACTURER, User::TYPE_SALES_REP], true)) {
            return $this->redirect($this->referer());
        }
        $this->_ajax_splitpayments($userType, $brandId);
        $this->render('ajax_splitpayments');
    }

    private function _ajax_splitpayments($userType, $brandId)
    {
        $pageNum = !empty($this->request->query['pageNo']) ? $this->request->query['pageNo'] : 1;
        $rowsPerPage = !empty($this->request->query['noRecords']) ? $this->request->query['noRecords'] : 10;
        $totalRows = !empty($this->request->query['totalRows']) ? $this->request->query['totalRows'] : $this->DealerOrder->splitpaymentReportSummariesCount($userType, $brandId);
        $paging = paging($pageNum, $rowsPerPage, $totalRows);

        $sortField = !empty($this->request->query['sortField']) ? $this->request->query['sortField'] : 'DealerOrder.company_name';
        $sortOrder = !empty($this->request->query['sortOrder']) ? $this->request->query['sortOrder'] : 'ASC';
        $orderBy = array_merge(['DealerOrder.period_end' => 'DESC'], [$sortField => $sortOrder]);

        $reportSummaries = $this->DealerOrder->splitpaymentReportSummaries($userType, $brandId, $orderBy, $paging['rowsPerPage'], $paging['offset']);

        $this->set('totalRows', $totalRows);
        $this->set('paging', $paging);
        $this->set('reportSummaries', $reportSummaries);
    }

    public function splitpayment($days = 30, $endDate = null, $currencyCode = null)
    {
        $userId = $this->Auth->user('id');
        $this->_splitpayment($userId, $days, $endDate, $currencyCode);
    }

    public function admin_splitpayment($userId, $days = 30, $endDate = null, $currencyCode = null)
    {
        $this->_splitpayment($userId, $days, $endDate, $currencyCode);
        $this->render('splitpayment', 'admin');
    }

    public function splitpayment_pdf($days = 30, $endDate = null, $currencyCode = null)
    {
        $userId = $this->Auth->user('id');
        $this->_splitpayment($userId, $days, $endDate, $currencyCode);

        return $this->_renderPdf('splitpayment_pdf');
    }

    public function admin_splitpayment_pdf($userId, $days = 30, $endDate = null, $currencyCode = null)
    {
        $this->_splitpayment($userId, $days, $endDate, $currencyCode);

        return $this->_renderPdf('splitpayment_pdf');
    }

    public function splitpayment_excel($days = 30, $endDate = null, $currencyCode = null)
    {
        $userId = $this->Auth->user('id');
        $this->_splitpayment($userId, $days, $endDate, $currencyCode);
        $this->layout = '';
    }

    public function admin_splitpayment_excel($userId, $days = 30, $endDate = null, $currencyCode = null)
    {
        $this->_splitpayment($userId, $days, $endDate, $currencyCode);
        $this->render('splitpayment_excel', '');
    }

    private function _splitpayment($userId, $days, $endDate, $currencyCode)
    {
        $this->User->addAssociations(['belongsTo' => ['Country', 'State']]);
        $brandData = $this->User->record($userId, [
            'contain' => [
                'Country' => ['fields' => ['country_code']],
                'State' => ['fields' => ['state_code']],
            ],
            'fields' => ['id', 'user_type', 'company_name', 'currency_code', 'address1', 'address2', 'city', 'zipcode'],
        ]);
        $this->User->unbindModel(['belongsTo' => ['Country', 'State']], false);

        if (!in_array($brandData['User']['user_type'], [User::TYPE_MANUFACTURER, User::TYPE_SALES_REP], true)) {
            return $this->redirect($this->referer());
        }

        if (!$currencyCode) {
            // Avoid an infinite loop by falling back to a placeholder
            $brandCurrency = $brandData['User']['currency_code'] ?: ':currency_code';
            $this->request->param('pass.2', $brandCurrency);
            $redirectUrl = Router::reverse($this->request);

            deprecationWarning("2.12 The URL '{$this->request->here()}' is deprecated. Use '{$redirectUrl}' instead.");

            return $this->redirect($redirectUrl, 301);
        }

        $inlineAddress = implode(', ', array_filter([
            implode(' ', array_filter([
                $brandData['User']['address1'],
                $brandData['User']['address2'],
                $brandData['User']['city'],
            ])),
            strtoupper($brandData['State']['state_code']),
            $brandData['User']['zipcode'],
            strtoupper($brandData['Country']['country_code']),
        ]));

        list($startDate, $endDate) = $this->_computeDateRange($days, $endDate);
        list($ordersData, $reportData) = $this->DealerOrder->splitpaymentReport($userId, $currencyCode, $startDate, $endDate);

        /** @var ShipearlyHelper $Helper */
        $Helper = (new View($this))->loadHelper('Shipearly');

        $tableColumns = array(
            array(
                'name' => __('Order ID'),
                'valueCallback' => function($row) {
                    return $row['Order']['orderID'];
                },
                'linkOptionsCallback' => function($row) use ($Helper) {
                    return $Helper->orderLink($row['Order']['id'], $row['Order']['orderID'], false, 'dealerorderTable');
                },
            ),
            array(
                'name' => __('Internal Order #'),
                'valueCallback' => function($row) {
                    if (!empty($row['Order']['external_invoice_id'])) {
                        return $row['Order']['external_invoice_id'];
                    } else {
                        return $this->Order->getSourceOrderName($row);
                    }
                }
            ),
            array(
                'name' => __('Order Date'),
                'valueCallback' => function($row) {
                    return format_datetime($row['Order']['created_at'], DATE_FORMAT);
                }
            ),
            array(
                'name' => __('Ship Date'),
                'valueCallback' => function($row) {
                    return format_datetime($row['DealerOrder']['shipment_date'], DATE_FORMAT);
                }
            ),
            array(
                'name' => __('Retailer'),
                'valueCallback' => function($row) {
                    return $row['Retailer']['company_name'];
                }
            ),
            array(
                'name' => __('Account ID'),
                'valueCallback' => function($row) {
                    return $row['ManufacturerRetailer']['external_retailer_account'];
                }
            ),
            array(
                'name' => __('Retail'), 'is_money' => true,
                'valueCallback' => function($row) {
                    return $row['Order']['total_price'];
                }
            ),
            array(
                'name' => __('Wholesale'), 'is_money' => true,
                'valueCallback' => function($row) {
                    return $row['DealerOrder']['product_total_price'];
                }
            ),
            array(
                'name' => __('Shipping'), 'is_money' => true,
                'valueCallback' => function($row) {
                    return $row['DealerOrder']['shipping_amount'];
                }
            ),
            array(
                'name' => __('B2B Taxes'), 'is_money' => true,
                'valueCallback' => function($row) {
                    return $row['DealerOrder']['total_tax'];
                }
            ),
            array(
                'name' => __('Total'), 'is_money' => true,
                'valueCallback' => function($row) {
                    return $row['DealerOrder']['total_price'];
                }
            ),
            array(
                'name' => __('Paid'), 'is_money' => true,
                'valueCallback' => function($row) {
                    return $row['DealerOrder']['wholesale_charge_amount'];
                }
            ),
            array(
                'name' => __('Refunded'), 'is_money' => true,
                'valueCallback' => function($row) {
                    return $row['DealerOrder']['total_refund_amount'];
                }
            ),
            array(
                'name' => __('Invoice'), 'is_money' => true,
                'valueCallback' => function($row) {
                    return $row['DealerOrder']['invoice_price'];
                }
            ),
        );
        $tableColumns = $this->_mergeTableColumnDefaults($tableColumns);

        $tableFooters = array(
            'Total' => $reportData['subtotal'],
            'Processing Fee' => $reportData['fees'],
            'Payment Due' => $reportData['total']
        );

        $filename = $brandData['User']['company_name'] . ' Split Payment ' . format_datetime($reportData['period_end'], 'Y-m-d');

        $this->set('companyName', $brandData['User']['company_name']);
        $this->set('inlineAddress', $inlineAddress);
        $this->set('currencyCode', $currencyCode);
        $this->set('reportData', $reportData);
        $this->set('tableColumns', $tableColumns);
        $this->set('tableRows', $ordersData);
        $this->set('tableFooters', $tableFooters);
        $this->set('filename', $filename);
    }

    private function _renderPdf(?string $view = null): CakeResponse
    {
        $headersSentBeforeResponse = headers_sent();

        $response = $this->render($view, 'pdf');

        if (!$headersSentBeforeResponse && headers_sent()) {
            Configure::write(Shim::MONITOR_HEADERS, false);
        }

        return $response;
    }

    public function dealerpayouts($endDate = '')
    {
        list($brandId, $associateId) = $this->_getCommissionsUserIdFromAuth();

        if ($this->request->is('post') && isset($this->request->data['Report'])) {
            $this->Order->saveCommissionReportPayments($this->request->data['Report']);
        }
        $this->_setupCommissionsView($endDate, $brandId, $associateId);
    }

    public function admin_dealerpayouts($endDate = '')
    {
        if ($this->request->is('post') && isset($this->request->data['Report'])) {
            $this->Order->saveCommissionReportPayments($this->request->data['Report']);
        }
        $this->_setupCommissionsView($endDate);

        $this->render('dealerpayouts', 'admin');
    }

    protected function _setupCommissionsView($endDate = '', $brandId = null, $associateId = null)
    {
        $titleDate = ($endDate) ? date(DATE_FORMAT, strtotime($endDate)) : '';
        $this->set('title_for_layout', 'Dealer Payouts ' . $titleDate);
        $this->set('endDate', $endDate);
        $this->set('brandId', $brandId);
        $this->set('associateId', $associateId);
        $this->set('rowsPerPage', 10);
        $this->set('totalRows', $this->Order->commissionReportSummariesCount($endDate, $brandId, $associateId));
    }

    public function ajax_commissions($endDate = '')
    {
        list($brandId, $associateId) = $this->_getCommissionsUserIdFromAuth();
        $this->admin_ajax_commissions($endDate, $brandId, $associateId);
    }

    public function admin_ajax_commissions($endDate = '', $brandId = null, $associateId = null)
    {
        if (!$this->request->is('ajax')) {
            return new CakeResponse(array('body' => 'Not an ajax request', 'status' => 200));
        }
        $pageNum = !empty($this->request->query['pageNo']) ? $this->request->query['pageNo'] : 1;
        $rowsPerPage = !empty($this->request->query['noRecords']) ? $this->request->query['noRecords'] : 10;
        $totalRows = isset($this->request->query['totalRows'])
            ? $this->request->query['totalRows']
            : $this->Order->commissionReportSummariesCount($endDate, $brandId, $associateId);
        $paging = paging($pageNum, $rowsPerPage, $totalRows);

        $sortField = !empty($this->request->query['sortField']) ? $this->request->query['sortField'] : 'Order.period_end';
        $sortOrder = !empty($this->request->query['sortOrder']) ? $this->request->query['sortOrder'] : 'DESC';
        $orderBy = array($sortField . ' ' . $sortOrder);

        $reportSummaries = $this->Order->commissionReportSummaries(
            $endDate,
            $brandId,
            $associateId,
            $orderBy,
            $paging['rowsPerPage'],
            $paging['offset']
        );

        $this->set('totalRows', $totalRows);
        $this->set('paging', $paging);
        $this->set('reportSummaries', $reportSummaries);

        $view = empty($endDate) ? 'ajax_commissions' : 'ajax_commission_dates';
        $this->render($view);
    }

    public function export_commissions($endDate = '')
    {
        $this->autoRender = false;

        list($brandId, $associateId) = $this->_getCommissionsUserIdFromAuth();

        $sortField = !empty($this->request->query['sortField']) ? $this->request->query['sortField'] : 'Order.period_end';
        $sortOrder = !empty($this->request->query['sortOrder']) ? $this->request->query['sortOrder'] : 'DESC';
        $orderBy = array($sortField . ' ' . $sortOrder);

        $reportSummaries = $this->Order->commissionReportSummaries($endDate, $brandId, $associateId, $orderBy);

        if (!empty($reportSummaries)) {
            $this->PhpExcel->createWorksheet();
            $this->PhpExcel->setDefaultFont('Calibri', 12);

            $tableHeader = array();
            if ($brandId) {
                array_push($tableHeader,
                    array('label' => __('Associate'), 'filter' => true),
                    array('label' => __('Email'), 'filter' => true),
                    array('label' => __('Retailer'), 'filter' => true)
                );
            }
            if ($associateId) {
                array_push($tableHeader,
                    array('label' => __('Brand'), 'filter' => true)
                );
            }
            array_push($tableHeader,
                array('label' => __('Payment Method'), 'filter' => true),
                array('label' => __('Reference Number'), 'filter' => true),
                array('label' => __('# of Transactions')),
                array('label' => __('Payment Due'))
            );
            $this->PhpExcel->addTableHeader($tableHeader, array('name' => 'Cambria', 'bold' => true));

            foreach ($reportSummaries as $report) {
                $rowData = array();
                if ($brandId) {
                    array_push($rowData,
                        $report['StoreAssociate']['company_name'],
                        $report['Order']['report_email'],
                        $report['Retailer']['company_name']
                    );
                }
                if ($associateId) {
                    array_push($rowData,
                        $report['Brand']['company_name']
                    );
                }
                array_push($rowData,
                    $report['Order']['report_payment_method'],
                    $report['Order']['report_payment_reference_id'],
                    $report['Order']['order_count'],
                    number_format($report['Order']['report_total'], 2, '.', '')
                );
                $this->PhpExcel->addTableRow($rowData);
            }

            $this->PhpExcel->addTableFooter();
            $filename = "{$this->shipearly_user['User']['company_name']} Sales Commission Reports {$endDate}.xlsx";
            $this->PhpExcel->render($filename);
        }
    }

    public function commission($userId, $days = 30, $endDate = null)
    {
        list($brandId, $associateId) = $this->_getCommissionsUserIdFromAuth($userId);
        $this->_commission($brandId, $associateId, $days, $endDate);
    }

    public function admin_commission($brandId, $payeeId, $days = 30, $endDate = null)
    {
        $this->_commission($brandId, $payeeId, $days, $endDate);
        $this->render('commission', 'admin');
    }

    public function commission_pdf($userId, $days = 30, $endDate = null)
    {
        list($brandId, $associateId) = $this->_getCommissionsUserIdFromAuth($userId);
        $this->_commission($brandId, $associateId, $days, $endDate);

        return $this->_renderPdf('commission_pdf');
    }

    public function admin_commission_pdf($brandId, $associate_id, $days = 30, $endDate = null)
    {
        $this->_commission($brandId, $associate_id, $days, $endDate);

        return $this->_renderPdf('commission_pdf');
    }

    public function commission_excel($userId, $days = 30, $endDate = null)
    {
        list($brandId, $associateId) = $this->_getCommissionsUserIdFromAuth($userId);
        $this->_commission($brandId, $associateId, $days, $endDate);
        $this->layout = '';
    }

    public function admin_commission_excel($brandId, $associate_id, $days = 30, $endDate = null)
    {
        $this->_commission($brandId, $associate_id, $days, $endDate);
        $this->render('commission_excel', '');
    }

    private function _commission($brandId, $payeeId, $days, $endDate)
    {
        $this->User->addAssociations(['belongsTo' => ['Country', 'State']]);
        $payeeData = $this->User->record($payeeId, [
            'contain' => [
                'Country' => ['fields' => ['country_code']],
                'State' => ['fields' => ['state_code']],
            ],
            'fields' => ['id', 'user_type', 'company_name', 'address1', 'address2', 'city', 'zipcode'],
        ]);
        $this->User->unbindModel(['belongsTo' => ['Country', 'State']], false);

        if (!in_array($payeeData['User']['user_type'], [User::TYPE_STAFF, User::TYPE_RETAILER], true)) {
            $this->redirect($this->referer());
        }

        $inlineAddress = implode(', ', array_filter([
            implode(' ', array_filter([
                $payeeData['User']['address1'],
                $payeeData['User']['address2'],
                $payeeData['User']['city'],
            ])),
            strtoupper($payeeData['State']['state_code']),
            $payeeData['User']['zipcode'],
            strtoupper($payeeData['Country']['country_code']),
        ]));

        $brandData = $this->User->record($brandId, [
            'fields' => ['id', 'user_type', 'company_name', 'currency_code'],
        ]);
        if ($brandData['User']['user_type'] !== User::TYPE_MANUFACTURER) {
            $this->redirect($this->referer());
        }

        list($startDate, $endDate) = $this->_computeDateRange($days, $endDate);
        list($ordersData, $reportData) = $this->Order->commissionReport($brandId, $payeeId, $startDate, $endDate);

        /** @var ShipearlyHelper $Helper */
        $Helper = (new View($this))->loadHelper('Shipearly');

        $tableColumns = array(
            array(
                'name' => __('Order ID'),
                'valueCallback' => function($row) {
                    return $row['Order']['orderID'];
                },
                'linkOptionsCallback' => function($row) use ($Helper) {
                    return $Helper->orderLink($row['Order']['id'], $row['Order']['orderID'], false);
                },
            ),
            array(
                'name' => __('Order Status'),
                'valueCallback' => function($row) {
                    return OrderStatus::getLabel($row['Order']['order_status']);
                }
            ),
            array(
                'name' => __('Order Date'),
                'valueCallback' => function($row) {
                    return format_datetime($row['Order']['created_at'], DATE_FORMAT);
                }
            ),
            array(
                'name' => __('Delivery Date'),
                'valueCallback' => function($row) {
                    return format_datetime($row['Order']['deliveryDate'], DATE_FORMAT);
                }
            ),
            array(
                'name' => __('Retailer'),
                'valueCallback' => function($row) {
                    return $row['Retailer']['company_name'];
                }
            ),
            array(
                'name' => __('Retail'), 'is_money' => true,
                'valueCallback' => function($row) {
                    return $row['Order']['total_price'];
                }
            ),
            array(
                'name' => __('Commission'), 'is_money' => true,
                'valueCallback' => function($row) {
                    return $row['Order']['total_commission'];
                }
            ),
        );
        $tableColumns = $this->_mergeTableColumnDefaults($tableColumns);

        $tableFooters = array(
            'Payment Due' => $reportData['total']
        );

        $filename = $brandData['User']['company_name'] . ' Commission ' . date('Y-m-d', strtotime($reportData['period_end']));

        $this->set('companyName', $payeeData['User']['company_name']);
        $this->set('inlineAddress', $inlineAddress);
        $this->set('brandName', $brandData['User']['company_name']);
        $this->set('currencyCode', $brandData['User']['currency_code']);
        $this->set('reportData', $reportData);
        $this->set('tableColumns', $tableColumns);
        $this->set('tableRows', $ordersData);
        $this->set('tableFooters', $tableFooters);
        $this->set('filename', $filename);
    }

    protected function _computeDateRange($days, $endDate)
    {
        $days = (int) $days;
        $endDate = !empty($endDate) ? $endDate : date('Y-m-d');

        $startTimestamp = strtotime("{$endDate} -{$days} DAYS MIDNIGHT");
        $endTimestamp = strtotime("{$endDate} +1 DAY MIDNIGHT") - 1;

        return array_map(function ($timestamp) {
            return date('Y-m-d', $timestamp);
        }, [$startTimestamp, $endTimestamp]);
    }

    protected function _getCommissionsUserIdFromAuth($otherId = null)
    {
        $brandId = $otherId;
        $associateId = $otherId;
        if ($this->Auth->user('user_type') === User::TYPE_MANUFACTURER) {
            $brandId = $this->Auth->user('id');
        } elseif ($this->Auth->user('user_type') === User::TYPE_STAFF) {
            $associateId = $this->Auth->user('id');
        } else {
            $this->redirect($this->referer());
        }
        return array($brandId, $associateId);
    }

    /**
     * @param array $tableColumns
     * @return array
     */
    protected function _mergeTableColumnDefaults(array $tableColumns)
    {
        return array_map(function($column) {
            return array_merge(array(
                'name' => '',
                'is_money' => false,
                'valueCallback' => function() {},
                'linkOptionsCallback' => function() {},
            ), $column);
        }, $tableColumns);
    }

}
