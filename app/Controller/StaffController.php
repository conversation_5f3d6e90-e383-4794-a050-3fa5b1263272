<?php
App::uses('AppController', 'Controller');

/**
 * Staff Controller.
 *
 * @property AuthComponent $Auth
 * @property UserLogicComponent $UserLogic
 *
 * @property EmailTemplate $EmailTemplate
 * @property Order $Order
 * @property RetailerCreditPayment $RetailerCreditPayment
 * @property Staff $Staff
 * @property User $User
 */
class StaffController extends AppController
{
    public $components = array('Auth', 'UserLogic');

    public $uses = array('EmailTemplate', 'Order', 'RetailerCreditPayment', 'Staff', 'User');

    public function isAuthorized()
    {
        return ($this->Auth->user('user_type') === User::TYPE_RETAILER && !$this->Auth->user('Branch')) && parent::isAuthorized();
    }

    public function index()
    {
        $this->set('title_for_layout', __('Staff'));
        $this->set('staffUsers', $this->Staff->findAllForIndex($this->Auth->user('id')));
    }

    public function view($id = null)
    {
        $authId = (int)$this->Auth->user('id');

        $staff = array();
        if ($id) {
            $staff = $this->Staff->findForView($id);

            if (empty($staff['User']['id'])) {
                throw new NotFoundException();
            }
            if (!in_array($staff['User']['Branch'], $this->User->listRetailerStoreIds($authId))) {
                throw new ForbiddenException();
            }
        }
        $this->set('staff', $staff);

        $breadcrumb = Hash::get($staff, 'User.company_name', __('New'));
        $this->set('title_for_layout', __('Staff') . ' / ' . $breadcrumb);
        $this->set('breadcrumb', $breadcrumb);

        $this->set('roleOptions', Staff::getRoleOptions());
        $this->set('branchOptions', $this->User->listBranchNames($authId));

        $storeTiming = !empty($staff['User']['store_timing'])
            ? $staff['User']['store_timing']
            : Hash::get($staff, 'Store.store_timing', $this->Auth->user('store_timing'));
        $this->set('storeTiming', User::decodeStoreTiming($storeTiming));
        $this->set('enableDelete', $id && !$this->Order->exists(['Order.store_associate_id' => $id]));

        $this->getLanguages();
    }

    public function ajax_store_timing($id = null)
    {
        $storeTiming = $this->request->data('storetiming')
            ? $this->request->data('storetiming')
            : $this->request->query('data.storetiming');
        if (!$storeTiming) {
            throw new BadRequestException('Missing request data');
        }

        if ($id && $this->request->is('post')) {
            $this->User->updateStoreTiming($id, $storeTiming);
        }

        $storeTiming = User::decodeStoreTiming($storeTiming);
        $this->set('storeTiming', $storeTiming);
        $this->render('/Elements/Staff/ajax_store_timing', '');
    }

    public function add()
    {
        $this->request->allowMethod('post');
        if (!$this->request->data('User')) {
            throw new BadRequestException('Missing request data');
        }
        if (!$this->Staff->createFromForm($this->Auth->user('id'), $this->request->data)) {
            return $this->_validationErrorFlashResponse($this->Staff->validationErrors);
        }
        $this->UserLogic->activationMail($this->User->id);

        return $this->_successResponse('New staff member created', ['controller' => 'staff', 'action' => 'index']);
    }

    public function edit($id = null)
    {
        $existing = $this->User->findById($id, ['id', 'user_type'], null, -1);
        if (empty($existing['User']['id'])) {
            throw new NotFoundException();
        }
        $this->request->allowMethod('post', 'put');
        if ($existing['User']['user_type'] !== User::TYPE_STAFF) {
            throw new BadRequestException('Unexpected user_type ' . json_encode($existing['User']['user_type']));
        }
        if (!$this->request->data('User')) {
            throw new BadRequestException('Missing request data');
        }
        if (!$this->Staff->saveFromForm($id, $this->Auth->user('id'), $this->request->data)) {
            return $this->_validationErrorFlashResponse($this->Staff->validationErrors);
        }

        return $this->_successResponse();
    }

    public function delete($id = null)
    {
        $existing = $this->User->findById($id, ['id', 'user_type'], null, -1);
        if (empty($existing['User']['id'])) {
            throw new NotFoundException();
        }
        $this->request->allowMethod('post', 'delete');
        if ($existing['User']['user_type'] !== User::TYPE_STAFF) {
            throw new BadRequestException('Unexpected user_type ' . json_encode($existing['User']['user_type']));
        }
        if ($this->Order->exists(['Order.store_associate_id' => $id])) {
            $msg = 'Cannot delete a staff member with order commissions';
            return $this->_exceptionResponse(new BadRequestException($msg), $msg);
        }

        $this->RetailerCreditPayment->updateAllJoinless(['RetailerCreditPayment.created_by' => $this->Auth->user('id')], ['RetailerCreditPayment.created_by' => $id]);

        if (!$this->User->delete($id)) {
            return $this->_exceptionResponse();
        }
        return $this->_successResponse(__('The staff member has been deleted'), ['controller' => 'staff', 'action' => 'index']);
    }

}
