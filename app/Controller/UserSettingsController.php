<?php
App::uses('AppController', 'Controller');

/**
 * UserSettings Controller.
 *
 * @property Page $Page
 * @property UserSetting $UserSetting
 */
class UserSettingsController extends AppController
{
    public $components = array();

    public $uses = array('Page', 'UserSetting');

    public function admin_edit_policy($field = null, $userId = null)
    {
        if (!in_array($field, ['return_policy', 'privacy_policy', 'terms_of_service', 'checkout_head_snippet', 'success_page_snippet'], true)) {
            return $this->_exceptionResponse(
                new NotFoundException(json_encode(['field' => $field])),
                '', false, $this->referer(ADMIN_PATH . "users/Manufacturer", true)
            );
        }
        $fieldName = str_replace('_', ' ', $field);

        $userSetting = $this->UserSetting->findForAdminEditPolicy((int)$userId, (string)$field);
        if (empty($userSetting['UserSetting']['id'])) {
            return $this->_exceptionResponse(
                new NotFoundException(json_encode(['UserSetting' => ['user_id' => $userId]])),
                "Invalid brand.", false, $this->referer(ADMIN_PATH . "users/Manufacturer", true)
            );
        }

        if ($this->request->is(['post', 'put'])) {
            if (!isset($this->request->data['UserSetting'][$field])) {
                return $this->_exceptionResponse(
                    new BadRequestException(json_encode($this->request->data)),
                    null, true, $this->referer(ADMIN_PATH . "contact/" . $userId, true)
                );
            }
            $update = [
                'id' => $userSetting['UserSetting']['id'],
                // Empty value must not be null to trigger clearing the associated translation
                $field => (string)$this->request->data['UserSetting'][$field],
            ];
            if (!$this->UserSetting->updateUserSetting(['UserSetting' => $update])) {
                return $this->_exceptionResponse(
                    new InternalErrorException(json_encode(['errors' => $this->UserSetting->validationErrors, 'data' => $this->request->data])),
                    "Failed to update the {$fieldName}.", true, $this->referer(ADMIN_PATH . "contact/" . $userId, true)
                );
            }
            return $this->_successResponse("Successfully updated the {$fieldName}.", $this->referer(ADMIN_PATH . "contact/" . $userId, true));
        }

        $placeholder = '';
        if ($field === 'return_policy') {
            $placeholder = $this->Page->returnPolicy($userSetting['User']['email_address']);
        }

        $this->layout = 'admin';
        $this->set('title_for_layout', ucwords($fieldName));
        $this->set('placeholder', $placeholder);
        $this->request->data = $userSetting;
    }

}
