<?php
App::uses('AppController', 'Controller');
App::uses('User', 'Model');

/**
 * UserTaxes Controller.
 *
 * @property AuthComponent $Auth
 *
 * @property UserTax $UserTax
 * @property UserCountryTax $UserCountryTax
 * @property Country $Country
 * @property CountryTax $CountryTax
 * @property CountryValidationMethod $CountryValidationMethod
 * @property ShippingZone $ShippingZone
 * @property State $State
 */
class UserTaxesController extends AppController
{
    public $components = [
        'Auth',
    ];

    public $uses = [
        'UserTax',
        'UserCountryTax',
        'Country',
        'CountryTax',
        'CountryValidationMethod',
        'ShippingZone',
        'State',
    ];

    public $_registrationCountryCodes = ['US', 'CA'];

    public function isAuthorized()
    {
        if ($this->request->param('admin')) {
            $isAuthorized = (bool)$this->Auth->user('username');
        } else {
            $isAuthorized = (
                in_array($this->Auth->user('user_type'), [User::TYPE_MANUFACTURER, User::TYPE_RETAILER, User::TYPE_SALES_REP], true) &&
                !$this->Auth->user('Branch') &&
                $this->Permissions->userHasPermission($this->Auth->user(), Permissions::NAME_TAX_SETTINGS)
            );
        }
        return $isAuthorized && parent::isAuthorized();
    }

    public function admin_index()
    {
        $this->set('countries', $this->Country->findAllForAdminCountrySettings());
    }

    public function admin_edit_validation_methods($countryId = null)
    {
        if (!$this->Country->existsById($countryId)) {
            throw new NotFoundException(json_encode(['message' => 'Country not found', 'country_id' => $countryId]));
        }
        $this->request->allowMethod('post', 'put');

        if (!$this->CountryValidationMethod->saveMethodsEnabledForCountry($countryId, $this->request->data)) {
            return $this->_exceptionResponse(new InternalErrorException(json_encode(['errors' => $this->CountryValidationMethod->validationErrors, 'data' => $this->CountryValidationMethod->data])), null, true);
        }

        return $this->_successResponse();
    }

    public function admin_edit($countryCode = null)
    {
        $countryId = $this->validateCountryCode($countryCode);

        if ($this->request->is('post')) {
            if ($this->CountryTax->saveAdminCountryTaxSettings($countryId, $this->request->data)) {
                return $this->_successResponse();
            } else {
                $this->setFlash('An error occurred. Please, try again.', 'error');
            }
        } else {
            $this->request->data = $this->CountryTax->findAdminCountryTaxSettings($countryId);
        }

        $this->set('countryName', $this->Country->getCountryName($countryId));
        $this->set('states', $this->State->getStateList($countryId));
    }

    public function index()
    {
        $userId = $this->Auth->user('id');
        $userType = $this->Auth->user('user_type');

        if ($userType === User::TYPE_SALES_REP) {
            $userId = array_keys($this->User->listSalesRepBrandNames($this->Auth->user('id'), true));
        }
        if ($userType === User::TYPE_RETAILER) {
            $countryCode = $this->Country->getCountryCode($this->Auth->user('country_id'));
            if (!$countryCode) {
                throw new NotFoundException(json_encode([
                    'message' => 'Retailer country not found',
                    'Auth' => array_intersect_key($this->Auth->user(), array_flip(['id', 'email_address', 'company_name', 'user_type', 'country_id'])),
                ]));
            }
            return $this->redirect(['controller' => 'user_taxes', 'action' => 'edit', 'country_code' => $countryCode]);
        }

        $countryIds = $this->ShippingZone->findAllShippingCountryIds($userId);
        $countries = $this->Country->findAllById($countryIds, ['id', 'country_code', 'country_name', 'country_icon'], null, -1);

        $countries = array_column($countries, 'Country');
        $countries = array_map(function($country) {
            $country['country_code'] = strtoupper($country['country_code']);
            return $country;
        }, $countries);
        $this->set('countries', $countries);
    }

    public function edit($countryCode = null)
    {
        $countryCode = strtoupper($countryCode);
        if (in_array($countryCode, $this->_registrationCountryCodes, true)) {
            return $this->redirect(['controller' => 'user_taxes', 'action' => 'registrations', 'country_code' => $countryCode]);
        }

        $countryId = $this->validateCountryCode($countryCode);
        $this->validateUserCountry($countryId);

        $userId = (int)$this->Auth->user('id');

        if ($this->request->is('post')) {
            if ($this->UserCountryTax->saveUserTaxSettings($userId, $countryId, $this->request->data)) {
                return $this->_successResponse();
            } else {
                $this->setFlash($this->_buildFlashMessage('error'), 'error');
            }
        } else {
            $this->request->data = $this->UserCountryTax->findUserTaxSettings($userId, $countryId);
        }

        $this->set('countryName', $this->Country->getCountryName($countryId));
        $this->set('countryPlaceholder', $this->UserCountryTax->findPlaceholder($countryId));
        $this->set('statePlaceholders', $this->UserTax->listPlaceholdersByStateId($countryId));
    }

    public function registrations($countryCode = null)
    {
        $countryId = $this->validateCountryCode(strtoupper($countryCode));
        $this->validateUserCountry($countryId);

        $userId = (int)$this->Auth->user('id');

        $this->set('countryName', $this->Country->getCountryName($countryId));
        $this->set('stateTaxes', $this->UserTax->listRegisteredStates($userId, $countryId));
    }

    public function edit_registration($countryCode = null, $stateId = null)
    {
        $countryId = $this->validateCountryCode(strtoupper($countryCode));
        $this->validateUserCountry($countryId);

        $stateId = (int)$stateId ?: null;
        if ($stateId && !$this->State->exists(['id' => $stateId, 'country_id' => $countryId])) {
            throw new NotFoundException(json_encode([
                'message' => 'State not found in country',
                'country_code' => $countryCode,
                'state_id' => $stateId,
                'Auth' => array_intersect_key($this->Auth->user(), array_flip(['id', 'email_address', 'company_name', 'user_type'])),
            ]));
        }

        $userId = (int)$this->Auth->user('id');
        $userType = (string)$this->Auth->user('user_type');

        if ($this->request->is(['post', 'put'])) {
            if (!$stateId && $this->UserTax->exists(['user_id' => $userId, 'state_id' => $this->request->data['UserTax']['state_id']])) {
                return $this->_exceptionResponse(new BadRequestException(), 'The selected state has already been registered');
            }

            $stateId = (int)($stateId ?: $this->request->data['UserTax']['state_id']);
            if ($this->UserTax->saveTaxRegistration($userId, $stateId, $this->request->data)) {
                return $this->_successResponse();
            } else {
                return $this->_exceptionResponse(new InternalErrorException(json_encode(['errors' => $this->UserTax->validationErrors, 'data' => $this->UserTax->data])), null, true);
            }
        } elseif ($stateId) {
            $this->request->data = $this->UserTax->findTaxRegistration($userId, $stateId);
        }

        $statePlaceholders = ($stateId)
            ? [$stateId => $this->UserTax->findPlaceholder($stateId)]
            : $this->UserTax->listPlaceholdersByStateId($countryId);
        $states = array_column($statePlaceholders, 'state_name', 'state_id');

        $locationsByStateId = [];
        if ($userType === User::TYPE_RETAILER) {
            $locationsByStateId = $this->User->findTaxLocationsByStateId($userId, array_keys($states));
            $this->request->data['User'] = [];
            foreach ($locationsByStateId as $locations) {
                $this->request->data['User'] += $locations;
            }
        }

        $this->set('states', $states);
        $this->set('statePlaceholders', $statePlaceholders);
        $this->set('locationsByStateId', $locationsByStateId);
    }

    public function delete($id = null)
    {
        $user_id = $this->UserTax->field('user_id', compact('id'));
        if (!$user_id) {
            throw new NotFoundException(json_encode([
                'message' => 'UserTax not found',
                'UserTax' => compact('id'),
                'Auth' => array_intersect_key($this->Auth->user(), array_flip(['id', 'email_address', 'company_name', 'user_type'])),
            ]));
        }
        $this->request->allowMethod('post', 'delete');
        if ($this->Auth->user('id') !== $user_id) {
            throw new ForbiddenException(json_encode([
                'message' => 'UserTax does not belong to user',
                'UserTax' => compact('id', 'user_id'),
                'Auth' => array_intersect_key($this->Auth->user(), array_flip(['id', 'email_address', 'company_name', 'user_type'])),
            ]));
        }
        if (!$this->UserTax->delete($id)) {
            return $this->_exceptionResponse(new InternalErrorException(json_encode([
                'message' => 'Failed to delete UserTax',
                'UserTax' => compact('id', 'user_id'),
                'Auth' => array_intersect_key($this->Auth->user(), array_flip(['id', 'email_address', 'company_name', 'user_type'])),
            ])));
        }
        return $this->_successResponse('The state tax registration has been removed');
    }

    private function validateCountryCode(string $countryCode): int
    {
        $countryId = (int)$this->Country->field('id', ['country_code' => $countryCode]);

        if (!$countryId) {
            throw new NotFoundException(json_encode([
                'message' => 'Country not found',
                'country_code' => $countryCode,
                'Auth' => array_intersect_key($this->Auth->user(), array_flip(['id', 'email_address', 'company_name', 'user_type'])),
            ]));
        }

        return $countryId;
    }

    private function validateUserCountry(int $countryId)
    {
        $validCountryIds = [$this->Auth->user('country_id')];
        $userType = $this->Auth->user('user_type');
        $userId = $this->Auth->user('id');
        if ($userType === User::TYPE_SALES_REP) {
            $userId = array_keys($this->User->listSalesRepBrandNames($this->Auth->user('id'), true));
        }

        if ($userType === User::TYPE_MANUFACTURER || $userType === User::TYPE_SALES_REP) {
            $validCountryIds = (array)$this->ShippingZone->findAllShippingCountryIds($userId);
        }

        if (!in_array($countryId, $validCountryIds)) {
            throw new ForbiddenException(json_encode([
                'message' => 'User does not have access to this country',
                'country_id' => $countryId,
                'valid_country_ids' => $validCountryIds,
                'Auth' => array_intersect_key($this->Auth->user(), array_flip(['id', 'email_address', 'company_name', 'user_type'])),
            ]));
        }
    }
}
