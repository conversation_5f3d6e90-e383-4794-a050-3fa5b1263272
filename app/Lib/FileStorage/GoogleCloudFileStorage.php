<?php
App::uses('FileStorage', 'Lib/FileStorage');

use Google\Cloud\Storage\ObjectIterator;
use Google\Cloud\Storage\StorageClient;
use Google\Cloud\Storage\StorageObject;

class GoogleCloudFileStorage extends FileStorage
{
    public $scheme = 'gs';

    protected function listFiles(string $destination, string $path, string $sort = '', int $sortDirection = SORT_ASC): array
    {

        //$sort and $sortDirection not implemented
        $objects = $this->getObjects($destination, $path);

        return array_map(
            function(StorageObject $object) {
                return $object->gcsUri();
            },
            iterator_to_array($objects)
        );
    }

    protected function uploadFile(File $source, string $destination, string $path): bool
    {
        $source->open();

        $uploadedObject = $this->getClient()
            ->bucket($destination)
            ->upload($source->handle, ['name' => ltrim($path, '/')]);

        $source->close();

        return (bool)($uploadedObject->info()['timeCreated'] ?? false);
    }

    protected function deleteFiles(string $destination, string $path): bool
    {
        $objects = $this->getObjects($destination, $path);

        $success = true;
        /** @var StorageObject $object */
        foreach ($objects as $object) {
            $object->delete();
            // Consider querying the API if certainty of success is worth the extra call
            //$success &= !$object->exists();
        }

        return $success;
    }

    protected function getFile(string $destination, string $path)
    {
        $object = $this->getObject($destination, $path);

        if (!$object->exists()) {
            return false;
        }

        return $object->downloadAsString();
    }

    /**
     * Lazily load the storage client.
     *
     * Loads a JSON key file located by:
     *
     * 1. The path specified by the environment variable GOOGLE_APPLICATION_CREDENTIALS.
     * 2. $HOME/.config/gcloud/application_default_credentials.json
     * 3. Running the app in a Google Cloud instance with an attached Service Account will detect it.
     *
     * @return StorageClient
     */
    protected function getClient(): StorageClient
    {
        static $_client = null;

        if (!$_client) {
            $_client = new StorageClient();
        }

        return $_client;
    }

    /**
     * @param string $destination
     * @param string $path
     * @return ObjectIterator
     */
    protected function getObjects(string $destination, string $path): ObjectIterator
    {
        return $this->getClient()
            ->bucket($destination)
            ->objects([
                'prefix' => ltrim($path, '/'),
                'fields' => 'items/name,nextPageToken',
            ]);
    }

    /**
     * @param string $destination
     * @param string $path
     * @return StorageObject
     */
    protected function getObject(string $destination, string $path): StorageObject
    {
        return $this->getClient()
            ->bucket($destination)
            ->object($path);
    }
}
