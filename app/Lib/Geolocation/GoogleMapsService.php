<?php
namespace ShipEarlyApp\Lib\Geolocation;

use App;
use AppLogTrait;
use Geocoder\Exception\Exception;
use Geocoder\Location;
use Geocoder\Provider\Cache\ProviderCache;
use Geocoder\Provider\GoogleMaps\GoogleMaps;
use Geocoder\Provider\GoogleMaps\Model\GoogleAddress;
use Geocoder\Provider\Provider;
use Geocoder\Query\GeocodeQuery;
use Geocoder\Query\Query;
use Geocoder\Query\ReverseQuery;
use GuzzleHttp\Client;
use GuzzleHttp\ClientInterface;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\MessageFormatterInterface;
use GuzzleHttp\Middleware;
use GuzzleHttp\Psr7\Utils;
use Psr\Http\Message\ResponseInterface;
use Psr\Log\LoggerInterface;
use ShipEarlyApp\Lib\Cache\CakeSimpleCache;
use ShipEarlyApp\Lib\Log\CakeLogger;
use ShipEarlyApp\Lib\Log\JsonMessageFormatter;

App::uses('AppLogTrait', 'Log/Engine');

class GoogleMapsService
{
    protected Provider $geocoder;

    public function __construct(?string $apiKey = null, ?ClientInterface $httpClient = null)
    {
        if (!$apiKey) {
            $apiKey = GOOGLE_GEOCODING_API_KEY;
        }

        $this->geocoder = new GoogleMaps($httpClient ?? static::newClient(), null, $apiKey);
        $this->geocoder = new ProviderCache($this->geocoder, new CakeSimpleCache('google_maps'));
    }

    public static function newClient(?HandlerStack $handler = null): ClientInterface
    {
        $handler = $handler ?? HandlerStack::create();
        $handler->push(Middleware::log(static::getLogger(), static::getMessageFormatter(), 'debug'), 'log');
        $handler->push(static::getAddressComponentEdgeCasesMiddleware(), 'address_component_edge_cases');

        return new Client(['handler' => $handler]);
    }

    public static function getLogger(): LoggerInterface
    {
        static $logger = null;
        if ($logger === null) {
            $logger = new CakeLogger(['google_maps']);
        }

        return $logger;
    }

    public static function getMessageFormatter(): MessageFormatterInterface
    {
        static $formatter = null;
        if ($formatter === null) {
            $formatter = new JsonMessageFormatter();
        }

        return $formatter;
    }

    /**
     * Modifying the response body to handle `GoogleMaps::updateAddressComponent()` edge cases.
     *
     * The GoogleMaps provider class cannot be overridden, so modify the response data before it reads the address.
     *
     * @return callable
     * @see GoogleMaps::updateAddressComponent()
     */
    public static function getAddressComponentEdgeCasesMiddleware(): callable
    {
        return Middleware::mapResponse(static function(ResponseInterface $response): ResponseInterface
        {
            $bodyArray = AppLogTrait::extractPsrMessageBody($response);
            if (empty($bodyArray['results']) || !is_array($bodyArray['results']) || $bodyArray['status'] !== 'OK') {
                return $response;
            }

            // We only read the first result, so remove other results to reduce the size of logs and processing.
            $bodyArray['results'] = array_slice($bodyArray['results'], 0, 1);

            $bodyArray['results'] = array_map(function(array $result): array {
                $allTypes = [];
                foreach ($result['address_components'] as $component) {
                    $allTypes += array_combine($component['types'], $component['types']);
                }

                foreach ($result['address_components'] as &$component) {
                    foreach ($component['types'] as $type) {
                        if ($type === 'postal_code_prefix') {
                            // Treat 'postal_code_prefix' as 'postal_code' if the latter is not present.
                            if (!isset($allTypes['postal_code'])) {
                                $component['types'][] = 'postal_code';
                            }
                        } elseif ($type === 'postal_town') {
                            // If both 'postal_town' and 'locality' are present, prevent 'postal_town' from being
                            // matched and overriding 'locality'.
                            if (isset($allTypes['locality'])) {
                                $component['types'] = array_map(function(string $type): string {
                                    return ($type === 'postal_town') ? '_postal_town' : $type;
                                }, $component['types']);
                            }
                        }
                    }
                }

                return $result;
            }, $bodyArray['results']);

            return $response->withBody(Utils::streamFor(json_encode($bodyArray)));
        });
    }

    /**
     * @param string $street Line 1 of the street address. Line 2 should not be included.
     * @param string $city
     * @param string $postalCode
     * @param string $stateName
     * @param string $countryCode Country ISO 3166-1 alpha-2 code or full name.
     * @param int $level
     * @return Geolocation
     * @throws Exception
     */
    public function geocodeFromComponents(string $street, string $city, string $postalCode, string $stateName, string $countryCode, int $level = 1): Geolocation
    {
        while (true) {
            try {
                // Address params should not overlap with component params
                $address = implode(', ', array_filter([$street, $city, $stateName, $postalCode]));

                $geolocation = $this->geocode($address, $countryCode);

                if ($postalCode && $geolocation->zipcode && $postalCode !== $geolocation->zipcode) {
                    static::getLogger()->warning(json_encode(['message' => 'Geocode responded with a different postal_code', 'request' => $postalCode, 'response' => $geolocation->zipcode]));
                }

                return $geolocation;
            } catch (Exception $e) {
                switch ($level) {
                    case 4:
                        $countryCode = '';
                        // no break
                    case 3:
                        $stateName = '';
                        // no break
                    case 2:
                        $city = '';
                        // no break
                    case 1:
                        $street = '';

                        $level++;
                        static::getLogger()->warning('Retrying geocode with fewer components', ['level' => $level]);

                        break;
                    default:
                        throw $e;
                }
            }
        }
    }

    /**
     * @param string $address
     * @param string|null $country
     * @return Geolocation
     * @throws Exception
     */
    public function geocode(string $address, ?string $country = null): Geolocation
    {
        $query = GeocodeQuery::create($address)
            ->withLimit(1);
        if ($country) {
            // Only using components for country/country_code disambiguation because
            // adding more seems to interfere with precise street address matching
            $query = $query->withData('components', ['country' => $country]);
        }

        static::getLogger()->info(static::getQueryLog($query));

        $location = $this->geocoder->geocodeQuery($query)->first();

        static::getLogger()->info(static::getLocationLog($location));

        return $this->locationToGeolocation($location);
    }

    /**
     * @param float|string $latitude
     * @param float|string $longitude
     * @return Geolocation
     * @throws Exception
     */
    public function reverse($latitude, $longitude): Geolocation
    {
        $query = ReverseQuery::fromCoordinates($latitude, $longitude)
            ->withLimit(1);

        static::getLogger()->info(static::getQueryLog($query));

        $location = $this->geocoder->reverseQuery($query)->first();

        static::getLogger()->info(static::getLocationLog($location));

        return $this->locationToGeolocation($location);
    }

    protected static function locationToGeolocation(Location $location): Geolocation
    {
        $coordinates = $location->getCoordinates();
        $subLocalityLevel1 = ($location instanceof GoogleAddress && $location->getSubLocalityLevels()->has(1))
            ? $location->getSubLocalityLevels()->get(1)
            : null;
        $country = $location->getCountry();
        $state = $location->getAdminLevels()->has(1) ? $location->getAdminLevels()->get(1) : null;

        return new Geolocation($coordinates->getLatitude(), $coordinates->getLongitude(), [
            'address1' => trim($location->getStreetNumber() . ' ' . $location->getStreetName()) ?: null,
            'city' => $location->getLocality() ?: ($subLocalityLevel1 ? $subLocalityLevel1->getName() : null),
            'country_code' => $country ? $country->getCode() : null,
            'country_name' => $country ? $country->getName() : null,
            'state_code' => $state ? $state->getCode() : null,
            'state_name' => $state ? $state->getName() : null,
            'zipcode' => $location->getPostalCode(),
        ]);
    }

    /**
     * @param Query $query
     * @return string
     * @see Query::__toString()
     */
    protected static function getQueryLog(Query $query): string
    {
        $logArray = ['class' => get_class($query)];
        if ($query instanceof GeocodeQuery) {
            $logArray += [
                'text' => $query->getText(),
                'bounds' => $query->getBounds() ? $query->getBounds()->toArray() : null,
            ];
        } elseif ($query instanceof ReverseQuery) {
            $logArray += [
                'lat' => $query->getCoordinates()->getLatitude(),
                'lng' => $query->getCoordinates()->getLongitude(),
            ];
        }
        $logArray += [
            'locale' => $query->getLocale(),
            'limit' => $query->getLimit(),
            'data' => $query->getAllData(),
        ];

        return AppLogTrait::json_encode($logArray);
    }

    /**
     * @param Location $location
     * @return string
     * @see Location::toArray()
     */
    protected static function getLocationLog(Location $location): string
    {
        $logArray = ['class' => get_class($location)];
        $logArray += $location->toArray();

        return AppLogTrait::json_encode($logArray);
    }
}
