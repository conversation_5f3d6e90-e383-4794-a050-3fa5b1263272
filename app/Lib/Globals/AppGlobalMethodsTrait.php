<?php

namespace ShipEarlyApp\Lib\Globals;

/**
 * Wrappers for global methods that can be mocked in tests.
 */
trait AppGlobalMethodsTrait
{
    /**
     * Format a local time/date.
     *
     * @param string $format The format of the outputted date string.
     * @param int|null $timestamp [optional] Default value: time(). The optional timestamp parameter is an integer Unix timestamp
     * that defaults to the current local time if a timestamp is not given.
     * @return string|null Formatted date string. If the provided timestamp cannot be parsed,
     * NULL is returned and an E_WARNING level error is emitted.
     * @see \date
     */
    protected function date(string $format = 'Y-m-d H:i:s', ?int $timestamp = null): ?string
    {
        return AppGlobalMethods::instance()->date($format, $timestamp);
    }

    /**
     * Return current Unix timestamp.
     *
     * @return int <p>Returns the current time measured in the number of seconds since the Unix Epoch (January 1 1970 00:00:00 GMT).</p>
     * @see \time
     */
    protected function time(): int
    {
        return AppGlobalMethods::instance()->time();
    }

    /**
     * Delay execution in fractional seconds.
     *
     * @param float $seconds Halt time in fractional seconds.
     */
    protected function sleep(float $seconds): void
    {
        AppGlobalMethods::instance()->sleep($seconds);
    }

    /**
     * Delay execution in microseconds.
     *
     * @param int $micro_seconds Halt time in millionths of a second.
     * @see \usleep
     */
    protected function usleep(int $micro_seconds): void
    {
        AppGlobalMethods::instance()->usleep($micro_seconds);
    }
}
