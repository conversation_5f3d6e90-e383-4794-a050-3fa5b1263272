<?php

namespace ShipEarlyApp\Lib\Integrations\Klaviyo\Event;

class B2bCartUpdatedEvent extends B2bCartCreatedEvent
{
    const NAME = 'B2b Cart Updated';

    public function __construct(array $b2bCart, array $retailerAddress, array $shipToAddress)
    {
        parent::__construct($b2bCart, $retailerAddress, $shipToAddress);

        if (isset($this->attributes->unique_id)) {
            $this->attributes->unique_id .= "_{$this->time()}";
        }
    }
}
