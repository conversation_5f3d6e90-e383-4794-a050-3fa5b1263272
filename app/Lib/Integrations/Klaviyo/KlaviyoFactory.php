<?php

namespace ShipEarlyApp\Lib\Integrations\Klaviyo;

use App;
use User;

App::uses('User', 'Model');

class KlaviyoFactory
{
    public static function new(User $userModel, int $userId): KlaviyoInterface
    {
        $user = $userModel->findForKlaviyo($userId);

        $publicKey = (string)($user[$userModel->alias]['klaviyo_public_key'] ?? '');
        if (!empty($publicKey)) {
            return new KlaviyoIntegration($publicKey);
        }

        return new NullKlaviyo();
    }
}
