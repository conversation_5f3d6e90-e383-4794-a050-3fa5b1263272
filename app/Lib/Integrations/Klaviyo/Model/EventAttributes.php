<?php

namespace ShipEarlyApp\Lib\Integrations\Klaviyo\Model;

class EventAttributes extends KlaviyoModelAttributes
{
    public array $properties = [];
    public ?string $time = null;
    public ?string $value = null;
    public ?string $value_currency = null;
    public ?string $unique_id = null;
    public KlaviyoModel $metric;
    public KlaviyoModel $profile;

    public function __construct(string $name)
    {
        $this->metric = new MetricModel($name);
        $this->profile = new ProfileModel();
    }
}
