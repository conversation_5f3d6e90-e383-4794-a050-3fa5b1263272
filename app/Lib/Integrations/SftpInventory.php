<?php

namespace ShipEarlyApp\Lib\Integrations;

use App;
use CakeLog;
use FileStorage;
use InvalidArgumentException;
use League\Csv\Reader;
use League\Csv\Statement;
use SftpFileStorage;

App::uses('CakeLog', 'Log');
App::uses('FileStorage', 'Lib/FileStorage');
App::uses('SftpFileStorage', 'Lib/FileStorage');

class SftpInventory
{
    /**
     * @var SftpFileStorage
     */
    public $FileStorage;

    /**
     * list of files that existed at import
     * @var string[]
     */
    protected $files;

    public function __construct(string $username)
    {
        $path = "{$username}/upload";
        $this->FileStorage = static::getFileStorage($path);
    }

    /**
     * 
     * @return mixed 
     * @throws InvalidArgumentException 
     */
    protected function getImportData()
    {
        //get most recent file from import landing pad
        $files = $this->FileStorage->findUris(static::getImportBaseUri($this->FileStorage), 'mtime', SORT_DESC);
        $this->setFileList($files);
        return $this->FileStorage->retrieveContents($this->getFilenameForImport());
    }

    /**
     * 
     * @return array 
     */
    public function getInventoryData(): array
    {
        $data = [];
        $importData = $this->getImportData();

        $csv = Reader::createFromString($importData);
        $csv->setHeaderOffset(0);
        $statement = Statement::create();
        $records = $statement->process($csv, ['upc', 'qty']);

        $mismatchQuantities = [];
        $nonnumericQuantities = [];
        foreach ($records as $offset => $record) {
            if (!empty($record['upc'])){
                if (!is_numeric($record['qty']) || ((int)$record['qty'] != $record['qty'])) {
                    $nonnumericQuantities[$record['upc']][] = $record['qty'];
                    continue;
                }
                if(isset($data[$record['upc']]) && $data[$record['upc']]['qty'] !== $record['qty']) {
                    $mismatchQuantities[$record['upc']][$record['qty']] = 1;
                    $mismatchQuantities[$record['upc']][$data[$record['upc']]['qty']] = 1;
                    $lowerQuantity = min($data[$record['upc']]['qty'], $record['qty']);
                    $record['qty'] = $lowerQuantity;
                }
                
                $data[$record['upc']] = $record;
            }
        }

        if(!empty($mismatchQuantities)){
            $mismatchQuantities = array_map(function($upcQuantites) {
                return array_keys($upcQuantites);
            }, $mismatchQuantities);
            CakeLog::warning("Mismatching quantities detected in file {$this->getFilenameForImport()} Using lower quantity. Mismatched quantities by upc:" . json_encode($mismatchQuantities));
        }
        if(!empty($nonnumericQuantities)){
            CakeLog::warning("Nonnumeric quantities detected in file {$this->getFilenameForImport()}:" . json_encode($nonnumericQuantities));
        }

        return $data;
    }

    public function cleanupFiles()
    {
        $success = true;
        foreach ($this->getFileList() as $file) {
            $success &= $this->FileStorage->deleteUri(static::getImportFileUri($this->FileStorage, $file));
        }

        return $success;
    }

    /**
     * Return the names of SFTP users with files pending import.
     *
     * @return string[]
     */
    public static function getUsernamesForImport(): array
    {
        $usernames = [];
        $fileStorage = static::getFileStorage();
        $dirs = static::getAllUsernames();
        foreach ($dirs as $dir) {
            $files = $fileStorage->findUris(static::getImportFileUri($fileStorage, "{$dir}/upload"));
            if ((bool)count($files)) {
                $usernames[] = $dir;
            }
        }

        return $usernames;
    }

    /**
     * Return the names of SFTP users with stale directories.
     *
     * @return string[]
     */
    public static function getUsernamesForReset(): array
    {
        $usernames = [];

        // threshold is 24 hours since last import.
        $resetThreshold = time() - (24*60*60);
        $fileStorage = static::getFileStorage();
        $dirs = static::getAllUsernames();
        foreach ($dirs as $dir) {
            $files = $fileStorage->findUriData(static::getImportFileUri($fileStorage, $dir));
            if (!empty($files['upload']) && $files['upload']['mtime'] < $resetThreshold) {
                $usernames[] = $dir;
            }
        }

        return $usernames;
    }

    public static function isUsername(string $username): bool
    {
        return in_array($username, static::getAllUsernames(), true);
    }

    public static function isUsernameForImport(string $username): bool
    {
        $fs = static::getFileStorage();

        return (
            static::isUsername($username)
            && count($fs->findUris(static::getImportFileUri($fs, "{$username}/upload"))) > 0
        );
    }

    /**
     * Return the names of all SFTP users.
     *
     * This method caches the list of usernames for the duration of the request.
     *
     * @param bool $cached Set false to force a refresh of the list of usernames.
     * @return string[]
     */
    public static function getAllUsernames(bool $cached = true): array
    {
        static $usernames = null;

        if ($usernames === null || !$cached) {
            $fs = static::getFileStorage();
            $usernames = $fs->findUris(static::getImportBaseUri($fs));
        }

        return (array)$usernames;
    }

    protected static function getFileStorage(string $destinationRoot = '.'): SftpFileStorage
    {
        return FileStorage::create('Sftp', INV_DATA_HOST, $destinationRoot, null, INV_DATA_USERNAME, INV_DATA_PASS);
    }

    protected static function getImportBaseUri(FileStorage $fileStorage): string
    {
        return sprintf('%s://%s/%s', $fileStorage->scheme, $fileStorage->getDestination(), $fileStorage->getDestinationRoot());
    }

    protected static function getImportFileUri(FileStorage $fileStorage, string $fileName)
    {
        return sprintf('%s/%s', static::getImportBaseUri($fileStorage), $fileName);
    }

    /**
     * 
     * @param array $files 
     * @return void 
     */
    protected function setFileList(array $files)
    {
        $this->files = $files;
    }

    /**
     * 
     * @return array 
     */
    protected function getFileList(): array
    {
        return $this->files;
    }

    protected function getFilenameForImport(): string
    {
        if(!empty($this->files)){
            return static::getImportFileUri($this->FileStorage, current($this->files));
        }
        return '';
    }
}
