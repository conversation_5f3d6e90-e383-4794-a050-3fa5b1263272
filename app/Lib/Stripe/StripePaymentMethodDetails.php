<?php

namespace Ship<PERSON>arlyApp\Lib\Stripe;

use Stripe\Card;
use Stripe\Charge;
use Stripe\Exception\ApiErrorException;
use Stripe\PaymentIntent;
use Stripe\PaymentMethod;
use Stripe\StripeObject;

/**
 * Wrapper for StripeObject representing payment_method_details with standardized properties.
 *
 * @property string $transactionID ID of the PaymentIntent or Charge.
 * @property string|null $stripe_account ID of the payee Account.
 * @property string|null $card_type Card brand. Can be <code>American Express</code>, <code>Diners Club</code>, <code>Discover</code>, <code>JCB</code>, <code>MasterCard</code>, <code>UnionPay</code>, <code>Visa</code>, or <code>Unknown</code>.
 * @property string|null $last_four_digit The last four digits of the card.
 * @property string|null $fraud_check_cvc Outcome of cvc fraud check. Can be <code>pass</code>, <code>fail</code>, <code>unavailable</code>, <code>unchecked</code>.
 * @property string|null $fraud_check_address Outcome of address fraud check. Can be <code>pass</code>, <code>fail</code>, <code>unavailable</code>, <code>unchecked</code>.
 * @property string|null $fraud_check_postal_code Outcome of postal code fraud check. Can be <code>pass</code>, <code>fail</code>, <code>unavailable</code>, <code>unchecked</code>.
 *
 * @see Card
 */
class StripePaymentMethodDetails
{
    /**
     * @param PaymentIntent $payment
     * @param string|null $stripe_account
     * @return static
     * @throws ApiErrorException
     */
    public static function fromPaymentIntent(PaymentIntent $payment, ?string $stripe_account): self
    {
        $charge = $payment->charges->first();
        if ($charge) {
            return static::fromStripeCharge($charge, $stripe_account);
        }

        $paymentMethod = $payment->payment_method ?? null;
        if (is_string($paymentMethod)) {
            $paymentMethod = PaymentMethod::retrieve($paymentMethod, ['stripe_account' => $stripe_account]);
        }

        return static::fromCardObject($paymentMethod->card ?? null, $payment->id, $stripe_account);
    }

    public static function fromStripeCharge(Charge $charge, ?string $stripe_account): self
    {
        $cardObject = ($charge->source instanceof Card)
            ? $charge->source
            : $charge->payment_method_details->card ?? null;
        $transactionID = $charge->payment_intent ?? $charge->id;

        return static::fromCardObject($cardObject, $transactionID, $stripe_account);
    }

    protected static function fromCardObject(?StripeObject $cardObject, string $transactionID, ?string $stripe_account): self
    {
        return new static(
            $transactionID,
            $stripe_account,
            $cardObject->brand ?? null,
            $cardObject->last4 ?? null,
            $cardObject->checks->cvc_check ?? null,
            $cardObject->checks->address_line1_check ?? null,
            $cardObject->checks->address_postal_code_check ?? null
        );
    }

    protected function __construct(
        string $transactionID = '',
        ?string $stripe_account = null,
        ?string $card_type = null,
        ?string $last_four_digit = null,
        ?string $fraud_check_cvc = null,
        ?string $fraud_check_address = null,
        ?string $fraud_check_postal_code = null
    )
    {
        $this->transactionID = $transactionID;
        $this->stripe_account = $stripe_account;
        $this->card_type = $card_type;
        $this->last_four_digit = $last_four_digit;
        $this->fraud_check_cvc = $fraud_check_cvc;
        $this->fraud_check_address = $fraud_check_address;
        $this->fraud_check_postal_code = $fraud_check_postal_code;
    }

    public function toArray(): array
    {
        return [
            'transactionID' => $this->transactionID,
            'stripe_account' => $this->stripe_account,
            'card_type' => $this->card_type,
            'last_four_digit' => $this->last_four_digit,
            'fraud_check_cvc' => $this->fraud_check_cvc,
            'fraud_check_address' => $this->fraud_check_address,
            'fraud_check_postal_code' => $this->fraud_check_postal_code,
        ];
    }
}
