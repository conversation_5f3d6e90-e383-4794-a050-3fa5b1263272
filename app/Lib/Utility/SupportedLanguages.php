<?php

namespace ShipEarlyApp\Lib\Utility;

use App;
use I18n;

App::uses('I18n', 'I18n');

/**
 * Global methods for looking up supported languages.
 *
 * The array in `getAllOptions()` acts as the single source of truth for supported languages.
 *
 */
class SupportedLanguages
{
    const DEFAULT_LANG = 'en';
    const DEFAULT_LOCALE = 'eng';

    /**
     * @return array<string, string> [lang => name]
     */
    public static function getAllOptions(): array
    {
        return [
            'en' => __('English'),
            'fr' => __('French'),
            'es' => __('Spanish'),
            'de' => __('German'),
            'it' => __('Italian'),
            'nl' => __('Dutch'),
            'pt' => __('Portuguese'),
        ];
    }

    /**
     * @return array<string, string> [lang => name]
     */
    public static function getSelfTranslatedOptions(): array
    {
        $selfTranslatedOptions = [];
        foreach (array_keys(static::getAllOptions()) as $lang) {
            $selfTranslatedOptions[$lang] = static::getSelfTranslatedName($lang);
        }

        return $selfTranslatedOptions;
    }

    /**
     * @return array<string, string> [locale => name]
     */
    public static function getOptionsByLocale(): array
    {
        $optionsByLocale = [];
        foreach (static::getAllOptions() as $lang => $name) {
            $optionsByLocale[static::toLocale($lang)] = $name;
        }

        return $optionsByLocale;
    }

    /**
     * @return array<string, string> [locale => locale]
     */
    public static function getLocalesSet(): array
    {
        $languageCodes = array_keys(static::getAllOptions());
        $locales = array_map([static::class, 'toLocale'], $languageCodes);

        return array_combine($locales, $locales);
    }

    /**
     * @param string|null $lang language code or locale (eg. 'en', 'en-us', or 'eng')
     * @return string|null language name (eg. 'English'), or null if not found
     */
    public static function getName(?string $lang): ?string
    {
        if (!$lang) {
            return null;
        }

        $languageNames = static::getAllOptions();

        return $languageNames[$lang] ?? $languageNames[static::toShortLang($lang)] ?? null;
    }

    /**
     * @param string|null $lang language code or locale (eg. 'en', 'en-us', or 'eng')
     * @return string|null language name in its own language (eg. 'English' or 'Français'), or null if not found
     */
    protected static function getSelfTranslatedName(?string $lang): ?string
    {
        if (!$lang) {
            return null;
        }

        return I18n::translate(
            static::getUntranslatedName($lang),
            null,
            null,
            I18n::LC_MESSAGES,
            null,
            static::toLocale($lang)
        );
    }

    /**
     * @param string|null $lang language code or locale (eg. 'en', 'en-us', or 'eng')
     * @return string|null language name in English (eg. 'English'), or null if not found
     */
    protected static function getUntranslatedName(?string $lang): ?string
    {
        if (!$lang) {
            return null;
        }

        $catalog = (array)(I18n::getInstance()->l10n->catalog($lang) ?: []);
        if (!$catalog) {
            return null;
        }

        $language = (string)$catalog['language'];

        // Fix default locales with names like 'French (Standard)' when 'French' is expected.
        $isDefaultLocale = ($catalog['locale'] === $catalog['localeFallback']);
        if ($isDefaultLocale) {
            $language = trim(preg_replace('/ \(.*\)$/', '', $language));
        }

        return $language ?: null;
    }

    /**
     * @param string|null $lang language code or locale (eg. 'en', 'en-us', or 'eng')
     * @return string|null 3-char locale (eg. 'eng'), or null if not found
     */
    public static function toLocale(?string $lang): ?string
    {
        if (!$lang) {
            return null;
        }

        $catalog = (array)(I18n::getInstance()->l10n->catalog($lang) ?: []);

        return (string)($catalog['localeFallback'] ?? '') ?: null;
    }

    /**
     * @param string|null $lang language code or locale (eg. 'en', 'en-us', or 'eng')
     * @return string 2-char language code (eg. 'en'), or null if not found
     */
    public static function toShortLang(?string $lang): ?string
    {
        if (!$lang) {
            return null;
        }

        $locale = (string)static::toLocale($lang);

        return (string)I18n::getInstance()->l10n->map($locale) ?: null;
    }
}
