<?php

namespace ShipEarlyApp\Lib\Utility;

/**
 * Represents an enum for User.site_type.
 */
class UserSiteType
{
    const MAGENTO = 'Magento';
    const SHIPEARLY = 'ShipEarly';
    const SHOPIFY = 'Shopify';
    const WOOCOMMERCE = 'Woocommerce';

    const ALL = [
        self::MAGENTO,
        self::SHIPEARLY,
        self::SHOPIFY,
        self::WOOCOMMERCE,
    ];

    public static function getLabel(string $siteType): string
    {
        return static::getAllOptions()[$siteType];
    }

    /**
     * @return array<string, string>
     */
    public static function getAllOptions(): array
    {
        $siteTypes = static::ALL;

        return array_merge(array_combine($siteTypes, $siteTypes), [
            static::WOOCOMMERCE => 'WooCommerce',
        ]);
    }
}
