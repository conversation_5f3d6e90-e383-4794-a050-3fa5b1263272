# LANGUAGE translation of CakePHP Application
# <AUTHOR> <EMAIL>
#
msgid ""
msgstr ""
"Project-Id-Version: ShipEarly\n"
"POT-Creation-Date: \n"
"PO-Revision-Date: 2025-06-24 13:06-0400\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: LANGUAGE <EMAIL@ADDRESS>\n"
"Language: es\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.6\n"

#: Controller/ApiClientsController.php:45;62
msgid "The api client has been saved."
msgstr "El cliente api fue guardado."

#: Controller/ApiClientsController.php:47;64
msgid "The api client could not be saved. Please, try again."
msgstr "El cliente api no pudo guardarse. Por favor, intente nuevamente."

#: Controller/ApiClientsController.php:56;76
msgid "Invalid api client"
msgstr "Cliente api inválido"

#: Controller/ApiClientsController.php:80
msgid "The api client could not be deleted. Please, try again."
msgstr "El cliente api no pudo eliminarse. Por favor, intente nuevamente."

#: Controller/ApiClientsController.php:83
msgid "The api client has been deleted."
msgstr "El cliente api fue eliminado."

#: Controller/AppController.php:814 Controller/OrdersController.php:2256
msgid "View Map"
msgstr "Ver Mapa"

#: Controller/AppController.php:1037
msgid "Update successful"
msgstr "Actualización Exitosa"

#: Controller/AppController.php:1039
msgid "An error occurred. Please, try again."
msgstr "Ha ocurrido un error. Por favor, intente nuevamente."

#: Controller/B2bCartsController.php:105
msgid "Draft Orders"
msgstr "Pedidos en Borrador"

#: Controller/B2bCartsController.php:132
msgid "%s Draft Order"
msgstr "%s Pedido en Borrador"

#: Controller/B2bCartsController.php:293 View/DealerOrderRefunds/refund.ctp:95
#: View/OrderRefunds/refund.ctp:125
msgid "(%s Remaining)"
msgstr "(%s Restante)"

#: Controller/B2bCartsController.php:300 View/B2bCarts/view.ctp:263
msgid "Please select a payment method"
msgstr "Seleccione un Método de Pago"

#: Controller/B2bCartsController.php:495
msgid "The cart has been updated."
msgstr "El carrito fue actualizado."

#: Controller/B2bCartsController.php:505
msgid "Failed to delete b2b cart"
msgstr "Error al eliminase el carrito b2b"

#: Controller/B2bCartsController.php:511
msgid "The b2b cart has been deleted."
msgstr "El carrito b2b fue eliminado."

#: Controller/B2bCartsController.php:517
msgid "Invalid b2b cart product"
msgstr "Producto b2b en carrito inválido"

#: Controller/B2bCartsController.php:540
msgid "Failed to delete b2b cart product"
msgstr "Error al eliminar el producto del carrito b2b"

#: Controller/B2bCartsController.php:553
msgid "The product has been removed from cart."
msgstr "El producto fue eliminado del carrito."

#: Controller/B2bCartsController.php:636
msgid "There was an error processing your payment. Please, try again."
msgstr ""

#: Controller/B2bCartsController.php:656
msgid "Purchase Order %s has been created."
msgstr "Pedido de Venta %s fue creado."

#: Controller/B2bCartsController.php:692
msgid "Failed placing purchase order %s in the ecommerce platform."
msgstr "Error al colocar el pedido de compra %s en la plataforma de comercio electrónico."

#: Controller/B2bCartsController.php:1186
msgid "That cart no longer exists or was emptied."
msgstr "El carrito ya no existe o fue vaciado."

#: View/ProductPricing/edit.ctp:20
msgid "Inserting blank pricing removes the item from catalog"
msgstr "Insertar un precio en blanco elimina el artículo del catálogo"

#: Controller/BranchsController.php:61
#: Plugin/Widgets/View/LocatorWidgets/products.ctp:17
#: View/Helper/SidebarHelper.php:406 View/Retailers/view.ctp:138
#: View/Staff/view.ctp:127;132 View/Users/<USER>
msgid "Locations"
msgstr "Ubicaciones"

#: Controller/BranchsController.php:95
msgid "Add new Location"
msgstr "Agregar nueva Ubicación"

#: Controller/BranchsController.php:162
msgid "New Location has been created successfully."
msgstr "La Nueva Ubicación fue creada con éxito."

#: Controller/BranchsController.php:184
msgid "Add Location"
msgstr "Agregar Ubicación"

#: Controller/BranchsController.php:203
msgid "Location moved to Offline mode."
msgstr "Ubicación movida a modo Desconectado."

#: Controller/BranchsController.php:221
msgid "Location moved to Online mode."
msgstr "Ubicación movida a modo Conectado."

#: Controller/BranchsController.php:245
msgid "Location information updated successfully."
msgstr "Información de Ubicación actualizada con éxito."

#: Controller/BranchsController.php:251
msgid "Edit Location"
msgstr "Editar Ubicación"

#: Controller/BranchsController.php:252
msgid "Update Location"
msgstr "Actualizar Ubicación"

#: Controller/BranchsController.php:272
msgid "Location permission has been updated"
msgstr "Permisos de la Ubicación actualizados"

#: Controller/BranchsController.php:280 View/Branchs/store_permission.ctp:9
msgid "Access Level"
msgstr "Nivel de Acceso"

#: Controller/BranchsController.php:376
msgid "Schedule has been setup successfully"
msgstr "El horario se ha configurado correctamente"

#: Controller/BranchsController.php:446
#: Controller/OrdersController.php:259;3703
#: View/Elements/Products/catalogue_variants.ctp:19 View/Products/view.ctp:516
msgid "UPC"
msgstr "UPC (Código de Producto Universal)"

#: Controller/BranchsController.php:447
msgid "QOH"
msgstr "Cantidad Disponible de Inventario"

#: Controller/BranchsController.php:576 Controller/InventoryController.php:766
msgid "Inventory successfully updated"
msgstr "Inventario actualizado con éxito"

#: Controller/BranchsController.php:579
#: Controller/InventoryController.php:769;1061
#: Controller/ProductsController.php:1101
msgid "An error occurred uploading the file"
msgstr "Un error ha ocurrido al subir el archivo"

#: Controller/BrandStaffController.php:31 Controller/StaffController.php:29;51
#: View/BrandStaff/view.ctp:17 View/Elements/Layouts/profile_dropdown.ctp:96
#: View/Helper/SidebarHelper.php:391 View/Staff/view.ctp:86
msgid "Staff"
msgstr "Personal"

#: Controller/BrandStaffController.php:117 Controller/StaffController.php:138
msgid "The staff member has been deleted"
msgstr "El miembro del personal ha sido eliminado"

#: Controller/CustomersController.php:97
#: Plugin/Shopify/View/Elements/address_inputs.ctp:39
#: Plugin/Widgets/View/Elements/CheckoutWidgets/address_inputs.ctp:42
#: View/Elements/B2bCarts/custom_address_inputs.ctp:35
#: View/Elements/Users/<USER>/profile_store_associate.ctp:21;22
#: View/Staff/view.ctp:105 View/Users/<USER>
#: View/Users/<USER>
msgid "First Name"
msgstr "Nombre"

#: Controller/CustomersController.php:98
#: Plugin/Shopify/View/Elements/address_inputs.ctp:50
#: Plugin/Widgets/View/Elements/CheckoutWidgets/address_inputs.ctp:47
#: View/Elements/B2bCarts/custom_address_inputs.ctp:40
#: View/Elements/Users/<USER>/profile_store_associate.ctp:29;30
#: View/Staff/view.ctp:106 View/Users/<USER>
#: View/Users/<USER>
msgid "Last Name"
msgstr "Apellido"

#: Controller/CustomersController.php:99
msgid "E-Mail"
msgstr "Correo Electrónico"

#: Controller/CustomersController.php:100
msgid "Marketing"
msgstr "Marketing"

#: Controller/CustomersController.php:101 Controller/ReportsController.php:139
msgid "Language"
msgstr ""

#: Controller/CustomersController.php:102
msgid "Street 1"
msgstr "Calle 1"

#: Controller/CustomersController.php:103
msgid "Street 2"
msgstr "Calle 2"

#: Controller/CustomersController.php:104
#: Controller/OrdersController.php:342;3818
#: Controller/ReportsController.php:141 Controller/RetailersController.php:605
#: Plugin/Shopify/View/Elements/address_inputs.ctp:95
#: Plugin/Widgets/View/Elements/CheckoutWidgets/address_inputs.ctp:68
#: View/Elements/B2bCarts/custom_address_inputs.ctp:64
#: View/Elements/Users/<USER>/profile_brand_retailer.ctp:123;124
#: View/Elements/Users/<USER>/profile_store_associate.ctp:60;61
#: View/Users/<USER>
msgid "City"
msgstr "Ciudad"

#: Controller/CustomersController.php:105
#: Plugin/Shopify/View/Elements/address_inputs.ctp:135
#: Plugin/Widgets/View/Elements/CheckoutWidgets/address_inputs.ctp:95
#: View/UserTaxes/edit_registration.ctp:15 View/UserTaxes/registrations.ctp:13
msgid "State"
msgstr "Estado"

#: Controller/CustomersController.php:106
msgid "ZIP/Postal Code"
msgstr "Código Postal"

#: Controller/CustomersController.php:107
#: Controller/RetailersController.php:617
#: View/Elements/B2bCarts/custom_address_inputs.ctp:96
#: View/Elements/Users/<USER>/profile_brand_retailer.ctp:162
msgid "Telephone"
msgstr "Teléfono"

#: Controller/CustomersController.php:108
#: View/Elements/Inventory/ajax_retailerproducts.ctp:6
msgid "# of Orders"
msgstr "# de Pedidos"

#: Controller/CustomersController.php:109
msgid "Last Order"
msgstr "Último Pedido"

#: Controller/CustomersController.php:110
msgid "Total Shipping"
msgstr "Total de Evnío"

#: Controller/CustomersController.php:111
msgid "Total Taxes"
msgstr "Total de Impuestos"

#: Controller/CustomersController.php:112
msgid "Total Spent"
msgstr "Total Gastado"

#: Controller/DashboardsController.php:40 View/Dashboards/index.ctp:20
#: View/Helper/SidebarHelper.php:88
msgid "Dashboard"
msgstr "Tablero"

#: Controller/DealerOrderRefundsController.php:191
msgid "Insufficient balance to perform refund, contact %s."
msgstr ""

#: Controller/DiscountsController.php:166;201
#: Plugin/Shopify/Controller/ShopifyController.php:401
#: Plugin/Shopify/View/Shopify/checkout_page.ctp:333
#: Plugin/Woocommerce/Controller/WoocommerceController.php:930
msgid "Invalid discount code"
msgstr "Código de descuento inválido"

#: Controller/DiscountsController.php:173
msgid "The discount code has been saved."
msgstr ""

#: Controller/DiscountsController.php:177
msgid "The discount code could not be saved. Please, try again."
msgstr ""

#: Controller/DiscountsController.php:206
msgid "The discount code could not be deleted. Please, try again."
msgstr ""

#: Controller/DiscountsController.php:209
msgid "The discount code has been deleted."
msgstr ""

#: Controller/DiscountsController.php:320
msgid "Discount ID"
msgstr "ID de Descuento"

#: Controller/DiscountsController.php:321 View/ApiClients/form.ctp:35
#: View/ApiClients/index.ctp:28
#: View/ShippingZones/ajax_tax_override_table.ctp:10 View/Staff/index.ctp:23
#: View/Warehouses/index.ctp:22
msgid "Name"
msgstr "Nombre"

#: Controller/DiscountsController.php:322
#: View/Elements/Discounts/manage_discounts/discount_code_card.ctp:73
msgid "Code"
msgstr "Código"

#: Controller/DiscountsController.php:323
msgid "Used"
msgstr "Usado"

#: Controller/DiscountsController.php:324
msgid "Start"
msgstr "Comienzo"

#: Controller/DiscountsController.php:325
msgid "End"
msgstr "Fin"

#: Controller/DiscountsController.php:326 View/B2bCarts/index.ctp:87
#: View/ShippingZones/ajax_tax_override_table.ctp:11
msgid "Type"
msgstr "Tipo"

#: Controller/DiscountsController.php:327 Controller/ReportsController.php:144
#: Lib/TimelineEvent.php:251 View/Elements/branch.ctp:39
#: View/Elements/latest.ctp:16
#: View/Elements/Inventory/ajax_retailerproducts.ctp:7
#: View/Products/product_collections.ctp:134 View/RetailerCredits/index.ctp:221
#: View/Products/ajax_browse_products.ctp:15
#: View/Elements/Retailers/display.ctp:32
msgid "Status"
msgstr "Estado"

#: Controller/FulfillmentsController.php:168;272
msgid "Selected distributor is not assigned to selected warehouse."
msgstr ""

#: Controller/InventoryController.php:86;283 View/Elements/branch.ctp:44
#: View/Elements/Inventory/ajax_index.ctp:19
#: View/Elements/Inventory/ajax_retailerproducts.ctp:8
#: View/Helper/SidebarHelper.php:211 View/Inventory/index.ctp:51;62
msgid "Inventory"
msgstr "Inventario"

#: Controller/InventoryController.php:814
msgid "No inventory items found."
msgstr ""

#: Controller/InventoryController.php:944
#: Controller/ProductsController.php:1072
msgid "Import file is empty"
msgstr ""

#: Controller/InventoryController.php:990;1154
#: Controller/ProductsController.php:1081;1233
msgid "Import file validation errors"
msgstr ""

#: Controller/InventoryController.php:1002
msgid "Import file missing valid warehouse columns"
msgstr ""

#: Controller/InventoryController.php:1055
msgid "Inventory items successfully uploaded"
msgstr ""

#: Controller/InventoryController.php:962
msgid "Import file missing required column: %s"
msgid_plural "Import file missing required columns: %s"
msgstr[0] ""
msgstr[1] ""

#: Controller/InventoryController.php:1104
msgid "Import file requires a valid value for %s in row: %s"
msgid_plural "Import file requires valid values for %s in rows: %s"
msgstr[0] ""
msgstr[1] ""

#: Controller/ManufacturersController.php:60 View/Helper/SidebarHelper.php:381
msgid "Brands"
msgstr "Marcas"

#: Controller/OrderCommentsController.php:51;83
msgid "Invalid order comment"
msgstr "Comentario de pedido inválido"

#: Controller/OrderCommentsController.php:74
msgid "The order comment has been saved."
msgstr "El comentario del pedido se guardó con éxito."

#: Controller/OrderCommentsController.php:102
msgid "The order comment has been deleted."
msgstr "El comentario del pedido ha sido eliminado."

#: Controller/OrdersController.php:208;3655
#: Controller/ReportsController.php:1797;2139
#: View/Elements/Orders/ajax_customer_index.ctp:28
msgid "Order ID"
msgstr "ID del Pedido"

#: Controller/OrdersController.php:211;3658
msgid "eCom ID"
msgstr "ID de comercio electrónico"

#: Controller/OrdersController.php:214;3661
#: Controller/ReportsController.php:1806
#: View/Elements/Orders/order_details_panel.ctp:79;90
#: View/InvoicePdf/generate_invoice.ctp:61
msgid "Internal Order #"
msgstr "Pedido Interno #"

#: Controller/OrdersController.php:217;3673
#: Controller/ReportsController.php:1816;2154 View/Elements/latest.ctp:18
#: View/Elements/Orders/ajax_retailerspurchaseorders.ctp:99
#: View/InvoicePdf/generate_invoice.ctp:52
#: View/Orders/ajax_dealerorders_index.ctp:46
msgid "Order Date"
msgstr "Fecha de la Orden"

#: Controller/OrdersController.php:220;3667
#: Controller/ReportsController.php:2148
#: View/Elements/Orders/ajax_customer_index.ctp:27
#: View/Helper/OrdersHelper.php:306 View/Orders/dealerorders_index.ctp:187
#: View/Orders/index.ctp:181
msgid "Order Status"
msgstr "Estado del Pedido"

#: Controller/OrdersController.php:223 View/Elements/latest.ctp:26
msgid "Fulfill Type"
msgstr "Tipo de Cumplido"

#: Controller/OrdersController.php:226;3670
#: View/Elements/Orders/ajax_index.ctp:36
#: View/Elements/Orders/ajax_retailerspurchaseorders.ctp:102
#: View/Helper/OrdersHelper.php:321 View/Orders/ajax_dealerorders_index.ctp:57
#: View/Orders/dealerorders_index.ctp:188 View/Orders/index.ctp:182
msgid "Fulfillment Status"
msgstr "Estado de Cumplimiento"

#: Controller/OrdersController.php:229
#: Controller/ReportsController.php:1828;2012;2166
#: View/B2bCarts/create_cart.ctp:57;61;224 View/B2bCarts/index.ctp:83
#: View/Elements/latest.ctp:21 View/Elements/Layouts/modals.ctp:33
#: View/Elements/Orders/ajax_index.ctp:40
#: View/Orders/ajax_dealerorders_index.ctp:49 View/Users/<USER>
#: View/Users/<USER>
msgid "Retailer"
msgstr "Tienda"

#: Controller/OrdersController.php:232;3691
#: Controller/ReportsController.php:1834 Model/User.php:732
#: View/B2bCarts/index.ctp:85
#: View/Elements/B2bCarts/custom_address_inputs.ctp:105
#: View/Orders/ajax_dealerorders_index.ctp:54 View/Retailers/edit.ctp:30
#: View/Retailers/view.ctp:132
msgid "Account ID"
msgstr "ID de la Cuenta"

#: Controller/OrdersController.php:235;3676 View/Retailers/edit.ctp:120
#: View/Warehouses/add.ctp:22 View/Warehouses/index.ctp:24
msgid "Distributor"
msgstr "Distribuidor"

#: Controller/OrdersController.php:247;3715 View/Retailers/price_tier.ctp:54
msgid "Currency"
msgstr "Moneda"

#: Controller/OrdersController.php:253;3697
#: Controller/ProductsController.php:3047
#: View/Products/product_collections.ctp:133
#: View/Products/ajax_browse_products.ctp:15
#: View/Elements/Retailers/display.ctp:30
msgid "Product Title"
msgstr "Título del Producto"

#: Controller/OrdersController.php:256;3700
#: Controller/ReportsController.php:146
msgid "Product SKU"
msgstr "SKU del Producto"

#: Controller/OrdersController.php:262;3706
#: Plugin/Widgets/View/CatalogueWidgets/index.ctp:59
#: Controller/ReportsController.php:280;368;1175;1293
msgid "Category"
msgstr "Categoría"

#: Controller/OrdersController.php:266 Controller/ReportsController.php:148
#: Plugin/Widgets/View/CatalogueWidgets/view.ctp:121
#: Plugin/Widgets/View/Elements/CheckoutWidgets/order_summary.ctp:17
#: Plugin/Widgets/View/Layouts/catalogue_widgets.ctp:97
#: Plugin/Widgets/View/LocatorWidgets/products.ctp:48
#: View/B2bCarts/index.ctp:93
#: View/Elements/B2bCarts/b2b_order_product_table.ctp:127
#: View/Elements/Discounts/manage_discounts/discount_rule_card.ctp:74;157
#: View/Elements/Discounts/manage_discounts/discount_rule_card/auto_add_y_row.ctp:27
#: View/InvoicePdf/generate_invoice.ctp:121
msgid "Quantity"
msgstr "Cantidad"

#: Controller/OrdersController.php:271
#: Controller/ReportsController.php:1178;1297
msgid "Gross Sales"
msgstr "Ventas brutas"

#: Controller/OrdersController.php:278
#: View/Discounts/manage_discounts.ctp:23;30 View/Helper/SidebarHelper.php:278
#: Controller/ReportsController.php:1179;1298
msgid "Discounts"
msgstr "Descuentos"

#: Controller/OrdersController.php:285
msgid "Refunds"
msgstr "Reembolsos"

#: Controller/OrdersController.php:292
#: Controller/ReportsController.php:1180;1299
msgid "Net Sales"
msgstr "Ventas Netas"

#: Controller/OrdersController.php:299
#: Controller/Component/NotificationLogicComponent.php:1362
#: Plugin/Shopify/View/Shopify/checkout_page.ctp:449
#: Plugin/Widgets/Controller/CheckoutWidgetsController.php:691
#: View/DealerOrderRefunds/refund.ctp:109 View/Elements/branch.ctp:74
#: View/Elements/Orders/dealerorder_invoice_totals.ctp:156
#: View/Elements/Orders/order_invoice_totals.ctp:97
#: View/InvoicePdf/generate_invoice.ctp:157 View/OrderRefunds/refund.ctp:140
#: Controller/ReportsController.php:1181;1300
msgid "Tax"
msgstr "Impuesto"

#: Controller/OrdersController.php:306;2049
#: Controller/ReportsController.php:1852
#: Controller/Component/NotificationLogicComponent.php:1361 Model/User.php:3444
#: Plugin/Shopify/Controller/ShopifyController.php:1304
#: Plugin/Shopify/View/Shopify/checkout_page.ctp:60;133;448
#: Plugin/Shopify/View/Shopify/payment_page.ctp:158;308;364;462;538;546;648;690;1443
#: Plugin/Widgets/Controller/CheckoutWidgetsController.php:687
#: View/DealerOrderRefunds/refund.ctp:95
#: View/Elements/B2bCarts/b2b_order_product_table.ctp:53;57
#: View/Elements/Orders/dealerorder_invoice_totals.ctp:136
#: View/Elements/Orders/order_invoice_totals.ctp:96
#: View/InvoicePdf/generate_invoice.ctp:156
#: Plugin/Shopify/View/templates/page.success.liquid.ctp:171;392
msgid "Shipping"
msgstr "Envío"

#: Controller/OrdersController.php:313
#: Controller/ReportsController.php:143;1864
#: Controller/Component/NotificationLogicComponent.php:1363
#: Plugin/Shopify/Controller/ShopifyController.php:1308
#: Plugin/Shopify/View/Elements/payment_page/instore_dealer_table_header.ctp:44
#: Plugin/Shopify/View/Shopify/checkout_page.ctp:164;450
#: Plugin/Shopify/View/Shopify/payment_page.ctp:191;336;370;495;654;696
#: Plugin/Widgets/Controller/CheckoutWidgetsController.php:696
#: View/B2bCarts/index.ctp:90 View/Elements/latest.ctp:27
#: View/Elements/B2bCarts/b2b_order_product_table.ctp:155
#: View/Elements/B2bCarts/existing_cart_row.ctp:40
#: View/Elements/Orders/ajax_customer_index.ctp:32
#: View/Elements/Orders/ajax_index.ctp:44
#: View/Elements/Orders/ajax_retailerspurchaseorders.ctp:103
#: View/Elements/Orders/order_invoice_totals.ctp:164
#: View/InvoicePdf/generate_invoice.ctp:161
#: View/Orders/ajax_dealerorders_index.ctp:58
#: View/RetailerCredits/make_payment.ctp:94
#: Plugin/Shopify/View/templates/page.success.liquid.ctp:199
msgid "Total"
msgstr "Total"

#: Controller/OrdersController.php:320
msgid "Service Fee"
msgstr "Tarifa de Servicio"

#: Controller/OrdersController.php:327
#: View/Elements/Orders/dealerorder_invoice_totals.ctp:83
#: View/Elements/Orders/order_invoice_totals.ctp:86
msgid "Processing fees"
msgstr "Tarifas de procesamiento"

#: Controller/OrdersController.php:330
#: View/Elements/Orders/dealerorder_invoice_totals.ctp:84
#: View/Elements/Orders/order_invoice_totals.ctp:87
msgid "ShipEarly fees"
msgstr "Tarifas de ShipEarly"

#: Controller/OrdersController.php:333 View/Elements/latest.ctp:25
#: View/Elements/Orders/ajax_index.ctp:38
#: View/Orders/ajax_dealerorders_index.ctp:56
msgid "Customer"
msgstr "Cliente"

#: Controller/OrdersController.php:336
msgid "Customer Email"
msgstr "Correo electrónico del Cliente"

#: Controller/OrdersController.php:339
msgid "Shipping Address"
msgstr "Dirección de envío"

#: Controller/OrdersController.php:345;3821
#: Controller/ReportsController.php:142 Controller/RetailersController.php:608
#: View/Elements/Users/<USER>/profile_brand_retailer.ctp:142
#: View/Elements/Users/<USER>/profile_store_associate.ctp:87
#: View/Users/<USER>
msgid "State/Province"
msgstr "Estado/Provincia"

#: Controller/OrdersController.php:348
msgid "Zip Code"
msgstr "Código Postal"

#: Controller/OrdersController.php:351;3824
#: Controller/RetailersController.php:614
#: Plugin/Shopify/View/Elements/address_inputs.ctp:114
#: Plugin/Widgets/View/Elements/CheckoutWidgets/address_inputs.ctp:80
#: View/BankAccounts/add.ctp:31
#: View/Elements/Users/<USER>/profile_brand_retailer.ctp:131
#: View/Elements/Users/<USER>/profile_store_associate.ctp:76
#: View/Users/<USER>
msgid "Country"
msgstr "País"

#: Controller/OrdersController.php:354 View/Orders/verification_code.ctp:26
msgid "Billing Name"
msgstr "Nombre de Facturación"

#: Controller/OrdersController.php:357
#: Plugin/Shopify/View/Shopify/payment_page.ctp:742
#: Plugin/Widgets/View/CheckoutWidgets/payment_details.ctp:108
msgid "Billing Address"
msgstr "Domicilio de Facturación"

#: Controller/OrdersController.php:360 View/Orders/verification_code.ctp:30
msgid "Billing City"
msgstr "Ciudad de Facturación"

#: Controller/OrdersController.php:363
msgid "Billing State/Province"
msgstr "Estado/Provincia de Facturación"

#: Controller/OrdersController.php:366
msgid "Billing Zip Code"
msgstr "Código Postal de Facturación"

#: Controller/OrdersController.php:369
msgid "Billing Country"
msgstr "País de Facturación"

#: Controller/OrdersController.php:375;3842 View/Elements/latest.ctp:28
#: View/Elements/Orders/ajax_index.ctp:45
#: View/Elements/Orders/order_tags.ctp:52
#: View/Orders/ajax_dealerorders_index.ctp:59 View/Orders/index.ctp:179
#: View/Products/view.ctp:639 View/Retailers/edit.ctp:169
msgid "Tags"
msgstr "Etiquetas"

#: Controller/OrdersController.php:611;1254
msgid "Failed placing the wholesale order for %s in the ecommerce platform."
msgstr "No se pudo realizar el pedido mayorista de %s en la plataforma de comercio electrónico."

#: Controller/OrdersController.php:1262
msgid "Wholesale order for %s placed in the ecommerce platform."
msgstr "Pedido al por mayor de %s realizado en la plataforma de comercio electrónico."

#: Controller/OrdersController.php:1306 View/Orders/verification_code.ctp:103
msgid "Incorrect verification code"
msgstr "Código de Verificación Incorrecto"

#: Controller/OrdersController.php:1328
msgid "There was an error uploading your attachment. Please, try again."
msgstr ""

#: Controller/OrdersController.php:1367;2184
msgid "There was an error capturing the payment for order %s. Please, try again."
msgstr "Hubo un error al capturar el pago del pedido %s. Inténtalo de nuevo."

#: Controller/OrdersController.php:1372
msgid "Order %s has been marked as delivered"
msgstr ""

#: Controller/OrdersController.php:1377
msgid "Failed to mark order %s as delivered. Please try again."
msgstr ""

#: Controller/OrdersController.php:1976
msgid "Order not found."
msgstr ""

#: Controller/OrdersController.php:2001
msgid "Unable to save order tag name: %s"
msgstr ""

#: Controller/OrdersController.php:2029
msgid "Tags updated for the order."
msgstr ""

#: Controller/OrdersController.php:2031
msgid "Unable to save order tags."
msgstr ""

#: Controller/OrdersController.php:2051
#: Plugin/Shopify/Controller/ShopifyController.php:1318
#: Plugin/Shopify/View/Shopify/payment_page.ctp:576;584;1439
#: Plugin/Widgets/Controller/CheckoutWidgetsController.php:716
#: Plugin/Shopify/View/templates/page.success.liquid.ctp:388
msgid "Installation"
msgstr "Instalación"

#: Controller/OrdersController.php:2194
msgid "There was an error capturing the payment for order %s in %s."
msgstr "Hubo un error al capturar el pago del pedido %s en %s."

#: Controller/OrdersController.php:2198
msgid "Order %s has been captured"
msgstr "El pedido %s ha sido capturado"

#: Controller/OrdersController.php:2298
msgid "Unable to cancel order %s"
msgstr ""

#: Controller/OrdersController.php:2307
msgid "Failed to cancel payment for order %s"
msgstr ""

#: Controller/OrdersController.php:2316
msgid "Failed to cancel order %s"
msgstr ""

#: Controller/OrdersController.php:2324
msgid "Order %s has been cancelled"
msgstr ""

#: Controller/OrdersController.php:2670
msgid "The verification code has been emailed to the customer. It may take up to 1 minute for them to receive it."
msgstr "El código de verificación ha sido enviado por correo electrónico al cliente. Pueden tardar hasta 1 minuto en recibirlo."

#: Controller/OrdersController.php:2675
msgid "Please wait at least 1 minute for the customer to receive their verification code email before attempting to send it again."
msgstr "Espere al menos 1 minuto para que el cliente reciba el correo electrónico con el código de verificación antes de intentar enviarlo nuevamente."

#: Controller/OrdersController.php:2730
msgid "The message has been sent to the consumer."
msgstr "El mensaje ha sido enviado al consumidor."

#: Controller/OrdersController.php:3664
#: View/Elements/Discounts/manage_discounts/discount_code_card.ctp:50
#: View/Orders/dealerorders_index.ctp:180 View/Orders/index.ctp:174
#: View/RetailerCredits/index.ctp:214
#: View/Reports/view.ctp:78
msgid "Order Type"
msgstr "Tipo de Pedido"

#: Controller/OrdersController.php:3688 Controller/RetailersController.php:518
msgid "Retailer Name"
msgstr "Nombre de la Tienda"

#: Controller/OrdersController.php:3709
msgid "Retail Qty"
msgstr "Cantidad en Tienda"

#: Controller/OrdersController.php:3718
msgid "Retail Price"
msgstr "Precio en Tienda"

#: Controller/OrdersController.php:3725
msgid "Retail Discount"
msgstr "Descuento en Tienda"

#: Controller/OrdersController.php:3733
msgid "Retail Tax"
msgstr "Impuesto en Tienda"

#: Controller/OrdersController.php:3740
msgid "Retail Shipping"
msgstr "Envio en Tienda"

#: Controller/OrdersController.php:3747
msgid "Retail Total"
msgstr "Total en Tienda"

#: Controller/OrdersController.php:3762
msgid "Invoice Qty"
msgstr "Cantidad en Remito"

#: Controller/OrdersController.php:3768
msgid "Invoice Price"
msgstr "Precio en Remito"

#: Controller/OrdersController.php:3775
msgid "B2B Shipping"
msgstr "Envío B2B"

#: Controller/OrdersController.php:3782
msgid "B2B Discount"
msgstr ""

#: Controller/OrdersController.php:3789 View/Retailers/edit.ctp:42
msgid "B2B Tax"
msgstr "Impuesto B2B"

#: Controller/OrdersController.php:3796
msgid "Wholesale Total"
msgstr "Total en Almacén"

#: Controller/OrdersController.php:3812 Controller/RetailersController.php:602
#: Plugin/Shopify/View/Elements/address_inputs.ctp:72
#: Plugin/Widgets/View/Elements/CheckoutWidgets/address_inputs.ctp:57
#: View/Elements/Orders/order_details_panel.ctp:123
msgid "Address"
msgstr "Dirección"

#: Controller/OrdersController.php:3815
msgid "Address 2"
msgstr "Dirección 2"

#: Controller/OrdersController.php:3827
#: Plugin/Shopify/View/Elements/address_inputs.ctp:146
#: Plugin/Widgets/View/Elements/CheckoutWidgets/address_inputs.ctp:100
#: View/Elements/Users/<USER>/profile_brand_retailer.ctp:154
#: View/Users/<USER>
msgid "Zip/Postal Code"
msgstr "Código Postal"

#: Controller/OrdersController.php:3830 View/Users/<USER>
#: View/Users/<USER>
msgid "Phone Number"
msgstr "Teléfono"

#: Controller/OrdersController.php:3901;372
#: View/Elements/Orders/ajax_index.ctp:33 View/Helper/OrdersHelper.php:336
#: View/Orders/index.ctp:183 View/Elements/credit_terms_edit.ctp:108
msgid "Payment Status"
msgstr "Estado del Pago"

#: Controller/OrdersController.php:3836 View/Helper/OrdersHelper.php:351
#: View/RetailerCredits/make_payment.ctp:98
msgid "Payment Type"
msgstr "Tipo de Pago"

#: Controller/OrdersController.php:3839 View/RetailerCredits/index.ctp:215
msgid "Term"
msgstr "Término"

#: Controller/PagesController.php:1189 Controller/UsersController.php:1012
msgid "The old password you entered is incorrect. Please try again."
msgstr ""

#: Controller/PagesController.php:1199
#: Controller/UsersController.php:947;1022;1418;2230;3114;3490
msgid "Your account has been updated successfully"
msgstr "Su cuenta ha sido actualizada con éxito"

#: Controller/ProductStateFeesController.php:129;290
msgid "No eCommerce shipping countries selected. Make sure they are configured and try again."
msgstr ""

#: Controller/ProductStateFeesController.php:314
#: View/InvoicePdf/generate_invoice.ctp:99
msgid "Item Description"
msgstr "Descripción del Ítem"

#: Controller/ProductsController.php:143
msgid "%s Retail Price"
msgstr "%s Precio de Tienda"

#: Controller/ProductsController.php:148
msgid "%s Base Price"
msgstr "%s Precio Base"

#: Controller/ProductsController.php:158
msgid "%s STS"
msgstr "%s Envío a la Tienda"

#: Controller/ProductsController.php:163
msgid "%s Commission"
msgstr ""

#: Controller/ProductsController.php:171;218;2797
#: View/Helper/SidebarHelper.php:142 View/Products/collections.ctp:49
#: View/ShippingZones/ajax_tax_override_table.ctp:13
msgid "Products"
msgstr "Productos"

#: Controller/ProductsController.php:424
msgid "Product not found where id=%s"
msgstr "No se encontró producto donde id=%s"

#: Controller/ProductsController.php:1095
msgid "Product details successfully uploaded"
msgstr ""

#: Controller/ProductsController.php:1123
msgid "Import file requires the %s column"
msgstr ""

#: Controller/ProductsController.php:1191
msgid "Import file validation errors for %s groups"
msgstr ""

#: Controller/ProductsController.php:2184;2270;2367;2388
msgid "Collection not found"
msgstr ""

#: Controller/ProductsController.php:2187
msgid "You do not have permission to view this collection"
msgstr ""

#: Controller/ProductsController.php:2279;2336
msgid "Product not found"
msgstr ""

#: Controller/ProductsController.php:2284;2341
msgid "Product title not found"
msgstr ""

#: Controller/ProductsController.php:2312
msgid "Some variants could not be added to the collection."
msgstr ""

#: Controller/ProductsController.php:2319
msgid "All variants added to collection successfully."
msgstr ""

#: Controller/ProductsController.php:2391
msgid "You do not have permission to edit this collection"
msgstr ""

#: Controller/ProductsController.php:2409
msgid "Product order updated successfully."
msgstr ""

#: Controller/ProductsController.php:2416
msgid "Failed to update some product orders. Please try again."
msgstr ""

#: Controller/ProductsController.php:2433
msgid "Some collections were not found. Please try again."
msgstr ""

#: Controller/ProductsController.php:2449
msgid "Failed to update some collection orders. Please try again."
msgstr ""

#: Controller/ProductsController.php:2457
msgid "Collection order updated successfully."
msgstr ""

#: Controller/ProductsController.php:2471
msgid "Invalid input data."
msgstr ""

#: Controller/ProductsController.php:2484
msgid "Collection title updated successfully."
msgstr ""

#: Controller/ProductsController.php:2484
msgid "Failed to update title. Please try again."
msgstr ""

#: Controller/ProductsController.php:2498;2566
msgid "Image not found."
msgstr ""

#: Controller/ProductsController.php:2508
msgid "Product not found."
msgstr ""

#: Controller/ProductsController.php:2539
msgid "Main image updated successfully."
msgstr ""

#: Controller/ProductsController.php:2545
msgid "Failed to update main image."
msgstr ""

#: Controller/ProductsController.php:2591
#: Controller/UsersController.php:1451;1484
msgid "Image deleted successfully."
msgstr ""

#: Controller/ProductsController.php:2597
#: Controller/UsersController.php:1457;1490
msgid "Failed to delete image."
msgstr ""

#: Controller/ProductsController.php:2643
msgid "Product title not found."
msgstr ""

#: Controller/ProductsController.php:2694
msgid "Failed to save product image."
msgstr ""

#: Controller/ProductsController.php:2755
msgid "Failed to update product."
msgstr ""

#: Controller/ProductsController.php:2763
msgid "The product has been updated."
msgstr ""

#: Controller/ProductsController.php:3044
msgid "Product ID"
msgstr "ID de Producto"

#: Controller/ProductsController.php:3050
#: View/Elements/Layouts/profile_dropdown.ctp:50
#: View/Users/<USER>
msgid "Product Categories"
msgstr "Categorías de Productos"

#: Controller/ProductsController.php:3129
#: View/Elements/Products/catalogue_variants.ctp:18
msgid "Variant"
msgstr "Variante"

#: Controller/ProductsController.php:1137
msgid "Import file requires a value for %s or %s in row: %s"
msgid_plural "Import file requires values for %s or %s in rows: %s"
msgstr[0] ""
msgstr[1] ""

#: Controller/ProductsController.php:1160
msgid "Import file contains an invalid %s value in row: %s"
msgid_plural "Import file contains invalid %s values in rows: %s"
msgstr[0] ""
msgstr[1] ""

#: Controller/ProductsController.php:1196
msgctxt "Import file validation errors"
msgid "\"%s\" for handle %s"
msgid_plural "\"%s\" for handles %s"
msgstr[0] ""
msgstr[1] ""

#: Controller/ProductsController.php:1239
msgctxt "Import file validation errors"
msgid "\"%s\" for row %s"
msgid_plural "\"%s\" for rows %s"
msgstr[0] ""
msgstr[1] ""

#: Controller/ReportsController.php:50;65 View/Helper/SidebarHelper.php:370
msgid "Reports"
msgstr "Reportes"

#: Controller/ReportsController.php:136
msgid "Checkout Code"
msgstr "Código de pago"

#: Controller/ReportsController.php:137
#: View/Elements/Orders/ajax_customer_index.ctp:29
#: View/Elements/Orders/ajax_index.ctp:32
msgid "Date"
msgstr "Fecha"

#: Controller/ReportsController.php:138
msgid "Customer Name"
msgstr "Nombre del Cliente"

#: Controller/ReportsController.php:140;2011
#: Plugin/Shopify/View/Shopify/checkout_page.ctp:192;198
#: Plugin/Widgets/View/CheckoutWidgets/delivery_methods.ctp:102;107
#: View/Branchs/store_permission.ctp:27 View/Staff/index.ctp:24
#: View/Staff/view.ctp:107 View/Users/<USER>
msgid "Email"
msgstr "Correo Electrónico"

#: Controller/ReportsController.php:145
msgid "Product Name"
msgstr "Nombre del Producto"

#: Controller/ReportsController.php:147
#: View/Elements/Orders/order_products_table/dealer_order.ctp:15
#: View/Elements/Orders/order_products_table/need_to_confirm.ctp:18
#: View/Elements/Orders/order_products_table/processing.ctp:27;79
#: View/Elements/Orders/order_products_table/purchase_order.ctp:38;83;117
#: View/Elements/Products/catalogue_variants.ctp:25
#: View/Elements/Products/product_grid_item.ctp:59
#: View/Elements/Products/product_list_item.ctp:45
#: View/InvoicePdf/generate_invoice.ctp:111 View/Products/view.ctp:404;522
msgid "Unit Price"
msgstr "Precio Unitario"

#: View/Elements/Products/productlist_retailer.ctp:118
msgid "Margin"
msgstr "Margen"

#: Controller/ReportsController.php:149
#: View/Elements/Products/catalogue_variants.ctp:26 View/Products/view.ctp:523
msgid "Line Total"
msgstr "Total de Línea"

#: Controller/ReportsController.php:1822 View/Elements/latest.ctp:19
#: View/Elements/Orders/ajax_retailerspurchaseorders.ctp:100
#: View/InvoicePdf/generate_invoice.ctp:55
#: View/Orders/ajax_dealerorders_index.ctp:47
msgid "Ship Date"
msgstr "Fecha de envío"

#: Controller/ReportsController.php:1840;2172
msgid "Retail"
msgstr ""

#: Controller/ReportsController.php:1846 Utility/OrderType.php:100
msgid "Wholesale"
msgstr "Distribuidor"

#: Controller/ReportsController.php:1858
msgid "B2B Taxes"
msgstr ""

#: Controller/ReportsController.php:1870 Utility/OrderPaymentStatus.php:55
#: Utility/RetailerCreditStatus.php:23
#: View/Elements/Orders/dealerorder_invoice_totals.ctp:235
#: View/Elements/Orders/order_invoice_totals.ctp:280
#: View/RetailerCredits/index.ctp:217 View/RetailerCredits/make_payment.ctp:95
msgid "Paid"
msgstr "Pagado"

#: Utility/OrderStatus.php:238
msgctxt "order_status"
msgid "Paid"
msgstr ""

#: Controller/ReportsController.php:1876 Lib/TimelineEvent.php:113
#: View/Elements/Orders/order_invoice_totals.ctp:200
#: View/Elements/Orders/order_products_table/dealerorder_invoice.ctp:62
#: View/Elements/Orders/order_products_table/invoice.ctp:66
#: View/Elements/Orders/order_products_table/processing.ctp:75
#: View/Elements/Orders/order_products_table/purchase_order.ctp:110
#: View/Elements/Orders/order_products_table/wholesale_invoice.ctp:68
msgid "Refunded"
msgstr "Reembolsado"

#: Utility/OrderStatus.php:221;240;256
msgctxt "order_status"
msgid "Refunded"
msgstr ""

#: Controller/ReportsController.php:1882
msgid "Invoice"
msgstr ""

#: Controller/ReportsController.php:2010
msgid "Associate"
msgstr "Asociado"

#: Controller/ReportsController.php:2017 View/B2bCarts/create_cart.ctp:34;38
#: View/B2bCarts/index.ctp:82 View/Elements/Layouts/modals.ctp:32
#: View/Elements/Orders/order_details_panel.ctp:31 View/Inventory/index.ctp:95
#: View/Orders/dealerorders_index.ctp:182 View/Orders/index.ctp:176
#: View/Users/<USER>
#: View/Reports/view.ctp:117
msgid "Brand"
msgstr "Marca"

#: Controller/ReportsController.php:2021
#: Plugin/Widgets/View/Elements/CheckoutWidgets/billing_summary.ctp:30
#: View/Elements/Orders/order_details_panel.ctp:111
#: View/InvoicePdf/generate_invoice.ctp:172
#: View/Orders/dealerorders_index.ctp:189 View/Orders/verification_code.ctp:27
msgid "Payment Method"
msgstr "Método de Pago"

#: Controller/ReportsController.php:2022
msgid "Reference Number"
msgstr "Número de Referencia"

#: Controller/ReportsController.php:2023
msgid "# of Transactions"
msgstr "# de Transacciones"

#: Controller/ReportsController.php:2024
msgid "Payment Due"
msgstr "Vencimiento del Pago"

#: Controller/ReportsController.php:2160
msgid "Delivery Date"
msgstr ""

#: Controller/ReportsController.php:2178
msgid "Commission"
msgstr ""

#: Controller/ReportsController.php:464
#: View/Reports/index.ctp:13
#: View/Reports/index_salesrep.ctp:13
msgid "Sales By Product"
msgstr "Ventas por producto"

#: Controller/ReportsController.php:672
#: View/Reports/index.ctp:16
#: View/Reports/index_salesrep.ctp:16
msgid "Sales By Product SKU"
msgstr "Ventas por SKU de producto"

#: Controller/ReportsController.php:229
#: View/Reports/index.ctp:45
#: View/Reports/index_salesrep.ctp:33
msgid "Views By Product"
msgstr "Vistas por producto"

#: Controller/ReportsController.php:317
#: View/Reports/index.ctp:48
#: View/Reports/index_salesrep.ctp:36
msgid "Views By Product SKU"
msgstr "Vistas por SKU del producto"

#: Controller/ReportsController.php:282;371
msgid "Views"
msgstr "Vistos"

#: Controller/ReportsController.php:283;372
msgid "Average Distance"
msgstr "Distancia promedio"

#: Controller/ReportsController.php:369;1294
msgid "Variant Title"
msgstr "Título variante"

#: Controller/ReportsController.php:370;1295
msgid "Variant SKU"
msgstr "SKU variante"

#: View/Reports/view.ctp:66
msgid "Order Currency"
msgstr "Moneda del pedido"

#: View/Reports/view.ctp:93
msgid "Account"
msgstr "Cuenta"

#: View/Reports/view.ctp:105
msgid "Sales Rep/Distributor"
msgstr "Representante de ventas / Distribuidor"

#: Controller/ReportsController.php:1177;1296
msgid "Net Quantity"
msgstr "Cantidad neta"

#: Controller/ReportsController.php:1182;1301
msgid "Total Sales"
msgstr "Ventas totales"

#: Controller/RetailerCreditsController.php:246
msgid "Payment recorded for Invoices: %s"
msgstr "Pago registrado para Facturas: %s"

#: Controller/RetailersController.php:352
msgid "Retailer settings not found"
msgstr "Configuración de tienda no encontrada"

#: Controller/RetailersController.php:363
msgid "No save data"
msgstr "No guardar datos"

#: Controller/RetailersController.php:572
msgid "Inventory Connected"
msgstr "Inventario Conectado"

#: Controller/RetailersController.php:575
msgid "Stripe Status"
msgstr "Estado de Stripe"

#: Controller/RetailersController.php:576
msgid "Activated"
msgstr ""

#: Controller/RetailersController.php:576
msgid "Deferred"
msgstr ""

#: Controller/RetailersController.php:578
msgid "Klarna Payments Status"
msgstr ""

#: Controller/RetailersController.php:581
msgid "Affirm Payments Status"
msgstr ""

#: Controller/RetailersController.php:584
msgid "Payments"
msgstr "Pagos"

#: Controller/RetailersController.php:585
msgid "Enabled"
msgstr ""

#: Controller/RetailersController.php:585
msgid "Disabled"
msgstr ""

#: Controller/RetailersController.php:587
#: View/Elements/Manufacturers/manufacturers.ctp:126
msgid "Outstanding Balance"
msgstr "Balance Sobresaliente"

#: Controller/RetailersController.php:611
msgid "ZIP"
msgstr "Código Postal"

#: Controller/RetailersController.php:623
msgid "Contact Person"
msgstr "Persona de Contacto"

#: Controller/RetailersController.php:626
msgid "Support eMail"
msgstr "Correo electrónico de soporte"

#: Controller/RetailersController.php:629
msgid "Support Telephone"
msgstr "Teléfono de Soporte"

#: Controller/ShippingZonesController.php:528
msgid "Package has been created successfully"
msgstr ""

#: Controller/ShippingZonesController.php:529
msgid "Package has been updated successfully"
msgstr ""

#: Controller/ShippingZonesController.php:539
msgid "Package has been removed"
msgstr ""

#: Controller/ShippingZonesController.php:573;584;595
#: Controller/UsersController.php:2767
msgid "Shipment Settings updated successfully"
msgstr "Configuración de Envío actualizado con éxito"

#: Controller/StaffController.php:50 View/ApiClients/form.ctp:10
#: View/Discounts/manage_discounts.ctp:22
msgid "New"
msgstr "Nuevo"

#: Controller/UsersController.php:470
msgid "Email address doesn't match the records. Please enter the Email address you gave us at the time of registration!"
msgstr ""

#: Controller/UsersController.php:490
msgid "Reset Password"
msgstr "Restablecer la contraseña"

#: Controller/UsersController.php:509
msgid "Your account details have been mailed to the email address provided."
msgstr ""

#: Controller/UsersController.php:515 View/Users/<USER>
msgid "Forgot Password"
msgstr "Olvidó su contraseña"

#: Controller/UsersController.php:671
msgid "Signup"
msgstr "Inscribirse"

#: Controller/UsersController.php:672
msgid "Register"
msgstr "Registrarse"

#: Controller/UsersController.php:757
msgid "Stripe authorization expired, please try again."
msgstr ""

#: Controller/UsersController.php:792
msgid "Your Stripe account has been connected successfully."
msgstr ""

#: Controller/UsersController.php:1328;3502
msgid "Inventory configuration"
msgstr "Configuración de Inventario"

#: Controller/UsersController.php:2206 View/Users/<USER>
msgid "Customer Support"
msgstr "Atención al Cliente"

#: Controller/UsersController.php:2281
#: View/Elements/Users/<USER>/profile_brand_retailer.ctp:174
#: View/Elements/Users/<USER>/profile_sales_rep.ctp:84
#: View/Users/<USER>
msgid "Select a Time Zone"
msgstr "Selección su Zona Horaria"

#: Controller/UsersController.php:2689
msgid "Failed to save an uploaded image"
msgstr ""

#: Controller/UsersController.php:2955
msgid "The email template could not be reset. Please, try again."
msgstr ""

#: Controller/UsersController.php:3001
#: View/Elements/Layouts/profile_dropdown.ctp:110
msgid "Payouts"
msgstr "Pagos"

#: Controller/UsersController.php:3095
msgid "Your password reset link has expired. Please try again."
msgstr ""

#: Controller/UsersController.php:3128
msgid "Shipearly subscription"
msgstr "Suscripción Shipearly"

#: Controller/UsersController.php:3210
msgid "Lightspeed authorization expired, please try again."
msgstr ""

#: Controller/UsersController.php:3228;3258;3309;3339;3383;3413
msgid "Location not found."
msgstr ""

#: Controller/UsersController.php:3235
msgid "Invalid Lightspeed account information."
msgstr ""

#: Controller/UsersController.php:3249
msgid "Your Lightspeed account has been connected successfully."
msgstr ""

#: Controller/UsersController.php:3291
msgid "LightSpeed Retail (X-Series) authorization expired, please try again."
msgstr ""

#: Controller/UsersController.php:3316
msgid "Invalid LightSpeed Retail (X-Series) account information."
msgstr ""

#: Controller/UsersController.php:3330
msgid "Your LightSpeed Retail (X-Series) account has been connected successfully."
msgstr ""

#: Controller/UsersController.php:3365
msgid "Square authorization expired, please try again."
msgstr ""

#: Controller/UsersController.php:3390
msgid "Invalid Square account information."
msgstr ""

#: Controller/UsersController.php:3404
msgid "Your Square account has been connected successfully."
msgstr ""

#: Controller/WarehousesController.php:81;120
msgid "The warehouse has been saved."
msgstr ""

#: Controller/WarehousesController.php:83;122
msgid "The warehouse could not be saved. Please, try again."
msgstr ""

#: Controller/WarehousesController.php:92;114;143
msgid "Invalid warehouse"
msgstr ""

#: Controller/WarehousesController.php:149
msgid "The warehouse has been deleted."
msgstr ""

#: Controller/WarehousesController.php:151
msgid "The warehouse could not be deleted. Please, try again."
msgstr ""

#: Controller/WsController.php:3615
msgid "Synchronization settings not found. Confirm your E-Commerce Settings."
msgstr ""

#: Controller/WsController.php:3627
msgid "Failed to add synchronization to queue"
msgstr ""

#: Controller/WsController.php:3632
msgid "Synchronization added to queue"
msgstr ""

#: Controller/Component/NotificationLogicComponent.php:1306
msgid "Confirm inventory for order %s within the next 12 hrs or a dealer order will be placed on your behalf."
msgstr "Confirme el inventario para el pedido %s dentro de las próximas 12 horas o se realizará un pedido de distribuidor en su nombre."

#: Controller/Component/NotificationLogicComponent.php:1359
#: Plugin/Shopify/View/Shopify/payment_page.ctp:114;266;420
#: Plugin/Widgets/View/Elements/CheckoutWidgets/order_summary.ctp:8
#: View/Elements/Orders/order_invoice_totals.ctp:143
msgid "Order Summary"
msgstr "Resumen del Pedido"

#: Controller/Component/NotificationLogicComponent.php:1365
#: Plugin/Shopify/Controller/ShopifyController.php:1324
#: Plugin/Shopify/View/Elements/payment_page/instore_dealer_table_header.ctp:35
#: Plugin/Shopify/View/Shopify/checkout_page.ctp:137;444
#: Plugin/Shopify/View/Shopify/payment_page.ctp:160;316;372;476;662;704
#: Plugin/Widgets/Controller/CheckoutWidgetsController.php:687
#: Plugin/Widgets/View/Layouts/catalogue_widgets.ctp:93
#: View/DealerOrderRefunds/refund.ctp:100
#: View/Elements/B2bCarts/b2b_order_product_table.ctp:160
#: View/Elements/Orders/dealerorder_invoice_totals.ctp:143
#: View/Elements/Orders/order_invoice_totals.ctp:92;144
#: View/InvoicePdf/generate_invoice.ctp:158 View/OrderRefunds/refund.ctp:112
#: View/Products/view.ctp:580
#: Plugin/Shopify/View/templates/page.success.liquid.ctp:168
msgid "Subtotal"
msgstr "Sub-total"

#: Controller/Component/NotificationLogicComponent.php:1822
msgid "View your order"
msgstr ""

#: Controller/Component/OrderLogicComponent.php:432;496
msgid "Order not found where id=%s"
msgstr "Pedido no encontrado donde id=%s"

#: Controller/Component/OrderLogicComponent.php:435;499
msgid "Invalid Stripe charge for order: %s"
msgstr "Cargo de Stripe inválido para el pedido: %s"

#: Controller/Component/UploadComponent.php:218
#: Lib/FileStorage/FileStorage.php:215
msgid "Deleted \"%s\""
msgstr "Eliminado “%s”"

#: Controller/Component/UploadComponent.php:220
#: Lib/FileStorage/FileStorage.php:217
msgid "Failed to delete \"%s\""
msgstr "Error al eliminar “%s”"

#: Controller/Component/UserLogicComponent.php:102
msgid "Your categories of interest has been updated successfully"
msgstr "Tus categorías de interés han sido actualizadas con éxito"

#: Controller/Component/UserLogicComponent.php:196
msgid "Activate Account"
msgstr "Activar la cuenta"

#: Controller/Component/UserLogicComponent.php:448
msgid "Complete Now"
msgstr "Completar Ahora"

#: Controller/Component/UserLogicComponent.php:449
msgid "You are not eligible to receive sales or payouts until your Stripe account is activated."
msgstr "No es elegible para recibir ventas o pagos hasta que se active su cuenta de Stripe."

#: Lib/TimelineEvent.php:45
msgid "%s sent an email to %s"
msgstr ""

#: Lib/TimelineEvent.php:71
msgid "Fulfilled Items"
msgstr ""

#: Lib/TimelineEvent.php:81 View/Elements/Orders/ajax_customer_index.ctp:31
#: View/Helper/OrdersHelper.php:147
msgid "Tracking"
msgstr "Seguimiento"

#: Lib/TimelineEvent.php:85
msgid "Fulfilled %u item(s) from %s."
msgstr ""

#: Lib/TimelineEvent.php:107;151
msgid "Refunded Items"
msgstr ""

#: Lib/TimelineEvent.php:119 View/OrderRefunds/refund.ctp:102
msgid "Restocking Fees"
msgstr "Tarifas de reabastecimiento"

#: Lib/TimelineEvent.php:125;163
msgid "Reason"
msgstr ""

#: Lib/TimelineEvent.php:129
msgid "Refunded Consumer %s %s"
msgstr ""

#: Lib/TimelineEvent.php:157
msgid "Refunded Shipping"
msgstr ""

#: Lib/TimelineEvent.php:168
msgid "Refunded Dealer %s %s"
msgstr ""

#: Lib/TimelineEvent.php:182
msgid "Payment captured for %.2f %s on %s"
msgstr ""

#: Lib/TimelineEvent.php:201
msgid "Delivered to the customer"
msgstr ""

#: Lib/TimelineEvent.php:215
msgid "Shipped to the customer"
msgstr ""

#: Lib/TimelineEvent.php:229
msgid "Shipped to the retailer"
msgstr ""

#: Lib/TimelineEvent.php:243
msgid "A payment of %s %.2f is being sent to %s account ending in ****%s"
msgstr ""

#: Lib/TimelineEvent.php:247
msgid "Total Payout Amount"
msgstr ""

#: Lib/TimelineEvent.php:255
msgid "Expected Arrival"
msgstr ""

#: Lib/TimelineEvent.php:255
msgid "Deposited"
msgstr ""

#: Lib/FileStorage/FileStorage.php:79
msgid "The URI string \"%s\" could not be parsed."
msgstr ""

#: Lib/FileStorage/FileStorage.php:119;209
msgid "Uri scheme \"%s://\" does not match expected \"%s://\"."
msgstr ""

#: Lib/FileStorage/FileStorage.php:148
msgid "Path %s does not exist locally"
msgstr ""

#: Lib/FileStorage/FileStorage.php:158
msgid "Stored \"%s\" from \"%s\""
msgstr ""

#: Lib/FileStorage/FileStorage.php:160
msgid "Failed to store \"%s\" from %s"
msgstr ""

#: Lib/FileStorage/FileStorage.php:175
msgid "Failed to retrieve file from \"%s\""
msgstr ""

#: Lib/FileStorage/FileStorage.php:177
msgid "Retrieved file from \"%s\""
msgstr ""

#: Lib/FileStorage/FileStorage.php:242
msgid "Directory \"%s\" does not reside within \"%s\""
msgstr ""

#: Lib/FileStorage/FileStorage.php:271
msgid "%s is not a valid file"
msgstr ""

#: Lib/FileStorage/FileStorage.php:274
msgid "File \"%s\" does not reside within \"%s\""
msgstr ""

#: Lib/Utility/SupportedLanguages.php:27
msgid "English"
msgstr "Inglés"

#: Lib/Utility/SupportedLanguages.php:28
msgid "French"
msgstr "Francés"

#: Lib/Utility/SupportedLanguages.php:29
msgid "Spanish"
msgstr "Español"

#: Lib/Utility/SupportedLanguages.php:30
msgid "German"
msgstr "Alemán"

#: Lib/Utility/SupportedLanguages.php:31
msgid "Italian"
msgstr "Italiano"

#: Lib/Utility/SupportedLanguages.php:32
msgid "Dutch"
msgstr "Holandés"

#: Lib/Utility/SupportedLanguages.php:33
msgid "Portuguese"
msgstr "Portugués"

#: Model/Order.php:324 Plugin/Stripe/Enum/StripeCardBrand.php:65
msgid "Unknown"
msgstr "Desconocido"

#: Model/Order.php:325
msgid "Not Assessed"
msgstr "No evaluado"

#: Model/Order.php:326
msgid "Normal"
msgstr "Normal"

#: Model/Order.php:327
msgid "Elevated"
msgstr "Elevado"

#: Model/Order.php:328
msgid "Highest"
msgstr "Mas alta"

#: Model/Order.php:337
msgid "Pass"
msgstr ""

#: Model/Order.php:338
msgid "Fail"
msgstr ""

#: Model/Order.php:339
msgid "Unavailable"
msgstr ""

#: Model/Order.php:340
msgid "Unchecked"
msgstr ""

#: Model/Page.php:117
msgid "Your credit card was declined. When we submit a charge to your bank, their systems determine whether or not to accept the charge. They take signals such as spending habits, account balance, and more into account. We recommend contacting your bank to determine why your card was declined."
msgstr "Su tarjeta de crédito ha sido rechazada. Cuando enviamos un cargo a su banco, sus sistemas determinan si aceptan el pago o lo rechazan. Los sistemas tienen en cuenta sus hábitos de consumo, el balance de su cuenta, atc. Le recomendamos se ponga en contacto con su banco para determinar la razón del rechazo."

#: Model/Page.php:118
msgid "The zip code you supplied in your billing address failed validation. Please verify your address information and try again with the Checkout button below or contact us at %s if the problem continues."
msgstr "El código postal provisto en su dirección de facturación no es válido. Por favor verifique su dirección e intente nuevamente haciéndalo click en el botón de Pago mas abajo o contacte con nosotros a %s si el problema persiste."

#: Model/Page.php:119
msgid "We are sorry, there was an unexpected error processing your payment. Please try again with the Checkout button below or contact us at %s if the problem continues."
msgstr "Lo sentimos, tuvimos un error inesperado al procesar su pago. Por favor inténtelo nuevamente con el botón de Pago mas abajo o contacte con nosotros a %s si el problema persiste."

#: Model/Product.php:2189
#: View/Elements/Products/productlist_retailer.ctp:120
#: View/Elements/Products/productlist_retailer_grid.ctp:52
#: View/Products/index_retailer.ctp:198
msgid "All Products"
msgstr "Todos los productos"

#: Model/Product.php:2190
msgid "In Stock Only"
msgstr "Solo en stock"

#: Model/Staff.php:50
msgid "Service Provider"
msgstr "Proveedor del Servicio"

#: Model/Staff.php:51
msgid "Store Associate"
msgstr "Asistente"

#: Model/User.php:379
msgid "Registered"
msgstr "Registrado"

#: Model/User.php:380
msgid "Approved"
msgstr "Aprobado"

#: Model/User.php:381 Utility/ProductStatus.php:26
msgid "Active"
msgstr "Activo"

#: Model/User.php:382
msgid "Inactive"
msgstr "Inactivo"

#: Model/User.php:383
msgid "Rejected"
msgstr "Rechazado"

#: Model/User.php:384
msgid "Suspended"
msgstr "Suspendido"

#: Model/User.php:1444;1456
#: Plugin/Widgets/View/Elements/CheckoutWidgets/retailer_summary.ctp:44;158
#: Plugin/Widgets/View/Elements/LocatorWidgets/retailer_item.ctp:42;111
#: View/Elements/storetiming.ctp:16
#: View/Elements/Staff/ajax_store_timing.ctp:11 View/Users/<USER>
msgid "Closed"
msgstr "Cerrado"

#: Model/User.php:1451;1468
#: Plugin/Widgets/View/Elements/CheckoutWidgets/retailer_summary.ctp:183
#: Plugin/Widgets/View/Elements/LocatorWidgets/retailer_item.ctp:129
#: View/Users/<USER>
msgid "By Appointment"
msgstr "Con cita previa"

#: Model/User.php:3410
msgid "Inventory Available: Reserve for Store Pickup"
msgstr "Inventario disponible: reservar para recogida en tienda"

#: Model/User.php:3411
msgid "Ship to Store: Available within 7 Business Days"
msgstr "Envío a la Tienda: Disponible en 7 días hábiles"

#: Model/User.php:3412
msgid "Ship to Store: On Backorder, Available in 3-4 Weeks"
msgstr "Envío a Tienda: Pedido en Lista de Espera, Disponible en 3-4 semanas"

#: Model/User.php:3413
msgid "We currently do not have a dealer in your area. If you would like to purchase through a retailer with professional assembly, please email us at %s first. Connect with us before placing your order, and we will find a retailer for you."
msgstr "Actualmente no tenemos un distribuidor en su área. Si desea comprar a través de un minorista con montaje profesional, envíenos un correo electrónico a %s. Conéctese con nosotros antes de realizar su pedido y encontraremos un distribuidor para usted."

#: Model/User.php:3421
msgid "Ship to Door"
msgstr "Envío a Domicilio"

#: Model/User.php:3422
msgid "Standard Ground Shipping Rates. Arrives in 5-7 Business Days"
msgstr "Tarifa Estándar de Envío Terrestre. Llega en 5-7 días hábiles"

#: Model/User.php:3423
msgid "An item in your order is on backorder. Anticipated to ship within 3-4 weeks"
msgstr "Un artículo de su pedido está pendiente. Anticipado para enviar dentro de 3-4 semanas"

#: Model/User.php:3425
#: Plugin/Widgets/View/Elements/LocatorWidgets/retailer_item.ctp:15
#: View/Elements/Users/<USER>/brand_dealer_options.ctp:56
msgid "Ship From Store"
msgstr "Envío Desde la Tienda"

#: Model/User.php:3427
#: Plugin/Widgets/View/Elements/LocatorWidgets/retailer_item.ctp:12
#: Utility/OrderType.php:97 View/Elements/branch.ctp:124
#: View/Elements/Users/<USER>/brand_dealer_options.ctp:31
#: View/Elements/Users/<USER>/retailer_dealer_options.ctp:34
msgid "Store Pickup"
msgstr "Recoger en Tienda"

#: Model/User.php:3433
#: Plugin/Widgets/View/Elements/LocatorWidgets/retailer_item.ctp:13
#: View/Elements/Users/<USER>/brand_dealer_options.ctp:130
msgid "Local Install"
msgstr "Instalación Local"

#: Model/User.php:3438
#: Plugin/Widgets/View/Elements/LocatorWidgets/retailer_item.ctp:14
#: Utility/OrderType.php:98 View/Elements/branch.ctp:141
#: View/Elements/Users/<USER>/brand_dealer_options.ctp:170
msgid "Local Delivery"
msgstr "Envío Local"

#: Model/User.php:3439
msgid "Inventory Available: Schedule Delivery within 3 Business Days"
msgstr "Stock Disponible: Programar Envío en 3 días hábiles"

#: Model/User.php:3445
msgid "Email me with news and offers"
msgstr "Envíame un correo electrónico con noticias y ofertas"

#: Model/User.php:402 View/Elements/storetiming.ctp:8
msgctxt "Days of week"
msgid "Monday"
msgstr "Lunes"

#: Model/User.php:403
#: Plugin/Widgets/View/Elements/LocatorWidgets/retailer_item.ctp:42
#: Plugin/Widgets/View/Elements/CheckoutWidgets/retailer_summary.ctp:40
msgctxt "Days of week"
msgid "Mon"
msgstr "Lun"

#: Model/User.php:406 View/Elements/storetiming.ctp:8
msgctxt "Days of week"
msgid "Tuesday"
msgstr "Martes"

#: Model/User.php:407
#: Plugin/Widgets/View/Elements/LocatorWidgets/retailer_item.ctp:42
#: Plugin/Widgets/View/Elements/CheckoutWidgets/retailer_summary.ctp:40
msgctxt "Days of week"
msgid "Tue"
msgstr "Mart"

#: Model/User.php:410 View/Elements/storetiming.ctp:8
msgctxt "Days of week"
msgid "Wednesday"
msgstr "Miércoles"

#: Model/User.php:411
#: Plugin/Widgets/View/Elements/LocatorWidgets/retailer_item.ctp:42
#: Plugin/Widgets/View/Elements/CheckoutWidgets/retailer_summary.ctp:40
msgctxt "Days of week"
msgid "Wed"
msgstr "Miérc"

#: Model/User.php:414 View/Elements/storetiming.ctp:8
msgctxt "Days of week"
msgid "Thursday"
msgstr "Jueves"

#: Model/User.php:415
#: Plugin/Widgets/View/Elements/LocatorWidgets/retailer_item.ctp:42
#: Plugin/Widgets/View/Elements/CheckoutWidgets/retailer_summary.ctp:40
msgctxt "Days of week"
msgid "Thu"
msgstr "Juev"

#: Model/User.php:418 View/Elements/storetiming.ctp:8
msgctxt "Days of week"
msgid "Friday"
msgstr "Viernes"

#: Model/User.php:419
#: Plugin/Widgets/View/Elements/LocatorWidgets/retailer_item.ctp:42
#: Plugin/Widgets/View/Elements/CheckoutWidgets/retailer_summary.ctp:40
msgctxt "Days of week"
msgid "Fri"
msgstr "Vier"

#: Model/User.php:422 View/Elements/storetiming.ctp:8
msgctxt "Days of week"
msgid "Saturday"
msgstr "Sábado"

#: Model/User.php:423
#: Plugin/Widgets/View/Elements/LocatorWidgets/retailer_item.ctp:42
#: Plugin/Widgets/View/Elements/CheckoutWidgets/retailer_summary.ctp:40
msgctxt "Days of week"
msgid "Sat"
msgstr "Sáb"

#: Model/User.php:426 View/Elements/storetiming.ctp:8
msgctxt "Days of week"
msgid "Sunday"
msgstr "Domingo"

#: Model/User.php:427
#: Plugin/Widgets/View/Elements/LocatorWidgets/retailer_item.ctp:42
#: Plugin/Widgets/View/Elements/CheckoutWidgets/retailer_summary.ctp:40
msgctxt "Days of week"
msgid "Sun"
msgstr "Dom"

#: Plugin/Widgets/View/Elements/LocatorWidgets/retailer_item.ctp:47;62
#: Plugin/Widgets/View/Elements/CheckoutWidgets/retailer_summary.ctp:46;61
msgid "Opens"
msgstr "Abre"

#: Plugin/Widgets/View/Elements/LocatorWidgets/retailer_item.ctp:47;61
#: Plugin/Widgets/View/Elements/CheckoutWidgets/retailer_summary.ctp:46;60
msgid "today"
msgstr "hoy"

#: Plugin/Api/v1/Controller/ApiV1InventoryController.php:96
msgid "Failed to delete inventory item"
msgstr ""

#: Plugin/Shopify/Controller/ShopifyController.php:378;1498
msgid "Your session has expired"
msgstr "Su sesión ha expirado"

#: Plugin/Shopify/Controller/ShopifyController.php:390
#: Plugin/Woocommerce/Controller/WoocommerceController.php:920
msgid "Please provide a valid email before applying code"
msgstr "Por favor ingrese una dirección de correo válida antes de aplicar un código"

#: Plugin/Shopify/Controller/ShopifyController.php:404
#: Plugin/Woocommerce/Controller/WoocommerceController.php:934
msgid "Coupon code was used on a previous order"
msgstr "El código de cupón ya ha sido utilizado en otra orden"

#: Plugin/Shopify/Controller/ShopifyController.php:618
msgid "Available in 5 to 7 Business Days"
msgstr "Disponible en 5 a 7 días hábiles"

#: Plugin/Shopify/Controller/ShopifyController.php:619
msgid "Available Immediately"
msgstr "Disponible Inmediatamente"

#: Plugin/Shopify/Controller/ShopifyController.php:929
msgid "Will Call"
msgstr "Lo llamaremos"

#: Plugin/Shopify/Controller/ShopifyController.php:1282
#: Plugin/Widgets/View/CheckoutWidgets/order_confirmation.ctp:18
#: Plugin/Shopify/View/templates/page.success.liquid.ctp:92
msgid "Order Confirmation"
msgstr "Confirmación de pedido"

#: Plugin/Shopify/Controller/ShopifyController.php:1284
#: Plugin/Shopify/View/templates/page.success.liquid.ctp:93
msgid "Order No"
msgstr "Número  de Pedido"

#: Plugin/Shopify/Controller/ShopifyController.php:1286
#: Plugin/Shopify/View/templates/page.success.liquid.ctp:99
msgid "Your order has been received"
msgstr "Su pedido ha sido recibida"

#: Plugin/Shopify/Controller/ShopifyController.php:1288
#: Plugin/Widgets/View/CheckoutWidgets/order_confirmation.ctp:62
#: View/Elements/Orders/order_details_panel.ctp:94
#: View/Orders/verification_code.ctp:38
#: Plugin/Shopify/View/templates/page.success.liquid.ctp:109
msgid "Verification Code"
msgstr "Código de Verificación"

#: Plugin/Shopify/Controller/ShopifyController.php:1290
#: Plugin/Widgets/View/CheckoutWidgets/order_confirmation.ctp:73
#: Plugin/Shopify/View/templates/page.success.liquid.ctp:117
msgid "Your estimated delivery date is"
msgstr "El día estimado para el envío es"

#: Plugin/Shopify/Controller/ShopifyController.php:1292
#: Plugin/Shopify/View/templates/page.success.liquid.ctp:119
msgid "Your delivery method is"
msgstr "Su método de envío es"

#: Plugin/Shopify/Controller/ShopifyController.php:1294
#: Plugin/Widgets/View/CheckoutWidgets/order_confirmation.ctp:76
#: Plugin/Shopify/View/templates/page.success.liquid.ctp:121
msgid "Store Hours"
msgstr "Horas de la Tienda"

#: Plugin/Shopify/Controller/ShopifyController.php:1296
#: Plugin/Widgets/View/CheckoutWidgets/order_confirmation.ctp:66
#: Plugin/Shopify/View/templates/page.success.liquid.ctp:124
msgid "Your Order is Being Fulfilled by"
msgstr "Su pedido está siendo preparado por"

#: Plugin/Shopify/Controller/ShopifyController.php:1298
#: View/Elements/Orders/order_details_panel.ctp:23
#: Plugin/Shopify/View/templates/page.success.liquid.ctp:131
msgid "Order Details"
msgstr "Detalle del Pedido"

#: Plugin/Shopify/Controller/ShopifyController.php:1300
#: Plugin/Shopify/View/Shopify/checkout_page.ctp:130;353;447
#: Plugin/Shopify/View/Shopify/payment_page.ctp:152;302;456
#: Plugin/Widgets/Controller/CheckoutWidgetsController.php:679
#: View/B2bCarts/index.ctp:88 View/DealerOrderRefunds/refund.ctp:89
#: View/Elements/Orders/order_invoice_totals.ctp:156
#: View/InvoicePdf/generate_invoice.ctp:154 View/OrderRefunds/refund.ctp:119
#: Plugin/Shopify/View/templates/page.success.liquid.ctp:165
msgid "Discount"
msgstr "Descuento"

#: Plugin/Shopify/Controller/ShopifyController.php:1306
#: Plugin/Shopify/View/Elements/payment_page/instore_dealer_table_header.ctp:41
#: Plugin/Shopify/View/Shopify/checkout_page.ctp:136
#: Plugin/Shopify/View/Shopify/payment_page.ctp:161;311;367;465;651;693
#: Plugin/Shopify/View/templates/page.success.liquid.ctp:174
msgid "Taxes"
msgstr "Impuestos"

#: Plugin/Shopify/Controller/ShopifyController.php:1310
#: Plugin/Shopify/View/Layouts/default.ctp:32 View/Users/<USER>
#: Plugin/Shopify/View/templates/page.success.liquid.ctp:214
msgid "Return Policy"
msgstr "Políticas de devolución"

#: Plugin/Shopify/Controller/ShopifyController.php:1312
#: Plugin/Shopify/View/templates/page.success.liquid.ctp:230
msgid "Store Directions"
msgstr "Direcciones a la Tienda"

#: Plugin/Shopify/Controller/ShopifyController.php:1314
#: Plugin/Shopify/View/templates/page.success.liquid.ctp:237
msgid "Total Distance"
msgstr "Distancia Total"

#: Plugin/Shopify/Controller/ShopifyController.php:1316
#: View/B2bCarts/view.ctp:71
#: Plugin/Shopify/View/templates/page.success.liquid.ctp:246
msgid "Continue Shopping"
msgstr "Continuar comprando"

#: Plugin/Shopify/Controller/ShopifyController.php:1393
msgid "System Error"
msgstr "Error de sistema"

#: Plugin/Shopify/Controller/ShopifyController.php:1394
msgid "Internal Error please try again"
msgstr "Error Interno por favor intente nuevamente"

#: Plugin/Shopify/Controller/ShopifyController.php:1418
#: Plugin/Widgets/Controller/CheckoutWidgetsController.php:306
#: View/Helper/InventoryHelper.php:221
msgid "Out of Stock"
msgstr "Fuera de stock"

#: Plugin/Shopify/Controller/ShopifyController.php:2085
#: Plugin/Widgets/Controller/CheckoutWidgetsController.php:478
msgid "Payment Error"
msgstr "Error en el pago"

#: Plugin/Shopify/View/Elements/address_inputs.ctp:61
#: Plugin/Widgets/View/Elements/CheckoutWidgets/address_inputs.ctp:52
msgid "Company (Optional)"
msgstr "Compañía (Opcional)"

#: Plugin/Shopify/View/Elements/address_inputs.ctp:84
#: Plugin/Widgets/View/Elements/CheckoutWidgets/address_inputs.ctp:63
msgid "Apt,suite,etc. (Optional)"
msgstr "Apartamento, Casa, etc (Opcional)"

#: Plugin/Shopify/View/Elements/address_inputs.ctp:104
#: View/Elements/B2bCarts/custom_address_inputs.ctp:72
#: View/Users/<USER>
msgid "Select Your Country"
msgstr "Seleccione País"

#: Plugin/Shopify/View/Elements/address_inputs.ctp:123
#: Plugin/Shopify/View/Shopify/get_states.ctp:9
msgid "Select State/Province"
msgstr "Seleccione Estado/Provincia"

#: Plugin/Shopify/View/Elements/address_inputs.ctp:158
#: Plugin/Widgets/View/Elements/CheckoutWidgets/address_inputs.ctp:106
#: View/Staff/index.ctp:25 View/Staff/view.ctp:108
msgid "Phone"
msgstr "Teléfono"

#: Plugin/Shopify/View/Elements/address_inputs.ctp:172
#: Plugin/Widgets/View/Elements/CheckoutWidgets/address_inputs.ctp:130
msgid "Address Validation Error"
msgstr "Error al Validar Dirección"

#: Plugin/Shopify/View/Elements/stripe_card_form.ctp:34
msgid "3-digit security code usually found on the back of your card. American Express cards have a 4-digit code located on the front."
msgstr "El código de seguridad consta de 3 dígitos que se encuentran al dorso de su tarjeta. American Express tiene 4 dígitos al dorso."

#: Plugin/Shopify/View/Elements/ErrorDetails/out_of_stock_error.ctp:7
#: Plugin/Widgets/Controller/CheckoutWidgetsController.php:307
msgid "Sorry, some items in your cart are no longer available. Please update your cart to complete your purchase."
msgstr "Disculpe, algunos productos en su carrito ya no se encuentran disponibles. Por favor actualice el carrito para completar su compra."

#: Plugin/Shopify/View/Elements/ErrorDetails/out_of_stock_error.ctp:29
#: Plugin/Widgets/View/CatalogueWidgets/view.ctp:32;76
msgid "Sold Out"
msgstr "Vendido"

#: Plugin/Widgets/View/CatalogueWidgets/view.ctp:69
#: Plugin/Widgets/View/Elements/CatalogueWidgets/catalogue_item.ctp:23
#: View/Retailers/view.ctp:104
msgid "On Display"
msgstr "En exhibición"

#: Plugin/Widgets/View/CatalogueWidgets/view.ctp:73
#: Plugin/Widgets/View/Elements/CatalogueWidgets/catalogue_item.ctp:27
msgid "Similar Model on Display"
msgstr "Modelo similar en exhibición"

#: Plugin/Widgets/View/Elements/CheckoutWidgets/retailer_summary.ctp:198
#: Plugin/Widgets/View/Elements/LocatorWidgets/retailer_item.ctp:145
msgid "Model on display"
msgstr "Modelo en exhibición"

#: Plugin/Widgets/View/Elements/CheckoutWidgets/retailer_summary.ctp:198
#: Plugin/Widgets/View/Elements/LocatorWidgets/retailer_item.ctp:145
msgid "Models on display"
msgstr "Modelos en exhibición"

#: Plugin/Shopify/View/Elements/ErrorDetails/out_of_stock_error.ctp:29
#: View/Elements/Inventory/ajax_index.ctp:33 View/Fulfillments/add.ctp:73
#: View/RetailerCredits/index.ctp:163
msgid "Available"
msgstr "Disponible"

#: Plugin/Shopify/View/Elements/checkout_page/ancillary_fee.ctp:19
msgid "Please note that additional fees have been applied to your order below as required by legislation"
msgstr "Por favor tenga en cuenta que tarifas adicionales se han aplicado a su pedido a continuación según lo exige la legislación"

#: Plugin/Shopify/View/Elements/checkout_page/ancillary_fee.ctp:24
msgid "Learn more about %s"
msgstr "Obtenga más información sobre %s"

#: Plugin/Shopify/View/Elements/payment_page/bnpl_redirect_message.ctp:13
msgid "After submitting your order, you will be redirected to securely complete your purchase."
msgstr "Luego de enviar su pedido, será redirigido a completar su compra de manera segura."

#: Plugin/Shopify/View/Elements/payment_page/instore_dealer_table_header.ctp:26
#: Plugin/Shopify/View/Shopify/payment_page.ctp:639 View/Users/<USER>
msgid "Select a Retailer"
msgstr "Seleccione Tienda Minorista"

#: Plugin/Shopify/View/Elements/payment_page/instore_dealer_table_header.ctp:29
msgid "Distance (%s)"
msgstr "Distancia (%s)"

#: Plugin/Shopify/View/Elements/payment_page/instore_dealer_table_header.ctp:32
#: View/Elements/storetiming.ctp:15
#: View/Elements/Users/<USER>/profile_brand_retailer.ctp:38
#: View/Staff/view.ctp:210 View/Users/<USER>
msgid "Operating Hours"
msgstr "Horas de operación"

#: Plugin/Shopify/View/Elements/payment_page/instore_dealer_table_header.ctp:47
#: Plugin/Shopify/View/Shopify/payment_page.ctp:657;699
msgid "Payment Options"
msgstr "Opciones de Pago"

#: Plugin/Shopify/View/Elements/payment_page/payment_methods.ctp:19
#: Plugin/Widgets/View/Elements/CheckoutWidgets/payment_methods.ctp:19
#: View/Elements/Orders/order_details_panel.ctp:109
msgid "Payment"
msgstr "Pago"

#: Plugin/Shopify/View/Elements/payment_page/payment_methods.ctp:20
#: Plugin/Widgets/View/Elements/CheckoutWidgets/payment_methods.ctp:20
msgid "All transactions are secure and encrypted."
msgstr "Todas las transacciones son seguras y enctriptadas."

#: Plugin/Shopify/View/Elements/payment_page/payment_methods.ctp:29
#: Plugin/Widgets/View/Elements/CheckoutWidgets/payment_methods.ctp:28
msgid "Credit / Debit Card"
msgstr "Tarjeta de Crédito / Débito"

#: Plugin/Shopify/View/Elements/payment_page/payment_methods.ctp:50
#: Plugin/Widgets/View/Elements/CheckoutWidgets/payment_methods.ctp:43
msgid "You are purchasing from"
msgstr "Ud. está comprando desde"

#: Plugin/Shopify/View/Elements/payment_page/place_order_button.ctp:44
msgid "Pay"
msgstr "Pago"

#: Plugin/Shopify/View/Elements/waiting_room/progress.ctp:12
msgid "%d%% Complete"
msgstr "%d%% Completado"

#: Plugin/Shopify/View/Elements/waiting_room/progress.ctp:16
msgid "There is %d person in line ahead of you"
msgid_plural "There are %d people in line ahead of you"
msgstr[0] "Hay %d persona en fila delante de ud"
msgstr[1] "Hay %d personas en fila delante de ud"

#: Plugin/Shopify/View/Layouts/default.ctp:33
msgid "Privacy Policy"
msgstr "Política de Privacidad"

#: Plugin/Shopify/View/Layouts/default.ctp:34
#: Plugin/Shopify/View/Shopify/payment_page.ctp:1776
msgid "Terms Of Service"
msgstr "Términos de Servicio"

#: Plugin/Shopify/View/Layouts/default.ctp:150
msgid "Logged in as"
msgstr "Conectado como"

#: Plugin/Shopify/View/Layouts/default.ctp:151
#: View/Elements/Layouts/profile_dropdown.ctp:120
msgid "Logout"
msgstr "Cierre Sesión"

#: Plugin/Shopify/View/Layouts/default.ctp:199
msgid "Thank You"
msgstr "Gracias"

#: Plugin/Shopify/View/Layouts/default.ctp:202
msgid "Your Order is Processing"
msgstr "Su pedido está siendo procesado"

#: Plugin/Shopify/View/Layouts/default.ctp:203
msgid "Please Wait. Do Not Refresh the Page."
msgstr "Por Favor Espere. No Refresque ésta Página."

#: Plugin/Shopify/View/Layouts/default.ctp:213
#: Plugin/Widgets/View/CheckoutWidgets/delivery_methods.ctp:34
#: Plugin/Widgets/View/CheckoutWidgets/payment_details.ctp:44
msgid "Zip"
msgstr "Código Postal"

#: Plugin/Shopify/View/Layouts/default.ctp:214;215
#: Plugin/Widgets/View/CheckoutWidgets/delivery_methods.ctp:35;36
#: Plugin/Widgets/View/CheckoutWidgets/payment_details.ctp:45;46
#: View/Elements/Orders/order_details_panel.ctp:126
msgid "Postal Code"
msgstr "Código Postal"

#: Plugin/Shopify/View/Layouts/default.ctp:227
msgid "Enter 5 Digit PIN"
msgstr "Ingrese PIN de 5 dígitos"

#: Plugin/Shopify/View/Layouts/default.ctp:242;261
msgid "Pin must be a 5 digit number"
msgstr "El PIN debe tener 5 dígitos"

#: Plugin/Shopify/View/Layouts/default.ctp:272
msgid "No match found for the provided PIN"
msgstr "No encontramos coincidencia para el PIN ingresado"

#: Plugin/Shopify/View/Shopify/checkout_page.ctp:25
#: Plugin/Shopify/View/Shopify/payment_page.ctp:44
msgid "Cart"
msgstr "Carrito"

#: Plugin/Shopify/View/Shopify/checkout_page.ctp:26
#: Plugin/Shopify/View/Shopify/payment_page.ctp:45
#: Plugin/Widgets/View/CheckoutWidgets/delivery_methods.ctp:95
msgid "Customer Information"
msgstr "Información del Cliente"

#: Plugin/Shopify/View/Shopify/checkout_page.ctp:27
#: Plugin/Shopify/View/Shopify/payment_page.ctp:46
#: Plugin/Widgets/View/Elements/CheckoutWidgets/delivery_method_summary.ctp:30
msgid "Delivery Method"
msgstr "Método de Envío"

#: Plugin/Shopify/View/Shopify/checkout_page.ctp:64
#: Plugin/Shopify/View/Shopify/payment_page.ctp:718
msgid "We currently do not have any authorized dealers carrying that product near you."
msgstr "Actualmente no tenemos distribuidores autorizados que vendan el producto cerca de ud."

#: Plugin/Shopify/View/Shopify/checkout_page.ctp:66
#: Plugin/Shopify/View/Shopify/payment_page.ctp:719
msgid "Please click continue to buy direct from our website."
msgstr "Por favor haga click en Continuar para comprar directamente desde nuestro sitio."

#: Plugin/Shopify/View/Shopify/checkout_page.ctp:70
#: Plugin/Shopify/View/Shopify/payment_page.ctp:732
msgid "Continue"
msgstr "Continuar"

#: Plugin/Shopify/View/Shopify/checkout_page.ctp:77
#: Plugin/Shopify/View/Shopify/payment_page.ctp:102;255;409
msgid "Show Order Summary"
msgstr "Mostrar Resumen del Pedido"

#: Plugin/Shopify/View/Shopify/checkout_page.ctp:78
#: Plugin/Shopify/View/Shopify/payment_page.ctp:103;256;410
msgid "Hide Order Summary"
msgstr "Ocultar Resumen del Pedido"

#: Plugin/Shopify/View/Shopify/checkout_page.ctp:103;110
#: View/Elements/Discounts/manage_discounts/discount_code_card.ctp:16
msgid "Discount Code"
msgstr "Código de Descuento"

#: Plugin/Shopify/View/Shopify/checkout_page.ctp:118
msgid "Apply"
msgstr "Aplicar"

#: Plugin/Shopify/View/Shopify/checkout_page.ctp:149;304
msgid "Calculated at next step"
msgstr "Calculado en el próximo paso"

#: Plugin/Shopify/View/Shopify/checkout_page.ctp:185
#: View/Branchs/store_permission.ctp:12
msgid "Contact"
msgstr "Contacto"

#: Plugin/Shopify/View/Shopify/checkout_page.ctp:212
msgid "Your Shipping Address"
msgstr "Dirección de Envío"

#: Plugin/Shopify/View/Shopify/checkout_page.ctp:220
msgid "Continue to delivery method"
msgstr "Continúe con el método de envío"

#: Plugin/Shopify/View/Shopify/checkout_page.ctp:224
msgid "Return To Cart"
msgstr "Volver al Carrito"

#: Plugin/Shopify/View/Shopify/checkout_page.ctp:302
msgid "FREE SHIPPING"
msgstr "ENVÍO GRATIS"

#: Plugin/Shopify/View/Shopify/checkout_page.ctp:366
msgid "An error occurred"
msgstr "Ha ocurrido un error"

#: Plugin/Shopify/View/Shopify/checkout_page.ctp:385
msgid "Cookies are not enabled on your browser. You must enable cookies by adjusting your browser's security settings to proceed to checkout."
msgstr "Las cookies no están habilitadas en su navegador. Debe habilitar las cookies ajustando la configuración de seguridad de su navegador para proceder al pago."

#: Plugin/Shopify/View/Shopify/payment_page.ctp:207
msgid "Your Address"
msgstr "Su Dirección"

#: Plugin/Shopify/View/Shopify/payment_page.ctp:223 View/Elements/branch.ctp:86
#: View/Elements/Users/<USER>/profile_brand_retailer.ctp:73
#: View/Helper/BrandStaffHelper.php:116 View/Helper/ShipearlyHelper.php:198
#: View/Orders/invoice.ctp:101 View/Products/product_collections.ctp:195
#: View/UserTaxes/registrations.ctp:98 View/Warehouses/index.ctp:47
msgid "Edit"
msgstr "Editar"

#: Plugin/Shopify/View/Shopify/payment_page.ctp:233;1452
#: Plugin/Shopify/View/Shopify/json/get_in_store_retailers.ctp:67
#: Plugin/Shopify/View/Shopify/json/get_sell_direct.ctp:101
msgid "FREE"
msgstr "GRATIS"

#: Plugin/Shopify/View/Shopify/payment_page.ctp:252;406
msgid "Shipment %d of %d"
msgstr "Envío %d de %d"

#: Plugin/Shopify/View/Shopify/payment_page.ctp:355
#: View/Elements/B2bCarts/custom_address_inputs.ctp:45
#: View/Elements/Users/<USER>/profile_brand_retailer.ctp:52;53
#: View/Users/<USER>
msgid "Company Name"
msgstr "Nobre de la Compañia"

#: Plugin/Shopify/View/Shopify/payment_page.ctp:358;642
msgid "State / Province"
msgstr "Estado / Provincia"

#: Plugin/Shopify/View/Shopify/payment_page.ctp:513
#: Plugin/Widgets/View/CheckoutWidgets/delivery_methods.ctp:17
msgid "Select Delivery Method"
msgstr "Seleccione método de envío"

#: Plugin/Shopify/View/Shopify/payment_page.ctp:515
msgid "Learn More"
msgstr "Leer mas"

#: Plugin/Shopify/View/Shopify/payment_page.ctp:550;588;620;665
msgid "Checking Available Retailers"
msgstr "Comprobando Distribuidores Disponibles"

#: Plugin/Shopify/View/Shopify/payment_page.ctp:684
msgid "Select Courier Method"
msgstr "Seleccione método de mensajería"

#: Plugin/Shopify/View/Shopify/payment_page.ctp:714
msgid "That product is currently not available for your selected retailer or delivery option."
msgstr "El producto no está disponible en el minorista seleccionado o para envío."

#: Plugin/Shopify/View/Shopify/payment_page.ctp:715
msgid "Please click continue to select a different retailer and delivery option."
msgstr "Haga clic en Continuar para seleccionar un minorista y una opción de entrega diferentes."

#: Plugin/Shopify/View/Shopify/payment_page.ctp:747
#: Plugin/Widgets/View/CheckoutWidgets/payment_details.ctp:112
msgid "Same as shipping address"
msgstr "Igual al domicilio de envío"

#: Plugin/Shopify/View/Shopify/payment_page.ctp:753
#: Plugin/Widgets/View/CheckoutWidgets/payment_details.ctp:113
msgid "Use a different billing address"
msgstr "Usar un domicilio de facturación distinto"

#: Plugin/Shopify/View/Shopify/payment_page.ctp:1166
msgid "Sorry there are no retailers available carrying this product"
msgstr "Lo sentimos, no tenemos minoristas disponibles que vendan este producto"

#: Plugin/Shopify/View/Shopify/payment_page.ctp:1530
#: Plugin/Widgets/View/CheckoutWidgets/payment_details.ctp:153
msgid "Card number"
msgstr "Número de tarjeta"

#: Plugin/Shopify/View/Shopify/payment_page.ctp:1531
#: Plugin/Widgets/View/CheckoutWidgets/payment_details.ctp:154
msgid "CVV"
msgstr "CVV"

#: Plugin/Shopify/View/Shopify/payment_page.ctp:1531
#: Plugin/Widgets/View/CheckoutWidgets/payment_details.ctp:154
msgid "Security code"
msgstr "Código de seguridad"

#: Plugin/Shopify/View/Shopify/payment_page.ctp:1781
msgid "Accept and Complete Purchase"
msgstr "Aceptar y Completar la Compra"

#: Plugin/Shopify/View/Shopify/payment_page.ctp:1782
msgid "Decline"
msgstr "Rechazada"

#: Plugin/Shopify/View/Shopify/payment_page.ctp:1809
msgid "Please select any one retailer to continue"
msgstr "Por favor seleccione algún minorista para continuar"

#: Plugin/Shopify/View/Shopify/waiting_room.ctp:59
msgid "You are now in line"
msgstr "Ud. se encuentra en línea"

#: Plugin/Shopify/View/Shopify/waiting_room.ctp:64
msgid "Due to overwhelming demand we have placed you in a waiting room before you proceed to checkout. We will let you into checkout as soon as space is available. Follow the progress bar for your turn to checkout and please note that your screen will automatically advance to checkout once available. Pressing back or leaving the page will remove you from the checkout queue. Thank you for your patience and we will be right with you."
msgstr "Debido a la cantidad de demandas, lo hemos colocado en una sala de espera antes de proceder con el pago. Le permitiremos pasar por caja tan pronto como haya espacio disponible. Siga la barra de progreso para su turno de pago y tenga en cuenta que su pantalla avanzará automáticamente al pago una vez que esté disponible. Presionar hacia atrás o salir de la página lo eliminará de la cola de pago. Gracias por su paciencia y nos pondremos en contacto con usted."

#: Plugin/Shopify/View/Shopify/waiting_room.ctp:65
msgid "In the meantime, please review our return policy below while you are waiting."
msgstr "Mientras tanto, por favor revise nuestra política de devolución mientras espera."

#: Plugin/Stripe/Enum/StripePaymentType.php:19
msgid "Card"
msgstr "Tarjeta"

#: Plugin/Widgets/Controller/CheckoutWidgetsController.php:279
msgid "%s is Not Available"
msgstr "%S no se encuentra disponible"

#: Plugin/Widgets/Controller/CheckoutWidgetsController.php:280
msgid "The selected delivery method is not available for the provided shipping address."
msgstr "El método de envío no se encuentra disponible para el domicilio de envío provisto."

#: Plugin/Widgets/Controller/CheckoutWidgetsController.php:1507
msgid "Available Now"
msgstr "Disponible Ahora"

#: Plugin/Widgets/View/CatalogueWidgets/index.ctp:20
#: Plugin/Widgets/View/CatalogueWidgets/view.ctp:16
msgid "Catalogue"
msgstr "Catálogo"

#: Plugin/Widgets/View/CatalogueWidgets/index.ctp:46
#: View/Products/index_retailer.ctp:159
msgid "All Collections"
msgstr "Todas las Colecciones"

#: Plugin/Widgets/View/CatalogueWidgets/index.ctp:49
msgid "Collection"
msgstr "Colección"

#: Plugin/Widgets/View/CatalogueWidgets/index.ctp:56
#: View/Products/index_retailer.ctp:171
msgid "All Categories"
msgstr "Todas las Categorías"

#: Plugin/Widgets/View/CatalogueWidgets/index.ctp:69
#: View/Products/index_retailer.ctp:203
msgid "Stock Option"
msgstr "Opción sobre acciones"

#: Plugin/Widgets/View/CatalogueWidgets/index.ctp:78;81;84
#: Plugin/Widgets/View/Layouts/locator_widgets.ctp:85 View/Common/index.ctp:28
#: View/Customers/index.ctp:19 View/Discounts/index.ctp:37
#: View/Inventory/index.ctp:32 View/Manufacturers/index.ctp:14
#: View/Orders/customer_index.ctp:43 View/Orders/dealerorders_index.ctp:45
#: View/Orders/index.ctp:48 View/Products/collections.ctp:29;30
#: View/Products/index.ctp:151 View/Products/index_retailer.ctp:38
#: View/Retailers/index.ctp:73 View/Retailers/index_salesrep.ctp:46
#: View/Search/admin_brand_search.ctp:17 View/Users/<USER>
#: View/Users/<USER>
msgid "Search"
msgstr "Buscar"

#: Plugin/Widgets/View/CatalogueWidgets/view.ctp:31
msgid "Add to Cart"
msgstr "Añadir a la cesta"

#: Plugin/Widgets/View/CatalogueWidgets/view.ctp:31
#: Plugin/Widgets/View/Elements/LocatorWidgets/retailer_view.ctp:39
msgid "Buy Now"
msgstr "Comprar Ahora"

#: Plugin/Widgets/View/CatalogueWidgets/view.ctp:36
#: Plugin/Widgets/View/CheckoutWidgets/delivery_methods.ctp:64
#: Plugin/Widgets/View/CheckoutWidgets/order_confirmation.ctp:79
#: Plugin/Widgets/View/CheckoutWidgets/payment_details.ctp:96
#: Plugin/Widgets/View/Elements/LocatorWidgets/retailer_view.ctp:28
#: Plugin/Widgets/View/Errors/checkout_widgets.ctp:72;78
msgid "Back"
msgstr "Volver"

#: Plugin/Widgets/View/CatalogueWidgets/view.ctp:78
#: Plugin/Widgets/View/Elements/CatalogueWidgets/catalogue_item.ctp:25
msgid "In Warehouse"
msgstr "En Almacén"

#: Plugin/Widgets/View/CatalogueWidgets/view.ctp:81
msgid "Default"
msgstr "Por defecto"

#: Plugin/Widgets/View/CatalogueWidgets/view.ctp:95
msgid "Model"
msgstr "Modelo"

#: Plugin/Widgets/View/CheckoutWidgets/delivery_methods.ctp:127
msgid "Please confirm your shipping address"
msgstr "Por favor confirme su domicilio de envío"

#: Plugin/Widgets/View/CheckoutWidgets/delivery_methods.ctp:131
msgid "Continue to Payment Details"
msgstr "Continuar con Detalles de Pago"

#: Plugin/Widgets/View/CheckoutWidgets/order_confirmation.ctp:52
msgid "Order %s"
msgstr "Orden %s"

#: Plugin/Widgets/View/CheckoutWidgets/order_confirmation.ctp:56
msgid "Thank you %s"
msgstr "Gracias %s"

#: Plugin/Widgets/View/CheckoutWidgets/payment_details.ctp:25
msgid "Payment Details"
msgstr "Detalles de Pago"

#: Plugin/Widgets/View/Elements/CatalogueWidgets/catalogue_item.ctp:24
msgid "%s of %s In Stock"
msgstr "%s de %s en stock"

#: Plugin/Widgets/View/Elements/CatalogueWidgets/catalogue_item.ctp:28
#: View/Elements/storetimingrow.ctp:26
msgid "From"
msgstr "De"

#: Plugin/Widgets/View/Elements/CheckoutWidgets/billing_summary.ctp:9
msgid "Billing address same as shipping address"
msgstr "Dirección de facturación igual que la dirección de envío"

#: Plugin/Widgets/View/Elements/CheckoutWidgets/billing_summary.ctp:21
#: Plugin/Widgets/View/Elements/CheckoutWidgets/delivery_method_summary.ctp:23
#: Plugin/Widgets/View/Elements/CheckoutWidgets/retailer_summary.ctp:12
#: View/Users/<USER>
msgid "Change"
msgstr "Cambiar"

#: Plugin/Widgets/View/Elements/CheckoutWidgets/billing_summary.ctp:28
msgid "Billing"
msgstr "Facturación"

#: Plugin/Widgets/View/Elements/CheckoutWidgets/order_summary.ctp:15
#: Plugin/Widgets/View/LocatorWidgets/products.ctp:50
#: View/Elements/Products/catalogue_variants.ctp:17 View/Products/view.ctp:514
msgid "Thumbnail"
msgstr "Miniatura"

#: Plugin/Widgets/View/Elements/CheckoutWidgets/retailer_summary.ctp:18
msgid "Selected Store"
msgstr "Tienda Seleccionada"

#: Plugin/Widgets/View/Elements/CheckoutWidgets/retailer_summary.ctp:158
#: Plugin/Widgets/View/Elements/LocatorWidgets/retailer_item.ctp:111
msgid "Open"
msgstr "Abierto"

#: Utility/OrderStatus.php:211
msgctxt "order_status"
msgid "Open"
msgstr "Abierto"

#: Plugin/Widgets/View/Elements/LocatorWidgets/retailer_item.ctp:19
#: View/Elements/Users/<USER>/brand_dealer_options.ctp:86
msgid "Sell Direct"
msgstr "Venta Directa"

#: Plugin/Widgets/View/Elements/LocatorWidgets/retailer_item.ctp:76;116
msgid "In-Stock"
msgstr "En Stock"

#: Plugin/Widgets/View/Elements/LocatorWidgets/retailer_item.ctp:76
#: Utility/OrderType.php:85
msgid "Ship to Store"
msgstr "Envío a Tienda"

#: Plugin/Widgets/View/Elements/LocatorWidgets/retailer_item.ctp:95
msgid "Delivery Options"
msgstr "Opciones de Envío"

#: Plugin/Widgets/View/Elements/LocatorWidgets/retailer_item.ctp:116
msgid "Items in Stock"
msgstr "Artículos en stock"

#: Plugin/Widgets/View/Elements/LocatorWidgets/retailer_view.ctp:32
msgid "Call Now"
msgstr "Llama ahora"

#: Plugin/Widgets/View/Errors/checkout_widgets.ctp:25
#: View/Errors/error400.ctp:26 View/Errors/error500.ctp:19
msgid "Error"
msgstr "Error"

#: Plugin/Widgets/View/Layouts/catalogue_widgets.ctp:86
msgid "Open cart"
msgstr ""

#: Plugin/Widgets/View/Layouts/catalogue_widgets.ctp:87
msgid "Shopping cart"
msgstr ""

#: Plugin/Widgets/View/Layouts/catalogue_widgets.ctp:88
msgid "Close panel"
msgstr ""

#: Plugin/Widgets/View/Layouts/catalogue_widgets.ctp:89
msgid "Reduce item quantity by one"
msgstr ""

#: Plugin/Widgets/View/Layouts/catalogue_widgets.ctp:90
msgid "Increase item quantity by one"
msgstr ""

#: Plugin/Widgets/View/Layouts/catalogue_widgets.ctp:91
msgid "Remove"
msgstr ""

#: Plugin/Widgets/View/Layouts/catalogue_widgets.ctp:92
msgid "No items in cart"
msgstr ""

#: Plugin/Widgets/View/Layouts/catalogue_widgets.ctp:94
msgid "Shipping and taxes calculated at checkout."
msgstr ""

#: Plugin/Widgets/View/Layouts/catalogue_widgets.ctp:95;102
msgid "Checkout"
msgstr ""

#: Plugin/Widgets/View/Layouts/catalogue_widgets.ctp:96
msgid "Cart count"
msgstr ""

#: Plugin/Widgets/View/LocatorWidgets/dealers.ctp:16
msgid "Dealer Locations"
msgstr "Ubicaciones de distribuidores"

#: Plugin/Widgets/View/LocatorWidgets/redirect.ctp:26
#: View/Elements/Layouts/modals.ctp:10
msgid "Loading..."
msgstr "Cargando…"

#: Utility/B2bCartType.php:35
msgid "Regular"
msgstr "Regular"

#: Utility/B2bCartType.php:36
msgid "Rental"
msgstr "Alquiler"

#: Utility/B2bCartType.php:37
msgid "Employee"
msgstr "Empleado"

#: Utility/B2bCartType.php:38
msgid "Demo"
msgstr "Demo"

#: Utility/B2bCartType.php:39
msgid "Booking"
msgstr "Reserva"

#: Utility/B2bCartType.php:40
msgid "Special"
msgstr "Especial"

#: Utility/B2bCartType.php:53
msgid "Regular Order"
msgstr "Pedido Regular"

#: Utility/B2bCartType.php:54
msgid "Rental Order"
msgstr "Pedido de Alquiler"

#: Utility/B2bCartType.php:55
msgid "Employee Order"
msgstr "Pedido de Empleado"

#: Utility/B2bCartType.php:56
msgid "Demo Order"
msgstr "Pedido Demo"

#: Utility/B2bCartType.php:57
msgid "Booking Order"
msgstr "Pedido de Reserva"

#: Utility/B2bCartType.php:58
msgid "Special Order"
msgstr "Pedido Espcecial"

#: Utility/FulfillmentStatus.php:34
#: View/Elements/Orders/order_products_table/dealer_order.ctp:12
#: View/Elements/Orders/order_products_table/dealerorder_invoice.ctp:22
#: View/Elements/Orders/order_products_table/invoice.ctp:27
#: View/Elements/Orders/order_products_table/need_to_confirm.ctp:14
#: View/Elements/Orders/order_products_table/processing.ctp:23
#: View/Elements/Orders/order_products_table/purchase_order.ctp:30
#: View/Elements/Orders/order_products_table/wholesale_invoice.ctp:24
msgid "Unfulfilled"
msgstr "Incumplido"

#: Utility/OrderStatus.php:210
msgctxt "order_status"
msgid "Unfulfilled"
msgstr ""

#: Utility/FulfillmentStatus.php:35
msgid "Fulfilled"
msgstr "Realizado"

#: Utility/FulfillmentStatus.php:36
msgid "Partially Fulfilled"
msgstr "Realizado Parcialmente"

#: Utility/FulfillmentStatus.php:37
msgid "Cancelled"
msgstr "Cancelado"

#: Utility/OrderStatus.php:255
msgctxt "order_status"
msgid "Cancelled"
msgstr ""

#: Utility/OrderPaymentMethod.php:39
msgid "Enable On Account"
msgstr "Habiliatar en la Cuenta"

#: Utility/OrderPaymentMethod.php:40
msgid "Enable Credit Card"
msgstr "Habilitar Tarjeta de Crédito"

#: Utility/OrderPaymentMethod.php:41
msgid "Enable On File Payment"
msgstr "Habilitar en Archivo de Pedido"

#: Utility/OrderPaymentMethod.php:55
msgid "On Account"
msgstr "En Cuenta"

#: Utility/OrderPaymentMethod.php:56 Utility/OrderPaymentMethodSubtype.php:43
msgid "Credit Card"
msgstr "Tarjeta de Crédito"

#: Utility/OrderPaymentMethod.php:57
msgid "On File Payment"
msgstr "Pago en Archivo"

#: Utility/OrderPaymentMethodSubtype.php:44;45
msgid "Affirm"
msgstr "Afrimar"

#: Utility/OrderPaymentMethodSubtype.php:46
msgid "Klarna"
msgstr "Klarna"

#: Utility/OrderPaymentStatus.php:53
msgid "Authorized"
msgstr "Autorizado"

#: Utility/OrderStatus.php:236
msgctxt "order_status"
msgid "Authorized"
msgstr ""

#: Utility/OrderPaymentStatus.php:54
msgid "Requested"
msgstr "Solicitado"

#: Utility/OrderPaymentStatus.php:56
msgid "Voided"
msgstr ""

#: Utility/OrderStatus.php:222;241
msgctxt "order_status"
msgid "Voided"
msgstr ""

#: Utility/OrderStatus.php:212;235;251
msgctxt "order_status"
msgid "Pending"
msgstr ""

#: Utility/RetailerCreditStatus.php:21
msgid "Pending"
msgstr "Pendiente"

#: Utility/OrderStatus.php:213
msgctxt "order_status"
msgid "Need To Confirm"
msgstr ""

#: Utility/OrderStatus.php:214
msgctxt "order_status"
msgid "Purchase Order"
msgstr ""

#: Utility/OrderStatus.php:215
msgctxt "order_status"
msgid "Dealer Order"
msgstr ""

#: Utility/OrderStatus.php:216;252
msgctxt "order_status"
msgid "Processing"
msgstr ""

#: View/OrderRefunds/refund.ctp:218
msgid "Processing"
msgstr "Procesando"

#: Utility/OrderStatus.php:217
msgctxt "order_status"
msgid "Not Picked Up"
msgstr ""

#: Utility/OrderStatus.php:218
msgctxt "order_status"
msgid "Ready for Delivery"
msgstr ""

#: Utility/OrderStatus.php:219
msgctxt "order_status"
msgid "Ready for Shipment"
msgstr ""

#: Utility/OrderStatus.php:220
msgctxt "order_status"
msgid "Delivered"
msgstr ""

#: Utility/OrderStatus.php:223
msgctxt "order_status"
msgid "Shipped"
msgstr ""

#: View/RetailerCredits/index.ctp:219
msgid "Shipped"
msgstr "Enviado"

#: Utility/OrderStatus.php:224
msgctxt "order_status"
msgid "In Transit"
msgstr ""

#: Utility/OrderStatus.php:225
msgctxt "order_status"
msgid "Canceled"
msgstr ""

#: Utility/OrderStatus.php:237
msgctxt "order_status"
msgid "Partially Paid"
msgstr ""

#: Utility/OrderStatus.php:239
msgctxt "order_status"
msgid "Partially Refunded"
msgstr ""

#: Utility/OrderStatus.php:253
msgctxt "order_status"
msgid "On Hold"
msgstr ""

#: Utility/OrderStatus.php:254
msgctxt "order_status"
msgid "Completed"
msgstr ""

#: Utility/OrderStatus.php:257
msgctxt "order_status"
msgid "Failed"
msgstr ""

#: Utility/OrderStatus.php:258
msgctxt "order_status"
msgid "Trash"
msgstr ""

#: Utility/OrderType.php:99
msgid "Ship from Store"
msgstr "Enviado desde la Tienda"

#: Utility/OrderType.php:101
msgid "Retailer Order"
msgstr "Pedido de Tienda"

#: Utility/OrderType.php:102
msgid "Direct"
msgstr "Directo"

#: Utility/PercentSource.php:23
msgctxt "percent_source"
msgid "From Retail Prices"
msgstr ""

#: Utility/PercentSource.php:24
msgctxt "percent_source"
msgid "From Base Prices"
msgstr ""

#: Utility/ProductSellDirect.php:43
msgid "Sell Direct Exclusively"
msgstr ""

#: Utility/ProductSellDirect.php:44
msgid "Sell Direct Unless Bundled"
msgstr ""

#: Utility/ProductSellDirect.php:45
msgid "Always Sell Direct"
msgstr ""

#: Utility/ProductSellDirect.php:46
msgid "Only in Unprotected Territories"
msgstr ""

#: Utility/ProductSellDirect.php:47 View/Products/ajax_edit.ctp:91
msgid "In-Stock Only"
msgstr ""

#: Utility/ProductSellDirect.php:48
msgid "Retail Exclusive Product"
msgstr ""

#: Utility/ProductStatus.php:27
msgid "Deactivated"
msgstr ""

#: Utility/ProductStatus.php:28
msgid "Ordered"
msgstr ""

#: Utility/ProductStatus.php:29
msgid "Discontinued"
msgstr ""

#: Utility/ProductStatus.php:30
msgid "Pre-order"
msgstr ""

#: Utility/ProductStatus.php:31
msgid "Incomplete"
msgstr ""

#: Utility/RateOption.php:19
msgctxt "install_rate_option"
msgid "Flat Rate Per Order"
msgstr ""

#: Utility/RateOption.php:20
msgctxt "install_rate_option"
msgid "Flat Rate Per Item"
msgstr ""

#: Utility/RateOption.php:21
msgctxt "install_rate_option"
msgid "Hourly Install Rate Per Item"
msgstr ""

#: Utility/RateOption.php:22
msgctxt "install_rate_option"
msgid "Percent of Sale"
msgstr ""

#: Utility/RateOption.php:48
msgctxt "install_rate_value"
msgid "Installers flat rate"
msgstr ""

#: Utility/RateOption.php:49
msgctxt "install_rate_value"
msgid "Installers flat rate per item"
msgstr ""

#: Utility/RateOption.php:50;63
msgctxt "install_rate_value"
msgid "Configure installation rates by retailer"
msgstr ""

#: Utility/RateOption.php:51
msgctxt "install_rate_value"
msgid "Installers percent rate"
msgstr ""

#: Utility/RateOption.php:61
msgctxt "install_rate_value"
msgid "Local delivery flat rate"
msgstr ""

#: Utility/RateOption.php:62
msgctxt "install_rate_value"
msgid "Local delivery flat rate per item"
msgstr ""

#: Utility/RateOption.php:64
msgctxt "install_rate_value"
msgid "Local delivery percent rate"
msgstr ""

#: Utility/RetailerCreditStatus.php:22
msgid "Overdue"
msgstr "Atrasado"

#: Utility/Discount/DiscountBuyXValueTypes.php:18
msgid "Minimum quantity of items"
msgstr ""

#: Utility/Discount/DiscountBuyXValueTypes.php:19
msgid "Minimum purchase amount"
msgstr ""

#: View/ApiClients/form.ctp:9 View/ApiClients/index.ctp:7
msgid "API Clients"
msgstr ""

#: View/ApiClients/form.ctp:25 View/Discounts/manage_discounts.ctp:37
#: View/Elements/branch.ctp:165 View/Elements/Layouts/head_scripts.ctp:24
#: View/Elements/Layouts/scripts.ctp:602
#: View/Elements/Orders/order_popup_header.ctp:232;256
#: View/Elements/Users/<USER>/retailer_inventory_import_popup_script.ctp:24
#: View/Inventory/index.ctp:74 View/Orders/dealerorders_index.ctp:150
#: View/Orders/index.ctp:144 View/Orders/invoice.ctp:104
#: View/Pages/admin_reset_password.ctp:61 View/Products/index.ctp:101
#: View/Products/index_retailer.ctp:89
#: View/Products/product_collections.ctp:222 View/Retailers/index.ctp:136
#: View/Staff/view.ctp:92;215 View/Users/<USER>
#: View/Users/<USER>
#: View/Products/product_collections.ctp:406
msgid "Cancel"
msgstr "Cancelar"

#: View/ApiClients/form.ctp:26 View/Branchs/store_permission.ctp:40
#: View/Discounts/manage_discounts.ctp:38
#: View/Elements/Users/<USER>/brand_dealer_options.ctp:336
#: View/Elements/Users/<USER>/retailer_dealer_options.ctp:48
#: View/Staff/view.ctp:93;221 View/UserTaxes/edit.ctp:43
#: View/Users/<USER>/Users/<USER>
#: View/Users/<USER>
#: View/Menus/edit.ctp:135
msgid "Save"
msgstr "Guardar"

#: View/ApiClients/form.ctp:33
msgid "App Details"
msgstr ""

#: View/ApiClients/form.ctp:40
msgid "API Keys"
msgstr ""

#: View/ApiClients/form.ctp:45 View/ApiClients/index.ctp:29
msgid "Client ID"
msgstr ""

#: View/ApiClients/form.ctp:52
msgid "Client Secret"
msgstr ""

#: View/ApiClients/form.ctp:53;70 View/Helper/LayoutHelper.php:227
msgid "Show"
msgstr "Mostrar"

#: View/ApiClients/form.ctp:67
msgid "Hide"
msgstr ""

#: View/ApiClients/index.ctp:18
msgid "Create a new client"
msgstr ""

#: View/ApiClients/index.ctp:30
msgid "Created"
msgstr ""

#: View/ApiClients/index.ctp:41 View/Discounts/manage_discounts.ctp:92
#: View/Elements/B2bCarts/b2b_order_product_table.ctp:86
#: View/Staff/view.ctp:191;197 View/Warehouses/edit.ctp:21
msgid "Delete"
msgstr "Borrar"

#: View/ApiClients/index.ctp:44
msgid "Are you sure you want to delete %s? This will revoke all credentials and cannot be reversed."
msgstr ""

#: View/B2bCarts/create_cart.ctp:78;80;271 View/B2bCarts/index.ctp:84
#: View/RetailerCredits/index.ctp:213 View/Products/index_retailer.ctp:75
msgid "Location"
msgstr "Ubicación"

#: View/B2bCarts/index.ctp:30
msgid "All Retailers"
msgstr "Todas las Tiendas"

#: View/B2bCarts/index.ctp:40
msgid "All Order Types"
msgstr "Todos los Demás Tipos"

#: View/B2bCarts/index.ctp:86
#: View/Elements/Orders/ajax_retailerspurchaseorders.ctp:101
#: View/Orders/ajax_dealerorders_index.ctp:56
msgid "PO #"
msgstr "Oficina Postal #"

#: View/B2bCarts/index.ctp:91 View/B2bCarts/view.ctp:63
msgid "Last Modified"
msgstr "Última Modificación"

#: View/B2bCarts/index.ctp:108
msgid "No Orders"
msgstr "Sin pedidos"

#: View/B2bCarts/index.ctp:109
msgid "Get started by creating a new order."
msgstr "Comience creando un nuevo pedido."

#: View/B2bCarts/index.ctp:112;60
msgid "Create Order"
msgstr "Crear pedido"

#: View/B2bCarts/view.ctp:109
#: View/Elements/Orders/dealerorder_invoice_totals.ctp:301
msgid "Custom"
msgstr "Personalizado"

#: View/B2bCarts/view.ctp:112
#: View/Elements/Orders/order_popup_address_table.ctp:22
#: View/InvoicePdf/generate_invoice.ctp:76
msgid "Ship To"
msgstr "Envío a"

#: View/B2bCarts/view.ctp:136 View/Elements/Orders/order_details_panel.ctp:42
msgid "Requested Ship Date"
msgstr "Fecha Solicitada de Envío"

#: View/B2bCarts/view.ctp:144 View/Elements/Orders/order_details_panel.ctp:25
#: View/InvoicePdf/generate_invoice.ctp:58
msgid "Purchase Order No"
msgstr "Orden de Compra Nro"

#: View/B2bCarts/view.ctp:153
msgid "Notes to seller"
msgstr "Notas al vendedor"

#: View/B2bCarts/view.ctp:161
msgid "Place wholesale order in %s"
msgstr "Hacer pedido al por mayor en %s"

#: View/B2bCarts/view.ctp:169 View/Products/view.ctp:65
msgid "Update Cart"
msgstr "Actualizar Carrito"

#: View/B2bCarts/view.ctp:175
msgid "Submit Order"
msgstr "Enviar Pedido"

#: View/B2bCarts/view.ctp:245
msgid "Please confirm your submission of this free order."
msgstr "Por favor confirme el envío de esta orden gratis."

#: View/BankAccounts/add.ctp:52
msgid "By clicking [accept], you authorize Shipearly to debit the bank account specified above for any amount owed for charges arising from your use of Shipearly's services and/or purchase of products from Shipearly, pursuant to Shipearly's website and terms, until this authorization is revoked. You may amend or cancel this authorization at any time by providing notice to Shipearly with 30 (thirty) days notice."
msgstr "Al hacer clic en [aceptar], autoriza a Shipearly a debitar de la cuenta bancaria especificada anteriormente cualquier monto adeudado por los cargos que surjan de su uso de los servicios de Shipearly y/o la compra de productos de Shipearly, de conformidad con el sitio web y los términos de Shipearly, hasta que se revoque esta autorización. Puede modificar o cancelar esta autorización en cualquier momento notificando a Shipearly con 30 (treinta) días de anticipación."

#: View/BankAccounts/add.ctp:53
msgid "If you use Shipearly's services or purchase additional products periodically pursuant to Shipearly's terms, you authorize Shipearly to debit your bank account periodically. Payments that fall outside of the regular debits authorized above will only be debited after your authorization is obtained."
msgstr "Si utiliza los servicios de Shipearly o compra productos adicionales periódicamente de conformidad con los términos de Shipearly, autoriza a Shipearly a debitar de su cuenta bancaria periódicamente los importes correspondientes. Los pagos que queden fuera de los débitos regulares autorizados anteriormente solo se debitarán después de obtener su autorización."

#: View/BankAccounts/add.ctp:55
msgid "Accept"
msgstr "Aceptar"

#: View/BankAccounts/add.ctp:60
msgid "Your account needs to be verified before you can begin using it to make payments"
msgstr "Su cuenta debe ser verificada antes de que pueda usarla para realizar pagos"

#: View/BankAccounts/add.ctp:61
msgid "You will receive and email at %s with instructions on how to verify your account."
msgstr "Recibirá un correo electrónico en %s con las instrucciones para verificar la cuenta."

#: View/BankAccounts/add.ctp:64
msgid "Your account was successfully added. It can now be used to make payments."
msgstr "Su cuenta ha sido agregada correctamente. Desde este momento pueda ser usada para realizar pagos."

#: View/BankAccounts/add.ctp:67
msgid "There was an error adding your account. Please try again."
msgstr "Tuvimos un error al agregar su cuenta. Por favor intente nuevamente."

#: View/BankAccounts/add.ctp:74
msgid "Please Wait..."
msgstr "Espere por favor…"

#: View/Branchs/index.ctp:7 View/Elements/branch.ctp:126;143
#: View/Elements/credit_terms_edit.ctp:52 View/Retailers/price_tier.ctp:38
msgid "Add New"
msgstr "Agregar Nuevo"

#: View/Branchs/store_permission.ctp:21 View/Elements/branch.ctp:56
msgid "Permission"
msgstr "Permiso"

#: View/Branchs/store_permission.ctp:22
msgid "Master Retailer"
msgstr "Tienda Maestra"

#: View/BrandStaff/index.ctp:8 View/Staff/index.ctp:11
msgid "Add Employee"
msgstr "Agregar Empleado"

#: View/BrandStaff/view.ctp:69
#: View/Elements/Users/<USER>/profile_brand_retailer.ctp:195
#: View/Elements/Users/<USER>/profile_sales_rep.ctp:102
#: View/Elements/Users/<USER>/profile_store_associate.ctp:99
#: View/Staff/view.ctp:120 View/Users/<USER>
msgid "Preferred Language"
msgstr "Idioma preferido"

#: View/BrandStaff/view.ctp:110 View/Staff/view.ctp:194
msgid "Delete this staff account? This operation cannot be undone."
msgstr "¿Remover esta cuenta de personal? Esta operación no puede ser revertida."

#: View/Dashboards/index.ctp:53
msgid "Watch the Quick Start Video"
msgstr "Ver Video de Inicio Rápido"

#: View/DealerOrderRefunds/refund.ctp:115
msgid "Dealer Refund"
msgstr "Reembolso al Distribuidor"

#: View/DealerOrderRefunds/refund.ctp:125 View/OrderRefunds/refund.ctp:160
msgid "Balance Before Refund"
msgstr "Balance Antes de Reembolso"

#: View/DealerOrderRefunds/refund.ctp:131 View/OrderRefunds/refund.ctp:166
msgid "Balance After Refund"
msgstr "Balancr Después de Reembolso"

#: View/DealerOrderRefunds/refund.ctp:142 View/OrderRefunds/refund.ctp:177
msgid "Reason for refund"
msgstr "Razón del Reembolso"

#: View/DealerOrderRefunds/refund.ctp:149 View/OrderRefunds/refund.ctp:191
msgid "Place refund in %s"
msgstr "Coloque el Reembolso en %s"

#: View/DealerOrderRefunds/refund.ctp:154 View/OrderRefunds/refund.ctp:196
msgid ""
"Inventory for refunded items will not be adjusted automatically.<br />\n"
"\t\t\tTo adjust please do so manually outside ShipEarly."
msgstr ""
"El inventario de artículos reembolsados no se ajustará automáticamente.<br />\n"
"\t\t\tPara ajustar, hágalo manualmente fuera de ShipEarly."

#: View/Discounts/index.ctp:16
msgid "Create Discount"
msgstr ""

#: View/Discounts/manage_discounts.ctp:98
msgid "Are you sure you wish to delete this discount? This action cannot be undone."
msgstr ""

#: View/Discounts/manage_discounts.ctp:305
#: View/Elements/Discounts/manage_discounts/usage_limits_card.ctp:47
msgid "Ignored by automatic discounts"
msgstr ""

#: View/Elements/branch.ctp:38
msgid "Location Name"
msgstr ""

#: View/Elements/branch.ctp:40
msgid "Action"
msgstr "Acción"

#: View/Elements/branch.ctp:41
#: View/ShippingZones/ajax_tax_override_table.ctp:12
#: View/ShippingZones/tax_override_form.ctp:42 View/UserTaxes/edit.ctp:69;135
#: View/UserTaxes/edit_registration.ctp:68 View/UserTaxes/registrations.ctp:80
msgid "Tax Rate"
msgstr "Tasa de Impuesto"

#: View/Elements/branch.ctp:42;75 View/UserTaxes/edit.ctp:78;145
#: View/UserTaxes/edit_registration.ctp:78 View/UserTaxes/registrations.ctp:81
msgid "Tax Name"
msgstr "Nombre del Impuesto"

#: View/Elements/branch.ctp:43
msgid "Scheduling"
msgstr "Planificación"

#: View/Elements/branch.ctp:58
msgid "Store Timing"
msgstr "Tiempo de la Tienda"

#: View/Elements/branch.ctp:64
#: View/Elements/Users/<USER>/retailer_inventory_form.ctp:323;324
msgid "Example: 7.2 for 7.2%"
msgstr ""

#: View/Elements/branch.ctp:91 View/Elements/Layouts/scripts.ctp:294
#: View/Elements/Manufacturers/manufacturers.ctp:113
#: View/Elements/Retailers/ajax_index.ctp:80
#: View/Elements/Retailers/ajax_index_salesrep.ctp:31
msgid "Connected"
msgstr "Conectado"

#: View/Elements/branch.ctp:95
msgid "Sync"
msgstr "Sincronizar"

#: View/Elements/branch.ctp:102
msgid "No Locations"
msgstr "No hay ubicaciones"

#: View/Elements/branch.ctp:132
msgid "Store pickup name"
msgstr "Nombre de recogida en tienda"

#: View/Elements/branch.ctp:135
msgid "Calendly Link for Store pickup"
msgstr "Enlace de Calendly para recogida en tienda"

#: View/Elements/branch.ctp:149
msgid "Local Delivery Name"
msgstr "Nombre de Entrega Local"

#: View/Elements/branch.ctp:152
msgid "Calendly Link for Local Delivery"
msgstr "Enlace de Calnedly para retiro local"

#: View/Elements/branch.ctp:158
msgid "We've partnered with Calendly to make scheduling appointment without back and forth emails or phone calls with customers easier. Basic accounts are free but for best service the $8/month plan can cover all your stores and local delivery to sync with your calender and set customizable rules."
msgstr "Nos hemos asociado con Calendly para facilitar la programación de citas sin correos electrónicos o llamadas telefónicas de ida y vuelta con los clientes. Las cuentas básicas son gratuitas, pero para un mejor servicio, el plan de USD $8/mes puede cubrir todas sus tiendas y entregas locales para sincronizar con su calendario y establecer reglas personalizables."

#: View/Elements/branch.ctp:166 View/Elements/credit_terms_edit.ctp:56
#: View/Elements/Users/<USER>/profile_brand_retailer.ctp:233
#: View/Orders/edit.ctp:135 View/Products/product_collections.ctp:102
#: View/Products/view.ctp:356 View/Retailers/price_tier.ctp:41
#: View/Users/<USER>/Users/<USER>
#: View/Users/<USER>
msgid "Update"
msgstr "Actualizar"

#: View/Elements/credit_terms_edit.ctp:48
#: View/Elements/Discounts/manage_discounts/retailer_eligibility_card.ctp:69
#: View/Retailers/edit.ctp:61
msgid "Credit Terms"
msgstr "Términos de Crédito"

#: View/Elements/credit_terms_edit.ctp:87
#: View/Elements/Discounts/manage_discounts/discount_code_card.ctp:103
msgid "Description"
msgstr "Descripción"

#: View/Elements/credit_terms_edit.ctp:90
msgid "Days to Pay"
msgstr "Días para pagar"

#: View/Elements/credit_terms_edit.ctp:93
msgid "Discount Days"
msgstr "Días de descuento"

#: View/Elements/credit_terms_edit.ctp:96
msgid "Early Payment Discount %"
msgstr "% de descuento por pago anticipado"

#: View/Elements/credit_terms_edit.ctp:99
msgid "Condition"
msgstr "Condición"

#: View/Elements/credit_terms_edit.ctp:102
msgid "Condition Value"
msgstr "Valor de condición"

#: View/Elements/credit_terms_edit.ctp:105
msgid "Minimum Condition"
msgstr "Condición mínima"

#: View/Elements/credit_terms_edit.ctp:87
msgid "Describes the payment term to be displayed to user"
msgstr "Describe el plazo de pago que se mostrará al usuario."

#: View/Elements/credit_terms_edit.ctp:90
msgid "After fulfillment how many days account has to pay invoice"
msgstr "Después del cumplimiento, ¿cuántos días tiene la cuenta para pagar la factura?"

#: View/Elements/credit_terms_edit.ctp:93
msgid "Days after invoice issued if paid in full account qualifies for discount"
msgstr "Días después de la emisión de la factura, si se paga en su totalidad, califica para el descuento"

#: View/Elements/credit_terms_edit.ctp:96
msgid "Percentage off invoice account receives if paid before discount day expiry"
msgstr "Porcentaje de descuento en la factura que la cuenta recibe si se paga antes de que expire el día del descuento"

#: View/Elements/credit_terms_edit.ctp:99
msgid "Qualifier to use in Condition Value"
msgstr "Calificador para usar en el valor de condición"

#: View/Elements/credit_terms_edit.ctp:102
msgid "Amount customer must reach to display credit term"
msgstr "Monto que el cliente debe alcanzar para mostrar el plazo de crédito"

#: View/Elements/credit_terms_edit.ctp:105
msgid "Products that must be ordered to meet condition for credit term"
msgstr "Productos que se deben pedir para cumplir con las condiciones del plazo de crédito"

#: View/Elements/credit_terms_edit.ctp:108
msgid "The status in which the invoice is marked once order is fulfilled"
msgstr "El estado en el que se marca la factura una vez cumplido el pedido"

#: View/Elements/latest.ctp:17 View/Elements/Orders/ajax_index.ctp:28
#: View/Elements/Orders/ajax_retailerspurchaseorders.ctp:98
#: View/Orders/ajax_dealerorders_index.ctp:45
msgid "Order #"
msgstr "Orden #"

#: View/Elements/latest.ctp:27 View/Elements/Orders/ajax_index.ctp:39
#: View/Elements/Orders/ajax_retailerspurchaseorders.ctp:104
#: View/Orders/ajax_dealerorders_index.ctp:59
msgid "Items"
msgstr "Elementos"

#: View/Elements/Orders/dealerorder_invoice_totals.ctp:130
#: View/Elements/Orders/order_invoice_totals.ctp:145
#: View/Elements/B2bCarts/b2b_order_product_table.ctp:162
#: Controller/B2bCartsController.php:179
msgid "%d item"
msgid_plural "%d items"
msgstr[0] "%d artículo"
msgstr[1] "%d elementos"

#: View/Elements/latest.ctp:23 View/Elements/Orders/ajax_index.ctp:42
#: View/Orders/ajax_dealerorders_index.ctp:51
msgid "Manufacturer"
msgstr "Fabricante"

#: View/Elements/latest.ctp:77
msgid "No Activity Required"
msgstr "No se Requiere Actividad"

#: View/Elements/storetiming.ctp:14 View/Users/<USER>
msgid "Day"
msgstr "Día"

#: View/Elements/storetimingrow.ctp:38
msgid "to"
msgstr "a"

#: View/Elements/B2bCarts/b2b_cart_row.ctp:15
msgid "View"
msgstr "Ver"

#: View/Elements/B2bCarts/b2b_order_product_row.ctp:106
#: View/Elements/Products/product_grid_item.ctp:77
msgid "Min QTY"
msgstr "Cantidad Min"

#: View/Elements/B2bCarts/b2b_order_product_row.ctp:111
#: View/Elements/Products/product_grid_item.ctp:83
msgid "Max QTY"
msgstr "Cantidad Max"

#: View/Elements/B2bCarts/b2b_order_product_table.ctp:13
msgid "Discount %s"
msgstr "Descuento %s"

#: View/Elements/B2bCarts/b2b_order_product_table.ctp:42
msgid "Minimum of %s required"
msgstr "Mínimo de %s requerido"

#: View/Elements/B2bCarts/b2b_order_product_table.ctp:50
msgid "Custom Name"
msgstr "Nombre Personalizado"

#: View/Elements/B2bCarts/b2b_order_product_table.ctp:79
msgid "Title"
msgstr "Título"

#: View/Elements/B2bCarts/b2b_order_product_table.ctp:80
#: View/Elements/Products/catalogue_variants.ctp:20
#: View/InvoicePdf/generate_invoice.ctp:95 View/Products/view.ctp:517
msgid "SKU"
msgstr "SKU"

#: View/Elements/B2bCarts/b2b_order_product_table.ctp:81
#: View/Products/view.ctp:495
msgid "Warehouse"
msgstr "Depósito"

#: View/Elements/B2bCarts/b2b_order_product_table.ctp:82
#: View/Elements/Products/catalogue_variants.ctp:22 View/Products/view.ctp:519
msgid "Stock"
msgstr "Stock"

#: View/Elements/B2bCarts/b2b_order_product_table.ctp:84
#: View/Elements/Products/catalogue_variants.ctp:24 View/Products/view.ctp:521
msgid "Min/Max Order Quantity"
msgstr "Min/Max Cantidad en Pedido"

#: View/Elements/B2bCarts/b2b_order_product_table.ctp:85
msgid "Price"
msgstr "Precio"

#: View/Elements/B2bCarts/b2b_order_product_table.ctp:148
msgid "Tax (%s%%)"
msgstr "Impuesto (%s%)"

#: View/Elements/B2bCarts/b2b_order_type_table.ctp:19
msgid "Select Order Type:"
msgstr "Selección el tipo de Pedido:"

#: View/Elements/B2bCarts/custom_address_inputs.ctp:30
#: View/Elements/Users/<USER>/profile_brand_retailer.ctp:101
#: View/Elements/Users/<USER>/profile_store_associate.ctp:38
#: View/Users/<USER>
msgid "Email Address"
msgstr "Dirección de Correo Electrónico"

#: View/Elements/B2bCarts/custom_address_inputs.ctp:51
#: View/Elements/Users/<USER>/profile_brand_retailer.ctp:108;109
#: View/Elements/Users/<USER>/profile_store_associate.ctp:45;46
#: View/Users/<USER>
msgid "Street Line 1"
msgstr "Calle Línea 1"

#: View/Elements/B2bCarts/custom_address_inputs.ctp:57
#: View/Elements/Users/<USER>/profile_brand_retailer.ctp:116;117
#: View/Elements/Users/<USER>/profile_store_associate.ctp:53;54
#: View/Users/<USER>
msgid "Street Line 2"
msgstr "Calle Línea 2"

#: View/Elements/B2bCarts/custom_address_inputs.ctp:90
#: View/Elements/Users/<USER>/profile_brand_retailer.ctp:155
#: View/Elements/Users/<USER>/profile_store_associate.ctp:68;69
msgid "Zipcode"
msgstr "Código Postal"

#: View/Elements/B2bCarts/existing_cart_row.ctp:41
msgid "# of Items"
msgstr "# de Productos"

#: View/Elements/B2bCarts/existing_cart_table.ctp:10
msgid "Add to Existing Cart"
msgstr "Agregar al Carrito Existente"

#: View/Elements/Discounts/manage_discounts/active_dates_card.ctp:8
msgid "Shipping Window"
msgstr ""

#: View/Elements/Discounts/manage_discounts/active_dates_card.ctp:16;39
msgid "Start Date"
msgstr ""

#: View/Elements/Discounts/manage_discounts/active_dates_card.ctp:25;48
msgid "End Date"
msgstr ""

#: View/Elements/Discounts/manage_discounts/active_dates_card.ctp:31
msgid "Active Dates"
msgstr ""

#: View/Elements/Discounts/manage_discounts/discount_code_card.ctp:18
msgid "Generate Code"
msgstr ""

#: View/Elements/Discounts/manage_discounts/discount_code_card.ctp:33
msgid "Single"
msgstr ""

#: View/Elements/Discounts/manage_discounts/discount_code_card.ctp:34
msgid "Bulk"
msgstr ""

#: View/Elements/Discounts/manage_discounts/discount_code_card.ctp:35
msgid "Automatic Discount"
msgstr ""

#: View/Elements/Discounts/manage_discounts/discount_code_card.ctp:36
msgid "B2B"
msgstr ""

#: View/Elements/Discounts/manage_discounts/discount_code_card.ctp:40
msgid "Generate Options"
msgstr ""

#: View/Elements/Discounts/manage_discounts/discount_code_card.ctp:59
msgid "Number of discount codes"
msgstr ""

#: View/Elements/Discounts/manage_discounts/discount_code_card.ctp:75
msgid "Customers will enter this discount code at checkout"
msgstr ""

#: View/Elements/Discounts/manage_discounts/discount_code_card.ctp:84
msgid "Code Prefix"
msgstr ""

#: View/Elements/Discounts/manage_discounts/discount_code_card.ctp:95
#: View/Elements/Discounts/manage_discounts/discount_term_card.ctp:187
msgid "Discount Type"
msgstr ""

#: View/Elements/Discounts/manage_discounts/discount_code_card.ctp:105
msgid "Customers will see this when provided information about the discount"
msgstr ""

#: View/Elements/Discounts/manage_discounts/discount_term_card.ctp:15
msgid "Applies only to selected products"
msgstr ""

#: View/Elements/Discounts/manage_discounts/discount_term_card.ctp:20
msgid "Customer Buys"
msgstr ""

#: View/Elements/Discounts/manage_discounts/discount_term_card.ctp:34
msgid "Any items from"
msgstr ""

#: View/Elements/Discounts/manage_discounts/discount_term_card.ctp:67
#: View/RetailerCredits/index.ctp:216
msgid "Amount"
msgstr "Monto"

#: View/Elements/Discounts/manage_discounts/discount_term_card.ctp:83;148;264
msgid "List of Product Titles"
msgstr ""

#: View/Elements/Discounts/manage_discounts/discount_term_card.ctp:95;160;276
msgid "List of Product Variants"
msgstr ""

#: View/Elements/Discounts/manage_discounts/discount_term_card.ctp:107;172;252
msgid "List of Categories"
msgstr ""

#: View/Elements/Discounts/manage_discounts/discount_term_card.ctp:114
msgid "Customer Gets"
msgstr ""

#: View/Elements/Discounts/manage_discounts/discount_term_card.ctp:179
msgid "At a Discounted Value"
msgstr ""

#: View/Elements/Discounts/manage_discounts/discount_term_card.ctp:195;212
msgid "Discount Value"
msgstr ""

#: View/Elements/Discounts/manage_discounts/discount_term_card.ctp:221
msgid "Exclude shipping rates over a certain amount"
msgstr ""

#: View/Elements/Discounts/manage_discounts/discount_term_card.ctp:229
msgid "Applies To"
msgstr ""

#: View/Elements/Discounts/manage_discounts/discount_term_card.ctp:288
msgid "List of Collections"
msgstr ""

#: View/Elements/Discounts/manage_discounts/discount_term_card.ctp:300
msgid "List of Product Tags"
msgstr ""

#: View/Elements/Discounts/manage_discounts/discount_term_card.ctp:310
msgid "Only show products eligible for this discount"
msgstr ""

#: View/Elements/Discounts/manage_discounts/discount_term_card.ctp:316
msgid "Minimum Requirements"
msgstr ""

#: View/Elements/Discounts/manage_discounts/discount_term_card.ctp:338
msgid "Orders with amount over"
msgstr ""

#: View/Elements/Discounts/manage_discounts/discount_term_card.ctp:351
msgid "Orders with quantity over"
msgstr ""

#: View/Elements/Discounts/manage_discounts/retailer_eligibility_card.ctp:17
#: View/Elements/Inventory/ajax_index.ctp:18 View/Helper/SidebarHelper.php:254
msgid "Retailers"
msgstr "Tiendas"

#: View/Elements/Discounts/manage_discounts/retailer_eligibility_card.ctp:40
#: View/Retailers/price_tier.ctp:34
msgid "Pricing Tiers"
msgstr ""

#: View/Elements/Discounts/manage_discounts/retailer_eligibility_card.ctp:52
msgid "List of Retailers"
msgstr ""

#: View/Elements/Discounts/manage_discounts/retailer_eligibility_card.ctp:65
msgid "Default Credit Terms"
msgstr ""

#: View/Elements/Discounts/manage_discounts/retailer_eligibility_card.ctp:72
#: View/Retailers/edit.ctp:66
msgid "Add or edit credit terms"
msgstr "Agregar o Editar Términos de Crédito"

#: View/Elements/Discounts/manage_discounts/usage_limits_card.ctp:14
msgid "Usage Limits"
msgstr ""

#: View/Elements/Discounts/manage_discounts/usage_limits_card.ctp:18
msgid "Unlimited"
msgstr ""

#: View/Elements/Discounts/manage_discounts/usage_limits_card.ctp:21
msgid "Usage Limit Quantity"
msgstr ""

#: View/Elements/Discounts/manage_discounts/usage_limits_card.ctp:44
msgid "Limit 1 Per Customer (tracked by customer email)"
msgstr ""

#: View/Elements/Inventory/ajax_index.ctp:14
#: View/Elements/Orders/order_products_table/purchase_order.ctp:76
#: View/Fulfillments/add.ctp:72
#: Controller/ReportsController.php:281;1176
msgid "Product"
msgstr "Producto"

#: View/Elements/Inventory/ajax_index.ctp:15
#: View/Elements/Inventory/ajax_retailerproducts.ctp:4
msgid "Part #"
msgstr "Parte #"

#: View/Elements/Inventory/ajax_index.ctp:16
msgid "Barcode (UPC, EAN, etc)"
msgstr "Código de barras (UPC, EAN, etc.)"

#: View/Elements/Inventory/ajax_index.ctp:22 View/Products/view.ctp:585
msgid "Vendor"
msgstr "Vendedor"

#: View/Elements/Inventory/ajax_index.ctp:23
msgid "Sum of all inventory for every location"
msgstr "La suma de todo el inventario para cada ubicación"

#: View/Elements/Inventory/ajax_index.ctp:24;31
msgid "On Hand"
msgstr "En existencias"

#: View/Elements/Inventory/ajax_index.ctp:31
msgid "Total inventory available at the location"
msgstr "Inventario total disponible en la ubicación"

#: View/Elements/Inventory/ajax_index.ctp:32
msgid "Inventory that is part of an unfulfilled order"
msgstr "Inventario que es parte de un pedido no completado"

#: View/Elements/Inventory/ajax_index.ctp:32
msgid "Committed"
msgstr "Comprometido"

#: View/Elements/Inventory/ajax_index.ctp:105
#: View/Elements/Products/productlist_retailer.ctp:137
msgid "No Products"
msgstr "No hay productos"

#: View/Elements/Inventory/ajax_retailerproducts.ctp:5
msgid "Part Name"
msgstr ""

#: View/Elements/Inventory/ajax_retailerproducts.ctp:9
msgid "Inventory $"
msgstr ""

#: View/Elements/Layouts/head_scripts.ctp:29
#: View/Elements/Layouts/modals.ctp:39 View/Pages/admin_reset_password.ctp:66
#: View/Products/addcollection.ctp:18 View/Users/<USER>
#: View/Users/<USER>/Warehouses/add.ctp:26
#: View/Warehouses/edit.ctp:18
msgid "Submit"
msgstr "Enviar"

#: View/Elements/Layouts/modals.ctp:28
msgid "I am a:"
msgstr "Soy:"

#: View/Elements/Layouts/profile_dropdown.ctp:30 View/Users/<USER>
msgid "My Account"
msgstr "Mi Cuenta"

#: View/Elements/Layouts/profile_dropdown.ctp:36
msgid "API Settings"
msgstr "Configuracion de la API"

#: View/Elements/Layouts/profile_dropdown.ctp:43 View/Users/<USER>
msgid "Integrations"
msgstr "Integraciones"

#: View/Elements/Layouts/profile_dropdown.ctp:58
msgid "E-Commerce Settings"
msgstr "Configuración de E-Commerce"

#: View/Elements/Layouts/profile_dropdown.ctp:59
msgid "Inventory Settings"
msgstr "Configuración de Inventario"

#: View/Elements/Layouts/profile_dropdown.ctp:65
msgid "Checkout Settings"
msgstr "Configuración de Pago"

#: View/Elements/Layouts/profile_dropdown.ctp:73
#: View/Users/<USER>
msgid "Shipment Settings"
msgstr "Configuración de Envío"

#: View/Elements/Layouts/profile_dropdown.ctp:81 View/UserTaxes/edit.ctp:9;37
#: View/UserTaxes/index.ctp:7 View/UserTaxes/registrations.ctp:8;52
msgid "Tax Settings"
msgstr "Configuración de Impuestos"

#: View/Elements/Layouts/profile_dropdown.ctp:89
msgid "Notifications"
msgstr "Notificaciones"

#: View/Elements/Layouts/profile_dropdown.ctp:103
#: View/Users/<USER>
msgid "Subscription"
msgstr "Subscripción"

#: View/Elements/Layouts/profile_dropdown.ctp:116
#: View/Users/<USER>
msgid "Change Password"
msgstr "Cambiar Contraseña"

#: View/Elements/Layouts/scripts.ctp:295
#: View/Elements/Manufacturers/manufacturers.ctp:113
#: View/Elements/Retailers/ajax_index.ctp:81
#: View/Elements/Retailers/ajax_index_salesrep.ctp:32
msgid "Not Connected"
msgstr "No Conectado"

#: View/Elements/Layouts/scripts.ctp:596
msgid "Select Brand and Order Type"
msgstr "Seleccionar marca y tipo de pedido"

#: View/Elements/Layouts/scripts.ctp:642
msgid "The cart could not be emptied. Please try again later."
msgstr "El carrito no se pudo vaciar. Por favor, inténtelo de nuevo más tarde."

#: View/Elements/Layouts/scripts.ctp:645
msgid "This cart will be emptied and deleted. Are you sure you want to empty this cart?"
msgstr "Este carrito se vaciará y eliminará. ¿Está seguro de que quiere vaciar este carrito?"

#: View/Elements/Layouts/scripts.ctp:729
#: View/Elements/Products/catalogue_variants.ctp:27
msgid "Add to Order"
msgstr "Agregar al Pedido"

#: View/Elements/Layouts/scripts.ctp:782
msgid "Next"
msgstr "Próximo"

#: View/Elements/Layouts/scripts.ctp:783
msgid "Previous"
msgstr "Previo"

#: View/Elements/Layouts/validate-messages.ctp:8
msgid "Please enter your username"
msgstr "Por favor ingrese su nombre de usuario"

#: View/Elements/Layouts/validate-messages.ctp:9
msgid "Please enter your email"
msgstr "Por favor ingrese su corre electrónico"

#: View/Elements/Layouts/validate-messages.ctp:10
msgid "Please enter a valid email"
msgstr "Por favor ingrese un correo electrónico válido"

#: View/Elements/Layouts/validate-messages.ctp:11
msgid "Please enter username"
msgstr "Por favor ingrese nombre de usuario"

#: View/Elements/Layouts/validate-messages.ctp:12
msgid "Please enter your password"
msgstr "Por favor ingrese su contraseña"

#: View/Elements/Layouts/validate-messages.ctp:13
msgid "Passwords must contain at least 8 characters, including at least one uppercase character, one lowercase character, and a number."
msgstr "Las contraseñas deben contener al menos 8 caracteres, incluido al menos un carácter en mayúscula, un carácter en minúscula y un número."

#: View/Elements/Layouts/validate-messages.ctp:14
msgid "Please enter current password"
msgstr "Por favor ingrese su contraseña actual"

#: View/Elements/Layouts/validate-messages.ctp:15
msgid "Please enter new password"
msgstr "Por favor ingrese la nueva contraseña"

#: View/Elements/Layouts/validate-messages.ctp:16
msgid "Please retype your password"
msgstr "Por favor re ingrese su contraseña"

#: View/Elements/Layouts/validate-messages.ctp:19
msgid "Please enter page title"
msgstr "Por favor ingrese título de página"

#: View/Elements/Layouts/validate-messages.ctp:22
msgid "Please enter company name"
msgstr "Por favor ingrese nombre de la compañía"

#: View/Elements/Layouts/validate-messages.ctp:23
msgid "Passwords do not match. Please try again"
msgstr "Las contraseñas no coinciden. Por favor intente de nuevo"

#: View/Elements/Layouts/validate-messages.ctp:24
msgid "Please select your e-commerce site type"
msgstr "Por favor seleccione su tipo de sitio de comercio electrónico"

#: View/Elements/Layouts/validate-messages.ctp:25
msgid "Please enter your shop url"
msgstr "Por favor ingrese el URL de su tienda"

#: View/Elements/Layouts/validate-messages.ctp:26
msgid "Please enter a valid shop url"
msgstr "Por favor ingrese un url de tienda válido"

#: View/Elements/Layouts/validate-messages.ctp:27
msgid "Please enter your API Username"
msgstr "Por favor ingrese su nombre de usuario de API"

#: View/Elements/Layouts/validate-messages.ctp:28
msgid "Please enter your API key"
msgstr "Por favor ingrese su llave API"

#: View/Elements/Layouts/validate-messages.ctp:29
msgid "Please enter your paypal id"
msgstr "Por favor ingrese su id de PayPal"

#: View/Elements/Layouts/validate-messages.ctp:30
msgid "Please enter a valid paypal id"
msgstr "Por favor ingrese un id de PayPal válido"

#: View/Elements/Layouts/validate-messages.ctp:31
msgid "Please enter first name"
msgstr "Por favor ingrese su nombre"

#: View/Elements/Layouts/validate-messages.ctp:32
msgid "Please enter last name"
msgstr "Por favor ingrese su apellido"

#: View/Elements/Layouts/validate-messages.ctp:33
msgid "Please select country"
msgstr "Por favor seleccione su país"

#: View/Elements/Layouts/validate-messages.ctp:34
msgid "Please select state/province"
msgstr "Por favor seleccione su estado/provincia"

#: View/Elements/Layouts/validate-messages.ctp:35;61 Model/User.php:validation
#: for field zipcode
msgid "Please enter your zipcode"
msgstr "Por favor ingrese su código postal"

#: View/Elements/Layouts/validate-messages.ctp:36
msgid "Please enter a valid postal code"
msgstr "Por favor ingrese un código postal válido"

#: View/Elements/Layouts/validate-messages.ctp:37
msgid "Please enter a valid zip code"
msgstr "Por favor ingrese un código postal válido"

#: View/Elements/Layouts/validate-messages.ctp:38
msgid "Please enter your street name"
msgstr "Por favor ingrese su calle"

#: View/Elements/Layouts/validate-messages.ctp:39
msgid "Please enter your city"
msgstr "Por favor ingrese su ciudad"

#: View/Elements/Layouts/validate-messages.ctp:40
msgid "Time zone is a required field"
msgstr "La zona horaria es un campo obligatorio"

#: View/Elements/Layouts/validate-messages.ctp:40
msgid "You must agree to the Terms & Conditions and Privacy Policy"
msgstr "Debe aceptar los Términos y Condiciones y las Políticas de Privacidad"

#: View/Elements/Layouts/validate-messages.ctp:41
msgid "Please upload valid image type"
msgstr "Cargue un tipo de imagen válido"

#: View/Elements/Layouts/validate-messages.ctp:42
msgid "Please choose your primary currency"
msgstr "Elija su moneda principal"

#: View/Elements/Layouts/validate-messages.ctp:43
msgid "Please enter store working timing"
msgstr "Ingrese el horario de trabajo de la tienda"

#: View/Elements/Layouts/validate-messages.ctp:44
msgid "Please enter valid min order Amount"
msgstr "Ingrese un monto de pedido mínimo válido"

#: View/Elements/Layouts/validate-messages.ctp:45
msgid "Please enter flat rate"
msgstr "Por favor ingrese tarifa plana"

#: View/Elements/Layouts/validate-messages.ctp:46
msgid "Please enter return policy"
msgstr "Por favor ingrese la política de devolución"

#: View/Elements/Layouts/validate-messages.ctp:49
msgid "Please enter invoice amount"
msgstr "Por favor ingrese el monto de la factura"

#: View/Elements/Layouts/validate-messages.ctp:50
msgid "Please enter minimum quantity"
msgstr "Por favor ingrese la cantidad mínima"

#: View/Elements/Layouts/validate-messages.ctp:51
msgid "Please enter minimum Order amount for free shipping"
msgstr "Por favor ingrese el monto mínimo del pedido para el envío gratuito"

#: View/Elements/Layouts/validate-messages.ctp:52
msgid "Please enter product UPC code"
msgstr "Ingrese el código UPC del producto"

#: View/Elements/Layouts/validate-messages.ctp:53
msgid "Please enter product part number"
msgstr "Ingrese el número de parte del producto"

#: View/Elements/Layouts/validate-messages.ctp:54
msgid "Please choose at least one category"
msgstr "Por favor elige al menos una categoría"

#: View/Elements/Layouts/validate-messages.ctp:55 Model/Product.php:validation
#: for field b2b_min_order_quantity;validation b2b_max_order_quantity
msgid "Maximum B2B Quantity must be greater than or equal to Minimum"
msgstr "La cantidad máxima B2B debe ser mayor o igual que la mínima"

#: View/Elements/Layouts/validate-messages.ctp:58
msgid "Please enter a store name"
msgstr "Por favor ingrese el nombre de la tienda"

#: View/Elements/Layouts/validate-messages.ctp:59
msgid "Please enter store description"
msgstr "Por favor ingrese la descripción de la tienda"

#: View/Elements/Layouts/validate-messages.ctp:60
msgid "Please enter a store address"
msgstr "Por favor ingrese la dirección de la tienda"

#: View/Elements/Layouts/validate-messages.ctp:62
msgid "Please enter store timing details"
msgstr "Por favor ingrese el horario de la tienda"

#: View/Elements/Layouts/validate-messages.ctp:63
msgid "Please enter phone number"
msgstr "Por favor ingrese número de teléfono"

#: View/Elements/Layouts/validate-messages.ctp:64
msgid "Please enter a valid phone number"
msgstr "Por favor ingrese un número de teléfono válido"

#: View/Elements/Layouts/validate-messages.ctp:65
msgid "Please enter store contact Email"
msgstr "Por favor ingrese correo electrónico de contacto de la tienda"

#: View/Elements/Layouts/validate-messages.ctp:68
msgid "Please enter In store code"
msgstr "Por favor ingrese código de la tienda"

#: View/Elements/Layouts/validate-messages.ctp:69
msgid "Please enter Tracking No."
msgstr "Por favor ingrese número de seguimiento."

#: View/Elements/Layouts/validate-messages.ctp:70
msgid "Please select courier type."
msgstr "Por favor seleccione el tipo de mensajería."

#: View/Elements/Layouts/validate-messages.ctp:73
msgid "Please enter title for your review"
msgstr "Por favor ingrese título para su reseña"

#: View/Elements/Layouts/validate-messages.ctp:74
msgid "Please enter comments. comments field can't be empty"
msgstr "Por favor ingrese comentarios. El campo de comentarios no puede estar vacío"

#: View/Elements/Layouts/validate-messages.ctp:77;78
msgid "Please enter Inventory API Key"
msgstr "Por favor ingrese la clave API de inventario"

#: View/Elements/Layouts/validate-messages.ctp:79
msgid "Please enter Inventory API Password"
msgstr "Por favor ingrese la contraseña API de inventario"

#: View/Elements/Layouts/validate-messages.ctp:80
msgid "Please enter Inventory Account Id"
msgstr "Por favor ingrese el Id de la Cuenta de Inventario"

#: View/Elements/Layouts/validate-messages.ctp:81
msgid "Please enter store name"
msgstr "Por favor ingrese el nombre de la tienda"

#: View/Elements/Layouts/validate-messages.ctp:82
msgid "Please select Employee"
msgstr "Por favor selección empleado"

#: View/Elements/Layouts/validate-messages.ctp:83;86
msgid "Please select Register"
msgstr "Por favor seleccione Registrarse"

#: View/Elements/Layouts/validate-messages.ctp:84
msgid "Please select Store"
msgstr "Por favor seleccione Tienda"

#: View/Elements/Layouts/validate-messages.ctp:85
msgid "Please select User"
msgstr "Por favor selección Usuario"

# Outlet meaning a discount store?
#: View/Elements/Layouts/validate-messages.ctp:87
msgid "Please select Outlet"
msgstr "Por favor seleccione Tienda de Liquidación"

#: View/Elements/Layouts/validate-messages.ctp:88
msgid "Please enter Other Inventory Type names"
msgstr "Por favor ingrese Otro Inventario y nombres de Tipo"

#: View/Elements/Layouts/validate-messages.ctp:89
msgid "Please enter your sales tax rate"
msgstr "Ingrese su tasa de impuesto sobre las ventas"

#: View/Elements/Layouts/validate-messages.ctp:90
msgid "Please enter a password."
msgstr "Por favor ingrese una contraseña."

#: View/Elements/Layouts/validate-messages.ctp:93
msgid "Nothing Selected"
msgstr ""

#: View/Elements/Layouts/admin/sidebar.ctp:76 View/UserTaxes/admin_index.ctp:8
msgid "Country Settings"
msgstr ""

#: View/Elements/OrderComments/form.ctp:27
msgid "Leave a comment"
msgstr "Deje un comentario"

#: View/Elements/OrderComments/form.ctp:30
msgid "Post"
msgstr "Postear"

#: View/Elements/OrderComments/form.ctp:38
msgid "Internal comments are only visible to brands and their staff/sales reps."
msgstr "Los comentarios internos solo son visibles para las marcas y su personal/representantes de ventas."

#: View/Elements/OrderComments/form.ctp:45
msgid "Internal Only"
msgstr "Interno Solamente"

#: View/Elements/OrderComments/view.ctp:42
msgid "Are you sure you wish to delete this comment? This action cannot be undone."
msgstr "¿Está seguro que desea eliminar este comentario? Esta acción no puede revestirse."

#: View/Elements/Orders/ajax_customer_index.ctp:30
msgid "Fullfill Type"
msgstr "Tipo de Cumplimiento"

#: View/Elements/Orders/ajax_customer_index.ctp:60
#: View/Elements/Orders/ajax_index.ctp:97
msgid "No Order"
msgstr ""

#: View/Elements/Orders/ajax_index.ctp:30
msgid "Shipment status"
msgstr "Estado del Envío"

#: View/Elements/Orders/ajax_index.ctp:35
msgid "Fulfillment Type"
msgstr "Tipo de Cumplimiento"

#: View/Elements/Orders/ajax_retailerspurchaseorders.ctp:83
msgid "All time"
msgstr "Todo el tiempo"

#: View/Elements/Orders/ajax_retailerspurchaseorders.ctp:86
msgid "Amount Spent"
msgstr "Cantidad gastada"

#: View/Elements/Orders/ajax_retailerspurchaseorders.ctp:90
#: View/Orders/index.ctp:22
msgid "Orders"
msgstr "Pedidos"

#: View/Elements/Orders/dealerorder_invoice_totals.ctp:102
msgid "Wholesale Transaction"
msgstr "Transacción Mayorista"

#: View/Elements/Orders/dealerorder_invoice_totals.ctp:165
#: View/Elements/Orders/order_invoice_totals.ctp:98
msgid "Dealer Cost"
msgstr "Costo del Distribuidor"

#: View/Elements/Orders/dealerorder_invoice_totals.ctp:176
msgid "Dealer Refunds"
msgstr "Reembolsos del Distribuidor"

#: View/Elements/Orders/dealerorder_invoice_totals.ctp:184
#: View/Elements/Orders/order_invoice_totals.ctp:208
msgid "Net"
msgstr "Neto"

#: View/Elements/Orders/dealerorder_invoice_totals.ctp:194
msgid "Order Payout Summary"
msgstr "Resumen de Pagos"

#: View/Elements/Orders/dealerorder_invoice_totals.ctp:197
msgid "Retail Value"
msgstr "Valor de Venta"

#: View/Elements/Orders/dealerorder_invoice_totals.ctp:206
#: View/Elements/Orders/order_invoice_totals.ctp:248
msgid "Retail Refunds"
msgstr "Reembolsos de Venta"

#: View/Elements/Orders/dealerorder_invoice_totals.ctp:215
#: View/Elements/Orders/order_invoice_totals.ctp:257
msgid "Order Fees"
msgstr "Tarifas del Pedido"

#: View/Elements/Orders/dealerorder_invoice_totals.ctp:225
#: View/Elements/Orders/order_invoice_totals.ctp:50
msgid "Net Dealer Cost"
msgstr "Costo Neto del Distribuidor"

#: View/Elements/Orders/dealerorder_invoice_totals.ctp:244
#: View/Elements/Orders/order_invoice_totals.ctp:289
msgid "Gross Payout"
msgstr "Pago Bruto"

#: View/Elements/Orders/dealerorder_invoice_totals.ctp:252
#: View/Elements/Orders/order_invoice_totals.ctp:297
msgid "Sales Tax Payable"
msgstr "Impuesto sobre las ventas a pagar"

#: View/Elements/Orders/dealerorder_invoice_totals.ctp:253
#: View/Elements/Orders/order_invoice_totals.ctp:298
msgid "Amount collected by your business to be paid to the tax authorities"
msgstr "Importe recaudado por su empresa a pagar a la autoridad fiscal"

#: View/Elements/Orders/dealerorder_invoice_totals.ctp:262
#: View/Elements/Orders/order_invoice_totals.ctp:307
msgid "Input Tax Credit"
msgstr "Crédito fiscal soportado"

#: View/Elements/Orders/dealerorder_invoice_totals.ctp:271
#: View/Elements/Orders/order_invoice_totals.ctp:316
msgid "Profit"
msgstr "Beneficio"

#: View/Elements/Orders/dealerorder_invoice_totals.ctp:297
msgid "Shipping Name"
msgstr "Nombre de Envío"

#: View/Elements/Orders/dealerorder_invoice_totals.ctp:313
msgid "Custom Shipping Name"
msgstr "Nombre Dre Envío Personalizado"

#: View/Elements/Orders/dealerorder_invoice_totals.ctp:326;342
#: View/Elements/Orders/order_invoice_totals.ctp:218
#: View/InvoicePdf/generate_invoice.ctp:83
msgid "Notes"
msgstr "Notas"

#: View/Elements/Orders/order_details_panel.ctp:28
msgid "Ecommerce ID"
msgstr "ID del Comercio Electrónico"

#: View/Elements/Orders/order_details_panel.ctp:32
msgid "Store"
msgstr "Tienda"

#: View/Elements/Orders/order_details_panel.ctp:35
msgid "Created By"
msgstr "Creado por"

#: View/Elements/Orders/order_details_panel.ctp:60;84;145
msgid "Saving"
msgstr "Guardando"

#: View/Elements/Orders/order_details_panel.ctp:61;85;146
msgid "Saved"
msgstr "Guardado"

#: View/Elements/Orders/order_details_panel.ctp:97
msgid "Mark as Delivered"
msgstr ""

#: View/Elements/Orders/order_details_panel.ctp:100
msgid "Are you sure you wish to apply the verification code and mark the order as delivered?"
msgstr ""

#: View/Elements/Orders/order_details_panel.ctp:114
msgid "Risk Level"
msgstr "Nivel de Riesgo"

#: View/Elements/Orders/order_details_panel.ctp:120
msgid "CVC"
msgstr ""

#: View/Elements/Orders/order_details_panel.ctp:133;152
msgid "Credit Term"
msgstr "Plazo de Crédito"

#: View/Elements/Orders/order_invoice_totals.ctp:181
#: View/UserTaxes/edit.ctp:119 View/UserTaxes/registrations.ctp:62
msgid "Sales Tax"
msgstr "Impuestos de Venta"

#: View/Elements/Orders/order_invoice_totals.ctp:191
msgid "Grand Total"
msgstr "Gran Total"

#: View/Elements/Orders/order_invoice_totals.ctp:228
msgid "Capture Payment"
msgstr "Capturar pago"

#: View/Elements/Orders/order_invoice_totals.ctp:234
msgid "Are you sure you wish to capture the order?"
msgstr "¿Estás seguro de que deseas capturar el pedido?"

#: View/Elements/Orders/order_invoice_totals.ctp:242
msgid "Financial Summary"
msgstr ""

#: View/Elements/Orders/order_popup_address_table.ctp:22
msgid "Customer Shipping"
msgstr "Envío al Cliente"

#: View/Elements/Orders/order_popup_address_table.ctp:31
msgid "Customer Billing"
msgstr "Facturación del Cliente"

#: View/Elements/Orders/order_popup_address_table.ctp:33
msgid "Fulfilled By"
msgstr "Cumplido Por"

#: View/Elements/Orders/order_popup_address_table.ctp:33
msgid "Serviced By"
msgstr "Servicio por"

#: View/Elements/Orders/order_popup_header.ctp:40
msgid "Print Order"
msgstr "Imprimir Pedido"

#: View/Elements/Orders/order_popup_header.ctp:85;94;102
#: View/Elements/Orders/order_products_table/processing.ctp:80
#: View/Elements/Orders/order_products_table/purchase_order.ctp:118
#: View/InvoicePdf/generate_invoice.ctp:103;159
msgid "Refund"
msgstr "Reembolso"

#: View/Elements/Orders/order_popup_header.ctp:130;176
msgid "Re-order"
msgstr "Reordenar"

#: View/Elements/Orders/order_popup_header.ctp:108
#: View/OrderRefunds/refund.ctp:15 View/Orders/edit.ctp:144
msgid "Cancel Order"
msgstr "Cancelar Pedido"

#: View/Elements/Orders/order_popup_header.ctp:109
msgid "Refund Customer"
msgstr "Reembolsar al Cliente"

#: View/Elements/Orders/order_popup_header.ctp:114
msgid "Refund Dealer"
msgstr "Reembolsar al Distribuidor"

#: View/Elements/Orders/order_popup_header.ctp:129
msgid "from"
msgstr "de"

#: View/Elements/Orders/order_popup_header.ctp:145
msgid "Message Customer"
msgstr "Mensaje al Cliente"

#: View/Elements/Orders/order_popup_header.ctp:227
#: View/OrderRefunds/refund.ctp:15 View/Orders/admin_view.ctp:246
msgid "Refund Payments"
msgstr ""

#: View/Elements/Orders/order_popup_header.ctp:238
#: View/OrderRefunds/refund.ctp:235 View/Orders/admin_view.ctp:253
msgid "Process"
msgstr "Procesado"

#: View/Elements/Orders/order_timeline.ctp:11
msgid "Timeline"
msgstr "Línea de Tiempo"

#: View/Elements/Orders/order_timeline.ctp:43
msgid "Verification Image"
msgstr "Imagen de Verificación"

#: View/Elements/Orders/order_products_table/dealer_order.ctp:13
#: View/Elements/Orders/order_products_table/need_to_confirm.ctp:16
#: View/Elements/Orders/order_products_table/processing.ctp:25
msgid "Order Qty"
msgstr "Cantidad en Pedido"

#: View/Elements/Orders/order_products_table/dealer_order.ctp:16
#: View/Elements/Orders/order_products_table/need_to_confirm.ctp:19
#: View/Elements/Orders/order_products_table/processing.ctp:28
#: View/Elements/Orders/order_products_table/purchase_order.ctp:39;84
msgid "Ext Price"
msgstr "Precio Externo"

#: View/Elements/Orders/order_products_table/need_to_confirm.ctp:9
msgid "Please confirm the amount of stock on hand"
msgstr "Por favor, confirme la cantidad de existencias disponibles."

#: View/Elements/Orders/order_products_table/need_to_confirm.ctp:15
msgid "Qty on Hand"
msgstr "Cantidad Disponible"

#: View/Elements/Orders/order_products_table/processing.ctp:24;76
#: View/Elements/Orders/order_products_table/purchase_order.ctp:32;77;111
msgid "WH Available"
msgstr "Disponible en Almacén"

#: View/Elements/Orders/order_products_table/processing.ctp:77
msgid "Refund Qty"
msgstr "Cantidad Reembolsada"

#: View/Elements/Orders/order_products_table/purchase_order.ctp:31
msgid "Estimated Ship Date"
msgstr "Fecha de envío estimada"

#: View/Elements/Orders/order_products_table/purchase_order.ctp:33;78;112
msgid "Qty to Fulfill"
msgstr "Cantidad a cumplir"

#: View/Elements/Orders/order_products_table/purchase_order.ctp:34;79;113
msgid "Qty"
msgstr "Cantidad"

#: View/Elements/Pages/affirm_financing_app_promo.ctp:27
msgid "Enter Affirm MasterCard details below to complete your purchase"
msgstr "Ingrese los detalles de Affirm MasterCard a continuación para completar su compra"

#: View/Elements/Products/catalogue_variants.ctp:21
#: View/Elements/Products/product_grid_item.ctp:46
#: View/Elements/Products/product_list_item.ctp:63
#: View/Products/view.ctp:391;518
msgid "MSRP"
msgstr "MSRP (Precio de Venta Sugerido por el Fabricante)"

#: View/Elements/Products/catalogue_variants.ctp:23 View/Products/view.ctp:520
msgid "Order QTY"
msgstr "Cantidad en Pedido"

#: View/Elements/Products/product_grid_item.ctp:70
#: View/Elements/Products/product_list_item.ctp:56 View/Products/view.ctp:415
msgid "Request"
msgstr "Solicitud"

#: View/Elements/Products/product_list_item.ctp:75
msgid "Order Now"
msgstr "Pedir Ahora"

#: View/Elements/Products/productlist_retailer.ctp:105
#: View/Elements/Products/productlist_retailer_grid.ctp:37
#: View/Retailers/price_tier.ctp:56
msgid "Featured Collections"
msgstr "Colecciones destacadas"

#: View/Elements/Products/productlist_retailer.ctp:150
msgid "Add All to Order"
msgstr "Agregar Todo al Pedido"

#: View/Elements/Products/productlist_retailer.ctp:193
msgid "on Order"
msgstr "en Pedido"

#: View/Elements/Products/productlist_retailer.ctp:195
msgid "Remove from Order"
msgstr "Quitar del Pedido"

#: View/Elements/Retailers/ajax_index.ctp:84
#: View/Elements/Retailers/ajax_index_salesrep.ctp:35
msgid "Select Pricing Tier To Connect"
msgstr ""

#: View/Elements/Users/<USER>
msgid "Stripe"
msgstr "Stripe"

#: View/Elements/Users/<USER>
msgid "Account Activated"
msgstr "Cuenta Activada"

#: View/Elements/Users/<USER>
msgid "Charges Enabled"
msgstr "Cargos habilitados"

#: View/Elements/Users/<USER>
msgid "Payouts Enabled"
msgstr "Pagos Habiliados"

#: View/Elements/Users/<USER>/retailer_inventory_form.ctp:17;23
msgid "Select your currency type"
msgstr "Seleccione tu tipo de moneda"

#: View/Elements/Users/<USER>/retailer_inventory_form.ctp:28
msgid "* Please choose the same currency type that you are using in your inventory software"
msgstr "* Elija el mismo tipo de moneda que está utilizando en su software de inventario"

#: View/Elements/Users/<USER>/retailer_inventory_form.ctp:37
#: View/Users/<USER>
msgid "Select your inventory type"
msgstr "Seleccione su tipo de inventario"

#: View/Elements/Users/<USER>/retailer_inventory_form.ctp:48
msgid "Other"
msgstr "Otro"

#: View/Elements/Users/<USER>/retailer_inventory_form.ctp:49
#: View/Retailers/edit.ctp:124;166
msgid "None"
msgstr "Ninguno"

#: View/Elements/Users/<USER>/retailer_inventory_form.ctp:56
msgid "Select your inventory software"
msgstr "Seleccione su software de inventario"

#: View/Elements/Users/<USER>/retailer_inventory_form.ctp:70
msgid "Download QWC File"
msgstr "Descargar archivo QWC"

#: View/Elements/Users/<USER>/retailer_inventory_form.ctp:84
msgid "Shopify Domain"
msgstr "Dominio de Shopify"

#: View/Elements/Users/<USER>/retailer_inventory_form.ctp:100
msgid "Webhook Shared Secret"
msgstr "Secreto Compartido de Webhook"

#: View/Elements/Users/<USER>/retailer_inventory_form.ctp:113
msgid "Lightspeed Connect"
msgstr "Conexión de Lightspeed"

#: View/Elements/Users/<USER>/retailer_inventory_form.ctp:123
msgid "Square Connect"
msgstr "Conexión de Square"

#: View/Elements/Users/<USER>/retailer_inventory_form.ctp:133
msgid "Lightspeed Retail (X-series) Connect"
msgstr ""

#: View/Elements/Users/<USER>/retailer_inventory_form.ctp:148
msgid "API Key"
msgstr "Llave API"

#: View/Elements/Users/<USER>/retailer_inventory_form.ctp:153
msgid "* Please use different api keys for different stores to avoid api throttle limit"
msgstr "* Utilice diferentes claves de api para diferentes tiendas para evitar el límite de aceleración de api"

#: View/Elements/Users/<USER>/retailer_inventory_form.ctp:155;158
msgid "Click here for help setting up SFTP access."
msgstr "Haga click aquí para obtener ayuda para configurar el acceso SMTP."

#: View/Elements/Users/<USER>/retailer_inventory_form.ctp:172
#: View/Elements/Users/<USER>/retailer_inventory_script.ctp:65;66
msgid "Lightspeed Retail Account ID"
msgstr "ID de Cuenta de Lighspeed"

#: View/Elements/Users/<USER>/retailer_inventory_form.ctp:178
msgid "Click here for help setting up Quickbooks POS."
msgstr "Haga clic aquí para obtener ayuda para configurar Quickbooks POS."

#: View/Elements/Users/<USER>/retailer_inventory_form.ctp:189
msgid "Select Store"
msgstr "Seleccione Tienda"

#: View/Elements/Users/<USER>/retailer_inventory_form.ctp:195;254
#: View/Elements/Users/<USER>/retailer_inventory_script.ctp:44
msgid "Inventory Store ID"
msgstr "ID de Tienda de Inventario"

#: View/Elements/Users/<USER>/retailer_inventory_form.ctp:208
msgid "Select Employee"
msgstr "Seleccione Empleado"

#: View/Elements/Users/<USER>/retailer_inventory_form.ctp:214
#: View/Elements/Users/<USER>/retailer_inventory_script.ctp:43
msgid "Inventory Employee ID"
msgstr "ID de Invenatario del Empleado"

#: View/Elements/Users/<USER>/retailer_inventory_form.ctp:227
msgid "Select Register"
msgstr "Seleccione Registrarse"

#: View/Elements/Users/<USER>/retailer_inventory_form.ctp:233
msgid "Inventory Register ID"
msgstr "ID de Registro de Inventario"

#: View/Elements/Users/<USER>/retailer_inventory_form.ctp:248
msgid "Select Location"
msgstr "Seleccione Ubicación"

#: View/Elements/Users/<USER>/retailer_inventory_form.ctp:273
#: View/Users/<USER>
msgid "Other Inventory Name"
msgstr "Otro Nombre de Inventario"

#: View/Elements/Users/<USER>/retailer_inventory_form.ctp:285
msgid "Manual Inventory Upload"
msgstr "Carga Manual de Inventario"

#: View/Elements/Users/<USER>/retailer_inventory_form.ctp:289
#: View/Inventory/index.ctp:79
msgid "Import"
msgstr "Importar"

#: View/Elements/Users/<USER>/retailer_inventory_form.ctp:292
msgid "Download Template"
msgstr "Descargar Plantilla"

#: View/Elements/Users/<USER>/retailer_inventory_form.ctp:294
msgid "Excel"
msgstr "Excel"

#: View/Elements/Users/<USER>/retailer_inventory_form.ctp:298
msgid "CSV"
msgstr "CSV"

#: View/Elements/Users/<USER>/retailer_inventory_form.ctp:302
msgid "(Inventory is reset daily)"
msgstr "(El inventario se restablece diariamente)"

#: View/Elements/Users/<USER>/retailer_inventory_form.ctp:310
msgid "Create sale in POS system"
msgstr "Crear venta en sistema POS"

#: View/Elements/Users/<USER>/retailer_inventory_form.ctp:327
#: View/Users/<USER>
msgid "Sales Tax Rate"
msgstr "Tasa de impuesto sobre las ventas"

#: View/Elements/Users/<USER>/retailer_inventory_form.ctp:343
msgid "Tax Identification Number"
msgstr "Número de identificación de impuestos"

#: View/Elements/Users/<USER>/retailer_inventory_form.ctp:361
msgid "Installation Rate (hourly)"
msgstr "Tarifa de instalación (por hora)"

#: View/Elements/Users/<USER>/retailer_inventory_import_popup_script.ctp:15
msgid "Upload Inventory for"
msgstr "Subir inventario para"

#: View/Elements/Users/<USER>/retailer_inventory_import_popup_script.ctp:15
#: View/Inventory/index.ctp:282
msgid "Upload Inventory"
msgstr "Subir inventario"

#: View/Elements/Users/<USER>/retailer_inventory_import_popup_script.ctp:18
msgid "Manually uploaded inventory will be reset daily."
msgstr "El inventario cargado manualmente se restablecerá diariamente."

#: View/Elements/Users/<USER>/retailer_inventory_import_popup_script.ctp:18
#: View/Inventory/index.ctp:297
msgid "The expected file format is an Excel or CSV file with the same layout as the export file."
msgstr "El formato de archivo esperado es un archivo Excel o CSV con el mismo diseño que el archivo de exportación."

#: View/Elements/Users/<USER>/retailer_inventory_script.ctp:46
msgid "Inventory User ID"
msgstr "ID de usuario de inventario"

#: View/Elements/Users/<USER>/retailer_inventory_script.ctp:47
msgid "Inventory Outlet ID"
msgstr "ID de liquidación de inventario"

#: View/Elements/Users/<USER>/retailer_inventory_script.ctp:77;78
msgid "LightSpeed Retail (X-Series) Account ID"
msgstr "ID de cuenta LightSpeed ​​Retail (Serie X)"

#: View/Elements/Users/<USER>/retailer_inventory_script.ctp:90
msgid "Admin API access token"
msgstr "Token de acceso a la API de administración"

#: View/Elements/Users/<USER>/retailer_inventory_script.ctp:96
msgid "Create QuickBooks Web Connector Password"
msgstr "Crear Contraseña para QuickBooks Web Connector"

#: View/Elements/Users/<USER>/retailer_inventory_script.ctp:97
msgid "Create Your QuickBooks Web Connector Password"
msgstr "Cree Su Contraseña para QuickBooks Web Connector"

#: View/Elements/Users/<USER>/retailer_inventory_script.ctp:111;120
msgid "SFTP Password"
msgstr "Contraseña SFTP"

#: View/Elements/Users/<USER>/retailer_inventory_script.ctp:112;116;118
msgid "SFTP Username"
msgstr "Usuario SFTP"

#: View/Elements/Users/<USER>/profile_brand_retailer.ctp:16
msgid "Edit Customer Support"
msgstr "Editar Atención al Cliente"

#: View/Elements/Users/<USER>/profile_brand_retailer.ctp:24
msgid "About Company"
msgstr "Sobre la Compañía"

#: View/Elements/Users/<USER>/profile_brand_retailer.ctp:27
msgid "Contact Information"
msgstr "Información de Contacto"

#: View/Elements/Users/<USER>/profile_brand_retailer.ctp:32
msgid "Whitelabel Settings"
msgstr "Configuración de Whitelabel"

#: View/Elements/Users/<USER>/profile_brand_retailer.ctp:60
#: View/Users/<USER>
msgid "Company Description"
msgstr "Descripción de la Compañía"

#: View/Elements/Users/<USER>/profile_brand_retailer.ctp:61
msgid "Company description"
msgstr "Descripción de la Compañía"

#: View/Elements/Users/<USER>/profile_brand_retailer.ctp:66
msgid "Company Logo"
msgstr "Logo de la Compañia"

#: View/Elements/Users/<USER>/profile_brand_retailer.ctp:81
msgid "Upload a File"
msgstr "Subir Archivo"

#: View/Elements/Users/<USER>/profile_brand_retailer.ctp:100
#: View/Elements/Users/<USER>/profile_store_associate.ctp:37
#: View/Users/<USER>
msgid "Contact email address"
msgstr "Dirección de correo electrónico de contacto"

#: View/Elements/Users/<USER>/profile_brand_retailer.ctp:134
#: View/Elements/Users/<USER>/profile_store_associate.ctp:79
#: View/Users/<USER>
#: View/Products/ajax_browse_products.ctp:14
#: View/Elements/Retailers/display.ctp:29
msgid "Select"
msgstr "Seleccione"

#: View/Elements/Users/<USER>/profile_brand_retailer.ctp:145
#: View/Elements/Users/<USER>/profile_store_associate.ctp:90
#: View/Users/<USER>
msgid "Select your state"
msgstr "Seleccione su estado"

#: View/Elements/Users/<USER>/profile_brand_retailer.ctp:163
#: View/Elements/Users/<USER>/profile_store_associate.ctp:115
#: View/Pages/admin_useredit.ctp:83 View/Users/<USER>
msgid "Contact Phone Number"
msgstr "Número de Teléfono de Contacto"

#: View/Elements/Users/<USER>/profile_brand_retailer.ctp:179
#: View/Users/<USER>
msgid "Preferred Time Zone"
msgstr "Zona Horaria Preferida"

#: View/Elements/Users/<USER>/profile_brand_retailer.ctp:190
#: View/Elements/Users/<USER>/profile_sales_rep.ctp:97
#: View/Elements/Users/<USER>/profile_store_associate.ctp:102
#: View/Users/<USER>
msgid "Select a Language"
msgstr ""

#: View/Elements/Users/<USER>/profile_brand_retailer.ctp:204
msgid "Facebook page"
msgstr "Página de Facebook"

#: View/Elements/Users/<USER>/profile_brand_retailer.ctp:205
msgid "Contact Facebook page URL"
msgstr "URL de página de contacto de Facebook"

#: View/Elements/Users/<USER>/profile_brand_retailer.ctp:210;219
msgid "Website"
msgstr "Sitio web"

#: View/Elements/Users/<USER>/brand_dealer_options.ctp:44
msgid "Offer free shipping on Ship To Store orders over"
msgstr ""

#: View/Elements/Users/<USER>/brand_dealer_options.ctp:66
msgid "In-Stock Retailers Only"
msgstr ""

#: View/Elements/Users/<USER>/brand_dealer_options.ctp:74
msgid "Double Shipment"
msgstr ""

#: View/Elements/Users/<USER>/brand_dealer_options.ctp:95
msgid "Charge Immediately"
msgstr ""

#: View/Elements/Users/<USER>/brand_dealer_options.ctp:96
msgid "Authorize"
msgstr ""

#: View/Elements/Users/<USER>/brand_dealer_options.ctp:115
msgid "Charge B2B taxes on B2B shipping rates"
msgstr ""

#: View/Elements/Users/<USER>/brand_dealer_options.ctp:124
msgid "Charge taxes on shipping rates"
msgstr ""

#: View/Elements/Users/<USER>/brand_dealer_options.ctp:132
msgid "Install Rate"
msgstr ""

#: View/Elements/Users/<USER>/brand_dealer_options.ctp:182
msgid "Local delivery shipping column header"
msgstr ""

#: View/Elements/Users/<USER>/brand_dealer_options.ctp:186
msgid "Local Delivery Rate"
msgstr ""

#: View/Elements/Users/<USER>/brand_dealer_options.ctp:256
msgid "Wholesale Top-Up"
msgstr ""

#: View/Elements/Users/<USER>/brand_dealer_options.ctp:263
msgid "Wholesale Top-Up Infographic (Width 576px)"
msgstr ""

#: View/Elements/Users/<USER>/brand_dealer_options.ctp:287;320
msgid "Preview"
msgstr ""

#: View/Elements/Users/<USER>/brand_dealer_options.ctp:244
msgctxt "local_delivery"
msgid "No Retailer Listing"
msgstr ""

#: View/Elements/Users/<USER>/brand_dealer_options.ctp:269;302
#: View/Orders/verification_code.ctp:52
msgctxt "files"
msgid "Browse"
msgstr "Buscar Archivo"

#: View/Elements/Users/<USER>/retailer_dealer_options.ctp:23
msgid "Do Not Display"
msgstr ""

#: View/Elements/Users/<USER>/retailer_dealer_options.ctp:44
msgid "Charges Taxes on Shipping"
msgstr "Cargos de impuestos sobre el envío"

#: View/Errors/error400.ctp:28
msgid "The requested address %s was not found on this server."
msgstr "La dirección solicitada %s no se encontró en este servidor."

#: View/Errors/error500.ctp:20
msgid "An Internal Error Has Occurred."
msgstr "Ha ocurrido un error interno."

#: View/Fulfillments/add.ctp:33
msgid "Not Stocked"
msgstr ""

#: View/Fulfillments/add.ctp:74
msgid "Fulfill"
msgstr "Realizar"

#: View/Helper/B2bCartsHelper.php:69
msgid "Empty"
msgstr "Vacío"

#: View/Helper/B2bCatalogueHelper.php:28 View/RetailerCredits/index.ctp:211
msgid "Order"
msgstr "Pedido"

#: View/Helper/B2bCatalogueHelper.php:39
msgid "Select Cart..."
msgstr "Seleccione Carrito…"

#: View/Helper/B2bCatalogueHelper.php:40
msgid "New Cart"
msgstr "Nuevo Carrito"

#: View/Helper/B2bCatalogueHelper.php:85;88
msgid "Create Purchase Order"
msgstr "Crear Orden de Compra"

#: View/Helper/B2bCatalogueHelper.php:105
msgid "%s out of %s Variants In Stock"
msgstr "%s de %s variantes en stock"

#: View/Helper/B2bCatalogueHelper.php:106
msgid "%s Variants"
msgstr "%s de Variantes"

#: View/Helper/B2bCatalogueHelper.php:25
msgctxt "Order type"
msgid "%1$s %2$s (%d)"
msgstr ""

#: View/Helper/BrandStaffHelper.php:87 View/Staff/index.ctp:45
msgid "No Staff"
msgstr "Sin personal"

#: View/Helper/EcommerceHelper.php:119
msgid "Credit Card Accepted"
msgstr "Tarjeta de crédito aceptada"

#: View/Helper/EcommerceHelper.php:120
msgid "Affirm Accepted"
msgstr "Affirm aceptado"

#: View/Helper/EcommerceHelper.php:121
msgid "Klarna Accepted"
msgstr "Klarna aceptado"

#: View/Helper/InventoryHelper.php:34;178
msgid "Not Stocked in Warehouse"
msgstr "No Almacenado en Almacén"

#: View/Helper/InventoryHelper.php:225
msgid "Not Tracked"
msgstr "Sin Seguimiento"

#: View/Helper/InventoryHelper.php:229
msgid "Call to Confirm"
msgstr "Llamar para Confirmar"

#: View/Helper/InventoryHelper.php:234
msgid "In Stock"
msgstr "En Invenatario"

#: View/Helper/LayoutHelper.php:83;100
msgid "List All Order Types"
msgstr ""

#: View/Helper/LayoutHelper.php:113;163 View/Inventory/index.ctp:91
#: View/Products/view.ctp:488 View/Retailers/edit.ctp:133;151;158;207;228
msgid "All"
msgstr "Todo"

#: View/Helper/LayoutHelper.php:151
msgid "Select Status"
msgstr ""

#: View/Helper/LayoutHelper.php:212
msgid "ALL"
msgstr "TODO"

#: View/Helper/OrdersHelper.php:121
msgid "Fulfillment"
msgstr "Cumplimiento"

#: View/Helper/RetailerCreditHelper.php:100
msgid "Select or add a bank account."
msgstr "Selecciona o agregue una cuenta bancaria."

#: View/Helper/RetailerCreditHelper.php:174
msgid "Add Bank Account"
msgstr "Agregar Cuenta Bancaria"

#: View/Helper/ShipearlyHelper.php:187
msgid "Offline"
msgstr "Fuera de Línea"

#: View/Helper/ShipearlyHelper.php:189;196
msgid "Online"
msgstr "En Línea"

#: View/Helper/SidebarHelper.php:97
msgid "Catalog"
msgstr "Catálogo"

#: View/Helper/SidebarHelper.php:133 View/Products/collections.ctp:26
#: View/Products/index_retailer.ctp:139;165 View/Products/view.ctp:600
#: View/ShippingZones/ajax_tax_override_table.ctp:14
msgid "Collections"
msgstr "Colecciones"

#: View/Helper/SidebarHelper.php:224
msgid "Consumer Orders"
msgstr "Orden del Cliente"

#: View/Helper/SidebarHelper.php:239 View/Orders/dealerorders_index.ctp:26
msgid "Purchase Orders"
msgstr "Orden de Compra"

#: View/Helper/SidebarHelper.php:267
msgid "Customers"
msgstr "Clientes"

#: View/Helper/SidebarHelper.php:307;316
msgid "Help"
msgstr "Ayuda"

#: View/Helper/SidebarHelper.php:343;349
msgid "Support"
msgstr "Apoyo"

#: View/Helper/SidebarHelper.php:355
msgid "Brand Assets"
msgstr "Activos de Marca"

#: View/Helper/StripeHelper.php:12
msgid "To participate in ShipEarly, you must enable an account with our payment gateway partner so we can deposit funds from sales directly into your bank account. The first deposit may take up to seven business days and subsequent transactions take just two business days to arrive in your bank account. Our payment gateway partner you are to connect with is fully PCI Compliant and collects the minimum required information to identify you or your corporation for each sale for tax reporting purposes. This is known as “KYC” or Know Your Customer legislation, which is required for all eCommerce transactions. Your information is never stored by our company and is encrypted."
msgstr "Para participar en ShipEarly, debe habilitar una cuenta con nuestro socio de pasarela de pago para que podamos depositar fondos de las ventas directamente en su cuenta bancaria. El primer depósito puede demorar hasta siete días hábiles y las transacciones posteriores demoran solo dos días hábiles en llegar a su cuenta bancaria. Nuestro socio de pasarela de pago con el que debe conectarse es totalmente compatible con PCI y recopila la información mínima requerida para identificarlo a usted o a su corporación para cada venta con fines de declaración de impuestos. Esto se conoce como “KYC” o legislación Know Your Customer, que se requiere para todas las transacciones de comercio electrónico. Nuestra empresa nunca almacena su información y está encriptada."

#: View/Inventory/index.ctp:70 View/Orders/dealerorders_index.ctp:146
#: View/Orders/index.ctp:140 View/Products/index.ctp:97
#: View/Products/index_retailer.ctp:85 View/Retailers/index.ctp:132
msgid "Search and Filter"
msgstr "Buscar y filtrar"

#: View/Inventory/index.ctp:83 View/Orders/customer_index.ctp:195
#: View/Orders/dealerorders_index.ctp:156 View/Orders/index.ctp:150
#: View/Reports/view.ctp:129
msgid "Export"
msgstr "Exportar"

#: View/Inventory/index.ctp:103 View/Retailers/index.ctp:150
msgid "Add/Edit"
msgstr "Agregar/Editar"

#: View/Inventory/index.ctp:125 View/Orders/dealerorders_index.ctp:176
#: View/Orders/index.ctp:170 View/Products/index.ctp:163
#: View/Products/index_retailer.ctp:135 View/Retailers/index.ctp:171
msgid "Add Filter +"
msgstr ""

#: View/Inventory/index.ctp:129 View/Products/index.ctp:166
msgid "Product Status"
msgstr ""

#: View/InvoicePdf/generate_invoice.ctp:71
msgid "Bill To"
msgstr "Factura a"

#: View/InvoicePdf/generate_invoice.ctp:107
msgid "Barcode"
msgstr ""

#: View/InvoicePdf/generate_invoice.ctp:120
msgid "Total Price"
msgstr "Precio Total"

#: View/OrderRefunds/refund.ctp:150
msgid "Customer Refund"
msgstr "Reembolso del Cliente"

#: View/Orders/customer_index.ctp:70
msgid "List All Stores"
msgstr "Listar Todas las Tiendas"

#: View/Orders/dealerorders_index.ctp:80 View/Orders/index.ctp:77
msgid "List All Brands"
msgstr "Listar Todas las Marcas"

#: View/Orders/dealerorders_index.ctp:94 View/Orders/index.ctp:91
msgid "List All Tags"
msgstr ""

#: View/Orders/dealerorders_index.ctp:108 View/Orders/index.ctp:105
#: View/RetailerCredits/index.ctp:108;110
msgid "All Locations"
msgstr "Todas las Sucursales"

#: View/Orders/dealerorders_index.ctp:159 View/Orders/index.ctp:153
msgid "Orders Export"
msgstr "Exportar Pedidos"

#: View/Orders/dealerorders_index.ctp:160 View/Orders/index.ctp:154
msgid "Product Export"
msgstr "Exportar Productos"

#: View/Orders/dealerorders_index.ctp:179 View/Orders/index.ctp:173
#: View/Reports/view.ctp:62
msgid "Date Range"
msgstr "Rango de fechas"

#: View/Orders/dealerorders_index.ctp:185
msgid "Tag"
msgstr ""

#: View/Orders/dealerorders_index.ctp:250 View/Orders/index.ctp:225
msgid "Today"
msgstr "Hoy"

#: View/Orders/dealerorders_index.ctp:251 View/Orders/index.ctp:226
msgid "Yesterday"
msgstr "Ayer"

#: View/Orders/dealerorders_index.ctp:252 View/Orders/index.ctp:227
msgid "Last 7 Days"
msgstr "Últimos 7 Días"

#: View/Orders/dealerorders_index.ctp:253 View/Orders/index.ctp:228
msgid "Last 30 Days"
msgstr "Últimos 30 Días"

#: View/Orders/dealerorders_index.ctp:254 View/Orders/index.ctp:229
msgid "This Month"
msgstr "Este Mes"

#: View/Orders/dealerorders_index.ctp:255 View/Orders/index.ctp:230
msgid "Last Month"
msgstr "Último Mes"

#: View/Orders/dealerorders_index.ctp:259 View/Orders/index.ctp:234
msgid "Custom Range"
msgstr "Rango Personalizado"

#: View/Orders/edit.ctp:134
msgid "Confirm"
msgstr "Confirmar"

#: View/Orders/edit.ctp:148
msgid "Fulfill Items"
msgstr "Cumplir artículos"

#: View/Orders/invoice.ctp:111
msgid "Fulfill Item"
msgstr "Cumplir artículo"

#: View/Orders/invoice.ctp:136
msgid "Enter Verification Code"
msgstr "Ingrese Código de Verifiación"

#: View/Orders/invoice.ctp:144
msgid "Order Ready for Pickup / Resend Verification Code"
msgstr "Pedido listo para recoger/Reenviar código de verificación"

#: View/Orders/invoice.ctp:145
msgid "Schedule Delivery / Resend Verification Code"
msgstr "Programar entrega / Reenviar código de verificación"

#: View/Orders/invoice.ctp:182
msgid "Please confirm to send the customer an email their order is ready for pickup, schedule a pickup time and resend the verification code"
msgstr "Confirme para enviar al cliente un correo electrónico de que su pedido está listo para ser retirado, programe una hora de retiro y vuelva a enviar el código de verificación"

#: View/Orders/invoice.ctp:185
msgid "Calender to Send"
msgstr "Calendario Para Enviar"

#: View/Orders/invoice.ctp:190
msgid "Send without Calendar"
msgstr "Enviar sin Calendario"

#: View/Orders/invoice.ctp:199
msgid "Click to Integrate Calendar"
msgstr "Haga clic para integrar el calendario"

#: View/Orders/message_customer.ctp:8
msgid "Send Email to %s"
msgstr "Enviar Correo Electrónico a %s"

#: View/Orders/message_customer.ctp:32
msgid "Subject"
msgstr "Asunto"

#: View/Orders/message_customer.ctp:38
msgid "Message"
msgstr "Mensaje"

#: View/Orders/verification_code.ctp:12
msgid "Enter the verification code obtained from the customer. Consumer forgot verification code?"
msgstr "Introduzca el código de verificación obtenido del cliente. ¿El cliente olvidó el código de verificación?"

#: View/Orders/verification_code.ctp:14
msgid "Click to Resend"
msgstr "Haga Click para Reenviar"

#: View/Orders/verification_code.ctp:23
msgid "Customer Details"
msgstr "Detalles del Cliente"

#: View/Orders/verification_code.ctp:43
msgid "Proof of Pickup (Recommended)"
msgstr "Comprobante de retiro (recomendado)"

#: View/Orders/verification_code.ctp:44
msgid "Have the consumer sign indicating they are satisfied with their purchase and collected their items. You canupload a signed packing slip/form or with permission an image of their Government issued ID. It is not mandatorybut recommended to prevent chargebacks to have proof of customer pickup."
msgstr "Haga que el cliente firme indicando que está satisfecho con su compra y retiró sus artículos. Puede cargar un formulario firmado o, con permiso, una imagen de su identificación emitida por el gobierno. No es obligatorio, pero se recomienda tener un comprobante del retiro del cliente para evitar devoluciones de cargo."

#: View/Orders/verification_code.ctp:68
msgid "Verification Image Preview"
msgstr "Vista Previa de la Imagen de Verificación"

#: View/Orders/verification_code.ctp:102
msgid "Please enter a verification code"
msgstr "Por favor ingrese el código de verificación"

#: View/Pages/admin_reset_password.ctp:56 View/Users/<USER>
msgid "2FA Setup"
msgstr ""

#: View/Products/addcollection.ctp:7 View/Products/view.ctp:601
msgid "Add Collection"
msgstr "Agregar colección"

#: View/Products/collections.ctp:34
msgid "Create Collection"
msgstr "Crear colección"

#: View/Products/collections.ctp:48
msgid "Collection Name"
msgstr "Nombre de la colección"

#: View/Products/collections.ctp:50
msgid "Collection Condition"
msgstr "Condición de colección"

#: View/Products/collections.ctp:78
msgid "Remove Collection"
msgstr ""

#: View/Products/collections.ctp:87
msgid "No collections found."
msgstr ""

#: View/Products/index_retailer.ctp:47
msgid "Featured"
msgstr "Promocionado"

#: View/Products/index_retailer.ctp:48
msgid "Favorites"
msgstr "Favoritos"

#: View/Products/index_retailer.ctp:49
msgid "Recently Ordered"
msgstr "Pedido Recientemente"

#: View/Products/index_retailer.ctp:50
msgid "Date, new to old"
msgstr "Fecha, nueva a vieja"

#: View/Products/index_retailer.ctp:51
msgid "Date, old to new"
msgstr "Fecha, vieja a nueva"

#: View/Products/index_retailer.ctp:52
msgid "Alphabetically, A-Z"
msgstr "Alfabético, A-Z"

#: View/Products/index_retailer.ctp:53
msgid "Alphabetically, Z-A"
msgstr "Alfabético, Z-A"

#: View/Products/index_retailer.ctp:54
msgid "Price, low to high"
msgstr "Precio, mas bajo a mas alto"

#: View/Products/index_retailer.ctp:55
msgid "Price, high to low"
msgstr "Precio, mas alto a mas bajo"

#: View/Products/index_retailer.ctp:73
msgid "Order Type:"
msgstr ""

#: View/Products/index_retailer.ctp:79
msgid "Warehouse:"
msgstr ""

#: View/Products/index_retailer.ctp:97
msgid "Display"
msgstr "Mostrar"

#: View/Products/index_retailer.ctp:106
msgid "Image"
msgstr "Imágen"

#: View/Products/index_retailer.ctp:115
msgid "List"
msgstr "Lista"

#: View/Products/index_retailer.ctp:138;153
msgid "Sort by"
msgstr "Ordenar por"

#: View/Products/index_retailer.ctp:140;177
msgid "Categories"
msgstr ""

#: View/Products/index_retailer.ctp:141;192 View/Products/view.ctp:476
#: View/Elements/Retailers/display.ctp:31
msgid "Variants"
msgstr "Variantes"

#: View/Products/index_retailer.ctp:148
msgid "Product Availability"
msgstr "Disponibilidad del producto"

#: View/Products/index_retailer.ctp:184;185
msgid "All Variants"
msgstr "Todas las Variantes"

#: View/Products/index_retailer.ctp:341 View/Products/view.ctp:1142
msgid "Hides Invoice Prices to show consumers"
msgstr "Oculta los precios de las facturas para mostrar a los consumidores"

#: View/Products/index_retailer.ctp:341 View/Products/view.ctp:1142
msgid "Shows Invoice Prices"
msgstr "Muestra precios de factura"

#: View/Products/product_collections.ctp:111
msgid "Collection Title"
msgstr "Título de la colección"

#: View/Products/product_collections.ctp:121
msgid "Search Products"
msgstr "Buscar productos"

#: View/Products/product_collections.ctp:125
msgid "Add Selected"
msgstr "Agregar seleccionado"

#: View/Products/product_collections.ctp:165
msgid "Remove Product"
msgstr ""

#: View/Products/product_collections.ctp:174
msgid "No products found in this collection."
msgstr ""

#: View/Products/product_collections.ctp:186
msgid "Collection Image"
msgstr "Imagen de colección"

#: View/Products/product_collections.ctp:201
msgid "Add Image"
msgstr "Agregar imagen"

#: View/Products/product_collections.ctp:131;401
msgid "Browse Products"
msgstr "Explorar productos"

#: View/Products/view.ctp:364
msgid "Favorite"
msgstr "Favorito"

#: View/Products/view.ctp:371
msgid "Company"
msgstr "Compañía"

#: View/Products/view.ctp:515
msgid "Option"
msgstr "Opción"

#: View/Products/view.ctp:561
msgid "Product Organization"
msgstr "Organización del producto"

#: View/Products/view.ctp:565
msgid "Product type"
msgstr "Tipo de producto"

#: View/Products/view.ctp:592
msgid "Enter vendor name"
msgstr ""

#: View/Products/view.ctp:621
msgid "Retail Category"
msgstr "Categoría minorista"

#: View/Products/watch_products.ctp:11
msgid "You have no products on your watch list"
msgstr "No tiene productos en su lista de seguimiento"

#: View/Reports/prospects.ctp:38
msgid "All Brands"
msgstr "Todas las Marcas"

#: View/Reports/prospects.ctp:49
#: Plugin/Widgets/View/Layouts/locator_widgets.ctp:99
msgid "All Stores"
msgstr "Todas las Tiendas"

#: Plugin/Widgets/View/Layouts/locator_widgets.ctp:92
msgid "Filter by Category"
msgstr "Filtrar por categoría"

#: Plugin/Widgets/View/Layouts/locator_widgets.ctp:118
msgid "Store Locator by ShipEarly"
msgstr "Localizador de tiendas de ShipEarly"

#: View/RetailerCreditVouchers/index.ctp:93
msgid "Add New Credit"
msgstr "Agregar Nuevo Crédito"

#: View/RetailerCredits/index.ctp:128;130
msgid "All Statuses"
msgstr "Todos los Estados"

#: View/RetailerCredits/index.ctp:151;218
#: View/RetailerCredits/make_payment.ctp:96
msgid "Outstanding"
msgstr "Sobresaliente"

#: View/RetailerCredits/index.ctp:157 View/Retailers/edit.ctp:55
msgid "Credit Limit"
msgstr "Límite de Crédito"

#: View/RetailerCredits/index.ctp:169
msgid "Credits"
msgstr "Créditos"

#: View/RetailerCredits/index.ctp:183
msgid "Period Start"
msgstr "Comienzo del Período"

#: View/RetailerCredits/index.ctp:187
msgid "Period End"
msgstr "Fin del Período"

#: View/RetailerCredits/index.ctp:195
msgid "Store Location"
msgstr "Ubicación de la Tienda"

#: View/RetailerCredits/index.ctp:199
msgid "Financial Status"
msgstr "Estado Financiero"

#: View/RetailerCredits/index.ctp:212
msgid "Invoice #"
msgstr "Remito #"

#: View/RetailerCredits/index.ctp:220 View/RetailerCredits/make_payment.ctp:93
msgid "Due Date"
msgstr "Fecha de Vencimiento"

#: View/RetailerCredits/index.ctp:227
msgid "Add New Transaction"
msgstr "Agregar Nueva Transacción"

#: View/RetailerCredits/index.ctp:330;353
msgid "Make Payment"
msgstr "Hacer un Pago"

#: View/RetailerCredits/index.ctp:352
msgid "You must select a bank account."
msgstr "Debe seleccionar una cuenta bancaria."

#: View/RetailerCredits/index.ctp:354
msgid "New Transaction"
msgstr "Nueva Transacción"

#: View/RetailerCredits/index.ctp:355
msgid "New Voucher"
msgstr "Nuevo Bono"

#: View/RetailerCredits/index.ctp:364
msgid "Verify Bank Account"
msgstr "Verificación de Cuenta Bancaria"

#: View/RetailerCredits/index.ctp:365
msgid "New Account Details"
msgstr "Nuevos Detalles de la Cuenta"

#: View/RetailerCredits/make_payment.ctp:92
msgid "Invoice ID"
msgstr "ID de Remito"

#: View/RetailerCredits/make_payment.ctp:100
msgid "Payment Amount"
msgstr "Monto del Pago"

#: View/Retailers/edit.ctp:33
msgid "Billing Email"
msgstr "Correo Electrónico de Facturación"

#: View/Retailers/edit.ctp:36
msgid "Billing Company"
msgstr "Compañía de Facturación"

#: View/Retailers/edit.ctp:39
msgid "Install Rate ($/hr)"
msgstr "Tasa de Instalación ($/hr)"

#: View/Retailers/edit.ctp:49
msgid "Minimum Order Value"
msgstr "Valor Mínimo de Pedido"

#: View/Retailers/edit.ctp:101
msgid "Enable Split Payments"
msgstr "Habilitar Pagos Divididos"

#: View/Retailers/edit.ctp:104
msgid "Sales Reps"
msgstr "Representantes de Ventas"

#: View/Retailers/edit.ctp:113
msgid "Dealer Type"
msgstr ""

#: View/Retailers/edit.ctp:129
msgid "Assigned Product Tags"
msgstr "Etiquetas Asignadas a Productos"

#: View/Retailers/edit.ctp:145
msgid "Only display if all items in cart match tags assigned."
msgstr "Solo mostrar si todos los ítems en el carrito coinciden con las etiquetas asignadas."

#: View/Retailers/edit.ctp:148
msgid "Default Warehouse"
msgstr "Almacén"

#: View/Retailers/edit.ctp:161 View/Retailers/index.ctp:176
msgid "Territory"
msgstr "Territorio"

#: View/Retailers/edit.ctp:184
msgid "Enable Consumer Orders"
msgstr "Activar los pedidos de los consumidores"

#: View/Retailers/edit.ctp:187
msgid "Displays or hides a retailer from showing for consumers to select as an option in checkout."
msgstr "Muestra u oculta un minorista para que los consumidores lo seleccionen como opción en el proceso de pago."

#: View/Retailers/edit.ctp:196
msgid "Non-Stocking"
msgstr "Sin Almacenamiento"

#: View/Retailers/edit.ctp:201
msgid "Local Delivery Zip Codes"
msgstr "Códigos Postales para Envío Local"

#: View/Retailers/edit.ctp:212
msgid "Ship from Store Distance"
msgstr "Distancia desde la Tienda de Envío"

#: View/Retailers/edit.ctp:222
msgid "Ship from Store Zip Codes"
msgstr ""

#: View/Retailers/edit.ctp:236
msgid "Unprotected Zones Only"
msgstr ""

#: View/Retailers/edit.ctp:245
msgid "Use Retailer Shipping Rates"
msgstr ""

#: View/Retailers/edit.ctp:253
msgid "Uploaded Inventory Daily Reset"
msgstr ""

#: View/Retailers/edit.ctp:259
msgid "Remove Retailer"
msgstr "Remover Tienda"

#: View/Retailers/edit.ctp:262
msgid "Are you sure you want to remove this retailer connection?"
msgstr "¿Está seguro que desea eliminar esta conexión con la tienda?"

#: View/Retailers/edit.ctp:413
msgid "Provide a comma-separated list of zip/postal codes inside<br>the retailer's delivery radius to further refine their territory.<br>You can specify partial matches using * as a wildcard."
msgstr "Proporcione una lista separada por comas de códigos postales dentro del<br>radio de entrega del minorista para refinar aún más su territorio.<br>Puede especificar coincidencias parciales utilizando * como comodín."

#: View/Retailers/edit.ctp:438
msgid "Provide a comma-separated list of zip/postal codes inside and<br>only the codes inside the Ship from Store radius will be included.<br>You can specify partial matches using * as a wildcard."
msgstr "Proporcione una lista separada por comas de códigos postales internos y<br>solo se incluirán los códigos dentro del radio Enviar desde tienda.<br>Puede especificar coincidencias parciales utilizando * como comodín."

#: View/Retailers/index.ctp:174
msgid "Sales Rep"
msgstr ""

#: View/Retailers/index.ctp:175 View/Retailers/price_tier.ctp:53
#: View/Retailers/view.ctp:144
msgid "Pricing Tier"
msgstr "Nivel de precios"

#: View/Retailers/price_tier.ctp:55
msgid "Warehouse Availability"
msgstr ""

#: View/SalesReps/index.ctp:93
msgid "Remove %s from your sales reps?"
msgstr ""

#: View/Staff/index.ctp:26
msgid "Access"
msgstr "Acceso"

#: View/Staff/index.ctp:27
msgid "Last Login"
msgstr "Último Acceso"

#: View/Staff/index.ctp:40
msgid "Never"
msgstr "Nunca"

#: View/Staff/view.ctp:22
msgid "This staff account will have full permissions"
msgstr "La cuenta personal tendrá permiso completo"

#: View/Staff/view.ctp:26
msgid "View/Pay Invoices"
msgstr "Ver/Pagar Remitos"

#: View/Staff/view.ctp:27
msgid "Create/Edit Purchase Orders"
msgstr "Crear/Editar Ordenes de Compra"

#: View/Staff/view.ctp:102
msgid "Details"
msgstr "Detalles"

#: View/Staff/view.ctp:113
msgid "Role"
msgstr "Rol"

#: View/Staff/view.ctp:140
msgid "Send Order Notifications"
msgstr "Enviar Notificaciones de Pedido"

#: View/Staff/view.ctp:151
msgid "Staff Member Hours"
msgstr "Horario del Personal"

#: View/Staff/view.ctp:154
msgid "Manage Staff Hours"
msgstr "Administrar Horas del Personal"

#: View/Staff/view.ctp:162
msgid "Permissions"
msgstr "Permisos"

#: View/Staff/view.ctp:176
msgid "Services Offered"
msgstr "Servicios Ofrecidos"

#: View/Staff/view.ctp:178
msgid "Feature coming soon..."
msgstr "Característica próximamente…"

#: View/Staff/view.ctp:198
msgid "Cannot delete a staff member with order commissions"
msgstr "No puede eliminar un miembro del personal con comisiones de peidos"

#: View/UserTaxes/admin_edit.ctp:70;129 View/UserTaxes/edit.ctp:94;161
#: View/UserTaxes/edit_registration.ctp:90
msgid "Charge tax on shipping rates"
msgstr "Cobrar impuestos sobre el monto de envío"

#: View/UserTaxes/admin_edit.ctp:81 View/UserTaxes/edit.ctp:107
msgid "Include sales tax in product price and shipping rate"
msgstr ""

#: View/UserTaxes/admin_index.ctp:31
msgid "Postal validation"
msgstr ""

#: View/UserTaxes/admin_index.ctp:44
msgid "Phone validation"
msgstr ""

#: View/UserTaxes/admin_index.ctp:49
msgid "Edit Taxes"
msgstr ""

#: View/UserTaxes/edit.ctp:54
msgid "Country Tax"
msgstr ""

#: View/UserTaxes/edit.ctp:55
msgid "Base tax rate to be used if a state tax rate is not specified."
msgstr ""

#: View/UserTaxes/edit.ctp:120
msgid "All applicable taxes for %s. These taxes will be used unless overrides are specified in a Shipping Zone."
msgstr "Todos los impuestos aplicables para %s. Estos impuestos se utilizarán a menos que se especifiquen anulaciones en una Zona de envío."

#: View/UserTaxes/edit.ctp:172 View/UserTaxes/registrations.ctp:128
msgid "Back to top"
msgstr "Volver Arriba"

#: View/UserTaxes/edit_registration.ctp:10 View/UserTaxes/registrations.ctp:139
msgid "Tax Registration"
msgstr "Registro Fiscal"

#: View/UserTaxes/edit_registration.ctp:10 View/UserTaxes/registrations.ctp:69
msgid "Add Tax Registration"
msgstr "Agregar Registro Fiscal"

#: View/UserTaxes/edit_registration.ctp:15 View/UserTaxes/registrations.ctp:13
msgid "Province"
msgstr ""

#: View/UserTaxes/edit_registration.ctp:42 View/UserTaxes/registrations.ctp:82
msgid "Sales Tax ID"
msgstr "ID del Impuesto sobre las Ventas"

#: View/UserTaxes/edit_registration.ctp:47
msgid "Use Automatic Rate (Recommended)"
msgstr "Usar Tasa Automática (Recomendado)"

#: View/UserTaxes/edit_registration.ctp:48
msgid "Use Manual Rate"
msgstr "Usar Tarifa Manual"

#: View/UserTaxes/registrations.ctp:16
msgid "To charge sales tax in a state, you need to be registered with the appropriate government agency."
msgstr "Para cobrar los impuestos de venta en un estado, necesita estar registrado con la agencia estatal correspondiente."

#: View/UserTaxes/registrations.ctp:17
msgid "To charge sales tax in a province, you need to be registered with the appropriate government agency."
msgstr "Para cobrar los impuestos de venta en una provincia, necesita estar registrado con la agencia provincial correspondiente."

#: View/UserTaxes/registrations.ctp:83 View/Warehouses/index.ctp:25
msgid "Actions"
msgstr ""

#: View/UserTaxes/registrations.ctp:91
msgid "Looked up on demand"
msgstr ""

#: View/UserTaxes/registrations.ctp:91
msgid "Automatic"
msgstr ""

#: View/UserTaxes/registrations.ctp:93
msgid "Manual"
msgstr ""

#: View/UserTaxes/registrations.ctp:109
msgid "Are you sure you want to stop collecting tax from %s?"
msgstr "¿Está seguro que quiere dejar de cobrar impuestos en %s?"

#: View/UserTaxes/registrations.ctp:112
msgid "Local state is always registered"
msgstr ""

#: View/UserTaxes/registrations.ctp:112
msgid "Remove State"
msgstr "Eliminar Estado"

#: View/Users/<USER>
msgid "Core Application"
msgstr "Aplicación Principal"

#: View/Users/<USER>
msgid "Subscribe"
msgstr "Suscribirse"

#: View/Users/<USER>
msgid "Card Information"
msgstr "Información de la Tarjeta"

#: View/Users/<USER>
msgid "Select up to 3 categories of interest"
msgstr "Seleccione hasta 3 categorías de interés"

#: View/Users/<USER>
msgid "Select your categories of interest"
msgstr "Seleccione sus categorías de interes"

#: View/Users/<USER>
msgid "You can't select more than 3 categories of interest"
msgstr "No puede seleccionar mas de 3 categorías de interés"

#: View/Users/<USER>
msgid "Old password"
msgstr "Contraseña Anterior"

#: View/Users/<USER>
msgid "New Password"
msgstr "Contraseña Nueva"

#: View/Users/<USER>
msgid "Confirm new Password"
msgstr "Confirme su Contraseña"

#: View/Users/<USER>
msgid "Typography"
msgstr "Tipografía"

#: View/Users/<USER>
msgid "Font Family"
msgstr "Fuente"

#: View/Users/<USER>
msgid "Font Preview"
msgstr "Vista Previa de la Fuente"

#: View/Users/<USER>
msgid "Checkout Header"
msgstr ""

#: View/Users/<USER>
msgid "Background Image"
msgstr ""

#: View/Users/<USER>
msgid "Checkout Logo"
msgstr ""

#: View/Users/<USER>
msgid "Display Left"
msgstr ""

#: View/Users/<USER>
msgid "Display Center"
msgstr ""

#: View/Users/<USER>
msgid "Company Contact"
msgstr "Contacto de la Compañía"

#: View/Users/<USER>
msgid "Support Email"
msgstr "Correo Electrónico de Soporte"

#: View/Users/<USER>
msgid "Position"
msgstr "Posición"

#: View/Users/<USER>
msgid "Support Link"
msgstr "Link de Soporte"

#: View/Users/<USER>
msgid "Organization Support Link"
msgstr "Link de Soporte de la Organización"

#: View/Users/<USER>
msgid "Brand Asset Link"
msgstr "Enlace de activo de marca"

#: View/Users/<USER>
msgid "Redirect customers to digital assets such as logos, images, etc."
msgstr "Redirigir a los clientes a activos digitales como logotipos, imágenes, etc."

#: View/Users/<USER>
msgid "Forgot Password?"
msgstr "¿Olvidó su contraseña?"

#: View/Users/<USER>
msgid "If you have forgotten your password, enter your email below and press Submit"
msgstr "Si olvidó su contraseña, ingrese su correo electrónico y presione Enviar"

#: View/Users/<USER>
msgid "Disable to prevent conflicts with Google Tag Manager"
msgstr "Deshabilitar para evitar conflictos con Google Tag Manager"

#: View/Users/<USER>
msgid "Pixel ID"
msgstr ""

#: View/Users/<USER>
msgid "Conversions API Access Token"
msgstr ""

#: View/Users/<USER>
msgid "Conversions API Test Event Code"
msgstr ""

#: View/Users/<USER>
msgid "Used for debugging with the Test Events Tool. Should be left empty otherwise."
msgstr ""

#: View/Users/<USER>/Users/<USER>
msgid "Password"
msgstr "Contraseña"

#: View/Users/<USER>
msgid "Remember me"
msgstr "Recordarme"

#: View/Users/<USER>
msgid "Login"
msgstr "Ingresar"

#: View/Users/<USER>
msgid "Create an Account"
msgstr "Crear una Cuenta"

#: View/Users/<USER>
msgid "I am a"
msgstr "Soy"

#: View/Users/<USER>
msgid "Connect Stripe Account"
msgstr "Conectar con Cuenta de Stripe"

#: View/Users/<USER>
msgid "Payment Methods"
msgstr "Métodos de Pago"

#: View/Users/<USER>
msgid "Financing"
msgstr ""

#: View/Users/<USER>
msgid "Enable Affirm Financing"
msgstr ""

#: View/Users/<USER>
msgid "Dealer Payout"
msgstr "Pago al crupier"

#: View/Users/<USER>
msgid "Shipment Dealer Options"
msgstr ""

#: View/Users/<USER>
msgid "Dealer Options"
msgstr ""

#: View/Users/<USER>
msgid "Enter a Zip/Postal Code"
msgstr "Ingrese Código Postal"

#: View/Users/<USER>
msgid "Retailer Search"
msgstr "Búsqueda de Distribuidor"

#: View/Users/<USER>
msgid "Confirm Password"
msgstr "Confirmar Contraseña"

#: View/Users/<USER>
msgid "Select Your State/Province"
msgstr "Selección su Estado/Provincia"

#: View/Users/<USER>
msgid "eCommerce Platform"
msgstr "Plataforma de correo electrónico"

#: View/Users/<USER>
msgid "Select Your Primary Currency"
msgstr "Seleccione su Moneda Principal"

#: View/Users/<USER>
msgid "Primary Currency"
msgstr "Moneda Principal"

#: View/Users/<USER>
msgid "Please choose the same primary currency that you are using in your inventory software"
msgstr "Elija la misma moneda principal que está utilizando en su software de inventario"

#: View/Users/<USER>
msgid "Select Your Inventory Software"
msgstr "Seleccione su software de inventario"

#: View/Users/<USER>
msgid "Inventory Software"
msgstr "Software de Inventario"

#: View/Users/<USER>
msgid "I agree to %s and %s"
msgstr "Acepto %s y %s"

#: View/Users/<USER>
msgid "the Terms of Use"
msgstr "los Términos y Condiciones"

#: View/Users/<USER>
msgid "the Privacy policy"
msgstr "las Políticas de Privacidad"

#: View/Warehouses/add.ctp:7
msgid "Add Warehouse"
msgstr ""

#: View/Warehouses/edit.ctp:6
msgid "Edit Warehouse"
msgstr ""

#: View/Warehouses/edit.ctp:21
msgid "Are you sure you want to delete %s?"
msgstr ""

#: View/Warehouses/index.ctp:8
msgid "Warehouses"
msgstr ""

#: View/Warehouses/index.ctp:13
msgid "New Warehouse"
msgstr ""

#: View/Warehouses/index.ctp:23
msgid "Is active"
msgstr ""

#: View/Warehouses/index.ctp:32
msgid "Yes"
msgstr ""

#: View/Warehouses/index.ctp:32
msgid "No"
msgstr ""

#: Controller/MenusController.php:37
#: View/Helper/SidebarHelper.php:278
msgid "Menus"
msgstr "Menús"

#: View/Menus/index.ctp:12
msgid "Create Menu"
msgstr "Crear menú"

#: View/Menus/index.ctp:21
msgid "Menu Name"
msgstr "Nombre del menú"

#: View/Menus/index.ctp:22
#: View/Menus/edit.ctp:90
msgid "Menu Items"
msgstr "Elementos del menú"

#: View/Menus/index.ctp:40
msgid "No items"
msgstr "Sin artículos"

#: View/Menus/index.ctp:49
msgid "No menus found."
msgstr "No se encontraron menús."

#: View/Menus/edit.ctp:108;301
msgid "Label"
msgstr "Etiqueta"

#: View/Menus/edit.ctp:118;302
msgid "URL"
msgstr "URL"

#: View/Menus/edit.ctp:132
msgid "Add menu item"
msgstr "Agregar elemento de menú"

#: Model/AccessToken.php:validation for field id
#: Model/B2bShipToAddress.php:validation manufacturer_retailer_id;validation
#: country_id;validation state_id Model/BrandStaff.php:validation
#: brand_id;validation staff_id;validation role_id
#: Model/BrandStaffPermission.php:validation brand_staff_id
#: Model/BrandStaffRole.php:validation brand_id Model/CountryTax.php:validation
#: country_id Model/FulfillmentProduct.php:validation fulfillment_id;validation
#: order_product_id;validation dealer_order_product_id
#: Model/ManufacturerRetailerSalesRep.php:validation sales_rep_id
#: Model/ManufacturerRetailerTag.php:validation tag_id
#: Model/ManufacturerSalesRep.php:validation user_id;validation
#: Model/OrderComment.php:validation order_id;validation author_id
#: Model/OrderSalesRep.php:validation Model/ProductTag.php:validation
#: product_id;validation Model/StateTax.php:validation state_id;validation
#: Model/Tag.php:validation user_id Model/Territory.php:validation
#: Model/UserTax.php:validation
msgid "Invalid id"
msgstr ""

#: Model/AccessToken.php:validation for field token
msgid "Invalid uuid token"
msgstr ""

#: Model/AccessToken.php:validation for field access_granted
#: Model/CountryTax.php:validation includes_shipping;validation
#: included_in_prices Model/CountryValidationMethod.php:validation enabled
#: Model/Courier.php:validation deleted Model/Customer.php:validation
#: accepts_marketing Model/ManufacturerRetailer.php:validation
#: use_retailer_shipping_rates;validation enable_uploaded_inventory_reset
#: Model/ManufacturerSalesRep.php:validation is_distributor
#: Model/OrderComment.php:validation is_internal Model/Product.php:validation
#: is_in_stock_only Model/ProductStateFee.php:validation is_taxable
#: Model/ShippingCarrier.php:validation is_active Model/StateTax.php:validation
#: uses_api Model/StripeUserCapability.php:validation status
#: Model/User.php:validation enable_ga_tracking;validation
#: enable_creating_pos_orders Model/UserCountryTax.php:validation
#: Model/UserShippingPackage.php:validation is_default
#: Model/UserTax.php:validation uses_manual_rate;validation includes_shipping
msgid "Invalid boolean"
msgstr ""

#: Model/AccessToken.php:validation for field expires_at;validation
#: created_at;validation updated_at Model/B2bShipToAddress.php:validation
#: Model/B2bShippingRateTitle.php:validation created_at
#: Model/BrandStaff.php:validation Model/BrandStaffPermission.php:validation
#: Model/BrandStaffRole.php:validation Model/Collection.php:validation
#: Model/CollectionsProduct.php:validation Model/CountryTax.php:validation
#: Model/CountryValidationMethod.php:validation Model/Courier.php:validation
#: Model/DealerOrder.php:validation Model/DealerOrderProduct.php:validation
#: Model/DealerOrderRefund.php:validation
#: Model/DealerOrderRefundProduct.php:validation
#: Model/DiscountUsage.php:validation Model/EcommerceView.php:validation
#: Model/EcommerceViewProduct.php:validation
#: Model/EcommerceViewRetailer.php:validation Model/Fulfillment.php:validation
#: Model/FulfillmentProduct.php:validation
#: Model/InventoryTransfer.php:validation
#: Model/InventoryTransferProduct.php:validation
#: Model/InventoryTransferProductReservation.php:validation
#: Model/Label.php:validation Model/LocalDeliveryZipCode.php:validation
#: Model/ManufacturerRetailerLabel.php:validation
#: Model/ManufacturerRetailerNote.php:validation
#: Model/ManufacturerRetailerSalesRep.php:validation
#: Model/ManufacturerRetailerTag.php:validation
#: Model/ManufacturerSalesRep.php:validation Model/OrderComment.php:validation
#: Model/OrderFulfillmentGroup.php:validation Model/OrderPayout.php:validation
#: Model/OrderSalesRep.php:validation Model/OrderTag.php:validation
#: Model/OrderTagName.php:validation
#: Model/PricingTiersCollection.php:validation
#: Model/PricingTiersHiddenWarehouse.php:validation
#: Model/ProductPrice.php:validation Model/ProductStateFee.php:validation
#: Model/ProductTag.php:validation Model/ProductTitle.php:validation
#: Model/ProductVariantOption.php:validation
#: Model/RetailStaffLocation.php:validation Model/RetailerCredit.php:validation
#: Model/RetailerCreditItem.php:validation
#: Model/RetailerCreditPayment.php:validation
#: Model/RetailerCreditVoucher.php:validation
#: Model/ShipFromStoreZipCode.php:validation
#: Model/StaffPermission.php:validation Model/StateTax.php:validation
#: Model/StripeConnectCustomer.php:validation
#: Model/StripeUserCapability.php:validation Model/Tag.php:validation
#: Model/Territory.php:validation Model/UnitBasedShippingRate.php:validation
#: Model/UnitBasedShippingRateCategory.php:validation
#: Model/UserCountryTax.php:validation Model/UserCurrency.php:validation
#: Model/UserMapMarker.php:validation Model/UserSchedule.php:validation
#: Model/UserTax.php:validation Model/VariantOption.php:validation
#: Model/VariantOptionValue.php:validation
#: Model/VariantSortOrder.php:validation
#: Model/WarehouseDistributor.php:validation
#: Model/WarehouseProductReservation.php:validation
msgid "Invalid datetime"
msgstr ""

#: Model/Administrator.php:validation for field username;validation password
#: Model/ApiClient.php:validation client_id;validation client_secret;validation
#: name Model/B2bShippingRateCategory.php:validation product_category
#: Model/B2bShippingRateTitle.php:validation product_title
#: Model/Collection.php:validation source_id;validation handle;validation title
#: Model/Configuration.php:validation name;validation value
#: Model/Country.php:validation country_code;validation country_name;validation
#: country_icon Model/CountryValidationMethod.php:validation method
#: Model/Courier.php:validation slug;validation phone;validation
#: other_name;validation web_url Model/Customer.php:validation
#: email_address;validation customerID;validation preferred_language
#: Model/CustomerAddress.php:validation customer_addresses_id
#: Model/DealerOrder.php:validation orderNO;validation
#: source_order_name;validation wholesale_charge_id;validation
#: product_details;validation payment_method;validation payment_reference_id
#: Model/DealerOrderRefund.php:validation transaction_id;validation reason
#: Model/DiscountUsage.php:validation email_address
#: Model/Fulfillment.php:validation Model/InventoryTransfer.php:validation
#: Model/Label.php:validation Model/LocalDeliveryZipCode.php:validation
#: zip_code Model/ManufacturerRetailerNote.php:validation content
#: Model/OrderAddress.php:validation type;validation first_name;validation
#: last_name;validation company_name;validation address1;validation
#: address2;validation city;validation zipcode;validation telephone
#: Model/OrderFulfillmentGroup.php:validation groupID
#: Model/OrderPayout.php:validation stripe_payout_id;validation
#: stripe_balance_transaction_id;validation currency;validation
#: payout_currency;validation status;validation last_four_digits;validation
#: institution;validation destination_type Model/OrderRefund.php:validation
#: reason;validation source_id Model/PricingTier.php:validation
#: pricingtiername;validation currencytype Model/ProductPrice.php:validation
#: currency_code Model/ProductTitle.php:validation source_type
#: Model/ProductVariantOption.php:validation
#: Model/RetailerCreditItem.php:validation type
#: Model/ShipFromStoreZipCode.php:validation
#: Model/ShippingCarrier.php:validation fullname;validation
#: parameters;validation icon Model/StaffPermission.php:validation
#: Model/StripeConnectCustomer.php:validation stripe_cus_id
#: Model/StripeUserCapability.php:validation
#: Model/UnitBasedShippingRateCategory.php:validation
#: Model/UpdateCron.php:validation unique_id Model/User.php:validation
#: gtm_container_id;validation ga_tracking_id;validation
#: google_conversion_id;validation google_conversion_label;validation
#: fbpixelId;validation fbpixel_access_token;validation
#: fbpixel_test_event_code;validation klaviyo_public_key
#: Model/UserCountryTax.php:validation Model/UserCurrency.php:validation
#: Model/UserMapMarker.php:validation uri;validation
#: Model/UserSchedule.php:validation order_type;validation
#: schedule_name;validation schedule_link
#: Model/UserShippingPackage.php:validation dimension_unit;validation
#: weight_unit Model/VariantOption.php:validation
#: Model/VariantOptionValue.php:validation
#: Model/VariantSortOrder.php:validation variant_name
msgid "This field cannot be left blank"
msgstr ""

#: Model/Administrator.php:validation for field 2fa_secret
msgid "Invalid 2FA secret"
msgstr ""

#: Model/Administrator.php:validation for field 2fa_bypass_duration
msgid "Invalid 2FA bypass duration"
msgstr ""

#: Model/Administrator.php:validation for field 2fa_bypass_duration
#: Model/B2bShippingRate.php:validation price;validation min_range;validation
#: max_range Model/InventoryTransferProductReservation.php:validation quantity
#: Model/WarehouseProductReservation.php:validation
#: Model/ZoneTaxOverride.php:validation tax_rate
msgid "Cannot be negative"
msgstr ""

#: Model/Administrator.php:validation for field last_login
msgid "Invalid last login date"
msgstr ""

#: Model/ApiClient.php:validation for field user_id
#: Model/Collection.php:validation Model/Customer.php:validation
#: Model/DealerOrder.php:validation Model/DealerOrderProduct.php:validation
#: Model/DiscountUsage.php:validation Model/EcommerceView.php:validation
#: Model/ImportEmail.php:validation from Model/InventoryTransfer.php:validation
#: Model/Label.php:validation Model/PricingTier.php:validation
#: Model/ProductTitle.php:validation Model/RetailStaffLocation.php:validation
#: Model/RetailerCredit.php:validation
#: Model/RetailerCreditVoucher.php:validation
#: Model/StripeConnectCustomer.php:validation
#: Model/UnitBasedShippingRate.php:validation Model/UpdateCron.php:validation
#: Model/UserCountryTax.php:validation Model/UserCurrency.php:validation
#: Model/UserMapMarker.php:validation Model/UserSchedule.php:validation
#: Model/UserShippingCarrier.php:validation
#: Model/UserShippingCarrierRate.php:validation
#: Model/UserShippingPackage.php:validation
#: Model/VariantSortOrder.php:validation
msgid "Invalid user id"
msgstr ""

#: Model/ApiClient.php:validation for field name
msgid "You already have a client with this name"
msgstr ""

#: Model/B2bCart.php:validation for field requested_ship_date
#: Model/Order.php:validation requested_b2b_ship_date
msgid "Invalid ship date"
msgstr ""

#: Model/B2bCart.php:validation for field requested_ship_date
msgid "Ship date has already passed"
msgstr ""

#: Model/B2bCartProduct.php:validation for field b2b_cart_id
msgid "Invalid b2b cart id"
msgstr ""

#: Model/B2bCartProduct.php:validation for field product_id
#: Model/CollectionsProduct.php:validation
#: Model/DealerOrderProduct.php:validation
#: Model/EcommerceViewProduct.php:validation
#: Model/InventoryTransferProduct.php:validation
#: Model/ProductCategories.php:validation Model/ProductPrice.php:validation
#: Model/ProductStateFee.php:validation Model/ProductTier.php:validation
#: Model/ProductVariantOption.php:validation
#: Model/WarehouseProduct.php:validation
msgid "Invalid product id"
msgstr ""

#: Model/B2bCartProduct.php:validation for field product_id
msgid "Product does not belong to cart owner"
msgstr ""

#: Model/B2bCartProduct.php:validation for field warehouse_id
#: Model/DealerOrderProduct.php:validation Model/Fulfillment.php:validation
#: Model/InventoryTransfer.php:validation destination_warehouse_id
#: Model/PricingTiersHiddenWarehouse.php:validation
#: Model/WarehouseDistributor.php:validation
#: Model/WarehouseProduct.php:validation
msgid "Invalid warehouse id"
msgstr ""

#: Model/B2bCartProduct.php:validation for field warehouse_id
msgid "Warehouse does not belong to cart owner"
msgstr ""

#: Model/B2bCartProduct.php:validation for field inventory_transfer_id
#: Model/DealerOrderProduct.php:validation Model/OrderProduct.php:validation
msgid "Invalid transfer id"
msgstr ""

#: Model/B2bCartProduct.php:validation for field inventory_transfer_id
msgid "Transfer has the wrong destination warehouse"
msgstr ""

#: Model/B2bCartProduct.php:validation for field quantity
msgid "The order quantity must be a number greater than zero"
msgstr ""

#: Model/B2bCartProduct.php:validation for field quantity
msgid "The order quantity exceeds the amount of inventory available"
msgstr ""

#: Model/B2bCartProduct.php:validation for field quantity
msgid "The order quantity does not meet the minimum order quantity"
msgstr ""

#: Model/B2bCartProduct.php:validation for field quantity
msgid "The order quantity exceeds the maximum order quantity"
msgstr ""

#: Model/B2bShippingRate.php:validation for field type;validation
#: price;validation min_range;validation max_range
#: Model/MailQueue.php:validation status Model/ZoneTaxOverride.php:validation
#: tax_rate
msgid "required"
msgstr ""

#: Model/B2bShippingRate.php:validation for field type
msgid "Invalid type"
msgstr ""

#: Model/B2bShippingRateCategory.php:validation for field b2b_shipping_rate_id
#: Model/B2bShippingRateTitle.php:validation
msgid "Invalid b2b shipping rate id"
msgstr ""

#: Model/BrandStaff.php:validation for field has_full_permissions
msgid "Invalid value for full permissions"
msgstr ""

#: Model/BrandStaffPermission.php:validation for field level
msgid "Invalid permission level"
msgstr ""

#: Model/CollectionsProduct.php:validation for field collection_id
#: Model/PricingTiersCollection.php:validation
#: Model/ZoneTaxOverrideCollection.php:validation
msgid "Invalid collection id"
msgstr ""

#: Model/Contactpersons.php:validation for field firstname
msgid "First name cannot be empty"
msgstr ""

#: Model/Contactpersons.php:validation for field lastname
msgid "Last name cannot be empty"
msgstr ""

#: Model/Country.php:validation for field display_order
msgid "Must be an integer"
msgstr ""

#: Model/CountryTax.php:validation for field rate Model/StateTax.php:validation
#: Model/UserCountryTax.php:validation tax_rate Model/UserTax.php:validation
msgid "Invalid tax rate"
msgstr ""

#: Model/CountryTax.php:validation for field rate;validation percentage
#: Model/DealerOrderRefund.php:validation tax_portion
#: Model/StateTax.php:validation Model/UserCountryTax.php:validation
#: tax_rate;validation Model/UserTax.php:validation tax_percentage
msgid "Tax cannot be negative"
msgstr ""

#: Model/CountryTax.php:validation for field percentage
#: Model/StateTax.php:validation Model/UserCountryTax.php:validation
#: Model/UserTax.php:validation tax_percentage
msgid "Invalid tax percentage"
msgstr ""

#: Model/CountryValidationMethod.php:validation for field country_id
#: Model/OrderAddress.php:validation Model/ProductStateFee.php:validation
#: Model/UserCountryTax.php:validation
msgid "Invalid country id"
msgstr ""

#: Model/CountryValidationMethod.php:validation for field method
msgid "Invalid method"
msgstr ""

#: Model/CreditTerm.php:validation for field user_id;validation days_due
msgid "numeric"
msgstr ""

#: Model/CreditTerm.php:validation for field description
msgid "notBlank"
msgstr ""

#: Model/CustomerAddress.php:validation for field customer_id
msgid "Invalid customer id"
msgstr ""

#: Model/DealerOrder.php:validation for field order_id
#: Model/DealerOrderProduct.php:validation Model/Fulfillment.php:validation
#: Model/InventoryTransferProductReservation.php:validation
#: Model/OrderAddress.php:validation Model/OrderFulfillmentGroup.php:validation
#: Model/OrderPayout.php:validation Model/OrderRefund.php:validation
#: Model/OrderTag.php:validation
#: Model/WarehouseProductReservation.php:validation
msgid "Invalid order id"
msgstr ""

#: Model/DealerOrder.php:validation for field order_id
msgid "The order already has a dealer order"
msgstr ""

#: Model/DealerOrder.php:validation for field total_price
msgid "Invalid total price"
msgstr ""

#: Model/DealerOrder.php:validation for field product_total_price
msgid "Invalid product subtotal"
msgstr ""

#: Model/DealerOrder.php:validation for field total_discount
#: Model/DealerOrderProduct.php:validation
msgid "Invalid total discount"
msgstr ""

#: Model/DealerOrder.php:validation for field tax
#: Model/DealerOrderProduct.php:validation
msgid "Invalid tax rate percent"
msgstr ""

#: Model/DealerOrder.php:validation for field total_tax
msgid "Invalid total tax"
msgstr ""

#: Model/DealerOrder.php:validation for field shipping_amount
msgid "Invalid shipping amount"
msgstr ""

#: Model/DealerOrder.php:validation for field shipping_tax_amount
msgid "Invalid shipping tax amount"
msgstr ""

#: Model/DealerOrder.php:validation for field splitpayment_percentage
msgid "Invalid split payment rate percent"
msgstr ""

#: Model/DealerOrder.php:validation for field splitpayment_amount
msgid "Invalid split payment amount"
msgstr ""

#: Model/DealerOrder.php:validation for field wholesale_charge_amount
msgid "Invalid wholesale charge amount"
msgstr ""

#: Model/DealerOrder.php:validation for field payment_status
msgid "Invalid payment status"
msgstr ""

#: Model/DealerOrder.php:validation for field shipment_date
msgid "Invalid shipment datetime"
msgstr ""

#: Model/DealerOrderProduct.php:validation for field dealer_order_id
#: Model/DealerOrderRefund.php:validation Model/Fulfillment.php:validation
#: Model/RetailerCredit.php:validation
msgid "Invalid dealer order id"
msgstr ""

#: Model/DealerOrderProduct.php:validation for field product_price
msgid "Invalid unit price"
msgstr ""

#: Model/DealerOrderProduct.php:validation for field product_quantity
#: Model/DealerOrderRefundProduct.php:validation quantity
#: Model/InventoryTransferProductReservation.php:validation
#: Model/OrderRefundProduct.php:validation
#: Model/WarehouseProduct.php:validation
#: Model/WarehouseProductReservation.php:validation
msgid "Invalid quantity"
msgstr ""

#: Model/DealerOrderProduct.php:validation for field restock_date
msgid "Invalid restock datetime"
msgstr ""

#: Model/DealerOrderRefund.php:validation for field amount
msgid "Invalid refund value"
msgstr ""

#: Model/DealerOrderRefund.php:validation for field amount
msgid "Refund must be greater than 0.00"
msgstr ""

#: Model/DealerOrderRefund.php:validation for field amount
msgid "Refund exceeds the amount available"
msgstr ""

#: Model/DealerOrderRefund.php:validation for field shipping_portion
msgid "Invalid shipping value"
msgstr ""

#: Model/DealerOrderRefund.php:validation for field shipping_portion
msgid "Shipping cannot be negative"
msgstr ""

#: Model/DealerOrderRefund.php:validation for field shipping_portion
msgid "Shipping exceeds the amount available"
msgstr ""

#: Model/DealerOrderRefund.php:validation for field tax_portion
msgid "Invalid tax value"
msgstr ""

#: Model/DealerOrderRefund.php:validation for field tax_portion
msgid "Tax exceeds the amount available"
msgstr ""

#: Model/DealerOrderRefundProduct.php:validation for field
#: dealer_order_refund_id
msgid "Invalid dealer order refund id"
msgstr ""

#: Model/DealerOrderRefundProduct.php:validation for field
#: dealer_order_product_id
msgid "Invalid dealer order product id"
msgstr ""

#: Model/DealerOrderRefundProduct.php:validation for field quantity
#: Model/FulfillmentProduct.php:validation
msgid "Quantity exceeds the amount available"
msgstr ""

#: Model/Discount.php:validation for field code;validation start_date
msgid "An existing discount code conflicts with this date range"
msgstr ""

#: Model/Discount.php:validation for field option
msgid "Invalid discount option"
msgstr ""

#: Model/Discount.php:validation for field option_amount
msgid "Discount amount cannot be negative"
msgstr ""

#: Model/Discount.php:validation for field start_date
msgid "Start date must be before the end date"
msgstr ""

#: Model/Discount.php:validation for field start_date;validation is_automatic
msgid "An existing automatic discount conflicts with this date range"
msgstr ""

#: Model/Discount.php:validation for field end_date
msgid "End date must be after the start date"
msgstr ""

#: Model/Discount.php:validation for field shipping_window_start_date
msgid "Shipping window start date must be before the shipping window end date"
msgstr ""

#: Model/Discount.php:validation for field shipping_window_end_date
msgid "Shipping window end date must be after the shipping window start date"
msgstr ""

#: Model/Discount.php:validation for field prerequisite_subtotal
msgid "Invalid minimum amount"
msgstr ""

#: Model/Discount.php:validation for field prerequisite_subtotal
msgid "Minimum amount cannot be negative"
msgstr ""

#: Model/Discount.php:validation for field prerequisite_quantity;validation
#: order_quantity;validation usage_limit_quantity;validation usage_count
msgid "rule"
msgstr ""

#: Model/Discount.php:validation for field prerequisite_option
msgid "Invalid buy x option"
msgstr ""

#: Model/Discount.php:validation for field prerequisite_values
msgid "Invalid buy x values"
msgstr ""

#: Model/Discount.php:validation for field order_option
msgid "Invalid order option"
msgstr ""

#: Model/Discount.php:validation for field option_values
msgid "Invalid order values"
msgstr ""

#: Model/Discount.php:validation for field retailer_option
msgid "Invalid retailer option"
msgstr ""

#: Model/Discount.php:validation for field retailer_values
msgid "Invalid retailer values"
msgstr ""

#: Model/Discount.php:validation for field usage_limit_option
msgid "Invalid usage limit option"
msgstr ""

#: Model/DiscountCreditTerm.php:validation for field discount_id
msgid "discount_id must be numeric"
msgstr ""

#: Model/DiscountCreditTerm.php:validation for field credit_term_id
msgid "credit_term_id must be numeric"
msgstr ""

#: Model/DiscountUsage.php:validation for field discount_id
msgid "Invalid discount id"
msgstr ""

#: Model/EcommerceViewProduct.php:validation for field ecommerce_view_id
#: Model/EcommerceViewRetailer.php:validation
msgid "Invalid ecommerce view id"
msgstr ""

#: Model/EcommerceViewRetailer.php:validation for field retailer_id
#: Model/RetailStaffLocation.php:validation Model/RetailerCredit.php:validation
#: Model/RetailerCreditVoucher.php:validation
#: Model/StripeConnectCustomer.php:validation
msgid "Invalid retailer id"
msgstr ""

#: Model/EcommerceViewRetailer.php:validation for field distance
msgid "Invalid distance"
msgstr ""

#: Model/EcommerceViewRetailer.php:validation for field distance
msgid "Distance cannot be negative"
msgstr ""

#: Model/Fulfillment.php:validation for field order_id
msgid "Cannot set both dealer_order_id and order_id"
msgstr ""

#: Model/Fulfillment.php:validation for field dealer_order_id
msgid "Cannot set both order_id and dealer_order_id"
msgstr ""

#: Model/Fulfillment.php:validation for field name
msgid "The name has already been taken"
msgstr ""

#: Model/Fulfillment.php:validation for field courier_id
msgid "Invalid courier id"
msgstr ""

#: Model/Fulfillment.php:validation for field tracking_number
msgid "Tracking name cannot be blank"
msgstr ""

#: Model/Fulfillment.php:validation for field tracking_url
msgid "Tracking url cannot be blank"
msgstr ""

#: Model/Fulfillment.php:validation for field tracking_url
msgid "Tracking url must be a valid full url"
msgstr ""

#: Model/FulfillmentProduct.php:validation for field quantity
#: Model/InventoryTransferProduct.php:validation
msgid "Invalid quantity value"
msgstr ""

#: Model/ImportEmail.php:validation for field email
msgid "Invalid email"
msgstr ""

#: Model/ImportEmail.php:validation for field notimes
msgid "Invalid notimes"
msgstr ""

#: Model/InventoryTransfer.php:validation for field name
msgid "The value is already taken"
msgstr ""

#: Model/InventoryTransfer.php:validation for field status
#: Model/MailQueue.php:validation Model/ManufacturerRetailer.php:validation
#: Model/UpdateCron.php:validation
msgid "Invalid status"
msgstr ""

#: Model/InventoryTransfer.php:validation for field expected_arrival_date
msgid "Invalid arrival date"
msgstr ""

#: Model/InventoryTransfer.php:validation for field reference
msgid "Reference cannot be blank"
msgstr ""

#: Model/InventoryTransferProduct.php:validation for field
#: inventory_transfer_id
msgid "Invalid inventory transfer id"
msgstr ""

#: Model/InventoryTransferProduct.php:validation for field quantity
msgid "Quantity must be greater than zero"
msgstr ""

#: Model/InventoryTransferProduct.php:validation for field quantity
msgid "Remaining quantity cannot be negative"
msgstr ""

#: Model/InventoryTransferProduct.php:validation for field accepted_quantity
msgid "Invalid accepted quantity value"
msgstr ""

#: Model/InventoryTransferProductReservation.php:validation for field
#: inventory_transfer_product_id
msgid "Invalid inventory transfer product id"
msgstr ""

#: Model/InventoryTransferProductReservation.php:validation for field
#: inventory_transfer_product_id
#: Model/WarehouseProductReservation.php:validation warehouse_product_id
msgid "This item already has a reservation"
msgstr ""

#: Model/LegacyRetailerCredit.php:validation for field value
msgid "Invalid transaction value."
msgstr ""

#: Model/LegacyRetailerCredit.php:validation for field value
msgid "Transaction value exceeds the remaining credit limit."
msgstr ""

#: Model/LocalDeliveryZipCode.php:validation for field manufacturer_retailer_id
#: Model/ManufacturerRetailerLabel.php:validation
#: Model/ManufacturerRetailerNote.php:validation
#: Model/ShipFromStoreZipCode.php:validation
msgid "Invalid manufacturer retailer id"
msgstr ""

#: Model/ManufacturerRetailer.php:validation for field status
msgid "Unknown status"
msgstr ""

#: Model/ManufacturerRetailer.php:validation for field
#: dealer_protect_radius;validation local_delivery_radius
msgid "Invalid radius"
msgstr ""

#: Model/ManufacturerRetailer.php:validation for field
#: dealer_protect_radius;validation local_delivery_radius
msgid "Radius cannot be negative"
msgstr ""

#: Model/ManufacturerRetailerLabel.php:validation for field label_id
msgid "Invalid label id"
msgstr ""

#: Model/ManufacturerRetailerNote.php:validation for field author_id
msgid "Invalid author id"
msgstr ""

#: Model/ManufacturerRetailerNote.php:validation for field author_name
#: Model/OrderComment.php:validation
msgid "Author name cannot be empty"
msgstr ""

#: Model/Order.php:validation for field external_invoice_id
msgid "Invalid invoice number"
msgstr ""

#: Model/Order.php:validation for field payment_method
msgid "Invalid payment method"
msgstr ""

#: Model/Order.php:validation for field payment_method_subtype
msgid "Invalid payment method subtype"
msgstr ""

#: Model/Order.php:validation for field payment_captured_at
msgid "Invalid capture date"
msgstr ""

#: Model/Order.php:validation for field risk_level
msgid "Invalid risk level"
msgstr ""

#: Model/OrderAddress.php:validation for field state_id
#: Model/ProductStateFee.php:validation
msgid "Invalid state id"
msgstr ""

#: Model/OrderAddress.php:validation for field latitude
msgid "Invalid latitude"
msgstr ""

#: Model/OrderAddress.php:validation for field longitude
msgid "Invalid longitude"
msgstr ""

#: Model/OrderFulfillmentGroup.php:validation for field order_id
msgid "Order already has a fulfillment group"
msgstr ""

#: Model/OrderFulfillmentGroup.php:validation for field groupID
msgid "Invalid groupID"
msgstr ""

#: Model/OrderPayout.php:validation for field amount
#: Model/OrderRefund.php:validation Model/RetailerCreditItem.php:validation
#: Model/RetailerCreditPayment.php:validation
#: Model/RetailerCreditVoucher.php:validation
#: Model/UnitBasedShippingRate.php:validation
msgid "Invalid amount"
msgstr ""

#: Model/OrderPayout.php:validation for field payout_total_amount
msgid "Invalid payout total amount"
msgstr ""

#: Model/OrderPayout.php:validation for field date
msgid "Invalid date"
msgstr ""

#: Model/OrderRefund.php:validation for field shipping_portion
msgid "Invalid shipping portion"
msgstr ""

#: Model/OrderRefund.php:validation for field tax_portion
msgid "Invalid tax portion"
msgstr ""

#: Model/OrderRefund.php:validation for field restocking_fees
msgid "Invalid restocking fees"
msgstr ""

#: Model/OrderRefund.php:validation for field fees
msgid "Invalid fees"
msgstr ""

#: Model/OrderRefundProduct.php:validation for field order_refund_id
msgid "Invalid order refund id"
msgstr ""

#: Model/OrderRefundProduct.php:validation for field order_product_id
msgid "Invalid order product id"
msgstr ""

#: Model/OrderTag.php:validation for field order_tag_name_id
msgid "Invalid tag id"
msgstr ""

#: Model/PricingTier.php:validation for field pricingtiername
msgid "You already have a pricing tier with this name"
msgstr ""

#: Model/PricingTiersCollection.php:validation for field pricing_tier_id
#: Model/PricingTiersHiddenWarehouse.php:validation
#: Model/ProductTier.php:validation pricingtierid
msgid "Invalid pricing tier id"
msgstr ""

#: Model/Product.php:validation for field sell_direct
msgid "Invalid sell_direct option"
msgstr ""

#: Model/Product.php:validation for field b2b_min_order_quantity
msgid "Minimum B2B quantity must be a positive integer"
msgstr ""

#: Model/Product.php:validation for field b2b_max_order_quantity
msgid "Maximum B2B quantity must be a positive integer"
msgstr ""

#: Model/ProductCategories.php:validation for field cat_id
msgid "Invalid cat id"
msgstr ""

#: Model/ProductInquiries.php:validation for field comment
msgid "Comment Field can't be empty"
msgstr ""

#: Model/ProductPrice.php:validation for field price
msgid "Invalid price"
msgstr ""

#: Model/ProductPrice.php:validation for field price
msgid "Price cannot be negative"
msgstr ""

#: Model/ProductPrice.php:validation for field compare_at_price
msgid "Invalid base price"
msgstr ""

#: Model/ProductPrice.php:validation for field compare_at_price
msgid "Base price cannot be negative"
msgstr ""

#: Model/ProductRetailer.php:validation for field product_id
#: Model/Store.php:validation productId
msgid "product_id can't be empty"
msgstr ""

#: Model/ProductRetailer.php:validation for field product_id;validation
#: user_id;validation inventory_count Model/Store.php:validation
#: productId;validation storeId;validation inventoryCount
msgid "product_id should be number"
msgstr ""

#: Model/ProductRetailer.php:validation for field user_id
#: Model/Store.php:validation storeId
msgid "user_id can't be empty"
msgstr ""

#: Model/ProductStateFee.php:validation for field fee_product_id
msgid "Invalid fee product id"
msgstr ""

#: Model/ProductStateFee.php:validation for field fee_amount
msgid "Invalid fee amount"
msgstr ""

#: Model/ProductStateFee.php:validation for field fee_amount
msgid "Fee cannot be negative"
msgstr ""

#: Model/ProductStateFee.php:validation for field b2b_fee_amount
msgid "Invalid b2b fee amount"
msgstr ""

#: Model/ProductStateFee.php:validation for field b2b_fee_amount
msgid "B2B fee cannot be negative"
msgstr ""

#: Model/ProductTier.php:validation for field dealer_price
msgid "Invalid B2B price"
msgstr ""

#: Model/ProductTier.php:validation for field dealer_price
msgid "B2B price cannot be negative"
msgstr ""

#: Model/ProductTier.php:validation for field alt_nonstock_dealer_price
msgid "Invalid Ship to Store price"
msgstr ""

#: Model/ProductTier.php:validation for field alt_nonstock_dealer_price
msgid "Ship to Store price cannot be negative"
msgstr ""

#: Model/ProductTier.php:validation for field commission
msgid "Invalid commission"
msgstr ""

#: Model/ProductTier.php:validation for field commission
msgid "Commission cannot be negative"
msgstr ""

#: Model/ProductTitle.php:validation for field source_type
msgid "Invalid source type"
msgstr ""

#: Model/ProductTitle.php:validation for field handle
msgid "Product handle is required"
msgstr ""

#: Model/ProductTitle.php:validation for field handle
msgid "Product handle is a duplicate"
msgstr ""

#: Model/ProductTitle.php:validation for field title
msgid "Product title is required"
msgstr ""

#: Model/ProductVariantOption.php:validation for field variant_option_id
#: Model/VariantOptionValue.php:validation
msgid "Invalid variant option id"
msgstr ""

#: Model/RetailerCredit.php:validation for field credit_date
msgid "Invalid credit_date"
msgstr ""

#: Model/RetailerCredit.php:validation for field due_date
msgid "Invalid due_date"
msgstr ""

#: Model/RetailerCredit.php:validation for field credit_term_id
msgid "Invalid credit term id"
msgstr ""

#: Model/RetailerCreditItem.php:validation for field retailer_credit_id
#: Model/RetailerCreditPayment.php:validation
msgid "Invalid retailer credit id"
msgstr ""

#: Model/RetailerCreditItem.php:validation for field fulfillment_id
msgid "Invalid fulfillment id"
msgstr ""

#: Model/RetailerCreditTerm.php:validation for field credit_term_id
msgid "Invalid Credit Term"
msgstr ""

#: Model/RetailerCreditVoucher.php:validation for field created_by
msgid "Invalid created by"
msgstr ""

#: Model/StaffPermission.php:validation for field staff_id
msgid "Invalid retail staff id"
msgstr ""

#: Model/StripeUserCapability.php:validation for field stripe_user_id
msgid "Invalid stripe user id"
msgstr ""

#: Model/UnitBasedShippingRate.php:validation for field zone_id
#: Model/UserShippingCarrierRate.php:validation
msgid "Invalid zone id"
msgstr ""

#: Model/UnitBasedShippingRate.php:validation for field min
msgid "Invalid min"
msgstr ""

#: Model/UnitBasedShippingRate.php:validation for field max
msgid "Invalid max"
msgstr ""

#: Model/UnitBasedShippingRateCategory.php:validation for field
#: shipping_rate_id
msgid "Invalid shipping rate id"
msgstr ""

#: Model/User.php:validation for field email_address
msgid "Please enter a email address"
msgstr ""

#: Model/User.php:validation for field email_address
msgid "Please enter a valid email address"
msgstr ""

#: Model/User.php:validation for field email_address
msgid "Email already registered"
msgstr ""

#: Model/User.php:validation for field password
msgid "Password should be minimum 8 characters"
msgstr ""

#: Model/User.php:validation for field company_name
msgid "Company name cannot be empty"
msgstr ""

#: Model/User.php:validation for field user_type
msgid "User type is required"
msgstr ""

#: Model/User.php:validation for field user_type
msgid "Invalid user type"
msgstr ""

#: Model/User.php:validation for field paypal_id
msgid "Please enter a paypal ID"
msgstr ""

#: Model/User.php:validation for field paypal_id
msgid "Please enter a valid paypal ID"
msgstr ""

#: Model/User.php:validation for field shop_url
msgid "Please enter a site url"
msgstr ""

#: Model/User.php:validation for field api_key
msgid "Please enter API username / key"
msgstr ""

#: Model/User.php:validation for field secret_key
msgid "Please enter API password / key"
msgstr ""

#: Model/User.php:validation for field address
msgid "Please enter your address"
msgstr ""

#: Model/User.php:validation for field site_type
msgid "Please select site type"
msgstr ""

#: Model/User.php:validation for field site_type
msgid "Site type not supported"
msgstr ""

#: Model/User.php:validation for field country_id
msgid "Please select a country name"
msgstr ""

#: Model/User.php:validation for field state_id
msgid "Please select a state name"
msgstr ""

#: Model/User.php:validation for field city
msgid "Please enter your city name"
msgstr ""

#: Model/User.php:validation for field company_code
msgid "Given company code is already used by other retailer"
msgstr ""

#: Model/User.php:validation for field store_associate_pin
msgid "Pin is already used by another associate"
msgstr ""

#: Model/User.php:validation for field brand_accent_color
msgid "Must be a 6 digit hex color"
msgstr ""

#: Model/UserShippingCarrier.php:validation for field carrier_id
#: Model/UserShippingCarrierRate.php:validation
msgid "Invalid carrier id"
msgstr ""

#: Model/UserShippingCarrierRate.php:validation for field
#: adjustment_rate_percent
msgid "Invalid adjustment rate percent"
msgstr ""

#: Model/UserShippingCarrierRate.php:validation for field adjustment_flat_fee
msgid "Invalid adjustment flat fee"
msgstr ""

#: Model/UserShippingPackage.php:validation for field length
msgid "Invalid length"
msgstr ""

#: Model/UserShippingPackage.php:validation for field width
msgid "Invalid width"
msgstr ""

#: Model/UserShippingPackage.php:validation for field height
msgid "Invalid height"
msgstr ""

#: Model/UserShippingPackage.php:validation for field weight
msgid "Invalid weight"
msgstr ""

#: Model/UserSubdomain.php:validation for field subdomain
msgid "Subdomains must be less than 64 characters"
msgstr ""

#: Model/UserSubdomain.php:validation for field subdomain
msgid "This subdomain is already taken"
msgstr ""

#: Model/VariantOption.php:validation for field product_title_id
msgid "Invalid product title id"
msgstr ""

#: Model/VariantOption.php:validation for field position
msgid "Invalid position"
msgstr ""

#: Model/VariantSortOrder.php:validation for field sort_value
msgid "Invalid sort value"
msgstr ""

#: Model/Warehouse.php:validation for field name
msgid "Cannot exceed 100 characters"
msgstr ""

#: Model/WarehouseDistributor.php:validation for field distributor_id
msgid "Invalid distributor id"
msgstr ""

#: Model/WarehouseProduct.php:validation for field reserved_quantity
msgid "Invalid reserved quantity"
msgstr ""

#: Model/WarehouseProduct.php:validation for field restock_date
msgid "Invalid restock date"
msgstr ""

#: Model/WarehouseProductReservation.php:validation for field
#: warehouse_product_id
msgid "Invalid warehouse product id"
msgstr ""

#: Model/ZoneTaxOverride.php:validation for field tax_rate
msgid "Must be fractional"
msgstr ""

#: Model/ZoneTaxOverrideCollection.php:validation for field
#: zone_tax_override_id
msgid "Invalid zone tax override id"
msgstr ""

#: Model/Behavior/Trait/AppModelValidationsTrait.php:200
msgid "Could not find %s class, unable to complete validation."
msgstr ""

#: Model/Behavior/Trait/AppModelValidationsTrait.php:203
msgid "Method %s does not exist on %s unable to complete validation."
msgstr ""

#~ msgid "Secure Checkout"
#~ msgstr "Pago Seguro"

#~ msgid "can't be blank"
#~ msgstr "no puede ser vacío"

#~ msgid "First name"
#~ msgstr "Nombre"

#~ msgid "Last name"
#~ msgstr "Apellido"

#~ msgid "Purchase Draft Order"
#~ msgstr "Comprar Orden en Borrador"

#~ msgid "LightSpeed Retail (X-Series) Store"
#~ msgstr "Tienda minorista LightSpeed ​​(Serie X)"

#~ msgid "LightSpeed Retail (X-Series) Connect"
#~ msgstr "Conexión LightSpeed ​​Retail (Serie X)"

#~ msgid "Instore Pickup"
#~ msgstr "Retiro en la Tienda"

#~ msgid "Default (1 Year)"
#~ msgstr "Por Defecto (1 año)"

#~ msgid "Select Refund Type"
#~ msgstr "Seleccione el tipo de reembolso"

#~ msgid "Contact Brand"
#~ msgstr "Contacto de Marca"

#~ msgid "Consumer View"
#~ msgstr "Vista de Cliente"

#~ msgid "Retailer View"
#~ msgstr "Vista de Tienda"

#~ msgid "Reserve Inventory"
#~ msgstr "Inventario de reserva"

#~ msgid "Your Shipping Method is"
#~ msgstr "Su Medio de envío es"

#~ msgid "Need To Confirm"
#~ msgstr "Necesito Confirmación"

#~ msgid "Dealer Order"
#~ msgstr "Orden del Distribuidor"

#~ msgid "Not Picked Up"
#~ msgstr "No Retirado"

#~ msgid "Ready for Delivery"
#~ msgstr "Listo para ser Entregado"

#~ msgid "Delivered"
#~ msgstr "Entregado"

#~ msgid "Purchase Order"
#~ msgstr "Orden de Compra"

#~ msgid "Branch Name"
#~ msgstr "Nombre de la Sucursal"

#~ msgid "This location has an active POS"
#~ msgstr "Esta ubicación tiene un POS activo"

#~ msgid "Inventory is reset daily"
#~ msgstr "El Invenario es reiniciado diariamente"

#~ msgid "Value"
#~ msgstr "Valor"

#~ msgid "state"
#~ msgstr "estado"

#~ msgid "province"
#~ msgstr "provincia"

#~ msgid "Base Price"
#~ msgstr "Precio Base"

#~ msgid "Provide a comma-separated list of zip/postal codes inside and<br>only the zip codes inside the above input radius will be included.<br>You can specify partial matches using * as a wildcard."
#~ msgstr "Proporcione una lista separada por comas de códigos postales internos y<br>solo se incluirán los códigos postales dentro del radio de entrada anterior.<br>Puede especificar coincidencias parciales utilizando * como comodín."

#~ msgid "Reserved Inventory"
#~ msgstr "Inventario Reservado"

#~ msgid "Warehouse Inventory"
#~ msgstr "Inventario en Almacén"

#~ msgid "Available Inventory"
#~ msgstr "Inventario Disponible"

#~ msgid "Units Sold"
#~ msgstr "Unidades Vendidas"

#~ msgid "Reserved"
#~ msgstr "Reservado"

#~ msgid "%s ReStock Date (YYYY-MM-DD)"
#~ msgstr "%s Fecha de reabastecimiento (AAAA-MM-DD)"

#~ msgid "Monday"
#~ msgstr "Lunes"

#~ msgid "Tuesday"
#~ msgstr "Martes"

#~ msgid "Wednesday"
#~ msgstr "Miércoles"

#~ msgid "Thursday"
#~ msgstr "Jueves"

#~ msgid "Friday"
#~ msgstr "Viernes"

#~ msgid "Saturday"
#~ msgstr "Sábado"

#~ msgid "Sunday"
#~ msgstr "Domingo"
