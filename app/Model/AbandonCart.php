<?php
App::uses('AppModel', 'Model');
App::uses('CakeTime', 'Utility');
App::uses('AuthComponent', 'Controller/Component');

/**
 * AbandonCart Model.
 *
 * @property User $User
 * @property UserSubdomain $UserSubdomain
 */
class AbandonCart extends AppModel
{
    public $recursive = -1;

    public function initialize(array $config)
    {
        parent::initialize($config);

        $this->addAssociations([
            'belongsTo' => [
                'User',
                'UserSubdomain' => ['foreignKey' => 'user_id'],
            ],
        ]);

        $this->virtualFields['status_name'] = "IF({$this->alias}.status, IF({$this->alias}.preOrderID, 'Sold', 'Emailed'), 'New')";
    }

    public function findAllForEmails(): array
    {
        return (array)$this->find('all', [
            'contain' => [
                'User' => ['fields' => ['id', 'email_address', 'company_name', 'avatar', 'currency_code', 'brand_abandon_cart_subject', 'brand_abandon_cart_message']],
                'UserSubdomain' => ['fields' => ['subdomain']],
            ],
            'conditions' => [
                "{$this->alias}.preOrderID" => null,
                "{$this->alias}.status" => 0,
                "{$this->alias}.created_at <= now() - INTERVAL 180 MINUTE",
                "COALESCE(User.klaviyo_public_key, '')" => '',
            ],
            'fields' => ['id', 'user_id', 'abandon_cart_token', 'email_address', 'preferred_language', 'cart', 'address', 'created_at'],
            'order' => ["{$this->alias}.created_at" => 'ASC'],
        ]);
    }

    /**
     * @param string $abandonCartToken
     * @return array|null
     */
    public function findEmailedCartByToken($abandonCartToken)
    {
        return $this->find('first', [
            'conditions' => [
                'abandon_cart_token' => $abandonCartToken,
                'preOrderID' => null,
            ],
        ]);
    }

    /**
     * @param int $userId
     * @param string $customerEmail
     * @return string
     */
    public function findNewCartIdByCustomer($userId, $customerEmail)
    {
        return $this->field('id', [
            'user_id' => $userId,
            'email_address' => $customerEmail,
            'preOrderId' => null,
            'status' => 0,
        ]);
    }

    public function getAbandonedCarts($userId, $date_range_start = '', $date_range_end = '')
    {
        $conditions = $this->getExportConditions($userId, $date_range_start, $date_range_end);
        $query = $this->_getAbandonedCartQuery($conditions);
        $result = $this->find('all', $query);

        return array_map([$this, '_processJsonFields'], $result);
    }

    public function streamExportData($conditions, callable $callback)
    {
        $query = $this->_getAbandonedCartQuery($conditions);

        return $this->streamPagedQuery($query, function($cart) use ($callback) {
            $cart = $this->_processJsonFields($cart);
            $callback($cart);
        });
    }

    public function getExportConditions($userId, $date_range_start = '', $date_range_end = '')
    {
        $conditions = ['AbandonCart.user_id' => $userId];
        if (!empty($date_range_start) && !empty($date_range_end)) {
            $conditions['AbandonCart.created_at BETWEEN ? AND ?'] = $this->convertDateRange($date_range_start, $date_range_end);
        }

        return $conditions;
    }

    protected function _getAbandonedCartQuery(array $conditions)
    {
        return [
            'fields' => [
                'AbandonCart.id',
                'AbandonCart.user_id',
                'AbandonCart.preOrderID',
                'AbandonCart.status',
                'AbandonCart.status_name',
                'AbandonCart.abandon_cart_token',
                'AbandonCart.email_address',
                'AbandonCart.preferred_language',
                'AbandonCart.cart',
                'AbandonCart.address',
                'AbandonCart.created_at',
            ],
            'conditions' => $conditions,
            'order' => 'AbandonCart.created_at DESC',
        ];
    }

    protected function _processJsonFields(array $cart)
    {
        $cart = $this->_processCartFields($cart);
        $cart = $this->_processAddressFields($cart);
        $cart = $this->_processCreatedAtField($cart);

        return $cart;
    }

    protected function _processCartFields(array $cart)
    {
        if (!empty($cart['AbandonCart']['cart'])) {
            $cart['AbandonCart']['cart'] = json_decode($cart['AbandonCart']['cart'], true);
            foreach (['total_price', 'original_total_price', 'total_discount', 'items_subtotal_price'] as $field) {
                $cart['AbandonCart']['cart'][$field] = number_format($cart['AbandonCart']['cart'][$field] / 100, 2, '.', '');
            }
            $cart['AbandonCart']['cart']['items'] = array_map(function($item) {
                foreach (['price', 'original_price', 'discounted_price', 'line_price', 'original_line_price', 'total_discount', 'final_price', 'final_line_price'] as $field) {
                    $item[$field] = number_format($item[$field] / 100, 2, '.', '');
                }

                return $item;
            }, $cart['AbandonCart']['cart']['items']);
        }

        return $cart;
    }

    protected function _processAddressFields(array $cart)
    {
        if (!empty($cart['AbandonCart']['address'])) {
            $cart['AbandonCart']['address'] = json_decode($cart['AbandonCart']['address'], true);
            $address = $cart['AbandonCart']['address'];
            $cart[0]['customer_name'] = $address['address']['First_name'] . ' ' . $address['address']['Last_name'];
        }

        return $cart;
    }

    protected function _processCreatedAtField(array $cart)
    {
        if (!empty($cart['AbandonCart']['created_at'])) {
            $cart[0]['created_at'] = CakeTime::format(DATETIME_FORMAT, $cart['AbandonCart']['created_at'], false, AuthComponent::user('timezone') ?: null);
        }

        return $cart;
    }

    /**
     * @param int $id
     * @param int $preOrderId
     * @return bool
     */
    public function claimIfExists($id, $preOrderId)
    {
        if (empty($id)) {
            return false;
        }

        return (bool)$this->save([
            'id' => $id,
            'preOrderID' => $preOrderId,
            'status' => 1,
        ]);
    }

    public function findForStartCheckoutEvent(int $abandonCartId): array
    {
        $cart = (array)$this->record($abandonCartId, [
            'contain' => [
                'User' => ['fields' => ['shop_url']],
                'UserSubdomain' => ['fields' => ['subdomain']],
            ],
            'fields' => ['id', 'user_id', 'abandon_cart_token', 'email_address', 'cart', 'address'],
        ]);
        $cart = $this->_processJsonFields($cart);
        $cart = $this->_addTagsAndCollectionsToCartItems($cart);

        if (!empty($cart['AbandonCart']['address']['address']['country'])) {
            $cart['AbandonCart']['address']['address'] = $this->_fixAddressCountryName($cart['AbandonCart']['address']['address']);
        }

        return $cart;
    }

    /**
     * Fix countryName that was set to the country code instead of the country name.
     *
     * @param array $address
     * @return array Address with fixed countryName
     * @see ShopifyController::_processAddressFormFields()
     */
    private function _fixAddressCountryName(array $address): array
    {
        $countryId = (int)($address['country'] ?? 0) ?: null;
        if ($countryId && strlen($address['countryName'] ?? '') <= 2) {
            /** @var Country $Country */
            $Country = ClassRegistry::init('Country');
            $address['countryName'] = $Country->getCountryName($countryId);
        }

        return $address;
    }

    private function _addTagsAndCollectionsToCartItems(array $cart): array
    {
        if (is_array($cart[$this->alias]['cart']['items'] ?? null)) {
            $brandId = $cart[$this->alias]['user_id'];
            $productIDs = array_column($cart[$this->alias]['cart']['items'], 'id');

            /** @var Product $Product */
            $Product = ClassRegistry::init('Product');
            $products = $Product->findAllForAbandonCart($brandId, $productIDs);

            $cart[$this->alias]['cart']['items'] = array_map(fn($item) => array_merge($item, [
                'tags' => array_column($products[$item['id']]['Tag'] ?? [], 'name'),
                'collections' => array_column($products[$item['id']]['Collection'] ?? [], 'title'),
            ]), $cart[$this->alias]['cart']['items']);
        }

        return $cart;
    }
}
