<?php
App::uses('AppModel', 'Model');

/**
 * B2bShippingRateTitle Model.
 *
 * @property B2bShippingRate $B2bShippingRate
 */
class B2bShippingRateTitle extends AppModel
{
    public $validate = [
        'b2b_shipping_rate_id' => [
            'numeric' => ['rule' => 'numeric', 'message' => 'Invalid b2b shipping rate id'],
        ],
        'product_title' => [
            'notBlank' => ['rule' => 'notBlank', 'message' => 'This field cannot be left blank'],
        ],
        'created_at' => [
            'datetime' => ['rule' => 'datetime', 'message' => 'Invalid datetime'],
        ],
    ];

    public $belongsTo = [
        'B2bShippingRate',
    ];

    public $recursive = -1;
}
