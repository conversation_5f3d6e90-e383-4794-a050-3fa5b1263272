<?php

use ShipEarlyApp\Lib\Utility\SupportedLanguages;

App::uses('AppModel', 'Model');
App::uses('TranslateBehaviorTrait', 'Model/Behavior/Trait');

/**
 * B2bShippingZone Model.
 *
 * @property B2bShippingRate $B2bShippingRate
 * @property AppModel $B2bShippingZoneTier
 * @property PricingTier $PricingTier
 * @property User $User
 */
class B2bShippingZone extends AppModel
{
    use TranslateBehaviorTrait;

    public $validate = [
        'user_id' => ['rule' => 'naturalNumber', 'required' => 'create'],
        'name' => ['rule' => 'notBlank', 'required' => 'create'],
    ];

    public $recursive = -1;

    public $actsAs = [
        'Translate' => [
            'className' => 'AppTranslate',
            'joinType' => 'LEFT',
            'free_shipping_message_initial',
            'free_shipping_message_progress',
            'free_shipping_message_success',
        ],
    ];

    public function initialize(array $config)
    {
        parent::initialize($config);

        $this->addAssociations([
            'belongsTo' => [
                'User',
            ],
            'hasMany' => [
                'B2bShippingRate',
                'B2bShippingZoneTier',
            ],
            'belongsToMany' => [
                'PricingTier' => ['with' => 'B2bShippingZoneTier', 'unique' => 'keepExisting'],
            ],
        ]);
    }

    public function findForIndex($userId): array
    {
        return (array)$this->find('all', [
            'contain' => [
                'PricingTier' => [
                    'with' => ['B2bShippingZoneTier' => ['id', 'b2b_shipping_zone_id', 'pricing_tier_id', 'created_at']],
                    'fields' => ['id', 'user_id', 'pricingtiername', 'currencytype'],
                    'order' => ['PricingTier.pricingtiername' => 'ASC'],
                ],
            ],
            'conditions' => ["{$this->alias}.user_id" => $userId],
            'fields' => ['id', 'user_id', 'name'],
            'order' => ["{$this->alias}.name" => 'ASC'],
        ]);
    }

    public function findForEdit($id): array
    {
        $zone = (array)$this->recordWithAllTranslations($id, [
            'contain' => [
                'B2bShippingZoneTier' => ['fields' => ['id', 'b2b_shipping_zone_id', 'pricing_tier_id']],
            ],
            'fields' => [
                'id',
                'user_id',
                'name',
                'free_shipping_unit_based_rate_option',
                'free_shipping_price_based_rate_option',
                'free_shipping_unit_threshold',
                'free_shipping_price_threshold',
                'free_shipping_message_initial',
                'free_shipping_message_progress',
                'free_shipping_message_success',
            ],
        ]);
        if (empty($zone[$this->alias]['id'])) {
            return [];
        }

        $zone[$this->alias]['pricing_tier_ids'] = array_column($zone['B2bShippingZoneTier'], 'pricing_tier_id');
        unset($zone['B2bShippingZoneTier']);

        $zone['B2bShippingRate'] = array_column($this->B2bShippingRate->findForIndex($id), 'B2bShippingRate');

        return $zone;
    }

    public function getFreeShippingRatesAndMessages($userId, $pricingTierId): array
    {
        $results = $this->find('all', [
            'recursive' => -1,
            'joins' => [
                [
                    'table' => 'b2b_shipping_zone_tiers',
                    'alias' => 'B2bShippingZoneTier',
                    'type' => 'INNER',
                    'conditions' => [
                        "B2bShippingZoneTier.b2b_shipping_zone_id" => $this->primaryKeyIdentifier(),
                    ],
                ],
            ],
            'conditions' => [
                "{$this->alias}.user_id" => $userId,
                "B2bShippingZoneTier.pricing_tier_id" => $pricingTierId,
             ],
            'fields' => [
                'id',
                'user_id',
                'name',
                'free_shipping_unit_based_rate_option',
                'free_shipping_price_based_rate_option',
                'free_shipping_unit_threshold',
                'free_shipping_price_threshold',
                'free_shipping_message_initial',
                'free_shipping_message_progress',
                'free_shipping_message_success',
            ],
            'order' => ["{$this->alias}.name" => 'ASC'],
        ]);

        foreach ($results as &$result) {
            $zone = &$result[$this->alias];
            $zoneId = $zone['id'];

            $rates = $this->B2bShippingRate->findAllForFreeShipping($zoneId);
            $result['B2bShippingRate'] = array_column($rates, 'B2bShippingRate');
            $isUnitBased = !empty($zone['free_shipping_unit_based_rate_option']);
            $isPriceBased = !empty($zone['free_shipping_price_based_rate_option']);
            if ($isPriceBased && $isUnitBased) {
                if (empty($zone['free_shipping_message_initial'])) {
                    $zone['free_shipping_message_initial'] = __('Free Shipping on Orders with more than %1$s or %2$s', '{{price}}', '{{units}}');
                }
                if (empty($zone['free_shipping_message_progress'])) {
                    $zone['free_shipping_message_progress'] = __('Only %1$s or %2$s away from Free Shipping', '{{price}}', '{{units}}');
                }
            } elseif ($isPriceBased) {
                if (empty($zone['free_shipping_message_initial'])) {
                    $zone['free_shipping_message_initial'] = __('Free Shipping on Orders over %1$s', '{{price}}');
                }
                if (empty($zone['free_shipping_message_progress'])) {
                    $zone['free_shipping_message_progress'] = __('Only %1$s away from Free Shipping', '{{price}}');
                }
            } elseif ($isUnitBased) {
                if (empty($zone['free_shipping_message_initial'])) {
                    $zone['free_shipping_message_initial'] = __('Free Shipping on Orders over %1$s', '{{units}}');
                }
                if (empty($zone['free_shipping_message_progress'])) {
                    $zone['free_shipping_message_progress'] = __('Only %1$s away from Free Shipping', '{{units}}');
                }
            } else {
                $zone['free_shipping_message_initial'] = '';
                $zone['free_shipping_message_progress'] = '';
                $zone['free_shipping_message_success'] = '';
            }
            if (empty($zone['free_shipping_message_success'])) {
                $zone['free_shipping_message_success'] = __('Congratulations! You’ve Earned Free Shipping');
            }
        }

        return $results;
    }

    public function getFreeShippingBarData($userId, $pricingTierId)
    {
        $freeShippingRatesAndMessages = $this->getFreeShippingRatesAndMessages($userId, $pricingTierId);
        $shippingZone = $freeShippingRatesAndMessages[0]['B2bShippingZone'] ?? null;
        $shippingRates = $freeShippingRatesAndMessages[0]['B2bShippingRate'] ?? [];

        if (empty($shippingZone)) {
            return [
                'unitThreshold' => 0,
                'priceThreshold' => 0.0,
                'unitBasedRateOption' => '',
                'priceBasedRateOption' => '',
                'shippingFilters' => [],
                'freeShippingMessages' => [],
            ];
        }

        $unitThreshold = (int)($shippingZone['free_shipping_unit_threshold'] ?? 0);
        $priceThreshold = (float)($shippingZone['free_shipping_price_threshold'] ?? 0.0);
        $unitBasedRateOption = $shippingZone['free_shipping_unit_based_rate_option'] ?? '';
        $priceBasedRateOption = $shippingZone['free_shipping_price_based_rate_option'] ?? '';

        $shippingFilters = [];
        foreach ($shippingRates as $rate) {
            if ($unitBasedRateOption && $rate['type'] === 'unit') {
                $shippingFilters[] = [
                    'type' => $rate['type'],
                    'product_titles' => $rate['product_titles'] ?? [],
                    'product_categories' => $rate['product_categories'] ?? []
                ];
            }
            if ($priceBasedRateOption && $rate['type'] === 'price') {
                $shippingFilters[] = [
                    'type' => $rate['type'],
                    'product_titles' => $rate['product_titles'] ?? [],
                    'product_categories' => $rate['product_categories'] ?? []
                ];
            }
        }

        $messages = [
            'initial' => $shippingZone['free_shipping_message_initial'] ?? '',
            'progress' => $shippingZone['free_shipping_message_progress'] ?? '',
            'success' => $shippingZone['free_shipping_message_success'] ?? __('Congratulations! You’ve Earned Free Shipping'),
        ];

        return compact(
            'unitThreshold',
            'priceThreshold',
            'unitBasedRateOption',
            'priceBasedRateOption',
            'shippingFilters',
        ) + ['freeShippingMessages' => $messages];
    }

    public function listDisabledTierIds($userId, $id): array
    {
        return (array)$this->B2bShippingZoneTier->find('list', [
            'recursive' => -1,
            'joins' => [
                [
                    'table' => $this->table,
                    'alias' => $this->alias,
                    'type' => 'INNER',
                    'conditions' => [
                        "{$this->alias}.id" => $this->identifier("{$this->B2bShippingZoneTier->alias}.b2b_shipping_zone_id"),
                    ],
                ],
            ],
            'conditions' => [
                "{$this->alias}.id !=" => $id,
                "{$this->alias}.user_id" => $userId,
            ],
            'fields' => ['pricing_tier_id', 'pricing_tier_id'],
            'group' => ["{$this->B2bShippingZoneTier->alias}.pricing_tier_id"],
        ]);
    }

    /**
     * @param int|null $id
     * @param int $userId
     * @param array $data
     * @return bool Success
     */
    public function saveFromEdit($id, $userId, array $data): bool
    {
        $rateCategoryIdMap = [];
        $rateTitleIdMap = [];
        $language = $data['B2bShippingZone']['locale'] ?? SupportedLanguages::DEFAULT_LOCALE;
        if ($id) {
            $rateCategoryIdMap = $this->B2bShippingRate->B2bShippingRateCategory->find('list', [
                'recursive' => -1,
                'conditions' => [
                    'B2bShippingRateCategory.b2b_shipping_rate_id' => array_filter(array_column($data['B2bShippingRate'], 'id')),
                ],
                'fields' => ['product_category', 'id', 'b2b_shipping_rate_id'],
            ]);
            $rateTitleIdMap = $this->B2bShippingRate->B2bShippingRateTitle->find('list', [
                'recursive' => -1,
                'conditions' => [
                    'B2bShippingRateTitle.b2b_shipping_rate_id' => array_filter(array_column($data['B2bShippingRate'], 'id')),
                ],
                'fields' => ['product_title', 'id', 'b2b_shipping_rate_id'],
            ]);
        }
        $this->locale = $language;
        $saved = [
            $this->alias => ['id' => $id, 'user_id' => $userId] + $data[$this->alias],
            'PricingTier' => ['PricingTier' => $data[$this->alias]['pricing_tier_ids']],
            'B2bShippingRate' => array_values(array_map(
                function($rate) use ($id, $rateCategoryIdMap, $rateTitleIdMap) {
                    $rateId = $rate['id'];
                    $rate = ['id' => $rateId, 'b2b_shipping_zone_id' => $id] + $rate;

                    if (!is_numeric($rate['max_range'])) {
                        $rate['max_range'] = null;
                    }

                    $rateCategories = array_map(
                        function($category) use ($rateId, $rateCategoryIdMap) {
                            return [
                                'id' => $rateCategoryIdMap[$rateId][$category] ?? null,
                                'product_category' => $category,
                            ];
                        },
                        json_decode($rate['product_categories'], true)
                    );
                    unset($rate['product_categories']);
                    $rateTitles = array_map(
                        function ($title) use ($rateId, $rateTitleIdMap) {
                            return [
                                'id' => $rateTitleIdMap[$rateId][$title] ?? null,
                                'product_title' => $title,
                            ];
                        },
                        json_decode($rate['product_titles'], true)
                    );

                    return $rate + ['B2bShippingRateCategory' => $rateCategories, 'B2bShippingRateTitle' => $rateTitles];
                },
                $data['B2bShippingRate']
            )),
        ];
        unset($saved[$this->alias]['pricing_tier_ids']);

        $success = true;
        if ($id) {
            $savedRateIds = array_filter(Hash::extract($saved['B2bShippingRate'], '{n}.id'));
            $savedRateCategoryIds = array_filter(Hash::extract($saved['B2bShippingRate'], '{n}.B2bShippingRateCategory.{n}.id'));
            $savedRateTitleIds = array_filter(Hash::extract($saved['B2bShippingRate'], '{n}.B2bShippingRateTitle.{n}.id'));
            $deleted = [
                'B2bShippingRate' => $this->B2bShippingRate->deleteAllJoinless([
                    'B2bShippingRate.b2b_shipping_zone_id' => $id,
                    'B2bShippingRate.id !=' => $savedRateIds,
                ], false),
                'B2bShippingRateCategory' => $this->B2bShippingRate->B2bShippingRateCategory->deleteAllJoinless([
                    'B2bShippingRateCategory.b2b_shipping_rate_id' => $savedRateIds,
                    'B2bShippingRateCategory.id !=' => $savedRateCategoryIds,
                ], false),
                'B2bShippingRateTitle' => $this->B2bShippingRate->B2bShippingRateTitle->deleteAllJoinless([
                    'B2bShippingRateTitle.b2b_shipping_rate_id' => $savedRateIds,
                    'B2bShippingRateTitle.id !=' => $savedRateTitleIds,
                ], false),
            ];
            if (in_array(false, $deleted, true)) {
                CakeLog::error(json_encode(compact('deleted', 'saved')));
                $success = false;
            }
        }

        // See https://book.cakephp.org/2.0/en/models/saving-your-data.html#saving-related-model-data-hasone-hasmany-belongsto
        unset($this->B2bShippingRate->validate['b2b_shipping_zone_id']);

        $success = $this->saveAssociated($saved, ['deep' => true]) && $success;

        return $success;
    }
}
