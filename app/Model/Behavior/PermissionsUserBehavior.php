<?php
App::uses('AppBehavior', 'Model/Behavior');

/**
 * Behavior to handle setting permissions of a user.
 */
class PermissionsUserBehavior extends AppBehavior
{

    const PERMISSIONS_TABLE_ALIAS = 'permissionsTableAlias';
    const PERMISSIONS_PATH_KEY = 'permissionsPath';

    public function setup(Model $model, $config = [])
    {
        parent::setup($model, $config);

        $permissionsTable = Hash::get($config, static::PERMISSIONS_TABLE_ALIAS);
        if ($permissionsTable && empty($model->hasMany[$permissionsTable])) {
            triggerWarning('Invalid PermissionsUserBehavior settings: ' . json_encode($config));
        }
        $config[static::PERMISSIONS_PATH_KEY] = "{$model->alias}.{$permissionsTable}";

        $this->settings[$model->alias] = $config;
    }

    public function mapPermissionsByName(Model $model, array $user): array
    {
        if (!empty($this->getPermissions($model, $user))) {
            $user = $this->setPermissions($model, $user, Hash::combine($this->getPermissions($model, $user), '{n}.name', '{n}'));
        }

        return $user;
    }

    public function mapToExistingPermissionsRecords(Model $model, array $existing, array $data): array
    {
        return array_map(function ($permission) use ($existing, $model) {
            $name = $permission['name'];
            $permissionsPath = $this->getPermissionsPath($model);
            return [
                'id' => Hash::get($existing, "{$permissionsPath}.{$name}.id") ?? null,
                'name' => $name,
                'level' => $permission['level'],
            ];
        }, $this->getPermissions($model, $data) ?? []);
    }

    protected function getPermissions(Model $model, array $data)
    {
        return Hash::get($data, $this->getPermissionsPath($model));
    }

    protected function setPermissions(Model $model, array $data, array $values): array
    {
        $data = Hash::remove($data, $this->getPermissionsPath($model));
        return Hash::insert($data, $this->getPermissionsPath($model), $values);
    }

    protected function getPermissionsPath(Model $model): string
    {
        return $this->settings[$model->alias][static::PERMISSIONS_PATH_KEY];
    }
}
