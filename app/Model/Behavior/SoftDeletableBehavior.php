<?php
App::uses('AppBehavior', 'Model/Behavior');
App::uses('Model', 'Model');

/**
 * Soft Delete Behavior.
 */
class SoftDeletableBehavior extends AppBehavior
{
    public function setup(Model $model, $config = [])
    {
        parent::setup($model, $config);

        $defaults = [
            'deleteField' => 'deleted',
            'additionalUpdates' => [],
        ];
        $this->settings[$model->alias] = array_merge($this->settings[$model->alias] ?? $defaults, (array)$config);

        if (empty($model->deleteField)) {
            $model->deleteField = (string)$this->settings[$model->alias]['deleteField'];
        }
    }

    public function beforeFind(Model $model, $query)
    {
        $query = (array)$query;
        $query['conditions'] = (array)$query['conditions'];

        $query += ['includeDeleted' => $this->_hasPrimaryKeyCondition($model, $query['conditions'])];

        if (!$query['includeDeleted']) {
            $query['conditions'] += ["{$model->alias}.{$this->_getDeleteField($model)}" => false];
        }

        return parent::beforeFind($model, $query);
    }

    protected function _hasPrimaryKeyCondition(Model $model, array $conditions): bool
    {
        $alias = preg_quote($model->alias);
        $key = preg_quote($model->primaryKey);
        $pattern = "/^(?:{$alias}\.)?{$key}$|^{$alias}\.{$key} = | = {$alias}\.{$key}$/";

        foreach ($conditions as $field => $value) {
            if (is_numeric($field)) {
                $field = $value;
            }
            if (is_object($field) && in_array($field->type ?? null, ['expression', 'identifier'], true)) {
                $field = $field->value;
            }
            if (!is_scalar($field)) {
                continue;
            }

            // Remove back-tick quotes and brackets to simplify matching field names
            $field = str_replace(['`', '(', ')'], '', (string)$field);

            if (preg_match($pattern, $field)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Returns conditions for filtering out soft deleted records.
     *
     * @param Model $model
     * @param array $conditions
     * @return array $conditions with additional conditions
     */
    public function getConditionsForNonDeleted(Model $model, array $conditions = []): array
    {
        return array_merge($conditions, ["{$model->alias}.{$this->_getDeleteField($model)}" => false]);
    }

    /**
     * Removes record for given ID. If no ID is given, the current ID is used. Returns true on success.
     *
     * @param Model $model
     * @param int|string $id ID of record to delete
     * @return bool True on success
     */
    public function softDelete(Model $model, $id): bool
    {
        if (!empty($id)) {
            $model->id = $id;
        }

        $id = $model->getID();

        if (!$model->exists(["{$model->alias}.{$model->primaryKey}" => $id])) {
            return false;
        }

        if (!$this->softDeleteAll($model, ["{$model->alias}.{$model->primaryKey}" => $id])) {
            return false;
        }

        $model->id = false;

        return true;
    }

    /**
     * Deletes multiple model records based on a set of conditions.
     *
     * @param Model $model
     * @param mixed $conditions Conditions to match
     * @return bool True on success, false on failure
     */
    public function softDeleteAll(Model $model, $conditions): bool
    {
        if (empty($conditions)) {
            return false;
        }

        $fields = array_merge(
            (array)$this->settings[$model->alias]['additionalUpdates'],
            ["{$model->alias}.{$this->_getDeleteField($model)}" => 'TRUE']
        );

        return method_exists($model, 'updateAllJoinless')
            ? (bool)$model->updateAllJoinless($fields, $conditions)
            : (bool)$model->updateAll($fields, $conditions);
    }

    protected function _getDeleteField(Model $model): string
    {
        return (string)(!empty($model->deleteField) ? $model->deleteField : $this->settings[$model->alias]['deleteField']);
    }
}
