<?php

/**
 * Trait RangeColumnGroupValidatorBehavior
 *
 * Explicitly defines magic methods that RangeColumnGroupValidatorBehavior adds to a Model.
 *
 * @see RangeColumnGroupValidatorBehavior
 */
trait RangeColumnGroupValidatorBehaviorTrait
{
    /**
     * Validates a range column group
     * @param array $check 
     * @param string $rangeName 
     * @return bool 
     * 
     * @see RangeColumnGroupValidatorBehavior::validateRange
     */
    public function rangeColumnGroup(array $check, string $rangeName): bool
    {
        $result = $this->Behaviors->dispatchMethod($this, 'rangeColumnGroup', func_get_args());
        if ($result === ['unhandled']) {
            return false;
        }

        return (bool)$result;
    }
    /**
     * Validates a given range, fetching upper or lower values if needed
     * @param mixed $recordId 
     * @param mixed $lowerLimit 
     * @param mixed $lowerLimitColumn 
     * @param mixed $upperLimit 
     * @param mixed $upperLimitColumn 
     * @return bool true if uppLimit is greater than or equal to lowerLimit
     */
    public function validateRange($recordId, $lowerLimit, $lowerLimitColumn, $upperLimit, $upperLimitColumn)
    {
        $result = $this->Behaviors->dispatchMethod($this, 'validateRange', func_get_args());
        if ($result === ['unhandled']) {
            return false;
        }

        return (bool)$result;
    }
}
