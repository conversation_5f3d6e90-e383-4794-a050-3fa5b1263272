<?php
App::uses('AppModel', 'Model');

/**
 * BrandStaffRole Model.
 *
 * @property User $BrandUser
 * @property BrandStaff $BrandStaff
 */
class BrandStaffRole extends AppModel
{
    public $displayField = 'name';

    public $validate = [
        'brand_id' => ['rule' => 'naturalNumber', 'message' => 'Invalid id'],
        'name' => ['rule' => 'notBlank', 'required' => 'create'],
        'created_at' => ['rule' => 'datetime', 'message' => 'Invalid datetime'],
        'updated_at' => ['rule' => 'datetime', 'message' => 'Invalid datetime'],
    ];

    public $belongsTo = [
        'BrandUser' => ['className' => 'User', 'foreignKey' => 'brand_id'],
    ];

    public $hasMany = [
        'BrandStaff' => ['foreignKey' => 'role_id'],
    ];

    public $recursive = -1;

    public function listOptions($brandId): array
    {
        return (array)$this->find('list', [
            'conditions' => ["{$this->alias}.brand_id" => $brandId],
            'fields' => ["{$this->alias}.id", "{$this->alias}.name"],
            'order' => ["{$this->alias}.name" => 'ASC'],
        ]);
    }

    public function saveByName($brandId, $name): ?string
    {
        $success = (bool)$this->save([
            'id' => $this->field('id', ['brand_id' => $brandId, 'name' => $name]),
            'brand_id' => $brandId,
            'name' => $name,
        ]);

        return $success ? (string)$this->id : null;
    }

    public function deleteAllOrphansWithBrandId(int $brandId): bool
    {
        return $this->deleteAllOrphans(["{$this->alias}.brand_id" => $brandId]);
    }
}
