<?php
App::uses('AppModel', 'Model');

/**
 * Cart Model.
 *
 * @property User $User
 * @property ApiClient $ApiClient
 * @property User $Retailer
 * @property OrderAddress $ShippingAddress
 * @property OrderAddress $BillingAddress
 * @property CartItem $CartItem
 */
class Cart extends AppModel
{
    public $validate = [
        'uuid' => [
            'uuid' => ['rule' => 'uuid', 'allowEmpty' => true, 'message' => 'Invalid uuid'],
        ],
        'user_id' => [
            'naturalNumber' => ['rule' => 'naturalNumber', 'message' => 'Invalid user id'],
        ],
        'client_id' => [
            'notBlank' => ['rule' => 'notBlank', 'allowEmpty' => true, 'message' => 'This field cannot be left blank'],
        ],
        'retailer_id' => [
            'naturalNumber' => ['rule' => 'naturalNumber', 'allowEmpty' => true, 'message' => 'Invalid retailer id'],
        ],
        'shipping_address_id' => [
            'naturalNumber' => ['rule' => 'naturalNumber', 'allowEmpty' => true, 'message' => 'Invalid shipping address id'],
        ],
        'billing_address_id' => [
            'naturalNumber' => ['rule' => 'naturalNumber', 'allowEmpty' => true, 'message' => 'Invalid billing address id'],
        ],
        'delivery_method' => [
            'inList' => ['rule' => 'inList', 'allowEmpty' => true, 'values' => ['in_store', 'local_delivery', 'local_install', 'ship_from_store', 'sell_direct'], 'message' => 'Invalid delivery method'],
        ],
        'email_address' => [
            'email' => ['rule' => 'email', 'allowEmpty' => true, 'message' => 'Invalid email address'],
        ],
        'accepts_marketing' => [
            'boolean' => ['rule' => 'boolean', 'message' => 'Invalid boolean'],
        ],
        'preferred_language' => [
            'notBlank' => ['rule' => 'notBlank', 'allowEmpty' => true, 'message' => 'This field cannot be left blank'],
        ],
        'currency_code' => [
            'notBlank' => ['rule' => 'notBlank', 'message' => 'This field cannot be left blank'],
        ],
        'shipping_amount' => [
            'decimal' => ['rule' => 'decimal', 'allowEmpty' => true, 'message' => 'Invalid shipping amount'],
            'positive' => ['rule' => ['comparison', '>=', 0], 'message' => 'Shipping amount cannot be negative'],
        ],
        'shipping_tax_rate' => [
            'decimal' => ['rule' => 'decimal', 'allowEmpty' => true, 'message' => 'Invalid shipping tax rate'],
            'positive' => ['rule' => ['comparison', '>=', 0], 'message' => 'Shipping tax rate cannot be negative'],
        ],
        'status' => [
            'inList' => ['rule' => 'inList', 'values' => ['open', 'abandoned', 'closed'], 'message' => 'Invalid status'],
        ],
        'created_at' => [
            'datetime' => ['rule' => 'datetime', 'message' => 'Invalid datetime'],
        ],
        'updated_at' => [
            'datetime' => ['rule' => 'datetime', 'message' => 'Invalid datetime'],
        ],
    ];

    public $belongsTo = [
        'User',
        'ApiClient' => ['foreignKey' => 'client_id'],
        'Retailer' => ['className' => 'User'],
        'ShippingAddress' => ['className' => 'OrderAddress'],
        'BillingAddress' => ['className' => 'OrderAddress'],
    ];

    public $hasMany = [
        'CartItem',
    ];

    public $recursive = -1;

    public function existsByUuid(string $uuid): bool
    {
        return $this->exists(["{$this->alias}.uuid" => $uuid]);
    }

    public function getByUuid(string $uuid, array $options = []): array
    {
        return (array)$this->get(['uuid', $uuid], $options);
    }

    public function recordByUuid(string $uuid, array $options = []): array
    {
        return (array)$this->record(['uuid', $uuid], $options);
    }

    public function deleteByUuid(string $uuid, $dependent = true, $callbacks = false): bool
    {
        return $this->deleteAllJoinless(["{$this->alias}.uuid" => $uuid], $dependent, $callbacks);
    }

    public function getForWidgetsView(int $userId, string $uuid): array
    {
        $cart = $this->getByUuid($uuid, [
            'contain' => [
                'CartItem' => [
                    'fields' => ['id', 'cart_id', 'product_id', 'quantity'],
                ],
            ],
            'conditions' => [
                "{$this->alias}.user_id" => $userId,
                "{$this->alias}.status !=" => 'closed',
            ],
            'fields' => ['id', 'uuid', 'retailer_id', 'currency_code'],
        ]);

        $currencyCode = (string)$cart[$this->alias]['currency_code'];
        $retailerId = (int)($cart[$this->alias]['retailer_id'] ?? 0) ?: null;
        $quantityById = array_column((array)$cart['CartItem'], 'quantity', 'product_id');
        $products = $this->CartItem->Product->findAllForShoppingCartWidget($userId, $currencyCode, $retailerId, $quantityById);

        $cartItemIds = array_column((array)$cart['CartItem'], 'id', 'product_id');
        $cart['CartItem'] = array_map(fn(array $product): array => [
            'id' => $cartItemIds[$product['id']],
            'cart_id' => $cart[$this->alias]['id'],
            'product_id' => $product['id'],
            'quantity' => $product['quantity'],
            'Product' => $product,
        ], $products);

        return $cart;
    }

    public function getForWidgetsEdit(int $userId, string $uuid): array
    {
        return $this->getByUuid($uuid, [
            'contain' => [
                'CartItem' => [
                    'fields' => ['id', 'cart_id', 'product_id', 'quantity'],
                    'order' => false,
                ],
            ],
            'conditions' => [
                "{$this->alias}.user_id" => $userId,
                "{$this->alias}.status !=" => 'closed',
            ],
            'fields' => ['id', 'currency_code'],
        ]);
    }

    public function saveFromWidgetsEdit(array $cart, array $items, array $removedItems): ?string
    {
        $fieldList = [
            $this->alias => [
                'id',
                'uuid', // Added by beforeSave
                'user_id',
                'client_id',
                'retailer_id',
                'shipping_address_id',
                'billing_address_id',
                'delivery_method',
                'email_address',
                'accepts_marketing',
                'preferred_language',
                'currency_code',
                'shipping_amount',
                'shipping_tax_rate',
                'status',
                'created_at',
                'updated_at',
            ],
            'CartItem' => [
                'id',
                'product_id',
                'quantity',
                'price',
                'compare_at_price',
                'total_discount',
                'tax_rate',
            ],
        ];

        $success = $this->doInTransaction(fn(): bool => (
            $this->saveAssociated([$this->alias => $cart, 'CartItem' => $items], ['fieldList' => $fieldList])
            && (!$removedItems || $this->CartItem->deleteAllJoinless(['CartItem.id' => array_column($removedItems, 'id')], false))
        ));
        if (!$success) {
            return null;
        }

        return (string)$this->fieldByConditions('uuid', ["{$this->alias}.{$this->primaryKey}" => $this->id]);
    }
}
