<?php
App::uses('AppModel', 'Model');

/**
 * CartItem Model.
 *
 * @property Cart $Cart
 * @property Product $Product
 */
class CartItem extends AppModel
{
    public $validate = [
        'cart_id' => [
            'naturalNumber' => ['rule' => 'naturalNumber', 'message' => 'Invalid cart id'],
        ],
        'product_id' => [
            'naturalNumber' => ['rule' => 'naturalNumber', 'message' => 'Invalid product id'],
        ],
        'quantity' => [
            'naturalNumber' => ['rule' => 'naturalNumber', 'required' => 'create', 'message' => 'Invalid quantity'],
        ],
        'price' => [
            'decimal' => ['rule' => 'decimal', 'message' => 'Invalid price'],
            'positive' => ['rule' => ['comparison', '>=', 0], 'message' => 'Price cannot be negative'],
        ],
        'compare_at_price' => [
            'decimal' => ['rule' => 'decimal', 'allowEmpty' => true, 'message' => 'Invalid compare at price'],
            'positive' => ['rule' => ['comparison', '>=', 0], 'message' => 'Base price cannot be negative'],
        ],
        'total_discount' => [
            'decimal' => ['rule' => 'decimal', 'message' => 'Invalid total discount'],
            'positive' => ['rule' => ['comparison', '>=', 0], 'message' => 'Total discount cannot be negative'],
        ],
        'tax_rate' => [
            'decimal' => ['rule' => 'decimal', 'message' => 'Invalid tax rate'],
            'positive' => ['rule' => ['comparison', '>=', 0], 'message' => 'Tax rate cannot be negative'],
        ],
        'created_at' => [
            'datetime' => ['rule' => 'datetime', 'message' => 'Invalid datetime'],
        ],
        'updated_at' => [
            'datetime' => ['rule' => 'datetime', 'message' => 'Invalid datetime'],
        ],
    ];

    public $belongsTo = [
        'Cart',
        'Product',
    ];

    public $recursive = -1;
}
