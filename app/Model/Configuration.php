<?php
App::uses('AppModel', 'Model');

/**
 * Configuration Model.
 */
class Configuration extends AppModel
{
    public $displayField = 'value';

    public $primaryKey = 'name';

    public $validate = [
        'name' => [
            'notBlank' => ['rule' => 'notBlank', 'message' => 'This field cannot be left blank'],
        ],
        'value' => [
            'notBlank' => ['rule' => 'notBlank', 'allowEmpty' => true, 'message' => 'This field cannot be left blank'],
        ],
    ];

    public $recursive = -1;

    /**
     * @var string
     */
    public $cacheConfig = 'verylong';

    /**
     * Whether the bootstrap method has been called.
     *
     * @var bool
     */
    public $bootstrapped = false;

    protected function _clearCache($type = null)
    {
        Cache::delete('list_configurations', $this->cacheConfig);

        return parent::_clearCache($type);
    }

    /**
     * Load and define configuration constants.
     *
     * @return void
     */
    public function bootstrap(): void
    {
        if ($this->bootstrapped) {
            return;
        }

        $this->defineConstants($this->listConfigurations());
        $this->bootstrapped = true;
    }

    /**
     * Assign config name-value pairs to configuration constants used in the app.
     *
     * @param string[] $configs Name-value pairs to attempt defining as constants
     * @return string[] Name-value pairs of the actual defined constants
     */
    protected function defineConstants(array $configs): array
    {
        $constants = [];

        // Deliberately calling define() with literals for better IDE tracing
        $name = 'AFTERSHIP_KEY';
        if (!defined($name)) {
            define('AFTERSHIP_KEY', $configs[$name] ?? '');
        }
        $constants[$name] = AFTERSHIP_KEY;

        $name = 'AVALARA_ACCOUNTNUMBER';
        if (!defined($name)) {
            define('AVALARA_ACCOUNTNUMBER', $configs[$name] ?? '');
        }
        $constants[$name] = AVALARA_ACCOUNTNUMBER;

        $name = 'AVALARA_LICENSEKEY';
        if (!defined($name)) {
            define('AVALARA_LICENSEKEY', $configs[$name] ?? '');
        }
        $constants[$name] = AVALARA_LICENSEKEY;

        $name = 'PAYPAL_API_PASSWORD';
        if (!defined($name)) {
            define('PAYPAL_API_PASSWORD', $configs[$name] ?? '');
        }
        $constants[$name] = PAYPAL_API_PASSWORD;

        $name = 'PAYPAL_API_SIGNATURE';
        if (!defined($name)) {
            define('PAYPAL_API_SIGNATURE', $configs[$name] ?? '');
        }
        $constants[$name] = PAYPAL_API_SIGNATURE;

        $name = 'PAYPAL_API_USERNAME';
        if (!defined($name)) {
            define('PAYPAL_API_USERNAME', $configs[$name] ?? '');
        }
        $constants[$name] = PAYPAL_API_USERNAME;

        $name = 'PAYPAL_APPID';
        if (!defined($name)) {
            define('PAYPAL_APPID', $configs[$name] ?? '');
        }
        $constants[$name] = PAYPAL_APPID;

        $name = 'PAYPAL_MASS_LIMIT';
        if (!defined($name)) {
            define('PAYPAL_MASS_LIMIT', $configs[$name] ?? '');
        }
        $constants[$name] = PAYPAL_MASS_LIMIT;

        $name = 'PAYPAL_SANDBOX';
        if (!defined($name)) {
            define('PAYPAL_SANDBOX', $configs[$name] ?? '');
        }
        $constants[$name] = PAYPAL_SANDBOX;

        $name = 'SHIPEARLY_MANUFACTURE_DEFAULT_AMOUNT';
        if (!defined($name)) {
            define('SHIPEARLY_MANUFACTURE_DEFAULT_AMOUNT', $configs[$name] ?? '');
        }
        $constants[$name] = SHIPEARLY_MANUFACTURE_DEFAULT_AMOUNT;

        $name = 'SHIPEARLY_MANUFACTURE_PERCENTAGE';
        if (!defined($name)) {
            define('SHIPEARLY_MANUFACTURE_PERCENTAGE', $configs[$name] ?? '');
        }
        $constants[$name] = SHIPEARLY_MANUFACTURE_PERCENTAGE;

        $name = 'SHIPEARLY_RETAILER_DEFAULT_AMOUNT';
        if (!defined($name)) {
            define('SHIPEARLY_RETAILER_DEFAULT_AMOUNT', $configs[$name] ?? '');
        }
        $constants[$name] = SHIPEARLY_RETAILER_DEFAULT_AMOUNT;

        $name = 'SHIPEARLY_RETAILER_PERCENTAGE';
        if (!defined($name)) {
            define('SHIPEARLY_RETAILER_PERCENTAGE', $configs[$name] ?? '');
        }
        $constants[$name] = SHIPEARLY_RETAILER_PERCENTAGE;

        $name = 'IN_STORE_RADIUS';
        if (!defined($name)) {
            define('IN_STORE_RADIUS', $configs[$name] ?? '');
        }
        $constants[$name] = IN_STORE_RADIUS;

        $name = 'SHIP_FROM_STORE_RADIUS';
        if (!defined($name)) {
            define('SHIP_FROM_STORE_RADIUS', $configs[$name] ?? '');
        }
        $constants[$name] = SHIP_FROM_STORE_RADIUS;

        $name = 'STRIPE_SECRET_KEY';
        if (!defined($name)) {
            define('STRIPE_SECRET_KEY', $configs[$name] ?? '');
        }
        $constants[$name] = STRIPE_SECRET_KEY;

        $name = 'STRIPE_PUBLISHABLE_KEY';
        if (!defined($name)) {
            define('STRIPE_PUBLISHABLE_KEY', $configs[$name] ?? '');
        }
        $constants[$name] = STRIPE_PUBLISHABLE_KEY;

        $name = 'STRIPE_CLIENT_ID';
        if (!defined($name)) {
            define('STRIPE_CLIENT_ID', $configs[$name] ?? '');
        }
        $constants[$name] = STRIPE_CLIENT_ID;

        $name = 'LIGHTSPEED_CLIENT_ID';
        if (!defined($name)) {
            define('LIGHTSPEED_CLIENT_ID', $configs[$name] ?? '');
        }
        $constants[$name] = LIGHTSPEED_CLIENT_ID;

        $name = 'LIGHTSPEED_CLIENT_SECRET';
        if (!defined($name)) {
            define('LIGHTSPEED_CLIENT_SECRET', $configs[$name] ?? '');
        }
        $constants[$name] = LIGHTSPEED_CLIENT_SECRET;

        $name = 'SHIPEARLY_BRAND_DIRECT_PERCENTAGE';
        if (!defined($name)) {
            define('SHIPEARLY_BRAND_DIRECT_PERCENTAGE', $configs[$name] ?? '');
        }
        $constants[$name] = SHIPEARLY_BRAND_DIRECT_PERCENTAGE;

        $name = 'SHIPEARLY_BRAND_DIRECT_DEFAULT_AMOUNT';
        if (!defined($name)) {
            define('SHIPEARLY_BRAND_DIRECT_DEFAULT_AMOUNT', $configs[$name] ?? '');
        }
        $constants[$name] = SHIPEARLY_BRAND_DIRECT_DEFAULT_AMOUNT;

        $name = 'VENDPOS_CLIENT_ID';
        if (!defined($name)) {
            define('VENDPOS_CLIENT_ID', $configs[$name] ?? '');
        }
        $constants[$name] = VENDPOS_CLIENT_ID;

        $name = 'VENDPOS_CLIENT_SECRET';
        if (!defined($name)) {
            define('VENDPOS_CLIENT_SECRET', $configs[$name] ?? '');
        }
        $constants[$name] = VENDPOS_CLIENT_SECRET;

        $name = 'DB_VERSION';
        if (!defined($name)) {
            define('DB_VERSION', $configs[$name] ?? '');
        }
        $constants[$name] = DB_VERSION;

        $name = 'LIGHTSPEED_REQUEST_LIMIT';
        if (!defined($name)) {
            define('LIGHTSPEED_REQUEST_LIMIT', $configs[$name] ?? '');
        }
        $constants[$name] = LIGHTSPEED_REQUEST_LIMIT;

        $name = 'SHIPEARLY_RETAILER_REFUND_PERIOD';
        if (!defined($name)) {
            define('SHIPEARLY_RETAILER_REFUND_PERIOD', $configs[$name] ?? '30 DAYS');
        }
        $constants[$name] = SHIPEARLY_RETAILER_REFUND_PERIOD;

        $name = 'SQUARE_APPLICATION_ID';
        if (!defined($name)) {
            define('SQUARE_APPLICATION_ID', $configs[$name] ?? '');
        }
        $constants[$name] = SQUARE_APPLICATION_ID;

        $name = 'SQUARE_APPLICATION_SECRET';
        if (!defined($name)) {
            define('SQUARE_APPLICATION_SECRET', $configs[$name] ?? '');
        }
        $constants[$name] = SQUARE_APPLICATION_SECRET;

        $name = 'SHIPPO_PRIVATE_AUTH_TOKEN';
        if (!defined($name)) {
            define('SHIPPO_PRIVATE_AUTH_TOKEN', $configs[$name] ?? '');
        }
        $constants[$name] = SHIPPO_PRIVATE_AUTH_TOKEN;

        $name = 'SHIPPING_PACKAGE_LENGTH';
        if (!defined($name)) {
            define('SHIPPING_PACKAGE_LENGTH', $configs[$name] ?? '');
        }
        $constants[$name] = SHIPPING_PACKAGE_LENGTH;

        $name = 'SHIPPING_PACKAGE_WIDTH';
        if (!defined($name)) {
            define('SHIPPING_PACKAGE_WIDTH', $configs[$name] ?? '');
        }
        $constants[$name] = SHIPPING_PACKAGE_WIDTH;

        $name = 'SHIPPING_PACKAGE_HEIGHT';
        if (!defined($name)) {
            define('SHIPPING_PACKAGE_HEIGHT', $configs[$name] ?? '');
        }
        $constants[$name] = SHIPPING_PACKAGE_HEIGHT;

        $name = 'SHIPPING_PACKAGE_DIMENSION_UNIT';
        if (!defined($name)) {
            define('SHIPPING_PACKAGE_DIMENSION_UNIT', $configs[$name] ?? '');
        }
        $constants[$name] = SHIPPING_PACKAGE_DIMENSION_UNIT;

        $name = 'SHIPPING_PACKAGE_WEIGHT_EMPTY';
        if (!defined($name)) {
            define('SHIPPING_PACKAGE_WEIGHT_EMPTY', $configs[$name] ?? '');
        }
        $constants[$name] = SHIPPING_PACKAGE_WEIGHT_EMPTY;

        $name = 'SHIPPING_PACKAGE_WEIGHT_UNIT';
        if (!defined($name)) {
            define('SHIPPING_PACKAGE_WEIGHT_UNIT', $configs[$name] ?? '');
        }
        $constants[$name] = SHIPPING_PACKAGE_WEIGHT_UNIT;

        $name = 'USPS_API_USERNAME';
        if (!defined($name)) {
            define('USPS_API_USERNAME', $configs[$name] ?? '');
        }
        $constants[$name] = USPS_API_USERNAME;

        $name = 'USPS_API_PASSWORD';
        if (!defined($name)) {
            define('USPS_API_PASSWORD', $configs[$name] ?? '');
        }
        $constants[$name] = USPS_API_PASSWORD;

        $name = 'COURIER_MAX_WEIGHT';
        if (!defined($name)) {
            define('COURIER_MAX_WEIGHT', $configs[$name] ?? '');
        }
        $constants[$name] = COURIER_MAX_WEIGHT;

        $name = 'SHIPPING_CARRIER_LIMIT';
        if (!defined($name)) {
            define('SHIPPING_CARRIER_LIMIT', $configs[$name] ?? '');
        }
        $constants[$name] = SHIPPING_CARRIER_LIMIT;

        $name = 'GOOGLE_GEOCODING_API_KEY';
        if (!defined($name)) {
            define('GOOGLE_GEOCODING_API_KEY', $configs[$name] ?? '');
        }
        $constants[$name] = GOOGLE_GEOCODING_API_KEY;

        $name = 'RETAILER_DASHBOARD_QUICKSTART_VIDEO_URL';
        if (!defined($name)) {
            define('RETAILER_DASHBOARD_QUICKSTART_VIDEO_URL', $configs[$name] ?? '');
        }
        $constants[$name] = RETAILER_DASHBOARD_QUICKSTART_VIDEO_URL;

        $name = 'PAYMENT_RAILS_ACCESS_KEY';
        if (!defined($name)) {
            define('PAYMENT_RAILS_ACCESS_KEY', $configs[$name] ?? '');
        }
        $constants[$name] = PAYMENT_RAILS_ACCESS_KEY;

        $name = 'PAYMENT_RAILS_SECRET_KEY';
        if (!defined($name)) {
            define('PAYMENT_RAILS_SECRET_KEY', $configs[$name] ?? '');
        }
        $constants[$name] = PAYMENT_RAILS_SECRET_KEY;

        $name = 'MANUFACTURER_HELP_URL';
        if (!defined($name)) {
            define('MANUFACTURER_HELP_URL', $configs[$name] ?? '');
        }
        $constants[$name] = MANUFACTURER_HELP_URL;

        $name = 'RETAILER_HELP_URL';
        if (!defined($name)) {
            define('RETAILER_HELP_URL', $configs[$name] ?? '');
        }
        $constants[$name] = RETAILER_HELP_URL;

        $name = 'SHIPEARLY_LOGO_URL';
        if (!defined($name)) {
            define('SHIPEARLY_LOGO_URL', $configs[$name] ?? '');
        }
        $constants[$name] = SHIPEARLY_LOGO_URL;

        $name = 'TAXJAR_API_KEY';
        if (!defined($name)) {
            define('TAXJAR_API_KEY', $configs[$name] ?? '********************************');
        }
        $constants[$name] = TAXJAR_API_KEY;

        $name = 'TAXJAR_API_SANDBOX';
        if (!defined($name)) {
            define('TAXJAR_API_SANDBOX', $configs[$name] ?? 'true');
        }
        $constants[$name] = TAXJAR_API_SANDBOX;

        $name = 'CHECKOUT_ACCESS_LIMIT';
        if (!defined($name)) {
            define('CHECKOUT_ACCESS_LIMIT', $configs[$name] ?? '');
        }
        $constants[$name] = CHECKOUT_ACCESS_LIMIT;

        $name = 'CHECKOUT_ACCESS_EXPIRES';
        if (!defined($name)) {
            define('CHECKOUT_ACCESS_EXPIRES', $configs[$name] ?? '15 minutes');
        }
        $constants[$name] = CHECKOUT_ACCESS_EXPIRES;

        $name = 'IPINFO_ACCESS_TOKEN';
        if (!defined($name)) {
            define('IPINFO_ACCESS_TOKEN', $configs[$name] ?? '');
        }
        $constants[$name] = IPINFO_ACCESS_TOKEN;

        $name = 'BTASK_BATCH_SIZE';
        if (!defined($name)) {
            define('BTASK_BATCH_SIZE', $configs[$name] ?? '200');
        }
        $constants[$name] = BTASK_BATCH_SIZE;

        $name = 'INVENTORY_EMAIL';
        if (!defined($name)) {
            define('INVENTORY_EMAIL', $configs[$name] ?? '<EMAIL>');
        }
        $constants[$name] = INVENTORY_EMAIL;

        $name = 'INVENTORY_EMAIL_ALIAS';
        if (!defined($name)) {
            define('INVENTORY_EMAIL_ALIAS', $configs[$name] ?? '');
        }
        $constants[$name] = INVENTORY_EMAIL_ALIAS;

        $name = 'INVENTORY_EMAIL_PASS';
        if (!defined($name)) {
            define('INVENTORY_EMAIL_PASS', $configs[$name] ?? '');
        }
        $constants[$name] = INVENTORY_EMAIL_PASS;

        return $constants;
    }

    /**
     * @return string[] Name-value pairs of configuration constants
     */
    public function listConstants(): array
    {
        $this->bootstrap();

        return $this->defineConstants([]);
    }

    /**
     * @return string[] Name-value pairs of configuration values
     */
    public function listConfigurations(): array
    {
        $callback = function() {
            return $this->find('list') ?: false;
        };

        return (array)(Cache::remember('list_configurations', $callback, $this->cacheConfig) ?: []);
    }

    /**
     * @return array configuration constants as records
     */
    public function getAllConstantsAsRecords(): array
    {
        $fakeRecords = [];

        $constants = $this->listConstants();
        $id = 0;
        foreach ($constants as $name => $value) {
            $id++;
            $fakeRecords[] = [$this->alias => compact('id', 'name', 'value')];
        }

        return $fakeRecords;
    }

    /**
     * Update a single configuration value.
     *
     * @param string $name
     * @param string $value
     * @return bool
     * @throws Exception
     */
    public function updateValue(string $name, $value): bool
    {
        return (bool)$this->save(compact('name', 'value'));
    }

    /**
     * Update all configuration values.
     *
     * @param array $data
     * @return bool
     */
    public function updateConfiguration(array $data)
    {
        $success = (bool)$this->saveMany($data);

        if ($success) {
            $this->deleteAllRemovedConfigs();
        }

        return $success;
    }

    public function deleteAllRemovedConfigs()
    {
        $configsToDelete = array_diff_key($this->listConfigurations(), $this->listConstants());
        if ($configsToDelete) {
            $this->deleteAllJoinless(["{$this->alias}.name" => array_keys($configsToDelete)], false, true);
        }
    }
}
