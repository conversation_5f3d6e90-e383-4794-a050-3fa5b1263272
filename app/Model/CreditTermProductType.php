<?php
App::uses('AppModel', 'Model');

/**
 * CreditTermProductType Model.
 *
 * @property CreditTerm $CreditTerm
 */
class CreditTermProductType extends AppModel
{
    public $validate = [
        'credit_term_id' => [
            'numeric' => ['rule' => 'numeric', 'message' => 'Invalid credit term id'],
        ],
        'product_type' => [
            'notBlank' => ['rule' => 'notBlank', 'message' => 'This field cannot be left blank'],
        ],
    ];

    public $belongsTo = [
        'CreditTerm',
    ];

    public $recursive = -1;
}
