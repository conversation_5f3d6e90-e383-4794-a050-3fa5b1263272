<?php
App::uses('AppModel', 'Model');

/**
 * DealerOrderRefund Model.
 *
 * @property DealerOrder $DealerOrder
 * @property DealerOrderRefundProduct $DealerOrderRefundProduct
 */
class DealerOrderRefund extends AppModel
{
    public $validate = [
        'dealer_order_id' => [
            'naturalNumber' => ['rule' => 'naturalNumber', 'message' => 'Invalid dealer order id'],
        ],
        'source_id' => [
            'notBlank' => ['rule' => 'notBlank', 'allowEmpty' => true, 'message' => 'This field cannot be left blank'],
        ],
        'transaction_id' => [
            'notBlank' => ['rule' => 'notBlank', 'allowEmpty' => true, 'message' => 'This field cannot be left blank'],
        ],
        'amount' => [
            'decimal' => ['rule' => 'decimal', 'message' => 'Invalid refund value'],
            'minimum' => ['rule' => ['comparison', '>=', '0.01'], 'message' => 'Refund must be greater than 0.00'],
            'maximum' => [
                'rule' => ['doesNotExceedRemaining', 'DealerOrder.total_price'],
                'message' => 'Refund exceeds the amount available',
            ],
        ],
        'shipping_portion' => [
            'decimal' => ['rule' => 'decimal', 'message' => 'Invalid shipping value'],
            'minimum' => ['rule' => ['comparison', '>=', '0.00'], 'message' => 'Shipping cannot be negative'],
            'maximum' => [
                'rule' => ['doesNotExceedRemaining', 'DealerOrder.shipping_amount'],
                'message' => 'Shipping exceeds the amount available',
            ],
        ],
        'tax_portion' => [
            'decimal' => ['rule' => 'decimal', 'message' => 'Invalid tax value'],
            'minimum' => ['rule' => ['comparison', '>=', '0.00'], 'message' => 'Tax cannot be negative'],
            'maximum' => [
                'rule' => ['doesNotExceedRemaining', 'DealerOrder.total_tax'],
                'message' => 'Tax exceeds the amount available',
            ],
        ],
        'reason' => [
            'notBlank' => ['rule' => 'notBlank', 'allowEmpty' => true, 'message' => 'This field cannot be left blank'],
        ],
        'created_at' => [
            'datetime' => ['rule' => 'datetime', 'message' => 'Invalid datetime'],
        ],
        'updated_at' => [
            'datetime' => ['rule' => 'datetime', 'message' => 'Invalid datetime'],
        ],
    ];

    public $belongsTo = [
        'DealerOrder',
    ];

    public $hasMany = [
        'DealerOrderRefundProduct',
    ];

    public $recursive = -1;

    public function doesNotExceedRemaining($check, $dealerOrderFieldName, $ruleParams)
    {
        $id = (int)($this->data[$this->alias]['id'] ?? $this->id);
        $dealerOrderId = (int)(
            $this->data[$this->alias]['dealer_order_id'] ??
            $this->fieldByConditions('dealer_order_id', compact('id'))
        );

        $fieldName = key($check);
        $fieldValue = current($check);

        $remainingValue = $this->DealerOrder->fieldByConditions(
            "{$dealerOrderFieldName} - COALESCE(SUM({$this->alias}.{$fieldName}), 0)",
            [
                'DealerOrder.id' => $dealerOrderId,
                "{$this->alias}.id !=" => $id,
            ],
            [
                'joins' => [
                    [
                        'table' => $this->table,
                        'alias' => $this->alias,
                        'type' => 'LEFT',
                        'conditions' => ["{$this->alias}.dealer_order_id" => $this->identifier('DealerOrder.id')],
                    ],
                ],
            ]
        );

        return Validation::comparison($fieldValue, '<=', $remainingValue);
    }

    public function findAllForOrderTimeline($orderId)
    {
        $this->DealerOrderRefundProduct->DealerOrderProduct->bindModel(['belongsTo' => ['Product']]);
        $refunds = $this->find('all', [
            'contain' => [
                'DealerOrder' => [
                    'fields' => ['id'],
                ],
                'DealerOrderRefundProduct' => [
                    'fields' => ['id', 'dealer_order_product_id', 'dealer_order_refund_id', 'quantity'],
                    'DealerOrderProduct' => [
                        'fields' => ['id', 'product_id'],
                        'Product' => [
                            'fields' => ['id', 'product_title'],
                        ],
                    ],
                ],
            ],
            'conditions' => [
                'DealerOrder.order_id' => $orderId,
            ],
            'fields' => [
                $this->alias . '.id',
                $this->alias . '.amount',
                $this->alias . '.shipping_portion',
                $this->alias . '.reason',
                $this->alias . '.created_at',
            ],
        ]);

        $this->resetBindings();
        $this->DealerOrderRefundProduct->resetBindings();

        return $refunds;
    }
}
