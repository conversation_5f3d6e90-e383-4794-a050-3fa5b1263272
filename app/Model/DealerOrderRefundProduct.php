<?php
App::uses('AppModel', 'Model');

/**
 * DealerOrderRefundProduct Model.
 *
 * @property DealerOrderRefund $DealerOrderRefund
 * @property DealerOrderProduct $DealerOrderProduct
 */
class DealerOrderRefundProduct extends AppModel
{
    public $validate = [
        'dealer_order_refund_id' => [
            'naturalNumber' => ['rule' => 'naturalNumber', 'message' => 'Invalid dealer order refund id'],
        ],
        'dealer_order_product_id' => [
            'naturalNumber' => ['rule' => 'naturalNumber', 'message' => 'Invalid dealer order product id'],
        ],
        'quantity' => [
            'naturalNumber' => ['rule' => 'naturalNumber', 'required' => 'create', 'message' => 'Invalid quantity'],
            'maximum' => [
                'rule' => ['doesNotExceedRemaining', 'DealerOrderProduct.product_quantity'],
                'message' => 'Quantity exceeds the amount available',
            ],
        ],
        'created_at' => [
            'datetime' => ['rule' => 'datetime', 'message' => 'Invalid datetime'],
        ],
        'updated_at' => [
            'datetime' => ['rule' => 'datetime', 'message' => 'Invalid datetime'],
        ],
    ];

    public $belongsTo = [
        'DealerOrderRefund',
        'DealerOrderProduct',
    ];

    public $recursive = -1;

    public function doesNotExceedRemaining($check, $dealerOrderProductFieldName, $ruleParams)
    {
        $id = (int)($this->data[$this->alias]['id'] ?? $this->id);
        $dealerOrderProductId = (int)(
            $this->data[$this->alias]['dealer_order_product_id'] ??
            $this->fieldByConditions('dealer_order_product_id', compact('id'))
        );

        $fieldName = key($check);
        $fieldValue = current($check);

        $remainingValue = $this->DealerOrderProduct->fieldByConditions(
            "{$dealerOrderProductFieldName} - COALESCE(SUM({$this->alias}.{$fieldName}), 0)",
            [
                'DealerOrderProduct.id' => $dealerOrderProductId,
                "{$this->alias}.id !=" => $id,
            ],
            [
                'joins' => [
                    [
                        'table' => $this->table,
                        'alias' => $this->alias,
                        'type' => 'LEFT',
                        'conditions' => ["{$this->alias}.dealer_order_product_id" => $this->identifier('DealerOrderProduct.id')],
                    ],
                ],
            ]
        );

        return Validation::comparison($fieldValue, '<=', $remainingValue);
    }
}
