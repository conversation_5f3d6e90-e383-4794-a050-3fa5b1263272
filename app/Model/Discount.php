<?php
App::uses('AppModel', 'Model');
App::uses('DiscountRule', 'Model');
App::uses('DiscountOptions', 'Utility/Discount');
App::uses('DiscountOrderOptions', 'Utility/Discount');
App::uses('DiscountPrerequisiteOptions', 'Utility/Discount');
App::uses('DiscountBuyXValueTypes', 'Utility/Discount');
App::uses('DiscountRetailerOptions', 'Utility/Discount');
App::uses('DiscountUsageLimitOptions', 'Utility/Discount');


/**
 * Class Discount
 * 
 * @property User $User
 * @property DiscountRule $DiscountRule
 * @property CreditTerm $CreditTerm
 */
class Discount extends AppModel
{

    public const CALCULATION_REQUIRED_FIELDS = [
        'id',
        'code',
        'description',
        'is_b2b_discount',
        'b2b_hide_ineligible_products',
        'enable_free_freight',
        'retailer_option',
        'retailer_values',
    ];

    public $validate = [
        'user_id' => ['rule' => 'naturalNumber'],
        'uuid' => ['rule' => 'uuid'],
        'code' => [
            ['rule' => 'notBlank', 'required' => 'create'],
            /** @see Discount::noDateRangeConflict() */
            ['rule' => 'noDateRangeConflict', 'message' => 'An existing discount code conflicts with this date range'],
        ],
        'description' => [
            ['rule' => ['maxLength', 255], 'required' => 'create'],
        ],
        'start_date' => [
            ['rule' => 'date', 'required' => 'create'],
            /** @see Discount::dateFieldComparison() */
            ['rule' => ['dateFieldComparison', '<', 'end_date'], 'message' => 'Start date must be before the end date'],
            /** @see Discount::noDateRangeConflict() */
            ['rule' => ['noDateRangeConflict', 'code'], 'message' => 'An existing discount code conflicts with this date range'],
            /** @see Discount::checkAutomaticDiscountNoConflict() */
            ['rule' => 'checkAutomaticDiscountNoConflict', 'message' => 'An existing automatic discount conflicts with this date range'],
        ],
        'end_date' => [
            ['rule' => 'date', 'required' => 'create'],
            /** @see Discount::dateFieldComparison() */
            ['rule' => ['dateFieldComparison', '>', 'start_date'], 'message' => 'End date must be after the start date'],
        ],
        'shipping_window_start_date' => [
            ['rule' => 'date', 'allowEmpty' => true],
            /** @see Discount::dateFieldComparison() */
            ['rule' => ['dateFieldComparison', '<', 'shipping_window_end_date'], 'message' => 'Shipping window start date must be before the shipping window end date'],
        ],
        'shipping_window_end_date' => [
            ['rule' => 'date', 'allowEmpty' => true],
            /** @see Discount::dateFieldComparison() */
            ['rule' => ['dateFieldComparison', '>', 'shipping_window_start_date'], 'message' => 'Shipping window end date must be after the shipping window start date'],
        ],
        'retailer_option' => [
            'rule' => ['inList', DiscountRetailerOptions::OPTIONS],
            'required' => 'create',
            'message' => 'Invalid retailer option',
        ],
        'retailer_values' => ['rule' => 'notBlank', 'allowEmpty' => true, 'message' => 'Invalid retailer values'],
        'usage_limit_option' => [
            'rule' => ['inList', DiscountUsageLimitOptions::OPTIONS],
            'required' => 'create',
            'message' => 'Invalid usage limit option',
        ],
        'usage_limit_quantity' => ['rule' => ['naturalNumber', true]],
        'limit_per_customer' => ['rule' => 'boolean'],
        'is_active' => ['rule' => 'boolean'],
        'is_enabled' => ['rule' => 'boolean'],
        'is_automatic' => [
            ['rule' => 'boolean'],
            /** @see Discount::checkAutomaticDiscountNoConflict() */
            ['rule' => 'checkAutomaticDiscountNoConflict', 'message' => 'An existing automatic discount conflicts with this date range'],
        ],
        'usage_count' => ['rule' => ['naturalNumber', true]],
        'created_at' => ['rule' => 'datetime'],
        'updated_at' => ['rule' => 'datetime'],
    ];

    public $belongsTo = [
        'User',
    ];

    public $hasMany = [
        'DiscountRule',
    ];

    public $hasAndBelongsToMany = [
        'CreditTerm' => ['with' => 'DiscountCreditTerm', 'unique' => 'keepExisting'],
    ];

    public $recursive = -1;

    public $virtualFields = [];

    public function initialize(array $config)
    {
        parent::initialize($config);

        $this->virtualFields = array_merge($this->virtualFields, [
            'name' => "{$this->alias}.code",
        ]);
    }

    public function beforeValidate($options = array())
    {
        $this->data = $this->_beforeSave($this->data);

        return parent::beforeValidate($options);
    }

    public function beforeSave($options = array())
    {
        $this->data = $this->_beforeSave($this->data);

        return parent::beforeSave($options);
    }

    protected function _beforeSave(array $data): array
    {
        return $this->setEmptyStringFieldsToNull($data);
    }

    public function buildSearchCondition($term, $searchFields = [], $query = [])
    {
        if (empty($searchFields)) {
            $searchFields = [
                $this->alias . '.code',
                $this->alias . '.usage_limit_option',
            ];
        }

        return parent::buildSearchCondition($term, $searchFields, $query);
    }

    public function dateFieldComparison($check, $operator = null, $fieldName = null)
    {
        $field = $this->data[$this->alias][$fieldName] ?? $this->field($fieldName, ['id' => $this->id]);
        $check = strtotime(current($check));
        $field = strtotime($field);

        return Validation::comparison($check, $operator, $field);
    }

    public function checkAutomaticDiscountNoConflict($check, $ruleParams)
    {
        $is_automatic = $this->data[$this->alias]['is_automatic'] ?? $this->field('is_automatic', ['id' => $this->id]);
        if (!$is_automatic) {
            return true;
        }

        return $this->noDateRangeConflict(compact('is_automatic'), $ruleParams);
    }

    public function noDateRangeConflict($check, $ruleParams)
    {
        $conflictField = key($check);

        $args = func_get_args();
        if (count($args) === 3) {
            $conflictField = $args[1];
            $ruleParams = $args[2];
        }

        $id = $this->id;

        $fields = ['id', 'user_id', 'start_date', 'end_date', $conflictField];
        $existing = (array)$this->findById($id, $fields, null, -1);
        $data = $this->data[$this->alias] + ($existing[$this->alias] ?? []) + compact('id');

        $missingFields = array_filter($fields, function($field) use ($data) {
            return !array_key_exists($field, $data);
        });
        if ($missingFields) {
            Validation::$errors[] = sprintf('Discount missing required fields for %s: %s', __METHOD__, implode(', ', $missingFields));

            return false;
        }

        return !$this->exists([
            'id !=' => $data['id'],
            'user_id' => $data['user_id'],
            'start_date <=' => $data['end_date'],
            'end_date >=' => $data['start_date'],
            $conflictField => $data[$conflictField],
        ]);
    }

    public static function applyManualDiscountToDealerProducts(float $manualDiscount, array $dealerProducts): array
    {
        if (!$manualDiscount) {
            return $dealerProducts;
        }

        $subtotalForManualDiscount = array_sum(array_map(
            fn(array $dealerProduct): float => (!$dealerProduct['Product']['is_fee_product'])
                ? ($dealerProduct['dealer_price'] * $dealerProduct['quantity'])
                : 0.00,
            $dealerProducts
        ));

        return array_map(
            function($dealerProduct) use ($manualDiscount, $subtotalForManualDiscount) {
                if (!$dealerProduct['Product']['is_fee_product']) {
                    $dealerProduct['discount']['manual_amount'] = format_number(DiscountRule::calculateItemDiscountAmount(
                        'amount_off',
                        $manualDiscount,
                        ($dealerProduct['dealer_price'] * $dealerProduct['quantity']),
                        $subtotalForManualDiscount
                    ));
                }

                return $dealerProduct;
            },
            $dealerProducts
        );
    }

    public function findForTemporaryCart(int $discountId): array
    {
        $record = $this->record($discountId, [
            'contain' => [
                'DiscountRule' => [
                    'fields' => DiscountRule::CALCULATION_REQUIRED_FIELDS,
                ],
            ],
            'fields' => static::CALCULATION_REQUIRED_FIELDS,
        ]);

        if (isset($record['DiscountRule'])) {
            $record[$this->alias]['DiscountRule'] = $record['DiscountRule'];
            unset($record['DiscountRule']);
        }

        return $record;
    }

    public function findForEcommerceByCode(int $userId, string $code, array $items): array
    {
        return $this->_findForEcommerce($userId, ["{$this->alias}.code" => $code], $items);
    }

    public function findForEcommerceByAuto(int $userId, array $items): array
    {
        return $this->_findForEcommerce($userId, ["{$this->alias}.is_automatic" => true], $items);
    }

    protected function _findForEcommerce(int $userId, array $conditions, array $items): array
    {
        $discount = (array)$this->find('first', [
            'contain' => [
                'DiscountRule' => [
                    'fields' => DiscountRule::CALCULATION_REQUIRED_FIELDS,
                ],
            ],
            'conditions' => array_merge(
                $conditions,
                [
                    "{$this->alias}.user_id" => $userId,
                    "{$this->alias}.is_b2b_discount" => false,
                    'OR' => [
                        "{$this->alias}.usage_limit_option" => DiscountUsageLimitOptions::UNLIMITED,
                        "{$this->alias}.usage_count < {$this->alias}.usage_limit_quantity",
                    ],
                ],
                $this->_currentlyAvailableDiscountsConditions()
            ),
            'fields' => [
                'id',
                'code',
                'enable_free_freight',
                'retailer_option',
                'retailer_values',
                'usage_limit_option',
                'usage_limit_quantity',
                'limit_per_customer',
                'is_active',
                'is_automatic',
                'usage_count',
                'name',
            ],
        ]);
        if (empty($discount[$this->alias]['id'])) {
            return [];
        }

        $items = $this->_formatEcommerceItems($userId, $items);
        $hasValidRules = (bool)array_filter(array_map(fn(array $rule): array => DiscountRule::processOrderItems($rule, $items), $discount['DiscountRule']));
        if (!$hasValidRules) {
            return [];
        }

        return ($discount[$this->alias] + ['DiscountRule' => $discount['DiscountRule']]);
    }

    public function findEcommerceAutoAddQuantitiesByVariantId(int $userId, array $discountRules, array $items): array
    {
        $items = $this->_formatEcommerceItems($userId, $items);
        $autoAddItemsBySku = $this->findAutoAddItemsBySku($discountRules, $items);
        if (!$autoAddItemsBySku) {
            return [];
        }

        /** @var Product $Product */
        $Product = ClassRegistry::init('Product');
        $variantIdBySku = (array)$Product->find('list', [
            'recursive' => -1,
            'conditions' => [
                'Product.product_sku' => array_keys($autoAddItemsBySku),
                'Product.user_id' => $userId,
                'Product.product_status' => ProductStatus::ENABLED,
                'Product.deleted' => false,
            ],
            'fields' => ['product_sku', 'productID'],
            'order' => false,
        ]);

        $quantityByVariantId = [];
        foreach ($variantIdBySku as $sku => $variantId) {
            $quantityByVariantId[$variantId] = $autoAddItemsBySku[$sku]['quantity'];
        }

        return $quantityByVariantId;
    }

    protected function findAutoAddItemsBySku(array $discountRules, array $items): array
    {
        $rules = array_filter(array_map(fn(array $rule): array => DiscountRule::processOrderItems($rule, $items), $discountRules));
        if (!$rules) {
            return [];
        }
        $allAutoAddItems = array_merge(...array_column($rules, 'auto_add_items'));
        if (!$allAutoAddItems) {
            return [];
        }

        $autoAddItemsBySku = array_reduce($allAutoAddItems, function(array $map, array $item): array {
            $sku = (string)$item['sku'];
            $quantity = (int)$item['quantity'];

            $quantity += (int)($map[$sku]['quantity'] ?? 0);
            $map[$sku] = array_merge($item, ['quantity' => $quantity]);

            return $map;
        }, []);
        ksort($autoAddItemsBySku);

        return $autoAddItemsBySku;
    }

    public function calculateDealerProductDiscounts(int $brandId, array $discountInfo, array $dealerProducts): array
    {
        /** @var Product $Product */
        $Product = ClassRegistry::init('Product');

        $products = $Product->findForPurchaseOrderEdit(array_column($dealerProducts, 'product_id'));
        $products = Hash::combine($products, '{n}.Product.id', '{n}.Product');
        $dealerProducts = array_map(function($dealerProduct) use ($products) {
            // Unique fake id required as a key for discount calculations
            $dealerProduct['id'] = generate_key([$dealerProduct['warehouse_id'], $dealerProduct['product_id']]);

            $dealerProduct['unit_price'] = $dealerProduct['dealer_price'];
            $dealerProduct['total_price'] = format_number($dealerProduct['dealer_price'] * $dealerProduct['quantity']);
            $dealerProduct['Product'] = $products[$dealerProduct['product_id']];

            return $dealerProduct;
        }, $dealerProducts);

        return $this->calculateOrderProductDiscounts($brandId, $discountInfo, $dealerProducts);
    }

    public function calculateEcommerceItemDiscounts(int $brandId, array $discountInfo, array $items): array
    {
        if (empty($discountInfo['id'])) {
            return [];
        }
        $items = $this->_formatEcommerceItems($brandId, $items);

        return $this->_calculateItemDiscounts($brandId, $discountInfo, $items);
    }

    public function calculateShippingRateDiscount(array $discountInfo, array $shippingRate): float
    {
        $shippingAmount = (float)$shippingRate['amount'];

        $shippingDiscount = array_sum(array_map(
            fn(array $rule): float => DiscountRule::calculateShippingDiscount($rule, $shippingAmount),
            $discountInfo['DiscountRule'] ?? []
        ));

        $shippingDiscount = min($shippingDiscount, $shippingAmount);
        $shippingDiscount = max($shippingDiscount, 0.00);

        return (float)$shippingDiscount;
    }

    public function calculateB2bCartItemDiscounts(int $brandId, array $discountInfo, array $cartItems): array
    {
        $items = $this->_formatB2bCartItems($cartItems);
        $discounts = $this->_calculateB2bItemDiscounts($brandId, $discountInfo, $items);

        return $this->mapDiscountsToItems($cartItems, $discounts);
    }

    public function calculateProductListItemDiscounts(int $brandId, array $discountInfo, array $variants): array
    {
        $discounts = [];
        if (!empty($discountInfo['id'])) {
            $items = $this->_formatProductListItems($variants);
            $discountInfo['DiscountRule'] = array_map(fn(array $rule): array => array_merge($rule, [
                'prerequisite_subtotal' => '0.00',
                'prerequisite_quantity' => '0',
            ]), $discountInfo['DiscountRule']);
            $discounts = $this->_calculateB2bItemDiscounts($brandId, $discountInfo, $items);
        }

        return $this->mapDiscountsToItems($variants, $discounts);
    }

    public function calculateOrderProductDiscounts(int $brandId, array $discountInfo, array $orderProducts): array
    {
        $items = $this->_formatOrderProductItems($orderProducts);
        $discounts = $this->_calculateB2bItemDiscounts($brandId, $discountInfo, $items);

        return $this->mapDiscountsToItems($orderProducts, $discounts);
    }

    private function _calculateB2bItemDiscounts(int $brandId, array $discountInfo, array $items): array
    {
        if (!($discountInfo['is_b2b_discount'] ?? false)) {
            return [];
        }

        $items = array_filter($items, fn(array $item): bool => !($item['is_fee_product'] ?? $item['Product']['is_fee_product']));
        if (!$items) {
            return [];
        }

        return $this->_calculateItemDiscounts($brandId, $discountInfo, $items);
    }

    public function calculateApiOrderProductDiscounts(int $brandId, array $discountInfo, array $apiOrderItems): array
    {
        $items = $this->_formatApiOrderProductItems($apiOrderItems);
        $discounts = $this->_calculateApiOrderItemDiscounts($brandId, $discountInfo, $items);

        return $this->mapDiscountsToItems($apiOrderItems, $discounts);
    }

    private function _calculateApiOrderItemDiscounts(int $brandId, array $discountInfo, array $items): array
    {
        $discounts = [];

        if ($discountInfo['is_b2b_discount'] ?? false) {
            /** @var Product $Product */
            $Product = ClassRegistry::init('Product');

            // API item.variant_id maps to ApiV1Variant.id which is equal to Product.id
            $variantIdField = 'id';

            $validVariantIds = (array)$Product->find('list', [
                'recursive' => -1,
                'conditions' => $Product->getConditionsForActiveProducts([
                    "{$Product->alias}.user_id" => $brandId,
                    "{$Product->alias}.{$variantIdField}" => array_column($items, 'variant_id'),
                ]),
                'fields' => ["{$Product->alias}.{$variantIdField}", "{$Product->alias}.{$variantIdField}"],
                'order' => false,
            ]);
            $discountedItems = array_filter($items, function($item) use ($validVariantIds) {
                return array_key_exists($item['variant_id'], $validVariantIds);
            });

            if ($discountedItems) {
                $discounts = $this->_calculateItemDiscounts($brandId, $discountInfo, $discountedItems);
            }
        }

        return (array)$discounts;
    }

    private function mapDiscountsToItems(array $items, array $discounts): array
    {
        return array_map(function($item) use ($discounts) {
            $item['discount'] = $discounts[$item['id']] ?? [];

            return $item;
        }, $items);
    }

    private function _calculateItemDiscounts(int $brandId, array $discountInfo, array $items): array
    {
        if (empty($discountInfo['id'])) {
            return [];
        }

        $rules = array_filter(array_map(fn(array $rule): array => DiscountRule::processOrderItems($rule, $items), $discountInfo['DiscountRule']));
        if (!$rules) {
            return [];
        }

        $retailers = $this->User->listDiscountRetailerIds($brandId, $discountInfo['retailer_option'], $discountInfo['retailer_values']);

        return (array)array_reduce($rules, function(array $itemDiscounts, array $rule) use ($items, $retailers) {
            $ruleItemDiscounts = DiscountRule::calculateItemDiscounts($rule, $retailers);
            $itemDiscounts = $this->_resolveOverlappingItemDiscounts($itemDiscounts, $ruleItemDiscounts, $items);

            return $itemDiscounts + $ruleItemDiscounts;
        }, []);
    }

    protected function _resolveOverlappingItemDiscounts(array $itemDiscounts, array $ruleItemDiscounts, array $items): array
    {
        $overlappingVariantIds = array_keys(array_intersect_key($itemDiscounts, $ruleItemDiscounts));
        foreach ($overlappingVariantIds as $itemKey) {
            $itemDiscounts[$itemKey]['discount_quantity'] = (int)min($itemDiscounts[$itemKey]['discount_quantity'] + $ruleItemDiscounts[$itemKey]['discount_quantity'], $items[$itemKey]['quantity']);
            $itemDiscounts[$itemKey]['discount_amount'] = format_number(min($itemDiscounts[$itemKey]['discount_amount'] + $ruleItemDiscounts[$itemKey]['discount_amount'], $items[$itemKey]['line_price']));
            $itemDiscounts[$itemKey]['is_product_discount'] = ($itemDiscounts[$itemKey]['is_product_discount'] || $ruleItemDiscounts[$itemKey]['is_product_discount']);
        }

        return $itemDiscounts;
    }

    private function _formatEcommerceItems(int $userId, array $items): array
    {
        /** @var Product $Product */
        $Product = ClassRegistry::init('Product');
        $products = $Product->findAllForEcommerceDiscount($userId, array_column($items, 'variant_id'));
        $products = Hash::combine($products, '{n}.Product.productID', '{n}.Product');

        $items = array_filter($items, function($item) use ($products) {
            return array_key_exists($item['variant_id'], $products);
        });

        return array_map(
            function($value) use ($products) {
                $productID = $value['variant_id'];

                $value['price'] /= 100;
                $value['line_price'] /= 100;
                $value['tags'] = array_column($products[$productID]['Tag'],'name');
                $value['collections'] = array_column($products[$productID]['Collection'],'title');

                return $value;
            },
            // Note that cart items may be keyed by {n} or {s} depending on the eCom platform.
            Hash::combine(array_values($items), '{n}.id', '{n}')
        );
    }

    private function _formatB2bCartItems(array $items): array
    {
        return array_map(
            function ($value) {
                $value['price'] = $value['dealer_price'];
                $value['line_price'] = $value['dealer_price'] * $value['quantity'];
                $value['product_type'] = $value['Product']['product_type'];
                $value['product_title'] = $value['Product']['product_name'];
                $value['sku'] = $value['Product']['product_sku'];
                $value['variant_id'] = $value['Product']['id'];
                $value['tags'] = array_column($value['Product']['Tag'],'name');
                $value['collections'] = array_column($value['Product']['Collection'],'title');

                return $value;
            },
            Hash::combine(array_values($items), '{n}.id', '{n}')
        );
    }

    private function _formatProductListItems(array $variants): array
    {
        return $this->_formatB2bCartItems(array_map(
            fn(array $variant): array => $variant + ['quantity' => 1, 'Product' => $variant],
            $variants
        ));
    }

    private function _formatOrderProductItems(array $items): array
    {
        return array_map(
            function ($value) {
                $value['price'] = $value['unit_price'];
                $value['line_price'] = $value['total_price'];
                $value['product_type'] = $value['Product']['product_type'];
                $value['product_title'] = $value['Product']['product_name'];
                $value['sku'] = $value['Product']['product_sku'];
                $value['variant_id'] = $value['Product']['id'];
                $value['tags'] = array_column($value['Product']['Tag'], 'name');
                $value['collections'] = array_column($value['Product']['Collection'], 'title');

                return $value;
            },
            Hash::combine(array_values($items), '{n}.id', '{n}')
        );
    }

    private function _formatApiOrderProductItems(array $items): array
    {
        return array_map(
            function ($value) {
                $value['price'] = ($value['total_price'] / $value['quantity']);
                $value['line_price'] = $value['total_price'];
                $value['product_type'] =  $value['ApiV1Variant']['product_type'];
                $value['product_title'] = $value['ApiV1Variant']['title'];
                $value['sku'] = $value['ApiV1Variant']['product_sku'];
                $value['variant_id'] = $value['ApiV1Variant']['id'];
                $value['tags'] = array_column($value['ApiV1Variant']['Tag'], 'name');
                $value['collections'] = array_column($value['ApiV1Variant']['Collection'], 'title');

                return $value;
            },
            Hash::combine(array_values($items), '{n}.id', '{n}')
        );
    }

    public function findManageDiscountsForm(string $token): array
    {
        $discount = (array)$this->find('first', [
            'contain' => [
                'DiscountRule' => [
                    'fields' => [
                        'id',
                        'discount_id',
                        'option',
                        'option_amount',
                        'prerequisite_subtotal',
                        'prerequisite_quantity',
                        'prerequisite_option',
                        'prerequisite_values',
                        'order_quantity',
                        'order_option',
                        'order_values',
                        'auto_add_sku_quantities',
                        'is_buy_x_get_y',
                        'is_auto_add_y',
                    ],
                ],
                'CreditTerm' => [
                    'with' => ['DiscountCreditTerm' => []],
                    'fields' => ['id'],
                ],
            ],
            'conditions' => ['Discount.uuid' => $token],
            'fields' => [
                'id',
                'code',
                'description',
                'is_b2b_discount',
                'b2b_discount_type',
                'b2b_hide_ineligible_products',
                'enable_free_freight',
                'shipping_window_start_date',
                'shipping_window_end_date',
                'start_date',
                'end_date',
                'retailer_option',
                'retailer_values',
                'usage_limit_option',
                'usage_limit_quantity',
                'limit_per_customer',
                'is_active',
                'is_enabled',
                'is_automatic',
            ],
        ]);
        if (!empty($discount[$this->alias]['id'])) {
            foreach (['start_date', 'end_date', 'shipping_window_start_date', 'shipping_window_end_date'] as $field) {
                $discount[$this->alias][$field] = ($discount[$this->alias][$field]) ? format_datetime_from(DATE_FORMAT_SQL, $discount[$this->alias][$field], DATEPICKER_FORMAT) : null;
            }

            $discount[$this->alias] = $this->_decodeValueSet($discount[$this->alias], 'retailer', fn(string $values): array => explode(',', $values) ?: []);

            $discount['DiscountRule'] = array_map(function(array $rule): array {
                foreach (['prerequisite', 'order'] as $prefix) {
                    $rule = $this->_decodeValueSet($rule, $prefix);
                }
                $rule['auto_add_sku_quantities'] = json_decode_if_array((string)$rule['auto_add_sku_quantities']) ?? [];

                $rule['exclude_shipping_rates_above'] = ($rule['option'] === DiscountOptions::FREE_SHIPPING && $rule['option_amount'] > 0);

                return $this->_set_buy_x_get_y_form($rule);
            }, $discount['DiscountRule']);

            // DiscountRule fields that are controlled per Discount
            $ruleAggregates = array_intersect_key(current($discount['DiscountRule']) ?: [], array_flip([
                'option',
                'prerequisite_option',
                'prerequisite_quantity',
                'prerequisite_subtotal',
                'is_auto_add_y',
            ]));
            $discount[$this->alias] += $ruleAggregates;
        }

        return (array)$discount;
    }

    public function getCreditTermOptionsByDiscount($discountId)
    {
        $contain = [
            'CreditTerm' => ['fields' => ['id', 'description']]
        ];

        $result = $this->find('first', [
            'contain' => $contain,
            'conditions' => ["{$this->alias}.id" => $discountId],
            'fields' => ['id']
        ]);

        return $result ? Hash::combine($result, 'CreditTerm.{n}.id', 'CreditTerm.{n}.description') : [];
    }

    protected function _set_buy_x_get_y_form(array $data): array
    {
        if ($data['is_buy_x_get_y']) {
            $data['buy_x_get_y_option'] = $data['option'];
            $data['option'] = DiscountOptions::BUY_X_GET_Y;
            $data['buy_x_get_y_amount'] = $data['option_amount'];
            unset($data['option_amount']);

            $data['buy_x_value_type'] = null;
            $data['buy_x_quantity'] = null;
            $data['buy_x_subtotal'] = null;
            if ($data['prerequisite_quantity'] > 0) {
                $data['buy_x_value_type'] = DiscountBuyXValueTypes::QUANTITY;
                $data['buy_x_quantity'] = $data['prerequisite_quantity'];
            }
            unset($data['prerequisite_quantity']);
            if ($data['prerequisite_subtotal'] > 0) {
                $data['buy_x_value_type'] = DiscountBuyXValueTypes::SUBTOTAL;
                $data['buy_x_subtotal'] = $data['prerequisite_subtotal'];
            }
            unset($data['prerequisite_subtotal']);

            $data['buy_x_option'] = $data['prerequisite_option'];
            unset($data['prerequisite_option']);
            $data['buy_x_values'] = $data['prerequisite_values'];
            unset($data['prerequisite_values']);

            $data['get_y_quantity'] = $data['order_quantity'];
            unset($data['order_quantity']);
            $data['get_y_option'] = $data['order_option'];
            unset($data['order_option']);
            $data['get_y_values'] = $data['order_values'];
            unset($data['order_values']);

            $data['get_y_any_quantity'] = ($data['get_y_quantity'] <= 0);
            if ($data['get_y_any_quantity']) {
                $data['get_y_quantity'] = null;
            }

            if ($data['is_auto_add_y']) {
                $data['auto_add_y'] = [];
                foreach ($data['auto_add_sku_quantities'] as $sku => $quantity) {
                    $data['auto_add_y'][] = ['sku' => $sku, 'quantity' => $quantity];
                }
            } else {
                // Placeholders
                $data['auto_add_y'] = array_map(
                    fn($value): array => ['sku' => $value, 'quantity' => 0],
                    (array)($data['get_y_values'][DiscountOrderOptions::PRODUCT_VARIANT] ?? [])
                );
            }
        } else {
            unset(
                $data['prerequisite_option'],
                $data['prerequisite_values'],
                $data['order_quantity']
            );
            if ($data['prerequisite_subtotal'] > 0) {
                $data['prerequisite_option'] = DiscountPrerequisiteOptions::SUBTOTAL;
            }
            if ($data['prerequisite_quantity'] > 0) {
                $data['prerequisite_option'] = DiscountPrerequisiteOptions::QUANTITY;
            }
        }

        return $data;
    }

    protected function _decodeValueSet(array $data, string $prefix, ?callable $serializeMethod = null): array
    {
        $optionKey = ($prefix . '_option');
        $valuesKey = ($prefix . '_values');
        if ($serializeMethod === null) {
            $serializeMethod = fn(string $values): array => (array)json_decode_if_array($values);
        }

        if (!empty($data[$optionKey])) {
            $data[$valuesKey] = [
                $data[$optionKey] => call_user_func($serializeMethod, $data[$valuesKey]),
            ];
        } else {
            // Remove to use form defaults
            unset($data[$optionKey], $data[$valuesKey]);
        }

        return $data;
    }

    /**
     * @param int $userId
     * @param array $data
     * @return bool
     */
    public function saveManageDiscountsForm($userId, $data)
    {
        $discount = $data['Discount'];
        $creditTermIds = $data['CreditTerm']['id'];

        $discountRules = $data['DiscountRule'];

        if ($discount['option'] === DiscountOptions::NO_DISCOUNT) {
            $discountRules = array_slice($discountRules, 0, 1);
        }

        if (!$discountRules) {
            // For logs
            $this->validationErrors['DiscountRule'] = ['At least one discount rule is required.'];

            return false;
        }

        $discount['is_buy_x_get_y'] = ($discount['option'] === DiscountOptions::BUY_X_GET_Y);
        $discount['usage_limit_quantity'] = (int)$discount['usage_limit_quantity'];
        $discount['limit_per_customer'] = (bool)$discount['limit_per_customer'];
        foreach (['start_date', 'end_date', 'shipping_window_start_date', 'shipping_window_end_date'] as $field) {
            $discount[$field] = format_from_datepicker($discount[$field]);
        }

        $discount['retailer_values'] = $this->_encodeValueSet($discount['retailer_option'], $discount['retailer_values'],
            function($values) {
                return implode(',', $values);
            }
        );

        if (!$discount['is_buy_x_get_y']) {
            $discount['is_auto_add_y'] = false;
        }
        $discount['is_b2b_discount'] = $discount['is_b2b_discount'] || ($discount['discount_generate_option'] ?? '') === 'b2b';
        if (!$discount['is_b2b_discount'] || $discount['is_buy_x_get_y']) {
            $discount['b2b_hide_ineligible_products'] = false;
        }

        $existing = $this->record($discount['id'], [
            'contain' => [
                'DiscountRule' => [
                    'fields' => ['id', 'discount_id'],
                    'order' => ['DiscountRule.id' => 'ASC'],
                ],
            ],
            'fields' => ['id', 'usage_count'],
        ]);
        $id = Hash::get($existing, $this->alias . '.' . $this->primaryKey);
        $usage_count = (int)Hash::get($existing, $this->alias . '.usage_count');

        $discount = array_merge($discount, [
            'id' => $id,
            'user_id' => $userId,
            'is_active' => ($usage_count < $discount['usage_limit_quantity'] || $discount['usage_limit_option'] === DiscountUsageLimitOptions::UNLIMITED),
        ]);
        if (!$discount['id']) {
            $discount = array_merge($discount, [
                'is_enabled' => true,
                'is_automatic' => ($discount['discount_generate_option'] === 'automatic'),
            ]);
        }

        $existingRuleIds = array_filter(array_column($existing['DiscountRule'] ?? [], 'id', 'id'));
        $discountRules = array_filter($discountRules, fn(array $rule): bool => !$rule['id'] || array_key_exists($rule['id'], $existingRuleIds));
        $deletedRuleIds = array_diff($existingRuleIds, array_column($discountRules, 'id'));

        // DiscountRule fields that are controlled per Discount
        $ruleAggregates = array_intersect_key($discount, array_flip([
            'option',
            'is_auto_add_y',
            'prerequisite_option',
            'prerequisite_quantity',
            'prerequisite_subtotal',
        ]));
        $discountRules = array_map(function(array $rule) use ($ruleAggregates): array {
            $rule = $this->_read_buy_x_get_y_form($rule + $ruleAggregates);

            foreach (['prerequisite_quantity', 'order_quantity'] as $field) {
                $rule[$field] = (int)$rule[$field];
            }
            foreach (['option_amount', 'prerequisite_subtotal'] as $field) {
                $rule[$field] = (float)$rule[$field];
            }
            if ($rule['is_buy_x_get_y']) {
                $rule['prerequisite_values'] = $this->_encodeValueSet($rule['prerequisite_option'], $rule['prerequisite_values']);
            }
            $rule['order_values'] = $this->_encodeValueSet($rule['order_option'], $rule['order_values']);
            $rule['auto_add_sku_quantities'] = $rule['auto_add_sku_quantities'] ? json_encode($rule['auto_add_sku_quantities']) : null;

            return $rule;
        }, $discountRules);

        $save = [
            $this->alias => $discount,
            'DiscountRule' => $discountRules,
            // See: https://book.cakephp.org/2.0/en/models/saving-your-data.html#saving-related-model-data-habtm
            'CreditTerm' => ['CreditTerm' => $creditTermIds]
        ];

        if (!$discount['id']) {
            $bulk_codes = json_decode($discount['bulk_discount_codes'], true);
            if (!empty($bulk_codes)) {
                return (bool)$this->saveMany(array_map(
                    function(string $code) use ($save): array {
                        $save[$this->alias]['code'] = $code;

                        return $save;
                    },
                    $bulk_codes
                ), ['deep' => true]);
            }
        }

        $success = (bool)$this->saveAssociated($save, ['deep' => true]);

        if ($success) {
            if ($deletedRuleIds && !$this->DiscountRule->deleteAllJoinless(['DiscountRule.id' => $deletedRuleIds], false)) {
                CakeLog::warning(json_encode(['message' => 'Failed to delete discount rules', 'conditions' => ['DiscountRule.id' => $deletedRuleIds]]));
            }
        }

        return $success;
    }

    protected function _read_buy_x_get_y_form(array $data): array
    {
        $data['is_buy_x_get_y'] = ($data['option'] === DiscountOptions::BUY_X_GET_Y);
        if ($data['is_buy_x_get_y']) {
            $data['option'] = $data['buy_x_get_y_option'];
            $data['option_amount'] = ($data['option'] !== DiscountOptions::FREE) ? $data['buy_x_get_y_amount'] : '0.00';

            $data['prerequisite_quantity'] = ($data['buy_x_value_type'] === DiscountBuyXValueTypes::QUANTITY) ? $data['buy_x_quantity'] : '0';
            $data['prerequisite_subtotal'] = ($data['buy_x_value_type'] === DiscountBuyXValueTypes::SUBTOTAL) ? $data['buy_x_subtotal'] : '0.00';
            $data['prerequisite_option'] = $data['buy_x_option'];
            $data['prerequisite_values'] = $data['buy_x_values'];

            if ($data['is_auto_add_y']) {
                $data['auto_add_sku_quantities'] = array_reduce((array)$data['auto_add_y'], function(array $map, array $value): array {
                    $sku = (string)($value['sku'] ?? '');
                    if ($sku) {
                        $quantity = (int)($value['quantity'] ?? 0);
                        // Edge case: add quantities from duplicated values.
                        $quantity += (int)($map[$sku] ?? 0);
                        if ($quantity > 0) {
                            $map[$sku] = $quantity;
                        }
                    }

                    return $map;
                }, []);
                ksort($data['auto_add_sku_quantities']);

                // Backwards compatibility: convert to Buy X Get Y fields
                $data['order_quantity'] = array_sum(array_values($data['auto_add_sku_quantities']));
                $data['order_option'] = DiscountOrderOptions::PRODUCT_VARIANT;
                $data['order_values'] = [$data['order_option'] => array_keys($data['auto_add_sku_quantities'])];
            } else {
                $data['auto_add_sku_quantities'] = null;

                $data['order_quantity'] = (!$data['get_y_any_quantity']) ? $data['get_y_quantity'] : '0';
                $data['order_option'] = $data['get_y_option'];
                $data['order_values'] = $data['get_y_values'];
            }
        } else {
            if ($data['prerequisite_option'] !== DiscountPrerequisiteOptions::SUBTOTAL) {
                $data['prerequisite_subtotal'] = '0.00';
            }
            if ($data['prerequisite_option'] !== DiscountPrerequisiteOptions::QUANTITY) {
                $data['prerequisite_quantity'] = '0';
            }
            $data['prerequisite_option'] = null;
            $data['prerequisite_values'] = null;
            $data['order_quantity'] = '0';
            $data['auto_add_sku_quantities'] = null;
        }

        unset(
            $data['buy_x_get_y_option'],
            $data['buy_x_get_y_amount'],
            $data['buy_x_value_type'],
            $data['buy_x_quantity'],
            $data['buy_x_subtotal'],
            $data['buy_x_option'],
            $data['buy_x_values'],
            $data['get_y_quantity'],
            $data['get_y_any_quantity'],
            $data['get_y_option'],
            $data['get_y_values'],
            $data['auto_add_y']
        );

        return $data;
    }

    protected function _encodeValueSet($option, $values, $serializeMethod = 'json_encode')
    {
        $extracted = Hash::get($values, $option);
        if (is_array($extracted)) {
            $extracted = $serializeMethod($extracted);
        }

        return (string)$extracted;
    }

    public function getAvailableB2bDiscounts(?int $retailerId, int $brandId): array
    {
        $discounts = (array)$this->find('all', [
            'recursive' => -1,
            'conditions' => array_merge([
                "{$this->alias}.user_id" => $brandId,
                "{$this->alias}.is_b2b_discount" => true,
            ], $this->_currentlyAvailableDiscountsConditions()),
            'fields' => [
                'id',
                'code',
                'description',
                'b2b_discount_type',
                'retailer_option',
                'retailer_values',
            ],
        ]);

        if ($retailerId === null) {
            return $discounts;
        }

        return array_filter($discounts, fn(array $discount): bool => (
            in_array($retailerId, $this->User->listDiscountRetailerIds($brandId, $discount['Discount']['retailer_option'], $discount['Discount']['retailer_values']))
        ));
    }

    public function calculateTotalDiscount(array $discounts)
    {
        return array_sum(array_column($discounts, 'discount_amount'));
    }

    public function getDiscountType(?int $discountId) {
        return $this->field('b2b_discount_type', ['id' => $discountId]);
    }

    public function _currentlyAvailableDiscountsConditions(): array
    {
        $today = $this->date('Y-m-d');

        return [
            "{$this->alias}.is_enabled" => true,
            "{$this->alias}.is_active" => true,
            "{$this->alias}.start_date <=" => $today,
            "{$this->alias}.end_date >=" => $today,
        ];
    }

    public function setInactiveIfOverUsageLimit(int $id): bool
    {
        $inactiveConditions = [
            "{$this->alias}.usage_limit_option" => DiscountUsageLimitOptions::LIMITED,
            "{$this->alias}.usage_limit_quantity <= {$this->alias}.usage_count",
        ];
        $updates = [
            "{$this->alias}.is_active" => "IF({$this->conditions($inactiveConditions)}, FALSE, `{$this->alias}`.`is_active`)",
        ];

        return $this->updateAllJoinless($updates, ["{$this->alias}.{$this->primaryKey}" => $id]);
    }
}
