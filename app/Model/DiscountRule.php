<?php

App::uses('AppModel', 'Model');
App::uses('Product', 'Model');
App::uses('DiscountOptions', 'Utility/Discount');
App::uses('DiscountOrderOptions', 'Utility/Discount');
App::uses('DiscountBuyXGetYPrerequisiteOptions', 'Utility/Discount');

/**
 * DiscountRule Model.
 *
 * @property Discount $Discount
 */
class DiscountRule extends AppModel
{
    public const CALCULATION_REQUIRED_FIELDS = [
        'id',
        'discount_id',
        'option',
        'option_amount',
        'prerequisite_subtotal',
        'prerequisite_quantity',
        'prerequisite_option',
        'prerequisite_values',
        'order_quantity',
        'order_option',
        'order_values',
        'auto_add_sku_quantities',
    ];

    public $validate = [
        'discount_id' => [
            'numeric' => ['rule' => 'numeric', 'message' => 'Invalid discount id'],
        ],
        'option' => [
            'notBlank' => ['rule' => 'notBlank', 'required' => 'create', 'message' => 'This field cannot be left blank'],
            'isValid' => ['rule' => ['inList', DiscountOptions::ALL], 'message' => 'Invalid discount option'],
        ],
        'option_amount' => [
            'decimal' => ['rule' => 'decimal', 'required' => 'create', 'message' => 'Invalid discount amount'],
            'positive' => ['rule' => ['comparison', '>=', 0], 'message' => 'Discount amount cannot be negative'],
        ],
        'prerequisite_subtotal' => [
            'decimal' => ['rule' => 'decimal', 'message' => 'Invalid minimum amount'],
            'positive' => ['rule' => ['comparison', '>=', 0], 'message' => 'Minimum amount cannot be negative'],
        ],
        'prerequisite_quantity' => [
            'naturalNumber' => ['rule' => ['naturalNumber', true], 'message' => 'Invalid minimum quantity'],
        ],
        'prerequisite_option' => [
            'notBlank' => ['rule' => 'notBlank', 'allowEmpty' => true, 'message' => 'This field cannot be left blank'],
            'isValid' => ['rule' => ['inList', DiscountBuyXGetYPrerequisiteOptions::OPTIONS], 'message' => 'Invalid buy x option'],
        ],
        'prerequisite_values' => [
            'notBlank' => ['rule' => 'notBlank', 'allowEmpty' => true, 'message' => 'Invalid buy x values'],
        ],
        'order_quantity' => [
            'naturalNumber' => ['rule' => ['naturalNumber', true], 'message' => 'Invalid order quantity'],
        ],
        'order_option' => [
            'notBlank' => ['rule' => 'notBlank', 'required' => 'create', 'message' => 'This field cannot be left blank'],
            'isValid' => ['rule' => ['inList', DiscountOrderOptions::OPTIONS], 'message' => 'Invalid order option'],
        ],
        'order_values' => [
            'notBlank' => ['rule' => 'notBlank', 'allowEmpty' => true, 'message' => 'This field cannot be left blank'],
        ],
        'auto_add_sku_quantities' => [
            'notBlank' => ['rule' => 'notBlank', 'allowEmpty' => true, 'message' => 'This field cannot be left blank'],
        ],
        'created_at' => [
            'datetime' => ['rule' => 'datetime', 'message' => 'Invalid datetime'],
        ],
        'updated_at' => [
            'datetime' => ['rule' => 'datetime', 'message' => 'Invalid datetime'],
        ],
    ];

    public $belongsTo = [
        'Discount',
    ];

    public $recursive = -1;

    public $virtualFields = [];

    public function initialize(array $config)
    {
        parent::initialize($config);

        $this->virtualFields = array_merge($this->virtualFields, [
            'is_buy_x_get_y' => "COALESCE({$this->alias}.prerequisite_option, '') != ''",
            'is_auto_add_y' => "COALESCE({$this->alias}.auto_add_sku_quantities, '') != ''",
        ]);
    }

    public function beforeValidate($options = array())
    {
        $this->data = $this->_beforeSave($this->data);

        return parent::beforeValidate($options);
    }

    public function beforeSave($options = array())
    {
        $this->data = $this->_beforeSave($this->data);

        return parent::beforeSave($options);
    }

    protected function _beforeSave(array $data): array
    {
        return $this->setEmptyStringFieldsToNull($data);
    }

    /**
     * @param array $discountRule
     * @return bool
     */
    public static function isBuyXGetY(array $discountRule): bool
    {
        // Trigger Notice if the array does not have the required key: prerequisite_option
        return (bool)($discountRule['is_buy_x_get_y'] ?? $discountRule['prerequisite_option']);
    }

    public static function isAutoAddY(array $discountRule): bool
    {
        // Trigger Notice if the array does not have the required key: prerequisite_option
        return (bool)($discountRule['is_auto_add_y'] ?? $discountRule['auto_add_sku_quantities']);
    }

    public static function isProductDiscount(array $discountRule): bool
    {
        return $discountRule['option'] === DiscountOptions::PERCENT_OFF && !static::isBuyXGetY($discountRule);
    }

    public static function processOrderItems(array $discountRule, array $items): array
    {
        $isBuyXGetY = static::isBuyXGetY($discountRule);
        $isAutoAddY = static::isAutoAddY($discountRule);
        $prerequisiteQuantity = (int)$discountRule['prerequisite_quantity'];
        $prerequisiteSubtotal = (float)$discountRule['prerequisite_subtotal'];
        $orderOption = (string)$discountRule['order_option'];
        $orderValues = (string)$discountRule['order_values'];

        $orderItems = static::extractOptionValueItems($items, $orderOption, $orderValues);
        if (!$isAutoAddY && !$orderItems) {
            return [];
        }

        $prerequisiteItems = ($isBuyXGetY)
            ? static::extractOptionValueItems($items, (string)$discountRule['prerequisite_option'], (string)$discountRule['prerequisite_values'])
            : $orderItems;
        if (
            array_sum(array_column($prerequisiteItems, 'quantity')) < $prerequisiteQuantity
            || array_sum(array_column($prerequisiteItems, 'line_price')) < $prerequisiteSubtotal
        ) {
            return [];
        }

        $autoAddItems = [];
        if ($isBuyXGetY) {
            $orderItems = ($prerequisiteQuantity > 0)
                ? static::deductPrerequisiteQuantityFromOrderItems($orderItems, $prerequisiteItems, $prerequisiteQuantity)
                : static::deductPrerequisiteSubtotalFromOrderItems($orderItems, $prerequisiteItems, $prerequisiteSubtotal);
            if (!$isAutoAddY && !$orderItems) {
                return [];
            }

            if ($isAutoAddY) {
                $autoAddSkuQuantities = json_decode_if_array((string)$discountRule['auto_add_sku_quantities']) ?? [];
                list($orderItems, $autoAddItems) = static::filterAutoAddDiscountItems($orderItems, $autoAddSkuQuantities);
                if (!$orderItems && !$autoAddItems) {
                    return [];
                }
            } else {
                $orderQuantity = (int)$discountRule['order_quantity'];
                if ($orderQuantity > 0) {
                    $orderItems = static::filterQuantityDiscountItems($orderItems, $orderQuantity);
                    if (!$orderItems || array_sum(array_column($orderItems, 'quantity')) < $orderQuantity) {
                        return [];
                    }
                }
            }
        }

        $discountOption = (string)$discountRule['option'];
        $optionAmount = (float)$discountRule['option_amount'];
        $totalPrice = (float)array_sum(array_column($orderItems, 'line_price'));

        $discountRule['items'] = array_map(fn(array $item): array => array_merge($item, [
            'total_discount' => format_number(static::calculateItemDiscountAmount($discountOption, $optionAmount, (float)$item['line_price'], $totalPrice)),
        ]), $orderItems);
        $discountRule['auto_add_items'] = $autoAddItems;

        return $discountRule;
    }

    protected static function extractOptionValueItems(array $items, string $option, string $values): array
    {
        if ($option === DiscountOrderOptions::ALL) {
            return $items;
        }

        $optionToField = [
            DiscountOrderOptions::CATEGORY => 'product_type',
            DiscountOrderOptions::PRODUCT_TITLE => 'product_title',
            DiscountOrderOptions::PRODUCT_VARIANT => 'sku',
            DiscountOrderOptions::PRODUCT_COLLECTION => 'collections',
            DiscountOrderOptions::PRODUCT_TAG => 'tags',
        ];
        $field = $optionToField[$option] ?? null;
        if (!$field) {
            triggerWarning('Unexpected discount option: ' . json_encode($option));

            return [];
        }

        $decoded_values = json_decode_if_array($values);
        if (!$decoded_values) {
            triggerWarning('Invalid discount values: ' . json_encode($values));

            return [];
        }

        return array_filter($items, fn(array $item): bool => (bool)array_intersect(
            // WooCommerce gives 'product_type' as an array which we store as JSON, so decode it if necessary.
            ($field === 'product_type') ? Product::decodeProductType($item[$field]) : (array)$item[$field],
            $decoded_values
        ));
    }

    protected static function deductPrerequisiteQuantityFromOrderItems(array $orderItems, array $prerequisiteItems, int $prerequisiteQuantity): array
    {
        // Deduct higher value items first
        $sortByPriceDesc = fn($current, $next) => ((float)$next['price']) <=> ((float)$current['price']);
        uasort($prerequisiteItems, $sortByPriceDesc);

        $exclusivePrerequisiteItems = array_diff_key($prerequisiteItems, $orderItems);
        $overlappingItems = array_intersect_key($prerequisiteItems, $orderItems);

        // Deduct items until prerequisite is met
        $prerequisiteQuantity -= array_sum(array_column($exclusivePrerequisiteItems, 'quantity'));
        foreach (array_keys($overlappingItems) as $key) {
            if ($prerequisiteQuantity <= 0) {
                break;
            }

            $item = (array)$orderItems[$key];
            $price = (float)$item['price'];
            $quantity = (int)$item['quantity'];

            $deductedQuantity = (int)min($quantity, $prerequisiteQuantity);
            if ($deductedQuantity <= 0) {
                continue;
            }

            $prerequisiteQuantity -= $deductedQuantity;
            $quantity -= $deductedQuantity;

            if ($quantity > 0) {
                $orderItems[$key] = array_merge($item, [
                    'quantity' => $quantity,
                    'line_price' => $quantity * $price,
                ]);
            } else {
                unset($orderItems[$key]);
            }
        }

        return $orderItems;
    }

    protected static function deductPrerequisiteSubtotalFromOrderItems(array $orderItems, array $prerequisiteItems, float $prerequisiteSubtotal): array
    {
        // Deduct higher value items first
        $sortByPriceDesc = fn($current, $next) => ((float)$next['price']) <=> ((float)$current['price']);
        uasort($prerequisiteItems, $sortByPriceDesc);

        $exclusivePrerequisiteItems = array_diff_key($prerequisiteItems, $orderItems);
        $overlappingItems = array_intersect_key($prerequisiteItems, $orderItems);

        // Deduct items until prerequisite is met
        $prerequisiteSubtotal -= array_sum(array_column($exclusivePrerequisiteItems, 'line_price'));
        foreach (array_keys($overlappingItems) as $key) {
            if ($prerequisiteSubtotal <= 0.00) {
                break;
            }

            $item = (array)$orderItems[$key];
            $price = (float)$item['price'];
            $linePrice = (float)$item['line_price'];

            $deductedSubtotal = (float)min($linePrice, $prerequisiteSubtotal);
            if ($deductedSubtotal <= 0.00 || $price <= 0.00) {
                continue;
            }

            $prerequisiteSubtotal -= $deductedSubtotal;
            $quantity = (int)floor(($linePrice - $deductedSubtotal) / $price);

            if ($quantity > 0) {
                $orderItems[$key] = array_merge($item, [
                    'quantity' => $quantity,
                    'line_price' => $quantity * $price,
                ]);
            } else {
                unset($orderItems[$key]);
            }
        }

        return $orderItems;
    }

    protected static function filterAutoAddDiscountItems(array $items, array $autoAddSkuQuantities): array
    {
        // Preserve the original sort order
        $sortedItems = $items;
        // If there is more than one of the same SKU, discount higher quantity items first
        $sortByQuantityDesc = fn(array $current, array $next): int => ((int)$next['quantity']) <=> ((int)$current['quantity']);
        uasort($sortedItems, $sortByQuantityDesc);

        foreach ($sortedItems as $key => $item) {
            $sku = (string)$item['sku'];
            $quantity = (int)min($item['quantity'], $autoAddSkuQuantities[$sku] ?? 0);
            if ($quantity > 0) {
                $autoAddSkuQuantities[$sku] -= $quantity;
                if ($autoAddSkuQuantities[$sku] <= 0) {
                    unset($autoAddSkuQuantities[$sku]);
                }

                $items[$key] = array_merge($item, [
                    'quantity' => $quantity,
                    'line_price' => $quantity * $item['price'],
                ]);
            } else {
                unset($items[$key]);
            }
        }

        $autoAddItems = [];
        foreach ($autoAddSkuQuantities as $sku => $quantity) {
            $autoAddItems[] = [
                'sku' => $sku,
                'quantity' => $quantity,
            ];
        }

        return [$items, $autoAddItems];
    }

    protected static function filterQuantityDiscountItems(array $items, int $orderQuantity): array
    {
        // Preserve the original sort order
        $sortedItems = $items;
        // Discount higher value items first
        $sortByPriceDesc = fn($current, $next) => ((float)$next['price']) <=> ((float)$current['price']);
        uasort($sortedItems, $sortByPriceDesc);

        foreach ($sortedItems as $key => $item) {
            $quantity = (int)min($item['quantity'], $orderQuantity);
            if ($quantity > 0) {
                $orderQuantity -= $quantity;

                $items[$key] = array_merge($item, [
                    'quantity' => $quantity,
                    'line_price' => $quantity * $item['price'],
                ]);
            } else {
                unset($items[$key]);
            }
        }

        return $items;
    }

    public static function calculateItemDiscounts(array $processedRule, array $retailers = []): array
    {
        if ($processedRule['option'] === DiscountOptions::FREE_SHIPPING) {
            return [];
        }

        return array_map(fn(array $item): array => [
            'id' => $item['variant_id'],
            'discount_amount' => $item['total_discount'],
            'discount_quantity' => $item['quantity'],
            'is_product_discount' => static::isProductDiscount($processedRule),
            'retailers' => $retailers,
        ], (array)$processedRule['items']);
    }

    public static function calculateShippingDiscount(array $discountRule, float $shippingAmount): float
    {
        if ($discountRule['option'] !== DiscountOptions::FREE_SHIPPING) {
            return 0.00;
        }

        return static::calculateShippingDiscountAmount((string)$discountRule['option'], (float)$discountRule['option_amount'], $shippingAmount);
    }

    /**
     * @param string $option Any of DiscountOptions::OPTIONS
     * @param float $option_amount Discount value type depending on the selected option
     * @param float $line_price Quantity times unit price of the product before discounts
     * @param float $subtotal Sum of all product line prices before discounts
     * @return float Discount amount to be applied to the line item
     */
    public static function calculateItemDiscountAmount(string $option, float $option_amount, float $line_price, float $subtotal): float
    {
        if ($option === DiscountOptions::PERCENT_OFF) {
            $discountAmount = $line_price * ($option_amount / 100);
        } elseif ($option === DiscountOptions::AMOUNT_OFF) {
            $discountAmount = $line_price * ($subtotal > 0 ? $option_amount / $subtotal : 0);
        } elseif ($option === DiscountOptions::FREE_SHIPPING || $option === DiscountOptions::NO_DISCOUNT) {
            $discountAmount = 0.00;
        } elseif ($option === DiscountOptions::FREE) {
            $discountAmount = $line_price;
        } else {
            triggerWarning(sprintf('Unexpected discount type %s', json_encode($option)));
            $discountAmount = 0.00;
        }

        $discountAmount = min($discountAmount, $line_price);
        $discountAmount = max($discountAmount, 0.00);

        return $discountAmount;
    }

    public static function calculateShippingDiscountAmount(string $option, float $optionAmount, float $shippingAmount): float
    {
        $shippingDiscountAmount = 0.00;
        if ($option === DiscountOptions::FREE_SHIPPING) {
            $excludeShippingAbove = $optionAmount;
            if ($excludeShippingAbove <= 0.00 || $shippingAmount <= $excludeShippingAbove) {
                $shippingDiscountAmount = $shippingAmount;
            }
        } else {
            triggerWarning(sprintf('Unexpected discount type %s', json_encode($option)));
        }

        $shippingDiscountAmount = min($shippingDiscountAmount, $shippingAmount);
        $shippingDiscountAmount = max($shippingDiscountAmount, 0.00);

        return (float)$shippingDiscountAmount;
    }
}
