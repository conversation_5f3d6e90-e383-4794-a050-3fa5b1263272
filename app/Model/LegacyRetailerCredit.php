<?php
App::uses('AppModel', 'Model');

/**
 * LegacyRetailerCredit Model.
 *
 * @property User $User
 * @property User $Retailer
 * @property ManufacturerRetailer $ManufacturerRetailer
 * 
 * @deprecated all retailer credit operations should be directed to RetailerCredit
 */
class LegacyRetailerCredit extends AppModel
{
    public $useTable = 'legacy_retailer_credits';

    public $validate = array(
        'title' => array(
            'rule' => 'notBlank',
            'required' => 'create',
        ),
        'value' => array(
            'numeric' => array(
                'rule' => 'numeric',
                'required' => 'create',
                'message' => 'Invalid transaction value.'
            ),
            'balanceUnderLimit' => array(
                'rule' => 'balanceUnderLimit',
                'message' => 'Transaction value exceeds the remaining credit limit.',
            )
        ),
    );

    public $belongsTo = [
        'User' => [
            'className' => 'User',
            'foreignKey' => 'user_id',
        ],
        'Retailer' => [
            'className' => 'User',
            'foreignKey' => 'retailer_id',
        ],
        'CreditTerm' => [
            'className' => 'CreditTerm',
            'foreignKey' => 'credit_term_id'
        ]
    ];

    public $virtualFields = array(
        'credit' => 'IF(LegacyRetailerCredit.value > 0.00, ABS(LegacyRetailerCredit.value), 0.00)',
        'debit' => 'IF(LegacyRetailerCredit.value < 0.00, ABS(LegacyRetailerCredit.value), 0.00)',
        'order_number' => 'COALESCE(SUBSTRING(LegacyRetailerCredit.title, INSTR(LegacyRetailerCredit.title, "#SE")), title)',
    );

    public function __construct($id = false, $table = null, $ds = null)
    {
        parent::__construct($id, $table, $ds);


        $this->validator()->add('credit_term_id', 'Invalid credit term', array(
            'rule' => 'validateCreditTerm',
            'allowEmpty' => true,
            'on' => 'create'
        ));

        $this->bindModel(array('belongsTo' => array(
            'ManufacturerRetailer' => array(
                'foreignKey' => false,
                'conditions' => array(
                    $this->alias . '.user_id = ManufacturerRetailer.user_id',
                    $this->alias . '.retailer_id = ManufacturerRetailer.retailer_id',
                ),
            )
        )), false);

        /** @var LegacyRetailerCredit $CumulativeCredit */
        $CumulativeCredit = ClassRegistry::init(array('class' => 'LegacyRetailerCredit', 'alias' => 'CumulativeCredit'));
        $this->virtualFields['balance'] = $CumulativeCredit->buildSubquery(array(
            'recursive' => -1,
            'fields' => array('SUM(CumulativeCredit.value)'),
            'conditions' => array(
                $this->alias . '.user_id = CumulativeCredit.user_id',
                $this->alias . '.retailer_id = CumulativeCredit.retailer_id',
                $this->alias . '.created_at >= CumulativeCredit.created_at',
            ),
        ));
    }

    public function validateCreditTerm($check)
    {
        $userId = $this->data[$this->alias]['user_id'] ?? null;
        // valid if not provided
        if (empty($check['credit_term_id'])){
            return true;
        }
        // if these are not provided we cannot validate. If they are missing on create something else went wrong
        if (empty($userId)){
            return false;
        }
        /** @var CreditTerm $CreditTerm */
        $CreditTerm = ClassRegistry::init('CreditTerm');
        $validCreditTerms = $CreditTerm->getAllCreditTermOptions($userId);
        return array_key_exists($check['credit_term_id'], $validCreditTerms);
    }

    /**
     * Wrapper for isBalanceUnderLimit as a custom model validation.
     *
     * This method is only public to allow it to be called by the model
     * validator. It is not expected to be called under any other context.
     *
     * @param array $check Fields under validation. Expecting array('value' => $value).
     * @param array $validator
     * @return bool
     * @see LegacyRetailerCredit::isBalanceUnderLimit
     */
    public function balanceUnderLimit($check, $validator = array())
    {
        return $this->isBalanceUnderLimit($check['value']);
    }

    /**
     * @param float $nextValue A new value being added to the balance
     * @param int|null $userId
     * @param int|null $retailerId
     * @return bool
     */
    public function isBalanceUnderLimit($nextValue, $userId = null, $retailerId = null)
    {
        if (!$userId) {
            $userId = $this->data[$this->alias]['user_id'];
        }
        if (!$retailerId) {
            $retailerId = $this->data[$this->alias]['retailer_id'];
        }
        return ($nextValue <= $this->fetchRemainingBalance($userId, $retailerId));
    }

    /**
     * @param int $userId
     * @param int $retailerId
     * @return string
     */
    public function fetchRemainingBalance($userId, $retailerId)
    {
        $conditions = array('user_id' => $userId, 'retailer_id' => $retailerId);
        $creditLimit = (float)$this->ManufacturerRetailer->field('credit_limit', $conditions);
        $balance = (float)$this->field('balance', $conditions, 'created_at DESC');
        return $creditLimit - $balance;
    }

    public function findTotals($userId, $retailerId)
    {
        $originalVirtualFields = $this->virtualFields;

        $this->virtualFields = array(
            'total_credit' => "SUM({$this->virtualFields['credit']})",
            'total_debit' => "SUM({$this->virtualFields['debit']})",
            'total_balance' => "SUM({$this->alias}.value)",
            'credit_limit' => "ManufacturerRetailer.credit_limit",
            'count' => "COUNT({$this->alias}.id)",
        );
        $this->virtualFields['available_credit'] = "{$this->virtualFields['credit_limit']} - {$this->virtualFields['total_balance']}";

        $totals = $this->find('first', array(
            'recursive' => 0,
            'conditions' => array($this->alias . '.user_id' => $userId, $this->alias . '.retailer_id' => $retailerId),
            'fields' => array_keys($this->virtualFields),
            'order' => $this->alias . '.created_at DESC',
        ));
        if ($totals['LegacyRetailerCredit']['credit_limit'] === null) {
            $totals['LegacyRetailerCredit']['credit_limit'] = $this->ManufacturerRetailer->field('credit_limit', array(
                'ManufacturerRetailer.user_id' => $userId,
                'ManufacturerRetailer.retailer_id' => $retailerId,
            ));
            $totals['LegacyRetailerCredit']['available_credit'] = $totals['LegacyRetailerCredit']['credit_limit'] - $totals['LegacyRetailerCredit']['total_balance'];
        }

        $this->virtualFields = $originalVirtualFields;

        return $totals;
    }

    public function buildTotalsSubquery($conditions = array())
    {
        $group = array($this->alias . '.user_id', $this->alias . '.retailer_id');
        return $this->buildSubquery(array(
            'recursive' => -1,
            'conditions' => $conditions,
            'fields' => array_merge($group, array(
                "SUM({$this->virtualFields['credit']}) AS `total_credit`",
                "SUM({$this->virtualFields['debit']}) AS `total_debit`",
                "SUM({$this->alias}.value) AS `total_balance`",
                "COUNT({$this->alias}.id) AS `count`",
            )),
            'group' => $group,
        ));
    }

    public function getCreditsForIndex($userId, $retailerId, $startDate, $endDate)
    {

        $contain = ['CreditTerm' => ['fields' => ['id', 'description', 'days_due']]];
        $fields = [
            "{$this->alias}.id",
            "{$this->alias}.order_number",
            "{$this->alias}.credit",
            "{$this->alias}.debit",
            "{$this->alias}.balance",
            "{$this->alias}.value",
            "{$this->alias}.created_at",
        ];
        $credits = $this->find('all', array(
            'contain' => $contain,
            'conditions' => array(
                'LegacyRetailerCredit.user_id' => $userId,
                'LegacyRetailerCredit.retailer_id' => $retailerId,
                'LegacyRetailerCredit.created_at BETWEEN ? AND ?' => [$startDate, $endDate],
            ),
            'fields' => $fields,
            'order' => 'LegacyRetailerCredit.created_at DESC',
        ));
        return $credits;
    }

    public function getPaymentStatuses()
    {
        return ['pending', 'paid', 'overdue'];
    }
}
