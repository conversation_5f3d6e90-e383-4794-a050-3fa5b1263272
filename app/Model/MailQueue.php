<?php
App::uses('AppModel', 'Model');

/**
 * MailQueue Model.
 */
class MailQueue extends AppModel
{
    const STATUS_OPEN = '0';
    const STATUS_PROCESSING = '1';
    const STATUS_CLOSED = '2';
    const STATUS_ERROR = '3';

    const FROM_ADMIN = SITE_NAME . '<' . ADMIN_EMAIL . '>';

    public $validate = [
        'status' => [
            'required' => ['rule' => 'notBlank', 'required' => 'create'],
            'valid' => [
                'rule' => ['inList', [self::STATUS_OPEN, self::STATUS_PROCESSING, self::STATUS_CLOSED, self::STATUS_ERROR]],
                'message' => 'Invalid status',
            ],
        ],
        'subject' => ['rule' => 'notBlank', 'required' => 'create'],
        'content' => ['rule' => 'notBlank', 'required' => 'create'],
    ];

    public $recursive = -1;

    /**
     * @param array $mail_data EmailTemplate model after replacing template vars.
     * @param string|string[] $to_email
     * @param string|string[] $cc_mail
     * @param string|string[] $bcc_mail
     * @param string[] $attachments
     * @param string $generateUrl
     * @return bool|mixed
     * @throws Exception
     */
    public function queueEmail($mail_data, $to_email, $cc_mail = array(), $bcc_mail = array(), $attachments = array(), $generateUrl = '')
    {
        $data = [
            'status' => static::STATUS_OPEN,
            'fromemail' => $mail_data['EmailTemplate']['fromEmail'] ?? static::FROM_ADMIN,
            'to' => json_encode($to_email),
            'cc' => json_encode($cc_mail),
            'bcc' => json_encode($bcc_mail),
            'logo' => $mail_data['EmailTemplate']['logo'] ?? '',
            'subject' => $mail_data['EmailTemplate']['subject'],
            'content' => $mail_data['EmailTemplate']['content'],
            'attachments' => json_encode($attachments),
            'generateUrl' => $generateUrl,
        ];

        $data['unique_hash'] = md5(json_encode($data));

        $this->create();
        return $this->save($data);
    }

    public function dequeueEmails()
    {
        $data = $this->find('all', [
            'recursive' => -1,
            'conditions' => ['status' => [static::STATUS_OPEN, static::STATUS_ERROR]],
            'limit' => 100,
        ]);

        $this->updateAll(
            [
                'status' => $this->value(static::STATUS_PROCESSING),
                'unique_hash' => $this->value(null),
            ],
            ['id' => Hash::extract($data, "{n}.{$this->alias}.id")]
        );

        return array_map(function($email) {
            // Records prior to Version1.32 were quoted by json_encode prior to saving
            $email['MailQueue']['fromemail'] = trim($email['MailQueue']['fromemail'], '"');

            if (empty($email['MailQueue']['fromemail'])) {
                $email['MailQueue']['fromemail'] = static::FROM_ADMIN;
            }
            $email['MailQueue']['to'] = array_filter((array)json_decode($email['MailQueue']['to'], true));
            $email['MailQueue']['cc'] = array_filter((array)json_decode($email['MailQueue']['cc'], true));
            $email['MailQueue']['bcc'] = array_filter((array)json_decode($email['MailQueue']['bcc'], true));
            $email['MailQueue']['attachments'] = json_decode($email['MailQueue']['attachments'], true);
            return $email;
        }, $data);
    }

    public function setStatus($id, $status): bool
    {
        return $this->updateAllJoinless(['status' => $this->value($status)], ['id' => $id]);
    }

    public function clearSentEmails(): bool
    {
        return $this->deleteAllJoinless(['status' => static::STATUS_CLOSED], false);
    }
}
