<?php
App::uses('AppModel', 'Model');

/**
 * ManufacturerRetailerNote Model.
 *
 * @property ManufacturerRetailer $ManufacturerRetailer
 * @property User $User
 */
class ManufacturerRetailerNote extends AppModel
{
    public $validate = [
        'manufacturer_retailer_id' => [
            'numeric' => ['rule' => 'numeric', 'message' => 'Invalid manufacturer retailer id'],
        ],
        'content' => [
            'notBlank' => ['rule' => 'notBlank', 'message' => 'This field cannot be left blank'],
        ],
        'author_id' => [
            'numeric' => ['rule' => 'numeric', 'message' => 'Invalid author id'],
        ],
        'author_name' => [
            'notBlank' => ['rule' => 'notBlank', 'message' => 'Author name cannot be empty'],
        ],
        'created_at' => [
            'datetime' => ['rule' => 'datetime', 'message' => 'Invalid datetime'],
        ],
        'updated_at' => [
            'datetime' => ['rule' => 'datetime', 'message' => 'Invalid datetime'],
        ],
    ];

    public $belongsTo = [
        'ManufacturerRetailer',
        'User' => [
            'foreignKey' => 'author_id',
        ],
    ];

    public $recursive = -1;

    public function findForIndex(int $brandId, int $retailerId, int $page, int $pageLimit): array
    {
        return $this->find('all', [
            'contain' => [
                'ManufacturerRetailer' => [
                    'fields' => ['user_id', 'retailer_id'],
                ],
                'User' => [
                    'fields' => ['company_name'],
                ],
            ],
            'conditions' => [
                'ManufacturerRetailer.user_id' => $brandId,
                'ManufacturerRetailer.retailer_id' => $retailerId,
            ],
            'fields' => [
                'id',
                'content',
                'author_name',
                'created_at',
                'updated_at',
            ],
            'order' => [
                'created_at' => 'DESC',
            ],
            'limit' => $pageLimit,
            'page' => $page,
        ]);
    }

    public function countForIndex(int $brandId, int $retailerId)
    {
        return $this->find('count', [
            'contain' => [
                'ManufacturerRetailer' => [
                    'fields' => ['user_id', 'retailer_id'],
                ],
            ],
            'conditions' => [
                'ManufacturerRetailer.user_id' => $brandId,
                'ManufacturerRetailer.retailer_id' => $retailerId,
            ],
        ]);
    }

    public function existsForBrand($id, $brandId): bool
    {
        return (bool)$this->find('count', [
            'contain' => [
                'ManufacturerRetailer',
            ],
            'conditions' => [
                'ManufacturerRetailer.user_id' => $brandId,
                'ManufacturerRetailerNote.id' => $id,
            ],
        ]);
    }

    public function edit(int $id, array $data): bool
    {
        $saveData = [
            'id' => $id,
            'content' => $data['content'],
        ];

        return !empty($this->save($saveData));
    }
}
