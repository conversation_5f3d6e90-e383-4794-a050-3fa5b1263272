<?php

use ShipEarlyApp\Lib\Geolocation\Geolocation;

App::uses('AppModel', 'Model');

/**
 * OrderAddress Model.
 *
 * @property Order $Order
 * @property Country $Country
 * @property State $State
 */
class OrderAddress extends AppModel
{
    const TYPE_SHIPPING = 'shipping';
    const TYPE_BILLING = 'billing';

    public $validate = [
        'order_id' => [
            'naturalNumber' => ['rule' => 'naturalNumber', 'message' => 'Invalid order id'],
        ],
        'type' => [
            'notBlank' => ['rule' => 'notBlank', 'required' => 'create', 'message' => 'This field cannot be left blank'],
        ],
        'first_name' => [
            'notBlank' => ['rule' => 'notBlank', 'required' => 'create', 'message' => 'This field cannot be left blank'],
        ],
        'last_name' => [
            'notBlank' => ['rule' => 'notBlank', 'required' => 'create', 'message' => 'This field cannot be left blank'],
        ],
        'company_name' => [
            'notBlank' => ['rule' => 'notBlank', 'allowEmpty' => true, 'message' => 'This field cannot be left blank'],
        ],
        'address1' => [
            'notBlank' => ['rule' => 'notBlank', 'required' => 'create', 'message' => 'This field cannot be left blank'],
        ],
        'address2' => [
            'notBlank' => ['rule' => 'notBlank', 'allowEmpty' => true, 'message' => 'This field cannot be left blank'],
        ],
        'city' => [
            'notBlank' => ['rule' => 'notBlank', 'required' => 'create', 'message' => 'This field cannot be left blank'],
        ],
        'country_id' => [
            'naturalNumber' => ['rule' => 'naturalNumber', 'required' => 'create', 'message' => 'Invalid country id'],
        ],
        'state_id' => [
            'naturalNumber' => ['rule' => 'naturalNumber', 'required' => 'create', 'message' => 'Invalid state id'],
        ],
        'zipcode' => [
            'notBlank' => ['rule' => 'notBlank', 'required' => 'create', 'message' => 'This field cannot be left blank'],
        ],
        'telephone' => [
            'notBlank' => ['rule' => 'notBlank', 'required' => 'create', 'message' => 'This field cannot be left blank'],
        ],
        'latitude' => [
            'decimal' => ['rule' => 'decimal', 'allowEmpty' => true, 'message' => 'Invalid latitude'],
        ],
        'longitude' => [
            'decimal' => ['rule' => 'decimal', 'allowEmpty' => true, 'message' => 'Invalid longitude'],
        ],
    ];

    public $belongsTo = [
        'Order',
        'Country',
        'State',
    ];

    public $recursive = -1;

    public $virtualFields = [];

    public function initialize(array $config)
    {
        parent::initialize($config);

        $this->virtualFields = array_merge(
            $this->virtualFields,
            $this->getCountryInlineVirtualFields(),
            $this->getStateInlineVirtualFields()
        );
    }

    public function getCountryInlineVirtualFields(): array
    {
        return array_map(
            fn(string $rawField): string => $this->Country->buildSubquery([
                'conditions' => ['Country.id' => $this->identifier("{$this->alias}.country_id")],
                'fields' => [$rawField],
            ]),
            $this->getCountryJoinVirtualFields()
        );
    }

    public function getCountryJoinVirtualFields(string $alias = 'Country'): array
    {
        return [
            'country_code' => "UPPER({$alias}.country_code)",
            'country_name' => "{$alias}.country_name",
        ];
    }

    public function getStateInlineVirtualFields(): array
    {
        return array_map(
            fn(string $rawField): string => $this->State->buildSubquery([
                'conditions' => ['State.id' => $this->identifier("{$this->alias}.state_id")],
                'fields' => [$rawField],
            ]),
            $this->getStateJoinVirtualFields()
        );
    }

    public function getStateJoinVirtualFields(string $alias = 'State'): array
    {
        return [
            'state_code' => "UPPER({$alias}.state_code)",
            'state_name' => "{$alias}.state_name",
        ];
    }

    public static function toGeolocation(array $address): Geolocation
    {
        return new Geolocation($address['latitude'], $address['longitude'], [
            'address1' => $address['address1'],
            'address2' => $address['address2'],
            'city' => $address['city'],
            'country_name' => $address['country_name'],
            'country_code' => $address['country_code'],
            'state_name' => $address['state_name'],
            'state_code' => $address['state_code'],
            'zipcode' => $address['zipcode'],
        ]);
    }

    public function validateEcommerceForm(string $countryCode, array $data, array $options = []): bool
    {
        $countryCode = strtolower($countryCode);

        $this->validator()
            ->add('email_address', 'notBlank', [
                'rule' => 'notBlank',
                'allowEmpty' => true, // Optional for the Stripe payment request button
                'message' => 'This field cannot be left blank',
            ])
            ->add('email_address', 'email', ['rule' => ['email', true], 'message' => 'Please enter a valid email address'])
            ->add('country_code', 'notBlank', ['rule' => 'notBlank', 'message' => 'This field cannot be left blank'])
            ->add('state_code', 'notBlank', [
                'rule' => 'notBlank',
                'allowEmpty' => (bool)$data['state_id'],
                'message' => 'This field cannot be left blank',
            ])
            ->add('zipcode', 'postal', [
                'rule' => ['postal', null, $countryCode],
                'message' => ($countryCode === 'us') ? 'Invalid zip code' : 'Invalid postal code',
            ])
            ->add('telephone', 'phone', [
                'rule' => ['phone', null, $countryCode],
                'message' => 'Please enter a valid phone number',
            ]);

        try {
            return (
                $this->clear() &&
                $this->set($data) &&
                $this->validates($options)
            );
        } finally {
            $this->validator()
                ->remove('telephone', 'phone')
                ->remove('zipcode', 'postal')
                ->remove('state_code')
                ->remove('country_code')
                ->remove('email_address');
        }
    }

    /**
     * Save the billing address for an ecommerce order.
     *
     * Note that the billing address is saved in a separate transaction from the
     * order to prevent address validation errors from blocking order creation.
     *
     * @param int $orderId
     * @param array $billing Preorder.log.billing
     * @return bool Success
     */
    public function saveBillingFromEcommercePreorder(int $orderId, array $billing): bool
    {
        $type = static::TYPE_BILLING;
        $keys = [
            'id' => (int)$this->fieldByConditions('id', ["{$this->alias}.order_id" => $orderId, "{$this->alias}.type" => $type]),
            'order_id' => $orderId,
            'type' => $type,
        ];

        $state = $this->State->findWithCountryName($billing['province']);
        $geopoints = $this->_findGeocode(
            $billing['address'],
            $billing['city'],
            $billing['PostalCode'],
            $state['State']['state_name'],
            $state['State']['country_name']
        );
        $data = [
            'first_name' => $billing['First_name'],
            'last_name' => $billing['Last_name'],
            'company_name' => !empty($billing['company']) ? $billing['company'] : null,
            'address1' => $billing['address'],
            'address2' => !empty($billing['address2']) ? $billing['address2'] : null,
            'city' => $billing['city'],
            'country_id' => $billing['country'],
            'state_id' => $billing['province'],
            'zipcode' => $billing['PostalCode'],
            'telephone' => $billing['phone'],
            'latitude' => $geopoints['lat'] ?? null,
            'longitude' => $geopoints['lng'] ?? null,
        ];

        return (bool)$this->save($keys + $data);
    }

    /**
     * Save the billing address for an ecommerce checkout widget order.
     *
     * Note that the billing address is saved in a separate transaction from the
     * order to prevent address validation errors from blocking order creation.
     *
     * @param int $orderId
     * @param array $billingAddress
     * @return bool Success
     */
    public function saveBillingFromCheckoutWidget(int $orderId, array $billingAddress): bool
    {
        $type = static::TYPE_BILLING;
        $keys = [
            'id' => (int)$this->fieldByConditions('id', ["{$this->alias}.order_id" => $orderId, "{$this->alias}.type" => $type]),
            'order_id' => $orderId,
            'type' => $type,
        ];

        $data = $billingAddress;

        return (bool)$this->save($keys + $data);
    }

    public function saveBillingFromShopifyWebhook($newOrderId, array $billing_address)
    {
        $type = static::TYPE_BILLING;

        $countryCode = $billing_address['country_code'];
        $stateCode = get_shopify_state_code((string)$countryCode, (string)$billing_address['province_code']);

        $state = $this->State->findStateAndCountryByCode($stateCode, $countryCode);

        if (empty($billing_address['latitude']) || empty($billing_address['longitude'])) {
            $geopoints = $this->_findGeocode(
                $billing_address['address1'],
                $billing_address['city'],
                $billing_address['zip'],
                $state['State']['state_name'],
                $state['Country']['country_name']
            );
            $billing_address['latitude'] = $geopoints['lat'] ?? null;
            $billing_address['longitude'] = $geopoints['lng'] ?? null;
        }

        return $this->save([
            'id' => $this->field('id', ['order_id' => $newOrderId, 'type' => $type]),
            'order_id' => $newOrderId,
            'type' => $type,
            'first_name' => $billing_address['first_name'],
            'last_name' => $billing_address['last_name'],
            'company_name' => $billing_address['company'],
            'address1' => $billing_address['address1'],
            'address2' => $billing_address['address2'],
            'city' => $billing_address['city'],
            'country_id' => $state['Country']['id'],
            'state_id' => $state['State']['id'],
            'zipcode' => $billing_address['zip'],
            'telephone' => $billing_address['phone'],
            'latitude' => $billing_address['latitude'],
            'longitude' => $billing_address['longitude'],
        ]);
    }

    public function saveBillingFromWooCommerceWebhook($newOrderId, array $billing_address)
    {
        $type = static::TYPE_BILLING;

        $state = $this->State->findStateAndCountryByCode($billing_address['state'], $billing_address['country']);

        $geopoints = $this->_findGeocode(
            $billing_address['address_1'],
            $billing_address['city'],
            $billing_address['postcode'],
            $state['State']['state_name'],
            $state['Country']['country_name']
        );

        return $this->save([
            'id' => $this->fieldByConditions('id', ['order_id' => $newOrderId, 'type' => $type]),
            'order_id' => $newOrderId,
            'type' => $type,
            'first_name' => $billing_address['first_name'],
            'last_name' => $billing_address['last_name'],
            'company_name' => $billing_address['company'],
            'address1' => $billing_address['address_1'],
            'address2' => $billing_address['address_2'],
            'city' => $billing_address['city'],
            'country_id' => $state['Country']['id'],
            'state_id' => $state['State']['id'],
            'zipcode' => $billing_address['postcode'],
            'telephone' => $billing_address['phone'],
            'latitude' => $geopoints['lat'] ?? null,
            'longitude' => $geopoints['lng'] ?? null,
        ]);
    }
}
