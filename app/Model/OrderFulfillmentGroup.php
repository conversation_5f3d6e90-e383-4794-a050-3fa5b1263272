<?php
App::uses('AppModel', 'Model');

/**
 * OrderFulfillmentGroup Model.
 *
 * @property Order $Order
 */
class OrderFulfillmentGroup extends AppModel
{
    public $validate = [
        'order_id' => [
            'numeric' => ['rule' => 'numeric', 'message' => 'Invalid order id'],
            'isUnique' => ['rule' => 'isUnique', 'message' => 'Order already has a fulfillment group'],
        ],
        'groupID' => [
            'required' => ['rule' => 'notBlank', 'required' => 'create', 'message' => 'This field cannot be left blank'],
            'numeric' => ['rule' => 'numeric', 'required' => 'create', 'message' => 'Invalid groupID'],
        ],
        'created_at' => [
            'datetime' => ['rule' => 'datetime', 'message' => 'Invalid datetime'],
        ],
    ];

    public $belongsTo = [
        'Order',
    ];

    public $recursive = -1;

    public function nextIndex(int $groupID): int
    {
        return 1 + (int)$this->find('count', ['conditions' => ["{$this->alias}.groupID" => $groupID]]);
    }
}
