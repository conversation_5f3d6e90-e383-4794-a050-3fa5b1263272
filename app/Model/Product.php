<?php
App::uses('AppModel', 'Model');
App::uses('User', 'Model');
App::uses('Viewslog', 'Model');
App::uses('B2bCartType', 'Utility');
App::uses('Currency', 'Utility');
App::uses('ProductSellDirect', 'Utility');
App::uses('ProductSourceType', 'Utility');
App::uses('ProductStatus', 'Utility');
App::uses('DiscountOrderOptions', 'Utility/Discount');
App::uses('RangeColumnGroupValidatorBehaviorTrait', 'Model/Behavior/Trait');

/**
 * Class Product
 */
class Product extends AppModel
{
    use RangeColumnGroupValidatorBehaviorTrait;

    /**
     * @deprecated Use ProductStatus::ENABLED instead;
     */
    const STATUS_ENABLED = ProductStatus::ENABLED;
    /**
     * @deprecated Use ProductStatus::DISABLED instead;
     */
    const STATUS_DISABLED = ProductStatus::DISABLED;
    /**
     * @deprecated Use ProductStatus::ORDERED instead;
     */
    const STATUS_ORDERED = ProductStatus::ORDERED;
    /**
     * @deprecated Use ProductStatus::DELETED instead;
     */
    const STATUS_DELETED = ProductStatus::DELETED;
    /**
     * @deprecated Use ProductStatus::PREORDER instead;
     */
    const STATUS_PREORDER = ProductStatus::PREORDER;
    /**
     * @deprecated Use ProductStatus::INCOMPLETE instead;
     */
    const STATUS_INCOMPLETE = ProductStatus::INCOMPLETE;
    /**
     * @deprecated Use ProductStatus::STATUSES instead;
     */
    const STATUSES = ProductStatus::STATUSES;

    /** @deprecated Use ProductSellDirect::EXCLUSIVELY instead. */
    const SELL_DIRECT_EXCLUSIVELY = ProductSellDirect::EXCLUSIVELY;
    /** @deprecated Use ProductSellDirect::UNLESS_BUNDLED instead. */
    const SELL_DIRECT_UNLESS_BUNDLED = ProductSellDirect::UNLESS_BUNDLED;
    /** @deprecated Use ProductSellDirect::IN_STOCK_ONLY instead. */
    const SELL_DIRECT_IN_STOCK_ONLY = ProductSellDirect::IN_STOCK_ONLY;
    /** @deprecated Use ProductSellDirect::ALWAYS instead. */
    const SELL_DIRECT_ALWAYS = ProductSellDirect::ALWAYS;
    /** @deprecated Use ProductSellDirect::UNPROTECTED_TERRITORIES instead. */
    const SELL_DIRECT_UNPROTECTED_TERRITORIES = ProductSellDirect::UNPROTECTED_TERRITORIES;
    /** @deprecated Use ProductSellDirect::RETAIL_EXCLUSIVE instead. */
    const SELL_DIRECT_RETAIL_EXCLUSIVE = ProductSellDirect::RETAIL_EXCLUSIVE;

    public $actsAs = [
        'RangeColumnGroupValidator' => [
            'rangeGroups' => [
                'B2bOrderMinMax' => [
                    'upperLimitColumn' => 'b2b_max_order_quantity',
                    'lowerLimitColumn' => 'b2b_min_order_quantity',
                ],
            ],
        ],
    ];

    public $virtualFields = array(
        'variant_sort' => "COALESCE(Product.sort_order, 0) - FLOOR(COALESCE(Product.sort_order, 0))"
    );

    public $validate = [
        'is_in_stock_only' => [
            'boolean' => ['rule' => 'boolean', 'message' => 'Invalid boolean'],
        ],
        'sell_direct' => [
            'inList' => ['rule' => ['inList', ProductSellDirect::ALL], 'message' => 'Invalid sell_direct option'],
        ],
        'b2b_min_order_quantity' => [
            'naturalNumber' => ['rule' => 'naturalNumber', 'allowEmpty' => true, 'message' => 'Minimum B2B quantity must be a positive integer'],
            'MinLessThanMax' => ['rule' => ['rangeColumnGroup', 'B2bOrderMinMax'], 'message' => 'Maximum B2B Quantity must be greater than or equal to Minimum'],
        ],
        'b2b_max_order_quantity' => [
            'naturalNumber' => ['rule' => 'naturalNumber', 'allowEmpty' => true, 'message' => 'Maximum B2B quantity must be a positive integer'],
            'MinLessThanMax' => ['rule' => ['rangeColumnGroup', 'B2bOrderMinMax'], 'message' => 'Maximum B2B Quantity must be greater than or equal to Minimum'],
        ],
    ];

    public function initialize(array $config)
    {
        parent::initialize($config);

        $this->virtualFields['sell_direct_method'] = "CASE {$this->alias}.sell_direct";
        foreach (ProductSellDirect::getAllOptions() as $method => $name) {
            $this->virtualFields['sell_direct_method'] .= " WHEN {$method} THEN '{$name}'";
        }
        $this->virtualFields['sell_direct_method'] .= ' END';

        /** @var ProductTitle $ProductTitle */
        $ProductTitle = ClassRegistry::init('ProductTitle');

        // The raw `product_handle` column is deprecated
        $this->virtualFields['product_handle'] = $ProductTitle->buildSubquery([
            'conditions' => ['ProductTitle.id' => $this->identifier("{$this->alias}.product_title_id")],
            'fields' => ['handle'],
        ]);

        $productNameSubquery = $ProductTitle->buildSubquery([
            'conditions' => ['ProductTitle.id' => $this->identifier("{$this->alias}.product_title_id")],
            'fields' => ['title'],
        ]);
        $this->virtualFields['product_name'] = "COALESCE({$productNameSubquery}, SUBSTRING_INDEX({$this->alias}.product_title, ' - ', 1))";

        /** @var ProductVariantOption $ProductVariantOption */
        $ProductVariantOption = ClassRegistry::init('ProductVariantOption');
        $variantTitleSubquery = $ProductVariantOption->buildVariantTitleSubquery($this->primaryKeyIdentifier());
        $this->virtualFields['variant_options'] = "COALESCE({$variantTitleSubquery}, TRIM(LEADING ' - ' FROM TRIM(LEADING {$this->virtualFields['product_name']} FROM {$this->alias}.product_title)))";

        // Redefine virtual fields with translated ProductTitle.title after using the raw value to derive variant_options.
        $this->initializeTranslatedVirtualFields();

        /** @var ProductStateFee $ProductStateFee */
        $ProductStateFee = ClassRegistry::init('ProductStateFee');
        $this->virtualFields['is_fee_product'] = $ProductStateFee->buildExistsSubquery([
            'ProductStateFee.fee_product_id' => $this->primaryKeyIdentifier(),
        ]);

        /** @var ProductNonApplicableOrderType $ProductNonApplicableOrderType */
        $ProductNonApplicableOrderType = ClassRegistry::init('ProductNonApplicableOrderType');
        $this->virtualFields['show_on_regular_orders'] = $ProductNonApplicableOrderType->buildNotExistsSubquery([
            'ProductNonApplicableOrderType.product_id' => $this->primaryKeyIdentifier(),
            'ProductNonApplicableOrderType.order_type' => B2bCartType::REGULAR,
        ]);
    }

    /**
     * Redefine virtual fields to look up translations of dependent fields.
     *
     * Expected to be called after 'Config.language' is set by `AppController::beforeFilter()`.
     *
     * @param bool|string|null $locale Optionally override the model locale.
     *      Ignore by setting `false` because `null` is significant.
     * @return void
     */
    public function initializeTranslatedVirtualFields($locale = false): void
    {
        /** @var ProductTitle $ProductTitle */
        $ProductTitle = ClassRegistry::init('ProductTitle');
        $originalProductTitleLocale = $ProductTitle->locale;
        if ($locale !== false) {
            $ProductTitle->locale = isset($locale) ? (string)$locale : null;
        }

        $translatedProductNameSubquery = $ProductTitle->buildSubquery([
            'conditions' => ['ProductTitle.id' => $this->identifier("{$this->alias}.product_title_id")],
            'fields' => ["COALESCE({$ProductTitle->buildTranslatedFieldSubquery('title')}, ProductTitle.title)"],
        ]);
        $this->virtualFields['product_name'] = "COALESCE({$translatedProductNameSubquery}, SUBSTRING_INDEX({$this->alias}.product_title, ' - ', 1))";
        $this->virtualFields['product_title'] = "CONCAT_WS(' - ', {$this->virtualFields['product_name']}, NULLIF({$this->virtualFields['variant_options']}, ''))";

        $this->virtualFields['product_description'] = $ProductTitle->buildSubquery([
            'conditions' => ['ProductTitle.id' => $this->identifier("{$this->alias}.product_title_id")],
            'fields' => ["COALESCE({$ProductTitle->buildTranslatedFieldSubquery('description')}, ProductTitle.description)"],
        ]);

        $ProductTitle->locale = $originalProductTitleLocale;
    }

    /**
     * @param string $user_site_type
     * @return string[]
     * @deprecated Use ProductSellDirect::getSelectOptions() instead.
     */
    public static function getSellDirectMethods($user_site_type = ''): array
    {
        return ProductSellDirect::getSelectOptions((string)$user_site_type);
    }

    /**
     * Extract the highest priority sell_direct option from a set of options.
     *
     * @param string[] $sellDirectOptions
     * @return string
     * @deprecated Use ProductSellDirect::getHighestPriorityOption() instead.
     */
    public static function getHighestPrioritySellDirectOption(array $sellDirectOptions): string
    {
        return ProductSellDirect::getHighestPriorityOption($sellDirectOptions);
    }

    /**
     * @param string $product_status
     * @return string
     * @deprecated Use ProductStatus::getStatusName() instead.
     */
    public static function getStatusName($product_status)
    {
        return ProductStatus::getStatusName($product_status);
    }

    /**
     * @param null|string|string[] $product_type
     * @return string[]
     */
    public static function decodeProductType($product_type): array
    {
        if (is_array($product_type)) {
            return $product_type;
        }
        $product_type = (string)$product_type;

        $typeNames = json_decode($product_type, true);
        if (!is_array($typeNames)) {
            $typeNames = [$product_type];
        }

        return array_filter(array_map('trim', $typeNames), fn($name) => $name !== '');
    }

    /**
     * @param null|string|string[] $variant_options
     * @return string[]
     */
    public static function decodeVariantOptions($variant_options): array
    {
        if (is_array($variant_options)) {
            return $variant_options;
        }
        $variant_options = (string)$variant_options;

        $optionNames = explode(' / ', $variant_options);
        if (!is_array($optionNames)) {
            $optionNames = [$variant_options];
        }

        return array_filter(array_map('trim', $optionNames), fn($name) => $name !== '');
    }

    /**
     * Derive distinct and truthy 'source_product_id' and 'productID' based on product title source type.
     *
     * Only necessary because 'source_product_id' and 'productID' are assumed to not be empty for valid records.
     *
     * @param array $product
     * @param array $productTitle
     * @return array Derived 'source_product_id' and 'productID', or empty to indicate no change.
     */
    public static function deriveSourceIdsFromTitle(array $product, array $productTitle): array
    {
        $id = (int)$product['id'];
        $productID = (int)$product['productID'];
        $sourceProductId = (string)$product['source_product_id'] ?: null;

        $titleHandle = (string)$productTitle['handle'] ?: null;
        $titleSourceType = (string)$productTitle['source_type'] ?: null;
        $titleSourceId = (string)$productTitle['source_id'] ?: null;

        if ($titleSourceType === ProductSourceType::SHIPEARLY) {
            return ['source_product_id' => $titleHandle, 'productID' => $id];
        }

        if ($productID && $titleSourceId === $sourceProductId) {
            // Assume values are from the eCommerce platform and correct.
            return [];
        }

        // Edge case of a product added to an eCommerce title from ShipEarly.
        // Set productID negative to avoid conflicting with positive eCommerce ids.
        return ['source_product_id' => $titleSourceId, 'productID' => -$id];
    }

    public function afterFind($results, $primary = false)
    {
        $results = array_map([$this, '_convertIfLegacySellDirectOption'], $results);

        return parent::afterFind($results, $primary);
    }

    public function beforeValidate($options = [])
    {
        if (
            !array_key_exists('published_at', $this->data[$this->alias] ?? [])
            && ($this->data[$this->alias]['product_status'] ?? null) === ProductStatus::ENABLED
            && $this->field('product_status', ['id' => $this->id]) === ProductStatus::INCOMPLETE
        ) {
            $this->data[$this->alias]['published_at'] = $this->date();
        }

        $this->data = $this->_convertIfLegacySellDirectOption($this->data, 1);
        $this->data = $this->setEmptyStringFieldsToNull($this->data, ['source_product_id']);

        return parent::beforeValidate($options);
    }

    private function _convertIfLegacySellDirectOption(array $data, int $warningStackFrame = 2): array
    {
        if (
            is_numeric($data[$this->alias]['sell_direct'] ?? null) &&
            (int)$data[$this->alias]['sell_direct'] === ProductSellDirect::IN_STOCK_ONLY
        ) {
            deprecationWarning(sprintf(
                'Deprecated %s value "%s"',
                "{$this->alias}.sell_direct",
                ProductSellDirect::getLabel(ProductSellDirect::IN_STOCK_ONLY)
            ), $warningStackFrame);

            $data[$this->alias] = array_merge($data[$this->alias], [
                'is_in_stock_only' => true,
                'sell_direct' => ProductSellDirect::UNPROTECTED_TERRITORIES,
            ]);
        }

        return $data;
    }

    public function afterSave($created, $options = array())
    {
        parent::afterSave($created, $options);
        $this->_clearProductCache($this->id);

        if (
            $created
            && (empty($this->data[$this->alias]['source_product_id']) || empty($this->data[$this->alias]['productID']))
            && ($sourceIds = $this->findSourceIdsDerivedFromTitle($this->id))
        ) {
            $this->updateAllJoinless(array_map([$this, 'value'], $sourceIds), ["{$this->alias}.id" => $this->id]);
        }
    }

    public function beforeDelete($cascade = true)
    {
        return $this->_beforeDeleteAllByIds($this->id) && parent::beforeDelete($cascade);
    }

    public function afterDelete()
    {
        parent::afterDelete();
        $this->_clearProductCache($this->id);
    }

    public function buildSearchCondition($term, $searchFields = [], $query = [])
    {
        if (empty($searchFields)) {
            $searchFields = [
                "{$this->alias}.product_title",
                "{$this->alias}.product_sku",
                "{$this->alias}.product_upc",
                'Tag.name',
            ];
            $query['joins'] = array_merge($query['joins'] ?? [], [
                [
                    'table' => 'product_tags',
                    'alias' => 'ProductTag',
                    'type' => 'LEFT',
                    'conditions' => "{$this->alias}.id = ProductTag.product_id",
                ],
                [
                    'table' => 'tags',
                    'alias' => 'Tag',
                    'type' => 'LEFT',
                    'conditions' => 'ProductTag.tag_id = Tag.id',
                ],
            ]);
        }

        return parent::buildSearchCondition($term, $searchFields, $query);
    }

    public function buildDealerOrderItemSearchCondition(string $term)
    {
        return $this->buildSearchCondition($term, ["{$this->alias}.product_title", "{$this->alias}.product_sku"]);
    }

    /**
     * Remove products that do not match any of the provided product types.
     *
     * @param array $products Each must contain the 'product_type' field.
     * @param array $productTypeMask
     * @return array $products containing any of the types in $productTypeMask
     */
    public function filterTypes(array $products, array $productTypeMask)
    {
        return array_filter($products, function($product) use ($productTypeMask) {
            if (isset($product[$this->alias])) {
                $product = $product[$this->alias];
            }
            $productTypes = self::decodeProductType($product['product_type']);
            foreach ($productTypes as $type) {
                if (in_array($type, $productTypeMask)) {
                    return true;
                }
            }
            return false;
        });
    }

    public function getConditionsForActiveProducts(array $conditions = []): array
    {
        return array_merge($conditions, [
            "{$this->alias}.product_status" => ProductStatus::ENABLED,
            "{$this->alias}.is_fee_product" => false,
            "{$this->alias}.deleted" => false,
        ]);
    }

    /**
     * @param int $id
     * @return array
     */
    public function findForAuthorization(int $id): array
    {
        $this->bindModel(['belongsTo' => ['User']], false);

        $record = (array)$this->find('first', [
            'contain' => ['User'],
            'conditions' => [
                "{$this->alias}.id" => $id,
                "{$this->alias}.deleted" => false,
            ],
            'fields' => [
                "{$this->alias}.id",
                "{$this->alias}.user_id",
                "{$this->alias}.product_title",
                'User.id',
                'User.email_address',
                'User.company_name',
            ],
            'order' => false,
        ]);

        $this->unbindModel(['belongsTo' => ['User']], false);

        return $record;
    }

    /**
     * Update the retailer counts for given product Id in product table.
     *
     * @param int|int[] $productIds
     * @return bool
     */
    public function updateRetailerCount(array $productIds): bool
    {
        /** @var ProductRetailer $ProductRetailer */
        $ProductRetailer = ClassRegistry::init('ProductRetailer');

        $retailerCountSql = $ProductRetailer->buildSubquery([
            'recursive' => -1,
            'conditions' => ["{$ProductRetailer->alias}.product_id" => $this->primaryKeyIdentifier()],
            'fields' => [$ProductRetailer->getDboSource()->calculate($ProductRetailer, 'count')],
        ]);

        return $this->_updateAllById($productIds, ['Product.no_of_retailers' => $this->expression($retailerCountSql)]);
    }

    /**
     * Update the product status as incomplete for given product Id in product table.
     *
     * @param int|int[] $ids
     * @return bool Success
     */
    public function setIncomplete($ids)
    {
        return $this->updateStatusByIds($ids, self::STATUS_INCOMPLETE);
    }

    public function updateStatusByIds($ids, $product_status)
    {
        if (!in_array($product_status, self::STATUSES, true)) {
            return false;
        }
        return $this->_updateAllById($ids, [$this->alias . '.product_status' => $product_status]);
    }

    /**
     * Returns information  about newly added product from product table in array format.
     * @param $cat
     * @param null $limit
     * @param null $offset
     * @return mixed
     */
    public function newlyAddedProduct($cat, $limit = null, $offset = null)
    {
        if (is_array($cat)) {
            $catList = implode(',', array_map('intval', $cat));
        } else {
            $catList = implode(',', array_map('intval', explode(',', $cat)));
        }

        $query = $this->find('all', [
            'recursive' => -1,
            'fields' => [
                'Product.id',
                'Product.productID',
                'Product.product_title',
                'Product.product_description',
                'Product.product_price',
                'Product.product_image',
                'Product.uuid',
                'Product.product_sku',
                'Product.invoice_amount',
                'Product.min_shipping',
                'Product.user_id',
            ],
            'joins' => [
                [
                    'table' => 'product_categories',
                    'alias' => 'ProductCategories',
                    'type' => 'INNER',
                    'conditions' => [
                        'ProductCategories.product_id = Product.id'
                    ]
                ]
            ],
            'conditions' => [
                'Product.deleted' => '0',
                'Product.product_status' => 'Enabled',
                'ProductCategories.cat_id IN (' . $catList . ')',
                'Product.created_at BETWEEN NOW() - INTERVAL 30 DAY AND NOW()'
            ],
            'group' => 'Product.id',
            'order' => ['Product.created_at DESC'],
            'limit' => $limit,
            'offset' => $offset
        ]);

        return $query;
    }

    /**
     * Returns count of newly added product from product table.
     * @param $cat
     * @return mixed
     */
    public function newlyAddedProductCount($cat)
    {
        $productCount = $this->query("SELECT COUNT(DISTINCT `Product`.`id`) as productCount FROM `ship_products` AS `Product` INNER JOIN `ship_product_categories` AS `ProductCategories` ON (`ProductCategories`.`product_id` = `Product`.`id`) WHERE `deleted` = '0' AND `product_status` = 'Enabled' AND `ProductCategories`.`cat_id` IN (" . $cat . ") AND Product.created_at BETWEEN NOW() -INTERVAL 30 DAY AND NOW() ORDER BY `created_at` DESC ");
        return $productCount[0][0]['productCount'];
    }

    /**
     * Returns product Ids for given user Id from product table in array format.
     * @param $userId
     * @return int[]
     */
    public function getProductIds($userId): array
    {
        return array_values(
            (array)$this->find('list', [
                'recursive' => -1,
                'conditions' => ['user_id' => $userId],
                'fields' => ['id', 'id'],
            ])
        );
    }

    /**
     * Returns the product information for the given product Id from product table in array format.
     * @param $productId
     * @param array $fields
     * @return array|mixed
     */
    public function getProductInfo($productId, $fields = array())
    {
        $result = Cache::read('Products_' . $productId, 'product');
        if (!$result) {
            $result = $this->findById($productId);
            Cache::write('Products_' . $productId, $result, 'product');
        }

        if (count($fields)) {
            $result = $this->_filterFields($result, $fields);
        }

        return $result;
    }

    /**
     * Filter the product fields for the given product Id from product table.
     * @param $result
     * @param $fields
     * @return array
     */
    protected function _filterFields($result, $fields)
    {
        $list = array();
        foreach ($result['Product'] as $key => $value) {
            if (in_array($key, $fields)) {
                $list['Product'][$key] = $value;
            }
        }
        return $list;
    }

    public function findAllExistingForWebhookByVariantId(int $userId, int $productTitleId, array $fields = []): array
    {
        return Hash::combine(
            (array)$this->find('all', [
                'recursive' => -1,
                'conditions' => [
                    "{$this->alias}.user_id" => $userId,
                    "{$this->alias}.product_title_id" => $productTitleId,
                ],
                'fields' => ($fields) ? array_merge($fields, ['productID']) : [],
                // In case of duplicate productID values, prefer any not deleted
                'order' => ['Product.deleted' => 'DESC'],
            ]),
            "{n}.{$this->alias}.productID",
            '{n}'
        );
    }

    /**
     * Derive distinct and truthy 'source_product_id' and 'productID' based on product title source type.
     *
     * Only necessary because 'source_product_id' and 'productID' are assumed to not be empty for valid records.
     *
     * @param int $id
     * @return array Derived 'source_product_id' and 'productID', or empty to indicate no change.
     */
    public function findSourceIdsDerivedFromTitle(int $id): array
    {
        // Not using addAssociation on query performed inside afterSave callback.
        $record = $this->record($id, [
            'recursive' => -1,
            'joins' => [
                [
                    'table' => 'product_titles',
                    'alias' => 'ProductTitle',
                    'type' => 'LEFT',
                    'conditions' => ['ProductTitle.id' => $this->identifier("{$this->alias}.product_title_id")],
                ],
            ],
            'fields' => [
                "{$this->alias}.id",
                "{$this->alias}.source_product_id",
                "{$this->alias}.productID",
                'ProductTitle.id',
                'ProductTitle.source_id',
                'ProductTitle.source_type',
                'ProductTitle.handle',
            ],
        ]);

        if (empty($record[$this->alias]['id']) || empty($record['ProductTitle']['id'])) {
            triggerWarning('Product not found where id=' . json_encode($id));

            return [];
        }

        return static::deriveSourceIdsFromTitle($record[$this->alias], $record['ProductTitle']);
    }

    public function assignProductTitleToLegacyProducts(int $userId, string $sourceId, int $productTitleId): bool
    {
        return $this->updateAllJoinless(["{$this->alias}.product_title_id" => $this->value($productTitleId)], [
            "{$this->alias}.product_title_id" => null,
            "{$this->alias}.user_id" => $userId,
            "{$this->alias}.source_product_id" => $sourceId,
        ]);
    }

    public function saveManyFromBrandIndex(int $brandId, array $productsData): bool
    {
        $productsById = Hash::combine($productsData, '{n}.id', '{n}');
        $existingById = $this->findAllExistingForBrandIndexSave($brandId, array_keys($productsById));

        $productsIdsWithCategories = array_keys(array_column($existingById, 'has_categories', 'id'), true);
        $productRetailerAssocs = $this->findAllProductRetailerAssocs($brandId, $productsIdsWithCategories);
        $retailerIdsByProductId = Hash::combine($productRetailerAssocs, '{n}.user_id', '{n}.user_id', '{n}.product_id');

        $records = array_map(
            function($existing) use ($productsById, $retailerIdsByProductId) {
                $id = $existing['id'];
                $product = $productsById[$id];
                $retailerIds = array_values($retailerIdsByProductId[$id] ?? []);

                if (
                    $existing['product_status'] === ProductStatus::INCOMPLETE
                    && $existing['product_sku']
                    && $existing['has_categories']
                ) {
                    $product['product_status'] = ProductStatus::ENABLED;
                }

                /** @see ProductRetailer::saveProductAssoc */
                //FIXME technically wrong because it includes inactive and disconnected retailers but this is consistent
                // with other processes for populating ProductRetailers. Consider replacing with a virtual field.
                $product['no_of_retailers'] = count($retailerIds);

                return [
                    $this->alias => $product,
                    'Retailer' => $retailerIds,
                ];
            },
            // Ensure only valid records are saved
            array_values($existingById)
        );

        $this->bindModel([
            'hasAndBelongsToMany' => [
                'Retailer' => [
                    'className' => 'User',
                    'with' => ['ProductRetailer' => []],
                    'unique' => 'keepExisting',
                ],
            ],
        ], false);

        try {
            return (bool)$this->saveMany($records, ['deep' => true]);
        } finally {
            $this->unbindModel(['hasAndBelongsToMany' => ['Retailer']], false);
        }
    }

    private function findAllExistingForBrandIndexSave(int $brandId, array $productIds): array
    {
        /** @var ProductCategories $ProductCategories */
        $ProductCategories = ClassRegistry::init('ProductCategories');
        $originalVirtualFields = $this->virtualFields;

        try {
            $this->virtualFields['has_categories'] = $ProductCategories->buildExistsSubquery([
                "{$ProductCategories->alias}.product_id = {$this->alias}.id",
            ]);

            return Hash::combine(
                (array)$this->find('all', [
                    'recursive' => -1,
                    'conditions' => [
                        "{$this->alias}.id" => array_filter($productIds),
                        "{$this->alias}.user_id" => $brandId,
                        "{$this->alias}.deleted" => false,
                    ],
                    'fields' => [
                        "{$this->alias}.id",
                        "{$this->alias}.product_status",
                        "{$this->alias}.product_sku",
                        "{$this->alias}.has_categories",
                    ],
                ]),
                "{n}.{$this->alias}.id",
                "{n}.{$this->alias}"
            );
        } finally {
            $this->virtualFields = $originalVirtualFields;
        }
    }

    private function findAllProductRetailerAssocs(int $brandId, array $productsIds): array
    {
        /** @var ProductCategories $ProductCategories */
        $ProductCategories = ClassRegistry::init('ProductCategories');

        $productRetailerAssocs = (array)$this->find('all', [
            'recursive' => -1,
            'joins' => [
                [
                    'table' => 'manufacturer_retailers',
                    'alias' => 'ManufacturerRetailer',
                    'type' => 'INNER',
                    'conditions' => [
                        "ManufacturerRetailer.user_id = {$this->alias}.user_id",
                        /** @see UserCategories::getRetailers */
                        //FIXME there should be conditions to filter for "connected and active retailers" but this is
                        // consistent with other processes for populating ProductRetailers. Doing so will require adding
                        // handlers for activation/deactivation and connection/disconnection events. Actions that check
                        // for ProductRetailer relations should already be checking for those constraints.
                    ],
                ],
            ],
            'conditions' => [
                "{$this->alias}.id" => array_filter($productsIds),
                "{$this->alias}.user_id" => $brandId,
                "{$this->alias}.deleted" => false,
                $ProductCategories->buildRetailerCategoryMatchCondition(
                    $this->primaryKeyIdentifier(),
                    $this->identifier('ManufacturerRetailer.retailer_id')
                ),
            ],
            'fields' => ["DISTINCT {$this->alias}.id", 'ManufacturerRetailer.retailer_id'],
        ]);

        return array_map(function($assoc) {
            return [
                'product_id' => $assoc[$this->alias]['id'],
                'user_id' => $assoc['ManufacturerRetailer']['retailer_id'],
            ];
        }, $productRetailerAssocs);
    }

    public function findForEdit(int $id): array
    {
        $this->addAssociations([
            'belongsTo' => ['User'],
            'belongsToMany' => ['Tag' => ['with' => 'ProductTag', 'unique' => 'keepExisting']],
        ]);
        $record = (array)$this->record($id, [
            'contain' => [
                'User' => [
                    'fields' => ['id', 'site_type', 'admin_sell_direct', 'sell_direct'],
                ],
                'Tag' => [
                    'with' => ['ProductTag' => []],
                    'fields' => ['name'],
                    'order' => ['Tag.name' => 'ASC'],
                ],
            ],
            'fields' => [
                'id',
                'uuid',
                'user_id',
                'product_title',
                'product_sku',
                'product_upc',
                'sales_commission',
                'is_in_stock_only',
                'non_stocking',
                'enable_b2b_oversell',
                'installation_hours',
                'product_status',
                'b2b_min_order_quantity',
                'b2b_max_order_quantity',
                'sell_direct',
                'show_on_regular_orders',
            ],
        ]);
        $this->unbindModel([
            'belongsTo' => ['User'],
            'hasAndBelongsToMany' => ['Tag'],
        ], false);

        return $record;
    }

    public function saveFromEdit(int $id, array $data): bool
    {
        $saveData = [
            //FIXME the products DB table does not auto-update created_at/updated_at columns
            $this->alias => ['id' => $id] + $data['ProductInvoice'] + ['updated_at' => $this->date()],
        ];

        $showOnRegularOrders = isset($saveData[$this->alias]['show_on_regular_orders']) ? (bool)$saveData[$this->alias]['show_on_regular_orders'] : null;
        unset($saveData[$this->alias]['show_on_regular_orders']);

        $deleteNonApplicableOrderTypeConditions = [];
        if ($showOnRegularOrders !== null && $showOnRegularOrders !== (bool)$this->fieldByConditions('show_on_regular_orders', ['id' => $id])) {
            $nonApplicableOrderType = ['product_id' => $id, 'order_type' => B2bCartType::REGULAR];
            if ($showOnRegularOrders) {
                $deleteNonApplicableOrderTypeConditions = $nonApplicableOrderType;
            } else {
                $saveData['ProductNonApplicableOrderType'][] = $nonApplicableOrderType;
            }
        }

        $fieldList = [
            $this->alias => [
                'product_sku',
                'product_upc',
                'sales_commission',
                'is_in_stock_only',
                'non_stocking',
                'enable_b2b_oversell',
                'installation_hours',
                'updated_at',
                'b2b_min_order_quantity',
                'b2b_max_order_quantity',
                'no_of_retailers',
                'store_pickup_option',
                'assembly_option',
                'ship_from_store',
            ],
            'ProductNonApplicableOrderType' => [
                'product_id',
                'order_type',
            ],
        ];

        $this->addAssociations([
            'hasMany' => [
                'ProductNonApplicableOrderType',
            ],
        ]);

        try {
            if (!$this->validateAssociated($saveData, ['fieldList' => $fieldList])) {
                return false;
            }

            $success = true;
            if ($deleteNonApplicableOrderTypeConditions) {
                /** @var ProductNonApplicableOrderType $ProductNonApplicableOrderType */
                $ProductNonApplicableOrderType = $this->ProductNonApplicableOrderType;

                $success = $ProductNonApplicableOrderType->deleteAllJoinless($deleteNonApplicableOrderTypeConditions, false);
            }

            return $success && $this->saveAssociated($saveData, ['fieldList' => $fieldList, 'validate' => false]);
        } finally {
            $this->unbindModel([
                'hasMany' => [
                    'ProductNonApplicableOrderType',
                ],
            ], false);
        }
    }

    public function findForPricingEdit(int $id): array
    {
        /** @var PricingTier $PricingTier */
        $PricingTier = ClassRegistry::init('PricingTier');
        /** @var User $User */
        $User = ClassRegistry::init('User');
        /** @var UserCurrency $UserCurrency */
        $UserCurrency = ClassRegistry::init('UserCurrency');

        $PricingTier->bindModel([
            'hasOne' => [
                'ProductTier' => [
                    'foreignKey' => 'pricingtierid',
                    'conditions' => [
                        'ProductTier.product_id' => $id,
                    ],
                ],
            ],
        ], false);

        $UserCurrency->bindModel([
            'hasOne' => [
                'ProductPrice' => [
                    'foreignKey' => 'currency_code',
                    'conditions' => [
                        'ProductPrice.product_id' => $id,
                    ],
                ],
            ],
        ], false);
        $originalUserCurrencyPrimaryKey = $UserCurrency->primaryKey;
        $UserCurrency->primaryKey = 'currency_code';

        try {
            $product = $this->find('first', [
                'recursive' => -1,
                'conditions' => ["{$this->alias}.id" => $id],
                'fields' => ['id', 'user_id', 'product_sku', 'product_price', 'compare_at_price', 'currency'],
            ]);

            if (!empty($product[$this->alias]['id'])) {
                $brandId = $product[$this->alias]['user_id'];

                $product[$this->alias]['currency'] = $product[$this->alias]['currency'] ?: $User->field('currency_code', ['User.id' => $brandId]);

                // Structure like hasAndBelongsToMany but include records not yet mapped to products
                $product['PricingTier'] = array_map(
                    function($tier) {
                        return $tier['PricingTier'] + ['ProductTier' => $tier['ProductTier']];
                    },
                    $PricingTier->find('all', [
                        'contain' => [
                            'ProductTier' => ['fields' => ['id', 'pricingtierid', 'product_id', 'dealer_price', 'dealer_base_price', 'alt_nonstock_dealer_price', 'commission']],
                        ],
                        'conditions' => ['PricingTier.user_id' => $brandId],
                        'fields' => ['id', 'user_id', 'pricingtiername', 'currencytype'],
                        'order' => ['PricingTier.pricingtiername' => 'ASC'],
                    ])
                );
                $product['UserCurrency'] = array_map(
                    function($price) {
                        return $price['UserCurrency'] + ['ProductPrice' => $price['ProductPrice']];
                    },
                    $UserCurrency->find('all', [
                        'contain' => [
                            'ProductPrice' => ['fields' => ['id', 'product_id', 'currency_code', 'price', 'compare_at_price']],
                        ],
                        'conditions' => ['UserCurrency.user_id' => $brandId],
                        'fields' => ['id', 'user_id', 'currency_code'],
                        'order' => ['UserCurrency.currency_code' => 'ASC'],
                    ])
                );

                $basePrice = [
                    'id' => null,
                    'user_id' => $product[$this->alias]['user_id'],
                    'currency_code' => $product[$this->alias]['currency'],
                    'ProductPrice' => [
                        'id' => null,
                        'product_id' => $product[$this->alias]['id'],
                        'currency_code' => $product[$this->alias]['currency'],
                        'price' => $product[$this->alias]['product_price'],
                        'compare_at_price' => $product[$this->alias]['compare_at_price'],
                    ]
                ];
                $product['UserCurrency'] = array_merge([$basePrice], $product['UserCurrency']);
            }

            return (array)$product;
        } finally {
            $UserCurrency->primaryKey = $originalUserCurrencyPrimaryKey;
            $UserCurrency->unbindModel(['hasOne' => ['ProductPrice']], false);
            $PricingTier->unbindModel(['hasOne' => ['ProductTier']], false);
        }
    }

    public function saveFromPricingEdit(int $id, array $formData): bool
    {
        /** @var UserCurrency $UserCurrency */
        $UserCurrency = ClassRegistry::init('UserCurrency');
        $brandCurrencies = $UserCurrency->find('list', [
            'recursive' => -1,
            'joins' => [
                [
                    'table' => $this->table,
                    'alias' => $this->alias,
                    'type' => 'INNER',
                    'conditions' => ["{$this->alias}.user_id = UserCurrency.user_id"],
                ]
            ],
            'conditions' => ["{$this->alias}.id" => $id],
            'fields' => ['UserCurrency.currency_code', 'UserCurrency.currency_code'],
        ]);

        $this->bindModel(['hasMany' => ['ProductPrice', 'ProductTier']], false);
        /** @var ProductPrice $ProductPrice */
        $ProductPrice = $this->ProductPrice;
        /** @var ProductTier $ProductTier */
        $ProductTier = $this->ProductTier;

        $originalProductTierProductIdValidation = $ProductTier->validator()->getField('product_id');
        $ProductTier->validator()->remove('product_id');

        try {
            $prices = array_column($formData['UserCurrency'], 'ProductPrice');
            $prices = array_filter($prices, function($price) use ($brandCurrencies) {
                return in_array($price['currency_code'], $brandCurrencies, true);
            });
            $priceIdsByCurrencyCode = $ProductPrice->find('list', [
                'recursive' => -1,
                'conditions' => [
                    'ProductPrice.product_id' => $id,
                    'ProductPrice.currency_code' => array_column($prices, 'currency_code'),
                ],
                'fields' => ['ProductPrice.currency_code', 'ProductPrice.id'],
            ]);
            $prices = array_map(function($price) use ($priceIdsByCurrencyCode) {
                return ['id' => $priceIdsByCurrencyCode[$price['currency_code']] ?? null] + $price;
            }, $prices);

            $tiers = array_column($formData['PricingTier'], 'ProductTier');
            $tierIdsByPricingTierId = $ProductTier->find('list', [
                'recursive' => -1,
                'conditions' => [
                    'ProductTier.product_id' => $id,
                    'ProductTier.pricingtierid' => array_column($tiers, 'pricingtierid'),
                ],
                'fields' => ['ProductTier.pricingtierid', 'ProductTier.id'],
            ]);
            $tiers = array_map(function($tier) use ($tierIdsByPricingTierId) {
                return ['id' => $tierIdsByPricingTierId[$tier['pricingtierid']] ?? null] + $tier;
            }, $tiers);

            $saveData = [
                'Product' => ['id' => $id],
                'ProductPrice' => $prices,
                'ProductTier' => $tiers,
            ];
            $fieldList = [
                'ProductPrice' => ['currency_code', 'price', 'compare_at_price'],
                'ProductTier' => ['pricingtierid', 'dealer_price', 'dealer_base_price', 'alt_nonstock_dealer_price', 'commission'],
            ];

            return (
                $ProductPrice->deleteAll(['ProductPrice.product_id' => $id, 'ProductPrice.id !=' => array_filter(array_column($saveData['ProductPrice'], 'id'))], false) &&
                $ProductTier->deleteAll(['ProductTier.product_id' => $id, 'ProductTier.id !=' => array_filter(array_column($saveData['ProductTier'], 'id'))], false) &&
                $this->saveAssociated($saveData, compact('fieldList'))
            );
        } finally {
            $ProductTier->validator()->add('product_id', $originalProductTierProductIdValidation);
            $this->unbindModel(['hasMany' => ['ProductPrice', 'ProductTier']], false);
        }
    }

    public function countRetailerIndex($storeId, $brandIds, $collectionIds, $productTypes, $variantOptions, $search, $sort, $orderType, $stockOption, $warehouseId, $discount)
    {
        return $this->_retailerIndexQuery($storeId, $brandIds, $collectionIds, $productTypes, $variantOptions, $search, $sort, $orderType, $stockOption, $warehouseId, $discount);
    }

    public function findAllForRetailerIndex($storeId, $brandIds, $collectionIds, $types, $variants, $search, $sort, $discount, $noRecords, $page, $orderType, $stockOption, $warehouseId): array
    {
        $products = $this->getRetailerIndex($storeId, $brandIds, $collectionIds, $types, $variants, $search, $sort, $discount, $noRecords, $page, $orderType, $stockOption, $warehouseId);
        $products = $this->getRetailerIndexAggregates($products, $storeId, $variants, $warehouseId, $discount, $orderType);

        return (array)$products;
    }

    public function findForCatalogueVariants($productId, $storeId, $variants, $discount, $orderType, $stockOption, $warehouseId): array
    {
        $product = $this->getCatalogueVariantsTitle($productId, $storeId, $warehouseId);

        $products = [$product];
        $products = $this->getRetailerIndexVariants($products, $storeId, $variants, $warehouseId, $discount, $orderType, $stockOption);
        $product = Hash::combine($products, "{n}.{$this->alias}.id", '{n}')[$productId] ?? [];

        return (array)$product;
    }

    public function getRetailerIndex($storeId, $brandIds, $collectionIds, $productTypes, $variantOptions, $search, $sort, $discount, $limit, $page, $orderType, $stockOption, $warehouseId)
    {
        $products = $this->_retailerIndexQuery($storeId, $brandIds, $collectionIds, $productTypes, $variantOptions, $search, $sort, $orderType, $stockOption, $warehouseId, $discount, 'all', [
            'fields' => [
                'Product.id',
                'Product.uuid',
                'Product.user_id',
                'Product.source_product_id',
                'Product.productID',
                'Product.product_sku',
                'Product.product_title',
                'Product.product_upc',
                'Product.product_image',
                'Product.brand_inventory',
                'Product.product_price',
                'Product.product_name',
                'Product.dealer_price',
                'Product.b2b_min_order_quantity',
                'Product.b2b_max_order_quantity',
                'User.id',
                'User.company_name',
                'User.currency_code',
                'User.enable_b2b_cart',
                'ManufacturerRetailer.id',
                'ManufacturerRetailer.warehouse_id',
                'PricingTier.currencytype',
            ],
            'limit' => $limit,
            'page' => $page,
        ]);

        return array_map(function($product) {
            $product[$this->alias]['product_upc'] = $product[$this->alias]['product_upc'] ?: 'No UPC';
            $product[$this->alias] = $this->_processViewProduct($product[$this->alias]);
            return $product;
        }, $products);
    }

    public function getRetailerIndexAggregates(array $products, $storeId, $variantOptions, $warehouseId, $discount, $orderType): array
    {
        /** @var Discount $Discount */
        $Discount = ClassRegistry::init('Discount');
        /** @var ProductPrice $ProductPrice */
        $ProductPrice = ClassRegistry::init('ProductPrice');

        $this->addAssociations([
            'belongsTo' => ['User'],
            'hasOne' => [
                'WarehouseProduct' => ['conditions' => ['WarehouseProduct.warehouse_id' => $warehouseId]],
            ],
        ]);
        $originalVirtualFields = $this->virtualFields;

        /** @var WarehouseProduct $WarehouseProduct */
        $WarehouseProduct = $this->WarehouseProduct;

        try {
            $isInWarehouseQuery = ($warehouseId)
                ? 'WarehouseProduct.id IS NOT NULL'
                : 'TRUE';

            // Used in stock option condition
            $this->virtualFields['brand_inventory'] = ($warehouseId)
                ? $WarehouseProduct->virtualFields['available_quantity']
                : "{$this->alias}.brand_inventory";

            $this->virtualFields['can_add_to_cart'] = "User.enable_b2b_cart AND SUM(ProductTier.dealer_price IS NOT NULL AND ProductTier.dealer_price >= 0 AND {$isInWarehouseQuery})";
            // Counting NULL as in-stock
            $this->virtualFields['count_instock'] = "SUM(COALESCE({$this->virtualFields['brand_inventory']}, 1) > 0)";
            $this->virtualFields['count_total'] = 'COUNT(*)';
            $this->virtualFields['lowest_price'] = "COALESCE(MIN(IF(ProductTier.dealer_price > 0, ProductTier.dealer_price, NULL)), 0)";
            $this->virtualFields['highest_price'] = "COALESCE(MAX(IF(ProductTier.dealer_price > 0, ProductTier.dealer_price, NULL)), 0)";
            $this->virtualFields['lowest_price_currency'] = 'PricingTier.currencytype';
            $this->virtualFields['lowest_retail_price'] = "COALESCE(MIN(ProductPrice.price), MIN({$this->alias}.product_price), 0)";
            $this->virtualFields['highest_retail_price'] = "COALESCE(MAX(ProductPrice.price), MAX({$this->alias}.product_price), 0)";
            $this->virtualFields['lowest_b2b_min_order_quantity'] = "COALESCE(MIN({$this->alias}.b2b_min_order_quantity), 0)";
            $this->virtualFields['highest_b2b_max_order_quantity'] = "COALESCE(MAX({$this->alias}.b2b_max_order_quantity), 0)";

            // Placeholders for lowest price assocs that are fetched later
            $this->virtualFields['lowest_price_discount'] = 'NULL';
            $this->virtualFields['lowest_retail_price_currency'] = 'NULL';
            // Placeholders for highest price assocs that are fetched later
            $this->virtualFields['highest_price_discount'] = 'NULL';
            // Resultant fields to help with fetching lowest price assocs
            $this->virtualFields['variant_ids'] = "CONCAT('[', GROUP_CONCAT({$this->alias}.id ORDER BY {$this->alias}.id ASC SEPARATOR ', '), ']')";
            $this->virtualFields['variant_currency'] = "IF({$this->alias}.currency, {$this->alias}.currency, User.currency_code)";

            $retailPriceSql = "COALESCE(ProductPrice.price, {$this->alias}.product_price, 0)";
            $dealerPriceSql = 'COALESCE(ProductTier.dealer_price, 0)';
            $marginSql = "IF({$retailPriceSql} > 0 AND {$dealerPriceSql} > 0, ROUND(({$retailPriceSql} - {$dealerPriceSql}) / {$retailPriceSql} * 100, 2), NULL)";
            $this->virtualFields['lowest_margin'] = "COALESCE(MIN({$marginSql}), 0)";
            $this->virtualFields['highest_margin'] = "COALESCE(MAX({$marginSql}), 0)";

            $sourceProductIdsByBrandId = Hash::combine($products, "{n}.{$this->alias}.source_product_id", "{n}.{$this->alias}.source_product_id", "{n}.{$this->alias}.user_id");

            $conditions = $this->getConditionsForActiveProducts([
                'OR' => array_map(function($brandId) use ($sourceProductIdsByBrandId) {
                    return [
                        "{$this->alias}.user_id" => $brandId,
                        "{$this->alias}.source_product_id" => array_values($sourceProductIdsByBrandId[$brandId]),
                    ];
                }, array_keys($sourceProductIdsByBrandId)),
            ]);
            $conditions = $this->_setVariantOptionsCondition($variantOptions, $conditions);
            $conditions = $this->_setNonApplicableOrderTypesCondition($orderType, $conditions);
            $conditions = $this->_setB2bDiscountFilterCondition((array)$discount, $conditions);

            $baseQuery = [
                'contain' => ['User', 'WarehouseProduct'],
                'joins' => [
                    [
                        'table' => 'product_prices',
                        'alias' => 'ProductPrice',
                        'type' => 'LEFT',
                        'conditions' => [
                            "ProductPrice.product_id = {$this->alias}.id",
                            'ProductPrice.currency_code = PricingTier.currencytype',
                        ],
                    ],
                ],
                'conditions' => $conditions,
                'fields' => [
                    "{$this->alias}.user_id",
                    "{$this->alias}.source_product_id",
                    "{$this->alias}.count_instock",
                    "{$this->alias}.count_total",
                    "{$this->alias}.lowest_price",
                    "{$this->alias}.highest_price",
                    "{$this->alias}.lowest_price_discount",
                    "{$this->alias}.highest_price_discount",
                    "{$this->alias}.lowest_price_currency",
                    "{$this->alias}.lowest_retail_price",
                    "{$this->alias}.highest_retail_price",
                    "{$this->alias}.lowest_retail_price_currency",
                    "{$this->alias}.lowest_b2b_min_order_quantity",
                    "{$this->alias}.highest_b2b_max_order_quantity",
                    "{$this->alias}.lowest_margin",
                    "{$this->alias}.highest_margin",
                    "{$this->alias}.can_add_to_cart",
                    "{$this->alias}.variant_ids",
                    "{$this->alias}.variant_currency",
                ],
                'group' => ["{$this->alias}.user_id", "{$this->alias}.source_product_id"],
            ];

            $variantAggregates = (array)$this->findWithDealerPricing($storeId, 'all', $baseQuery);
        } finally {
            $this->virtualFields = $originalVirtualFields;
            $this->unbindModel([
                'belongsTo' => ['User'],
                'hasOne' => ['WarehouseProduct'],
            ], false);
        }

        $variantAggregates = array_map(function(array $aggregate): array {
            $aggregate[$this->alias]['variant_ids'] = (array)json_decode_if_array($aggregate[$this->alias]['variant_ids']);

            return $aggregate;
        }, $variantAggregates);

        $lowestRetailPriceCurrencyOrConditions = array_values(array_filter(
            array_map(fn(array $aggregate): array => [
                'ProductPrice.product_id' => $aggregate[$this->alias]['variant_ids'],
                'ProductPrice.currency_code' => $aggregate[$this->alias]['lowest_price_currency'],
                'ProductPrice.price' => $aggregate[$this->alias]['lowest_retail_price'],
            ], $variantAggregates),
            fn(array $conditions): bool => ($conditions['ProductPrice.product_id'] && $conditions['ProductPrice.currency_code'])
        ));
        $lowestRetailPriceCurrencyMap = [];
        if ($lowestRetailPriceCurrencyOrConditions) {
            $lowestRetailPriceCurrencyMap = (array)$ProductPrice->find('list', [
                'contain' => ['Product'],
                'conditions' => ['OR' => $lowestRetailPriceCurrencyOrConditions],
                'fields' => ['Product.source_product_id', 'ProductPrice.currency_code', 'Product.user_id'],
                'group' => ['Product.user_id', 'Product.source_product_id'],
                'order' => false,
            ]);
        }

        $discountItemsMap = [];
        if (($discount['is_b2b_discount'] ?? false)) {
            $this->bindModel([
                'hasAndBelongsToMany' => [
                    'Collection' => ['with' => 'CollectionsProduct', 'unique' => 'keepExisting'],
                    'Tag' => ['with' => 'ProductTag', 'unique' => 'keepExisting'],
                ],
            ], false);
            $allDiscountItems = (array)$this->findWithDealerPricing($storeId, 'all', [
                'contain' => [
                    'Collection' => [
                        'with' => ['CollectionsProduct' => []],
                        'fields' => ['title'],
                    ],
                    'Tag' => [
                        'with' => ['ProductTag' => []],
                        'fields' => ['name'],
                    ],
                ],
                'conditions' => [
                    "{$this->alias}.id" => Hash::extract($variantAggregates, "{n}.{$this->alias}.variant_ids.{n}"),
                ],
                'fields' => [
                    'id',
                    'user_id',
                    'source_product_id',
                    'product_type',
                    'product_sku',
                    'product_name',
                    'dealer_price',
                    'product_price',
                    'is_fee_product',
                ],
            ]);
            $this->unbindModel([
                'hasAndBelongsToMany' => [
                    'Collection',
                    'Tag',
                ],
            ], false);

            $allDiscountItems = $this->nestAssociations($allDiscountItems, ['Collection', 'Tag']);
            $allDiscountItems = array_column($allDiscountItems, $this->alias);

            foreach ($allDiscountItems as $item) {
                $discountItemsMap[$item['user_id']][$item['source_product_id']][] = $item;
            }
        }

        $variantAggregates = array_map(function($aggregate) use ($storeId, $discount, $lowestRetailPriceCurrencyMap, $discountItemsMap, $Discount) {
            // Front end helpers expect this field to be an array
            $aggregate[$this->alias]['lowest_price_discount'] = [];
            $aggregate[$this->alias]['highest_price_discount'] = [];

            $variantIds = (array)$aggregate[$this->alias]['variant_ids'];
            unset($aggregate[$this->alias]['variant_ids']);

            $variantCurrency = (string)$aggregate[$this->alias]['variant_currency'];
            unset($aggregate[$this->alias]['variant_currency']);

            if (!$variantIds) {
                return $aggregate;
            }

            $userId = (int)$aggregate[$this->alias]['user_id'];
            $sourceProductId = (string)$aggregate[$this->alias]['source_product_id'] ?: null;

            $aggregate[$this->alias]['lowest_retail_price_currency'] = $lowestRetailPriceCurrencyMap[$userId][$sourceProductId] ?? $variantCurrency;

            if (!($discount['is_b2b_discount'] ?? false)) {
                return $aggregate;
            }

            $items = (array)($discountItemsMap[$userId][$sourceProductId] ?: []);
            if (!$items) {
                return $aggregate;
            }

            $items = $Discount->calculateProductListItemDiscounts($userId, (array)$discount, $items);

            $items = array_map(function($item) {
                $discountAmount = ($item['discount']['is_product_discount'] ?? false) ? $item['discount']['discount_amount'] : 0;

                $item['dealer_price'] = $item['dealer_price'] ?? 0;
                $item['discounted_dealer_price'] = $item['dealer_price'] - $discountAmount;

                $retailPrice = $item['product_price'] ?? 0;
                $margin = 0;
                if ($retailPrice > 0 && $item['discounted_dealer_price'] > 0) {
                    $margin = (($retailPrice - $item['discounted_dealer_price']) / $retailPrice) * 100;
                }
                $item['margin'] = $margin;

                return $item;
            }, $items);
            $lowestMarginItem = array_reduce($items, function($lowestMarginItem, $item) {
                return (
                    empty($lowestMarginItem) ||
                    ($item['margin'] < $lowestMarginItem['margin'])
                ) ? $item : $lowestMarginItem;
            }, []);
            $highestMarginItem = array_reduce($items, function($highestMarginItem, $item) {
                return (
                    empty($highestMarginItem) ||
                    ($item['margin'] > $highestMarginItem['margin'])
                ) ? $item : $highestMarginItem;
            }, []);
            $lowestPricedItem = array_reduce($items, function($lowestPricedItem, $item) {
                return (
                    empty($lowestPricedItem) ||
                    ($item['dealer_price'] > 0 && $item['discounted_dealer_price'] < $lowestPricedItem['discounted_dealer_price'])
                ) ? $item : $lowestPricedItem;
            }, []);
            $highestPricedItem = array_reduce($items, function($highestPricedItem, $item) {
                return (
                    empty($highestPricedItem) ||
                    ($item['dealer_price'] > 0 && $item['discounted_dealer_price'] > $highestPricedItem['discounted_dealer_price'])
                ) ? $item : $highestPricedItem;
            }, []);

            $aggregate[$this->alias]['lowest_margin'] = format_number($lowestMarginItem['margin']);
            $aggregate[$this->alias]['highest_margin'] = format_number($highestMarginItem['margin']);

            $aggregate[$this->alias]['lowest_price'] = format_number($lowestPricedItem['dealer_price']);
            $aggregate[$this->alias]['lowest_price_discount'] = $lowestPricedItem['discount'] ?? [];

            $aggregate[$this->alias]['highest_price'] = format_number($highestPricedItem['dealer_price']);
            $aggregate[$this->alias]['highest_price_discount'] = $highestPricedItem['discount'] ?? [];

            return $aggregate;
        }, $variantAggregates);

        $variantAggregatesMap = Hash::combine($variantAggregates, "{n}.{$this->alias}.source_product_id", "{n}.{$this->alias}", "{n}.{$this->alias}.user_id");

        return array_map(function($product) use ($variantAggregatesMap) {
            $brandId = $product[$this->alias]['user_id'];
            $sourceProductId = $product[$this->alias]['source_product_id'];

            $product[$this->alias] += $variantAggregatesMap[$brandId][$sourceProductId];

            return $product;
        }, $products);
    }

    public function getCatalogueVariantsTitle($productId, $storeId, $warehouseId): array
    {
        $this->addAssociations([
            'belongsTo' => ['User'],
            'hasOne' => [
                'WarehouseProduct' => ['conditions' => ['WarehouseProduct.warehouse_id' => $warehouseId]],
            ],
            'belongsToMany' => [
                'Collection' => ['with' => 'CollectionsProduct', 'unique' => 'keepExisting'],
                'Tag' => ['with' => 'ProductTag', 'unique' => 'keepExisting'],
            ],
        ]);
        $originalVirtualFields = $this->virtualFields;

        /** @var WarehouseProduct $WarehouseProduct */
        $WarehouseProduct = $this->WarehouseProduct;

        try {
            $this->virtualFields['brand_inventory'] = ($warehouseId)
                ? $WarehouseProduct->virtualFields['available_quantity']
                : "{$this->alias}.brand_inventory";

            $product = $this->findWithDealerPricing($storeId, 'first', [
                'contain' => [
                    'User' => ['fields' => ['id', 'company_name', 'currency_code', 'enable_b2b_cart']],
                    'WarehouseProduct',
                    'Collection' => [
                        'with' => ['CollectionsProduct' => []],
                        'fields' => ['title'],
                    ],
                    'Tag' => [
                        'with' => ['ProductTag' => []],
                        'fields' => ['name'],
                    ],
                ],
                'conditions' => ["{$this->alias}.id" => $productId],
                'fields' => [
                    "{$this->alias}.id",
                    "{$this->alias}.uuid",
                    "{$this->alias}.user_id",
                    "{$this->alias}.source_product_id",
                    "{$this->alias}.productID",
                    "{$this->alias}.product_sku",
                    "{$this->alias}.product_title",
                    "{$this->alias}.product_upc",
                    "{$this->alias}.product_image",
                    "{$this->alias}.brand_inventory",
                    "{$this->alias}.product_price",
                    "{$this->alias}.product_name",
                    "{$this->alias}.product_type",
                    "{$this->alias}.dealer_price",
                    'ManufacturerRetailer.id',
                    'ManufacturerRetailer.warehouse_id',
                    'PricingTier.currencytype',
                ],
            ]);

            if (!empty($product[$this->alias]['id'])) {
                $product[$this->alias]['product_upc'] = $product[$this->alias]['product_upc'] ?: 'No UPC';
                $product[$this->alias] = $this->_processViewProduct($product[$this->alias]);
            }

            return $product;
        } finally {
            $this->virtualFields = $originalVirtualFields;
            $this->unbindModel([
                'belongsTo' => ['User'],
                'hasOne' => ['WarehouseProduct'],
                'hasAndBelongsToMany' => ['Collection', 'Tag'],
            ], false);
        }
    }

    public function getRetailerIndexVariants(array $products, $storeId, $variantOptions, $warehouseId, $discount, $orderType, $stockOption)
    {
        $allVariants = $this->_findAllB2bCatalogueVariants($products, $storeId, $warehouseId, (array)$variantOptions, (array)$discount, $orderType);

        $products = $this->_assignAllB2bCatalogueVariants($products, $allVariants);

        if ($stockOption) {
            $products = $this->removeOutOfStockVariants($products);
        }

        return $products;
    }

    protected function removeOutOfStockVariants(array $products){
        return array_map(function($product){
            $product['Variants'] = array_filter($product['Variants'], function($variant){
                return ($variant['brand_inventory'] ?? 0) > 0;
            });

            return $product;
        }, $products);
    }

    private function _retailerIndexQuery($storeId, $brandIds, $collectionIds, $productTypes, $variantOptions, $search, $order, $orderType, $stockOption, $warehouseId, $discount, $type = 'count', $query = [])
    {
        /** @var OrderProduct $OrderProduct */
        $OrderProduct = ClassRegistry::init('OrderProduct');

        $this->addAssociations([
            'belongsTo' => ['User'],
            'hasOne' => [
                'WarehouseProduct' => ['conditions' => ['WarehouseProduct.warehouse_id' => $warehouseId]],
                'WatchProduct' => ['conditions' => ['WatchProduct.retailer_id' => $storeId]],
            ],
            'belongsToMany' => [
                'Collection' => ['with' => 'CollectionsProduct', 'unique' => 'keepExisting'],
                'Tag' => ['with' => 'ProductTag', 'unique' => 'keepExisting'],
            ],
        ]);
        $originalVirtualFields = $this->virtualFields;

        /** @var WarehouseProduct $WarehouseProduct */
        $WarehouseProduct = $this->WarehouseProduct;

        try {
            $productTitleBrandInventorySql = ($warehouseId)
                ? $WarehouseProduct->virtualFields['available_quantity']
                : "{$this->alias}.brand_inventory";
            // Used in stock option condition
            $this->virtualFields['brand_inventory'] = "ProductTitle.brand_inventory";

            $conditions = $this->getConditionsForActiveProducts([
                $this->alias . '.user_id' => $brandIds,
                $this->alias . '.source_product_id !=' => null,
            ]);
            $conditions = $this->_setRetailerCategoryMatchCondition($storeId, $conditions);
            $conditions = $this->_setCollectionsCondition($collectionIds, $conditions);
            $conditions = $this->_setProductTypesCondition($productTypes, $conditions);
            $conditions = $this->_setVariantOptionsCondition($variantOptions, $conditions);
            $conditions = $this->_setNonApplicableOrderTypesCondition($orderType, $conditions);
            $conditions = $this->_setB2bDiscountFilterCondition((array)$discount, $conditions);
            if (!empty($search)) {
                $conditions[] = $this->buildSearchCondition($search);
            }
            // Special Case: Sorting by Favorites also filters
            if (strpos($order, 'WatchProduct') !== false) {
                $conditions['WatchProduct.id !='] = null;
            }

            $joins = [
                [
                    'table' => $this->buildSubquery([
                        'recursive' => -1,
                        'joins' => [
                            [
                                'table' => 'warehouse_products',
                                'alias' => 'WarehouseProduct',
                                'type' => 'LEFT',
                                'conditions' => [
                                    'WarehouseProduct.product_id' => $this->primaryKeyIdentifier(),
                                    'WarehouseProduct.warehouse_id' => $warehouseId,
                                ],
                            ],
                            [
                                'table' => 'watch_products',
                                'alias' => 'WatchProduct',
                                'type' => 'LEFT',
                                'conditions' => [
                                    'WatchProduct.product_id' => $this->primaryKeyIdentifier(),
                                    'WatchProduct.retailer_id' => $storeId,
                                ],
                            ],
                        ],
                        'conditions' => $conditions,
                        'fields' => [
                            "MIN({$this->alias}.id) AS id",
                            "SUM(GREATEST({$productTitleBrandInventorySql}, 0)) AS brand_inventory",
                        ],
                        'group' => 'source_product_id',
                    ]),
                    'alias' => 'ProductTitle',
                    'conditions' => ["ProductTitle.id = {$this->alias}.id"],
                    'type' => 'INNER',
                ],
            ];
            if (strpos($order, 'LastOrder') !== false) {
                $joins[] = [
                    'table' => $OrderProduct->buildSubquery([
                        'recursive' => -1,
                        'joins' => [
                            [
                                'table' => 'products',
                                'alias' => 'LOProduct',
                                'type' => 'INNER',
                                'conditions' => ['LOProduct.id' => $this->identifier('OrderProduct.product_id')],
                            ],
                        ],
                        'conditions' => ['OrderProduct.status !=' => 'Cancelled'],
                        'fields' => ['LOProduct.source_product_id', "MAX(OrderProduct.created) AS `created_at`"],
                        'group' => 'LOProduct.source_product_id',
                    ]),
                    'alias' => 'LastOrder',
                    'type' => 'LEFT',
                    'conditions' => ['LastOrder.source_product_id' => $this->identifier("{$this->alias}.source_product_id")],
                ];
            }

            $conditions = $this->_setStockOptionCondition($stockOption, $conditions);

            $contain = [
                'User',
                'WarehouseProduct',
                'WatchProduct',
                'Collection' => [
                    'with' => ['CollectionsProduct' => []],
                    'fields' => ['id', 'title'],
                ],
                'Tag' => [
                    'with' => ['ProductTag' => []],
                    'fields' => ['name'],
                ],
            ];
            $baseQuery = compact('contain', 'joins', 'conditions', 'order');

            return $this->findWithDealerPricing($storeId, $type, $baseQuery + $query);
        } finally {
            $this->virtualFields = $originalVirtualFields;
            $this->unbindModel([
                'belongsTo' => ['User'],
                'hasOne' => ['WarehouseProduct', 'WatchProduct'],
                'hasAndBelongsToMany' => ['Collection', 'Tag']
            ], false);
        }
    }

    public function getViewModel($uuid, $retailerId = null)
    {
        $this->bindModel([
            'belongsTo' => ['User'],
            'hasMany' => ['ProductImage'],
        ], false);
        $product = $this->findWithDealerPricing($retailerId, 'first', [
            'contain' => [
                'User',
                'ProductImage' => [
                    'fields' => ['id', 'product_id', 'image_url', 'image_order'],
                    'order' => ['ProductImage.image_order' => 'ASC'],
                ],
            ],
            'conditions' => ["{$this->alias}.uuid" => $uuid],
            'fields' => ['*'],
        ]);
        $this->unbindModel([
            'belongsTo' => ['User'],
            'hasMany' => ['ProductImage'],
        ], false);
        if (empty($product[$this->alias]['id'])) {
            return $product;
        }
        $product[$this->alias]['product_upc'] = $product[$this->alias]['product_upc'] ?: 'No UPC';
        $product[$this->alias] = $this->_processViewProduct($product[$this->alias]);

        if ($product['Product']['product_title_id']) {
            /** @var ProductTitle $ProductTitle */
            $ProductTitle = ClassRegistry::init('ProductTitle');
            $product += $ProductTitle->recordWithAllTranslations((int)$product['Product']['product_title_id'], [
                'fields' => ['id', 'title', 'description'],
            ]);
        }

        return $product;
    }

    public function getViewVariants(array $product, $authUserId, $warehouseId, $discount, $orderType, $userType): array
    {
        $products = [$product];

        $allVariants = $this->_findAllB2bCatalogueVariants($products, $authUserId, $warehouseId, [], (array)$discount, $orderType, $userType);
        $products = $this->_assignAllB2bCatalogueVariants($products, $allVariants);

        return (array)current($products);
    }

    private function _findAllB2bCatalogueVariants(array $products, $retailerId, $warehouseId, array $variantOptions, array $discount, $orderType, $userType = null): array
    {
        /** @var Discount $Discount */
        $Discount = ClassRegistry::init('Discount');
        /** @var InventoryTransfer $InventoryTransfer */
        $InventoryTransfer = ClassRegistry::init('InventoryTransfer');

        $this->addAssociations([
            'hasOne' => [
                'WarehouseProduct' => ['conditions' => ['WarehouseProduct.warehouse_id' => $warehouseId]],
            ],
            'hasMany' => [
                'ProductPrice',
            ],
            'belongsToMany' => [
                'Collection' => ['with' => 'CollectionsProduct', 'unique' => 'keepExisting'],
                'Tag' => ['with' => 'ProductTag', 'unique' => 'keepExisting'],
                'InventoryTransfer' => [
                    'with' => 'InventoryTransferProduct',
                    'unique' => 'keepExisting',
                    'conditions' => $InventoryTransfer->getConditionsForActiveFutureTransfers([
                        'InventoryTransfer.destination_warehouse_id' => $warehouseId,
                    ]),
                ],
            ],
        ]);
        $originalVirtualFields = $this->virtualFields;

        /** @var WarehouseProduct $WarehouseProduct */
        $WarehouseProduct = $this->WarehouseProduct;

        try {
            $this->virtualFields['is_in_warehouse'] = ($warehouseId)
                ? 'WarehouseProduct.id IS NOT NULL'
                : 'TRUE';
            $this->virtualFields['brand_inventory'] = ($warehouseId)
                ? $WarehouseProduct->virtualFields['available_quantity']
                : "{$this->alias}.brand_inventory";

            $this->virtualFields['brand_restock_date'] = ($warehouseId)
                ? 'WarehouseProduct.restock_date'
                : "{$this->alias}.brand_restock_date";

            $this->virtualFields['can_add_to_cart'] = $this->conditions($this->getConditionsForActiveProducts([
                'ProductTier.dealer_price !=' => null,
                'ProductTier.dealer_price >=' => 0,
                $this->virtualFields['is_in_warehouse'],
            ]));
            $sourceProductIdsByBrandId = Hash::combine($products, "{n}.{$this->alias}.source_product_id", "{n}.{$this->alias}.source_product_id", "{n}.{$this->alias}.user_id");

            $conditions = [
                "{$this->alias}.product_status !=" => ProductStatus::DISABLED,
                "{$this->alias}.deleted" => false,
                'OR' => array_map(function($brandId) use ($sourceProductIdsByBrandId) {
                    return [
                        "{$this->alias}.user_id" => $brandId,
                        "{$this->alias}.source_product_id" => array_values($sourceProductIdsByBrandId[$brandId]),
                    ];
                }, array_keys($sourceProductIdsByBrandId)),
            ];
            if (!in_array($userType, User::TYPES_BRAND, true)) {
                $conditions = $this->getConditionsForActiveProducts($conditions);
            }

            $conditions = $this->_setVariantOptionsCondition($variantOptions, $conditions);
            $conditions = $this->_setNonApplicableOrderTypesCondition($orderType, $conditions);
            $conditions = $this->_setB2bDiscountFilterCondition($discount, $conditions);

            $variants = $this->findWithDealerPricing($retailerId, 'all', [
                'contain' => [
                    'WarehouseProduct',
                    'ProductPrice',
                    'Collection' => [
                        'with' => ['CollectionsProduct' => []],
                        'fields' => ['title'],
                    ],
                    'Tag' => [
                        'with' => ['ProductTag' => []],
                        'fields' => ['name'],
                    ],
                    'InventoryTransfer' => [
                        'with' => ['InventoryTransferProduct' => ['id', 'inventory_transfer_id', 'product_id', 'available_quantity']],
                        'fields' => ['id', 'destination_warehouse_id', 'name', 'expected_arrival_date'],
                        'order' => ['expected_arrival_date' => 'ASC'],
                    ],
                ],
                'conditions' => $conditions,
                'fields' => [
                    "{$this->alias}.id",
                    "{$this->alias}.user_id",
                    "{$this->alias}.source_product_id",
                    "{$this->alias}.product_type",
                    "{$this->alias}.product_title",
                    "{$this->alias}.product_sku",
                    "{$this->alias}.product_upc",
                    "{$this->alias}.enable_b2b_oversell",
                    "{$this->alias}.product_image",
                    "{$this->alias}.product_name",
                    "{$this->alias}.variant_options",
                    "{$this->alias}.is_fee_product",
                    "{$this->alias}.is_in_warehouse",
                    "{$this->alias}.brand_inventory",
                    "{$this->alias}.brand_restock_date",
                    "{$this->alias}.can_add_to_cart",
                    "{$this->alias}.dealer_price",
                    "{$this->alias}.dealer_base_price",
                    "{$this->alias}.product_price",
                    "{$this->alias}.compare_at_price",
                    "{$this->alias}.currency",
                    "{$this->alias}.b2b_min_order_quantity",
                    "{$this->alias}.b2b_max_order_quantity",
                    'PricingTier.currencytype',
                ],
                'order' => $this->alias . '.sort_order ASC',
            ]);

            $variants = $this->nestAssociations((array)$variants, ['Collection', 'Tag', 'InventoryTransfer', 'ProductPrice', 'PricingTier']);
            $variants = array_map(
                function($variant) {
                    return $this->_processViewProduct($variant[$this->alias]);
                },
                $variants
            );

            $variantsByBrandId = Hash::combine($variants, '{n}.id', '{n}', '{n}.user_id');
            $variantsByBrandId = array_map(
                fn(int $brandId): array => $Discount->calculateProductListItemDiscounts($brandId, $discount, (array)$variantsByBrandId[$brandId]),
                array_keys($variantsByBrandId)
            );

            // Remap back to a 1-dimensional array
            return Hash::combine($variantsByBrandId, '{n}.{n}.id', '{n}.{n}');
        } finally {
            $this->virtualFields = $originalVirtualFields;
            $this->unbindModel([
                'hasOne' => ['WarehouseProduct'],
                'hasMany' => ['ProductPrice'],
                'hasAndBelongsToMany' => ['Collection', 'Tag', 'InventoryTransfer'],
            ], false);
        }
    }

    /**
     * Groups the provided variants by product and assigns them to the provided product set.
     *
     * @param array $products
     * @param array $allVariants
     * @return array $products each with an assigned 'Variants' column.
     */
    private function _assignAllB2bCatalogueVariants(array $products, array $allVariants): array
    {
        $variantsByBrandIdAndSourceProductId = array_reduce($allVariants, function($map, $variant) {
            $map[$variant['user_id']][$variant['source_product_id']][$variant['id']] = $variant;

            return $map;
        }, []);

        return array_map(function($product) use ($variantsByBrandIdAndSourceProductId) {
            $brandId = $product[$this->alias]['user_id'];
            $sourceProductId = $product[$this->alias]['source_product_id'];

            $product['Variants'] = $variantsByBrandIdAndSourceProductId[$brandId][$sourceProductId] ?? [];

            return $this->_calcViewVariantTotals($product, $product['Variants']);
        }, $products);
    }

    private function _setNonApplicableOrderTypesCondition(?string $b2bOrderType, array $conditions = []): array
    {
        /** @var ProductNonApplicableOrderType $ProductNonApplicableOrderType */
        $ProductNonApplicableOrderType = ClassRegistry::init('ProductNonApplicableOrderType');

        if ($b2bOrderType) {
            $conditions[] = $ProductNonApplicableOrderType->buildNotExistsSubquery([
                'ProductNonApplicableOrderType.product_id' => $this->primaryKeyIdentifier(),
                'ProductNonApplicableOrderType.order_type' => $b2bOrderType,
            ]);
        }

        return $conditions;
    }

    public function _setStockOptionCondition($stockOption, $conditions = [])
    {
        if((bool)$stockOption){
            return array_merge($conditions, ["{$this->alias}.brand_inventory >" => 0]);
        }

        return $conditions;
    }

    private function _setB2bDiscountFilterCondition(array $discount, array $conditions = []): array
    {
        if (
            !empty($discount['b2b_hide_ineligible_products'])
            && !array_has_any($discount['DiscountRule'], fn(array $rule): bool => $rule['order_option'] === DiscountOrderOptions::ALL)
        ) {
            $conditions[]['OR'] = array_map(function(array $rule): array {
                $option = $rule['order_option'];
                $decoded_values = json_decode($rule['order_values'], true);

                switch ($option) {
                    case DiscountOrderOptions::CATEGORY:
                        return $this->_setProductTypesCondition($decoded_values, []);
                    case DiscountOrderOptions::PRODUCT_TITLE:
                        // Virtual field condition is not automatically converted for some reason
                        return [$this->virtualFields['product_name'] => $decoded_values];
                    case DiscountOrderOptions::PRODUCT_VARIANT:
                        return ["{$this->alias}.product_sku" => $decoded_values];
                    case DiscountOrderOptions::PRODUCT_COLLECTION:
                        return $this->_setCollectionTitlesCondition($decoded_values, []);
                    case DiscountOrderOptions::PRODUCT_TAG:
                        return $this->_setProductTagNamesCondition($decoded_values, []);
                    default:
                        CakeLog::warning('Unexpected discount option: ' . json_encode($option));
                        return [];
                }
            }, $discount['DiscountRule']);
        }

        return $conditions;
    }

    /**
     * @param int|int[]|stdClass $retailerId
     * @param array $conditions
     * @return array
     */
    private function _setRetailerCategoryMatchCondition($retailerId, array $conditions = []): array
    {
        /** @var ProductCategories $ProductCategories */
        $ProductCategories = ClassRegistry::init('ProductCategories');

        $conditions[] = $ProductCategories->buildRetailerCategoryMatchCondition(
            $this->primaryKeyIdentifier(),
            $retailerId
        );

        return $conditions;
    }

    private function _setStoreConnectionConditions(int $brandId, int $retailerId, array $conditions = []): array
    {
        /** @var Tag $Tag */
        $Tag = ClassRegistry::init('Tag');

        $storeConnection = $Tag->ManufacturerRetailer->getStoreConnection($brandId, $retailerId, [
            'id',
            'is_commission_tier',
            'enable_strict_tag_matching',
            'pricingtierid',
        ]);

        $conditions[] = $Tag->buildStoreAssignedProductTagMatchCondition($storeConnection, $this->primaryKeyIdentifier());
        if ($storeConnection['is_commission_tier']) {
            /** @var ProductTier $ProductTier */
            $ProductTier = ClassRegistry::init('ProductTier');
            $conditions = array_merge($conditions, [
                $ProductTier->buildExistsSubquery([
                    'ProductTier.pricingtierid' => $storeConnection['pricingtierid'],
                    'ProductTier.product_id' => $this->primaryKeyIdentifier(),
                    'ProductTier.commission >' => 0,
                ]),
            ]);
        }

        return $conditions;
    }

    protected function _setDealerPriceCondition($userIds, $retailerId, array $conditions = []): array
    {
        if ($retailerId) {
            /** @var ProductTier $ProductTier */
            $ProductTier = ClassRegistry::init('ProductTier');

            $conditions[] = $ProductTier->buildExistsSubquery([
                'ProductTier.product_id' => $this->primaryKeyIdentifier(),
                'ProductTier.dealer_price !=' => null,
            ], false, [
                [
                    'table' => 'manufacturer_retailers',
                    'alias' => 'ManufacturerRetailer',
                    'type' => 'INNER',
                    'conditions' => [
                        'ManufacturerRetailer.pricingtierid' => $ProductTier->identifier('ProductTier.pricingtierid'),
                        'ManufacturerRetailer.user_id' => $userIds,
                        'ManufacturerRetailer.retailer_id' => $retailerId,
                    ],
                ],
            ]);
        }

        return $conditions;
    }

    public function _setCollectionsCondition(array $collectionIds, array $conditions = []): array
    {
        /** @var CollectionsProduct $CollectionsProduct */
        $CollectionsProduct = ClassRegistry::init('CollectionsProduct');

        if ($collectionIds) {
            $conditions[] = $CollectionsProduct->buildExistsSubquery([
                "{$CollectionsProduct->alias}.product_id" => $this->primaryKeyIdentifier(),
                "{$CollectionsProduct->alias}.collection_id" => $collectionIds,
            ]);
        }

        return $conditions;
    }

    private function _setCollectionTitlesCondition(array $collectionTitles, array $conditions = []): array
    {
        /** @var CollectionsProduct $CollectionsProduct */
        $CollectionsProduct = ClassRegistry::init('CollectionsProduct');
        $Collection = $CollectionsProduct->Collection;

        if ($collectionTitles) {
            $conditions[] = $CollectionsProduct->buildExistsSubquery([
                "{$CollectionsProduct->alias}.product_id" => $this->primaryKeyIdentifier(),
                "{$Collection->alias}.title" => $collectionTitles,
            ], false, [
                [
                    'table' => $Collection->table,
                    'alias' => $Collection->alias,
                    'type' => 'LEFT',
                    'conditions' => [
                        "{$CollectionsProduct->alias}.collection_id" => $Collection->primaryKeyIdentifier(),
                    ],
                ],
            ]);
        }

        return $conditions;
    }

    private function _setProductTagNamesCondition(array $tagNames, array $conditions = []): array
    {
        /** @var ProductTag $ProductTag */
        $ProductTag = ClassRegistry::init('ProductTag');
        $Tag = $ProductTag->Tag;

        if ($tagNames) {
            $conditions[] = $ProductTag->buildExistsSubquery([
                "{$ProductTag->alias}.product_id" => $this->primaryKeyIdentifier(),
                "{$Tag->alias}.name" => $tagNames,
            ], false, [
                [
                    'table' => $Tag->table,
                    'alias' => $Tag->alias,
                    'type' => 'LEFT',
                    'conditions' => [
                        "{$ProductTag->alias}.tag_id" => $Tag->primaryKeyIdentifier(),
                    ],
                ],
            ]);
        }

        return $conditions;
    }

    public function _setProductTypesCondition($productTypes, $conditions = [])
    {
        if (!empty($productTypes)) {
            $conditions[]['OR'] = array_map(function($productType) {
                // Wrapping in quotes allows matching all possible value formats while avoiding substring matches
                return ["INSTR(CONCAT('\"', {$this->alias}.product_type, '\"'), ?) > 0" => "\"{$productType}\""];
            }, $productTypes);
        }
        return $conditions;
    }

    public function _setVariantOptionsCondition($variantOptions, $conditions = [])
    {
        if (!empty($variantOptions)) {
            $andConditions = [];

            foreach ($variantOptions as $type => $values) {
                if (!empty($values) && is_array($values)) {
                    $orConditions = array_map(function ($variantValue) {
                        return ["INSTR(CONCAT(' / ', {$this->virtualFields['variant_options']}, ' / '), ?) > 0" => " / {$variantValue} / "];
                    }, $values);

                    // Use OR within the same variant type
                    $andConditions[] = ['OR' => $orConditions];
                }
            }

            if (!empty($andConditions)) {
                $conditions[]['AND'] = $andConditions;
            }
        }

        return $conditions;
    }

    private function _calcViewVariantTotals($product, $variants)
    {
        $allowedVariants = array_filter($variants, function($variant) {
            return $variant['can_add_to_cart'];
        });

        $lowestPrice = array_reduce($variants, function ($lowestPricedVariant, $variant) {
            $variant['dealer_price_discount'] = ($variant['discount']['is_product_discount'] ?? false) ? $variant['discount']['discount_amount'] : 0;
            $variant['discounted_dealer_price'] = $variant['dealer_price'] - $variant['dealer_price_discount'];

            if (
                empty($lowestPricedVariant) ||
                ($variant['dealer_price'] > 0 && $variant['discounted_dealer_price'] < $lowestPricedVariant['discounted_dealer_price'])
            ) {
                return $variant;
            }

            return $lowestPricedVariant;
        }, []);

        $highestPrice = array_reduce($variants, function ($highestPricedVariant, $variant) {
            $variant['dealer_price_discount'] = ($variant['discount']['is_product_discount'] ?? false) ? $variant['discount']['discount_amount'] : 0;
            $variant['discounted_dealer_price'] = $variant['dealer_price'] - $variant['dealer_price_discount'];
            if (
                empty($highestPricedVariant) ||
                ($variant['dealer_price'] > 0 && $variant['discounted_dealer_price'] > $highestPricedVariant['discounted_dealer_price'])
            ) {
                return $variant;
            }
            return $highestPricedVariant;
        }, []);

        $currencyCode = $product['PricingTier']['currencytype'];
        $defaultCurrencyCode = $product['User']['currency_code'];
        $retailPrices = false;

        // the filter in Hash::extract() below matches anything when $currencyCode = null or ''
        if((bool)$currencyCode){
            $retailPrices = Hash::extract($variants, "{n}[retail_price_currency_code={$currencyCode}].retail_price");
        }
        if(!(bool)$retailPrices){
            $currencyCode = $defaultCurrencyCode;
            $retailPrices = Hash::extract($variants, "{n}[retail_price_currency_code={$currencyCode}].retail_price");
        }
        $lowestRetailPrice = (bool)$retailPrices ? min($retailPrices) : 0;
        $highestRetailPrice = (bool)$retailPrices ? max($retailPrices) : 0;

        $product[$this->alias] = array_merge($product[$this->alias], [
            'brand_inventory' => array_sum(array_filter(array_column($variants, 'brand_inventory'))),
            'count_instock' => count(
                array_filter($variants, function($variant) {
                    return ($variant['brand_inventory'] === null || $variant['brand_inventory'] > 0);
                })
            ),
            'count_total' => count($variants),
            'lowest_price' => $lowestPrice['dealer_price'] ?? '0.00',
            'highest_price' => $highestPrice['dealer_price'] ?? '0.00',
            'lowest_price_discount' => $lowestPrice['discount'] ?? [],
            'highest_price_discount' => $highestPrice['discount'] ?? [],
            'lowest_price_currency' => $product['PricingTier']['currencytype'],
            'lowest_retail_price' => $lowestRetailPrice,
            'highest_retail_price' => $highestRetailPrice,
            'lowest_retail_price_currency' => $currencyCode,
            'can_add_to_cart' => ($product['User']['enable_b2b_cart'] && count($allowedVariants) > 0)
        ]);

        return $product;
    }

    private function _processViewProduct($product)
    {
        if (empty($product['id'])) {
            return $product;
        }

        if (empty($product['product_image'])) {
            $product['product_image'] = BASE_PATH . 'images/no_img.gif';
        }
        if(key_exists('ProductPrice', $product)){
            /** @var User $User */
            $User = ClassRegistry::init('User');
            $defaultCurrencyCode = $product['currency'] ?: $User->field('currency_code', ['User.id' => $product['user_id']]);
            array_unshift($product['ProductPrice'], [
                'product_id' => $product['id'],
                'currency_code' => $defaultCurrencyCode,
                'price' => $product['product_price'],
                'compare_at_price' => $product['compare_at_price'] ?? null,
            ]);

            $currencyCode = $product['PricingTier']['currencytype'];
            $retailPrice = false;
            
            // the filter in Hash::extract() below matches anything when $currencyCode = null or ''
            if((bool)$currencyCode){
                $retailPrice = Hash::extract($product, "ProductPrice.{n}[currency_code={$currencyCode}].price");
            }
            if(!(bool)$retailPrice){
                $currencyCode = $defaultCurrencyCode;
                $retailPrice = Hash::extract($product, "ProductPrice.{n}[currency_code={$currencyCode}].price");
            }
            // we should be left with only 1 element in $retailPrice
            $product['retail_price'] = current($retailPrice);
            $product['retail_price_currency_code'] = $currencyCode;
        }
        return $product;
    }

    /**
     * Update the product inquiries for the given product and user Id into product table.
     *
     * @param $productId
     * @return mixed
     */
    public function updateProductInquiries($productId)
    {
        return $this->_updateAllById($productId, ['Product.no_of_inquiries' => 'Product.no_of_inquiries+1']);
    }

    /**
     * @param int $userId
     * @param string|null $orderType
     * @return string[]
     */
    public function getWholesaleCollectionOptionsByUser($userId, ?string $orderType, $pricingTierId = null, $storeId = null): array
    {
        $conditions = $this->getConditionsForActiveProducts([
            $this->alias . '.user_id' => $userId,
            $this->alias . '.source_product_id !=' => null,
        ]);
        $conditions = $this->_setDealerPriceCondition( $userId, $storeId, $conditions);
        $conditions = $this->_setNonApplicableOrderTypesCondition($orderType, $conditions);

        return $this->_findAllCollectionOptionsFilteredByPricingTierId($conditions, $pricingTierId);
    }

    public function getCollectionOptionsByProduct($productId): array
    {
        $conditions = ([
            'CollectionsProduct.product_id' => $productId,
        ]);

        return $this->_findAllCollectionOptions($conditions);
    }

    /**
     * @param array $conditions
     * @return string[]
     */
    protected function _findAllCollectionOptions(array $conditions): array
    {
        return $this->find('list', [
            'recursive' => -1,
            'joins' => [
                [
                    'table' => 'collections_products',
                    'alias' => 'CollectionsProduct',
                    'type' => 'INNER',
                    'conditions' => ["{$this->alias}.id = CollectionsProduct.product_id"],
                ],
                [
                    'table' => 'collections',
                    'alias' => 'Collection',
                    'type' => 'INNER',
                    'conditions' => ['CollectionsProduct.collection_id = Collection.id'],
                ],
            ],
            'conditions' => $conditions,
            'fields' => ['Collection.id', 'Collection.title'],
            'order' => ['Collection.title' => 'ASC'],
            'group' => ['Collection.id'],
        ]);
    }

    protected function _findAllCollectionOptionsFilteredByPricingTierId(array $conditions, $pricingTierId = null): array
    {
        $joins = [
            0 => [
                'table' => 'collections_products',
                'alias' => 'CollectionsProduct',
                'type' => 'INNER',
                'conditions' => ["{$this->alias}.id = CollectionsProduct.product_id"],
            ],
            1 => [
                'table' => 'collections',
                'alias' => 'Collection',
                'type' => 'INNER',
                'conditions' => ['CollectionsProduct.collection_id = Collection.id'],
            ],
            2 => [
                'table' => 'pricing_tiers_collections',
                'alias' => 'PricingTiersCollection',
                'type' => 'INNER',
                'conditions' => ['Collection.id = PricingTiersCollection.collection_id'],
            ],
        ];

        if ($pricingTierId !== null) {
            $joins[2]['conditions']['PricingTiersCollection.pricing_tier_id'] = $pricingTierId;
        }

        return $this->find('list', [
            'recursive' => -1,
            'joins' => $joins,
            'conditions' => $conditions,
            'fields' => ['Collection.id', 'Collection.title'],
            'order' => ['Collection.title' => 'ASC'],
            'group' => ['Collection.id'],
        ]);
    }

    /**
     * @param int|int[] $userIds
     * @param string|null $orderType
     * @param int[] $collectionIds
     * @return string[]
     */
    public function getWholesaleProductTypesByUser($userIds, ?string $orderType = null, array $collectionIds = [], $storeId = null): array
    {
        $conditions = $this->getConditionsForActiveProducts([
            "{$this->alias}.user_id" => $userIds,
            "{$this->alias}.source_product_id !=" => null,
        ]);
        $conditions = $this->_setDealerPriceCondition( $userIds, $storeId, $conditions);
        $conditions = $this->_setNonApplicableOrderTypesCondition($orderType, $conditions);
        $conditions = $this->_setCollectionsCondition($collectionIds, $conditions);

        return $this->_findAllProductTypeOptions($conditions);
    }

    /**
     * @param int|int[] $userIds
     * @param string|null $orderType
     * @param string[] $productTypes
     * @param int|null $storeId
     * @return array<string, array{name: string, values: array<string, string>}> Eg.
     *  ```
     *  ['color' => ['name' => 'Color', 'values' => ['red' => 'Red']]
     *  ```
     */
    public function getWholesaleVariantOptionsByName($userIds, ?string $orderType = null, array $productTypes = [], $storeId = null): array
    {
        $conditions = $this->getConditionsForActiveProducts([
            "{$this->alias}.source_product_id !=" => null,
        ]);
        $conditions = $this->_setDealerPriceCondition( $userIds, $storeId, $conditions);
        $conditions = $this->_setNonApplicableOrderTypesCondition($orderType, $conditions);
        $conditions = $this->_setProductTypesCondition($productTypes, $conditions);

        return $this->getVariantOptionsByName($userIds, $conditions);
    }

    /**
     * @return string[]
     */
    public function getStockOptions(): array
    {
        return [
            0 => __('All Products'),
            1 => __('In Stock Only'),
        ];
    }

    public function getVariantOptionsByUser($userIds, array $extraConditions = [], array $contain = [])
    {
        $conditions = [
            "{$this->alias}.user_id" => $userIds,
            "{$this->alias}.variant_options !=" => '',
            "{$this->alias}.deleted" => false,
        ];
        
        $products = (array)$this->find('all', [
            'contain' => $contain,
            'conditions' => $conditions + $extraConditions,
            'fields' => ["DISTINCT ({$this->virtualFields['variant_options']}) AS `{$this->alias}__variant_options`"],
            'order' => "{$this->alias}.variant_sort ASC",
        ]);

        $optionNames = (array)array_reduce(
            Hash::extract($products, "{n}.{$this->alias}.variant_options"),
            function($set, $options) {
                $names = static::decodeVariantOptions($options);

                return $set + array_combine(array_map('strtolower', $names), array_map('ucwords', $names));
            },
            []
        );

        return $optionNames;
    }

    /**
     * @param int|int[] $userIds
     * @param array $extraConditions
     * @return array<string, array{name: string, values: array<string, string>}> Eg.
     * ```
     * ['color' => ['name' => 'Color', 'values' => ['red' => 'Red']]
     * ```
     */
    public function getVariantOptionsByName($userIds, array $extraConditions = []): array
    {
        /** @var ProductVariantOption $ProductVariantOption */
        $ProductVariantOption = ClassRegistry::init('ProductVariantOption');
        $records = (array)$ProductVariantOption->find('all', [
            'contain' => [
                $this->alias => [
                    'fields' => ['id'],
                ],
                'VariantOption' => [
                    'fields' => ['id', 'name'],
                ],
            ],
            // Workaround to resolve association virtual fields
            'conditions' => $this->conditions(
                array_merge($extraConditions, [
                    "{$this->alias}.user_id" => $userIds,
                    "{$this->alias}.deleted" => false,
                ])
            ),
            'fields' => ['id', 'value'],
            'order' => ["{$this->alias}.variant_sort" => 'ASC'],
        ]);

        $variantValues = [];
        foreach ($records as $record) {
            $name = (string)$record['VariantOption']['name'];
            $value = (string)$record['ProductVariantOption']['value'];

            $nameKey = mb_strtolower(Inflector::slug($name, '-'));
            $valueKey = mb_strtolower($value);

            $variantValues[$nameKey]['name'] = $name;
            $variantValues[$nameKey]['values'][$valueKey] = $value;
        }

        /** @var VariantSortOrder $VariantSortOrder */
        $VariantSortOrder = ClassRegistry::init('VariantSortOrder');
        $sortOrder = (array)$VariantSortOrder->find('list', [
            'conditions' => ['VariantSortOrder.user_id' => $userIds],
            'fields' => ['variant_key', 'sort_value'],
        ]);
        if ($sortOrder) {
            foreach ($variantValues as $key => $value) {
                uksort($variantValues[$key]['values'], function($a, $b) use ($sortOrder) {
                    return ($sortOrder[$b] ?? PHP_INT_MAX) <=> ($sortOrder[$a] ?? PHP_INT_MAX);
                });
            }
        }
        ksort($variantValues);

        return $variantValues;
    }

    /**
     * @param int|int[] $userIds
     * @return string[]
     */
    public function getProductTypesByUser($userIds): array
    {
        $conditions = $this->getConditionsForActiveProducts([
            "{$this->alias}.user_id" => $userIds,
            "{$this->alias}.source_product_id !=" => null,
        ]);

        return $this->_findAllProductTypeOptions($conditions);
    }

    /**
     * @param array $conditions
     * @return string[]
     */
    protected function _findAllProductTypeOptions(array $conditions): array
    {
        $products = (array)$this->find('all', [
            'recursive' => -1,
            'conditions' => array_merge($conditions, [
                "COALESCE({$this->alias}.product_type, '') !=" => '',
            ]),
            'fields' => ["DISTINCT {$this->alias}.product_type"],
        ]);

        $types = (array)array_reduce(
            Hash::extract($products, "{n}.{$this->alias}.product_type"),
            function($set, $type) {
                $names = static::decodeProductType($type);

                return $set + array_combine($names, $names);
            },
            []
        );
        asort($types);

        return $types;
    }

    public function getProductTitlesByUser($userId)
    {
        return $this->find('list', array(
            'recursive' => -1,
            'conditions' => array(
                'user_id' => $userId,
                "source_product_id !=" => null,
                'product_status' => self::STATUS_ENABLED,
            ),
            'fields' => array('product_name', 'product_name'),
            'order' => 'product_title ASC',
        ));
    }

    public function getProductIdsByTitle($userId, $productTitles)
    {
        return Hash::combine(
            $this->find('all', array(
                'recursive' => -1,
                'conditions' => array(
                    'user_id' => $userId,
                    $this->virtualFields['product_name'] => $productTitles,
                ),
                'fields' => array('id', 'product_name'),
            )),
            "{n}.{$this->alias}.id", "{n}.{$this->alias}.id", "{n}.{$this->alias}.product_name"
        );
    }

    public function getProductTitlesGroupByTitleId($userId)
    {
        return $this->find('list', array(
            'recursive' => -1,
            'conditions' => $this->getConditionsForActiveProducts([
                "{$this->alias}.user_id" => $userId,
                "{$this->alias}.source_product_id !=" => null,
            ]),
            'fields' => array('uuid', 'product_name'),
            'group' => ["{$this->alias}.product_title_id"],
            'order' => 'product_title ASC',
        ));
    }

    public function getVisibleUuidsForRetailer($retailerId, array $userIds)
    {
        if (!$retailerId || empty($userIds)) {
            return [];
        }

        $conditions = $this->getConditionsForActiveProducts([
            "{$this->alias}.source_product_id !=" => null,
            "{$this->alias}.user_id" => $userIds,
        ]);

        $conditions = $this->_setDealerPriceCondition($userIds, $retailerId, $conditions);

        return $this->find('list', [
            'recursive' => -1,
            'fields' => ["{$this->alias}.uuid", "{$this->alias}.uuid"],
            'conditions' => $conditions,
        ]);
    }

    public function getProductVariantsByUser($userId)
    {
        return $this->find('list', array(
            'recursive' => -1,
            'conditions' => array(
                'user_id' => $userId,
                "COALESCE(product_sku, '') !=" => '',
                'product_status' => self::STATUS_ENABLED,
            ),
            'fields' => array('product_sku', 'product_sku'),
            'order' => 'product_sku ASC',
        ));
    }

    public function findForCollectionProductSearch(int $brandId, $term, $excludes): array
    {
        return (array)$this->find( 'all', [
            'recursive' => -1,
            'conditions' => $this->getConditionsForActiveProducts([
                $this->buildDealerOrderItemSearchCondition($term),
                "{$this->alias}.user_id" => $brandId,
                "{$this->alias}.product_title !=" => null,
                "{$this->alias}.id !=" => $excludes,
            ]),
            'fields' => [
                "{$this->alias}.id",
                "{$this->alias}.product_title_id",
                "{$this->alias}.product_title",
                "{$this->alias}.product_sku",
                "{$this->alias}.product_image",
                "{$this->alias}.product_price",
                "{$this->alias}.product_name",
            ],
            'group' => ["{$this->alias}.product_title_id"],
            'order' => ["{$this->alias}.product_title" => 'ASC'],
            'limit' => 50,
        ]);
    }

    public function findForNeedToConfirmProductSearch(int $brandId, int $retailerId, string $term, array $excludes): array
    {
        /** @var WarehouseProduct $WarehouseProduct */
        $WarehouseProduct = ClassRegistry::init('WarehouseProduct');

        $originalVirtualFields = $this->virtualFields;

        try {
            $this->virtualFields['brand_available_inventory'] = "IF(TotalWarehouseProduct.product_id, TotalWarehouseProduct.available_quantity, {$this->alias}.brand_inventory)";
            $this->virtualFields['brand_restock_date'] = "IF(TotalWarehouseProduct.product_id, TotalWarehouseProduct.restock_start_date, {$this->alias}.brand_restock_date)";

            return (array)$this->findWithDealerPricing($retailerId, 'all', [
                'recursive' => -1,
                'joins' => [
                    [
                        'table' => $WarehouseProduct->buildProductTotalsSubQuery(),
                        'alias' => 'TotalWarehouseProduct',
                        'type' => 'LEFT',
                        'conditions' => ['TotalWarehouseProduct.product_id' => $this->primaryKeyIdentifier()],
                    ],
                ],
                'conditions' => $this->getConditionsForActiveProducts([
                    $this->buildDealerOrderItemSearchCondition($term),
                    "{$this->alias}.sell_direct !=" => ProductSellDirect::EXCLUSIVELY,
                    "{$this->alias}.user_id" => $brandId,
                    "{$this->alias}.id !=" => array_column($excludes, 'product_id'),
                    "{$this->alias}.brand_available_inventory >" => 0,
                ]),
                'fields' => [
                    "{$this->alias}.id",
                    "{$this->alias}.product_title",
                    "{$this->alias}.product_sku",
                    "{$this->alias}.product_image",
                    "{$this->alias}.product_price",
                    "{$this->alias}.dealer_price",
                    "{$this->alias}.brand_available_inventory",
                    "{$this->alias}.brand_restock_date",
                ],
                'order' => ["{$this->alias}.product_title" => 'ASC'],
                'limit' => 50,
            ]);
        } finally {
            $this->virtualFields = $originalVirtualFields;
        }
    }

    public function findForPurchaseOrderProductSearch(int $brandId, int $retailerId, string $term, array $excludes): array
    {
        /** @var WarehouseProduct $WarehouseProduct */
        $WarehouseProduct = ClassRegistry::init('WarehouseProduct');
        /** @var InventoryTransferProduct $InventoryTransferProduct */
        $InventoryTransferProduct = ClassRegistry::init('InventoryTransferProduct');

        $originalVirtualFields = $this->virtualFields;

        try {
            $this->virtualFields['brand_available_inventory'] = "IF(InventoryTransfer.id, InventoryTransfer.available_quantity, ({$WarehouseProduct->virtualFields['available_quantity']}))";
            $this->virtualFields['brand_restock_date'] = 'IF(InventoryTransfer.id, InventoryTransfer.expected_arrival_date, WarehouseProduct.restock_date)';

            $warehouseProductExcludes = array_filter($excludes, function($exclude) {
                return !empty($exclude['warehouse_id']);
            });
            $productExcludes = array_filter($excludes, function($exclude) {
                return empty($exclude['warehouse_id']);
            });

            $validWarehouseIds = array_keys($WarehouseProduct->Warehouse->listValidUserWarehouses($brandId, $retailerId));

            return (array)$this->findWithDealerPricing($retailerId, 'all', [
                'recursive' => -1,
                'joins' => [
                    [
                        'table' => 'warehouse_products',
                        'alias' => 'WarehouseProduct',
                        'type' => 'INNER',
                        'conditions' => [
                            "WarehouseProduct.product_id = {$this->alias}.id",
                            'WarehouseProduct.warehouse_id' => $validWarehouseIds,
                            'NOT' => array_map(
                                function($exclude) {
                                    return [
                                        'WarehouseProduct.product_id' => $exclude['product_id'],
                                        'WarehouseProduct.warehouse_id' => $exclude['warehouse_id'],
                                    ];
                                },
                                $warehouseProductExcludes
                            ),
                        ],
                    ],
                    [
                        'table' => 'warehouses',
                        'alias' => 'Warehouse',
                        'type' => 'INNER',
                        'conditions' => ['Warehouse.id = WarehouseProduct.warehouse_id'],
                    ],
                    [
                        'table' => $InventoryTransferProduct->buildSubquery([
                            'recursive' => -1,
                            'joins' => [
                                [
                                    'table' => 'inventory_transfers',
                                    'alias' => 'InventoryTransfer',
                                    'type' => 'INNER',
                                    'conditions' => 'InventoryTransfer.id = InventoryTransferProduct.inventory_transfer_id',
                                ],
                            ],
                            'conditions' => $InventoryTransferProduct->InventoryTransfer->getConditionsForActiveFutureTransfers(),
                            'fields' => [
                                'InventoryTransfer.id',
                                'InventoryTransfer.destination_warehouse_id',
                                'InventoryTransfer.expected_arrival_date',
                                'InventoryTransferProduct.product_id',
                                "({$InventoryTransferProduct->virtualFields['available_quantity']}) AS `available_quantity`",
                            ],
                        ]),
                        'alias' => 'InventoryTransfer',
                        'type' => 'LEFT',
                        'conditions' => [
                            'WarehouseProduct.warehouse_id = InventoryTransfer.destination_warehouse_id',
                            'WarehouseProduct.product_id = InventoryTransfer.product_id',
                            "({$WarehouseProduct->virtualFields['available_quantity']}) <= 0"
                        ],
                    ],
                ],
                'conditions' => $this->getConditionsForActiveProducts([
                    $this->buildDealerOrderItemSearchCondition($term),
                    "{$this->alias}.user_id" => $brandId,
                    "{$this->alias}.id !=" => array_column($productExcludes, 'product_id'),
                    'OR' => [
                        "{$this->alias}.enable_b2b_oversell" => true,
                        "{$this->alias}.brand_available_inventory >" => 0,
                    ],
                ]),
                'fields' => [
                    "{$this->alias}.id",
                    "{$this->alias}.product_title",
                    "{$this->alias}.product_sku",
                    "{$this->alias}.product_image",
                    "{$this->alias}.product_price",
                    "{$this->alias}.dealer_price",
                    "{$this->alias}.brand_available_inventory",
                    "{$this->alias}.brand_restock_date",
                    'Warehouse.id',
                    'Warehouse.name',
                    'InventoryTransfer.id',
                ],
                'order' => [
                    'Warehouse.name' => 'ASC',
                    "{$this->alias}.product_title" => 'ASC',
                    "{$this->alias}.brand_restock_date" => 'ASC',
                ],
                'limit' => 50,
            ]);
        } finally {
            $this->virtualFields = $originalVirtualFields;
        }
    }

    /**
     * @param int|int[] $storeId
     * @param string $type Type of find operation (all / first / count / neighbors / list / threaded)
     * @param array $query Option fields (conditions / fields / joins / limit / offset / order / page / group / callbacks)
     * @return array|int|null Array of records, or Null on failure.
     * @see Model::find()
     */
    public function findWithDealerPricing($storeId, $type = 'first', $query = [])
    {
        $originalVirtualFields = $this->virtualFields;

        $this->virtualFields['dealer_price'] = 'ProductTier.dealer_price';

        $this->virtualFields['dealer_base_price'] = 'ProductTier.dealer_base_price';

        $query['recursive'] = (int)($query['recursive'] ?? $this->recursive);

        if(!empty($storeId)) {
            $query['conditions']['ProductTier.dealer_price !='] = null;
        }

        $baseJoins = [
            [
                'table' => 'manufacturer_retailers',
                'alias' => 'ManufacturerRetailer',
                'type' => 'LEFT',
                'conditions' => [
                    'ManufacturerRetailer.user_id' => $this->identifier('Product.user_id'),
                    'ManufacturerRetailer.retailer_id' => $storeId,
                ],
            ],
            [
                'table' => 'pricing_tiers',
                'alias' => 'PricingTier',
                'type' => 'LEFT',
                'conditions' => [
                    'PricingTier.id' => $this->identifier('ManufacturerRetailer.pricingtierid'),
                ],
            ],
            [
                'table' => 'product_tiers',
                'alias' => 'ProductTier',
                'type' => 'LEFT',
                'conditions' => [
                    'ProductTier.product_id' => $this->primaryKeyIdentifier(),
                    'ProductTier.pricingtierid' => $this->identifier('ManufacturerRetailer.pricingtierid'),
                ],
            ],
        ];
        $query['joins'] = array_merge($baseJoins, $query['joins'] ?? []);

        $products = $this->find($type, $query);

        $this->virtualFields = $originalVirtualFields;

        return $products;
    }

    public function findAllWithRegionalPricing(int $brandId, string $regionalCurrency, array $query, ?string $brandCurrency = null): array
    {
        $this->bindModel([
            'hasOne' => [
                'RegionalPrice' => [
                    'className' => 'ProductPrice',
                    'conditions' => ['RegionalPrice.currency_code' => $regionalCurrency],
                ],
            ],
        ], false);
        $originalVirtualFields = $this->virtualFields;

        try {
            /** @var User $User */
            $User = ClassRegistry::init('User');
            $brandCurrency = $brandCurrency ?: $User->field('currency_code', ['User.id' => $brandId]);

            // Use brand-level currency if product sync does not set product-level currency
            $this->virtualFields['currency'] = "IF({$this->alias}.currency, {$this->alias}.currency, '{$brandCurrency}')";

            $products = (array)$this->find('all', array_merge($query, [
                'contain' => array_merge((array)($query['contain'] ?? []), [
                    'RegionalPrice' => ['fields' => ['product_id', 'currency_code', 'price', 'compare_at_price']],
                ]),
                'conditions' => array_merge((array)($query['conditions'] ?? []), [
                    "{$this->alias}.user_id" => $brandId
                ]),
                'fields' => array_merge((array)($query['fields'] ?? []), [
                    "{$this->alias}.id",
                    "{$this->alias}.product_price",
                    "{$this->alias}.compare_at_price",
                    "{$this->alias}.currency",
                ]),
            ]));

            $anyProductIsMissingPricing = count(array_filter(Hash::extract($products, '{n}.RegionalPrice.price'))) < count($products);

            return array_map(function($product) use ($anyProductIsMissingPricing) {
                if ($anyProductIsMissingPricing) {
                    $product['RegionalPrice'] = [
                        'product_id' => $product['Product']['id'],
                        'currency_code' => $product['Product']['currency'],
                        'price' => $product['Product']['product_price'],
                        'compare_at_price' => $product['Product']['compare_at_price'],
                    ];
                }

                $product['RegionalPrice']['price'] = Currency::formatAsDecimal(
                    $product['RegionalPrice']['price'],
                    $product['RegionalPrice']['currency_code']
                );
                if ($product['RegionalPrice']['compare_at_price'] !== null) {
                    $product['RegionalPrice']['compare_at_price'] = Currency::formatAsDecimal(
                        $product['RegionalPrice']['compare_at_price'],
                        $product['RegionalPrice']['currency_code']
                    );
                }

                return $product;
            },  $products);
        } finally {
            $this->virtualFields = $originalVirtualFields;
            $this->unbindModel(['hasOne' => ['RegionalPrice']], false);
        }
    }

    public function listOversoldInventoryByProductID($brandId, array $quantityByProductID, bool $sellExclusiveOnly = false): array
    {
        /** @var WarehouseProduct $WarehouseProduct */
        $WarehouseProduct = ClassRegistry::init('WarehouseProduct');

        $originalVirtualFields = $this->virtualFields;

        $this->virtualFields['brand_available_inventory'] = 'IF(TotalWarehouseProduct.product_id, TotalWarehouseProduct.available_quantity, Product.brand_inventory)';

        $conditions = $this->getConditionsForActiveProducts([
            "{$this->alias}.productID" => array_keys($quantityByProductID),
            "{$this->alias}.enable_oversell" => false,
            "{$this->alias}.brand_available_inventory !=" => null,
            "{$this->alias}.user_id" => $brandId,
        ]);
        if ($sellExclusiveOnly) {
            $conditions["{$this->alias}.sell_direct"] = ProductSellDirect::EXCLUSIVELY;
        }

        $inventoryByProductID = $this->find('list', [
            'recursive' => -1,
            'joins' => [
                [
                    'table' => $WarehouseProduct->buildProductTotalsSubQuery(),
                    'alias' => 'TotalWarehouseProduct',
                    'type' => 'LEFT',
                    'conditions' => ['TotalWarehouseProduct.product_id' => $this->primaryKeyIdentifier()],
                ],
            ],
            'conditions' => $conditions,
            'fields' => [
                $this->alias . '.productID',
                $this->alias . '.brand_available_inventory',
            ],
        ]);

        $this->virtualFields = $originalVirtualFields;

        $oversoldInventoryByProductID = [];
        foreach ($inventoryByProductID as $productID => $inventory) {
            $quantity = $quantityByProductID[$productID];
            if ($quantity > $inventory) {
                $oversoldInventoryByProductID[$productID] = $inventory;
            }
        }
        return $oversoldInventoryByProductID;
    }

    public function findForRetailerOnDisplaySettings(int $userId, int $retailerId): array
    {
        $allProducts = (array)$this->find('all', [
            'recursive' => -1,
            'conditions' => $this->_getConditionsForCatalogueWidgetIndex($userId, $retailerId),
            'fields' => [
                'id',
                'product_title_id',
                'product_title',
                'product_sku',
                'product_image',
                'product_status',
                'product_name',
                'variant_options',
            ],
            'order' => ["{$this->alias}.sort_order" => 'ASC'],
        ]);
        $groupedProducts = Hash::combine($allProducts, "{n}.{$this->alias}.id", '{n}', "{n}.{$this->alias}.product_title_id");

        return array_map(function(array $productById): array {
            $mainId = min(array_keys($productById));

            return (array)$productById[$mainId] + ['Variants' => array_column(array_values($productById), $this->alias)];
        }, array_values($groupedProducts));
    }

    /**
     * @param int $brandId
     * @param int $retailerId
     * @param string|null $productType
     * @param string|null $search
     * @return string[]
     */
    public function findAllCatalogueWidgetCollectionOptions(int $brandId, int $retailerId, ?string $productType = null, ?string $search = null): array
    {
        $conditions = $this->_getConditionsForCatalogueWidgetIndex($brandId, $retailerId, $productType, null, $search);
        $conditions['Collection.is_on_store_locator'] = true;
        return $this->_findAllCollectionOptions($conditions);
    }

    /**
     * @param int $brandId
     * @param int $retailerId
     * @param int|null $collectionId
     * @param string|null $search
     * @return string[]
     */
    public function findAllCatalogueWidgetProductTypeOptions(int $brandId, int $retailerId, ?int $collectionId = null, ?string $search = null): array
    {
        $conditions = $this->_getConditionsForCatalogueWidgetIndex($brandId, $retailerId, null, $collectionId, $search);

        return $this->_findAllProductTypeOptions($conditions);
    }

    public function countForCatalogueWidgetIndex(
        int $brandId,
        string $currencyCode,
        int $retailerId,
        ?string $productType = null,
        ?int $collectionId = null,
        ?string $search = null
    ): int
    {
        $conditions = $this->_getConditionsForCatalogueWidgetIndex($brandId, $retailerId, $productType, $collectionId, $search);
        $fields = [$this->getDboSource()->calculate($this, 'count')];
        $results = $this->query($this->_subqueryForCatalogueWidgetIndex($brandId, $retailerId, $currencyCode, $conditions, $fields));
        $numberOfGroups = count($results);

        return ($numberOfGroups === 1) ? (int)$results[0][0]['count'] : $numberOfGroups;
    }

    public function findAllForCatalogueWidgetIndex(
        int $brandId,
        string $currencyCode,
        int $retailerId,
        ?string $productType = null,
        ?int $collectionId = null,
        ?string $search = null,
        ?array $sort = null,
        ?int $limit = null,
        ?int $page = null
    ): array
    {
        /** @var User $User */
        $User = ClassRegistry::init('User');

        $originalVirtualFields = $this->virtualFields;
        $originalOrder = $this->order;

        try {
            // Use brand-level currency if product sync does not set product-level currency
            $brandCurrency = (string)$User->field('currency_code', ["{$User->alias}.id" => $brandId]);
            $this->virtualFields['currency'] = "IF(`{$this->alias}`.`currency`, `{$this->alias}`.`currency`, '{$brandCurrency}')";

            $conditions = $this->_getConditionsForCatalogueWidgetIndex($brandId, $retailerId, $productType, $collectionId, $search);

            $pricingSubquery = $this->buildSubquery([
                'recursive' => -1,
                'joins' => [
                    [
                        'table' => 'product_prices',
                        'alias' => 'RegionalPrice',
                        'type' => 'LEFT',
                        'conditions' => [
                            'RegionalPrice.product_id' => $this->primaryKeyIdentifier(),
                            'RegionalPrice.currency_code' => $currencyCode,
                        ],
                    ],
                    [
                        'table' => $this->_subqueryForCatalogueWidgetIndex($brandId, $retailerId, $currencyCode, $conditions, [
                            "{$this->alias}.source_product_id",
                            "{$this->alias}.product_id",
                            "{$this->alias}.is_regional",
                            "{$this->alias}.price",
                            "{$this->alias}.min_price",
                            "{$this->alias}.max_price",
                            "{$this->alias}.count_instock",
                            "{$this->alias}.count_total",
                        ]),
                        'alias' => 'MinProductPrice',
                        'type' => 'INNER',
                        'conditions' => [
                            'MinProductPrice.source_product_id' => $this->identifier("{$this->alias}.source_product_id"),
                            'MinProductPrice.price' => $this->expression("IF(`MinProductPrice`.`is_regional`, `RegionalPrice`.`price`, `{$this->alias}`.`product_price`)"),
                        ],
                    ],
                ],
                'conditions' => $conditions,
                'fields' => [
                    "{$this->alias}.source_product_id",
                    // Aggregate fields with MIN() in case there are multiple matches for MinProductPrice.price
                    "MIN(`MinProductPrice`.`product_id`) AS `product_id`",
                    "MIN(IF(`MinProductPrice`.`is_regional`, `RegionalPrice`.`currency_code`, {$this->virtualFields['currency']})) AS `currency_code`",
                    "MIN(`MinProductPrice`.`price`) AS `price`",
                    "MIN(`MinProductPrice`.`min_price`) AS `min_price`",
                    "MIN(`MinProductPrice`.`max_price`) AS `max_price`",
                    "MIN(IF(`MinProductPrice`.`is_regional`, `RegionalPrice`.`compare_at_price`, `{$this->alias}`.`compare_at_price`)) AS `compare_at_price`",
                    "MIN(`MinProductPrice`.`count_instock`) AS `count_instock`",
                    "MIN(`MinProductPrice`.`count_total`) AS `count_total`",
                ],
                'group' => ["{$this->alias}.source_product_id"],
            ]);

            // Declare virtual fields referencing subquery fields after creating the subquery to avoid conflicts
            $this->virtualFields['currency'] = "ProductPrice.currency_code";
            $this->virtualFields['product_price'] = "ProductPrice.price";
            $this->virtualFields['lowest_price'] = "ProductPrice.min_price";
            $this->virtualFields['highest_price'] = "ProductPrice.max_price";
            $this->virtualFields['compare_at_price'] = "ProductPrice.compare_at_price";
            $this->virtualFields['count_instock'] = "ProductPrice.count_instock";
            $this->virtualFields['count_total'] = "ProductPrice.count_total";

            $this->order = [
                "{$this->virtualFields['count_instock']} > 0" => 'DESC',
                "{$this->alias}.sort_order" => 'ASC',
                // This query requires a unique sort field to be explicitly declared as a fallback,
                // otherwise ordering is non-deterministic if all other sort fields are equal.
                "{$this->alias}.id" => 'ASC',
            ];

            $products = (array)$this->find('all', [
                'recursive' => -1,
                'joins' => [
                    [
                        'table' => $pricingSubquery,
                        'alias' => 'ProductPrice',
                        'type' => 'INNER',
                        'conditions' => [
                            "ProductPrice.source_product_id = {$this->alias}.source_product_id",
                            "ProductPrice.product_id = {$this->alias}.id",
                        ],
                    ],
                ],
                'fields' => [
                    "{$this->alias}.id",
                    "{$this->alias}.product_title_id",
                    "{$this->alias}.product_price",
                    "{$this->alias}.lowest_price",
                    "{$this->alias}.highest_price",
                    "{$this->alias}.compare_at_price",
                    "{$this->alias}.product_image",
                    "{$this->alias}.currency",
                    "{$this->alias}.product_name",
                    "{$this->alias}.count_instock",
                    "{$this->alias}.count_total",
                ],
                'order' => $sort,
                'limit' => $limit,
                'page' => $page,
            ]);

            return array_map(function($product) {
                $product['Product']['product_price'] = Currency::formatAsDecimal($product['Product']['product_price'], $product['Product']['currency']);
                $product['Product']['lowest_price'] = Currency::formatAsDecimal($product['Product']['lowest_price'], $product['Product']['currency']);
                $product['Product']['highest_price'] = Currency::formatAsDecimal($product['Product']['highest_price'], $product['Product']['currency']);
                if ($product['Product']['compare_at_price'] !== null) {
                    $product['Product']['compare_at_price'] = Currency::formatAsDecimal($product['Product']['compare_at_price'], $product['Product']['currency']);
                }

                return $product;
            },  $products);
        } finally {
            $this->order = $originalOrder;
            $this->virtualFields = $originalVirtualFields;
        }
    }

    public function fetchVariantsGroupedByProductTitleId(array $productTitleIds, $brandId): array
    {
        if (empty($productTitleIds)) {
            return [];
        }

        return (array)$this->find('list', [
            'conditions' => [
                "{$this->alias}.product_title_id" => $productTitleIds,
                "{$this->alias}.user_id" => $brandId,
                "{$this->alias}.source_product_id !=" => null,
                "{$this->alias}.sell_direct !=" => ProductSellDirect::EXCLUSIVELY,
            ],
            'fields' => [
                "{$this->alias}.id",
                "{$this->alias}.id",
                "{$this->alias}.product_title_id",
            ],
            'recursive' => -1,
        ]);
    }

    private function _subqueryForCatalogueWidgetIndex(int $brandId, int $retailerId, string $currencyCode, array $conditions, array $fields = []): string
    {
        /** @var Store $Store */
        $Store = ClassRegistry::init('Store');
        /** @var WarehouseProduct $WarehouseProduct */
        $WarehouseProduct = ClassRegistry::init('WarehouseProduct');

        $originalVirtualFields = $this->virtualFields;

        try {
            $dealerInventorySql = "COALESCE({$Store->alias}.inventoryCount, 0)";
            $warehouseInventorySql = "IF({$WarehouseProduct->alias}.product_id, {$WarehouseProduct->alias}.available_quantity, 0)";
            $maxQuantitySql = $this->_getMaxQuantitySqlForCatalogueWidget($dealerInventorySql, $warehouseInventorySql);

            $isRegionalPriceSql = "COUNT(`RegionalPrice`.`product_id`) = COUNT(`{$this->alias}`.`id`)";

            $virtualFields = [
                'source_product_id' => "{$this->alias}.source_product_id",
                'product_id' => "MIN(`{$this->alias}`.`id`)",
                'is_regional' => $isRegionalPriceSql,
                'price' => "IF({$isRegionalPriceSql}, MIN(`RegionalPrice`.`price`), MIN(`{$this->alias}`.`product_price`))",
                'min_price' => "IF({$isRegionalPriceSql}, MIN(`RegionalPrice`.`price`), MIN(`{$this->alias}`.`product_price`))",
                'max_price' => "IF({$isRegionalPriceSql}, MAX(`RegionalPrice`.`price`), MAX(`{$this->alias}`.`product_price`))",
                'count_instock' => "COUNT(`{$Store->alias}`.`productId`)",
                'count_total' => "COUNT(`{$this->alias}`.`id`)",
                'not_sold_out' => "MAX(COALESCE({$maxQuantitySql}, 1)) > 0",
            ];
            $this->virtualFields = array_merge($this->virtualFields, $virtualFields);

            $conditions = array_merge($conditions, ["{$this->alias}.user_id" => $brandId]);

            $having = [$virtualFields['not_sold_out']];

            $sql = $this->buildSubquery([
                'recursive' => -1,
                'joins' => [
                    [
                        'table' => 'product_prices',
                        'alias' => 'RegionalPrice',
                        'type' => 'LEFT',
                        'conditions' => [
                            'RegionalPrice.product_id' => $this->primaryKeyIdentifier(),
                            'RegionalPrice.currency_code' => $currencyCode,
                        ],
                    ],
                    [
                        'table' => $Store->buildInventoryCountSubquery([
                            "{$Store->alias}.storeId" => $retailerId,
                            "{$Store->alias}.inventoryCount >" => 0,
                        ]),
                        'alias' => $Store->alias,
                        'type' => 'LEFT',
                        'conditions' => [
                            "{$Store->alias}.productId" => $this->primaryKeyIdentifier(),
                        ],
                    ],
                    [
                        'table' => $WarehouseProduct->buildProductAggregateSubquery(
                            [
                                "{$WarehouseProduct->alias}.product_id",
                                $this->getDboSource()->calculate($WarehouseProduct, 'max', ['available_quantity']),
                            ],
                            [
                                "{$WarehouseProduct->Warehouse->alias}.user_id" => $brandId,
                                "{$WarehouseProduct->alias}.warehouse_id !=" => $this->_listRetailerHiddenWarehouseIds($brandId, $retailerId),
                            ]
                        ),
                        'alias' => $WarehouseProduct->alias,
                        'type' => 'LEFT',
                        'conditions' => [
                            "{$WarehouseProduct->alias}.product_id" => $this->primaryKeyIdentifier(),
                        ],
                    ],
                ],
                'conditions' => $conditions,
                'fields' => $fields ?: array_keys($virtualFields),
                'group' => ["{$this->alias}.source_product_id"],
                'having' => $having,
            ]);

            return str_replace($this->alias . $this->getDboSource()->virtualFieldSeparator, '', $sql);
        } finally {
            $this->virtualFields = $originalVirtualFields;
        }
    }

    protected function _getConditionsForCatalogueWidgetIndex(int $brandId, int $retailerId, ?string $productType = null, ?int $collectionId = null, ?string $search = null): array
    {
        $conditions = $this->getConditionsForActiveProducts([
            "{$this->alias}.user_id" => $brandId,
            "{$this->alias}.product_title_id !=" => null,
            "{$this->alias}.source_product_id !=" => null,
            "{$this->alias}.sell_direct !=" => ProductSellDirect::EXCLUSIVELY,
        ]);
        $conditions = $this->_setRetailerCategoryMatchCondition($retailerId, $conditions);
        $conditions = $this->_setStoreConnectionConditions($brandId, $retailerId, $conditions);

        $conditions = $this->_setCollectionsCondition(array_filter((array)$collectionId), $conditions);
        $conditions = $this->_setProductTypesCondition(array_filter((array)$productType), $conditions);
        if ($search) {
            $conditions[] = $this->buildSearchCondition($search);
        }

        return $conditions;
    }

    public function findForCatalogueWidgetView(int $brandId, string $currencyCode, int $retailerId, int $productId, array $cartQuantityById): array
    {
        /** @var Store $Store */
        $Store = ClassRegistry::init('Store');
        /** @var WarehouseProduct $WarehouseProduct */
        $WarehouseProduct = ClassRegistry::init('WarehouseProduct');

        $originalVirtualFields = $this->virtualFields;

        $dealerInventorySql = "COALESCE({$Store->alias}.inventoryCount, 0)";
        $warehouseInventorySql = "IF({$WarehouseProduct->alias}.product_id, {$WarehouseProduct->alias}.available_quantity, 0)";
        $maxQuantitySql = $this->_getMaxQuantitySqlForCatalogueWidget($dealerInventorySql, $warehouseInventorySql);

        $this->virtualFields['dealer_inventory'] = $dealerInventorySql;
        $this->virtualFields['max_quantity'] = $maxQuantitySql;

        $conditions = array_merge($this->_getConditionsForCatalogueWidgetIndex($brandId, $retailerId), [
            "{$this->alias}.source_product_id" => $this->field('source_product_id', ['id' => $productId]),
        ]);

        $this->bindModel([
            'hasAndBelongsToMany' => [
                'VariantOption' => [
                    'with' => 'ProductVariantOption',
                    'unique' => 'keepExisting',
                ],
            ]
        ], false);

        $products = $this->findAllWithRegionalPricing($brandId, $currencyCode, [
            'contain' => [
                    'VariantOption' => [
                        'fields' => ['id', 'name', 'position'],
                        'with' => ['ProductVariantOption' => ['value']],
                        'order' => ['VariantOption.position' => 'ASC'],
                    ]
            ],
            'joins' => [
                [
                    'table' => $Store->buildInventoryCountSubquery([
                        "{$Store->alias}.storeId" => $retailerId,
                        "{$Store->alias}.inventoryCount >" => 0,
                    ]),
                    'alias' => $Store->alias,
                    'type' => 'LEFT',
                    'conditions' => [
                        "{$Store->alias}.productId" => $this->primaryKeyIdentifier(),
                    ],
                ],
                [
                    'table' => $WarehouseProduct->buildProductAggregateSubquery(
                        [
                            "{$WarehouseProduct->alias}.product_id",
                            $this->getDboSource()->calculate($WarehouseProduct, 'max', ['available_quantity']),
                        ],
                        [
                            "{$WarehouseProduct->Warehouse->alias}.user_id" => $brandId,
                            "{$WarehouseProduct->alias}.warehouse_id !=" => $this->_listRetailerHiddenWarehouseIds($brandId, $retailerId),
                        ]
                    ),
                    'alias' => $WarehouseProduct->alias,
                    'type' => 'LEFT',
                    'conditions' => [
                        "{$WarehouseProduct->alias}.product_id" => $this->primaryKeyIdentifier(),
                    ],
                ],
            ],
            'conditions' => $conditions,
            'fields' => [
                "{$this->alias}.id",
                "{$this->alias}.product_title_id",
                "{$this->alias}.product_sku",
                "{$this->alias}.product_price",
                "{$this->alias}.compare_at_price",
                "{$this->alias}.product_description",
                "{$this->alias}.product_image",
                "{$this->alias}.currency",
                "{$this->alias}.product_name",
                "{$this->alias}.variant_options",
                "{$this->alias}.dealer_inventory",
                "{$this->alias}.max_quantity",
            ],
            'order' => ["{$this->alias}.sort_order" => 'ASC'],
        ]);

        $this->virtualFields = $originalVirtualFields;

        $products = array_map(function(array $product) use ($cartQuantityById): array {
            $product[$this->alias] = array_merge($product[$this->alias], [
                'product_price' => $product['RegionalPrice']['price'],
                'compare_at_price' => $product['RegionalPrice']['compare_at_price'],
                'currency' => $product['RegionalPrice']['currency_code'],
            ]);
            unset($product['RegionalPrice']);

            $cartQuantity = (int)($cartQuantityById[$product[$this->alias]['id']] ?? 0);
            if ($product[$this->alias]['max_quantity'] !== null && $cartQuantity > 0) {
                $product[$this->alias]['max_quantity'] = strval((int)$product[$this->alias]['max_quantity'] - $cartQuantity);
            }

            return $product;
        }, $products);

        $product = [
            $this->alias => (array)(current(Hash::extract($products, "{n}.{$this->alias}[id={$productId}]")) ?: []),
            'Variant' => array_map(function($product){
                return array_merge($product['Product'], ['VariantOption' => $product['VariantOption']]);
            }, $products),
        ];
        if (empty($product[$this->alias]['id'])) {
            return [];
        }

        $variantOptions = [];
        foreach ($products as $prod) {
            if (!empty($prod['VariantOption'])) {
                foreach ($prod['VariantOption'] as $variant) {
                    $name = $variant['name'];
                    $value = $variant['ProductVariantOption']['value'];
                    $key = mb_strtolower(Inflector::slug($name, '-'));
                    $valKey = mb_strtolower($value);
                    $variantOptions[$key]['name'] = $name;
                    $variantOptions[$key]['values'][$valKey] = $value;
                }
            }
        }
        $product[$this->alias]['variantOptions'] = $variantOptions;

        return $product;
    }

    private function _getMaxQuantitySqlForCatalogueWidget(string $dealerInventorySql, string $warehouseInventorySql): string
    {
        $inStockOnlyQuantitySql = "IF({$this->conditions(["{$this->alias}.is_in_stock_only" => true])}, {$dealerInventorySql}, NULL)";
        $combinedQuantitySql = "IF({$this->conditions(["{$this->alias}.enable_oversell" => false])}, {$warehouseInventorySql} + {$dealerInventorySql}, NULL)";

        return "COALESCE({$inStockOnlyQuantitySql}, {$combinedQuantitySql})";
    }

    /**
     * @param int $brandId
     * @param int $retailerId
     * @return int[] A set of warehouse ids to be excluded
     */
    private function _listRetailerHiddenWarehouseIds(int $brandId, int $retailerId): array
    {
        return ($this->_listHiddenWarehouseIdsByRetailerId($brandId, [$retailerId])[$retailerId] ?? []);
    }

    /**
     * @param int $brandId
     * @param int[] $retailerIds
     * @return int[][] Sets of warehouse ids to be excluded keyed by retailer id
     */
    private function _listHiddenWarehouseIdsByRetailerId(int $brandId, array $retailerIds): array
    {
        /** @var ManufacturerRetailer $ManufacturerRetailer */
        $ManufacturerRetailer = ClassRegistry::init('ManufacturerRetailer');
        /** @var PricingTier $PricingTier */
        $PricingTier = ClassRegistry::init('PricingTier');

        $storeConnections = $ManufacturerRetailer->findStoreConnections($brandId, $retailerIds, [
            'id',
            'retailer_id',
            'pricingtierid',
        ]);
        $pricingTierIdByRetailerId = array_column($storeConnections, 'pricingtierid', 'retailer_id');
        $pricingTierExcludedWarehouses = $PricingTier->fetchHiddenWarehousesByPricingTierIds($pricingTierIdByRetailerId);

        return array_map(function($pricingTierId) use ($pricingTierExcludedWarehouses) {
            return (array)($pricingTierExcludedWarehouses[$pricingTierId] ?? []);
        }, $pricingTierIdByRetailerId);
    }

    public function findAllForShoppingCartWidget(int $brandId, string $currencyCode, ?int $retailerId, array $quantityById): array
    {
        /** @var Store $Store */
        $Store = ClassRegistry::init('Store');
        /** @var WarehouseProduct $WarehouseProduct */
        $WarehouseProduct = ClassRegistry::init('WarehouseProduct');

        $originalVirtualFields = $this->virtualFields;

        $dealerInventorySql = ($retailerId) ? "COALESCE({$Store->alias}.inventoryCount, 0)" : 'NULL';
        $warehouseInventorySql = "IF({$WarehouseProduct->alias}.product_id, {$WarehouseProduct->alias}.available_quantity, 0)";
        $maxQuantitySql = $this->_getMaxQuantitySqlForCatalogueWidget($dealerInventorySql, $warehouseInventorySql);

        $this->virtualFields['max_quantity'] = $maxQuantitySql;

        $conditions = $this->getConditionsForActiveProducts([
            "{$this->alias}.id" => array_keys($quantityById),
            "{$this->alias}.user_id" => $brandId,
            "{$this->alias}.source_product_id !=" => null,
            "{$this->alias}.sell_direct !=" => ProductSellDirect::EXCLUSIVELY,
        ]);
        if ($retailerId) {
            $conditions = $this->_setRetailerCategoryMatchCondition($retailerId, $conditions);
            $conditions = $this->_setStoreConnectionConditions($brandId, $retailerId, $conditions);
        }

        $products = $this->findAllWithRegionalPricing($brandId, $currencyCode, [
            'joins' => [
                [
                    'table' => $Store->buildInventoryCountSubquery([
                        "{$Store->alias}.storeId" => $retailerId,
                        "{$Store->alias}.inventoryCount >" => 0,
                    ]),
                    'alias' => $Store->alias,
                    'type' => 'LEFT',
                    'conditions' => [
                        "{$Store->alias}.productId" => $this->primaryKeyIdentifier(),
                    ],
                ],
                [
                    'table' => $WarehouseProduct->buildProductAggregateSubquery(
                        [
                            "{$WarehouseProduct->alias}.product_id",
                            $this->getDboSource()->calculate($WarehouseProduct, 'max', ['available_quantity']),
                        ],
                        [
                            "{$WarehouseProduct->Warehouse->alias}.user_id" => $brandId,
                            "{$WarehouseProduct->alias}.warehouse_id !=" => ($retailerId ? $this->_listRetailerHiddenWarehouseIds($brandId, $retailerId) : []),
                        ]
                    ),
                    'alias' => $WarehouseProduct->alias,
                    'type' => 'LEFT',
                    'conditions' => [
                        "{$WarehouseProduct->alias}.product_id" => $this->primaryKeyIdentifier(),
                    ],
                ],
            ],
            'conditions' => $conditions,
            'fields' => [
                "{$this->alias}.id",
                "{$this->alias}.product_price",
                "{$this->alias}.product_image",
                "{$this->alias}.currency",
                "{$this->alias}.product_name",
                "{$this->alias}.variant_options",
                "{$this->alias}.max_quantity",
            ],
            'order' => ["{$this->alias}.sort_order" => 'ASC'],
        ]);

        $this->virtualFields = $originalVirtualFields;

        $products = array_map(function($product) use ($quantityById): array {
            $product[$this->alias] = array_merge($product[$this->alias], [
                'product_price' => $product['RegionalPrice']['price'],
                'compare_at_price' => $product['RegionalPrice']['compare_at_price'],
                'currency' => $product['RegionalPrice']['currency_code'],
            ]);
            unset($product['RegionalPrice']);

            $quantity = (int)$quantityById[$product[$this->alias]['id']] ?: 1;

            $product[$this->alias] = array_merge($product[$this->alias], [
                'quantity' => $quantity,
            ]);

            return $product;
        }, $products);

        return array_column($products, $this->alias);
    }

    public function findAllForCheckoutWidget(int $brandId, string $currencyCode, array $quantityById): array
    {
        $products = $this->findAllWithRegionalPricing($brandId, $currencyCode, [
            'conditions' => $this->getConditionsForActiveProducts([
                "{$this->alias}.id" => array_keys($quantityById),
            ]),
            'fields' => [
                "{$this->alias}.id",
                "{$this->alias}.productID",
                "{$this->alias}.product_price",
                "{$this->alias}.compare_at_price",
                "{$this->alias}.product_image",
                "{$this->alias}.currency",
                "{$this->alias}.product_name",
                "{$this->alias}.variant_options",
            ],
        ]);

        $products = array_map(function(array $product) use ($quantityById): array {
            $product[$this->alias] = array_merge($product[$this->alias], [
                'product_price' => $product['RegionalPrice']['price'],
                'compare_at_price' => $product['RegionalPrice']['compare_at_price'],
                'currency' => $product['RegionalPrice']['currency_code'],
            ]);
            unset($product['RegionalPrice']);

            $quantity = (int)$quantityById[$product[$this->alias]['id']] ?: 1;

            $product[$this->alias] = array_merge($product[$this->alias], [
                'quantity' => $quantity,
            ]);

            return $product;
        }, $products);

        return array_column($products, $this->alias);
    }

    public function findAllForProductLocator(int $brandId, string $currencyCode, array $quantityById): array
    {
        $products = $this->findAllWithRegionalPricing($brandId, $currencyCode, [
            'conditions' => $this->getConditionsForActiveProducts([
                "{$this->alias}.id" => array_keys($quantityById),
            ]),
            'fields' => [
                "{$this->alias}.id",
                "{$this->alias}.source_product_id",
                "{$this->alias}.product_price",
                "{$this->alias}.compare_at_price",
                "{$this->alias}.product_image",
                "{$this->alias}.currency",
                "{$this->alias}.product_name",
                "{$this->alias}.variant_options",
            ],
        ]);
        $variantsMap = (array)$this->find('list', [
            'recursive' => -1,
            'conditions' => $this->getConditionsForActiveProducts([
                "{$this->alias}.user_id" => $brandId,
                "{$this->alias}.source_product_id" => array_filter(Hash::combine($products, "{n}.{$this->alias}.source_product_id", "{n}.{$this->alias}.source_product_id")),
            ]),
            'fields' => ['id', 'variant_options', 'source_product_id'],
        ]);

        return array_map(function(array $product) use ($quantityById, $variantsMap): array {
            $product[$this->alias] = array_merge($product[$this->alias], [
                'product_price' => $product['RegionalPrice']['price'],
                'compare_at_price' => $product['RegionalPrice']['compare_at_price'],
                'currency' => $product['RegionalPrice']['currency_code'],
            ]);
            unset($product['RegionalPrice']);

            $quantity = (int)$quantityById[$product[$this->alias]['id']] ?: 1;
            $price = (float)$product[$this->alias]['product_price'];
            $currency = (string)$product[$this->alias]['currency'];
            $linePriceDecimal = Currency::formatAsDecimal($quantity * $price, $currency);

            $product[$this->alias] = array_merge($product[$this->alias], [
                'product_image' => $product[$this->alias]['product_image'] ?: (BASE_PATH . 'images/no_img.gif'),
                'quantity' => $quantity,
                'line_price' => $linePriceDecimal,
                'amount_format' => ($linePriceDecimal . ' ' . $currency),
                'variants' => $variantsMap[$product[$this->alias]['source_product_id']],
            ]);

            return $product;
        }, $products);
    }

    public function findAllAsWsWithTokenCartItems(int $brandId, string $currencyCode, array $quantityById): array
    {
        $products = $this->findAllWithRegionalPricing($brandId, $currencyCode, [
            'conditions' => $this->getConditionsForActiveProducts([
                "{$this->alias}.id" => array_keys($quantityById),
            ]),
            'fields' => [
                "{$this->alias}.id",
                "{$this->alias}.uuid",
                "{$this->alias}.productID",
                "{$this->alias}.product_price",
                "{$this->alias}.compare_at_price",
            ],
        ]);

        return array_map(fn(array $product): array => [
            'key' => "{$product['Product']['productID']}:{$product['Product']['uuid']}",
            'variant_id' => $product['Product']['productID'],
            'quantity' => (int)$quantityById[$product['Product']['id']],
            'price' => (int)round($product['RegionalPrice']['price'] * 100),
            'compare_at_price' => isset($product['RegionalPrice']['compare_at_price']) ? (int)round($product['RegionalPrice']['compare_at_price'] * 100) : null,
        ], $products);
    }

    public function findAllForEcommerceGetRetailerIds($brandId, array $itemByProductID): array
    {
        return $this->_findAllForEcommerceRetailer($brandId, $itemByProductID, [], [
            $this->alias . '.id',
            $this->alias . '.productID',
            $this->alias . '.product_upc',
            $this->alias . '.assembly_option',
            $this->alias . '.enable_oversell',
            $this->alias . '.installation_hours',
            $this->alias . '.sell_direct',
            $this->alias . '.is_in_stock_only',
            $this->alias . '.store_pickup_option',
            $this->alias . '.ship_from_store',
            $this->alias . '.is_fee_product',
            // Useful for logs
            $this->alias . '.source_product_id',
            $this->alias . '.product_title',
        ]);
    }

    public function findAllForEcommerceShippingRates($brandId, array $itemByProductID): array
    {
        return $this->_findAllForEcommerceRetailer($brandId, $itemByProductID, [], [
            $this->alias . '.id',
            $this->alias . '.productID',
            $this->alias . '.product_type',
            $this->alias . '.product_price',
            $this->alias . '.compare_at_price',
            $this->alias . '.weight',
            $this->alias . '.weight_unit',
            $this->alias . '.sell_direct',
        ]);
    }

    public function findAllForEcommerceCheckProduct($brandId, array $itemByProductID, bool $buyDirectOnly): array
    {
        $conditions = (!$buyDirectOnly)
            ? [$this->alias . '.sell_direct !=' => ProductSellDirect::EXCLUSIVELY]
            : [];

        return $this->_findAllForEcommerceRetailer($brandId, $itemByProductID, $conditions, [
            $this->alias . '.id',
            $this->alias . '.user_id',
            $this->alias . '.productID',
            $this->alias . '.product_upc',
            $this->alias . '.taxcode',
            $this->alias . '.product_price',
            $this->alias . '.compare_at_price',
            $this->alias . '.weight',
            $this->alias . '.weight_unit',
            $this->alias . '.assembly_option',
            $this->alias . '.brand_inventory',
            $this->alias . '.non_stocking',
            $this->alias . '.installation_hours',
            $this->alias . '.is_fee_product',
            // Useful for logs
            $this->alias . '.source_product_id',
            $this->alias . '.product_title',
        ]);
    }

    public function findAllForEcommerceCheckSellExclusiveProduct($brandId, array $itemByProductID): array
    {
        return $this->_findAllForEcommerceRetailer($brandId, $itemByProductID, [
            $this->alias . '.sell_direct' => ProductSellDirect::EXCLUSIVELY,
        ], [
            $this->alias . '.id',
            $this->alias . '.user_id',
            $this->alias . '.productID',
            $this->alias . '.product_upc',
            $this->alias . '.taxcode',
            $this->alias . '.product_price',
            $this->alias . '.compare_at_price',
            $this->alias . '.weight',
            $this->alias . '.weight_unit',
            $this->alias . '.assembly_option',
            $this->alias . '.brand_inventory',
            // Useful for logs
            $this->alias . '.source_product_id',
            $this->alias . '.product_title',
        ]);
    }

    private function _findAllForEcommerceRetailer($brandId, array $itemByProductID, array $conditions, array $fields): array
    {
        /** @var WarehouseProduct $WarehouseProduct */
        $WarehouseProduct = ClassRegistry::init('WarehouseProduct');

        $baseConditions = [
            $this->alias . '.user_id' => $brandId,
            $this->alias . '.productID' => array_keys($itemByProductID),
            $this->alias . '.product_status' => Product::STATUS_ENABLED,
            $this->alias . '.deleted' => false,
        ];

        $requiredFields = [
            $this->alias . '.id',
            $this->alias . '.productID',
        ];

        $productsInfo = $this->find('all', [
            'recursive' => -1,
            'fields' => array_merge($requiredFields, $fields),
            'conditions' => $baseConditions + $conditions,
        ]);

        return array_map(
            function($product) use ($itemByProductID, $WarehouseProduct) {
                $id = $product[$this->alias]['id'];
                $productID = $product[$this->alias]['productID'];

                $item = $itemByProductID[$productID] ?? [];

                if (isset($item['price']) && array_key_exists('product_price', $product[$this->alias])) {
                    $product[$this->alias]['product_price'] = $item['price'];
                }
                if (isset($item['compare_at_price']) && array_key_exists('compare_at_price', $product[$this->alias])) {
                    $product[$this->alias]['compare_at_price'] = $item['compare_at_price'];
                }

                $product[$this->alias]['quantity'] = (int)($item['quantity'] ?? $item['qty'] ?? 0);
                $product[$this->alias]['restock_date'] = $WarehouseProduct->getRestockDate((int)$this->getBestWarehouseId($id), $id);

                return $product;
            },
            $productsInfo
        );
    }

    /**
     * @param int|int[] $ids
     * @param array $fields
     * @return bool
     */
    protected function _updateAllById($ids, $fields)
    {
        $result = $this->updateAll(array_map([$this, 'value'], $fields), [$this->alias . '.id' => $ids]);
        if ($result) {
            $this->_clearProductCache($ids);
        }
        return $result;
    }

    public function saveManyFromProductImport(array $data): bool
    {
        return $this->saveManyAssociatedFast($data);
    }

    public function saveProductTitleSortOrderImport($userId, $tableMap, $importHeaders)
    {
        $existing = $this->find('all', [
            'recursive' => -1,
            'conditions' => [
                'user_id' => $userId,
                'deleted' => false,
            ],
            'fields' => ['id', 'source_product_id', 'sort_order'],
            'order' => 'published_at DESC'
        ]);

        $tableMap = array_map(function ($product, $sortValue) {
            $product['sort_order'] = $sortValue;
            return $product;
        }, $tableMap, array_keys($tableMap));

        $postImportedProductOrderSeed = count($tableMap);

        $tableMap = Hash::combine($tableMap, "{n}.{$importHeaders['source_product_id']}", '{n}');
        $save = array_map(function ($product) use ($tableMap, &$postImportedProductOrderSeed) {
            $sortOrder = ($product[$this->alias]['sort_order'] ?? 0) - floor($product[$this->alias]['sort_order'] ?? 0);
            if(!empty($tableMap[$product[$this->alias]['source_product_id']]))
            {
                $sortOrder += $tableMap[$product[$this->alias]['source_product_id']]['sort_order'];
            } else {
                $sortOrder += $postImportedProductOrderSeed;
                $postImportedProductOrderSeed += 1;
            }
            $product[$this->alias]['sort_order'] = $sortOrder;
            return $product;
        }, $existing);
        return $this->saveMany($save);
    }
    
    public function saveProductVariantSortOrderImport($userId, $tableMap)
    {
        list($variantSortOrderSave, $save) = $this->processVariantSortOrderImport($userId, $tableMap);

        $success = true;
        $success = $this->saveMany($save);

        /** @var VariantSortOrder $VariantSortOrder */
        $VariantSortOrder = ClassRegistry::init('VariantSortOrder');
        $success = $VariantSortOrder->deleteAllJoinless(["{$VariantSortOrder->alias}.user_id" => $userId]);
        if(!$success){
            CakeLog::error('Product Variant import failed on delete step');
        }
        $success = $VariantSortOrder->saveMany($variantSortOrderSave);
        if(!$success){
            CakeLog::error('Product Variant import failed on save step');
        }

        $this->validationErrors += $VariantSortOrder->validationErrors;

        return $success;
    }

    protected function processVariantSortOrderImport($userId, $tableMap)
    {
        $existing = $this->find('all', [
            'recursive' => -1,
            'conditions' => [
                "{$this->alias}.user_id" => $userId,
                "{$this->alias}.variant_options !=" => '',
                "{$this->alias}.deleted" => false,
            ],
            'fields' => ['id', 'productID', 'variant_options', 'sort_order'],
            'order' => 'sort_order ASC'
        ]);

        $tableMap = array_reverse($tableMap);

        $variantSortOrderSave = array_map(function ($variant, $sortValue) use ($userId){
            //sort_value_tmp is used for ordering variants to save that order
            //sort_value is used for saving to VariantSortOrder for export later. 
            $variant['sort_value_tmp'] = pow(2, ($sortValue + 1));
            $variant['sort_value'] = $sortValue;
            $variant['user_id'] = $userId;
            $variant['variant_name'] = $variant['Variant'];
            unset($variant['Variant']);
            return $variant;
        }, $tableMap, array_keys($tableMap));

        $tableMap = Hash::combine($variantSortOrderSave, "{n}.variant_name", '{n}.sort_value_tmp');
        $save = array_map(function ($product) use ($tableMap) {
            if(!empty($product['Product']['variant_options'])){
                $variantOptions = array_flip($this->decodeVariantOptions($product['Product']['variant_options']));
                $applicableCategories = array_intersect_ukey($tableMap, $variantOptions, 'strcasecmp');
                if(!empty($applicableCategories)){
                    $product['Product']['sort_order_tmp'] = array_sum($applicableCategories);
                }
            }

            return $product;
        }, $existing);

        // because sort_order_tmp can be astrnoically large, 
        // order $save by sort_order_tmp and reduce to conscutive integers by using the assigned index
        usort($save, function($a, $b){
            return $a['Product']['sort_order_tmp'] <=> $b['Product']['sort_order_tmp'];
        });

        $save = array_map(function($product, $key){
            $product['Product']['sort_order'] = floor($product['Product']['sort_order']) + (1/($key+2));
            unset($product['Product']['sort_order_tmp']);
            return $product;
        }, $save, array_keys($save));

        return [$variantSortOrderSave, $save];
    }

    public function syncEcommerceVariant(array $productinfo)
    {
        /** @var AppModel $ProductImage */
        $ProductImage = ClassRegistry::init('ProductImage');

        $existing = $this->find('first', [
            'recursive' => -1,
            'conditions' => ['user_id' => $productinfo['user_id'], 'productID' => $productinfo['productID']],
            'fields' => ['id', 'user_id', 'deleted', 'product_title', 'source_product_id', 'productID'],
        ]);
        $productId = $existing['Product']['id'] ?? null;

        if ($productId) {
            $this->clear();
            $productinfo['id'] = $productId;

            if ($existing['Product']['deleted']) {
                CakeLog::warning('Syncing an archived product ' . json_encode($existing['Product']));
                $productinfo['deleted'] = false;
            }

            $ProductImage->deleteAll(['ProductImage.product_id' => $productinfo['id']], false);
        } else {
            $this->create();

            $productinfo['product_status'] = Product::STATUS_INCOMPLETE;
        }
        $this->save($productinfo);

        if (!isset($productinfo['id'])) {
            $productinfo['id'] = $this->getLastInsertID();
            $this->_setDefaultProductCategories($productinfo['user_id'], $productinfo['id']);
        }
        return $productinfo;
    }

    private function _setDefaultProductCategories($userId, $productId)
    {
        /** @var ProductCategories $ProductCategories */
        $ProductCategories = ClassRegistry::init('ProductCategories');
        /** @var ProductRetailer $ProductRetailer */
        $ProductRetailer = ClassRegistry::init('ProductRetailer');
        /** @var UserCategories $UserCategories */
        $UserCategories = ClassRegistry::init('UserCategories');

        $categoryIds = $UserCategories->getUserCategories($userId);
        $productCategories = array_map(function($categoryId) use ($productId) {
            return [
                'cat_id' => $categoryId,
                'product_id' => $productId,
                'type' => 'shipearly',
            ];
        }, $categoryIds);

        $deleteConditions = [
            'ProductCategories.cat_id !=' => $categoryIds,
            'ProductCategories.product_id' => $productId,
            'ProductCategories.type' => 'shipearly',
        ];
        $ProductCategories->deleteAll($deleteConditions, false);
        $ProductCategories->saveMany($productCategories);

        $retailerIds = $UserCategories->getRetailers($categoryIds, $userId);
        $ProductRetailer->saveProductAssoc($productId, $retailerIds);
    }

    public function removeAll(array $conditions): bool
    {
        if (empty($conditions)) {
            return false;
        }

        $this->addAssociations(['hasOne' => ['DealerOrderProduct', 'OrderProduct']]);
        $products = $this->find('all', [
            'contain' => [
                'OrderProduct' => ['fields' => ['id']],
                'DealerOrderProduct' => ['fields' => ['id']],
            ],
            'conditions' => $conditions,
            'fields' => [
                'id',
                'user_id',
                'product_title_id',
                'source_product_id',
                'productID',
                'product_status',
                'product_title',
                'deleted',
            ],
            'group' => ["{$this->alias}.id"],
        ]);
        $this->unbindModel(['hasOne' => ['DealerOrderProduct', 'OrderProduct']], false);

        if ($products === false || $products === null) {
            return false;
        }

        $products = Hash::combine($products, "{n}.{$this->alias}.id", '{n}');

        $productsToDelete = array_filter($products, function($product) {
            return (!$product['OrderProduct']['id'] && !$product['DealerOrderProduct']['id']);
        });
        $productsToArchive = array_filter(array_diff_key($products, $productsToDelete), function($product) {
            return !$product[$this->alias]['deleted'];
        });

        if (!$productsToDelete && !$productsToArchive) {
            return true;
        }

        $log = [
            'deleted' => ['success' => true, 'products' => array_column($productsToDelete, $this->alias)],
            'archived' => ['success' => true, 'products' => array_column($productsToArchive, $this->alias)],
        ];

        if ($productsToDelete) {
            $log['deleted']['success'] = $this->deleteAllByIds(array_keys($productsToDelete));
        }
        if ($productsToArchive) {
            $log['archived']['success'] = $this->doInTransaction(function() use ($productsToArchive) {
                $idsToArchive = array_keys($productsToArchive);
                return $this->archiveAllByIds($idsToArchive) &&
                       $this->trashAll([
                           $this->alias . '.id' => $idsToArchive,
                           $this->alias . '.product_status' => Product::STATUS_ENABLED,
                       ]);
            });
        }

        $success = ($log['deleted']['success'] && $log['archived']['success']);
        if (!$success) {
            triggerWarning(json_encode($log));

            return false;
        }
        CakeLog::debug(json_encode($log));

        $productTitleIds = array_keys(Hash::combine($products, "{n}.{$this->alias}.product_title_id", "{n}.{$this->alias}.product_title_id"));
        $productTitleConditions = ['ProductTitle.id' => $productTitleIds];

        /** @var ProductTitle $ProductTitle */
        $ProductTitle = ClassRegistry::init('ProductTitle');
        $success = $ProductTitle->removeAllOrphans($productTitleConditions);
        if (!$success) {
            triggerWarning(json_encode(['message' => 'Failed to remove orphaned product titles', 'conditions' => $productTitleConditions]));
        }

        return $success;
    }

    public function trashAll($conditions)
    {
        if (empty($conditions)) {
            return false;
        }
        $ids = $this->find('all', array(
            'recursive' => -1,
            'conditions' => $conditions,
            'fields' => $this->alias . '.id',
        ));
        if ($ids === false || $ids === null) {
            return false;
        }
        $ids = Hash::extract($ids, "{n}.{$this->alias}.id");
        if (empty($ids)) {
            return true;
        }
        return $this->updateStatusByIds($ids, Product::STATUS_DISABLED);
    }

    /**
     * @param int|int[] $ids
     * @return bool Success
     */
    public function archiveAllByIds($ids)
    {
        return $this->_updateAllById($ids, [$this->alias . '.deleted' => true]);
    }

    /**
     * @param int|int[] $ids
     * @return bool Success
     */
    public function deleteAllByIds($ids)
    {
        $success = $this->doInTransaction(function() use ($ids) {
            return $this->_beforeDeleteAllByIds($ids) && $this->deleteAll(['id' => $ids], false, false);
        });
        if ($success) {
            $this->_clearProductCache($ids);
        }
        return $success;
    }

    /**
     * @param int|int[] $ids
     * @return bool
     */
    private function _beforeDeleteAllByIds($ids): bool
    {
        /** @var Viewslog $Viewslog */
        $Viewslog = ClassRegistry::init('Viewslog');

        return $Viewslog->deleteAllJoinless(['cid' => $ids, 'type' => Viewslog::TYPE_PRODUCT], false, false);
    }

    /**
     * @param int|int[] $productIds
     * @return bool TRUE if the cache entry existed and was deleted.
     */
    private function _clearProductCache($productIds)
    {
        $keys = array_map(function($productId) { return 'Products_' . $productId; }, (array)$productIds);
        if (!$keys) {
            return false;
        }
        $result = true;
        foreach ($keys as $key) {
            $result = Cache::delete($key, 'product') && $result;
        }
        return $result;
    }

    /**
     * get the warehouse with the most inventory of product id, or earliest restock_date
     *
     * @param int $product_id
     * @return int|null warehouseId or null
     */
    public function getBestWarehouseId($product_id): ?int
    {
        /** @var WarehouseProduct $WarehouseProduct */
        $WarehouseProduct = ClassRegistry::init('WarehouseProduct');

        $conditions = $WarehouseProduct->Warehouse->getConditionsForActiveWarehouses([
            "{$WarehouseProduct->alias}.product_id" => $product_id,
        ]);
        $warehouseId = $WarehouseProduct->fieldByConditions('warehouse_id', $conditions, [
            'contain' => ['Warehouse'],
            'order' => [
                "GREATEST(0, {$WarehouseProduct->virtualFields['available_quantity']})" => 'DESC',
                "COALESCE({$WarehouseProduct->alias}.restock_date, '9999-01-01')" => 'ASC',
            ],
        ]);

        return (int)$warehouseId ?: null;
    }

    public function getProductTitleSortExportQuery($userId)
    {
        $conditions = [
            'Product.user_id' => $userId,
            'Product.deleted' => false,
        ];
        $query = [
            'recursive' => -1,
            'conditions' => $conditions,
            'fields' => [
                'Product.source_product_id',
                'Product.product_name',
                'Product.product_type',
            ],
            'order' => 'Product.sort_order ASC',
            'group' => [
                'Product.source_product_id',
                'Product.product_name',
                'Product.product_type',
            ],
        ];

        return $query;
    }

    public function getProductVariantSortExportQuery($userId)
    {
        $conditions = [
            'Product.user_id' => $userId,
            'Product.deleted' => false,
        ];
        $query = [
            'recursive' => -1,
            'conditions' => $conditions,
            'fields' => [
                'Product.source_product_id',
                'Product.productID',
                'Product.product_name',
                'Product.product_title',
                'Product.product_type',
                'Product.product_upc',
                'Product.product_sku'
            ],
            'order' => ['Product.sort_order ASC'],
        ];

        return $query;
    }

    public function findForPurchaseOrderEdit($productId): array
    {
        $conditions = ["{$this->alias}.id" => $productId];
        $fields = ['id', 'product_type', 'product_name', 'product_sku', 'is_fee_product'];

        return $this->findWithTagsAndCollections($fields, $conditions);
    }

    public function findAllForEcommerceDiscount($userId, $variantIds): array
    {
        $conditions = $this->getConditionsForActiveProducts([
            "{$this->alias}.user_id" => $userId,
            "{$this->alias}.productID" => $variantIds,
        ]);
        $fields = [
            "{$this->alias}.id", 
            "{$this->alias}.productID",
            "{$this->alias}.product_type",
            "{$this->alias}.product_name",
            "{$this->alias}.product_sku",
        ];

        return $this->findWithTagsAndCollections($fields, $conditions);
    }

    public function findWithTagsAndCollections($fields, $conditions): array
    {
        $this->bindModel([
            'hasAndBelongsToMany' => [
                'Collection' => [
                    'with' => ['CollectionsProduct' => []],
                    'unique' => 'keepExisting',
                ],
                'Tag' => [
                    'with' => ['ProductTag' => []],
                    'unique' => 'keepExisting',
                ],
            ],
        ], false);

        try {
            $products = $this->find('all', [
                'contain' => [
                    'Collection' => ['fields' => ['title'], 'order' => ['Collection.title' => 'ASC']],
                    'Tag' => ['fields' => ['name'], 'order' => ['Tag.name' => 'ASC']],
                ],
                'conditions' => $conditions,
                'fields' => $fields,
            ]);

            return $this->nestAssociations((array)$products, ['Collection', 'Tag']);
        } finally {
            $this->unbindModel(['hasAndBelongsToMany' => ['Collection', 'Tag']], false);
        }
    }

    public function findAllForAbandonCart($brandId, $productIds)
    {
        $fields = [
            "{$this->alias}.productID"
        ];
        $conditions = [
            "{$this->alias}.user_id" => $brandId,
            "{$this->alias}.productID" => $productIds,
        ];

        $products = $this->findWithTagsAndCollections($fields, $conditions);
        $products = Hash::combine($products, '{n}.Product.productID', '{n}.Product');

        return $products;
    }
}
