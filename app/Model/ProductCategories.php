<?php
App::uses('AppModel', 'Model');

/**
 * ProductCategories Model.
 *
 * @property Category $Category
 * @property Product $Product
 */
class ProductCategories extends AppModel
{
    public $validate = [
        'cat_id' => [
            'numeric' => ['rule' => 'numeric', 'message' => 'Invalid cat id'],
        ],
        'product_id' => [
            'numeric' => ['rule' => 'numeric', 'message' => 'Invalid product id'],
        ],
    ];

    public $belongsTo = [
        'Category' => ['foreignKey' => 'cat_id'],
        'Product',
    ];

    public $recursive = -1;

    /**
     * @param int|int[]|stdClass $productIds
     * @param int|int[]|stdClass $retailerIds
     * @return string
     */
    public function buildRetailerCategoryMatchCondition($productIds, $retailerIds): string
    {
        return $this->buildExistsSubquery(
            [
                "{$this->alias}.product_id" => $productIds,
                'UserCategories.user_id' => $retailerIds,
            ],
            false,
            [
                [
                    'table' => 'user_categories',
                    'alias' => 'UserCategories',
                    'type' => 'INNER',
                    'conditions' => [
                        "UserCategories.category_id = {$this->alias}.cat_id",
                        'UserCategories.user_type' => 'Retailer',
                    ],
                ],
            ]
        );
    }

    public function listIdByCatIds($productId): array
    {
        return (array)$this->find('list', [
            'recursive' => -1,
            'conditions' => ["{$this->alias}.product_id" => $productId],
            'fields' => ['cat_id', 'id'],
            'group' => ["{$this->alias}.cat_id"],
        ]);
    }

    /**
     * @param int $brandId
     * @param int[] $categoryIds
     * @return array<int, int> Product Ids
     */
    public function getBrandProductIds(int $brandId, array $categoryIds): array
    {
        return (array)$this->find('list', [
            'contain' => ['Product'],
            'conditions' => [
                "{$this->alias}.cat_id" => $categoryIds,
                'Product.user_id' => $brandId,
            ],
            'fields' => ['product_id', 'product_id'],
            'group' => ["{$this->alias}.product_id"],
        ]);
    }

    /**
     * Returns the product information for the given category and user Id by joining the products and ProductCategories table.
     *
     * @param int $userId
     * @param int[] $catIds
     * @return array
     */
    public function getUserProducts($userId, $catIds): array
    {
        return (array)$this->find('list', [
            'contain' => ['Product'],
            'conditions' => [
                "{$this->alias}.cat_id" => $catIds,
                'Product.user_id' => $userId,
            ],
            'fields' => ['id', 'product_id'],
        ]);
    }

    /**
     * Delete the category information from ProductCategories table for the given category Id.
     *
     * @param array $products
     * @return bool
     */
    public function unsetCatProducts(array $products): bool
    {
        return (bool)$this->deleteAllJoinless(["{$this->alias}.id" => array_keys($products)], false);
    }
}
