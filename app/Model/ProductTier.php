<?php
App::uses('AppModel', 'Model');
App::uses('OrderType', 'Utility');

/**
 * ProductTier Model.
 *
 * @property PricingTier $PricingTier
 * @property Product $Product
 */
class ProductTier extends AppModel
{
    public $validate = [
        'pricingtierid' => [
            'naturalNumber' => ['rule' => 'naturalNumber', 'message' => 'Invalid pricing tier id'],
        ],
        'product_id' => [
            'naturalNumber' => ['rule' => 'naturalNumber', 'message' => 'Invalid product id'],
        ],
        'dealer_price' => [
            'decimal' => ['rule' => 'decimal', 'allowEmpty' => true, 'message' => 'Invalid B2B price'],
            'positive' => ['rule' => ['comparison', '>=', 0], 'message' => 'B2B price cannot be negative'],
        ],
        'dealer_base_price' => [
            'decimal' => ['rule' => 'decimal', 'allowEmpty' => true, 'message' => 'Invalid B2B base price'],
            'positive' => ['rule' => ['comparison', '>=', 0], 'message' => 'B2B base price cannot be negative'],
        ],
        'alt_nonstock_dealer_price' => [
            'decimal' => ['rule' => 'decimal', 'allowEmpty' => true, 'message' => 'Invalid Ship to Store price'],
            'positive' => ['rule' => ['comparison', '>=', 0], 'message' => 'Ship to Store price cannot be negative'],
        ],
        'commission' => [
            'decimal' => ['rule' => 'decimal', 'message' => 'Invalid commission'],
            'positive' => ['rule' => ['comparison', '>=', 0], 'message' => 'Commission cannot be negative'],
        ],
    ];

    public $belongsTo = [
        'PricingTier' => ['foreignKey' => 'pricingtierid'],
        'Product',
    ];

    public $recursive = -1;

    /**
     * Calculate 'dealer_qty_ordered' values for creating a new dealer order.
     *
     * @param array $order required fields ['id', 'user_id', 'retailer_id', 'order_type']
     * @param array $dealer_qty_ordered will set ['products'][$productId]['quantity']
     *   to provided values except on ship from store orders
     * @return array $dealer_qty_ordered
     */
    public function calcNewDealerOrderPricing(array $order, array $dealer_qty_ordered = []): array
    {
        /** @var OrderProduct $OrderProduct */
        $OrderProduct = ClassRegistry::init('OrderProduct');
        /** @var ProductStateFee $ProductStateFee */
        $ProductStateFee = ClassRegistry::init('ProductStateFee');
        /** @var Warehouse $Warehouse */
        $Warehouse = ClassRegistry::init('Warehouse');

        $orderId = $order['Order']['id'];
        $brandId = $order['Order']['user_id'];
        $retailerId = $order['Order']['retailer_id'];

        $productQuantities = Hash::combine(
            $OrderProduct->findAllWithDerivedQuantities($orderId),
            '{n}.OrderProduct.product_id',
            '{n}.OrderProduct'
        );

        if (empty($dealer_qty_ordered['products']) || $order['Order']['order_type'] === OrderType::SHIP_FROM_STORE) {
            $dealer_qty_ordered['products'] = array_intersect_key((array)Hash::get($dealer_qty_ordered, 'products'), $productQuantities);
            foreach ($productQuantities as $productId => $quantities) {
                $dealer_qty_ordered['products'][$productId]['quantity'] = $quantities['remaining_quantity'];
            }
        }

        // Remove any consumer fee items so that they do not impact added b2b fee items
        $feeProductIds = $ProductStateFee->filterFeeProductIds(array_keys($dealer_qty_ordered['products'] + $productQuantities));
        $feeProductIds = array_combine($feeProductIds, $feeProductIds);
        $productQuantities = array_diff_key($productQuantities, $feeProductIds);
        $dealer_qty_ordered['products'] = array_diff_key($dealer_qty_ordered['products'], $feeProductIds);

        $dealerPricingData = $this->find('all', [
            'contain' => [
                'PricingTier' => ['fields' => ['id', 'currencytype']],
                'Product' => ['fields' => ['id']],
            ],
            'joins' => [
                [
                    'table' => 'manufacturer_retailers',
                    'alias' => 'ManufacturerRetailer',
                    'type' => 'INNER',
                    'conditions' => [
                        "ManufacturerRetailer.pricingtierid = {$this->alias}.pricingtierid",
                        'ManufacturerRetailer.user_id' => $brandId,
                        'ManufacturerRetailer.retailer_id' => $retailerId,
                    ],
                ],
            ],
            'conditions' => [
                "{$this->alias}.product_id" => array_keys($dealer_qty_ordered['products']),
                'Product.user_id' => $brandId,
            ],
            'fields' => [
                "{$this->alias}.id",
                "{$this->alias}.product_id",
                "{$this->alias}.pricingtierid",
                "{$this->alias}.dealer_price",
                "{$this->alias}.alt_nonstock_dealer_price",
                'ManufacturerRetailer.id',
                'ManufacturerRetailer.b2b_tax',
            ],
        ]);

        foreach ($dealerPricingData as $pricingData) {
            $productId = $pricingData[$this->alias]['product_id'];

            $dealer_qty_ordered['products'][$productId]['dealer_price'] = $pricingData[$this->alias]['alt_nonstock_dealer_price'] ??
                                                                          $pricingData[$this->alias]['dealer_price'];
            $dealer_qty_ordered['currencytype'] = $pricingData['PricingTier']['currencytype'];
            $dealer_qty_ordered['b2b_tax'] = $pricingData['ManufacturerRetailer']['b2b_tax'];
        }

        $dealer_qty_ordered = $ProductStateFee->applyToDealerQtyOrderedProducts($order, $dealer_qty_ordered);

        $topupProductIds = array_keys(array_diff_key($dealer_qty_ordered['products'], $productQuantities));
        $topupWarehouseIds = [];
        if ($topupProductIds) {
            $topupWarehouseIds = $Warehouse->listPreferredWarehouseIdsByProductId($brandId, $retailerId, array_map(
                function($productId) use ($dealer_qty_ordered) {
                    return [
                        'product_id' => $productId,
                        'quantity' => $dealer_qty_ordered['products'][$productId]['quantity'],
                    ];
                },
                $topupProductIds
            ));
        }
        foreach (array_keys($dealer_qty_ordered['products']) as $productId) {
            $dealer_qty_ordered['products'][$productId]['quantity'] += $productQuantities[$productId]['refunded_quantity'] ?? 0;
            $dealer_qty_ordered['products'][$productId]['warehouse_id'] = $productQuantities[$productId]['warehouse_id'] ?? $topupWarehouseIds[$productId] ?? null;
        }

        return $dealer_qty_ordered;
    }
}
