<?php

use ShipEarlyApp\Lib\Model\FastModelValidator;

App::uses('AppModel', 'Model');
App::uses('SoftDeletableBehaviorTrait', 'Model/Behavior/Trait');
App::uses('TranslateBehaviorTrait', 'Model/Behavior/Trait');
App::uses('ProductSourceType', 'Utility');

/**
 * ProductTitle Model.
 *
 * @property User $User
 * @property Product $Product
 * @property VariantOption $VariantOption
 */
class ProductTitle extends AppModel
{
    use SoftDeletableBehaviorTrait;
    use TranslateBehaviorTrait;

    public $displayField = 'title';

    public $validate = [
        'user_id' => [
            'numeric' => ['rule' => 'numeric', 'message' => 'Invalid user id'],
        ],
        'source_id' => [
            'notBlank' => ['rule' => 'notBlank', 'allowEmpty' => true, 'message' => 'This field cannot be left blank'],
        ],
        'source_type' => [
            'notBlank' => ['rule' => 'notBlank', 'allowEmpty' => true, 'message' => 'This field cannot be left blank'],
            'inList' => ['rule' => ['inList', ProductSourceType::ALL], 'message' => 'Invalid source type'],
        ],
        'handle' => [
            'notBlank' => ['rule' => 'notBlank', 'required' => 'create', 'allowEmpty' => true, 'message' => 'Product handle is required'],
            'isUnique' => ['rule' => ['isUnique', ['user_id', 'handle'], false], 'message' => 'Product handle is a duplicate'],
        ],
        'title' => [
            'notBlank' => ['rule' => 'notBlank', 'required' => 'create', 'message' => 'Product title is required'],
        ],
        'created_at' => [
            'datetime' => ['rule' => 'datetime', 'message' => 'Invalid datetime'],
        ],
        'updated_at' => [
            'datetime' => ['rule' => 'datetime', 'message' => 'Invalid datetime'],
        ],
    ];

    public $actsAs = [
        'Translate' => [
            'className' => 'AppTranslate',
            'joinType' => 'LEFT',
            'title',
            'description',
        ],
    ];

    public $belongsTo = [
        'User',
    ];

    public $hasMany = [
        'Product',
        'VariantOption',
    ];

    public $recursive = -1;

    public $virtualFields = [];

    /**
     * Normalize handle values received from user input to avoid conflicting with the model's unique key.
     *
     * @param string $handle
     * @return string
     */
    public static function normalizeHandle(string $handle): string
    {
        return mb_strtolower($handle);
    }

    public function initialize(array $config)
    {
        parent::initialize($config);

        $this->addBehavior('SoftDeletable', [
            'additionalUpdates' => ["{$this->alias}.handle" => 'NULL'],
        ]);

        $this->virtualFields['lower_case'] = "LOWER({$this->alias}.{$this->displayField})";
    }

    public function findAllExistingForProductImport(int $userId, array $handles, array $titleIds = []): array
    {
        $conditions = ["{$this->alias}.user_id" => $userId];

        $idCondition = ["{$this->alias}.id" => $titleIds];
        if ($handles) {
            $conditions = array_merge($conditions, [
                'OR' => [$idCondition, "{$this->alias}.handle" => $handles],
            ]);
        } else {
            $conditions = array_merge($conditions, $idCondition);
        }

        return (array)$this->find('all', [
            'contain' => [
                'VariantOption' => [
                    'fields' => array_merge(['id', 'product_title_id', 'position'], $this->VariantOption->fields(['lower_case'])),
                    'order' => ['VariantOption.position' => 'ASC'],
                ],
            ],
            'conditions' => $conditions,
            'fields' => ['id', 'handle', 'title', 'source_id', 'source_type'],
        ]);
    }

    /**
     * @param array $data Record data to save keyed by lower case handle
     * @return bool
     */
    public function saveManyFromProductImport(array $data): bool
    {
        $originalValidator = $this->validator();

        try {
            // Replace original isUnique validation rule with an equivalent bulk query
            $this->validator(new FastModelValidator($this))
                ->remove('handle', 'isUnique');

            $extractUniqueValues = fn(array $data, string $path): array => array_keys(array_flip(array_filter(Hash::extract($data, $path))));
            $duplicateHandles = (array)$this->find('list', [
                'conditions' => [
                    "{$this->alias}.user_id" => $extractUniqueValues($data, "{s}.{$this->alias}.user_id"),
                    "{$this->alias}.handle" => $extractUniqueValues($data, "{s}.{$this->alias}.handle"),
                    "{$this->alias}.{$this->primaryKey} !=" => $extractUniqueValues($data, "{s}.{$this->alias}.{$this->primaryKey}"),
                ],
                'fields' => ['handle', 'handle'],
            ]);
            if ($duplicateHandles) {
                $this->validationErrors = array_map(fn(string $handle): array => ['handle' => 'Product handle is a duplicate'], $duplicateHandles);

                return false;
            }

            return $this->saveManyAssociatedFast($data);
        } finally {
            $this->validator($originalValidator);
        }
    }

    public function findExistingForWebhook(int $userId, string $sourceId, string $sourceType, ?string $handle, array $fields = []): array
    {
        $query = [
            'includeDeleted' => true,
            'recursive' => -1,
            'conditions' => [
                "{$this->alias}.user_id" => $userId,
                "{$this->alias}.source_id" => $sourceId,
                "{$this->alias}.source_type" => $sourceType,
            ],
            'fields' => $fields,
            // In case of duplicate productID values, prefer any not deleted
            'order' => ["{$this->alias}.deleted" => 'ASC'],
        ];
        $record = (array)$this->find('first', $query);

        if (empty($record[$this->alias]) && $handle) {
            // Try matching an existing title being migrated from a different source
            $query['conditions'] = [
                "{$this->alias}.user_id" => $userId,
                "{$this->alias}.handle" => $handle,
            ];
            $record = (array)$this->find('first', $query);
        }

        return $record;
    }

    /**
     * Prepend/append eCom identifiers to the eCom handle if it is not unique in our system.
     *
     * @param string $handle
     * @param int $userId
     * @param int|null $productTitleId
     * @param array $uniqueDecorators
     * @return string
     */
    public function buildUniqueHandleFromWebhook(string $handle, int $userId, ?int $productTitleId, array $uniqueDecorators): string
    {
        $handleConflictCondition = [
            "{$this->alias}.handle" => $handle,
            "{$this->alias}.user_id" => $userId,
            "{$this->alias}.id !=" => $productTitleId
        ];
        if (!$this->exists($handleConflictCondition)) {
            return $handle;
        }

        foreach ($uniqueDecorators as $decorator) {
            $handle = implode('-', array_filter([
                $decorator['prefix'] ?? null,
                $handle,
                $decorator['suffix'] ?? null,
            ]));

            $handleConflictCondition["{$this->alias}.handle"] = $handle;
            if (!$this->exists($handleConflictCondition)) {
                break;
            }
        }

        return $handle;
    }

    public function removeAllOrphans(array $conditions): bool
    {
        if (empty($conditions)) {
            return false;
        }

        $success = $this->deleteAllOrphans($conditions);
        if ($success) {
            $success = $this->softDeleteAll(array_merge($conditions, [
                $this->Product->buildNotExistsSubquery([
                    'Product.product_title_id' => $this->primaryKeyIdentifier(),
                    'Product.deleted' => false,
                ]),
            ]));
        }

        return $success;
    }
}
