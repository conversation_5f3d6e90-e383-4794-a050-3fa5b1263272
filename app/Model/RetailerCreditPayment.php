<?php
App::uses('AppModel', 'Model');
App::uses('RetailerCreditPaymentStatus', 'Utility');
App::uses('RetailerCreditPaymentType', 'Utility');

/**
 * RetailerCreditPayment Model.
 *
 * @property RetailerCredit $RetailerCredit
 * @property RetailerCreditVoucher $RetailerCreditVoucher
 */
class RetailerCreditPayment extends AppModel
{
    /** minimum payment amount for retailers in dollars */
    const MINIMUM_PAYMENT_AMOUNT = 1;

    public $validate = [
        'retailer_credit_id' => [
            'naturalNumber' => ['rule' => 'naturalNumber', 'message' => 'Invalid retailer credit id'],
        ],
        'amount' => [
            'decimal' => ['rule' => 'decimal', 'message' => 'Invalid amount'],
        ],
        'created_at' => [
            'datetime' => ['rule' => 'datetime', 'message' => 'Invalid datetime'],
        ],
        'updated_at' => [
            'datetime' => ['rule' => 'datetime', 'message' => 'Invalid datetime'],
        ],
    ];

    public $belongsTo = [
        'RetailerCredit',
        'CreatedBy' => [
            'className' => 'User',
            'foreignKey' => 'created_by',
        ],
    ];

    public $hasOne = [
        'RetailerCreditVoucher',
    ];

    public $recursive = -1;

    public function buildTotalPaidSubquery()
    {
        return $this->buildSubquery([
            'fields' => [
                "{$this->alias}.retailer_credit_id",
                "SUM(COALESCE(`{$this->alias}`.`amount`, 0)) AS `amount`",
            ],
            'group' => "{$this->alias}.retailer_credit_id",
            'conditions' => ["{$this->alias}.payment_status <>" => RetailerCreditPaymentStatus::FAILED],
        ]);
    }

    public function findIdByStripeChargeId($paymentIntentId)
    {
        return $this->field('id', ["{$this->alias}.stripe_charge_id" => $paymentIntentId]);
    }

    public function updatePaymentStatus($id, $status)
    {
        return $this->save(['id' => $id, 'payment_status' => $status]);
    }

    public function createPayments(int $createdBy, int $brandId, int $retailerId, array $creditPayments): bool
    {
        return (bool)$this->saveMany(array_map(
            fn(array $payment): array => $this->processCreateData($createdBy, $brandId, $retailerId, $payment),
            $creditPayments
        ), ['deep' => true]);
    }

    public function processCreateData(int $createdBy, int $brandId, int $retailerId, array $creditPayment): array
    {
        $creditId = (int)$creditPayment['retailer_credit_id'];
        $invoiceNumber = (string)($creditPayment['invoice_number'] ?? $this->RetailerCredit->fieldByConditions('invoice_number', ['id' => $creditId]));
        $type = (string)($creditPayment['type'] ?? '');
        $paymentStatus = $creditPayment['payment_status'] ?? RetailerCreditPaymentStatus::getStatusForType($type);
        $amount = (float)($creditPayment['amount'] ?? 0.00);

        return array_merge($creditPayment, [
            'created_by' => $createdBy,
            'payment_status' => $paymentStatus,
            'RetailerCreditVoucher' => ($type === RetailerCreditPaymentType::VOUCHER)
                ? [
                    'amount' => -$amount,
                    'user_id' => $brandId,
                    'retailer_id' => $retailerId,
                    'created_by' => $createdBy,
                    'description' => "Payment for {$invoiceNumber}",
                ]
                : null,
        ]);
    }
}
