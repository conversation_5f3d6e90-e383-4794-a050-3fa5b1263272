<?php
App::uses('AppModel', 'Model');

/**
 * RetailerCreditTerm Model.
 *
 * @property ManufacturerRetailer $ManufacturerRetailer
 * @property CreditTerm $CreditTerm
 */
class RetailerCreditTerm extends AppModel
{
    public $validate = [
        'manufacturer_retailer_id' => ['rule' => 'naturalNumber', 'required' => 'create'],
        'credit_term_id' => [
            ['rule' => 'validateCreditTerm', 'message' => 'Invalid Credit Term'],
            ['rule' => 'notBlank', 'required' => 'create'],
        ],
    ];

    public $belongsTo = [
        'CreditTerm' => [
            'foreignKey' => 'credit_term_id',
        ],
        'ManufacturerRetailer' => [
            'foreignKey' => 'manufacturer_retailer_id',
        ],
    ];

    public function findCreditTermOptions($brandId, $retailerId = null)
    {
        $contain = [
            'CreditTerm' => ['fields' => ['id', 'description', 'days_due']],
            'ManufacturerRetailer' => ['fields' => ['id', 'user_id', 'retailer_id']],
        ];

        $conditions = [
            'ManufacturerRetailer.user_id' => $brandId,
        ] + ((bool)$retailerId ? ['ManufacturerRetailer.retailer_id' => $retailerId] : []);

        return $this->find('list', [
            'contain' => $contain,
            'conditions' => $conditions,
            'fields' => ['CreditTerm.id', 'CreditTerm.description'],
            'order' => 'CreditTerm.days_due'
        ]);
    }

    public function saveFromStoreSettings($connectionId, array $creditTermIds)
    {
        $retailerCreditTermIds = $this->find('list', [
            'recursive' => -1,
            'conditions' => [
                'manufacturer_retailer_id' => $connectionId,
                'credit_term_id' => $creditTermIds,
            ],
            'fields' => ['credit_term_id', 'id'],
        ]);

        $saved = array_map(function($creditTermId) use ($retailerCreditTermIds, $connectionId) {
            return [
                'id' => Hash::get($retailerCreditTermIds, $creditTermId),
                'manufacturer_retailer_id' => $connectionId,
                'credit_term_id' => $creditTermId,
            ];
        }, $creditTermIds);

        $success = $this->deleteAll([
            $this->alias . '.manufacturer_retailer_id' => $connectionId,
            $this->alias . '.id !=' => array_filter(array_column($saved, 'id')),
        ], false);
        if (empty($saved)) {
            return $success;
        }

        return $this->saveMany($saved) && $success;
    }

    protected function findCreditTermsByManufacturerRetailerId($manufacturerRetailerId)
    {
        $userId = $this->ManufacturerRetailer->field('user_id',["{$this->ManufacturerRetailer->alias}.id" => $manufacturerRetailerId]);
        return $this->CreditTerm->getAllCreditTermOptions($userId);

    }

    public function validateCreditTerm($check)
    {
        $manufacturerRetailerId = $this->data[$this->alias]['manufacturer_retailer_id'] ?? null;
        // invalid if not provided
        if (empty($check['credit_term_id'])) {
            return false;
        }
        // if these are not provided we cannot validate. If they are missing on create something else when wrong
        if (empty($manufacturerRetailerId)){
            return false;
        }
        $validCreditTerms = $this->findCreditTermsByManufacturerRetailerId($manufacturerRetailerId);

        return array_key_exists($check['credit_term_id'], $validCreditTerms);
    }
}
