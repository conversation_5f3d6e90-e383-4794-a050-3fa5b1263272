<?php
App::uses('AppModel', 'Model');

/**
 * ShipFromStoreZipCode Model.
 *
 * @property ManufacturerRetailer $ManufacturerRetailer
 */
class ShipFromStoreZipCode extends AppModel
{
    public $useTable = 'shipfromstore_zip_codes';

    public $validate = [
        'manufacturer_retailer_id' => [
            'numeric' => ['rule' => 'numeric', 'message' => 'Invalid manufacturer retailer id'],
        ],
        'zip_code' => [
            'notBlank' => ['rule' => 'notBlank', 'message' => 'This field cannot be left blank'],
        ],
        'created_at' => [
            'datetime' => ['rule' => 'datetime', 'message' => 'Invalid datetime'],
        ],
    ];

    public $belongsTo = [
        'ManufacturerRetailer',
    ];

    public $recursive = -1;
}
