<?php
App::uses('AppModel', 'Model');
App::uses('TranslateBehaviorTrait', 'Model/Behavior/Trait');

/**
 * StorefrontSlide Model.
 *
 * @property User $User
 * @property Storefront $Storefront
 */
class StorefrontSlide extends AppModel
{
    use TranslateBehaviorTrait;

    public $belongsTo = [
        'User',
        'Storefront',
    ];

    public $recursive = -1;

    public $actsAs = [
        'Translate' => [
            'className' => 'AppTranslate',
            'joinType' => 'LEFT',
            'slide_name',
            'heading',
            'button_label',
        ],
    ];
}
