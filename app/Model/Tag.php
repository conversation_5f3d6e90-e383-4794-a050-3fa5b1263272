<?php
App::uses('AppModel', 'Model');

/**
 * Tag Model.
 *
 * @property ManufacturerRetailer $ManufacturerRetailer
 * @property ManufacturerRetailerTag $ManufacturerRetailerTag
 * @property Product $Product
 * @property ProductTag $ProductTag
 */
class Tag extends AppModel
{
    public $displayField = 'name';

    public $validate = [
        'user_id' => ['rule' => 'naturalNumber', 'message' => 'Invalid id'],
        'name' => ['rule' => 'notBlank', 'required' => 'create'],
        'created_at' => ['rule' => 'datetime', 'message' => 'Invalid datetime'],
        'updated_at' => ['rule' => 'datetime', 'message' => 'Invalid datetime'],
    ];

    public $recursive = -1;

    public $order = ['name' => 'ASC'];

    public $virtualFields = [];

    /**
     * @param string[] $tagNames List of tag names
     * @return array<string, string> Map of tag names by lower case name
     */
    public static function toDistinctNameByLowerCase(array $tagNames): array
    {
        $tagNames = array_filter(array_map('trim', $tagNames));
        $tagSet = (array)(array_combine($tagNames, $tagNames) ?: []);

        return array_change_key_case($tagSet, CASE_LOWER);
    }

    public function initialize(array $config)
    {
        parent::initialize($config);

        $this->addAssociations([
            'hasMany' => [
                'ManufacturerRetailerTag' => [
                    // ManufacturerRetailerTag actually represents assignments of ProductTag.Tag,
                    // so it can be removed if the ProductTag has already been removed.
                    'dependent' => true,
                ],
                'ProductTag',
            ],
            'belongsToMany' => [
                'ManufacturerRetailer' => ['with' => 'ManufacturerRetailerTag', 'unique' => 'keepExisting'],
                'Product' => ['with' => 'ProductTag', 'unique' => 'keepExisting'],
            ],
        ]);

        $this->virtualFields['lower_case'] = "LOWER({$this->alias}.{$this->displayField})";
    }

    /**
     * @param int|string $userId
     * @return array
     */
    public function listSelectOptions($userId): array
    {
        return (array)$this->find('list', [
            'conditions' => ["{$this->alias}.user_id" => $userId],
            'fields' => ["{$this->alias}.id", "{$this->alias}.name"],
        ]);
    }

    /**
     * @param int|string $userId
     * @param array|null $tagNames
     * @return array
     */
    public function listIdsByName($userId, ?array $tagNames = null): array
    {
        $conditions = ["{$this->alias}.user_id" => $userId];
        if ($tagNames !== null) {
            // Index is case insensitive
            $conditions["{$this->alias}.name"] = $tagNames;
        }

        return (array)$this->find('list', [
            'conditions' => $conditions,
            'fields' => ["{$this->alias}.name", "{$this->alias}.id"],
        ]);
    }

    public function listIdsByNameWithNewTags($userId, ?array $tagNames = null): array
    {
        if (!$this->addTagsToSet($userId, $tagNames ?: [])) {
            CakeLog::warning(json_encode(['message' => 'Failed to add new tags', 'errors' => $this->validationErrors, 'data' => $this->data]));
        }
        return $this->listIdsByName($userId, $tagNames);
    }

    /**
     * @param int|string $userId
     * @param array|null $tagNames
     * @return array
     */
    public function listIdsByLowerCaseName($userId, ?array $tagNames = null): array
    {
        $conditions = ["{$this->alias}.user_id" => $userId];
        if ($tagNames !== null) {
            // Index is case insensitive
            $conditions["{$this->alias}.name"] = $tagNames;
        }

        return (array)$this->find('list', [
            'conditions' => $conditions,
            'fields' => ["{$this->alias}.lower_case", "{$this->alias}.id"],
        ]);
    }

    public function filterAssignedStoreConnections(array $storeConnections, array $productIds): array
    {
        if (!$storeConnections) {
            return [];
        }

        $fields = [];
        foreach ($storeConnections as $storeKey => $connection) {
            $fields[] = "{$this->buildStoreAssignedProductTagMatchCondition($connection, $productIds)} AS `{$storeKey}`";
        }
        $results = $this->query('SELECT ' . implode(', ', $fields));

        $hasProductTagMatchByStoreKey = (array)$results[0][0];

        return array_intersect_key($storeConnections, array_filter($hasProductTagMatchByStoreKey));
    }

    /**
     * Build an SQL condition to check for tag matches against a complete set of product ids.
     *
     * @param int|stdClass $manufacturerRetailerId
     * @param bool|stdClass $enableStrictTagMatching
     * @param array $productIds
     * @return string
     */
    public function buildStoreAssignedProductTagMatchConditionForProductIds($manufacturerRetailerId, $enableStrictTagMatching, array $productIds): string
    {
        if (is_object($enableStrictTagMatching) && ($enableStrictTagMatching->type ?? null) === 'identifier') {
            $enableStrictTagMatching = [$enableStrictTagMatching->value => true];
        }

        $storeHasNoAssignedTags = $this->ManufacturerRetailerTag->buildNotExistsSubquery([
            'ManufacturerRetailerTag.manufacturer_retailer_id' => $manufacturerRetailerId,
        ]);

        // Strict tag matching requires each product in the set to have a matching tag
        // eg. Product ids 1 and 2 produce something like
        // WHERE EXISTS(... WHERE product_id = 1 AND tag_id IN (...))
        //   AND EXISTS(... WHERE product_id = 2 AND tag_id IN (...))
        $strictTagMatchCondition = $this->conditions(array_map(fn($id) => $this->ProductTag->buildStoreAssignedTagMatchCondition($manufacturerRetailerId, $id), array_values($productIds)));

        // Otherwise, it is any product in the set that needs to have a matching tag
        // eg. Product ids 1 and 2 produce something like
        // WHERE EXISTS(... WHERE product_id IN (1, 2) AND tag_id IN (...))
        $looseTagMatchCondition = $this->ProductTag->buildStoreAssignedTagMatchCondition($manufacturerRetailerId, $productIds);

        $storeAssignedTagsMatchProductTags = "IF({$this->conditions($enableStrictTagMatching)}, {$strictTagMatchCondition}, {$looseTagMatchCondition})";

        return $this->conditions(['OR' => [$storeHasNoAssignedTags, $storeAssignedTagsMatchProductTags]]);
    }

    /**
     * @param array $storeConnection
     * @param int|int[]|stdClass $productIds
     * @return string
     */
    public function buildStoreAssignedProductTagMatchCondition(array $storeConnection, $productIds): string
    {
        $manufacturerRetailerId = (int)$storeConnection['id'];
        $enableStrictTagMatching = (bool)$storeConnection['enable_strict_tag_matching'];

        // Strict tag matching requires the full set of product ids. (ie. $productIds cannot represent a single id).
        if (is_scalar($productIds) || (is_object($productIds) && ($productIds->type ?? null) === 'identifier')) {
            $enableStrictTagMatching = false;
        }

        $buildProductTagMatchCondition = function($productIds) use ($manufacturerRetailerId) {
            return $this->ProductTag->buildStoreAssignedTagMatchCondition($manufacturerRetailerId, $productIds);
        };

        $storeHasNoAssignedTags = $this->ManufacturerRetailerTag->buildNotExistsSubquery([
            "{$this->ManufacturerRetailerTag->alias}.manufacturer_retailer_id" => $manufacturerRetailerId,
        ]);
        $storeAssignedTagsMatchProductTags = ($enableStrictTagMatching && is_array($productIds))
            // Strict tag matching requires each product in the set to have a matching tag
            // eg. Product ids 1 and 2 produce something like
            // WHERE EXISTS(... WHERE product_id = 1 AND tag_id IN (...))
            //   AND EXISTS(... WHERE product_id = 2 AND tag_id IN (...))
            ? array_map($buildProductTagMatchCondition, array_values($productIds))
            // Otherwise, it is any product in the set that needs to have a matching tag
            // eg. Product ids 1 and 2 produce something like
            // WHERE EXISTS(... WHERE product_id IN (1, 2) AND tag_id IN (...))
            // eg. Product id expressions also produce non-strict matching conditions
            // WHERE EXISTS(... WHERE product_id = Product.id AND tag_id IN (...))
            // WHERE EXISTS(... WHERE product_id IN (SELECT product_id FROM ...) AND tag_id IN (...))
            : call_user_func($buildProductTagMatchCondition, $productIds);

        return $this->conditions(['OR' => [$storeHasNoAssignedTags, $storeAssignedTagsMatchProductTags]]);
    }

    /**
     * @param int|string $userId
     * @param string[] $tagNames
     * @return bool Success
     */
    public function addTagsToSet($userId, array $tagNames): bool
    {
        $addedNames = static::toDistinctNameByLowerCase($tagNames);

        $existingNames = $this->listIdsByLowerCaseName($userId, $addedNames);

        $newNames = array_diff_key($addedNames, $existingNames);
        $newRecords = array_map(function($name) use ($userId) {
            return [
                'user_id' => $userId,
                'name' => $name,
            ];
        }, array_values($newNames));

        return (!$newRecords || $this->saveMany($newRecords));
    }

    /**
     * Delete all records that do not have any association table records.
     *
     * @param string|int $userId
     * @return bool
     */
    public function deleteAllOrphansWithUserId($userId): bool
    {
        return $this->deleteAllOrphans(["{$this->alias}.user_id" => $userId]);
    }

    public function getTagsByUser($userId)
    {
        $conditions = ["{$this->alias}.user_id" => $userId];
        $tags = (array)$this->find('list', [
            'conditions' => $conditions,
            'fields' => ["{$this->alias}.name"],
        ]);

        return array_combine($tags,$tags);
    }
}
