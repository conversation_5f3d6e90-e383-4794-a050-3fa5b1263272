<?php
App::uses('AppModel', 'Model');

/**
 * Class UserAddress
 *
 * @property User $User
 */
class UserAddress extends AppModel
{
    public $belongsTo = array(
        'User' => array(
            'className' => 'User',
            'foreignKey' => 'user_id',
        ),
    );

    public $recursive = -1;

    public function getAddress($user_id)
    {
        return $this->find('first', array('conditions' => array('user_id' => $user_id)));
    }

    /**
     * Retrieve the user's shipping address.
     *
     * Fall back to the user's account address if not found.
     *
     * @param int $userId
     * @return array|null
     * @see Model::find
     */
    public function findShippingOrigin($userId)
    {
        $address = $this->find('first', array(
            'conditions' => array('user_id' => $userId),
            'fields' => array(
                'id', 'user_id', 'address1', 'address2', 'city', 'state_id', 'country_id', 'zipcode', 'latitude', 'longitude'
            )
        ));
        if (empty($address[$this->alias]['id'])) {
            $user = $this->User->find('first', array(
                'recursive' => -1,
                'conditions' => array('id' => $userId),
                'fields' => array(
                    'id', 'address', 'city', 'state_id', 'country_id', 'zipcode', 'latitude', 'longitude'
                )
            ));
            if (empty($user['User']['id'])) {
                return null;
            }
            unset($user['User']['id']);

            $derived = array('user_id' => $userId);

            list($derived['address1'], $derived['address2']) = $this->User->splitAddressField($user['User']['address']);
            unset($user['User']['address']);

            $address[$this->alias] = $derived + $user['User'];
        }
        return $address;
    }

}
