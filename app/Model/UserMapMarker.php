<?php
App::uses('AppModel', 'Model');

/**
 * UserMapMarker Model.
 *
 * @property User $User
 */
class UserMapMarker extends AppModel
{
    public $validate = [
        'user_id' => [
            'numeric' => ['rule' => 'numeric', 'message' => 'Invalid user id'],
        ],
        'uri' => [
            'notBlank' => ['rule' => 'notBlank', 'message' => 'This field cannot be left blank'],
        ],
        'type' => [
            'notBlank' => ['rule' => 'notBlank', 'message' => 'This field cannot be left blank'],
        ],
        'created_at' => [
            'datetime' => ['rule' => 'datetime', 'message' => 'Invalid datetime'],
        ],
        'updated_at' => [
            'datetime' => ['rule' => 'datetime', 'message' => 'Invalid datetime'],
        ],
    ];

    public $belongsTo = [
        'User',
    ];

    public $recursive = -1;

    public function findAllForDisplay(int $userId)
    {
        return Hash::combine($this->find('all', [
            'conditions' => [
                'user_id' => $userId,
            ],
            'fields' => [
                'type', 'uri',
            ],
        ]), "{n}.{$this->alias}.type", "{n}.{$this->alias}");
    }

    public function saveForUser($userId, $data)
    {
        $existing = $this->find('all', [
            'conditions' => [
                'user_id' => $userId,
            ],
            'fields' => [
                'id', 'type', 'uri',
            ],
        ]);

        $existing = Hash::combine($existing, "{n}.{$this->alias}.type", "{n}.{$this->alias}");

        $delete = array_values(array_filter(array_map(function($record) use ($data) {
            if (empty($data[$record['type']])) {
                return $record['id'];
            }
        }, $existing)));

        $save = array_filter(array_map(function($mapMarker) use ($existing) {
            if (!empty($existing[$mapMarker['type']])) {
                $mapMarker['id'] = $existing[$mapMarker['type']]['id'];
            }

            return $mapMarker;
        }, $data));

        $success = true;
        if(!empty($save)){
            $success &= $this->saveMany($save);
        }
        if (!empty($delete)) {
            $success &= $this->deleteAll(["{$this->alias}.id" => $delete]);
        }

        return $success;
    }
}
