<?php

use ShipEarlyApp\Lib\Utility\SupportedLanguages;

App::uses('AppModel', 'Model');
App::uses('User', 'Model');
App::uses('TranslateBehaviorTrait', 'Model/Behavior/Trait');

/**
 * UserSetting Model.
 *
 * @property Page $Page
 * @property User $User
 *
 * @method array|null findByUserId($userId, $fields = null, $order = null, $recursive = null) Magic find method parsed by DboSource::query.
 */
class UserSetting extends AppModel
{
    use TranslateBehaviorTrait;

    public $belongsTo = [
        'User',
    ];

    public $actsAs = [
        'Translate' => [
            'className' => 'AppTranslate',
            'joinType' => 'LEFT',
            'terms_of_service',
            'return_policy',
            'credit_terms_conditions',
        ],
    ];

    public $recursive = -1;

    public function initialize(array $config)
    {
        parent::initialize($config);

        // Attach model instance as property without adding a model association to queries
        $this->_constructLinkedModel('Page');
    }

    /**
     * @param int $userId
     * @param string $field
     * @return array
     */
    public function findForAdminEditPolicy(int $userId, string $field): array
    {
        return (array)$this->find('first', [
            'contain' => [
                'User' => ['id', 'email_address'],
            ],
            'conditions' => [
                "{$this->alias}.user_id" => $userId,
                'User.user_type' => User::TYPE_MANUFACTURER,
            ],
            'fields' => ['id', 'user_id', $field],
        ]);
    }

    /**
     * @param int $userId
     * @param string|null $userEmail
     * @return string[]
     */
    public function listPolicies(int $userId, ?string $userEmail = null): array
    {
        $fields = ['id', 'return_policy', 'privacy_policy', 'terms_of_service'];

        $policies = (array)($this->findByUserId($userId, $fields)[$this->alias] ?? []);

        if (empty($policies['id'])) {
            return [];
        }

        $defaultLocale = SupportedLanguages::DEFAULT_LOCALE;

        if ($this->locale === $defaultLocale) {
            // Use model fields where default translation fields are empty
            $this->locale = '';
            $_policies = (array)$this->findByUserId($userId, $fields)[$this->alias];
            $this->locale = $defaultLocale;

            $policies = array_merge($_policies, array_filter($policies));
        }

        unset(
            $policies['id'],
            $policies['locale']
        );

        $policies['return_policy'] = $policies['return_policy'] ?: $this->Page->returnPolicy($userEmail ?: $this->User->getEmail($userId));

        $policies = array_map('nl2br', $policies);
        $policies = array_map('addslashes', $policies);

        return $policies;
    }

    /**
     * Saving user settings data's
     * @param $data array
     * @return mixed
     */
    public function saveUserSetting($data)
    {
        if(isset($data['UserSetting'])) {
            $data = $data['UserSetting'];
            $data['created_at'] = date('Y-m-d H:i:s');
            $data['updated_at'] = date('Y-m-d H:i:s');
            return $this->save($data);
        }
    }

    /**
     * updating user settings informations
     * @param $data array
     * @return mixed
     */
    public function updateUserSetting($data)
    {
        if(isset($data['UserSetting'])) {
            $data = $data['UserSetting'];
            $data['updated_at'] = date('Y-m-d H:i:s');
            return $this->save($data);
        }
    }

    public function listSelectedCountries($userId): array
    {
        return (array)json_decode($this->field('country_list', ['user_id' => $userId]), true);
    }

    /**
     * @param int|null $userId
     * @param string|null $userEmail
     * @return string
     */
    public function getReturnPolicy(?int $userId, ?string $userEmail = null): string
    {
        return (string)(
            $this->field('return_policy', ["{$this->alias}.user_id" => $userId]) ?:
            $this->Page->returnPolicy($userEmail ?: $this->User->getEmail($userId))
        );
    }

    public function getTermsAndConditions(int $userId): ?string
    {
        return (string)$this->fieldByConditions('credit_terms_conditions', ["{$this->alias}.user_id" => $userId]) ?: null;
    }
}
