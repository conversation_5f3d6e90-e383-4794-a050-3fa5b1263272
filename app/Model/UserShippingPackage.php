<?php
App::uses('AppModel', 'Model');

/**
 * UserShippingPackage Model.
 *
 * @property User $User
 */
class UserShippingPackage extends AppModel
{
    public $validate = [
        'user_id' => [
            'numeric' => ['rule' => 'numeric', 'message' => 'Invalid user id'],
        ],
        'name' => [
            'notBlank' => ['rule' => 'notBlank', 'message' => 'This field cannot be left blank'],
        ],
        'type' => [
            'notBlank' => ['rule' => 'notBlank', 'message' => 'This field cannot be left blank'],
        ],
        'length' => [
            'decimal' => ['rule' => 'decimal', 'message' => 'Invalid length'],
        ],
        'width' => [
            'decimal' => ['rule' => 'decimal', 'message' => 'Invalid width'],
        ],
        'height' => [
            'decimal' => ['rule' => 'decimal', 'message' => 'Invalid height'],
        ],
        'dimension_unit' => [
            'notBlank' => ['rule' => 'notBlank', 'message' => 'This field cannot be left blank'],
        ],
        'weight' => [
            'decimal' => ['rule' => 'decimal', 'message' => 'Invalid weight'],
        ],
        'weight_unit' => [
            'notBlank' => ['rule' => 'notBlank', 'message' => 'This field cannot be left blank'],
        ],
        'is_default' => [
            'boolean' => ['rule' => 'boolean', 'message' => 'Invalid boolean'],
        ],
    ];

    public $belongsTo = [
        'User',
    ];

    public $recursive = -1;

    public function getAllUserPackages($user_id): array
    {
        return (array)$this->find('all', [
            'recursive' => -1,
            'conditions' => ["{$this->alias}.user_id" => $user_id],
        ]);
    }

    public function resetUserPackageDefaultStatus($user_id, $id): bool
    {
        return (bool)$this->updateAllJoinless(["{$this->alias}.is_default" => $this->value(false)], [
            "{$this->alias}.user_id" => $user_id,
            "{$this->alias}.id !=" => $id,
        ]);
    }

    public function findDefaultPackage($userId): array
    {
        $packageDefaults = [
            'length' => SHIPPING_PACKAGE_LENGTH,
            'width' => SHIPPING_PACKAGE_WIDTH,
            'height' => SHIPPING_PACKAGE_HEIGHT,
            'dimension_unit' => SHIPPING_PACKAGE_DIMENSION_UNIT,
            'weight' => SHIPPING_PACKAGE_WEIGHT_EMPTY,
            'weight_unit' => SHIPPING_PACKAGE_WEIGHT_UNIT,
        ];

        $package = (array)$this->find('first', [
            'conditions' => [
                "{$this->alias}.user_id" => $userId,
                "{$this->alias}.is_default" => true
            ],
            'fields' => array_keys($packageDefaults),
        ]);
        if (empty($package[$this->alias])) {
            $package[$this->alias] = $packageDefaults;
        }

        return $package;
    }
}
