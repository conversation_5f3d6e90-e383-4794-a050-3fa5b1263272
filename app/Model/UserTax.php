<?php
App::uses('AppModel', 'Model');

/**
 * UserTax Model.
 *
 * @property User $User
 * @property State $State
 *
 * @method array|null findByUserIdAndStateId($userId, $stateId, $fields = array(), $order = null, $recursive = null) Magic find method parsed by DboSource::query.
 * @method array|null findAllByUserId($userId, $fields = array(), $order = null, $limit = null, $page = null, $recursive = null) Magic find method parsed by DboSource::query.
 */
class UserTax extends AppModel
{
    const API_RATES = 'rates';
    const API_TAXES = 'taxes';

    public $displayField = 'tax_rate';

    public $validate = [
        'user_id' => ['rule' => 'naturalNumber', 'message' => 'Invalid id'],
        'state_id' => ['rule' => 'naturalNumber', 'message' => 'Invalid id'],
        'name' => ['rule' => 'notBlank', 'allowEmpty' => true],
        'tax_id_number' => ['rule' => 'notBlank', 'allowEmpty' => true],
        'uses_manual_rate' => ['rule' => 'boolean', 'message' => 'Invalid boolean'],
        'tax_rate' => [
            ['rule' => 'decimal', 'message' => 'Invalid tax rate'],
            ['rule' => ['comparison', '>=', 0], 'message' => 'Tax cannot be negative'],
        ],
        'tax_percentage' => [
            ['rule' => 'decimal', 'allowEmpty' => true, 'message' => 'Invalid tax percentage'],
            ['rule' => ['comparison', '>=', 0], 'message' => 'Tax cannot be negative'],
        ],
        'includes_shipping' => ['rule' => 'boolean', 'message' => 'Invalid boolean'],
        'created_at' => ['rule' => 'datetime', 'message' => 'Invalid datetime'],
        'updated_at' => ['rule' => 'datetime', 'message' => 'Invalid datetime'],
    ];

    public $belongsTo = ['User', 'State'];

    public $recursive = -1;

    public function __construct($id = false, $table = null, $ds = null)
    {
        parent::__construct($id, $table, $ds);

        $this->virtualFields['tax_percentage'] = "ROUND({$this->alias}.tax_rate * 100, 3)";
    }

    public function beforeSave($options = array())
    {
        if (
            isset($this->data[$this->alias]['tax_percentage']) &&
            !isset($this->data[$this->alias]['tax_rate'])
        ) {
            $this->data[$this->alias]['tax_rate'] = format_number(floatval($this->data[$this->alias]['tax_percentage']) / 100, 5);
        }
        return parent::beforeSave($options);
    }

    public function listFieldsByStateIds(int $userId, int $countryId, array $fields): array
    {
        if (!empty($fields)) {
            $fields[] = "{$this->alias}.state_id";
        }

        return Hash::combine(
            (array)$this->find('all', [
                'contain' => ['State'],
                'conditions' => ["{$this->alias}.user_id" => $userId, 'State.country_id' => $countryId],
                'fields' => $fields,
            ]),
            "{n}.{$this->alias}.state_id",
            "{n}.{$this->alias}"
        );
    }

    public function listRegisteredStates(int $userId, int $countryId): array
    {
        $originalVirtualFields = $this->State->virtualFields;

        $this->State->virtualFields['state_id'] = "{$this->State->alias}.id";
        $this->State->virtualFields['country_code'] = "UPPER({$this->State->alias}.country_code)";
        $this->State->virtualFields['is_local'] = $this->State->conditions(['User.id !=' => null]);
        $records = (array)$this->State->find('all', [
            'contain' => [
                'CountryTax' => ['fields' => ['country_id', 'name', 'rate', 'includes_shipping']],
                'StateTax' => ['fields' => ['state_id', 'name', 'rate', 'includes_shipping', 'uses_origin_based_rates', 'uses_api']],
            ],
            'joins' => [
                [
                    'table' => 'users',
                    'alias' => 'User',
                    'type' => 'LEFT',
                    'conditions' => [
                        'User.state_id' => $this->State->primaryKeyIdentifier(),
                        'User.id' => $userId,
                    ],
                ],
                [
                    'table' => 'user_country_taxes',
                    'alias' => 'UserCountryTax',
                    'type' => 'LEFT',
                    'conditions' => [
                        'UserCountryTax.country_id' => $this->State->identifier('State.country_id'),
                        'UserCountryTax.user_id' => $userId,
                    ],
                ],
                [
                    'table' => 'user_taxes',
                    'alias' => 'UserTax',
                    'type' => 'LEFT',
                    'conditions' => [
                        'UserTax.state_id' => $this->State->primaryKeyIdentifier(),
                        'UserTax.user_id' => $userId,
                    ],
                ],
            ],
            'conditions' => [
                'State.country_id' => $countryId,
                'OR' => [
                    'State.country_code !=' => 'US',
                    'State.is_local' => true,
                    'UserTax.id !=' => null,
                ],
            ],
            'fields' => [
                'State.state_id',
                'State.state_name',
                'State.country_code',
                'State.is_local',
                'UserCountryTax.id',
                'UserCountryTax.name',
                'UserCountryTax.tax_rate',
                'UserCountryTax.includes_shipping',
                'UserTax.id',
                'UserTax.state_id',
                'UserTax.name',
                'UserTax.tax_id_number',
                'UserTax.uses_manual_rate',
                'UserTax.tax_rate',
                'UserTax.includes_shipping',
            ],
            'order' => [
                $this->State->conditions(['State.country_code' => 'US', 'State.is_local' => true]) => 'DESC',
                'State.state_name' => 'ASC',
            ],
        ]);

        $this->State->virtualFields = $originalVirtualFields;

        $userStateTaxes = array_map(fn(array $record): array => (
            $record['State'] + array_merge($record['UserTax'], $this->resolveUserStateTax($record))
        ), $records);

        return array_combine(array_column($userStateTaxes, 'state_id'), $userStateTaxes);
    }

    public function findTaxRegistration(int $userId, int $stateId): array
    {
        return (array)$this->findByUserIdAndStateId($userId, $stateId, [
            "{$this->alias}.id",
            "{$this->alias}.state_id",
            "{$this->alias}.name",
            "{$this->alias}.tax_id_number",
            "{$this->alias}.uses_manual_rate",
            "{$this->alias}.tax_percentage",
            "{$this->alias}.includes_shipping",
        ]);
    }

    public function listPlaceholdersByStateId(int $countryId): array
    {
        return $this->_listPlaceholdersByStateId(['State.country_id' => $countryId]);
    }

    public function findPlaceholder(int $stateId): array
    {
        return (array)($this->_listPlaceholdersByStateId(['State.id' => $stateId])[$stateId] ?? []);
    }

    private function _listPlaceholdersByStateId(array $conditions): array
    {
        $originalVirtualFields = $this->State->virtualFields;

        $this->State->virtualFields['state_id'] = "{$this->State->alias}.id";
        $records = (array)$this->State->find('all', [
            'contain' => [
                'CountryTax' => ['fields' => ['country_id', 'name', 'rate', 'includes_shipping']],
                'StateTax' => ['fields' => ['state_id', 'name', 'rate', 'includes_shipping', 'uses_origin_based_rates']],
            ],
            'conditions' => $conditions,
            'fields' => ['state_id', 'state_name'],
        ]);

        $this->State->virtualFields = $originalVirtualFields;

        $placeholders = array_map(fn(array $record): array => array_merge($record['State'], $this->resolveAdminStateTax($record)), $records);

        return array_combine(array_column($placeholders, 'state_id'), $placeholders);
    }

    public function findRatesForEcommerce(int $userId, int $stateId): array
    {
        $masterId = (int)$this->User->getMainRetailerId($userId);

        $originalVirtualFields = $this->State->virtualFields;

        $this->State->virtualFields['state_id'] = "{$this->State->alias}.id";
        $this->State->virtualFields['state_code'] = "UPPER({$this->State->alias}.state_code)";
        $this->State->virtualFields['country_code'] = "UPPER({$this->State->alias}.country_code)";
        $this->State->virtualFields['is_local'] = $this->State->conditions(['User.id !=' => null]);

        $record = (array)$this->State->record($stateId, [
            'contain' => [
                'CountryTax' => ['fields' => ['country_id', 'name', 'rate', 'includes_shipping', 'included_in_prices']],
                'StateTax' => ['fields' => ['state_id', 'name', 'rate', 'includes_shipping', 'uses_origin_based_rates', 'uses_api']],
            ],
            'joins' => [
                [
                    'table' => 'user_country_taxes',
                    'alias' => 'UserCountryTax',
                    'type' => 'LEFT',
                    'conditions' => [
                        'UserCountryTax.country_id' => $this->State->identifier('State.country_id'),
                        'UserCountryTax.user_id' => $masterId,
                    ],
                ],
                [
                    'table' => 'user_taxes',
                    'alias' => 'UserTax',
                    'type' => 'LEFT',
                    'conditions' => [
                        'UserTax.state_id' => $this->State->primaryKeyIdentifier(),
                        'UserTax.user_id' => $masterId,
                    ],
                ],
                [
                    'table' => 'users',
                    'alias' => 'User',
                    'type' => 'LEFT',
                    'conditions' => [
                        'User.state_id' => $this->State->primaryKeyIdentifier(),
                        'User.id' => $userId,
                    ],
                ],
            ],
            'fields' => [
                'State.state_id',
                'State.state_name',
                'State.state_code',
                'State.country_code',
                'State.is_local',
                'UserCountryTax.id',
                'UserCountryTax.name',
                'UserCountryTax.tax_rate',
                'UserCountryTax.includes_shipping',
                'UserCountryTax.included_in_prices',
                'UserTax.id',
                'UserTax.name',
                'UserTax.uses_manual_rate',
                'UserTax.tax_rate',
                'UserTax.includes_shipping',
                'User.id',
                'User.defaultTax',
                'User.defaultTaxName',
            ],
        ]);

        $this->State->virtualFields = $originalVirtualFields;

        $isLocal = (bool)$record['State']['is_local'];
        $isLocalCaliforniaTax = ($isLocal && $record['State']['country_code'] === 'US' && $record['State']['state_code'] === 'CA');

        $userStateTax = $this->resolveUserStateTax($record);

        $tax_name = (string)$userStateTax['name'];
        $tax_rate = (string)$userStateTax['tax_rate'];
        $includes_shipping = (bool)$userStateTax['includes_shipping'];
        $uses_api = $userStateTax['uses_api'] ? static::API_RATES : null;

        if ($isLocal && $userStateTax['uses_origin_based_rates']) {
            $tax_name = (string)($record['User']['defaultTaxName'] ?: $tax_name);
            $tax_rate = isset($record['User']['defaultTax']) ? format_number($record['User']['defaultTax'] / 100, 5) : $tax_rate;
            $uses_api = ($uses_api && $isLocalCaliforniaTax) ? static::API_TAXES : null;
        }

        $taxIncluded = (bool)($record['CountryTax']['included_in_prices'] ?? false);
        if ($userStateTax['uses_manual_rate']) {
            $taxIncluded = (bool)($record['UserCountryTax']['included_in_prices'] ?? $taxIncluded);
        }

        return [
            'tax_name' => $tax_name,
            'tax_rate' => $tax_rate,
            'shipping_tax_name' => $tax_name,
            'shipping_tax_rate' => ($includes_shipping) ? $tax_rate : '0.00000',
            'tax_included' => $taxIncluded,
            'uses_api' => $uses_api,
        ];
    }

    private function resolveUserStateTax(array $record): array
    {
        $countryCode = (string)$record['State']['country_code'];
        // Automatic rates are only available to US and CA
        $usesManualRate = (!in_array($countryCode, ['US', 'CA'], true) || $record['UserTax']['uses_manual_rate']);

        $adminTax = $this->resolveAdminStateTax($record);
        $adminTaxName = (string)$adminTax['name'];
        $adminTaxRate = (string)$adminTax['tax_rate'];
        $adminIncludesShipping = (bool)$adminTax['includes_shipping'];
        $usesOriginBasedRates = (bool)$adminTax['uses_origin_based_rates'];

        if ($usesManualRate) {
            $taxName = (string)($record['UserTax']['name'] ?: $record['UserCountryTax']['name'] ?: $adminTaxName);
            $taxRate = (string)($record['UserTax']['tax_rate'] ?? $record['UserCountryTax']['tax_rate'] ?? $adminTaxRate);
            $includesShipping = (bool)($record['UserTax']['includes_shipping'] ?? $record['UserCountryTax']['includes_shipping'] ?? $adminIncludesShipping);
            $usesApi = false;
        } else {
            $taxName = $adminTaxName;
            $taxRate = $adminTaxRate;
            $includesShipping = $adminIncludesShipping;
            $usesApi = (bool)$record['StateTax']['uses_api'];

            if ($countryCode === 'US') {
                $isTaxNexus = ($record['UserTax']['id'] || $record['State']['is_local']);
                if (!$isTaxNexus) {
                    $taxName = (string)($record['CountryTax']['name'] ?: 'Tax');
                    $taxRate = (string)($record['CountryTax']['rate'] ?? '0.00000');
                    $includesShipping = (bool)($record['CountryTax']['includes_shipping'] ?? true);
                    $usesApi = false;
                }
            }
        }

        return [
            'name' => $taxName,
            'uses_manual_rate' => $usesManualRate,
            'tax_rate' => $taxRate,
            'tax_percentage' => format_number($taxRate * 100, 3),
            'includes_shipping' => $includesShipping,
            'uses_origin_based_rates' => $usesOriginBasedRates,
            'uses_api' => $usesApi,
        ];
    }

    private function resolveAdminStateTax(array $record): array
    {
        $tax_rate = $record['StateTax']['rate'] ?? $record['CountryTax']['rate'] ?? '0.00000';

        return [
            'name' => $record['StateTax']['name'] ?: $record['CountryTax']['name'] ?: 'Tax',
            'tax_rate' => $tax_rate,
            'tax_percentage' => format_number($tax_rate * 100, 3),
            'includes_shipping' => $record['StateTax']['includes_shipping'] ?? $record['CountryTax']['includes_shipping'] ?? true,
            'uses_origin_based_rates' => (bool)$record['StateTax']['uses_origin_based_rates'],
        ];
    }

    public function saveTaxRegistration(int $userId, int $stateId, array $data): bool
    {
        $keys = [
            'id' => $this->field('id', ['user_id' => $userId, 'state_id' => $stateId]),
            'user_id' => $userId,
            'state_id' => $stateId,
        ];

        // Ensure compatibility with the model for countries that do not register states
        $placeholder = $this->findPlaceholder($stateId);
        $placeholderFields = [
            'name' => '',
            'tax_percentage' => $placeholder['tax_percentage'],
            'includes_shipping' => $placeholder['includes_shipping'],
        ];

        $data[$this->alias] = $keys + $data[$this->alias] + $placeholderFields;

        $locations = (array)($data['User'] ?? []);
        unset($data['User']);
        if ($locations) {
            $validLocations = $this->User->findTaxLocationsByStateId($userId, [$stateId])[$stateId] ?? [];
            $invalidLocations = array_diff_key($locations, $validLocations);
            if ($invalidLocations) {
                triggerWarning(json_encode(['message' => 'Ignoring locations that do not belong to the state', 'locations' => $invalidLocations, 'state_id' => $stateId]));
            }
            $locations = array_intersect_key($locations, $validLocations);

            $propagatedFields = [
                'tax_id_number' => (string)$data[$this->alias]['tax_id_number'] ?: null,
                'shiptostore_tax' => (bool)$data[$this->alias]['includes_shipping'],
            ];
            foreach ($locations as $locationId => &$location) {
                $location = ['id' => $locationId] + $location + $propagatedFields;
            }
        }

        $locationsFieldList = [
            'id',
            'defaultTax',
            'defaultTaxName',
            'tax_id_number',
            'shiptostore_tax',
        ];

        $validated = ($this->clear() && $this->set($data) && $this->validates());
        $locationsValidated = (!$locations || $this->User->validateMany($locations, ['fieldList' => $locationsFieldList]));
        if (!$validated || !$locationsValidated) {
            if ($this->User->validationErrors) {
                $this->validationErrors['User'] = $this->User->validationErrors;
                $this->data['User'] = array_map(
                    fn(array $location): array => (array)$location['User'],
                    array_intersect_key($locations, $this->User->validationErrors)
                );
            }

            return false;
        }

        return (
            $this->save($data, ['validate' => false])
            && (!$locations || $this->User->saveMany($locations, ['validate' => false, 'fieldList' => $locationsFieldList]))
        );
    }

    public function saveManyFromTaxSettings(int $userId, int $countryId, array $data): bool
    {
        if (!$this->validateMany($data)) {
            return false;
        }
        $data = Hash::combine($data, "{n}.{$this->alias}.state_id", "{n}.{$this->alias}");

        $data = array_filter($data, function($tax) {
            return is_numeric($tax['tax_percentage']);
        });
        $validStateIds = array_keys($this->State->getStateList($countryId));
        $validStateIds = array_combine($validStateIds, $validStateIds);
        $data = array_intersect_key($data, $validStateIds);

        $existingTaxIds = $this->find('list', [
            'conditions' => [
                "{$this->alias}.user_id" => $userId,
                "{$this->alias}.state_id" => $validStateIds,
            ],
            'fields' => ["{$this->alias}.state_id", "{$this->alias}.id"],
        ]);
        $data = array_map(function($tax) use ($userId, $existingTaxIds) {
            $stateId = $tax['state_id'];
            $keys = [
                'id' => ($existingTaxIds[$stateId] ?? null),
                'user_id' => $userId,
                'state_id' => $stateId,
            ];
            $derived = [
                'uses_manual_rate' => true,
                'tax_rate' => format_number(floatval($tax['tax_percentage']) / 100, 5),
            ];

            return $keys + $tax + $derived;
        }, $data);

        $success = (!$data || $this->saveMany($data));

        if ($success) {
            $taxIdsToSave = array_filter(array_column($data, 'id', 'id'));
            $taxIdsToDelete = array_diff($existingTaxIds, $taxIdsToSave);
            $taxDeleteConditions = ["{$this->alias}.id" => $taxIdsToDelete];
            if ($taxIdsToDelete && !$this->deleteAllJoinless($taxDeleteConditions, false)) {
                CakeLog::warning(json_encode(['message' => 'Failed to delete user state taxes', 'conditions' => $taxDeleteConditions]));

                return false;
            }
        }

        return $success;
    }
}
