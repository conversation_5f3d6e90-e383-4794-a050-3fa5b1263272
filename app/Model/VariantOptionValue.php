<?php
App::uses('AppModel', 'Model');

/**
 * VariantOptionValue Model.
 *
 * @property VariantOption $VariantOption
 */
class VariantOptionValue extends AppModel
{
    public $displayField = 'value';

    public $validate = [
        'variant_option_id' => [
            'numeric' => ['rule' => 'numeric', 'message' => 'Invalid variant option id'],
        ],
        'value' => [
            'notBlank' => ['rule' => 'notBlank', 'required' => 'create', 'message' => 'This field cannot be left blank'],
        ],
        'created_at' => [
            'datetime' => ['rule' => 'datetime', 'message' => 'Invalid datetime'],
        ],
    ];

    public $belongsTo = [
        'VariantOption',
    ];

    public $recursive = -1;

    public $order = ['value' => 'ASC'];

    public $virtualFields = [];

    public function initialize(array $config)
    {
        parent::initialize($config);

        $this->virtualFields['lower_case'] = "LOWER({$this->alias}.{$this->displayField})";
    }
}
