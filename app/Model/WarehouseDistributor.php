<?php
App::uses('AppModel', 'Model');

/**
 * WarehouseDistributor Model.
 *
 * @property Warehouse $Warehouse
 * @property User $Distributor
 */
class WarehouseDistributor extends AppModel
{
    public $validate = [
        'warehouse_id' => [
            'numeric' => ['rule' => 'numeric', 'message' => 'Invalid warehouse id'],
        ],
        'distributor_id' => [
            'numeric' => ['rule' => 'numeric', 'message' => 'Invalid distributor id'],
        ],
        'created_at' => [
            'datetime' => ['rule' => 'datetime', 'message' => 'Invalid datetime'],
        ],
    ];

    public $belongsTo = [
        'Warehouse',
        'Distributor' => ['className' => 'User'],
    ];

    public $recursive = -1;
}
