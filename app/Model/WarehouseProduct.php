<?php
App::uses('AppModel', 'Model');
App::uses('Warehouse', 'Model');

/**
 * WarehouseProduct Model.
 *
 * @property Warehouse $Warehouse
 * @property Product $Product
 * @property WarehouseProductReservation $WarehouseProductReservation
 *
 * @method array|null findByWarehouseIdAndProductId($warehouseId, $productId, $fields = array(), $order = null, $recursive = null) Magic find method parsed by DboSource::query.
 * @method array|null findAllByWarehouseIdAndProductId($warehouseId, $productId, $fields = array(), $order = null, $limit = null, $page = null, $recursive = null) Magic find method parsed by DboSource::query.
 */
class WarehouseProduct extends AppModel
{
    public $validate = [
        'warehouse_id' => [
            'naturalNumber' => ['rule' => 'naturalNumber', 'message' => 'Invalid warehouse id'],
        ],
        'product_id' => [
            'naturalNumber' => ['rule' => 'naturalNumber', 'message' => 'Invalid product id'],
        ],
        'quantity' => [
            'numeric' => ['rule' => 'numeric', 'allowEmpty' => true, 'message' => 'Invalid quantity'],
        ],
        'reserved_quantity' => [
            'numeric' => ['rule' => 'numeric', 'message' => 'Invalid reserved quantity'],
        ],
        'restock_date' => [
            'date' => ['rule' => 'date', 'allowEmpty' => true, 'message' => 'Invalid restock date'],
        ],
    ];

    public $belongsTo = ['Warehouse', 'Product'];

    public $hasMany = ['WarehouseProductReservation'];

    public $recursive = -1;

    public function initialize(array $config)
    {
        parent::initialize($config);

        $this->virtualFields['available_quantity'] = "IF({$this->alias}.quantity IS NOT NULL, GREATEST(0, {$this->alias}.quantity - {$this->alias}.reserved_quantity), NULL)";
    }

    public function buildOrderReservationSubquery($productId, $warehouseId = null)
    {
        $conditions = [
            'WarehouseProduct.product_id' => $productId,
        ];
        if ($warehouseId) {
            $conditions['WarehouseProduct.warehouse_id'] = $warehouseId;
        }
        return $this->buildSubquery([
            'recursive' => -1,
            'joins' => [
                [
                    'table' => 'warehouse_product_reservations',
                    'alias' => 'WarehouseProductReservation',
                    'type' => 'INNER',
                    'conditions' => ['WarehouseProduct.id = WarehouseProductReservation.warehouse_product_id'],
                ],
            ],
            'conditions' => $conditions,
            'fields' => [
                'WarehouseProductReservation.order_id',
                'SUM(WarehouseProduct.quantity) AS `quantity`',
                'SUM(WarehouseProductReservation.quantity) AS `reserved_quantity`',
                'COUNT(WarehouseProduct.id) AS `count`',
            ],
            'group' => 'WarehouseProductReservation.order_id',
        ]);
    }

    public function buildProductTotalsSubQuery(): string
    {
        return $this->buildProductAggregateSubquery([
            "{$this->alias}.product_id",
            "SUM({$this->alias}.quantity) AS `quantity`",
            "SUM({$this->alias}.reserved_quantity) AS `reserved_quantity`",
            "SUM({$this->virtualFields['available_quantity']}) AS `available_quantity`",
            "MIN({$this->alias}.restock_date) AS `restock_start_date`",
            "MAX({$this->alias}.restock_date) AS `restock_end_date`",
            "COUNT({$this->alias}.id) AS `count`",
        ]);
    }

    public function buildProductAggregateSubquery(array $fields, array $conditions = []): string
    {
        return $this->buildSubquery([
            'recursive' => -1,
            'joins' => [
                [
                    'table' => $this->Warehouse->table,
                    'alias' => $this->Warehouse->alias,
                    'type' => 'INNER',
                    'conditions' => [
                        "{$this->Warehouse->alias}.id" => $this->identifier("{$this->alias}.warehouse_id"),
                        "{$this->Warehouse->alias}.is_active" => true,
                        "{$this->Warehouse->alias}.name !=" => Warehouse::NAME_RESERVED,
                    ],
                ],
            ],
            'conditions' => $conditions,
            'fields' => $fields,
            'group' => "{$this->alias}.product_id",
        ]);
    }

    /**
     * Save the provided inventory data or delete any matching records if data is empty.
     *
     * @param int $warehouseId
     * @param int $productId
     * @param array $data
     * @return bool Success
     * @throws Exception
     */
    public function syncEcommerceInventory($warehouseId, $productId, array $data): bool
    {
        $conditions = [
            "{$this->alias}.warehouse_id" => $warehouseId,
            "{$this->alias}.product_id" => $productId,
        ];

        if (!$data) {
            return (bool)$this->deleteAll($conditions, false);
        }

        $keys = [
            'id' => $this->field('id', $conditions),
            'warehouse_id' => $warehouseId,
            'product_id' => $productId,
        ];
        $this->clear();

        return (bool)$this->save($keys + $data);
    }

    public function updateReservedQuantity($id, int $delta, bool $adjustQuantity = false): bool
    {
        $update = ["{$this->alias}.reserved_quantity" => "`{$this->alias}`.`reserved_quantity` + ({$delta})"];
        if ($adjustQuantity) {
            $update += ["{$this->alias}.quantity" => "`{$this->alias}`.`quantity` + ({$delta})"];
        }

        return $this->updateAllJoinless($update, ["{$this->alias}.id" => $id]);
    }

    public function getRestockDate(int $warehouseId, int $productId)
    {
        return $this->field('restock_date', [
                $this->alias . '.warehouse_id' => $warehouseId,
                $this->alias . '.product_id' => $productId,
        ]) ?: null;
    }

    /**
     * @param array $lineItems Expected to have keys 'warehouse_id' and 'product_id'.
     * @param int|null $orderId Optional order id to exclude from reserved quantity.
     * @return array $lineItems with associated 'WarehouseProduct'
     */
    public function addWarehouseProductsToLineItems(array $lineItems, ?int $orderId = null): array
    {
        // Build a condition to precisely select all productId/warehouseId combos
        $conditions['OR'] = array_map(function($item) {
            return [
                "{$this->alias}.warehouse_id" => $item['warehouse_id'],
                "{$this->alias}.product_id" => $item['product_id'],
            ];
        }, $lineItems);

        $fields = [
            'id',
            'warehouse_id',
            'product_id',
            'quantity',
            'reserved_quantity',
            'restock_date',
            'available_quantity',
        ];

        $originalVirtualFields = $this->virtualFields;
        $joins = [];
        if ($orderId !== null) {
            $joins = [
                [
                    'table' => $this->WarehouseProductReservation->table,
                    'alias' => 'WarehouseProductReservation',
                    'type' => 'LEFT',
                    'conditions' => [
                        'WarehouseProductReservation.warehouse_product_id' => $this->primaryKeyIdentifier(),
                        'WarehouseProductReservation.order_id' => $orderId,
                    ],
                ],
            ];
            $this->virtualFields['reserved_quantity'] = "{$this->alias}.reserved_quantity - COALESCE(WarehouseProductReservation.quantity, 0)";
            $this->virtualFields['available_quantity'] = "IF({$this->alias}.quantity IS NOT NULL, GREATEST(0, {$this->alias}.quantity - ({$this->virtualFields['reserved_quantity']})), NULL)";
        }
        $warehouseProducts = Hash::combine(
            $this->find('all', ['joins' => $joins, 'conditions' => $conditions, 'fields' => $fields]),
            "{n}.{$this->alias}.product_id",
            "{n}.{$this->alias}",
            "{n}.{$this->alias}.warehouse_id"
        );
        $this->virtualFields = $originalVirtualFields;

        $nullWarehouseProduct = array_fill_keys($fields, null);

        return array_map(function($item) use ($warehouseProducts, $nullWarehouseProduct) {
            $warehouseId = $item['warehouse_id'];
            $productId = $item['product_id'];

            $item['WarehouseProduct'] = $warehouseProducts[$warehouseId][$productId] ?? $nullWarehouseProduct;

            return $item;
        }, $lineItems);
    }

    public function listAvailableQuantitiesForProductIds(int $brandId, array $productIds): array
    {
        return (array)$this->find('list', [
            'contain' => ['Warehouse'],
            'conditions' => [
                "{$this->alias}.product_id" => $productIds,
                "{$this->Warehouse->alias}.user_id" => $brandId,
                "{$this->Warehouse->alias}.name !=" => Warehouse::NAME_RESERVED,
                "{$this->Warehouse->alias}.is_active" => true,
            ],
            'fields' => ["{$this->alias}.warehouse_id", "{$this->alias}.available_quantity", "{$this->alias}.product_id"],
        ]);
    }
}
