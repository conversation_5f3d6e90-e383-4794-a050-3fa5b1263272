<?php
App::uses('AppModel', 'Model');

/**
 * ZoneTaxOverrideCollection Model.
 *
 * @property Collection $Collection
 * @property ZoneTaxOverride $ZoneTaxOverride
 */
class ZoneTaxOverrideCollection extends AppModel
{
    public $displayField = 'id';

    public $validate = [
        'collection_id' => [
            'naturalNumber' => ['rule' => 'naturalNumber', 'message' => 'Invalid collection id'],
        ],
        'zone_tax_override_id' => [
            'naturalNumber' => ['rule' => 'naturalNumber', 'message' => 'Invalid zone tax override id'],
        ],
    ];

    public $belongsTo = [
        'Collection',
        'ZoneTaxOverride',
    ];

    public $recursive = -1;
}
