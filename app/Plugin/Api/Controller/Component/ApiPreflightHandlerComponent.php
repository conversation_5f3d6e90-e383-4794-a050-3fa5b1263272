<?php
App::uses('Component', 'Controller');
App::uses('CakeRequest', 'Network');
App::uses('CakeResponse', 'Network');
App::uses('Cache', 'Cache');
App::uses('AuthComponent', 'Controller/Component');

/**
 * Class ApiPreflightHandlerComponent.
 *
 * This should be added to the controller before AuthComponent.
 * Replies to preflight OPTIONS requests with CORS headers.
 *
 * @see CakeResponse::cors
 *
 * @property CakeRequest $request
 * @property CakeResponse $response
 */
class ApiPreflightHandlerComponent extends Component
{
    /**
     * List of allowed domains.
     *
     * @var string|string[]
     * @see CakeResponse::cors
     */
    public $allowedDomains = ['*'];

    /**
     * List of HTTP verbs allowed.
     *
     * @var string|string[]
     * @see CakeResponse::cors
     */
    public $allowedMethods = ['GET', 'PATCH', 'POST', 'PUT', 'DELETE', 'HEAD', 'OPTIONS'];

    /**
     * List of HTTP headers allowed.
     *
     * @var string|string[]
     * @see CakeResponse::cors
     */
    public $allowedHeaders = ['*'];

    public function initialize(Controller $controller)
    {
        parent::initialize($controller);
        $this->request = $controller->request;
        $this->response = $controller->response;

        $this->response->cors($this->request, $this->allowedDomains, $this->allowedMethods, $this->allowedHeaders);
    }

    public function startup(Controller $controller)
    {
        parent::startup($controller);

        $this->assertRequestBodyIsValidJson();

        if ($this->request->is('options')) {
            $this->response->statusCode(204);
            $this->response->send();
            $this->_stop();
        }
    }

    private function assertRequestBodyIsValidJson(){
        $exceptionText = 'The request body is invalid.';
        if(json_last_error() != JSON_ERROR_NONE){
            $jsonErrorMessage = json_last_error_msg();
            throw new BadRequestException("{$exceptionText} Error: {$jsonErrorMessage}");
        }
    }
}
