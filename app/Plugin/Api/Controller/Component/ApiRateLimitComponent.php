<?php
App::uses('Component', 'Controller');
App::uses('CakeRequest', 'Network');
App::uses('CakeResponse', 'Network');
App::uses('Cache', 'Cache');
App::uses('AuthComponent', 'Controller/Component');

/**
 * Class ApiThrottleComponent.
 *
 * This should be added to the controller after AuthComponent.
 *
 * @property CakeRequest $request
 * @property CakeResponse $response
 */
class ApiRateLimitComponent extends Component
{
    public $cacheConfig = 'default';

    public $cacheKeyPrefix = 'ApiRateLimit';

    public $bucketSize = 40;

    public $leaksPerSecond = 2.0;

    public $costByMethod = [
        'GET' => 1.0,
        'OPTIONS' => 1.0,
    ];

    public function initialize(Controller $controller)
    {
        parent::initialize($controller);
        $this->request = $controller->request;
        $this->response = $controller->response;
    }

    public function startup(Controller $controller)
    {
        parent::startup($controller);

        $bucket = $this->fetchBucket();
        $timestamp = microtime(true);

        $leakage = $this->leaksPerSecond * ($timestamp - $bucket['timestamp']);
        $drops = max($bucket['drops'] - $leakage, 0);
        $drops += $this->costByMethod[$this->request->method()] ?? 1.0;

        $isFull = ($drops > $this->bucketSize);
        $drops = min($drops, $this->bucketSize);

        $this->saveBucket(compact('drops', 'timestamp'));

        $reset = ceil(1 / $this->leaksPerSecond);

        $this->response->header([
            'RateLimit-Limit' => $this->bucketSize,
            'RateLimit-Remaining' => $this->bucketSize - ceil($drops),
            'RateLimit-Reset' => $reset,
        ]);

        if ($isFull) {
            $this->response->header('Retry-After', $reset);
            $this->response->statusCode(429);
            $this->response->send();
            $this->_stop();
            return false;
        }
    }

    protected function fetchBucket(): array
    {
        $bucket = $this->readCache('bucket');

        return [
            'drops' => $bucket['drops'] ?? 0,
            'timestamp' => $bucket['timestamp'] ?? microtime(true),
        ];
    }

    protected function saveBucket(array $bucket): bool
    {
        return $this->writeCache('bucket', $bucket);
    }

    protected function readCache(string $key): array
    {
        return (array)Cache::read($this->key($key), $this->cacheConfig) ?: [];
    }

    protected function writeCache(string $key, array $value): bool
    {
        return Cache::write($this->key($key), $value, $this->cacheConfig);
    }

    protected function key(string $key): string
    {
        $id = AuthComponent::user('client_id');
        if (!$id) {
            $id = $this->request->clientIp();
        }

        return implode('.', array_filter([$this->cacheKeyPrefix, $id, $key]));
    }
}
