<?php
App::uses('DispatcherFilter', 'Routing');
App::uses('CakeRequest', 'Network');

/**
 * Class ApiDispatcher.
 */
class ApiDispatcher extends DispatcherFilter
{
    /**
     * Default priority for all methods in this filter.
     *
     * This filter should run after the request gets parsed by Router.
     *
     * @var int
     */
    public $priority = 11;

    public function beforeDispatch(CakeEvent $event)
    {
        parent::beforeDispatch($event);
        /** @var CakeRequest $request */
        $request = $event->data['request'];
        if ($this->isApiRequest($request)) {
            $this->_overrideCacheConfig();
            $request->params = $this->setDefaultExt($request->params, 'json');
            Configure::write('Exception.renderer', 'Api.ApiExceptionRenderer');
        }
    }

    private function isApiRequest(CakeRequest $request)
    {
        return preg_match('#^api(/v\d+)?$#', (string)$request->param('plugin'));
    }

    /**
     * Override the Cache config as soon as we know we are in an api plugin.
     */
    private function _overrideCacheConfig()
    {
        App::uses('Cache', 'Cache');

        // EagerLoader plugin causes 'method_cache' in '_cake_core_' to grow until we run out of memory
        // Disable its cache until the issue is fixed
        Cache::drop('_cake_core_');
    }

    /**
     * Set the response to behave as if the url has an extension when it does not.
     *
     * @param array $params
     * @param null|string $ext
     * @return array
     */
    private function setDefaultExt(array $params, $ext = null)
    {
        if ($ext === null) {
            unset($params['ext']);
            return $params;
        }
        return $params + compact('ext');
    }
}
