<?php
App::uses('Router', 'Routing');
App::uses('Inflector', 'Utility');
App::uses('ApiRoute', 'Api.Routing/Route');
App::uses('User', 'Model');

$plugin = 'Api/v1';
$prefix = 'ApiV1';

// Gerneral API Routes
$controllers = [
    "{$plugin}.{$prefix}Couriers",
    "{$plugin}.{$prefix}CreditTerms",
    "{$plugin}.{$prefix}Discounts",
    "{$plugin}.{$prefix}Inventory",
    "{$plugin}.{$prefix}InventoryTransfers",
    "{$plugin}.{$prefix}Orders",
    "{$plugin}.{$prefix}PricingTiers",
    "{$plugin}.{$prefix}Products",
    "{$plugin}.{$prefix}RetailerCredits",
    "{$plugin}.{$prefix}Retailers",
    "{$plugin}.{$prefix}RetailInventory",
    "{$plugin}.{$prefix}SalesReps",
    "{$plugin}.{$prefix}Territories",
    "{$plugin}.{$prefix}Variants",
    "{$plugin}.{$prefix}Warehouses",
];
Router::mapResources($controllers, [
    'connectOptions' => ['routeClass' => 'ApiRoute'],
    'id' => Router::ID,
]);
foreach ($controllers as $controller) {
    (function ($controller) {
        list($plugin, $controller) = pluginSplit($controller);
        $controller = Inflector::underscore($controller);
        $plugin = Inflector::underscore($plugin);
        Router::connect(
            "/{$plugin}/{$controller}/count",
            ['plugin' => $plugin, 'controller' => $controller, 'action' => 'count', '[method]' => 'GET'],
            ['routeClass' => 'ApiRoute']
        );
    })($controller);
}

$childResources = [
    ['controller' => "{$plugin}.{$prefix}RetailInventory", 'path_parts' => ['retailers', 'inventory']],
    ['controller' => "{$plugin}.{$prefix}RetailerCredits", 'path_parts' => ['retailers', 'credits']],
    ['controller' => "{$plugin}.{$prefix}RetailerCreditItems", 'path_parts' => ['retailers', 'credits', 'items'], 'legacy_pass' => true],
    ['controller' => "{$plugin}.{$prefix}RetailerCreditPayments", 'path_parts' => ['retailers', 'credits', 'payments']],
    ['controller' => "{$plugin}.{$prefix}RetailerBranches", 'path_parts' => ['retailers', 'branches'], 'legacy_pass' => true],
    ['controller' => "{$plugin}.{$prefix}RetailInventory", 'path_parts' => ['variants', 'retail_inventory']],
    ['controller' => "{$plugin}.{$prefix}ProductTiers", 'path_parts' => ['variants', 'pricing_tiers']],
    ['controller' => "{$plugin}.{$prefix}Refunds", 'path_parts' => ['orders', 'refunds'], 'legacy_pass' => true],
    ['controller' => "{$plugin}.{$prefix}Fulfillments", 'path_parts' => ['orders', 'fulfillments'], 'legacy_pass' => true],
    ['controller' => "{$plugin}.{$prefix}OrderItems", 'path_parts' => ['orders', 'items'], 'legacy_pass' => true],
    ['controller' => "{$plugin}.{$prefix}DealerOrders", 'path_parts' => ['orders', 'dealer_order'], 'legacy_pass' => true],
    ['controller' => "{$plugin}.{$prefix}DealerOrderRefunds", 'path_parts' => ['orders', 'dealer_order/refunds'], 'legacy_pass' => true],
    ['controller' => "{$plugin}.{$prefix}DealerOrderFulfillments", 'path_parts' => ['orders', 'dealer_order/fulfillments'], 'legacy_pass' => true],
    ['controller' => "{$plugin}.{$prefix}DealerOrderItems", 'path_parts' => ['orders', 'dealer_order/items'], 'legacy_pass' => true],
    ['controller' => "{$plugin}.{$prefix}InventoryTransfers", 'path_parts' => ['warehouses', 'inventory_transfers']],
    ['controller' => "{$plugin}.{$prefix}Inventory", 'path_parts' => ['warehouses', 'inventory']],
    ['controller' => "{$plugin}.{$prefix}Inventory", 'path_parts' => ['variants', 'inventory']],
];
foreach ($childResources as $child) {
    (function (string $controller, array $pathParts, bool $legacyPass) {
        list($plugin, $controller) = pluginSplit($controller);
        $plugin = Inflector::underscore($plugin);
        $controller = Inflector::underscore($controller);

        $passParams = [];
        $childResource = array_pop($pathParts);

        $url = "/{$plugin}";
        foreach ($pathParts as $pathPart) {
            $pathPartSingular = Inflector::singularize($pathPart);
            $pathPartId = "{$pathPartSingular}_id";
            $passParams[$pathPartId] = Router::ID;
            $url .= "/{$pathPart}/:{$pathPartId}";
        }
        $url .= "/{$childResource}";
        
        $passParams['id'] = Router::ID;
        if (!$legacyPass) {
            // Pass :id as the first arg to view(), edit(), and delete() actions
            $passParams = array_reverse($passParams, true);
        }

        $options = [
            'routeClass' => 'ApiRoute', 
            'pass' => array_keys($passParams), 
        ] + $passParams;

        foreach (Router::resourceMap() as $params) {
            $resourceUrl = $url . (($params['id']) ? '/:id' : '');
            Router::connect(
                $resourceUrl,
                ['plugin' => $plugin, 'controller' => $controller, 'action' => $params['action'], '[method]' => $params['method']],
                $options
            );
        }
        Router::connect(
            $url . '/count',
            ['plugin' => $plugin, 'controller' => $controller, 'action' => 'count', '[method]' => 'GET'],
            $options
        );
    })($child['controller'], $child['path_parts'], !empty($child['legacy_pass']));
}

// Specialized routes that either don't fit into the patterns above
// OR should only be exposed to certain HTTP methods like POST
$plugin = Inflector::underscore($plugin);

Router::connect(
    "/{$plugin}/inventory/:retailer_id/count",
    ['plugin' => $plugin, 'controller' => Inflector::underscore("{$prefix}RetailInventory"), 'action' => 'count', 'user_types' => [User::TYPE_RETAILER]],
    ['routeClass' => 'ApiRoute', 'retailer_id' => Router::ID, 'pass' => ['retailer_id']]
);
Router::connect(
    "/{$plugin}/inventory_transfers/:id/receive",
    ['plugin' => $plugin, 'controller' => Inflector::underscore("{$prefix}InventoryTransfers"), 'action' => 'receive', '[method]' => 'POST'],
    ['routeClass' => 'ApiRoute', 'id' => Router::ID, 'pass' => ['id']]
);
Router::connect(
    "/{$plugin}/warehouses/:warehouse_id/inventory_transfers/:id/receive",
    ['plugin' => $plugin, 'controller' => Inflector::underscore("{$prefix}InventoryTransfers"), 'action' => 'receive', '[method]' => 'POST'],
    ['routeClass' => 'ApiRoute', 'id' => Router::ID, 'warehouse_id' => Router::ID, 'pass' => ['id', 'warehouse_id']]
);
Router::connect(
    "/{$plugin}/product_locator",
    ['plugin' => $plugin, 'controller' => Inflector::underscore("{$prefix}ProductLocator"), 'action' => 'locate', '[method]' => 'GET'],
    ['routeClass' => 'ApiRoute']
);

unset($plugin);
