<?php
App::uses('ApiV1ReadController', 'Api/v1.Controller');
App::uses('ApiV1RetailersController', 'Api/v1.Controller');
App::uses('ApiV1OrderItemsController', 'Api/v1.Controller');
App::uses('ApiV1FulfillmentsController', 'Api/v1.Controller');
App::uses('ApiV1RefundsController', 'Api/v1.Controller');
App::uses('ApiV1CustomersController', 'Api/v1.Controller');

/**
 * Class ApiV1CustomersController.
 */
class ApiV1CustomersController extends ApiV1ReadController
{
    public $presentationName = 'customer';
    public $presentationNamePlural = 'customers';

    public function inputFieldToModelField(): array
    {
        return [
            'first_name' => 'firstname',
            'last_name' => 'lastname',
            'email_address' => 'email_address',
            'accepts_marketing' => 'accepts_marketing',
        ];
    }
}
