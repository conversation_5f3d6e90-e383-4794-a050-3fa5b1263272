<?php
App::uses('ApiV1OrderItemsController', 'Api/v1.Controller');
App::uses('ApiV1OrderItem', 'Api/v1.Model');

/**
 * Class ApiV1OrderItemsController.
 *
 * @property ApiV1OrderItem $Model
 */
class ApiV1DealerOrderItemsController extends ApiV1OrderItemsController
{
    public $presentationName = 'order_item';
    public $presentationNamePlural = 'order_items';

    public function inputFieldToModelField(): array
    {
        return [
            'id' => 'id',
            'order_id' => 'order_id',
            'product_id' => 'product_title_id',
            'variant_id' => 'productID',
            'sku' => 'product_sku',
            'upc' => 'product_upc',
            'title' => 'product_title',
            'quantity' => 'product_quantity',
            'quantity_to_fulfill' => 'quantity_to_fulfill',
            'total_discount' => 'total_discount',
            'price' => 'product_price',
            'subtotal' => 'subtotal',
            'total_price' => 'total_price',
            'tax_amount' => 'total_tax',
            'estimated_ship_date' => 'estimated_ship_date',
            'inventory_transfer_id' => 'inventory_transfer_id',
            'warehouse_id' => 'warehouse_id',
            'created_at' => 'created_at',
            'updated_at' => 'updated_at',
        ];
    }
}
