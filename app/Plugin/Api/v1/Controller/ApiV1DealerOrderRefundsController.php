<?php
App::uses('ApiV1RefundsController', 'Api/v1.Controller');
App::uses('ApiV1RefundProductsController', 'Api/v1.Controller');

/**
 * Class ApiV1RefundsController.
 * 
 * @property ApiV1DealerOrderRefund $Model
 */
class ApiV1DealerOrderRefundsController extends ApiV1RefundsController
{
    public function inputFieldToModelField(): array
    {
        return array_replace(parent::inputFieldToModelField(),['created_at' => 'created_at']);
    }
}
