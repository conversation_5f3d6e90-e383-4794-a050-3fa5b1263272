<?php
App::uses('ApiV1OrderChildController', 'Api/v1.Controller');
App::uses('ApiV1RetailersController', 'Api/v1.Controller');
App::uses('ApiV1DealerOrderItemsController', 'Api/v1.Controller');
App::uses('ApiV1DealerOrderFulfillmentsController', 'Api/v1.Controller');
App::uses('ApiV1DealerOrderRefundsController', 'Api/v1.Controller');

/**
 * Class ApiV1DealerOrdersController.
 */
class ApiV1DealerOrdersController extends ApiV1OrderChildController
{
    public $presentationName = 'dealer_order';
    public $presentationNamePlural = 'dealer_orders';

    public $retailerActions = [
        'index',
        'view',
    ];

    public function inputFieldToModelField(): array
    {
        return [
            'id' => 'id',
            'total_discount' => 'total_discount',
            'subtotal' => 'product_total_price',
            'total_refund' => 'total_refund',
            'total_price' => 'total_price',
            'net_total' => 'net_total',
            'total_shipping' => 'shipping_amount',
            'total_tax' => 'total_tax',
            'fulfillment_status' => 'fulfillment_status',
            'created_at' => 'created_at',
            'updated_at' => 'updated_at',
            'shipping_lines' => 'shipping_lines',
            ApiV1DealerOrderItemsController::class => 'order_items',
            ApiV1DealerOrderFulfillmentsController::class => 'fulfillments',
            ApiV1DealerOrderRefundsController::class => 'refunds',
        ];
    }

    // read methods
    public function index()
    {
        $this->view();
    }
    
    public function view($id = null)
    {
        $this->set('data', $this->modelToPresentation($this->Model->view($id, $this->request->query)));
    }
}
