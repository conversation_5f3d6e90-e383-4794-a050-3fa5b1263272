<?php
App::uses('ApiV1ReadController', 'Api/v1.Controller');
App::uses('ApiV1OrderChildController', 'Api/v1.Controller');
App::uses('ApiV1FulfillmentProduct', 'Api/v1.Model');

/**
 * Class ApiV1FulfillmentsController.
 *
 * @property ApiV1FulfillmentProduct $Model
 */
class ApiV1FulfillmentProductsController extends ApiV1ReadController
{
    public $presentationName = 'item';
    public $presentationNamePlural = 'items';

    public function inputFieldToModelField(): array
    {
        return [
            'id' => 'id',
            'product_id' => 'product_title_id',
            'variant_id' => 'productID',
            'sku' => 'product_sku',
            'upc' => 'product_upc',
            'title' => 'product_title',
            'order_item_id' => 'order_product_id',
            'fulfilled_quantity' => 'quantity',
        ];
    }
}
