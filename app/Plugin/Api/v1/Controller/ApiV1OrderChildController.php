<?php
App::uses('ApiV1ReadController', 'Api/v1.Controller');
App::uses('ApiV1Order', 'Api/v1.Model');

/**
 * Class ApiV1OrderChildController.
 *
 * @property ApiV1Order $OrderModel
 */
abstract class ApiV1OrderChildController extends ApiV1ReadController
{
    public $orderId;

    public function beforeFilter()
    {
        parent::beforeFilter();

        $this->OrderModel = ClassRegistry::init('Api/v1.ApiV1Order');
        $this->orderId = $this->request->params['pass'][0];
        $this->Model->orderId = $this->orderId;
        $this->request->params['pass'][0] = $this->request->params['pass'][1] ?? null;
        unset($this->request->params['pass'][1]);
    }
}
