<?php

App::uses('ApiV1ReadWriteController', 'Api/v1.Controller');
App::uses('ApiV1ReadModel', 'Api/v1.Model');

/**
 * Class ApiV1ReadController.
 *
 * @property ApiV1ReadModel $Model
 */
abstract class ApiV1ReadController extends ApiV1ReadWriteController
{    
    public $components = [
        'Api.ApiPreflightHandler' => [
            'allowedMethods' => ['GET', 'HEAD', 'OPTIONS'],
        ],
    ];
    
    public function add()
    {
        throw new InvalidOperationException();
    }

    public function edit($id = null)
    {
        throw new InvalidOperationException();
    }

    public function delete($id = null)
    {
        throw new InvalidOperationException();
    }

    public function getWriteFields(): array
    {
        return [];
    }
}
