<?php
App::uses('ApiV1ReadController', 'Api/v1.Controller');

/**
 * Class ApiV1RetailerCredit.
 */
class ApiV1RetailerCreditItemProductsController extends ApiV1ReadController
{

    public $presentationName = 'product';
    public $presentationNamePlural = 'products';

    public function inputFieldToModelField(): array {
        return [
            'id' => 'id',
            'quantity' => 'quantity',
            'amount' => 'amount',
            'title' => 'product_title',
            'variant_id' => 'productID',
        ];
     }
}
