<?php
App::uses('ApiV1ReadWriteController', 'Api/v1.Controller');
App::uses('ApiV1RetailerBranchesController', 'Api/v1.Controller');
App::uses('User', 'Model');

/**
 * Class ApiV1RetailersController.
 *
 * @property ApiV1Retailer $ApiV1Retailer
 */
class ApiV1RetailersController extends ApiV1ReadWriteController
{
    public $presentationName = 'retailer';
    public $presentationNamePlural = 'retailers';

    public $uses = [
        'Api/v1.ApiV1Retailer',
    ];

    public function inputFieldToModelField(): array
    {
        $commonFields = [
            'id' => 'id',
            'account_id' => 'external_retailer_account',
            'billing_company' => 'external_retailer_company',
            'billing_email' => 'external_retailer_email',
            'local_delivery_radius' => 'local_delivery_radius',
            'enable_on_file_payment' => 'enable_b2b_external_payment',
            'company_name' => 'company_name',
            'address1' => 'address1',
            'address2' => 'address2',
            'city' => 'city',
            'state_code' => 'state_code',
            'zip_code' => 'zipcode',
            'country_code' => 'country_code',
            'latitude' => 'latitude',
            'longitude' => 'longitude',
            'default_warehouse_id' => 'warehouse_id',
            'is_non_stocking' => 'is_ship_to_store_only',
            'b2b_minimum_order_value' => 'b2b_minimum',
            'ship_from_store_distance' => 'ship_from_store_distance',
            'is_installer' => 'enable_install',
            'b2b_tax_rate' => 'b2b_tax',
            'created_at' => 'created',
            'updated_at' => 'updated',
            ApiV1RetailerBranchesController::class => 'branches',
        ];

        $retailerFields = [];

        $brandFields = [
            'status' => 'status',
            'pricing_tier_id' => 'pricingtierid',
            'sales_reps' => 'sales_rep_ids',
            'credit_terms' => 'credit_term_ids',
            'territory' => 'territory_name',
            'dealer_protect_radius' => 'dealer_protect_radius',
            'credit_limit' => 'credit_limit',
            'has_active_inventory_integration' => 'has_active_integration',
            'enable_credit_card' => 'enable_b2b_credit_card',

        ];

        if(AuthComponent::user('User.user_type') === User::TYPE_RETAILER) {
            $fields = array_merge($commonFields, $retailerFields);
        } else {
            $fields = array_merge($commonFields, $brandFields);
        }

        return $fields;
    }

    public function connect($id)
    {
        $this->ApiV1Retailer->connect($id);
    }

    public function disconnect($id)
    {
        $this->ApiV1Retailer->disconnect($id);
    }
}
