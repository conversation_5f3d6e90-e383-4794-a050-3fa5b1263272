<?php

App::uses('ApiV1ReadWriteController', 'Api/v1.Controller');

/**
 * Class ApiV1VariantOptionsController.
 *
 * @property ApiV1VariantOption $Model
 */
class ApiV1VariantOptionsController extends ApiV1ReadWriteController
{
    public $presentationName = 'variant_option';
    public $presentationNamePlural = 'variant_options';

    public function inputFieldToModelField(): array
    {
        return [
            'id' => 'id',
            'product_id' => 'product_title_id',
            'source_id' => 'source_id',
            'name' => 'name',
            'position' => 'position',
            'created_at' => 'created_at',
            'updated_at' => 'updated_at',
        ];
    }
}
