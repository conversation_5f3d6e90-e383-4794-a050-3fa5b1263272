<?php
App::uses('ApiAppModel', 'Api.Model');
App::uses('AuthComponent', 'Controller/Component');
App::uses('Validation', 'Utility');

/**
 * Class ApiV1AppModel.
 */
abstract class ApiV1AppModel extends ApiAppModel
{
    /**
     * The object "type" field given in an API response.
     *
     * @var string
     */
    public $type = null;

    /**
     * List of API types included in the model description. Used for associations.
     *
     * @var array
     */
    public $typeToModel = [];

    public function __construct($id = false, $table = null, $ds = null)
    {
        parent::__construct($id, $table, $ds);

        if (!$this->type) {
            $this->type = !empty($id['type']) ? $id['type'] : Inflector::tableize($this->name);
        }
        $this->typeToModel[$this->type] = $this->alias;
        foreach (array_keys($this->getAssociated()) as $assoc) {
            if (isset($this->{$assoc}->type)) {
                $this->typeToModel[$this->{$assoc}->type] = $assoc;
            }
        }
    }

    protected function _constructLinkedModel($assoc, $className = null, $plugin = null)
    {
        parent::_constructLinkedModel($assoc, $className, $plugin);

        if (isset($this->{$assoc}->type)) {
            $this->typeToModel[$this->{$assoc}->type] = $assoc;
        }
    }
}
