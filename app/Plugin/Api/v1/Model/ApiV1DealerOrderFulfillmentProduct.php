<?php

App::uses('ApiV1ReadModel', 'Api/v1.Model');

/**
 * Class ApiV1DealerOrderFulfillmentProduct.
 *
 * @property ApiV1DealerOrderItem $ApiV1DealerOrderItem
 * @property ApiV1DealerOrderFulfillment $ApiV1DealerOrderFulfillment
 */
class ApiV1DealerOrderFulfillmentProduct extends ApiV1ReadModel
{
    public $useTable = 'fulfillment_products';

    public $belongsTo = [
        'Api/v1.ApiV1DealerOrderFulfillment' => [
            'foreignKey' => 'fulfillment_id',
        ],
        'Api/v1.ApiV1DealerOrderItem' => [
            'foreignKey' => 'dealer_order_product_id',
        ],
    ];

    public function getReadFields(): array
    {
        return [
            "{$this->alias}.id",
            "{$this->alias}.quantity",
        ];
    }

    public function afterFind($results, $primary = false)
    {
        if (!$this->isCountResult($results)) {
            $results = array_map(function($result) use ($primary) {
                $item = key_exists($this->alias, $result) ? $result[$this->alias] : $result;
                if ($this->hasExpectedAssociations($result, array_keys($this->getBaseContain()))) {
                    $item['product_sku'] = $result[$this->ApiV1DealerOrderItem->alias][$this->ApiV1DealerOrderItem->ApiV1Variant->alias]['product_sku'] ?? null;
                    $item['product_upc'] = $result[$this->ApiV1DealerOrderItem->alias][$this->ApiV1DealerOrderItem->ApiV1Variant->alias]['product_upc'] ?? null;
                    $item['product_title'] = $result[$this->ApiV1DealerOrderItem->alias][$this->ApiV1DealerOrderItem->ApiV1Variant->alias]['product_title'] ?? null;
                    $item['productID'] = $result[$this->ApiV1DealerOrderItem->alias][$this->ApiV1DealerOrderItem->ApiV1Variant->alias]['productID'] ?? null;
                    $item['product_title_id'] = $result[$this->ApiV1DealerOrderItem->alias][$this->ApiV1DealerOrderItem->ApiV1Variant->alias]['product_title_id'] ?? null;
                }

                if (key_exists($this->alias, $result)) {
                    $result[$this->alias] = $item;
                } else {
                    $result = $item;
                }

                return $result;
            }, $results);
        }

        return parent::afterFind($results, $primary);
    }

    public function index(array $params = [])
    {
        throw new InvalidOperationException();
    }

    public function count(array $params = [])
    {
        throw new InvalidOperationException();
    }

    public function view($id, array $params = [])
    {
        throw new InvalidOperationException();
    }

    public function getBaseContain(): array
    {
        return [
            'ApiV1DealerOrderFulfillment' => [
                'fields' => [
                    'id',
                ],
            ],
            'ApiV1DealerOrderItem' => [
                'fields' => ['id', 'order_product_id', 'product_id'],
                'ApiV1Variant' => [
                    'fields' => ['id', 'productID', 'product_title_id', 'product_sku', 'product_upc', 'product_title'],
                ],
            ],
        ];
    }
}
