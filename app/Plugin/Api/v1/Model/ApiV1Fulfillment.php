<?php

use ShipEarlyApp\Plugin\Api\v1\Model\Traits\ApiV1FulfillmentTrait;

App::uses('ApiV1OrderChild', 'Api/v1.Model');

/**
 * Class ApiV1Fulfillment.
 *
 * @property ApiV1FulfillmentProduct $ApiV1FulfillmentProduct
 */
class ApiV1Fulfillment extends ApiV1OrderChild
{
    use ApiV1FulfillmentTrait;

    public $useTable = 'fulfillments';

    public $hasMany = [
        'Api/v1.ApiV1FulfillmentProduct' => [
            'foreignKey' => 'fulfillment_id',
        ],
    ];
}
