<?php

App::uses('ApiV1ReadWriteModel', 'Api/v1.Model');
App::uses('InventoryTransferStatus', 'Utility');

/**
 * Class ApiV1InventoryTransfer.
 *
 * @property ApiV1InventoryTransferProduct $ApiV1InventoryTransferProduct
 * @property InventoryTransfer $InventoryTransfer
 * @property ApiV1Warehouse $ApiV1Warehouse
 */
class ApiV1InventoryTransfer extends ApiV1ReadWriteModel
{
    public $useTable = 'inventory_transfers';

    public $warehouseId;

    protected $presentableErrors = [
        'destination_warehouse_id',
        'expected_arrival_date',
        'reference',
        'InventoryTransferProduct',
    ];

    public $hasOne = [
        'InventoryTransfer' => [
            'foreignKey' => 'id',
        ],
    ];

    public $hasMany = [
        'Api/v1.ApiV1InventoryTransferProduct' => [
            'foreignKey' => 'inventory_transfer_id',
        ],
    ];

    public $belongsTo = [
        'Api/v1.ApiV1Warehouse' => [
            'foreignKey' => 'destination_warehouse_id',
        ],
    ];

    public function add(array $data = []): int
    {
        $this->validateRequestData($data);
        $addData = $this->prepareDataForSave($data);
        if (!$this->InventoryTransfer->saveFromApi(null, $this->authUser('id'), $addData)) {
            $this->throwValidationError($this->InventoryTransfer);
        }

        return $this->InventoryTransfer->id;
    }

    public function remove(int $id)
    {
        if (!$this->exists(["{$this->alias}.{$this->primaryKey}" => $id])) {
            throw new NotFoundException();
        }
        
        $existing = $this->InventoryTransfer->findForDelete($id);
        if ($existing['InventoryTransfer']['status'] !== InventoryTransferStatus::PENDING) {
            $message = sprintf('Cannot delete a transfer in %s status', InventoryTransferStatus::getLabel($existing['InventoryTransfer']['status']));

            throw new BadRequestException($message);
        }

        if (!$this->delete($id)) {
            throw new InternalErrorException('Failed to delete transfer.');
        }
    }

    public function edit(int $id, array $data = [])
    {
        if (!$this->exists(["{$this->alias}.{$this->primaryKey}" => $id])) {
            throw new NotFoundException();
        }
        $existing = $this->InventoryTransfer->findForEdit($id);
        $this->validateRequestData($data);
        $editData = $this->prepareDataForSave($data, $existing);
        if (!$this->InventoryTransfer->saveFromApi($id, $this->authUser('id'), $editData)) {
            $this->throwValidationError($this->InventoryTransfer);
        }
    }

    public function receive(int $id)
    {
        if (!$this->exists(["{$this->alias}.{$this->primaryKey}" => $id])) {
            throw new NotFoundException();
        }

        $transferToReceive = $this->InventoryTransfer->findForEdit($id);
        if ($transferToReceive['InventoryTransfer']['status'] !== InventoryTransferStatus::PENDING) {
            $message = sprintf('Cannot receive a transfer in %s status', InventoryTransferStatus::getLabel($transferToReceive['InventoryTransfer']['status']));

            throw new BadRequestException($message);
        }

        $transferToReceive['InventoryTransfer']['status'] = InventoryTransferStatus::COMPLETED;
        if (!$this->InventoryTransfer->saveFromApi($id, $this->authUser('id'), $transferToReceive)) {
            $this->throwValidationError($this->InventoryTransfer);
        }
    }

    public function getWriteFields(): array
    {
        return [
            'expected_arrival_date',
            'destination_warehouse_id',
            'reference',
        ];
    }

    public function getReadFields(): array
    {
        return [
            "{$this->alias}.id",
            "{$this->alias}.name",
            "{$this->alias}.status",
            "{$this->alias}.expected_arrival_date",
            "{$this->alias}.destination_warehouse_id",
            "{$this->alias}.reference",
            "{$this->alias}.created_at",
            "{$this->alias}.updated_at",
        ];
    }

    public function beforeFind($query)
    {
        $query['conditions']["{$this->alias}.user_id"] = $this->authUser('id');
        if (isset($this->warehouseId)) {
            $query['conditions']["{$this->alias}.destination_warehouse_id"] = $this->warehouseId;
        }

        return parent::beforeFind($query);
    }

    public function afterFind($results, $primary = false)
    {
        if (!$this->isCountResult($results)) {
            $results = array_map(function($result) use ($primary) {
                $item = key_exists($this->alias, $result) ? $result[$this->alias] : $result;
                if ($this->hasExpectedAssociations($result, array_keys($this->getBaseContain()))) {
                    $item['products'] = $this->ApiV1InventoryTransferProduct->afterFind($result[$this->ApiV1InventoryTransferProduct->alias]);
                    $item['quantity'] = (string)array_sum(array_column($item['products'], 'quantity'));
                    $item['status'] = InventoryTransferStatus::getLabel($item['status']);
                }

                if (key_exists($this->alias, $result)) {
                    $result[$this->alias] = $item;
                } else {
                    $result = $item;
                }

                return $result;
            }, $results);
        }

        return $results;
    }

    public function getBaseContain(): array
    {
        return [
            'ApiV1InventoryTransferProduct' => [
                'fields' => array_keys($this->ApiV1InventoryTransferProduct->virtualFields),
            ] + $this->ApiV1InventoryTransferProduct->getBaseContain(),
        ];
    }

    protected function validateRequestData(array $data = [])
    {
        if (
            empty($data['destination_warehouse_id'])
            || !$this->ApiV1Warehouse->exists(["{$this->ApiV1Warehouse->alias}.{$this->ApiV1Warehouse->primaryKey}" => $data['destination_warehouse_id']])
        ) {
            throw new BadRequestException('destination_warehouse_id is invalid');
        }
    }

    protected function prepareDataForSave(array $data, array $existing = []): array
    {
        if (isset($data['products'])) {
            $saveData['InventoryTransferProduct'] = array_map(function($product) {
                $product = array_intersect_key($product, array_flip($this->ApiV1InventoryTransferProduct->getWriteFields()));
                $product['product_id'] = $this->ApiV1InventoryTransferProduct->ApiV1VariantAbbreviated->fetchDatabaseId($product['productID']);
                unset($product['productID']);

                return $product;
            }, $data['products']);

            unset($data['products']);
        }
        $saveData['InventoryTransfer'] = array_intersect_key($data, array_flip($this->getWriteFields()));
        if (!empty($existing['InventoryTransfer']['status'])) {
            $saveData['InventoryTransfer']['status'] = $existing['InventoryTransfer']['status'];
        }

        return $saveData;
    }
}
