<?php

App::uses('ApiV1ReadWriteModel', 'Api/v1.Model');
App::uses('ApiV1Retailer', 'Api/v1.Model');
App::uses('ApiV1OrderItem', 'Api/v1.Model');
App::uses('ApiV1Fulfillment', 'Api/v1.Model');
App::uses('ApiV1Refund', 'Api/v1.Model');
App::uses('ApiV1DealerOrder', 'Api/v1.Model');
App::uses('Discount', 'Model');
App::uses('DiscountRule', 'Model');
App::uses('OrderAddress', 'Model');
App::uses('Country', 'Model');
App::uses('State', 'Model');
App::uses('Order', 'Model');
App::uses('OrderType', 'Utility');
App::uses('B2bCartType', 'Utility');
App::uses('OrderPaymentStatus', 'Utility');
App::uses('OrderStatus', 'Utility');
App::uses('ShippingCalculatorComponent', 'Controller/Component');

/**
 * Class ApiV1Order.
 *
 * @property ApiV1Retailer $ApiV1Retailer
 * @property ApiV1OrderItem $ApiV1OrderItem
 * @property ApiV1Fulfillment $ApiV1Fulfillment
 * @property ApiV1Refund $ApiV1Refund
 * @property ApiV1DealerOrder $ApiV1DealerOrder
 * @property ApiV1Customer $ApiV1Customer
 * @property ApiV1CreditTerm $ApiV1CreditTerm
 * @property Order $Order
 * @property OrderAddress $BillingAddress
 * @property Country $Country
 * @property State $State
 * @property Discount $Discount
 * @property User $User
 */
class ApiV1Order extends ApiV1ReadWriteModel
{
    public $useTable = 'orders';

    protected $presentableErrors = [
        'external_invoice_id',
        'requested_b2b_ship_date',
        'inventory_transfer_id',
        'order_items',
        'currency_code',
        'total_shipping',
        'credit_term',
        'product_id',
        'country_code_state_code',
        'warehouse_id',
        'total_price',
        'requested_ship_date',
        'shipping_address',
        'credit_term_id',
    ];

    public $validate = [
        'external_invoice_id' => [
            'Invalid invoice number' => ['rule' => 'notBlank', 'allowEmpty' => true],
            'Invoice number too long' => ['rule' => ['maxLength', '50'], 'message' => 'Invoice number must be 50 characters or less.'],
        ],
        'requested_b2b_ship_date' => [
            'Invalid ship date' => ['rule' => ['date', 'ymd'], 'allowEmpty' => true, 'message' => 'Requested ship date must be in the format: yyyy-mm-dd'],
        ],
    ];

    public $belongsTo = [
        'Api/v1.ApiV1Customer' => [
            'foreignKey' => 'customerID',
        ],
        'Api/v1.ApiV1Retailer' => [
            'foreignKey' => 'retailer_id',
        ],
        'Api/v1.ApiV1CreditTerm' => [
            'foreignKey' => 'credit_term_id',
        ],
        'Country' => [
            'foreignKey' => 'shipping_countrycode',
        ],
        'State' => [
            'foreignKey' => 'shipping_statecode',
        ],
        'Discount' => [
            'foreignKey' => 'discount_id',
        ],
        'User' => [
            'className' => 'User',
            'foreignKey' => 'user_id',
        ],
    ];

    public $hasMany = [
        'Api/v1.ApiV1OrderItem' => [
            'foreignKey' => 'order_id',
        ],
        'Api/v1.ApiV1Fulfillment' => [
            'foreignKey' => 'order_id',
        ],
        'Api/v1.ApiV1Refund' => [
            'foreignKey' => 'order_id',
        ],
    ];

    public $hasOne = [
        'BillingAddress' => [
            'foreignKey' => 'order_id',
            'className' => 'OrderAddress',
            'conditions' => ['BillingAddress.type' => OrderAddress::TYPE_BILLING],
        ],
        'Api/v1.ApiV1DealerOrder' => [
            'foreignKey' => 'order_id',
        ],
        'Order' => [
            'foreignKey' => 'id',
        ],
    ];

    public function add(array $data = []): int
    {
        if($data['order_type'] === OrderType::WHOLESALE){
            return $this->createWholesaleOrder($data);
        } else {
            throw new BadRequestException("Unrecognized order_type: '{$data['order_type']}'");
        }
    }

    public function remove(int $id)
    {
        throw new InvalidOperationException();
    }

    public function edit(int $id, array $data = [])
    {
        $this->id = $id;
        if (!$this->canEdit($id)) {
            throw new BadRequestException('Order does not exist.');
        }

        // Do a manual check to see if $data is empty and perform no operation if it is
        // Normally a save with an empty data set is fine but for this model save([]) returns false.
        // when $data is empty, cake checks if 'created', 'updated' or 'modified' columns exist. if not, the save fails and returns false.
        // some, but not all, of our tables follow this convention exactly
        if (empty($data)) {
            return;
        }

        $data = $this->processEditData($id, $data);

        if (
            !empty($data['order_items'])
            || array_key_exists('shipping_amount', $data)
        ) {
            if ($data['order_status'] === OrderStatus::PURCHASE_ORDER) {
                if (!$this->Order->editPurchaseOrder($id, $data['order_items'], $data['shipping_amount'], $data['dealer_discount'])) {
                    $this->throwValidationError($this->Order);
                }
            } elseif ($data['order_status'] === OrderStatus::PROCESSING) {
                if (!$this->ApiV1DealerOrder->DealerOrder->updateWholesaleProcessingInventoryTransfers($id, $data['order_items'])) {
                    $this->throwValidationError($this->Order);
                }
            }
        }

        // remove any fields unintended for direct edits.
        $data = array_intersect_key($data, array_flip($this->getWriteFields()));
        if (empty($data)) {
            return;
        }
        if (!$this->save($data)) {
            $this->throwValidationError($this);
        }
    }

    public function getWriteFields(): array
    {
        return [
            'external_invoice_id',
            'requested_b2b_ship_date',
            'credit_term_id'
        ];
    }

    public function getReadFields(): array
    {
        $subtotalTaxSubquery = $this->ApiV1OrderItem->buildSubquery([
            'conditions' => [
                "{$this->ApiV1OrderItem->alias}.order_id" => $this->primaryKeyIdentifier(),
                "{$this->ApiV1OrderItem->alias}.status !=" => 'Cancelled',
            ],
            'fields' => ["SUM({$this->ApiV1OrderItem->alias}.total_tax)"],
        ]);

        return [
            "{$this->alias}.id",
            "{$this->alias}.order_type",
            "{$this->alias}.order_status",
            "{$this->alias}.external_invoice_id",
            "{$this->alias}.orderNO",
            "{$this->alias}.orderID",
            "{$this->alias}.purchase_order_number",
            "{$this->alias}.discount_code",
            "{$this->alias}.secretcode",
            "{$this->alias}.risk_level",
            "{$this->alias}.requested_b2b_ship_date",
            "{$this->alias}.shipped_date",
            "{$this->alias}.deliveryDate",
            "{$this->alias}.fulfillment_status",
            "{$this->alias}.payment_method",
            "{$this->alias}.payment_status",
            "{$this->alias}.currency_code",
            "{$this->alias}.total_discount",
            "{$this->alias}.total_price",
            "{$this->alias}.shipping_amount",
            "{$this->alias}.shipping_discount",
            'shipping_tax_amount' => "ROUND(GREATEST(0.00, {$this->alias}.total_tax - {$subtotalTaxSubquery}), 2)",
            "{$this->alias}.total_tax",
            "{$this->alias}.created_at",
            "{$this->alias}.updated_at",
            "{$this->alias}.billing_firstname",
            "{$this->alias}.billing_lastname",
            "{$this->alias}.shipping_company_name",
            "{$this->alias}.shipping_address1",
            "{$this->alias}.shipping_address2",
            "{$this->alias}.shipping_city",
            "{$this->alias}.shipping_state",
            "{$this->alias}.shipping_country",
            "{$this->alias}.shipping_zipcode",
            "{$this->alias}.latitude",
            "{$this->alias}.longitude",
            "{$this->alias}.shipping_telephone",
            "{$this->alias}.shipping_statecode",
        ];
    }

    public function beforeFind($query)
    {
        $query['conditions']["{$this->alias}.{$this->getUserIdColumn()}"] = $this->getChildAuthUserIds();

        return parent::beforeFind($query);
    }

    public function afterFind($results, $primary = false)
    {
        if (!$this->isCountResult($results)) {
            // Retrieving from ApiV1DealerOrder triggers ApiV1Order::afterFind through its ParentOrder association,
            // so beware of infinite recursion by checking that the retrieval is expected
            $resultsExpectingDealerOrder = array_filter($results, function(array $result): bool {
                return $this->hasExpectedAssociations($result, array_keys($this->getBaseContain()));
            });
            $dealerOrdersByOrderId = ($resultsExpectingDealerOrder)
                ? $this->listDealerOrdersByOrderId(array_map(fn($result) => $result[$this->alias]['id'] ?? $result['id'], $resultsExpectingDealerOrder))
                : [];

            $results = array_map(function($result) use ($dealerOrdersByOrderId) {
                $item = key_exists($this->alias, $result) ? $result[$this->alias] : $result;
                if ($this->hasExpectedAssociations($result, array_keys($this->getBaseContain()))) {
                    $result[$this->ApiV1Retailer->alias] = current($this->ApiV1Retailer->afterFind([$result[$this->ApiV1Retailer->alias]]));
                    $result[$this->ApiV1Customer->alias] = current($this->ApiV1Customer->afterFind([$result[$this->ApiV1Customer->alias]]));
                    $result[$this->ApiV1DealerOrder->alias] = (array)($dealerOrdersByOrderId[$item['id']] ?? []);
                    $item['shipping_lines'] = [];
                    if ($item['shipping_amount']) {
                        $item['shipping_lines'][] = [
                            'service' => null,
                            'price' => $item['shipping_amount'],
                            'discount' => $item['shipping_discount'],
                            'discounted_price' => format_number($item['shipping_amount'] - $item['shipping_discount']),
                            'total_tax' => $item['shipping_tax_amount'],
                        ];
                    }
                    if ($item['order_type'] === OrderType::WHOLESALE && !empty($result[$this->ApiV1DealerOrder->alias]['shipping_lines'])) {
                        $item['shipping_lines'] = $result[$this->ApiV1DealerOrder->alias]['shipping_lines'];
                    }
                    $item['order_items'] = $this->ApiV1OrderItem->afterFind($result[$this->ApiV1OrderItem->alias]);
                    $item['fulfillments'] = $this->ApiV1Fulfillment->afterFind($result[$this->ApiV1Fulfillment->alias]);
                    $item['refunds'] = $this->ApiV1Refund->afterFind($result[$this->ApiV1Refund->alias]);
                    $item['retailer'] = $result[$this->ApiV1Retailer->alias] ?: null;
                    $item['brand'] = $result[$this->User->alias]['company_name'] ?: null;
                    $item['store'] = $result[$this->ApiV1Retailer->alias]['company_name'] ?? '';
                    $item['customer'] = $result[$this->ApiV1Customer->alias] ?: null;
                    $item['dealer_order'] = $result[$this->ApiV1DealerOrder->alias] ?: null;
                    $item['credit_term'] = $result[$this->ApiV1CreditTerm->alias]['description'] ?? null;
                    $item['billing_address'] =
                        $this->normalizeAddressFields(
                            $item,
                            [],
                            [],
                            $result['BillingAddress']
                        );
                    $item['shipping_address'] =
                        $this->normalizeAddressFields(
                            $item,
                            $result[$this->State->alias],
                            $result[$this->Country->alias]
                        );
                    $item['subtotal'] = format_number($item['total_price'] - $item['shipping_amount'] - $item['total_tax']);
                    $item['total_price'] = format_number($item['total_price'] - $item['total_discount']);
                    $item['total_refund'] = format_number(array_sum(array_column($item['refunds'], 'amount')));
                    $item['payment_status'] = $this->resolvePaymentStatusString($item['payment_status'] ?? 0);
                    $item['net_total'] = format_number($item['total_price'] - $item['total_refund']);
                }

                if (key_exists($this->alias, $result)) {
                    $result[$this->alias] = $item;
                } else {
                    $result = $item;
                }

                return $result;
            }, $results);
        }

        return parent::afterFind($results, $primary);
    }

    protected function listDealerOrdersByOrderId(array $orderIds): array
    {
        $records = (array)$this->ApiV1DealerOrder->find('all', [
            'contain' => $this->ApiV1DealerOrder->getBaseContain(),
            'conditions' => [
                "{$this->ApiV1DealerOrder->alias}.order_id" => $orderIds,
            ],
            'fields' => array_keys($this->ApiV1DealerOrder->virtualFields),
        ]);

        // Format the direct query results to look like association query results
        $records = array_map(function($record) {
            $record[$this->ApiV1DealerOrder->alias] += array_diff_key($record, array_flip([$this->ApiV1DealerOrder->alias]));

            return $record[$this->ApiV1DealerOrder->alias];
        }, $records);

        return array_combine(array_column($records, 'order_id'), $records);
    }

    /**
     * @param int $paymentStatusId
     * @return string
     * @see OrderPaymentStatus::getLabel
     */
    protected function resolvePaymentStatusString(int $paymentStatusId): string
    {
        // Deliberately not using OrderPaymentStatus::getLabel to keep these values fixed
        $paymentStatus = OrderPaymentStatus::API_VALUE_MAP[$paymentStatusId] ?? '';
        // TODO Retire below values
        if ($paymentStatusId == OrderPaymentStatus::REQUESTED) {
            $paymentStatus = 'Requested';
        } elseif ($paymentStatusId == OrderPaymentStatus::PAID) {
            $paymentStatus = 'Paid';
        } elseif ($paymentStatusId == OrderPaymentStatus::AUTHORIZED) {
            $paymentStatus = 'On Hold';
        }

        return $paymentStatus;
    }

    protected function normalizeAddressFields(array $order, array $shippingState = [], array $shippingCountry = [], array $address = [])
    {
        $normalizedAddress = [];
        if (empty($address)) {
            $normalizedAddress = [
                'first_name' => $order['billing_firstname'] ?? '',
                'last_name' => $order['billing_lastname'] ?? '',
                'company_name' => $order['shipping_company_name'] ?? '',
                'address1' => $order['shipping_address1'] ?? '',
                'address2' => $order['shipping_address2'] ?? '',
                'city' => $order['shipping_city'],
                'state' => $shippingState['state_code'] ?? $order['shipping_state'] ?? '',
                'country' => $shippingCountry['country_code'] ?? $order['shipping_country'] ?? '',
                'zip_code' => $order['shipping_zipcode'] ?? '',
                'phone' => $order['shipping_telephone'] ?? '',
                'latitude' => $order['latitude'] ?? '',
                'longitude' => $order['longitude'] ?? '',
            ];
        } else {
            $normalizedAddress = [
                'first_name' => $address['first_name'] ?? '',
                'last_name' => $address['last_name'] ?? '',
                'company_name' => $address['company_name'] ?? '',
                'address1' => $address['address1'] ?? '',
                'address2' => $address['address2'] ?? '',
                'city' => $address['city'] ?? '',
                'state' => $address[$this->BillingAddress->State->alias]['state_code'] ?? '',
                'country' => $address[$this->BillingAddress->Country->alias]['country_code'] ?? '',
                'zip_code' => $address['zipcode'] ?? '',
                'phone' => $address['telephone'] ?? '',
                'latitude' => $address['latitude'] ?? '',
                'longitude' => $address['longitude'] ?? '',
            ];
        }

        return $normalizedAddress;
    }

    protected function findForEdit(int $id): array
    {
        $order = $this->record($id, [
            'contain' => [
                'ApiV1OrderItem' => ['fields' => array_keys($this->ApiV1OrderItem->virtualFields)] + $this->ApiV1OrderItem->getBaseContain(),
                'Discount' => [
                    'fields' => Discount::CALCULATION_REQUIRED_FIELDS,
                    'DiscountRule' => ['fields' => DiscountRule::CALCULATION_REQUIRED_FIELDS],
                ],
            ],
            'fields' => ['user_id', 'retailer_id', 'order_type', 'order_status', 'total_discount', 'payment_method'],
        ]) ?? [];

        $order[$this->ApiV1OrderItem->alias] = array_map(function($orderItem) {
            $orderItem = $this->ApiV1OrderItem->afterFind([$orderItem])[0];
            $orderItem['dealer_price'] = $orderItem['price'];

            return $orderItem;
        }, $order[$this->ApiV1OrderItem->alias]);

        $order[$this->ApiV1OrderItem->alias] = $this->Discount->calculateApiOrderProductDiscounts(
            (int)$order[$this->alias]['user_id'],
            (array)$order[$this->Discount->alias],
            $order[$this->ApiV1OrderItem->alias]
        );

        return $order;
    }

    public function getTotalPrice($orderId): float
    {
        return (float)$this->fieldByConditions('total_price', ["{$this->alias}.id" => $orderId]);
    }

    public function getBaseContain(): array
    {
        // Note that the ApiV1DealerOrder assoc is retrieved later during afterFind
        // because EagerLoader has been known to timeout when processing it
        return [
            'ApiV1Retailer' => [
                'fields' => array_keys($this->ApiV1Retailer->virtualFields),
            ] + array_diff_key($this->ApiV1Retailer->getBaseContain(), ['ApiV1RetailerBranch' => 0]),
            'ApiV1OrderItem' => [
                'fields' => array_keys($this->ApiV1OrderItem->virtualFields),
            ] + $this->ApiV1OrderItem->getBaseContain(),
            'ApiV1Fulfillment' => [
                'fields' => array_keys($this->ApiV1Fulfillment->virtualFields),
            ] + $this->ApiV1Fulfillment->getBaseContain(),
            'ApiV1Refund' => [
                'fields' => array_keys($this->ApiV1Refund->virtualFields),
            ] + $this->ApiV1Refund->getBaseContain(),
            'ApiV1Customer' => [
                'fields' => array_keys($this->ApiV1Customer->virtualFields),
            ] + $this->ApiV1Customer->getBaseContain(),
            'ApiV1CreditTerm' => ['fields' => ['id', 'description']],
            'BillingAddress' => [
                'fields' => [
                    'first_name',
                    'last_name',
                    'company_name',
                    'address1',
                    'address2',
                    'city',
                    'state_id',
                    'country_id',
                    'zipcode',
                    'telephone',
                    'latitude',
                    'longitude',
                ],
                'State' => ['fields' => ['id', 'state_code']],
                'Country' => ['fields' => ['id', 'country_code']],
            ],
            'State' => ['fields' => ['id', 'state_code']],
            'Country' => ['fields' => ['id', 'country_code']],
            'User' => ['fields' => ['id', 'company_name']],
        ];
    }

    public function fulfillOrderType($id)
    {
        $orderType = $this->field('order_type', ['id' => $id]);

        if (OrderType::filterOrderType($orderType) === OrderType::SELL_DIRECT) {
            return 'order';
        }

        return 'dealer_order';
    }

    public function canEditInventoryTransferId(array $order): bool
    {
        $canEdit = true;
        $canEdit = $canEdit && $order[$this->alias]['order_type'] === OrderType::WHOLESALE;
        $canEdit = $canEdit && (in_array($order[$this->alias]['order_status'], [OrderStatus::PURCHASE_ORDER, OrderStatus::PROCESSING]));

        return $canEdit;
    }

    public function canEditQuantity(array $order): bool
    {
        $canEdit = true;
        $canEdit = $canEdit && ($order[$this->alias]['order_status'] === OrderStatus::PURCHASE_ORDER);

        return $canEdit;
    }

    protected function getOrderStatus(int $id): string
    {
        $contain = [
            'ApiV1Order' => ['fields' => ['id', 'order_status']],
        ];
        $order = $this->find('first', [
            'contain' => $contain,
            'conditions' => [
                "{$this->alias}.id" => $id,
            ],
            'fields' => ['id'],
        ]);

        return $order[$this->ApiV1Order->alias]['order_status'] ?? '';
    }

    protected function isWholesaleOrder(int $id): bool
    {
        $contain = [
            'ApiV1Order' => ['fields' => ['id', 'order_type']],
        ];
        $order = $this->find('first', [
            'contain' => $contain,
            'conditions' => [
                "{$this->alias}.id" => $id,
            ],
            'fields' => ['id'],
        ]);

        return $order[$this->ApiV1Order->alias]['order_type'] === OrderType::WHOLESALE;
    }

    protected function hasAvailableQuantityForProduct(array $orderItem)
    {
        return $this->ApiV1OrderItem->ApiV1InventoryTransfer->InventoryTransfer->hasAvailableQuantityForProduct($orderItem['inventory_transfer_id'], $orderItem['product_id'], $orderItem['quantity']);
    }

    protected function calculateTotalDiscount(array $order)
    {
        return $this->Discount->calculateTotalDiscount(array_column($order[$this->ApiV1OrderItem->alias], 'discount'));
    }

    public function createDealerOrderFromConfirmedWholesaleOrder(int $orderId, ?string $shippingName): int
    {
        $order = $this->Order->findForPopup($orderId);
        $orderItems = $order['OrderProduct'];

        $dealer_qty_ordered = $order['Order']['dealer_qty_ordered'];
        $orderItems = array_map(function($orderItem) use($dealer_qty_ordered){
            $orderItem['price'] = $orderItem['price'] ?? $dealer_qty_ordered['products'][$orderItem['product_id']]['dealer_price'];
            $orderItem['dealer_price'] = $orderItem['price'];
            return $orderItem;
        }, $orderItems);

        $discountAmount = (float)$order['Order']['total_discount'];
        $shippingAmount = (float)$order['Order']['shipping_amount'];

        if (!$this->ApiV1DealerOrder->DealerOrder->createFromConfirmedOrder($order, $orderItems, $discountAmount, $shippingAmount, $shippingName)) {
            throw new InternalErrorException(json_encode(['errors' => $this->ApiV1DealerOrder->DealerOrder->validationErrors, 'data' => $this->ApiV1DealerOrder->DealerOrder->data]));
        }

        return (int)$this->ApiV1DealerOrder->DealerOrder->id;
    }

    protected function calculateShippingFromDealerProducts($brandId, $retailerId, array $dealerProducts)
    {
        return $this->getShippingCalculatorComponent()->calculateShippingFromDealerProducts($brandId, $retailerId, $dealerProducts);
    }

    public function calculateB2bCartShipping($b2bCart): float
    {
        return $this->getShippingCalculatorComponent()->calculateB2bCartShipping((array)$b2bCart);
    }

    protected function getShippingCalculatorComponent(): ShippingCalculatorComponent
    {
        // needed to calculate shipping for the updated/created orders. Ideally wouldn't call a component from model.
        return new ShippingCalculatorComponent(new ComponentCollection());
    }
    protected function prepareAddData(array $data)
    {
        $data['requested_b2b_ship_date'] = $data['requested_b2b_ship_date'] ?? '';
        $data['purchase_order_number'] = $data['purchase_order_number'] ?? '';
        $data['notes'] = $data['notes'] ?? '';

        if(isset($data['currency_code'])){
            if(count(Hash::extract($data['order_items'], '{n}[price]')) != count($data['order_items'])){
                $this->validationErrors['order_items'][] = 'order_items must include price.';
            }
            if (!Currency::currencyExists($data['currency_code'])){
                $this->validationErrors['currency_code'][] = 'Currency does not exist.';
            }
        } else {
            $data['currency_code'] = null;
        }
        if(isset($data['requested_ship_date'])
            && !(bool)preg_match('/^\d{4}-\d{2}-\d{2}$/', $data['requested_ship_date'])
            && !Validation::date($data['requested_ship_date'])){
                $this->validationErrors['requested_ship_date'][] = 'requested_ship_date bad date format.';
        }
        if (isset($data['shipping_amount'])
            && !is_numeric($data['shipping_amount'])){
            $this->validationErrors['total_shipping'][] = 'total_shipping must be a number.';
        }
        
        $this->validateCreditTerm($data);
        $data['order_items'] = $this->processOrderProducts($data['order_items']);
        $data['shipping_address'] = $this->processAddress($data['shipping_address'] ?? null, $data['retailer_id']);

        if(!empty($this->validationErrors)){
            $this->throwValidationError($this);
        }

        return $data;
    }

    /**
     * Manipulates the order products to interface properly with core app functions.
     *
     * @param array $products
     * @return array
     */
    protected function processOrderProducts(array $products): array
    {
        $ids = $this->ApiV1OrderItem->ApiV1Variant->find('list', [
            'recursive' => -1,
            'conditions' => [
                "{$this->ApiV1OrderItem->ApiV1Variant->alias}.productID" => array_column($products, 'productID'),
            ],
            'fields' => ['productID', 'id'],
        ]);

        $products = array_map(function($product) use ($ids) {
            $product['product_id'] = $ids[$product['productID']] ?? null;
            $product['price'] = $product['price'] ?? null;
            $product['inventory_transfer_id'] = $product['inventory_transfer_id'] ?? null;
            $product['restock_date'] = null;

            if (
                !empty($product['inventory_transfer_id'])
                && !$this->hasAvailableQuantityForProduct($product)
            ) {
                $this->invalidateInventoryTransferId($product['inventory_transfer_id'], $product['quantity']);
            }

            return $product;
        }, $products);

        if(in_array(null, array_column($products, 'product_id'))){
            $this->validationErrors['product_id'][] = 'product_id does not exist.';
        }

        $this->assertWarehousesExist(array_column($products, 'warehouse_id'));

        return $products;
    }

    /**
     * Get settings for the retailer for creating and updating order
     * @param int $userId
     * @param int $retailerId
     * @return array|int|null
     * @throws CakeException
     * @throws InvalidArgumentException
     * @throws MissingTableException
     * @throws MissingDatasourceException
     */
    protected function getManufacturerRetailer(int $userId, int $retailerId)
    {
        /**
         * @var ManufacturerRetailer $ManufacturerRetailer
         */
        $ManufacturerRetailer = ClassRegistry::init('ManufacturerRetailer');
        $originalAlias = $ManufacturerRetailer->alias;
        $ManufacturerRetailer->alias = 'ManufacturerRetailerBranch';

        $ManufacturerRetailer->bindModel([
            'belongsTo' => [
                'PricingTier' => ['foreignKey' => 'pricingtierid'],
            ],
        ], false);

        try {
            return $ManufacturerRetailer->find('first', [
                'contain' => [
                    'PricingTier' => ['fields' => ['currencytype']],
                ],
                'conditions' => [
                    "{$ManufacturerRetailer->alias}.user_id" => $userId,
                    "{$ManufacturerRetailer->alias}.retailer_id" => $retailerId,
                ],
                'fields' => [
                    "{$ManufacturerRetailer->alias}.id",
                    'PricingTier.currencytype',
                ],
            ]);
        } finally {
            $this->unbindModel(['belongsTo' => ['PricingTier']], false);
            $ManufacturerRetailer->alias = $originalAlias;
        }
    }

    /**
     * Manipulates the order products to interface properly with core app functions
     * @param array $address
     * @return void
     * @throws InvalidArgumentException
     * @throws MissingTableException
     * @throws MissingDatasourceException
     * @throws CakeException
     */
    protected function processAddress($address, int $retailerId)
    {
        if (is_array($address)) {
            $this->assertAddressHasRequiredFields($address);
            $state = $this->State->findStateAndCountryByCode($address['state'], $address['country']);
            if (empty($state)) {
                $this->validationErrors['country_code_state_code'][] = 'Unrecognized country_code, state_code combination.';
                return false;
            }
            list($state, $country) = Hash::extract($state, '{s}');
            $address = array_merge($address, [
                'b2b_ship_to_user_id' => null,
                'country_id' => $country['id'],
                'country_code' => $country['country_code'],
                'country_name' => $country['country_name'],
                'state_id' => $state['id'],
                'state_code' => $state['state_code'],
                'state_name' => $state['state_name'],
                'latitude' => null,
                'longitude' => null,
            ]);
        } else {
            $address = $this->User->findB2bShipToUserOrderAddress($this->authUser('id'), $retailerId);
        }

        $address['zipcode'] = $address['zipcode'] ?? $address['zip_code'];
        $address['email_address'] = $address['email_address'] ?? $address['email'];
        $address['telephone'] = $address['telephone'] ?? $address['phone'];
        $geopoints = $this->_findGeocode(
            $address['address1'],
            $address['city'],
            $address['zipcode'],
            $address['state_name'],
            $address['country_name']
        );
        $address = array_merge($address, [
            'latitude' => $geopoints['lat'] ?? null,
            'longitude' => $geopoints['lng'] ?? null,
        ]);

        return $address;
    }

    protected function assertAddressHasRequiredFields(array $address)
    {
        $requiredKeys = [
            'email',
            'first_name',
            'last_name',
            'address1',
            'city',
            'state',
            'country',
            'zip_code',
            'phone',
        ];

        if (count(array_diff($requiredKeys, array_keys($address))) > 0) {
            $this->validationErrors['shipping_address'][] = 'shipping_address is missing required fields.';
        }

        if(!empty($this->validationErrors)){
            $this->throwValidationError($this);
        }
    }

    protected function assertWarehousesExist(array $warehouseIds)
    {
        $warehouseIds = array_unique($warehouseIds);

        $warehouseCount = (int)$this->ApiV1OrderItem->ApiV1Warehouse->find('count', [
            'recursive' => -1,
            'conditions' => ["{$this->ApiV1OrderItem->ApiV1Warehouse->alias}.id" => $warehouseIds],
        ]);

        if ($warehouseCount !== count($warehouseIds)) {
            $this->validationErrors['warehouse_id'][] = 'warehouse_id does not exist.';
        }
    }

    /**
     * Map orderItemIds to order items based on warehouse and product ids.
     *
     * @param int $orderId
     * @param array $orderItems
     * @return array
     */
    protected function mapOrderItemIds(int $orderId, array $orderItems): array
    {
        $existing = $this->ApiV1OrderItem->find('list', [
            'recursive' => -1,
            'conditions' => ["{$this->ApiV1OrderItem->alias}.order_id" => $orderId],
            'fields' => ['product_id', 'id', 'warehouse_id'],
            'callbacks' => false,
        ]);

        return array_map(function($orderItem) use ($existing) {
            $orderItem['id'] = $existing[$orderItem['warehouse_id']][$orderItem['product_id']];

            return $orderItem;
        }, $orderItems);
    }

    protected function processEditData(int $id, array $data = [])
    {
        $existing = $this->findForEdit($id);

        $calculatedDiscount = $this->calculateTotalDiscount($existing);
        $dealerDiscount = $existing[$this->alias]['total_discount'] - $calculatedDiscount;
        $data['dealer_discount'] = $dealerDiscount;
        $data['order_status'] = $existing[$this->alias]['order_status'];
        
        if (
            !empty($data['order_items'])
            || array_key_exists('shipping_amount', $data)
        ) {
            $data['order_items'] = Hash::combine($data['order_items'] ?? [], '{n}.id', '{n}');

            $data['order_items'] = array_map(function($orderItem) use ($data, $existing) {
                $item = $data['order_items'][$orderItem['id']] ?? [];

                // if updating inventory transfer id.
                if (
                    array_key_exists('inventory_transfer_id', $item)
                    && $item['inventory_transfer_id'] != $orderItem['inventory_transfer_id']
                    && !$this->canEditInventoryTransferId($existing)
                ) {
                    throw new BadRequestException('Cannot update inventory transfer id');
                }

                // if updating quantity
                if (
                    array_key_exists('quantity', $item)
                    && $item['quantity'] != $orderItem['quantity']
                    && !$this->canEditQuantity($existing)
                ) {
                    throw new BadRequestException('Cannot update quantity.');
                }

                if (!empty($item)) {
                    $orderItem['dealer_price'] = $item['price'] ?? $orderItem['dealer_price'];
                    $orderItem['inventory_transfer_id'] = $item['inventory_transfer_id'] ?? $orderItem['inventory_transfer_id'];
                    $orderItem['quantity'] = $item['quantity'] ?? $orderItem['quantity'];
                }

                // throw validation error if transfer cannot satisfy quantity.
                if (
                    !empty($orderItem['inventory_transfer_id'])
                    && !$this->hasAvailableQuantityForProduct($orderItem)
                ) {
                    $this->invalidateInventoryTransferId($orderItem['inventory_transfer_id'], $orderItem['quantity']);
                }

                return $orderItem;
            }, $existing[$this->ApiV1OrderItem->alias]);

            // remove items with qty 0 from order
            $data['order_items'] = Hash::remove($data['order_items'], '{n}[quantity<=0]');

            if (!isset($data['shipping_amount'])) {
                // Setting shipping_amount `null` or editing order_items without shipping_amount will recalculate shipping.
                $data['shipping_amount'] = empty($existing['Discount']['enable_free_freight'])
                    ? $this->calculateShippingFromDealerProducts($this->authUser('id'), $existing[$this->alias]['retailer_id'], $data['order_items'])
                    : 0.00;

            }
        }

        $this->validateCreditTerm($data, $existing);
        if (!empty($this->validationErrors)) {
            $this->throwValidationError($this);
        }

        return $data;
    }

    protected function invalidateInventoryTransferId($transferId, $quantity){
        $this->validator()->invalidate('inventory_transfer_id', "Inventory Transfer (id:{$transferId}) does not have quantity ({$quantity}) available to satisfy order");
    }

    protected function createWholesaleOrder(array $data = [])
    {
        $data = $this->prepareAddData($data);

        $paymentMethod = $this->getPaymentMethod($data);

        /**
         * @var B2bCart $B2bCart
         */
        $B2bCart = ClassRegistry::init('B2bCart');
        $retailerId = $data['retailer_id'];

        // here we mimic the b2b cart workflow
        // create cart
        $b2bCart = $B2bCart->createNewCart($this->authUser('id'), $retailerId, B2bCartType::REGULAR, $retailerId);
        $b2bCartId = $b2bCart['id'];

        // add items to cart
        $B2bCart->B2bCartProduct->saveManyToCart($b2bCartId, $data['order_items']);

        // get cart view object and use it to create order
        $b2bCart = $B2bCart->findView($b2bCartId, [$this, 'calculateB2bCartShipping']);

        $orderId = $this->Order->createFromB2bCartView(
            $this->authUser('id'),
            $b2bCart,
            $data['shipping_address'],
            $data['requested_b2b_ship_date'],
            $data['purchase_order_number'],
            null,
            [],
            $data['notes'],
            $paymentMethod,
            null,
            $data['credit_term_id'] ?? null,
            null,
            $data['currency_code']
        );

        //delete cart
        $B2bCart->delete($b2bCartId);

        $data['order_items'] = $this->mapOrderItemIds($orderId, $data['order_items']);

        // This edit will adjust order item price and order shipping amount if included.
        $this->edit($orderId, $data);

        return $orderId;
    }

    protected function getPaymentMethod(array $data = [])
    {
        if(isset($data['credit_term_id'])){
            return OrderPaymentMethod::CREDIT;
        }
        return OrderPaymentMethod::EXTERNAL;
    }


    protected function validateCreditTerm(array $data, array $orderData = null)
    {
        if (isset($data['credit_term_id'])){
            // $orderData != null if editing an order
            if($orderData !== null){
                if($orderData[$this->alias]['order_status'] !== OrderStatus::PURCHASE_ORDER)
                {
                    $purchaseOrderStatus = OrderStatus::PURCHASE_ORDER;
                    $this->validationErrors['credit_term_id'][] = "Credit term must be set when order is at status:{$purchaseOrderStatus}. Current Status:{$orderData[$this->alias]['order_status']}";
                }
                if($orderData[$this->alias]['payment_method'] !== OrderPaymentMethod::CREDIT)
                {
                    $creditPaymentMethod = OrderPaymentMethod::CREDIT;
                    $this->validationErrors['credit_term_id'][] = "Credit term can only be set for orders with payment method:{$creditPaymentMethod}";
                }
            }
            if (!$this->ApiV1CreditTerm->exists(["{$this->ApiV1CreditTerm->alias}.{$this->ApiV1CreditTerm->primaryKey}" => $data['credit_term_id']])) {
                $this->validationErrors['credit_term_id'][] = 'credit term does not exist.';
            }
        }
    }
}
