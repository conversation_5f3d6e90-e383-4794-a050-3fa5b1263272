<?php
App::uses('ApiV1AppModel', 'Api/v1.Model');
App::uses('InvalidOperationException', 'Error');
App::uses('User', 'Model');
App::uses('Shim', 'Shim.Lib');

/**
 * Class ApiV1ReadWriteModel.
 */
abstract class ApiV1ReadWriteModel extends ApiV1AppModel
{
    public $actsAs = ['EagerLoader.EagerLoader'];

    public $virtualFields = [];

    protected $presentableErrors = [];

    protected $brandUserIdColumn = 'user_id';
    protected $retailerUserIdColumn = 'retailer_id';

    abstract public function add(array $data = []): int;
    abstract public function remove(int $id);
    abstract public function edit(int $id, array $data = []);

    public function index(array $params = [])
    {
        $query = $this->_getBaseFindQuery();

        $data = $this->find('all', $query + $params[$this->alias]);

        return array_column($data, $this->alias);
    }

    public function count(array $params = [])
    {
        $query = array_intersect_key($this->_getBaseFindQuery() + ($params[$this->alias] ?? []), array_flip([
            'contain',
            'joins',
            'conditions',
            'fields',
            'having',
        ]));
        if (!empty($query['having'])) {
            /** @see Model::_findCount() */
            // Set the first field to a COUNT field to keep virtual fields required for the having clause in the query.
            array_unshift($query['fields'], $this->getDboSource()->calculate($this, 'count'));
            // Always assign `$query['group']` to count the result set filtered by the having clause.
            if (empty($query['group'])) {
                $query['group'] = ["{$this->alias}.{$this->primaryKey}"];
            }
        } else {
            unset($query['fields']);
        }

        return (int)($this->find('count', $query));
    }

    public function view($id, array $params = [])
    {
        $query = $this->_getBaseFindQuery();
        $data = $this->find('first', $query + ['conditions' => [$this->alias . '.' . $this->primaryKey => $id]]);

        return $data[$this->alias] ?? [];
    }

    /**
     * Returns true if a record that meets given conditions exists.
     *
     * @param array $conditions SQL conditions array
     * @return bool True if such a record exists
     * @deprecated Deprecated in the shim context.
     */
    public function hasAny($conditions = null)
    {
        if (Configure::read(Shim::DEPRECATE_HAS_ANY)) {
            trigger_error('Deprecated in the shim context. Please use exists() or find() directly.', E_USER_DEPRECATED);
        }

        return $this->existsByConditions($conditions);
    }

    /**
     * Returns true if a record with particular ID exists.
     *
     * 2.x shim to separate deprecated scalar usage from new array argument.
     * This way is deprecated and should only be used if existsByConditions() doesn't work out.
     *
     * Override of Model::exists() to include base contain and execute before find callbacks which restrict the search
     * to the set of records belonging to the current user among other things.
     *
     * @param int|string $id ID of record to check for existence
     * @return bool True if such a record exists
     */
    public function existsById($id)
    {
        if ($id === null) {
            if (Configure::read(Shim::MODEL_EXISTS)) {
                trigger_error('Always pass an ID for exists() / existsById().', E_USER_DEPRECATED);
            }

            $id = $this->getID();
        }

        if ($id === false) {
            return false;
        }

        if ($this->useTable === false) {
            return false;
        }

        return $this->existsByConditions(["{$this->alias}.{$this->primaryKey}" => $id]);
    }

    /**
     * 2.x shim for 3.x exists().
     *
     * Override of Model::exists() to include base contain and execute before find callbacks which restrict the search
     * to the set of records belonging to the current user among other things.
     *
     * @param array|\ArrayAccess $conditions
     * @return bool
     */
    public function existsByConditions($conditions)
    {
        return (bool)$this->find('count', [
            'contain' => $this->getBaseContain(),
            'conditions' => $conditions,
            'recursive' => -1,
            'callbacks' => 'before',
        ]);
    }

    /**
     * check if the entity represented by $id can be edited by caller
     *
     * @param int|null $id
     * @return bool
     */
    public function canEdit(?int $id = null): bool
    {
        return $id && $this->existsById($id);
    }

    /**
     * get all read fields of entity
     * @return array
     */
    abstract public function getReadFields(): array;

    /**
     * get all write fields of entity
     * @return array
     */
    abstract public function getWriteFields(): array;

    public function __construct($id = false, $table = null, $ds = null)
    {
        parent::__construct($id, $table, $ds);

        foreach ($this->getReadFields() as $field => $definition) {
            if (is_numeric($field)) {
                $field = $definition;
            }

            if (strpos($field, '.') !== false) {
                list(, $field) = explode('.', $field, 2);
            }

            $this->virtualFields[$field] = $definition;
        }

        // Deprecated virtual fields definitions
        $this->virtualFields += $this->defineVirtualFields();
    }

    /**
     * determines if the result set is from a find('count') call
     * @param array $result result set to check
     * @return bool
     */
    protected function isCountResult(array $result)
    {
        return isset($result[0][0]['count']);
    }

    /**
     * validate the request data.
     * @param array $data
     * @return void
     */
    protected function validateRequestData(array $data = [])
    {
        if (!empty($this->validationErrors)) {
            $this->throwValidationError($this);
        }
    }

    /**
     * Get the base query for find operations.
     * @return array options[] including contain and fields elements
     */
    protected function _getBaseFindQuery()
    {
        return [
            'contain' => $this->getBaseContain(),
            'fields' => array_keys($this->virtualFields),
        ];
    }

    /**
     * Hook to define virtual fields in the constructor.
     *
     * @return array
     * @deprecated Define virtual fields in getReadFields() instead.
     */
    protected function defineVirtualFields(): array
    {
        return [];
    }

    /**
     * Get the base contain option for find queries
     * @return array
     */
    public function getBaseContain(): array
    {
        return [];
    }

    protected function throwValidationError($invalidModel)
    {
        $presentableErrors = array_intersect_key($invalidModel->validationErrors, array_flip($this->presentableErrors));

        // setting the message to json ends up with quotes escaped in the response. i.e. {"message": "{&quot;ESCAPED&quot;:&quot;JSON&quot;}"
        // an implementation of ExceptionRender for the API would let us control it better.
        throw new BadRequestException($presentableErrors ? json_encode($presentableErrors) : null);
    }

    /**
     * Check that all expected associations are present in the result set.
     * @param mixed $results 
     * @return bool 
     */
    protected function hasExpectedAssociations($results, $expectedAssociations)
    {
        $actualAssociations = array_keys($results);

        return empty(array_diff($expectedAssociations, $actualAssociations));
    }

    protected function getUserIdColumn()
    {
        if($this->authUser('user_type') === User::TYPE_RETAILER){
            return $this->retailerUserIdColumn;
        }
        return $this->brandUserIdColumn;
    }
}
