<?php
App::uses('ApiV1Retailer', 'Api/v1.Model');
App::uses('ApiV1RetailInventory', 'Api/v1.Model');
App::uses('User', 'Model');

/**
 * Class ApiV1RetailerBranch.
 *
 * @property ApiV1Retailer $ApiV1Retailer
 */
class ApiV1RetailerBranch extends ApiV1Retailer
{
    /**
     * Id of the branches parent retailer
     * @var int
     */
    public $retailerId;

    public function initialize(array $config)
    {
        parent::initialize($config);
        $this->bindModel(['belongsTo' => ['Api/v1.ApiV1Retailer' => [
            'foreignKey' => 'Branch',
            'conditions' => [
                'ApiV1Retailer.user_type' => User::TYPE_RETAILER,
            ],
        ]]], false);
    }

    public function beforeFind($query)
    {
        $query = parent::beforeFind($query);
        $query['conditions']["{$this->alias}.Branch"] = $this->retailerId;

        return $query;
    }

    public function getBaseContain(): array
    {
        return [
            'ApiV1ManufacturerRetailer' => [
                'fields' => ['id'],
            ] + $this->ApiV1ManufacturerRetailer->getBaseContain(),
        ];
    }

    protected function processBranches($branches)
    {
        return [];
    }
}
