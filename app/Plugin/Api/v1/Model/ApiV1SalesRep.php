<?php

App::uses('ApiV1ReadWriteModel', 'Api/v1.Model');

/**
 * Class ApiV1SalesRep.
 *
 * @property ApiV1Contact $ApiV1Contact
 * @property ApiV1ContactPerson $ApiV1ContactPerson
 * @property ApiV1ManufacturerSalesRep $ApiV1ManufacturerSalesRep
 */
class ApiV1SalesRep extends ApiV1ReadWriteModel
{
    public $useTable = 'users';

    public $type = 'sales_reps';

    public $hasOne = [
        'Api/v1.ApiV1ContactPerson' => [
            'foreignKey' => 'user_id',
        ],
        'Api/v1.ApiV1Contact' => [
            'foreignKey' => 'user_id',
            'conditions' => [
                'ApiV1Contact.contact_medium' => 'telephone',
                'ApiV1Contact.type' => 'company',
            ],
            'fields' => [
                'ApiV1Contact.value',
            ],
        ],
    ];

    public function initialize(array $config)
    {
        parent::initialize($config);
        $this->bindModel([
            'hasOne' => [
                'Api/v1.ApiV1ManufacturerSalesRep' => [
                    'type' => 'INNER',
                    'foreignKey' => 'sales_rep_id',
                    'conditions' => [
                        'ApiV1ManufacturerSalesRep.user_id' => $this->authUser('id'),
                    ],
                ],
            ],
        ], false);
    }

    public function add(array $data = []): int
    {
        throw new InvalidOperationException();
    }

    public function remove(int $id)
    {
        throw new InvalidOperationException();
    }

    public function edit(int $id, array $data = [])
    {
        $existing = $this->fetchExistingId($id);
        if (!$existing) {
            throw new NotFoundException('Sales rep not found');
        }

        $this->id = $id;
        $this->validateRequestData($data);

        $existing['ApiV1Contact'] += array_intersect_key($data, array_flip($this->ApiV1Contact->getWriteFields()));
        $existing['ApiV1ContactPerson'] += array_intersect_key($data, array_flip($this->ApiV1ContactPerson->getWriteFields()));
        $existing['ApiV1ManufacturerSalesRep'] += array_intersect_key($data, array_flip($this->ApiV1ManufacturerSalesRep->getWriteFields()));
        $existing[$this->alias] += array_intersect_key($data, array_flip($this->getWriteFields()));

        if (!$this->saveAssociated($existing)) {
            throw new Exception('edit fail');
        }
    }

    public function getWriteFields(): array
    {
        return [
            'company_name',
        ];
    }

    public function getReadFields(): array
    {
        return [
            "{$this->alias}.id",
            'descriptor' => 'ApiV1ManufacturerSalesRep.descriptor',
            "{$this->alias}.company_name",
            'firstname' => 'ApiV1ContactPerson.firstname',
            'lastname' => 'ApiV1ContactPerson.lastname',
            'email' => 'ApiV1ContactPerson.email',
            'value' => 'ApiV1Contact.value',
            'is_distributor' => 'ApiV1ManufacturerSalesRep.is_distributor',
            'created_at' => 'ApiV1ManufacturerSalesRep.created_at',
            'updated_at' => 'ApiV1ManufacturerSalesRep.updated_at',
        ];
    }

    public function beforeFind($query)
    {
        $query['conditions']['ApiV1ManufacturerSalesRep.user_id'] = $this->authUser('id');

        return parent::beforeFind($query);
    }

    public function getBaseContain(): array
    {
        // Retrieve minimal fields from assoc models because actual fields used are declared in getReadFields
        return [
            'ApiV1ManufacturerSalesRep' => [
                'fields' => [$this->hasOne['ApiV1ManufacturerSalesRep']['foreignKey']],
            ] + $this->ApiV1ManufacturerSalesRep->getBaseContain(),
            'ApiV1ContactPerson' => [
                'fields' => [$this->hasOne['ApiV1ContactPerson']['foreignKey']],
            ] + $this->ApiV1ContactPerson->getBaseContain(),
            'ApiV1Contact' => [
                'fields' => [$this->hasOne['ApiV1Contact']['foreignKey']],
            ] + $this->ApiV1Contact->getBaseContain(),
        ];
    }

    private function fetchExistingId(int $id): array
    {
        $contains = $this->getBaseContain();

        $contains = array_map(function($contain) {
            $contain['fields'] = ['id'];

            return $contain;
        }, $contains);

        return $this->find('first', [
            'contain' => $contains,
            'fields' => ['id'],
            'conditions' => ["{$this->alias}.id" => $id],
            'callbacks' => 'before',
        ]);
    }
}
