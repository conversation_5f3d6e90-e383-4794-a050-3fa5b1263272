<?php

use ShipEarlyApp\Plugin\Api\v1\TestSuite\ApiV1ControllerTestCase;

App::uses('ApiV1OrdersController', 'Api/v1.Controller');
App::uses('OrderType', 'Utility');
App::uses('User', 'Model');

/**
 * ApiV1OrdersController Test Case.
 *
 * ./app/Console/cake test Api/v1 Controller/ApiV1OrdersController --stderr
 *
 * @property ApiV1OrdersController|PHPUnit_Framework_MockObject_MockObject $controller
 */
class ApiV1OrdersControllerTest extends ApiV1ControllerTestCase
{
    const API_PUBLIC_KEY = 'public_27ddf8ab53dd55ece9d5c28a748013ef';
    const API_PRIVATE_KEY = 'secret_de108dcc3046703dc2afc2429fe92439';
    const URL = '/api/v1/orders';

    const USER_ID_BRAND = 12;
    const USER_ID_RETAILER = 15;
    const PRODUCT_ID = 10723507649;
    const WAREHOUSE_ID = 3;

    public $controllerName = 'ApiV1Orders';

    public $fixtures = [
        'app.api_client',
        'app.b2b_cart',
        'app.b2b_cart_product',
        'app.b2b_ship_to_address',
        'app.b2b_shipping_rate',
        'app.b2b_shipping_rate_category',
        'app.b2b_shipping_rate_title',
        'app.b2b_shipping_zone_tier',
        'app.brand_staff_permission',
        'app.btask',
        'app.collection',
        'app.collections_product',
        'app.configuration',
        'app.contact',
        'app.contactpersons',
        'app.country',
        'app.courier',
        'app.credit_term',
        'app.customer',
        'app.dealer_order',
        'app.dealer_order_product',
        'app.dealer_order_refund',
        'app.dealer_order_refund_product',
        'app.discount',
        'app.discount_rule',
        'app.fulfillment',
        'app.fulfillment_product',
        'app.i18n',
        'app.inventory_transfer',
        'app.inventory_transfer_product',
        'app.inventory_transfer_product_reservation',
        'app.manufacturer_retailer',
        'app.manufacturer_retailer_sales_rep',
        'app.manufacturer_sales_rep',
        'app.order',
        'app.order_address',
        'app.order_comment',
        'app.order_customer_message',
        'app.order_payout',
        'app.order_product',
        'app.order_refund',
        'app.order_refund_product',
        'app.order_sales_rep',
        'app.pricing_tier',
        'app.pricing_tiers_hidden_warehouse',
        'app.product',
        'app.product_non_applicable_order_type',
        'app.product_state_fee',
        'app.product_tag',
        'app.product_tier',
        'app.product_title',
        'app.product_variant_option',
        'app.retailer_credit',
        'app.retailer_credit_term',
        'app.staff_permission',
        'app.state',
        'app.store',
        'app.tag',
        'app.territory',
        'app.user',
        'app.user_setting',
        'app.variant_option',
        'app.warehouse',
        'app.warehouse_product',
        'app.warehouse_product_reservation',
    ];

    public function setUp()
    {
        parent::setUp();
        $this->generate("{$this->plugin}.{$this->controllerName}", [
            'components' => [
                /* @see OrderPlacerComponent::createEcommerceDealerOrder() */
                'OrderPlacer' => ['createEcommerceDealerOrder'],
            ],
        ]);

        $this->controller->Auth->unauthorizedRedirect = false;
        $this->configureApiRequest(static::API_PUBLIC_KEY, static::API_PRIVATE_KEY);

        // Temporarily set authUser('id') while the model mock initializes virtual fields that require it.
        $originalSessionKey = AuthComponent::$sessionKey;
        AuthComponent::$sessionKey = false;
        $this->controller->Auth->login(['User' => ['id' => static::USER_ID_BRAND, 'user_type' => User::TYPE_MANUFACTURER]]);

        try {
            /**
             * @var ApiV1Order|PHPUnit_Framework_MockObject_MockObject $mock
             * @see ApiV1Order::_findGeocode()
             */
            $this->controller->ApiV1Order = $this->getMockForModel("{$this->plugin}.ApiV1Order", ['_findGeocode']);
        } finally {
            $this->controller->Auth->logout();
            AuthComponent::$sessionKey = $originalSessionKey;
        }
    }

    /**
     * @dataProvider addProvider
     * @covers ApiV1OrdersController::add
     */
    public function testAdd($requestData, $expectedStatus)
    {
        $expectedShippingLine = [
            'service' => ($expectedStatus === 'Processing') ? ($requestData['shipping_service'] ?? null) : null,
            'price' => '25.00',
            'discount' => '0.00',
            'discounted_price' => '25.00',
            'total_tax' => '0.00',
        ];
        $expected = [
            'data' => [
                'retailer_id' => '15',
                'credit_term_id' => null,
                'id' => '__IGNORED__',
                'order_type' => 'wholesale',
                'order_status' => 'Purchase Order',
                'internal_order_number' => null,
                'ecommerce_id' => null,
                'order_number' => '#SE0018645',
                'purchase_order_number' => null,
                'discount_code' => '',
                'verification_code' => '',
                'risk_level' => 'not_assessed',
                'requested_ship_date' => null,
                'shipped_date' => null,
                'delivery_date' => null,
                'fulfillment_status' => 'unfulfilled',
                'payment_method' => 'external',
                'payment_status' => 'On Hold',
                'currency_code' => 'CAD',
                'total_discount' => '0.00',
                'total_price' => '3710.91',
                'total_shipping' => '25.00',
                'total_tax' => '175.33',
                'created_at' => '__IGNORED__',
                'updated_at' => '__IGNORED__',
                'shipping_lines' => [
                    0 => $expectedShippingLine,
                ],
                'order_items' => [
                    0 => [
                        'id' => '__IGNORED__',
                        'order_id' => '__IGNORED__',
                        'warehouse_id' => '3',
                        'quantity' => '10',
                        'total_discount' => '0.00',
                        'total_price' => '3499.90',
                        'inventory_transfer_id' => null,
                        'created_at' => '__IGNORED__',
                        'updated_at' => '__IGNORED__',
                        'variant_id' => '10723507649',
                        'product_id' => '4',
                        'sku' => 'BICYCLE',
                        'upc' => '9090909090909',
                        'title' => 'Bicycle',
                        'price' => '349.99',
                        'subtotal' => '3499.90',
                        'quantity_to_fulfill' => '10',
                        'estimated_ship_date' => null,
                    ],
                    1 => [
                        'id' => '__IGNORED__',
                        'order_id' => '__IGNORED__',
                        'warehouse_id' => '3',
                        'quantity' => '2',
                        'total_discount' => '0.00',
                        'total_price' => '0.00',
                        'inventory_transfer_id' => null,
                        'created_at' => '__IGNORED__',
                        'updated_at' => '__IGNORED__',
                        'variant_id' => '27557890305',
                        'product_id' => '15',
                        'sku' => 'VARIANT-1',
                        'upc' => '',
                        'title' => 'Variant - Red',
                        'price' => '0.00',
                        'subtotal' => '0.00',
                        'quantity_to_fulfill' => '2',
                        'estimated_ship_date' => null,
                    ],
                    2 => [
                        'id' => '__IGNORED__',
                        'order_id' => '__IGNORED__',
                        'warehouse_id' => '3',
                        'quantity' => '2',
                        'total_discount' => '0.00',
                        'total_price' => '0.00',
                        'inventory_transfer_id' => null,
                        'created_at' => '__IGNORED__',
                        'updated_at' => '__IGNORED__',
                        'variant_id' => '27557890369',
                        'product_id' => '15',
                        'sku' => 'VARIANT-2',
                        'upc' => '',
                        'title' => 'Variant - Blue',
                        'price' => '0.00',
                        'subtotal' => '0.00',
                        'quantity_to_fulfill' => '2',
                        'estimated_ship_date' => null,
                    ],
                    3 => [
                        'id' => '__IGNORED__',
                        'order_id' => '__IGNORED__',
                        'warehouse_id' => '3',
                        'quantity' => '4',
                        'total_discount' => '0.00',
                        'total_price' => '10.68',
                        'inventory_transfer_id' => null,
                        'created_at' => '__IGNORED__',
                        'updated_at' => '__IGNORED__',
                        'variant_id' => '33363926057005',
                        'product_id' => '30',
                        'sku' => 'ANCILLARY-FEES',
                        'upc' => '',
                        'title' => 'Ancillary Fees',
                        'price' => '2.67',
                        'subtotal' => '10.68',
                        'quantity_to_fulfill' => '4',
                        'estimated_ship_date' => null,
                    ],
                ],
                'fulfillments' => [
                ],
                'refunds' => [
                ],
                'retailer' => [
                    'id' => '15',
                    'company_name' => 'Sirisha Test Retailer',
                    'city' => 'Thunder Bay',
                    'zip_code' => 'P7B 1P4',
                    'latitude' => '48.433721',
                    'longitude' => '-89.22996',
                    'address1' => '333',
                    'address2' => 'Lark Street',
                    'state_code' => 'ON',
                    'country_code' => 'CA',
                    'has_active_inventory_integration' => false,
                    'account_id' => 'TEST_RETAILER',
                    'pricing_tier_id' => '5',
                    'billing_company' => null,
                    'billing_email' => null,
                    'dealer_protect_radius' => '0',
                    'local_delivery_radius' => '100',
                    'credit_limit' => '10000.00',
                    'enable_credit_card' => true,
                    'enable_on_file_payment' => true,
                    'is_installer' => false,
                    'ship_from_store_distance' => '0',
                    'is_non_stocking' => false,
                    'b2b_minimum_order_value' => '0.00',
                    'default_warehouse_id' => null,
                    'created_at' => '2016-05-02 19:34:43',
                    'updated_at' => '2018-04-30 09:49:05',
                    'status' => 'Connected',
                    'territory' => null,
                    'b2b_tax_rate' => '0.050000',
                    'sales_reps' => [
                        0 => '21',
                    ],
                    'credit_terms' => [
                        0 => '36',
                    ],
                    'branches' => [
                    ],
                ],
                'customer' => null,
                'dealer_order' => null,
                'credit_term' => null,
                'billing_address' => [
                    'first_name' => '',
                    'last_name' => '',
                    'company_name' => 'Sirisha Test Retailer',
                    'address1' => '333',
                    'address2' => 'Lark Street',
                    'city' => 'Thunder Bay',
                    'state' => 'ON',
                    'country' => 'CA',
                    'zip_code' => 'P7B 1P4',
                    'phone' => '6132972873',
                    'latitude' => '48.4340872',
                    'longitude' => '-89.2307255',
                ],
                'shipping_address' => [
                    'first_name' => '',
                    'last_name' => '',
                    'company_name' => 'Sirisha Test Retailer',
                    'address1' => '333',
                    'address2' => 'Lark Street',
                    'city' => 'Thunder Bay',
                    'state' => 'on',
                    'country' => 'ca',
                    'zip_code' => 'P7B 1P4',
                    'phone' => '6132972873',
                    'latitude' => '48.4340872',
                    'longitude' => '-89.2307255',
                ],
                'subtotal' => '3510.58',
                'total_refund' => '0.00',
                'net_total' => '3710.91',
            ],
        ];
        if ($expectedStatus === 'Processing') {
            $expected['data'] = array_merge($expected['data'], [
                'order_status' => 'Processing',
                'dealer_order' => [
                    'id' => '__IGNORED__',
                    'subtotal' => '3510.58',
                    'total_price' => '3710.91',
                    'total_discount' => '0.00',
                    'total_tax' => '175.33',
                    'total_shipping' => '25.00',
                    'fulfillment_status' => 'unfulfilled',
                    'created_at' => '__IGNORED__',
                    'updated_at' => '__IGNORED__',
                    'shipping_lines' => [
                        0 => $expectedShippingLine,
                    ],
                    'order_items' => [
                        0 => [
                            'id' => '__IGNORED__',
                            'order_id' => '__IGNORED__',
                            'warehouse_id' => '3',
                            'quantity' => '10',
                            'total_discount' => '0.00',
                            'price' => '349.99',
                            'inventory_transfer_id' => null,
                            'created_at' => '__IGNORED__',
                            'updated_at' => '__IGNORED__',
                            'variant_id' => '10723507649',
                            'product_id' => '4',
                            'sku' => 'BICYCLE',
                            'upc' => '9090909090909',
                            'title' => 'Bicycle',
                            'total_price' => '3499.90',
                            'subtotal' => '3499.90',
                            'quantity_to_fulfill' => '10',
                            'estimated_ship_date' => null,
                        ],
                        1 => [
                            'id' => '__IGNORED__',
                            'order_id' => '__IGNORED__',
                            'warehouse_id' => '3',
                            'quantity' => '2',
                            'total_discount' => '0.00',
                            'price' => '0.00',
                            'inventory_transfer_id' => null,
                            'created_at' => '__IGNORED__',
                            'updated_at' => '__IGNORED__',
                            'variant_id' => '27557890305',
                            'product_id' => '15',
                            'sku' => 'VARIANT-1',
                            'upc' => '',
                            'title' => 'Variant - Red',
                            'total_price' => '0.00',
                            'subtotal' => '0.00',
                            'quantity_to_fulfill' => '2',
                            'estimated_ship_date' => null,
                        ],
                        2 => [
                            'id' => '__IGNORED__',
                            'order_id' => '__IGNORED__',
                            'warehouse_id' => '3',
                            'quantity' => '2',
                            'total_discount' => '0.00',
                            'price' => '0.00',
                            'inventory_transfer_id' => null,
                            'created_at' => '__IGNORED__',
                            'updated_at' => '__IGNORED__',
                            'variant_id' => '27557890369',
                            'product_id' => '15',
                            'sku' => 'VARIANT-2',
                            'upc' => '',
                            'title' => 'Variant - Blue',
                            'total_price' => '0.00',
                            'subtotal' => '0.00',
                            'quantity_to_fulfill' => '2',
                            'estimated_ship_date' => null,
                        ],
                        3 => [
                            'id' => '__IGNORED__',
                            'order_id' => '__IGNORED__',
                            'warehouse_id' => '3',
                            'quantity' => '4',
                            'total_discount' => '0.00',
                            'price' => '2.67',
                            'inventory_transfer_id' => null,
                            'created_at' => '__IGNORED__',
                            'updated_at' => '__IGNORED__',
                            'variant_id' => '33363926057005',
                            'product_id' => '30',
                            'sku' => 'ANCILLARY-FEES',
                            'upc' => '',
                            'title' => 'Ancillary Fees',
                            'total_price' => '10.68',
                            'subtotal' => '10.68',
                            'quantity_to_fulfill' => '4',
                            'estimated_ship_date' => null,
                        ],
                    ],
                    'fulfillments' => [
                    ],
                    'refunds' => [
                    ],
                    'total_refund' => '0.00',
                    'net_total' => '3710.91',
                ],
            ]);
        }

        /** @var State $State */
        $State = ClassRegistry::init('State');
        $state = $State->findStateAndCountryByCode($expected['data']['shipping_address']['state'], $expected['data']['shipping_address']['country']);
        /** @var ApiV1Order|PHPUnit_Framework_MockObject_MockObject $ApiV1Order */
        $ApiV1Order = $this->controller->ApiV1Order;
        $ApiV1Order->expects($this->once())->method('_findGeocode')->with(
            $expected['data']['shipping_address']['address1'],
            $expected['data']['shipping_address']['city'],
            $expected['data']['shipping_address']['zip_code'],
            $state['State']['state_name'],
            $state['Country']['country_name']
        )->willReturn([
            'lat' => $expected['data']['shipping_address']['latitude'],
            'lng' => $expected['data']['shipping_address']['longitude'],
        ]);

        /** @var OrderPlacerComponent|PHPUnit_Framework_MockObject_MockObject $OrderPlacer */
        $OrderPlacer = $this->controller->OrderPlacer;
        $OrderPlacer->expects(($requestData['confirm_pricing'] ?? true) ? $this->once() : $this->never())->method('createEcommerceDealerOrder')->willReturn(true);

        $this->post(static::URL, $requestData);

        $this->assertResponseOk();
        $actualJson = $this->_response->body();
        $expectedSchema = $this->getResponseSchema('Order');
        $this->assertStringMatchesJsonSchema($expectedSchema, $actualJson);

        $actual = json_decode($actualJson, true);
        $expected = $this->assignIgnoredExpectedFieldsFromActual($expected, $actual);
        $this->assertEquals($expected, $actual);
    }

    public function addProvider()
    {
        $baseRequest = [
            'retailer_id' => static::USER_ID_RETAILER,
            'order_type' => OrderType::WHOLESALE,
            'order_items' => [
                [
                    'variant_id' => static::PRODUCT_ID,
                    'warehouse_id' => static::WAREHOUSE_ID,
                    'quantity' => 10,
                ],
                // Exercise ProductStateFee
                [
                    'variant_id' => '27557890305',
                    'warehouse_id' => static::WAREHOUSE_ID,
                    'quantity' => 2,
                ],
                [
                    'variant_id' => '27557890369',
                    'warehouse_id' => static::WAREHOUSE_ID,
                    'quantity' => 2,
                ],
            ],
        ];

        return [
            'Do Confirm' => [
                'requestData' => ['confirm_pricing' => true] + $baseRequest,
                'expectedStatus' => 'Processing',
            ],
            'Do Confirm with shipping_service' => [
                'requestData' => ['confirm_pricing' => true, 'shipping_service' => 'Domestic Price Rate'] + $baseRequest,
                'expectedStatus' => 'Processing',
            ],
            'Do Not Confirm' => [
                'requestData' => ['confirm_pricing' => false, 'shipping_service' => 'Should be ignored'] + $baseRequest,
                'expectedStatus' => 'Purchase Order',
            ],
            'Omit Confirm' => [
                'requestData' => $baseRequest,
                'expectedStatus' => 'Processing',
            ],
        ];
    }
}
