<?php

use Ship<PERSON>arlyApp\Plugin\Api\v1\Test\Provider\ApiV1ProductTiersControllerProvider;
use ShipEarlyApp\Plugin\Api\v1\TestSuite\ApiV1ControllerTestCase;

App::uses('ApiV1ProductTiersController', 'Api/v1.Controller');
App::uses('Hash', 'Utility');

/**
 * ApiV1ProductTiersController Test Case.
 *
 * ./app/Console/cake test Api/v1 Controller/ApiV1ProductTiersController --stderr
 *
 * @property ApiV1ProductTiersController|PHPUnit_Framework_MockObject_MockObject $controller
 */
class ApiV1ProductTiersControllerTest extends ApiV1ControllerTestCase
{
    const API_PUBLIC_KEY = 'public_27ddf8ab53dd55ece9d5c28a748013ef';
    const API_PRIVATE_KEY = 'secret_de108dcc3046703dc2afc2429fe92439';
    const USER_ID_BRAND = 12;

    public $fixtures = [
        'app.api_client',
        'app.configuration',
        'app.i18n',
        'app.pricing_tier',
        'app.product',
        'app.product_non_applicable_order_type',
        'app.product_state_fee',
        'app.product_tier',
        'app.product_title',
        'app.product_variant_option',
        'app.store',
        'app.user',
    ];

    protected static function getBaseUrl(?int $variantId = null): string
    {
        return '/api/v1/variants/' . ($variantId ?? ApiV1ProductTiersControllerProvider::DEFAULT_VARIANT_ID) . '/pricing_tiers';
    }

    public function setUp()
    {
        parent::setUp();

        $this->generate('Api/v1.ApiV1ProductTiers');

        $this->controller->Auth->unauthorizedRedirect = false;
        $this->configureApiRequest(static::API_PUBLIC_KEY, static::API_PRIVATE_KEY);
    }

    /**
     * @dataProvider providerIndex()
     */
    public function testIndex(int $variantId, $expected)
    {
        if ($expected instanceof Exception) {
            $this->setExpectedException(get_class($expected), $expected->getMessage(), $expected->getCode());
        }

        $this->get(static::getBaseUrl($variantId));

        if ($expected instanceof Exception) {
            return;
        }
        $this->assertResponseOk();

        $actualJson = $this->_response->body();
        $expectedSchema = $this->getResponseSchema('VariantTierList');
        $this->assertStringMatchesJsonSchema($expectedSchema, $actualJson);

        $actual = json_decode($actualJson, true);
        // An exact comparison is effective but brittle
        //$this->assertEquals($expected, $actual);
        // Instead, make assertions about the known subset of data
        $this->assertGreaterThanOrEqual(count($expected['data']), count($actual['data']), 'Number of records is less than expected');
        $expectedSample = Hash::combine($expected['data'], '{n}.id', '{n}');
        $actualSample = array_intersect_key(Hash::combine($actual['data'], '{n}.id', '{n}'), $expectedSample);
        $this->assertEquals($expectedSample, $actualSample, 'Sample records do not match');
    }

    public function providerIndex(): array
    {
        return ApiV1ProductTiersControllerProvider::testIndex();
    }

    public function testCount()
    {
        $this->setExpectedInvalidOperationException();
        $this->get(static::getBaseUrl() . '/count');
    }

    /**
     * @dataProvider providerGet()
     */
    public function testGet(int $variantId, int $pricingTierId, $expected)
    {
        if ($expected instanceof Exception) {
            $this->setExpectedException(get_class($expected), $expected->getMessage(), $expected->getCode());
        }

        $this->get(static::getBaseUrl($variantId) . '/' . $pricingTierId);

        if ($expected instanceof Exception) {
            return;
        }
        $this->assertResponseOk();

        $actualJson = $this->_response->body();
        $expectedSchema = $this->getResponseSchema('VariantTier');
        $this->assertStringMatchesJsonSchema($expectedSchema, $actualJson);

        $actual = json_decode($actualJson, true);
        $this->assertEquals($expected, $actual);
        $this->assertEquals(array_keys($expected['data']), array_keys($actual['data']), 'Fields not in same order');
    }

    public function providerGet(): array
    {
        return ApiV1ProductTiersControllerProvider::testGet();
    }

    /**
     * @dataProvider providerPost()
     */
    public function testPost(int $variantId, array $data, $expected)
    {
        if ($expected instanceof Exception) {
            $this->setExpectedException(get_class($expected), $expected->getMessage(), $expected->getCode());
        }

        $this->post(static::getBaseUrl($variantId), $data);

        if ($expected instanceof Exception) {
            return;
        }
        $this->assertResponseOk();

        $actualJson = $this->_response->body();
        $expectedSchema = $this->getResponseSchema('VariantTier');
        $this->assertStringMatchesJsonSchema($expectedSchema, $actualJson);

        $actual = json_decode($actualJson, true);
        $expected['data']['created_at'] = $actual['data']['created_at'];
        $expected['data']['updated_at'] = $actual['data']['updated_at'];
        $this->assertEquals($expected, $actual);
    }

    public function providerPost(): array
    {
        return ApiV1ProductTiersControllerProvider::testPost();
    }

    /**
     * @dataProvider providerPut()
     */
    public function testPut(int $variantId, int $pricingTierId, array $data, $expected)
    {
        if ($expected instanceof Exception) {
            $this->setExpectedException(get_class($expected), $expected->getMessage(), $expected->getCode());
        }

        $this->put(static::getBaseUrl($variantId) . '/' . $pricingTierId, $data);

        if ($expected instanceof Exception) {
            return;
        }
        $this->assertResponseOk();

        $actualJson = $this->_response->body();
        $expectedSchema = $this->getResponseSchema('VariantTier');
        $this->assertStringMatchesJsonSchema($expectedSchema, $actualJson);

        $actual = json_decode($actualJson, true);
        $expected['data']['updated_at'] = $actual['data']['updated_at'];
        $this->assertEquals($expected, $actual);
    }

    public function providerPut(): array
    {
        return ApiV1ProductTiersControllerProvider::testPut();
    }

    public function testDelete()
    {
        $this->setExpectedInvalidOperationException();
        $this->delete(static::getBaseUrl() . '/' . ApiV1ProductTiersControllerProvider::DEFAULT_PRICING_TIER_ID);
    }

    public function testOptions()
    {
        $this->options(static::getBaseUrl() . '/' . ApiV1ProductTiersControllerProvider::DEFAULT_PRICING_TIER_ID);
        $this->assertResponseCode(204);
    }
}
