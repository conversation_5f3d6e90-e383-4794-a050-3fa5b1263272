<?php

use Ship<PERSON>arlyApp\Plugin\Api\v1\Test\Provider\ApiV1ProductsControllerProvider;
use ShipEarlyApp\Plugin\Api\v1\TestSuite\ApiV1ControllerTestCase;
use ShipEarlyApp\Plugin\Api\v1\TestSuite\ApiV1ProductsTestHelperTrait;

App::uses('ApiV1ProductsController', 'Api/v1.Controller');
App::uses('Hash', 'Utility');

/**
 * ApiV1ProductsController Test Case.
 *
 * ./app/Console/cake test Api/v1 Controller/ApiV1ProductsController --stderr
 *
 * @property ApiV1ProductsController|PHPUnit_Framework_MockObject_MockObject $controller
 */
class ApiV1ProductsControllerTest extends ApiV1ControllerTestCase
{
    use ApiV1ProductsTestHelperTrait;

    const API_PUBLIC_KEY = 'public_27ddf8ab53dd55ece9d5c28a748013ef';
    const API_PRIVATE_KEY = 'secret_de108dcc3046703dc2afc2429fe92439';
    const USER_ID_BRAND = 12;

    public $fixtures = [
        'app.api_client',
        'app.configuration',
        'app.pricing_tier',
        'app.product',
        'app.product_non_applicable_order_type',
        'app.product_price',
        'app.product_state_fee',
        'app.product_tier',
        'app.product_title',
        'app.product_variant_option',
        'app.store',
        'app.user',
        'app.variant_option',
        'app.warehouse_product',
    ];

    public function setUp()
    {
        parent::setUp();

        $this->generate('Api/v1.ApiV1Products');

        $this->controller->Auth->unauthorizedRedirect = false;
        $this->configureApiRequest(static::API_PUBLIC_KEY, static::API_PRIVATE_KEY);

        $this->seedProductsCreatedByShipEarly();
        $this->seedLegacySellDirectInStockOnlyVariant();
    }

    /**
     * @dataProvider providerIndex()
     */
    public function testIndex(array $expected)
    {
        $this->get('/api/v1/products');
        $this->assertResponseOk();

        $actualJson = $this->_response->body();
        $expectedSchema = $this->getResponseSchema('ProductList');
        $this->assertStringMatchesJsonSchema($expectedSchema, $actualJson);

        $actual = json_decode($actualJson, true);
        // An exact comparison is effective but brittle
        //$this->assertEquals($expected, $actual);
        // Instead, make assertions about the known subset of data
        $this->assertGreaterThanOrEqual(count($expected['data']), count($actual['data']), 'Number of records is less than expected');
        $expectedSample = Hash::combine($expected['data'], '{n}.id', '{n}');
        $actualSample = array_intersect_key(Hash::combine($actual['data'], '{n}.id', '{n}'), $expectedSample);
        $this->assertEquals($expectedSample, $actualSample, 'Sample records do not match');
    }

    public function providerIndex(): array
    {
        return [
            'default' => [
                'expected' => [
                    'data' => array_map(
                        fn(array $case): array => $case['expected']['data'],
                        array_values(ApiV1ProductsControllerProvider::testGet())
                    ),
                ],
            ],
        ];
    }

    /**
     * @dataProvider providerCount()
     */
    public function testCount(array $expected)
    {
        $this->get('/api/v1/products/count');
        $this->assertResponseOk();

        $actualJson = $this->_response->body();
        $expectedSchema = $this->getResponseSchema('Count');
        $this->assertStringMatchesJsonSchema($expectedSchema, $actualJson);

        $actual = json_decode($actualJson, true);
        $this->assertGreaterThanOrEqual($expected['count'], $actual['count'], 'Number of records is less than expected');
    }

    public function providerCount(): array
    {
        return array_map(function(array $case): array {
            $case['expected']['count'] = count($case['expected']['data']);
            unset($case['expected']['data']);

            return $case;
        }, $this->providerIndex());
    }

    /**
     * @dataProvider providerGet()
     */
    public function testGet(int $id, array $expected)
    {
        $this->get('/api/v1/products/' . $id);
        $this->assertResponseOk();

        $actualJson = $this->_response->body();
        $expectedSchema = $this->getResponseSchema('Product');
        $this->assertStringMatchesJsonSchema($expectedSchema, $actualJson);

        $actual = json_decode($actualJson, true);
        $this->assertEquals($expected, $actual);
        $this->assertEquals(array_keys($expected['data']), array_keys($actual['data']), 'Fields not in same order');
    }

    public function providerGet(): array
    {
        return ApiV1ProductsControllerProvider::testGet();
    }

    public function testPost()
    {
        $this->setExpectedInvalidOperationException();
        $this->post('/api/v1/products');
    }

    /**
     * @dataProvider providerPut()
     */
    public function testPut($id, $editData, $expected)
    {
        if ($expected instanceof Exception) {
            $this->setExpectedException(get_class($expected), $expected->getMessage(), $expected->getCode());
        }

        $this->put('/api/v1/products/' . $id, $editData);

        if ($expected instanceof Exception) {
            return;
        }

        $this->assertResponseOk();
        $actualJson = $this->_response->body();
        $expectedSchema = $this->getResponseSchema('Product');
        $this->assertStringMatchesJsonSchema($expectedSchema, $actualJson);

        $actual = json_decode($actualJson, true);
        $expected = $this->assignIgnoredExpectedFieldsFromActual($expected, $actual);
        $this->assertEquals($expected, $actual);
    }

    public function providerPut(): array
    {
        return ApiV1ProductsControllerProvider::testPut();
    }

    public function testPutEmptyNullableStringBecomesNull()
    {
        $id = ApiV1ProductsControllerProvider::testGet()['Bicycle']['expected']['data']['id'];
        $nullableStringFields = [
            'description_html' => '',
        ];

        $this->put('/api/v1/products/' . $id, $nullableStringFields);

        $this->assertResponseOk();
        $actualJson = $this->_response->body();
        $expectedSchema = $this->getResponseSchema('Product');
        $this->assertStringMatchesJsonSchema($expectedSchema, $actualJson);

        $actual = array_intersect_key(json_decode($actualJson, true)['data'], $nullableStringFields);
        $expected = array_fill_keys(array_keys($nullableStringFields), null);
        $this->assertSame($expected, $actual);
    }

    public function testDelete()
    {
        $this->setExpectedInvalidOperationException();
        $this->delete('/api/v1/products/' . 4);
    }

    public function testOptions()
    {
        $this->options('/api/v1/products/' . 4);
        $this->assertResponseCode(204);
    }

    /**
     * @dataProvider providerDeprecatedIdCases()
     */
    public function testDeprecatedIdCases(string $method, int $deprecatedId, string $expectedWarning)
    {
        $deprecationWarning = '';
        set_error_handler(function($code, $message) use (&$deprecationWarning) {
            $deprecationWarning = $message;
        });

        try {
            $this->_sendRequest('/api/v1/products/' . $deprecatedId, $method);
            $this->assertResponseOk();
        } catch (InvalidOperationException $e) {
            $this->assertEmpty($this->_response ?? null, 'Response is not empty');
        } finally {
            restore_error_handler();
        }

        $this->assertStringStartsWith($expectedWarning, $deprecationWarning, 'Deprecation warning does not match');
    }

    public function providerDeprecatedIdCases(): array
    {
        $deprecatedId = **********;
        $id = 4;
        $expectedWarning = "Product referenced by source_id={$deprecatedId} is deprecated. Reference by id={$id} instead.";

        return [
            'GET' => [
                'method' => 'GET',
                'deprecatedId' => $deprecatedId,
                'expectedWarning' => $expectedWarning,
            ],
            'PUT' => [
                'method' => 'PUT',
                'deprecatedId' => $deprecatedId,
                'expectedWarning' => $expectedWarning,
            ],
            'DELETE' => [
                'method' => 'DELETE',
                'deprecatedId' => $deprecatedId,
                'expectedWarning' => $expectedWarning,
            ],
            'OPTIONS' => [
                'method' => 'OPTIONS',
                'deprecatedId' => $deprecatedId,
                'expectedWarning' => $expectedWarning,
            ],
        ];
    }
}
