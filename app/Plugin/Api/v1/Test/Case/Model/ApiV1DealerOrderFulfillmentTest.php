<?php

use ShipearlyTests\Util\Setup;

App::uses('ApiV1DealerOrderFulfillment', 'Api/v1.Model');
App::uses('ApiV1ModelTestCase', 'Api/v1.Test/Case/Model');
App::uses('DealerOrderFulfillmentProvider', 'Api/v1.Test/Provider');

/**
 * ApiV1DealerOrderFulfillment Test Case
 *
 * ./app/Console/cake test Api/v1 Model/ApiV1DealerOrderFulfillment
 *
 * @property ApiV1DealerOrderFulfillment $ApiV1DealerOrderFulfillment
 */
class ApiV1DealerOrderFulfillmentTest extends ApiV1ModelTestCase
{
    public $userId = 12;
    public $testModel = 'ApiV1DealerOrderFulfillment';
    public $orderId = 7;

    /**
     * Fixtures
     *
     * @var array
     */
    public $fixtures = [
        'app.collections_product',
        'app.courier',
        'app.dealer_order',
        'app.dealer_order_product',
        'app.dealer_order_refund',
        'app.dealer_order_refund_product',
        'app.fulfillment',
        'app.fulfillment_product',
        'app.i18n',
        'app.inventory_transfer',
        'app.order',
        'app.order_comment',
        'app.order_customer_message',
        'app.order_product',
        'app.order_refund',
        'app.order_refund_product',
        'app.product',
        'app.product_non_applicable_order_type',
        'app.product_state_fee',
        'app.product_tag',
        'app.product_title',
        'app.product_variant_option',
        'app.store',
        'app.user',
        'app.warehouse',
    ];

    /**
     * setUp method
     *
     * @return void
     */
    public function setUp()
    {
        parent::setUp();
        $this->{$this->testModel}->orderId = $this->orderId;
    }

    /**
     * @dataProvider DealerOrderFulfillmentProvider::addProvider
     */
    public function testAdd($orderId, $addData, $dirtyFields, $expected)
    {
        $this->ApiV1DealerOrderFulfillment->orderId = $orderId;
        $addData += $dirtyFields;
        if (is_a($expected, Exception::class)) {
            $this->expectException(get_class($expected), $expected->getMessage());
            $this->ApiV1DealerOrderFulfillment->add($addData);
        } else {
            $fulfillmentId = $this->ApiV1DealerOrderFulfillment->add($addData);
            $expected['id'] = (string)$fulfillmentId;
            $actual = $this->ApiV1DealerOrderFulfillment->view($fulfillmentId);
            $this->removeTimestampFields($actual);
            $this->assertEquals($expected, $actual);
        }
    }

    public function testEdit()
    {
        $this->expectException(InvalidOperationException::class, 'Invalid Operation');

        $this->ApiV1DealerOrderFulfillment->edit(0, []);
    }

    public function testRemove()
    {
        $this->expectException(InvalidOperationException::class, 'Invalid Operation');

        $this->ApiV1DealerOrderFulfillment->remove(0);
    }

    public function testCount()
    {
        $expected = 1;

        $actual = $this->ApiV1DealerOrderFulfillment->count();
        $this->assertEquals($expected, $actual);
    }

    /**
     * @dataProvider DealerOrderFulfillmentProvider::indexProvider
     */
    public function testIndex($orderId, $expected)
    {
        $query = [
            'order' => 'id',
            'limit' => 10,
        ];
        $this->ApiV1DealerOrderFulfillment->orderId = $orderId;
        $actual = $this->ApiV1DealerOrderFulfillment->index([$this->ApiV1DealerOrderFulfillment->alias => $query]);
        $this->removeTimestampFields($actual);
        $this->assertEquals($expected, $actual);
    }

    /**
     * @dataProvider DealerOrderFulfillmentProvider::viewProvider
     */
    public function testView($fulfillmentId, $orderId, $expected)
    {
        $this->ApiV1DealerOrderFulfillment->orderId = $orderId;
        $actual = $this->ApiV1DealerOrderFulfillment->view($fulfillmentId);
        $this->removeTimestampFields($actual);
        $this->assertEquals($expected, $actual);
    }

    public function testIndexContainsTimestamps()
    {
        $query = [
            'order' => 'id',
            'limit' => 10,
        ];
        $actual = $this->ApiV1DealerOrderFulfillment->index([$this->ApiV1DealerOrderFulfillment->alias => $query]);
        $this->assertArrayHasKey('created_at', current($actual));
    }

    public function testViewContainsTimestamps()
    {
        $actual = $this->ApiV1DealerOrderFulfillment->view(13);
        $this->assertArrayHasKey('created_at', $actual);
    }
}
