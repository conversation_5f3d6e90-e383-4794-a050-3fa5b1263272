<?php

App::uses('ApiV1RetailInventory', 'Api/v1.Model');
App::uses('ApiV1ModelTestCase', 'Api/v1.Test/Case/Model');
App::uses('InvalidOperationException', 'Error');
App::uses('User', 'Model');

/**
 * ApiV1RetailInventory Test Case
 *
 * ./app/Console/cake test Api/v1 Model/ApiV1RetailInventory
 *
 * @property ApiV1RetailInventory $ApiV1RetailInventory
 */
class ApiV1RetailInventoryTest extends ApiV1ModelTestCase
{
    public $userId = 12;

    public $testModel = 'ApiV1RetailInventory';

    public $fixtures = [
        'app.i18n',
        'app.manufacturer_retailer',
        'app.product',
        'app.product_non_applicable_order_type',
        'app.product_retailer',
        'app.product_state_fee',
        'app.product_title',
        'app.product_variant_option',
        'app.store',
        'app.user',
    ];

    public function testBeforeFind()
    {
        $expected = [
            'conditions' => [
                'ApiV1Variant.user_id' => $this->userId,
                'ApiV1Variant.product_title_id !=' => null,
                'ApiV1Variant.deleted' => false,
            ],
        ];
        $actual = $this->ApiV1RetailInventory->beforeFind([]);
        $this->removeTimestampFields($actual);
        $this->assertEquals($expected, $actual);
    }

    public function testIndex()
    {
        $expected = [
            0 => [
                'manufacturer' => 'Sirisha Test Brand',
                'variant_id' => '11130571329',
                'storeId' => '15',
                'productId' => '29',
                'inventoryCount' => '0',
                'product_title' => 'No Stock',
                'product_upc' => '0101010101010',
                'product_sku' => 'NO-STOCK',
                'company_name' => 'Sirisha Test Retailer',
            ],
            1 => [
                'manufacturer' => 'Sirisha Test Brand',
                'variant_id' => '10723507649',
                'storeId' => '15',
                'productId' => '25',
                'inventoryCount' => '708',
                'product_title' => 'Bicycle',
                'product_upc' => '9090909090909',
                'product_sku' => 'BICYCLE',
                'company_name' => 'Sirisha Test Retailer',
            ],
            2 => [
                'manufacturer' => 'Sirisha Test Brand',
                'variant_id' => '16885145025',
                'storeId' => '15',
                'productId' => '26',
                'inventoryCount' => '1956',
                'product_title' => 'In-Stock Dealer Protect',
                'product_upc' => '0202020202020',
                'product_sku' => 'IN-STOCK-DEALER-PROTECT-1',
                'company_name' => 'Sirisha Test Retailer',
            ],
            3 => [
                'manufacturer' => 'Sirisha Test Brand',
                'variant_id' => '16885170497',
                'storeId' => '15',
                'productId' => '27',
                'inventoryCount' => '1999',
                'product_title' => 'In-Stock Dealer Protect 2',
                'product_upc' => '1212121212121',
                'product_sku' => 'IN-STOCK-DEALER-PROTECT-2',
                'company_name' => 'Sirisha Test Retailer',
            ],
            4 => [
                'manufacturer' => 'Sirisha Test Brand',
                'variant_id' => '10723515137',
                'storeId' => '15',
                'productId' => '24',
                'inventoryCount' => '1951',
                'product_title' => 'Always Sell Direct',
                'product_upc' => '0303030303030',
                'product_sku' => 'ALWAYS-SELL-DIRECT',
                'company_name' => 'Sirisha Test Retailer',
            ],
            5 => [
                'manufacturer' => 'Sirisha Test Brand',
                'variant_id' => '16885440705',
                'storeId' => '15',
                'productId' => '33',
                'inventoryCount' => '969',
                'product_title' => 'Sell Direct Unless Bundled',
                'product_upc' => '0404040404040',
                'product_sku' => 'SELL-DIRECT-UNLESS-BUNDLED',
                'company_name' => 'Sirisha Test Retailer',
            ],
            6 => [
                'manufacturer' => 'Sirisha Test Brand',
                'variant_id' => '16885353665',
                'storeId' => '15',
                'productId' => '31',
                'inventoryCount' => '899',
                'product_title' => 'In-Stock Only',
                'product_upc' => '0505050505050',
                'product_sku' => 'IN-STOCK-ONLY',
                'company_name' => 'Sirisha Test Retailer',
            ],
            7 => [
                'manufacturer' => 'Sirisha Test Brand',
                'variant_id' => '11130571329',
                'storeId' => '16',
                'productId' => '29',
                'inventoryCount' => '0',
                'product_title' => 'No Stock',
                'product_upc' => '0101010101010',
                'product_sku' => 'NO-STOCK',
                'company_name' => 'Test New Retailer',
            ],
            8 => [
                'manufacturer' => 'Sirisha Test Brand',
                'variant_id' => '10723507649',
                'storeId' => '16',
                'productId' => '25',
                'inventoryCount' => '708',
                'product_title' => 'Bicycle',
                'product_upc' => '9090909090909',
                'product_sku' => 'BICYCLE',
                'company_name' => 'Test New Retailer',
            ],
            9 => [
                'manufacturer' => 'Sirisha Test Brand',
                'variant_id' => '16885145025',
                'storeId' => '16',
                'productId' => '26',
                'inventoryCount' => '1956',
                'product_title' => 'In-Stock Dealer Protect',
                'product_upc' => '0202020202020',
                'product_sku' => 'IN-STOCK-DEALER-PROTECT-1',
                'company_name' => 'Test New Retailer',
            ],
        ];

        $query = [
            'order' => 'id',
            'limit' => 10,
        ];
        $actual = $this->ApiV1RetailInventory->index([$this->ApiV1RetailInventory->alias => $query]);
        $this->removeTimestampFields($actual);

        $this->assertEquals($expected, $actual);
    }

    /**
     * @dataProvider providerRetailerIndex
     */
    public function testRetailerIndex(int $retailerId, array $expected)
    {
        $this->ApiV1RetailInventory->retailerId = $retailerId;
        $actual = $this->ApiV1RetailInventory->index([$this->ApiV1RetailInventory->alias => []]);
        $this->removeTimestampFields($actual);

        $this->assertEquals($expected, $actual);
    }

    public function providerRetailerIndex(): array
    {
        return [
            'exists' => [
                'retailerId' => 15,
                'expected' => [
                    0 => [
                        'manufacturer' => 'Sirisha Test Brand',
                        'variant_id' => '11130571329',
                        'storeId' => '15',
                        'productId' => '29',
                        'inventoryCount' => '0',
                        'product_title' => 'No Stock',
                        'product_upc' => '0101010101010',
                        'product_sku' => 'NO-STOCK',
                        'company_name' => 'Sirisha Test Retailer',
                    ],
                    1 => [
                        'manufacturer' => 'Sirisha Test Brand',
                        'variant_id' => '10723507649',
                        'storeId' => '15',
                        'productId' => '25',
                        'inventoryCount' => '708',
                        'product_title' => 'Bicycle',
                        'product_upc' => '9090909090909',
                        'product_sku' => 'BICYCLE',
                        'company_name' => 'Sirisha Test Retailer',
                    ],
                    2 => [
                        'manufacturer' => 'Sirisha Test Brand',
                        'variant_id' => '16885145025',
                        'storeId' => '15',
                        'productId' => '26',
                        'inventoryCount' => '1956',
                        'product_title' => 'In-Stock Dealer Protect',
                        'product_upc' => '0202020202020',
                        'product_sku' => 'IN-STOCK-DEALER-PROTECT-1',
                        'company_name' => 'Sirisha Test Retailer',
                    ],
                    3 => [
                        'manufacturer' => 'Sirisha Test Brand',
                        'variant_id' => '16885170497',
                        'storeId' => '15',
                        'productId' => '27',
                        'inventoryCount' => '1999',
                        'product_title' => 'In-Stock Dealer Protect 2',
                        'product_upc' => '1212121212121',
                        'product_sku' => 'IN-STOCK-DEALER-PROTECT-2',
                        'company_name' => 'Sirisha Test Retailer',
                    ],
                    4 => [
                        'manufacturer' => 'Sirisha Test Brand',
                        'variant_id' => '10723515137',
                        'storeId' => '15',
                        'productId' => '24',
                        'inventoryCount' => '1951',
                        'product_title' => 'Always Sell Direct',
                        'product_upc' => '0303030303030',
                        'product_sku' => 'ALWAYS-SELL-DIRECT',
                        'company_name' => 'Sirisha Test Retailer',
                    ],
                    5 => [
                        'manufacturer' => 'Sirisha Test Brand',
                        'variant_id' => '16885440705',
                        'storeId' => '15',
                        'productId' => '33',
                        'inventoryCount' => '969',
                        'product_title' => 'Sell Direct Unless Bundled',
                        'product_upc' => '0404040404040',
                        'product_sku' => 'SELL-DIRECT-UNLESS-BUNDLED',
                        'company_name' => 'Sirisha Test Retailer',
                    ],
                    6 => [
                        'manufacturer' => 'Sirisha Test Brand',
                        'variant_id' => '16885353665',
                        'storeId' => '15',
                        'productId' => '31',
                        'inventoryCount' => '899',
                        'product_title' => 'In-Stock Only',
                        'product_upc' => '0505050505050',
                        'product_sku' => 'IN-STOCK-ONLY',
                        'company_name' => 'Sirisha Test Retailer',
                    ],
                ],
            ],
            'doesNotExist' => [
                'retailerId' => 2345235,
                'expected' => [],
            ],
            'existsNotForUser' => [
                'retailerId' => 10,
                'expected' => [],
            ],
        ];
    }

    /**
     * @dataProvider providerVariantIndex
     */
    public function testVariantIndex(int $variantId, array $expected)
    {
        $this->ApiV1RetailInventory->variantId = $variantId;
        $actual = $this->ApiV1RetailInventory->index([$this->ApiV1RetailInventory->alias => []]);
        $this->removeTimestampFields($actual);

        $this->assertEquals($expected, $actual);
    }

    public function providerVariantIndex(): array
    {
        return [
            'exists' => [
                'variantId' => 11130571329,
                'expected' => [
                    0 => [
                        'manufacturer' => 'Sirisha Test Brand',
                        'variant_id' => '11130571329',
                        'storeId' => '15',
                        'productId' => '29',
                        'inventoryCount' => '0',
                        'product_title' => 'No Stock',
                        'product_upc' => '0101010101010',
                        'product_sku' => 'NO-STOCK',
                        'company_name' => 'Sirisha Test Retailer',
                    ],
                    1 => [
                        'manufacturer' => 'Sirisha Test Brand',
                        'variant_id' => '11130571329',
                        'storeId' => '16',
                        'productId' => '29',
                        'inventoryCount' => '0',
                        'product_title' => 'No Stock',
                        'product_upc' => '0101010101010',
                        'product_sku' => 'NO-STOCK',
                        'company_name' => 'Test New Retailer',
                    ],
                ],
            ],
            'doesNotExist' => [
                'variantId' => 786,
                'expected' => [],
            ],
            'existsNotForUser' => [
                'variantId' => 12641968198,
                'expected' => [],
            ],
        ];
    }

    public function testCount()
    {
        $expected = 14;
        $actual = $this->ApiV1RetailInventory->count();
        $this->assertEquals($expected, $actual);
    }

    /**
     * @dataProvider providerRetailerCount
     */
    public function testRetailerCount(int $retailerId, int $expected)
    {
        $this->ApiV1RetailInventory->retailerId = $retailerId;
        $actual = $this->ApiV1RetailInventory->count();

        $this->assertEquals($expected, $actual);
    }

    public function providerRetailerCount(): array
    {
        return array_map(
            fn(array $case): array => array_merge($case, ['expected' => count($case['expected'])]),
            $this->providerRetailerIndex()
        );
    }

    /**
     * @dataProvider providerVariantCount
     */
    public function testVariantCount(int $variantId, int $expected)
    {
        $this->ApiV1RetailInventory->variantId = $variantId;
        $actual = $this->ApiV1RetailInventory->count();

        $this->assertEquals($expected, $actual);
    }

    public function providerVariantCount(): array
    {
        return array_map(
            fn(array $case): array => array_merge($case, ['expected' => count($case['expected'])]),
            $this->providerVariantIndex()
        );
    }

    /**
     * @dataProvider providerRetailerView
     */
    public function testRetailerView(int $retailerId, int $variantId, array $expected)
    {
        $this->ApiV1RetailInventory->retailerId = $retailerId;
        $actual = $this->ApiV1RetailInventory->view($variantId);
        $this->removeTimestampFields($actual);

        $this->assertEquals($expected, $actual);
    }

    public function providerRetailerView(): array
    {
        return [
            'retailerExistsAndVariantExists' => [
                'retailerId' => 15,
                'variantId' => 11130571329,
                'expected' => [
                    'manufacturer' => 'Sirisha Test Brand',
                    'variant_id' => '11130571329',
                    'storeId' => '15',
                    'productId' => '29',
                    'inventoryCount' => '0',
                    'product_title' => 'No Stock',
                    'product_upc' => '0101010101010',
                    'product_sku' => 'NO-STOCK',
                    'company_name' => 'Sirisha Test Retailer',
                ],
            ],
            'retailerDoesNotExistAndVariantExists' => [
                'retailerId' => 2345235,
                'variantId' => 11130571329,
                'expected' => [],
            ],
            'retailerExistsNotForUserAndVariantExists' => [
                'retailerId' => 10,
                'variantId' => 11130571329,
                'expected' => [],
            ],
            'retailerExistsAndVariantDoesNotExist' => [
                'retailerId' => 15,
                'variantId' => 2323537645745,
                'expected' => [],
            ],
            'retailerDoesNotExistAndVariantDoesNotExist' => [
                'retailerId' => 2345235,
                'variantId' => 2323537645745,
                'expected' => [],
            ],
            'retailerExistsNotForUserAndVariantDoesNotExist' => [
                'retailerId' => 10,
                'variantId' => 2323537645745,
                'expected' => [],
            ],
            'retailerExistsAndVariantExistsNotForUser' => [
                'retailerId' => 15,
                'variantId' => 20016274759,
                'expected' => [],
            ],
            'retailerDoesNotExistAndVariantExistsNotForUser' => [
                'retailerId' => 2345235,
                'variantId' => 20016274759,
                'expected' => [],
            ],
            'retailerExistsNotForUserAndVariantExistsNotForUser' => [
                'retailerId' => 10,
                'variantId' => 20016274759,
                'expected' => [],
            ],
            'variantExistsAndNotInRetailerInventory' => [
                'retailerId' => 15,
                'variantId' => 16885254017,
                'expected' => [],
            ],
        ];
    }

    /**
     * @dataProvider providerVariantView
     */
    public function testVariantView(int $variantId, int $retailerId, array $expected)
    {
        $this->ApiV1RetailInventory->variantId = $variantId;
        $actual = $this->ApiV1RetailInventory->view($retailerId);
        $this->removeTimestampFields($actual);

        $this->assertEquals($expected, $actual);
    }

    public function providerVariantView(): array
    {
        return [
            'variantExistsRetailerExists' => [
                'variantId' => 11130571329,
                'retailerId' => 15,
                'expected' => [
                    'manufacturer' => 'Sirisha Test Brand',
                    'variant_id' => '11130571329',
                    'storeId' => '15',
                    'productId' => '29',
                    'inventoryCount' => '0',
                    'product_title' => 'No Stock',
                    'product_upc' => '0101010101010',
                    'product_sku' => 'NO-STOCK',
                    'company_name' => 'Sirisha Test Retailer',
                ],
            ],
            'variantDoesNotExistRetailerExists' => [
                'variantId' => 786,
                'retailerId' => 15,
                'expected' => [],
            ],
            'variantExistsNotForUserRetailerExists' => [
                'variantId' => 12641968198,
                'retailerId' => 15,
                'expected' => [],
            ],
            'variantExistsRetailerDoesNotExist' => [
                'variantId' => 11130571329,
                'retailerId' => *********,
                'expected' => [],
            ],
            'variantDoesNotExistRetailerDoesNotExist' => [
                'variantId' => 786,
                'retailerId' => *********,
                'expected' => [],
            ],
            'variantExistsNotForUserRetailerDoesNotExist' => [
                'variantId' => 12641968198,
                'retailerId' => *********,
                'expected' => [],
            ],
            'variantExistsRetailerExistsNotForUser' => [
                'variantId' => 11130571329,
                'retailerId' => 10,
                'expected' => [],
            ],
            'variantDoesNotExistRetailerExistsNotForUser' => [
                'variantId' => 786,
                'retailerId' => 10,
                'expected' => [],
            ],
            'variantExistsNotForUserRetailerExistsNotForUser' => [
                'variantId' => 12641968198,
                'retailerId' => 10,
                'expected' => [],
            ],
        ];
    }

    public function testAdd()
    {
        $this->expectException(InvalidOperationException::class, 'Invalid Operation');

        $this->ApiV1RetailInventory->add();
    }

    public function testRemove()
    {
        $this->expectException(InvalidOperationException::class, 'Invalid Operation');

        $this->ApiV1RetailInventory->remove(0);
    }

    public function testEdit()
    {
        $this->expectException(InvalidOperationException::class, 'Invalid Operation');

        $this->ApiV1RetailInventory->edit(0);
    }

    public function testIndexContainsTimestamps()
    {
        $actual = $this->ApiV1RetailInventory->index([$this->ApiV1RetailInventory->alias => []]);
        $this->assertArrayHasKey('updated', current($actual));
    }

    /**
     * @dataProvider providerIsValidRetailerId
     */
    public function testIsValidRetailerId($retailerId, $expected)
    {
        $actual = $this->ApiV1RetailInventory->isValidRetailerId($retailerId);

        $this->assertEquals($expected, $actual);
    }

    public function providerIsValidRetailerId(): array
    {
        return [
            'exists' => [
                'retailerId' => 15,
                'expected' => true,
            ],
            'exists is branch' => [
                'retailerId' => 27,
                'expected' => true,
            ],
            'does not exist' => [
                'retailerId' => 236436,
                'expected' => false,
            ],
            'exists not for brand' => [
                'retailerId' => 10,
                'expected' => false,
            ],
        ];
    }
}
