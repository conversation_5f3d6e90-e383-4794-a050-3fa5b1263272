<?php
App::uses('ApiV1SalesRep', 'Api/v1.Model');
App::uses('ApiV1ModelTestCase', 'Api/v1.Test/Case/Model');

/**
 * ApiV1SalesRep Test Case
 *
 * ./app/Console/cake test Api/v1 Model/ApiV1SalesRep
 *
 * @property ApiV1SalesRep $ApiV1SalesRep
 */
class ApiV1SalesRepTest extends ApiV1ModelTestCase
{
    public $userId = 12;
    public $testModel = 'ApiV1SalesRep';

    /**
     * Fixtures
     *
     * @var array
     */
    public $fixtures = [
        'app.contact',
        'app.contactpersons',
        'app.manufacturer_sales_rep',
        'app.user',
    ];

    public function testAdd()
    {
        $this->expectException(InvalidOperationException::class, 'Invalid Operation');

        $this->ApiV1SalesRep->add();
    }

    public function testRemove()
    {
        $this->expectException(InvalidOperationException::class, 'Invalid Operation');

        $this->ApiV1SalesRep->remove(0);
    }

    public function canEditProvider()
    {
        return [
            'exists' => ['id' => 21, 'expected' => true],
            'doesNotExist' => ['id' => 1990, 'expected' => false],
            'existsNotForUser' => ['id' => 20, 'expected' => false],
        ];
    }

    /**
     * @dataProvider canEditProvider
     */
    public function testCanEdit($id, $expected)
    {
        $actual = $this->ApiV1SalesRep->canEdit($id);
        $this->assertEquals($expected, $actual);
    }

    public function testBeforeFind()
    {
        $expected = [
            'conditions' => [
                'ApiV1ManufacturerSalesRep.user_id' => $this->userId,
            ],
        ];
        $actual = $this->ApiV1SalesRep->beforeFind([]);
        $this->assertEquals($expected, $actual);
    }

    public function testIndex()
    {
        $expected = [
            0 => [
                'id' => '21',
                'value' => '**********',
                'company_name' => 'Sales Rep',
                'firstname' => 'Aron',
                'lastname' => 'Schmidt',
                'email' => '<EMAIL>',
                'descriptor' => 'Sales Rep',
                'is_distributor' => false,
            ],
            1 => [
                'id' => '22',
                'value' => '**********',
                'company_name' => 'Distributor',
                'firstname' => 'Hiro',
                'lastname' => 'Protagonist',
                'email' => '<EMAIL>',
                'descriptor' => 'Distributor',
                'is_distributor' => true,
            ],

        ];
        $actual = $this->ApiV1SalesRep->index([$this->ApiV1SalesRep->alias => []]);
        $this->removeTimestampFields($actual);
        $this->assertEquals($expected, $actual);
    }

    public function testCount()
    {
        $expected = 2;
        $actual = $this->ApiV1SalesRep->count();

        $this->assertEquals($expected, $actual);
    }

    public function viewProvider()
    {
        return [
            'exists' => [
                'id' => 21,
                'expected' => [
                    'id' => '21',
                    'value' => '**********',
                    'company_name' => 'Sales Rep',
                    'firstname' => 'Aron',
                    'lastname' => 'Schmidt',
                    'email' => '<EMAIL>',
                    'descriptor' => 'Sales Rep',
                    'is_distributor' => false,
                ],
            ],
            'existsNotForUserId' => [
                'id' => 4,
                'expected' => [],
            ],
            'doesNotExist' => [
                'id' => 13253463,
                'expected' => [],
            ],
        ];
    }

    /**
     * @dataProvider viewProvider
     */
    public function testView($id, $expected)
    {
        if (is_a($expected, Exception::class)) {
            $this->expectException(get_class($expected), $expected->getMessage());
            $this->ApiV1SalesRep->view($id);
        } else {
            $actual = $this->ApiV1SalesRep->view($id);
            $this->removeTimestampFields($actual);
            $this->assertEquals($expected, $actual);
        }
    }

    public function editProvider()
    {
        return [
            'empty' => [
                'id' => 21,
                'editData' => [],
                'dirtyFields' => [],
                'expectedReturnFields' => [
                    'value' => '**********',
                    'id' => '21',
                    'company_name' => 'Sales Rep',
                    'firstname' => 'Aron',
                    'lastname' => 'Schmidt',
                    'email' => '<EMAIL>',
                    'descriptor' => 'Sales Rep',
                    'is_distributor' => false,
                ],
            ],
            'perfectInput' => [
                'id' => 21,
                'editData' => [
                    'value' => '(555) 555-5555',
                    'id' => '21',
                    'company_name' => 'test company name',
                    'firstname' => 'Billy',
                    'lastname' => 'Thomas',
                    'email' => '<EMAIL>',
                    'descriptor' => 'Master of Eternity',
                    'is_distributor' => true,
                ],
                'dirtyFields' => [],
                'expectedReturnFields' => [
                    'value' => '(555) 555-5555',
                    'id' => '21',
                    'company_name' => 'test company name',
                    'firstname' => 'Billy',
                    'lastname' => 'Thomas',
                    'email' => '<EMAIL>',
                    'descriptor' => 'Master of Eternity',
                    'is_distributor' => true,
                ],
            ],
            'dirtyInput' => [
                'id' => 21,
                'editData' => [
                    'value' => '(555) 555-5555',
                    'id' => '21',
                    'company_name' => 'test company name',
                    'firstname' => 'Billy',
                    'lastname' => 'Thomas',
                    'email' => '<EMAIL>',
                    'descriptor' => 'Master of Eternity',
                    'is_distributor' => true,
                ],
                'dirtyFields' => [
                    'dirtyfield1' => 'dirty',
                    'dirtyfield2' => 123,
                    'dirtyfield3' => false,
                ],
                'expectedReturnFields' => [
                    'value' => '(555) 555-5555',
                    'id' => '21',
                    'company_name' => 'test company name',
                    'firstname' => 'Billy',
                    'lastname' => 'Thomas',
                    'email' => '<EMAIL>',
                    'descriptor' => 'Master of Eternity',
                    'is_distributor' => true,
                ],
            ],
            'existsNotForUser' => [
                'id' => 23,
                'editData' => [
                    'value' => '(555) 555-5555',
                    'id' => '21',
                    'company_name' => 'test company name',
                    'firstname' => 'Billy',
                    'lastname' => 'Thomas',
                    'email' => '<EMAIL>',
                    'descriptor' => 'Master of Eternity',
                    'is_distributor' => true,
                ],
                'dirtyFields' => [
                    'dirtyfield1' => 'dirty',
                    'dirtyfield2' => 123,
                    'dirtyfield3' => false,
                ],
                'expectedReturnFields' => new NotFoundException('Sales rep not found'),
            ],
            'doesNotExist' => [
                'id' => 54322,
                'editData' => [
                    'value' => '(555) 555-5555',
                    'id' => '21',
                    'company_name' => 'test company name',
                    'firstname' => 'Billy',
                    'lastname' => 'Thomas',
                    'email' => '<EMAIL>',
                    'descriptor' => 'Master of Eternity',
                    'is_distributor' => true,
                ],
                'dirtyFields' => [
                    'dirtyfield1' => 'dirty',
                    'dirtyfield2' => 123,
                    'dirtyfield3' => false,
                ],
                'expectedReturnFields' => new NotFoundException('Sales rep not found'),
            ],
        ];
    }

    /**
     * @dataProvider editProvider
     */
    public function testEdit($id, $editData, $dirtyFields, $expectedReturnFields)
    {
        $editData += $dirtyFields;
        if (is_a($expectedReturnFields, Exception::class)) {
            $this->expectException(get_class($expectedReturnFields), $expectedReturnFields->getMessage());
            $this->ApiV1SalesRep->edit($id, $editData);
        } else {
            $expected = array_diff($editData, $dirtyFields) + $expectedReturnFields;
            $this->ApiV1SalesRep->edit($id, $editData);
            $actual = $this->ApiV1SalesRep->view($id);
            $this->removeTimestampFields($actual);
            $this->assertEquals($expected, $actual);
        }
    }

    public function testIndexContainsTimestamps()
    {
        $actual = $this->ApiV1SalesRep->index([$this->ApiV1SalesRep->alias => []]);
        $this->assertArrayHasKey('updated_at', current($actual));
        $this->assertArrayHasKey('created_at', current($actual));
    }

    public function testViewContainsTimestamps()
    {
        $actual = $this->ApiV1SalesRep->view(21);
        $this->assertArrayHasKey('updated_at', $actual);
        $this->assertArrayHasKey('created_at', $actual);
    }
}
