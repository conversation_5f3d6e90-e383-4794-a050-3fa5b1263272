<?php

namespace ShipEarlyApp\Plugin\Api\v1\Test\Provider;

use BadRequestException;
use NotFoundException;

class ApiV1ProductTiersControllerProvider
{
    const DEFAULT_VARIANT_ID = 10723507649;
    const DEFAULT_PRICING_TIER_ID = 5;

    public static function testIndex(): array
    {
        $variantId = static::DEFAULT_VARIANT_ID;
        $pricingTierId = static::DEFAULT_PRICING_TIER_ID;

        $cases = [
            'BICYCLE' => [
                'variantId' => $variantId,
                'expected' => [
                    'data' => [
                        0 => [
                            'id' => (string)$pricingTierId,
                            'variant_id' => (string)$variantId,
                            'dealer_price' => '349.99',
                            'ship_to_store_price' => null,
                            'commission_price' => '70.00',
                            'created_at' => '2016-06-11 00:25:38',
                            'updated_at' => '2016-06-11 00:25:38',
                        ],
                    ],
                ],
            ],
        ];

        return $cases + static::invalidVariantIdCases();
    }

    public static function testGet(): array
    {
        $variantId = static::DEFAULT_VARIANT_ID;
        $pricingTierId = static::DEFAULT_PRICING_TIER_ID;

        $cases = [
            'BICYCLE Tier 1' => [
                'variantId' => $variantId,
                'pricingTierId' => $pricingTierId,
                'expected' => [
                    'data' => [
                        'id' => (string)$pricingTierId,
                        'variant_id' => (string)$variantId,
                        'dealer_price' => '349.99',
                        'ship_to_store_price' => null,
                        'commission_price' => '70.00',
                        'created_at' => '2016-06-11 00:25:38',
                        'updated_at' => '2016-06-11 00:25:38',
                    ],
                ],
            ],
        ];
        $invalidVariantIdCases = array_map(
            fn(array $case): array => [
                'variantId' => $case['variantId'],
                'pricingTierId' => $pricingTierId,
                'expected' => $case['expected'],
            ],
            static::invalidVariantIdCases()
        );

        return $cases + $invalidVariantIdCases + static::invalidPricingTierIdCases();
    }

    public static function testPost(): array
    {
        $variantId = static::DEFAULT_VARIANT_ID;
        $assignedPricingTierId = static::DEFAULT_PRICING_TIER_ID;
        $unassignedPricingTierId = 6;

        $data = [
            'id' => $unassignedPricingTierId,
            'dealer_price' => 299.99,
            'ship_to_store_price' => 349,
            'commission_price' => 99.99,
            // Read-only field changes to be ignored
            'variant_id' => 1,
            'created_at' => '2024-11-13 17:30:00',
            'updated_at' => '2024-11-13 17:30:00',
        ];
        $expected = [
            'data' => [
                'id' => (string)$unassignedPricingTierId,
                'variant_id' => (string)$variantId,
                'dealer_price' => '299.99',
                'ship_to_store_price' => '349.00',
                'commission_price' => '99.99',
                'created_at' => '2024-11-13 17:00:00',
                'updated_at' => '2024-11-13 17:00:00',
            ],
        ];

        $cases = [
            'createsNewRecord' => [
                'variantId' => $variantId,
                'data' => $data,
                'expected' => $expected,
            ],
            'updatesExistingRecord' => [
                'variantId' => $variantId,
                'data' => ['id' => $assignedPricingTierId] + $data,
                'expected' => [
                    'data' => array_merge($expected['data'], ['id' => $assignedPricingTierId]),
                ],
            ],
            'pricingTierIdMissing' => [
                'variantId' => $variantId,
                'data' => array_diff_key($data, array_flip(['id'])),
                'expected' => new BadRequestException('{"id":["Pricing tier id is required"]}'),
            ],
            'pricingTierIdNaN' => [
                'variantId' => $variantId,
                'data' => array_merge($data, ['id' => 'bogus']),
                'expected' => new BadRequestException('{"id":["Invalid pricing tier id"]}'),
            ],
            'pricingTierIdNull' => [
                'variantId' => $variantId,
                'data' => array_merge($data, ['id' => null]),
                'expected' => new BadRequestException('{"id":["Pricing tier id is required"]}'),
            ],
            'pricingTierIdZero' => [
                'variantId' => $variantId,
                'data' => array_merge($data, ['id' => 0]),
                'expected' => new BadRequestException('{"id":["Invalid pricing tier id"]}'),
            ],
            'pricingTierIdNegative' => [
                'variantId' => $variantId,
                'data' => array_merge($data, ['id' => -1]),
                'expected' => new BadRequestException('{"id":["Invalid pricing tier id"]}'),
            ],
            'dealerPriceMissing' => [
                'variantId' => $variantId,
                'data' => array_diff_key($data, array_flip(['dealer_price'])),
                'expected' => [
                    'data' => array_merge($expected['data'], ['dealer_price' => null]),
                ],
            ],
            'dealerPriceNull' => [
                'variantId' => $variantId,
                'data' => array_merge($data, ['dealer_price' => null]),
                'expected' => [
                    'data' => array_merge($expected['data'], ['dealer_price' => null]),
                ],
            ],
            'dealerPriceNaN' => [
                'variantId' => $variantId,
                'data' => array_merge($data, ['dealer_price' => 'bogus']),
                'expected' => new BadRequestException('{"dealer_price":["Invalid dealer price"]}'),
            ],
            'dealerPriceZero' => [
                'variantId' => $variantId,
                'data' => array_merge($data, ['dealer_price' => 0]),
                'expected' => [
                    'data' => array_merge($expected['data'], ['dealer_price' => '0.00']),
                ],
            ],
            'dealerPriceNegative' => [
                'variantId' => $variantId,
                'data' => array_merge($data, ['dealer_price' => -0.01]),
                'expected' => new BadRequestException('{"dealer_price":["Dealer price cannot be negative"]}'),
            ],
            'shipToStorePriceMissing' => [
                'variantId' => $variantId,
                'data' => array_diff_key($data, array_flip(['ship_to_store_price'])),
                'expected' => [
                    'data' => array_merge($expected['data'], ['ship_to_store_price' => null]),
                ],
            ],
            'shipToStorePriceNaN' => [
                'variantId' => $variantId,
                'data' => array_merge($data, ['ship_to_store_price' => 'bogus']),
                'expected' => new BadRequestException('{"ship_to_store_price":["Invalid ship to store price"]}'),
            ],
            'shipToStorePriceNull' => [
                'variantId' => $variantId,
                'data' => array_merge($data, ['ship_to_store_price' => null]),
                'expected' => [
                    'data' => array_merge($expected['data'], ['ship_to_store_price' => null]),
                ],
            ],
            'shipToStorePriceZero' => [
                'variantId' => $variantId,
                'data' => array_merge($data, ['ship_to_store_price' => 0]),
                'expected' => [
                    'data' => array_merge($expected['data'], ['ship_to_store_price' => '0.00']),
                ],
            ],
            'shipToStorePriceNegative' => [
                'variantId' => $variantId,
                'data' => array_merge($data, ['ship_to_store_price' => -0.01]),
                'expected' => new BadRequestException('{"ship_to_store_price":["Ship to store price cannot be negative"]}'),
            ],
            'commissionPriceMissing' => [
                'variantId' => $variantId,
                'data' => array_diff_key($data, array_flip(['commission_price'])),
                'expected' => [
                    'data' => array_merge($expected['data'], ['commission_price' => '0.00']),
                ],
            ],
            'commissionPriceNaN' => [
                'variantId' => $variantId,
                'data' => array_merge($data, ['commission_price' => 'bogus']),
                'expected' => new BadRequestException('{"commission_price":["Invalid commission price"]}'),
            ],
            'commissionPriceNull' => [
                'variantId' => $variantId,
                'data' => array_merge($data, ['commission_price' => null]),
                'expected' => new BadRequestException('{"commission_price":["Invalid commission price"]}'),
            ],
            'commissionPriceZero' => [
                'variantId' => $variantId,
                'data' => array_merge($data, ['commission_price' => 0]),
                'expected' => [
                    'data' => array_merge($expected['data'], ['commission_price' => '0.00']),
                ],
            ],
            'commissionPriceNegative' => [
                'variantId' => $variantId,
                'data' => array_merge($data, ['commission_price' => -0.01]),
                'expected' => new BadRequestException('{"commission_price":["Commission price cannot be negative"]}'),
            ],
        ];
        $invalidVariantIdCases = array_map(
            fn(array $case): array => [
                'variantId' => $case['variantId'],
                'data' => $data,
                'expected' => $case['expected'],
            ],
            static::invalidVariantIdCases()
        );
        $invalidPricingTierIdCases = array_map(
            fn(array $case): array => [
                'variantId' => $case['variantId'],
                'data' => ['id' => (string)$case['pricingTierId']] + $data,
                'expected' => new BadRequestException('{"id":["Pricing tier id not found"]}'),
            ],
            array_diff_key(static::invalidPricingTierIdCases(), array_flip(['invalidPricingTierExistsNotForVariant']))
        );

        return $cases + $invalidVariantIdCases + $invalidPricingTierIdCases;
    }

    public static function testPut(): array
    {
        $variantId = static::DEFAULT_VARIANT_ID;
        $pricingTierId = static::DEFAULT_PRICING_TIER_ID;
        $data = [
            'dealer_price' => 299.99,
            'ship_to_store_price' => 349,
            'commission_price' => 99.99,
            // Read-only field changes to be ignored
            'id' => 1,
            'variant_id' => 1,
            'created_at' => '2024-11-13 17:30:00',
            'updated_at' => '2024-11-13 17:30:00',
        ];
        $expected = [
            'data' => [
                'id' => (string)$pricingTierId,
                'variant_id' => (string)$variantId,
                'dealer_price' => '299.99',
                'ship_to_store_price' => '349.00',
                'commission_price' => '99.99',
                'created_at' => '2016-06-11 00:25:38',
                'updated_at' => '2024-11-13 17:00:00',
            ],
        ];

        $cases = [
            'updatesExistingRecord' => [
                'variantId' => $variantId,
                'pricingTierId' => $pricingTierId,
                'data' => $data,
                'expected' => $expected,
            ],
            'dealerPriceMissing' => [
                'variantId' => $variantId,
                'pricingTierId' => $pricingTierId,
                'data' => array_diff_key($data, array_flip(['dealer_price'])),
                'expected' => [
                    'data' => array_merge($expected['data'], ['dealer_price' => '349.99']),
                ],
            ],
            'dealerPriceNull' => [
                'variantId' => $variantId,
                'pricingTierId' => $pricingTierId,
                'data' => array_merge($data, ['dealer_price' => null]),
                'expected' => [
                    'data' => array_merge($expected['data'], ['dealer_price' => null]),
                ],
            ],
            'dealerPriceNaN' => [
                'variantId' => $variantId,
                'pricingTierId' => $pricingTierId,
                'data' => array_merge($data, ['dealer_price' => 'bogus']),
                'expected' => new BadRequestException('{"dealer_price":["Invalid dealer price"]}'),
            ],
            'dealerPriceZero' => [
                'variantId' => $variantId,
                'pricingTierId' => $pricingTierId,
                'data' => array_merge($data, ['dealer_price' => 0]),
                'expected' => [
                    'data' => array_merge($expected['data'], ['dealer_price' => '0.00']),
                ],
            ],
            'dealerPriceNegative' => [
                'variantId' => $variantId,
                'pricingTierId' => $pricingTierId,
                'data' => array_merge($data, ['dealer_price' => -0.01]),
                'expected' => new BadRequestException('{"dealer_price":["Dealer price cannot be negative"]}'),
            ],
            'shipToStorePriceMissing' => [
                'variantId' => $variantId,
                'pricingTierId' => $pricingTierId,
                'data' => array_diff_key($data, array_flip(['ship_to_store_price'])),
                'expected' => [
                    'data' => array_merge($expected['data'], ['ship_to_store_price' => null]),
                ],
            ],
            'shipToStorePriceNaN' => [
                'variantId' => $variantId,
                'pricingTierId' => $pricingTierId,
                'data' => array_merge($data, ['ship_to_store_price' => 'bogus']),
                'expected' => new BadRequestException('{"ship_to_store_price":["Invalid ship to store price"]}'),
            ],
            'shipToStorePriceNull' => [
                'variantId' => $variantId,
                'pricingTierId' => $pricingTierId,
                'data' => array_merge($data, ['ship_to_store_price' => null]),
                'expected' => [
                    'data' => array_merge($expected['data'], ['ship_to_store_price' => null]),
                ],
            ],
            'shipToStorePriceZero' => [
                'variantId' => $variantId,
                'pricingTierId' => $pricingTierId,
                'data' => array_merge($data, ['ship_to_store_price' => 0]),
                'expected' => [
                    'data' => array_merge($expected['data'], ['ship_to_store_price' => '0.00']),
                ],
            ],
            'shipToStorePriceNegative' => [
                'variantId' => $variantId,
                'pricingTierId' => $pricingTierId,
                'data' => array_merge($data, ['ship_to_store_price' => -0.01]),
                'expected' => new BadRequestException('{"ship_to_store_price":["Ship to store price cannot be negative"]}'),
            ],
            'commissionPriceMissing' => [
                'variantId' => $variantId,
                'pricingTierId' => $pricingTierId,
                'data' => array_diff_key($data, array_flip(['commission_price'])),
                'expected' => [
                    'data' => array_merge($expected['data'], ['commission_price' => '70.00']),
                ],
            ],
            'commissionPriceNaN' => [
                'variantId' => $variantId,
                'pricingTierId' => $pricingTierId,
                'data' => array_merge($data, ['commission_price' => 'bogus']),
                'expected' => new BadRequestException('{"commission_price":["Invalid commission price"]}'),
            ],
            'commissionPriceNull' => [
                'variantId' => $variantId,
                'pricingTierId' => $pricingTierId,
                'data' => array_merge($data, ['commission_price' => null]),
                'expected' => new BadRequestException('{"commission_price":["Invalid commission price"]}'),
            ],
            'commissionPriceZero' => [
                'variantId' => $variantId,
                'pricingTierId' => $pricingTierId,
                'data' => array_merge($data, ['commission_price' => 0]),
                'expected' => [
                    'data' => array_merge($expected['data'], ['commission_price' => '0.00']),
                ],
            ],
            'commissionPriceNegative' => [
                'variantId' => $variantId,
                'pricingTierId' => $pricingTierId,
                'data' => array_merge($data, ['commission_price' => -0.01]),
                'expected' => new BadRequestException('{"commission_price":["Commission price cannot be negative"]}'),
            ],
        ];
        $invalidVariantIdCases = array_map(
            fn(array $case): array => [
                'variantId' => $case['variantId'],
                'pricingTierId' => $pricingTierId,
                'data' => $data,
                'expected' => $case['expected'],
            ],
            static::invalidVariantIdCases()
        );
        $invalidPricingTierIdCases = array_map(
            fn(array $case): array => [
                'variantId' => $case['variantId'],
                'pricingTierId' => $case['pricingTierId'],
                'data' => $data,
                'expected' => $case['expected'],
            ],
            static::invalidPricingTierIdCases()
        );

        return $cases + $invalidVariantIdCases + $invalidPricingTierIdCases;
    }

    public static function invalidVariantIdCases(): array
    {
        return [
            'invalidVariantDoesNotExist' => [
                'variantId' => PHP_INT_MAX,
                'expected' => new NotFoundException(),
            ],
            'invalidVariantExistsNotForUser' => [
                'variantId' => 12641968198,
                'expected' => new NotFoundException(),
            ],
        ];
    }

    public static function invalidPricingTierIdCases(): array
    {
        $variantId = static::DEFAULT_VARIANT_ID;

        return [
            'invalidPricingTierDoesNotExist' => [
                'variantId' => $variantId,
                'pricingTierId' => PHP_INT_MAX,
                'expected' => new NotFoundException(),
            ],
            'invalidPricingTierExistsNotForUser' => [
                'variantId' => $variantId,
                'pricingTierId' => 4,
                'expected' => new NotFoundException(),
            ],
            'invalidPricingTierExistsNotForVariant' => [
                'variantId' => $variantId,
                'pricingTierId' => 6,
                'expected' => new NotFoundException(),
            ],
        ];
    }
}
