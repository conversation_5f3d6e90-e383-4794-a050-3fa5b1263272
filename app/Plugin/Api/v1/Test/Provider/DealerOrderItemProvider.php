<?php
class DealerOrderItemProvider
{
    public static function indexProvider()
    {
        return [
            'orderExists' => [
                'orderId' => 7,
                'expected' => [
                    0 => [
                        'dealer_order_id' => '12',
                        'product_id' => '48',
                        'id' => '16',
                        'order_id' => '7',
                        'warehouse_id' => '5',
                        'product_quantity' => '5',
                        'total_discount' => '0.00',
                        'product_price' => '40.00',
                        'restock_date' => null,
                        'productID' => '34',
                        'product_title_id' => '21',
                        'product_sku' => 'ASD',
                        'product_upc' => null,
                        'product_title' => 'Always Sell Direct',
                        'total_price' => '200.00',
                        'subtotal' => '200.00',
                        'quantity_to_fulfill' => '4',
                        'inventory_transfer_id' => null,
                        'estimated_ship_date' => null,
                    ],
                    1 => [
                        'dealer_order_id' => '12',
                        'product_id' => '53',
                        'id' => '17',
                        'order_id' => '7',
                        'warehouse_id' => '5',
                        'product_quantity' => '5',
                        'total_discount' => '0.00',
                        'product_price' => '19.99',
                        'restock_date' => null,
                        'productID' => '40',
                        'product_title_id' => '26',
                        'product_sku' => 'BLACK',
                        'product_upc' => null,
                        'product_title' => 'Test - BLACK',
                        'total_price' => '99.95',
                        'subtotal' => '99.95',
                        'quantity_to_fulfill' => '4',
                        'inventory_transfer_id' => null,
                        'estimated_ship_date' => null,

                    ],
                ],
            ],
            'orderDoesNotExist' => [
                'orderId' => 8487234,
                'expected' => [],
            ],
            'orderExistsNotForUser' => [
                'orderId' => 5,
                'expected' => [],
            ],
        ];
    }
    public static function viewProvider()
    {
        return [
            'orderExistsOrderItemExists' => [
                'orderItemId' => 16,
                'orderId' => 7,
                'expected' => [
                    'dealer_order_id' => '12',
                    'product_id' => '48',
                    'id' => '16',
                    'order_id' => '7',
                    'warehouse_id' => '5',
                    'product_quantity' => '5',
                    'total_discount' => '0.00',
                    'product_price' => '40.00',
                    'restock_date' => null,
                    'productID' => '34',
                    'product_title_id' => '21',
                    'product_sku' => 'ASD',
                    'product_upc' => null,
                    'product_title' => 'Always Sell Direct',
                    'total_price' => '200.00',
                    'subtotal' => '200.00',
                    'quantity_to_fulfill' => '4',
                    'inventory_transfer_id' => null,
                    'estimated_ship_date' => null,
                ],
            ],
            'orderExistsOrderItemDoesNotExist' => [
                'orderItemId' => 65462345,
                'orderId' => 174,
                'expected' => [],
            ],
            'orderExistsOrderItemExistsNotForUser' => [
                'orderItemId' => 6,
                'orderId' => 174,
                'expected' => [],
            ],
            'orderExistsOrderItemExistsNotForOrder' => [
                'orderItemId' => 211,
                'orderId' => 174,
                'expected' => [],
            ],
            'orderExistsNotForUserOrderItemExistsForOrder' => [
                'orderItemId' => 11,
                'orderId' => 12,
                'expected' => [],
            ],
            'orderExistsNotForUserOrderItemExistsForUser' => [
                'orderItemId' => 210,
                'orderId' => 5,
                'expected' => [],
            ],
            'orderDoesNotExist' => [
                'orderItemId' => 210,
                'orderId' => 8487234,
                'expected' => [],
            ],
        ];
    }
}
