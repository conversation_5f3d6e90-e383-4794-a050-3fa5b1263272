<?php
class DealerOrderProvider
{
    public static function viewProvider()
    {
        return [
            'orderExists' => [
                'orderId' => 7,
                'expected' => [
                    'order_id' => '7',
                    'id' => '12',
                    'product_total_price' => '148.48',
                    'total_price' => '119.98',
                    'total_discount' => '0.00',
                    'total_tax' => '13.50',
                    'shipping_name' => null,
                    'shipping_amount' => '15.00',
                    'shipping_tax_amount' => '0.83',
                    'fulfillment_status' => 'partially_fulfilled',
                    'shipping_lines' => [
                        0 => [
                            'service' => null,
                            'price' => '15.00',
                            'discount' => '0.00',
                            'discounted_price' => '15.00',
                            'total_tax' => '0.83',
                        ],
                    ],
                    'order_items' => [
                        0 => [
                            'dealer_order_id' => '12',
                            'product_id' => '48',
                            'id' => '16',
                            'order_id' => '7',
                            'warehouse_id' => '5',
                            'product_quantity' => '5',
                            'total_discount' => '0.00',
                            'product_price' => '40.00',
                            'restock_date' => null,
                            'productID' => '34',
                            'product_title_id' => '21',
                            'product_sku' => 'ASD',
                            'product_upc' => null,
                            'product_title' => 'Always Sell Direct',
                            'ParentDealerOrder' => [
                                'user_id' => '12',
                                'id' => '12',
                                'order_id' => '7'
                            ],
                            'ApiV1Variant' => [
                                'id' => '48',
                                'productID' => '34',
                                'product_title_id' => '21',
                                'product_sku' => 'ASD',
                                'product_upc' => null,
                                'product_title' => 'Always Sell Direct',
                            ],
                            'ApiV1DealerOrderFulfillmentProduct' => [
                                0 => [
                                    'dealer_order_product_id' => '16',
                                    'quantity' => '1',
                                ],
                            ],
                            'ApiV1DealerOrderRefundProduct' => [],
                            'total_price' => '200.00',
                            'subtotal' => '200.00',
                            'quantity_to_fulfill' => '4',
                            'inventory_transfer_id' => null,
                            'ApiV1InventoryTransfer' => [],
                            'estimated_ship_date' => null,
                        ],
                        1 => [
                            'dealer_order_id' => '12',
                            'product_id' => '53',
                            'id' => '17',
                            'order_id' => '7',
                            'warehouse_id' => '5',
                            'product_quantity' => '5',
                            'total_discount' => '0.00',
                            'product_price' => '19.99',
                            'restock_date' => null,
                            'productID' => '40',
                            'product_title_id' => '26',
                            'product_sku' => 'BLACK',
                            'product_upc' => null,
                            'product_title' => 'Test - BLACK',
                            'ParentDealerOrder' => [
                                'user_id' => '12',
                                'id' => '12',
                                'order_id' => '7'
                            ],
                            'ApiV1Variant' => [
                                'id' => '53',
                                'productID' => '40',
                                'product_title_id' => '26',
                                'product_sku' => 'BLACK',
                                'product_upc' => null,
                                'product_title' => 'Test - BLACK',
                            ],
                            'ApiV1DealerOrderFulfillmentProduct' => [],
                            'ApiV1DealerOrderRefundProduct' => [
                                0 => [
                                    'dealer_order_product_id' => '17',
                                    'quantity' => '1',
                                ],
                            ],
                            'total_price' => '99.95',
                            'subtotal' => '99.95',
                            'quantity_to_fulfill' => '4',
                            'inventory_transfer_id' => null,
                            'ApiV1InventoryTransfer' => [],
                            'estimated_ship_date' => null,
                        ],
                    ],
                    'fulfillments' => [
                        0 => [
                            'dealer_order_id' => '12',
                            'id' => '13',
                            'warehouse_id' => '5',
                            'tracking_number' => null,
                            'tracking_url' => null,
                            'ParentDealerOrder' => [
                                'user_id' => '12',
                                'id' => '12',
                                'order_id' => '7'
                            ],
                            'ApiV1DealerOrderFulfillmentProduct' => [
                                0 => [
                                    'dealer_order_product_id' => '16',
                                    'fulfillment_id' => '13',
                                    'order_product_id' => null,
                                    'id' => '17',
                                    'quantity' => '1',
                                    'ApiV1DealerOrderFulfillment' => [
                                        'id' => '13',
                                    ],
                                    'ApiV1DealerOrderItem' => [
                                        'product_id' => '48',
                                        'id' => '16',
                                        'ApiV1Variant' => [
                                            'id' => '48',
                                            'productID' => '34',
                                            'product_title_id' => '21',
                                            'product_sku' => 'ASD',
                                            'product_upc' => null,
                                            'product_title' => 'Always Sell Direct',
                                        ],
                                    ],
                                ],
                            ],
                            'ApiV1Courier' => [],
                            'items' => [
                                0 => [
                                    'dealer_order_product_id' => '16',
                                    'fulfillment_id' => '13',
                                    'order_product_id' => null,
                                    'id' => '17',
                                    'quantity' => '1',
                                    'ApiV1DealerOrderFulfillment' => [
                                        'id' => '13',
                                    ],
                                    'ApiV1DealerOrderItem' => [
                                        'product_id' => '48',
                                        'id' => '16',
                                        'ApiV1Variant' => [
                                            'id' => '48',
                                            'productID' => '34',
                                            'product_title_id' => '21',
                                            'product_sku' => 'ASD',
                                            'product_upc' => null,
                                            'product_title' => 'Always Sell Direct',
                                        ],
                                    ],
                                    'product_sku' => 'ASD',
                                    'product_upc' => null,
                                    'product_title' => 'Always Sell Direct',
                                    'productID' => '34',
                                    'product_title_id' => '21',
                                ],
                            ],
                            'courier' => null,
                        ],
                    ],
                    'refunds' => [
                        0 => [
                            'dealer_order_id' => '12',
                            'id' => '2',
                            'amount' => '36.92',
                            'shipping_portion' => '15.00',
                            'tax_portion' => '1.93',
                            'reason' => null,
                            'ParentDealerOrder' => [
                                'user_id' => '12',
                                'id' => '12',
                                'order_id' => '7'
                            ],
                            'ApiV1DealerOrderRefundProduct' => [
                                0 => [
                                    'dealer_order_product_id' => '17',
                                    'dealer_order_refund_id' => '2',
                                    'id' => '3',
                                    'quantity' => '1',
                                    'ApiV1DealerOrderRefund' => [
                                        'id' => '2',
                                    ],
                                    'ApiV1DealerOrderItem' => [
                                        'product_id' => '53',
                                        'id' => '17',
                                        'ApiV1Variant' => [
                                            'id' => '53',
                                            'productID' => '40',
                                            'product_title_id' => '26',
                                            'product_sku' => 'BLACK',
                                            'product_upc' => null,
                                            'product_title' => 'Test - BLACK',
                                        ],
                                    ],
                                ],
                            ],
                            'items' => [
                                0 => [
                                    'dealer_order_product_id' => '17',
                                    'dealer_order_refund_id' => '2',
                                    'id' => '3',
                                    'quantity' => '1',
                                    'ApiV1DealerOrderRefund' => [
                                        'id' => '2',
                                    ],
                                    'ApiV1DealerOrderItem' => [
                                        'product_id' => '53',
                                        'id' => '17',
                                        'ApiV1Variant' => [
                                            'id' => '53',
                                            'productID' => '40',
                                            'product_title_id' => '26',
                                            'product_sku' => 'BLACK',
                                            'product_upc' => null,
                                            'product_title' => 'Test - BLACK',
                                        ],
                                    ],
                                    'product_sku' => 'BLACK',
                                    'product_upc' => null,
                                    'product_title' => 'Test - BLACK',
                                    'productID' => '40',
                                    'product_title_id' => '26',
                                ],
                            ],
                        ],
                    ],
                    'total_refund' => '36.92',
                    'net_total' => '83.06',
                ],
            ],
            'orderExistsNotForUser' => [
                'orderId' => 5,
                'expected' => [],
            ],
            'orderDoesNotExist' => [
                'orderId' => 8487234,
                'expected' => [],
            ],
        ];
    }
}
