<?php

namespace ShipEarlyApp\Plugin\Api\v1\TestSuite;

use App;
use Configure;
use IntegrationTestCase;
use InvalidOperationException;
use Symfony\Component\Yaml\Yaml;

App::uses('Configure', 'Core');
App::uses('IntegrationTestCase', 'TestSuite');
App::uses('InvalidOperationException', 'Error');

/**
 * Abstract test class for ApiV1 controllers.
 *
 * {@inheritDoc}
 *
 * @package ApiV1.TestSuite
 */
abstract class ApiV1ControllerTestCase extends IntegrationTestCase
{
    public $plugin = 'Api/v1';

    public function setUp()
    {
        parent::setUp();
        Configure::write('Api/v1.debug', false);
    }

    public function generate($controller, $mocks = [])
    {
        // Prevent components, especially the rate limiter, from halting test execution.
        $mocks = array_merge_recursive([
            'components' => [
                'Api.ApiPreflightHandler' => ['_stop'],
                'Api.ApiRateLimit' => ['_stop', 'readCache', 'writeCache'],
            ],
        ], (array)$mocks);

        $controllerObj = parent::generate($controller, $mocks);

        // The generated controller tends to be missing its plugin property leading to the default model
        // being loaded as an AppModel instead of its actual class during tests.
        $controllerObj->plugin = $controllerObj->plugin ?? $this->plugin;
        if ($controllerObj->uses === [$controllerObj->modelClass]) {
            $controllerObj->uses = [$controllerObj->plugin . '.' . $controllerObj->modelClass];
        }

        return $controllerObj;
    }

    protected function configureApiRequest(string $publicKey, string $privateKey)
    {
        $this->configRequest([
            'headers' => [
                'Authorization' => 'Basic ' . base64_encode(implode(':', [$publicKey, $privateKey])),
            ],
        ]);
    }

    protected function setExpectedInvalidOperationException($message = null, $code = 404)
    {
        if (empty($message)) {
            $message = 'Invalid Operation';
        }

        $this->setExpectedException(InvalidOperationException::class, $message, $code);
    }

    protected function getResponseSchema(string $model): array
    {
        $schemaFile = $this->getSchemaFile();

        return $schemaFile['components']['responses'][$model]['content']['application/json']['schema'] + ['components' => ['schemas' => $schemaFile['components']['schemas']]];
    }

    protected function getSchemaFile()
    {
        static $schema = null;
        if ($schema === null) {
            $schema = Yaml::parseFile(APP . 'Plugin' . DS . $this->plugin . DS . 'schema.yaml');
        }

        return $schema;
    }

    /**
     * @param array $expected
     * @param array $actual
     * @return array Expected data with ignorable fields assigned the actual values.
     */
    protected function assignIgnoredExpectedFieldsFromActual(array $expected, array $actual): array
    {
        foreach ($expected as $field => $value) {
            if (is_array($value) && is_array($actual[$field] ?? null)) {
                $expected[$field] = $this->assignIgnoredExpectedFieldsFromActual($value, $actual[$field]);
            } elseif ($field === 'updated_at' || $value === '__IGNORED__') {
                $expected[$field] = $actual[$field];
            }
        }

        return $expected;
    }
}
