<?php

namespace ShipEarlyApp\Plugin\Api\v1\TestSuite;

use App;
use ClassRegistry;
use RetailerCredit;
use RuntimeException;

App::uses('RetailerCredit', 'Model');
App::uses('ClassRegistry', 'Utility');

trait ApiV1RetailerCreditsTestHelperTrait
{
    protected function seedExistingPayments(): void
    {
        $userId = 12;
        $retailerId = 15;

        /** @var RetailerCredit $RetailerCredit */
        $RetailerCredit = ClassRegistry::init('RetailerCredit');
        if (!$RetailerCredit->save([
            'id' => 1,
            'dealer_order_id' => 13,
        ])) {
            throw new RuntimeException(json_encode(['message' => 'Failed to attach order to existing credit.', 'errors' => $RetailerCredit->validationErrors]));
        }
        $RetailerCredit->clear();

        $RetailerCreditPayment = $RetailerCredit->RetailerCreditPayment;
        if (!$RetailerCreditPayment->saveMany([
            [
                'id' => 1,
                'retailer_credit_id' => 1,
                'created_by' => $userId,
                'stripe_charge_id' => '',
                'payment_status' => 'complete',
                'type' => 'credit card',
                'amount' => 99.99,
                'created_at' => '2023-08-01 00:00:00',
                'updated_at' => '2023-08-02 00:00:00',
            ],
            [
                'id' => 2,
                'retailer_credit_id' => 1,
                'created_by' => $userId,
                'stripe_charge_id' => '',
                'payment_status' => 'complete',
                'type' => 'credit',
                'amount' => 199.99,
                'created_at' => '2023-08-01 00:00:00',
                'updated_at' => '2023-08-02 00:00:00',
            ],
        ])) {
            throw new RuntimeException(json_encode(['message' => 'Failed to seed existing payments.', 'errors' => $RetailerCreditPayment->validationErrors]));
        }
        $RetailerCreditPayment->clear();

        $RetailerCreditVoucher = $RetailerCreditPayment->RetailerCreditVoucher;
        if (!$RetailerCreditVoucher->saveMany([
            [
                'id' => 1,
                'user_id' => $userId,
                'retailer_id' => $retailerId,
                'amount' => 500.00,
                'created_by' => $userId,
                'retailer_credit_payment_id' => null,
                'description' => 'Voucher for testRetailerInvoice1',
                'created_at' => '2023-08-01 00:00:00',
                'updated_at' => '2023-08-01 00:00:00',
            ],
            [
                'id' => 2,
                'user_id' => $userId,
                'retailer_id' => $retailerId,
                'amount' => -199.99,
                'created_by' => $userId,
                'retailer_credit_payment_id' => 2,
                'description' => 'Payment for testRetailerInvoice1',
                'created_at' => '2023-08-01 00:00:00',
                'updated_at' => '2023-08-01 00:00:00',
            ],
        ])) {
            throw new RuntimeException(json_encode(['message' => 'Failed to seed existing vouchers.', 'errors' => $RetailerCreditVoucher->validationErrors]));
        }
        $RetailerCreditVoucher->clear();
    }
}
