---
openapi: 3.1.0
info:
  version: '1.2'
  title: ShipEarly
  license:
    name: MIT
security:
  - basicAuth: []
servers:
  - url: https://shipearlyapp.localhost/api/v1
paths:
  /product_locator:
    get:
      parameters:
        - $ref: '#/components/parameters/zip_code'
        - $ref: '#/components/parameters/postal_code'
        - $ref: '#/components/parameters/variant_id'
        - $ref: '#/components/parameters/retailer_id'
        - $ref: '#/components/parameters/delivery_method'
      description: Get all retailers with product and fulfillment options
      responses:
        '200':
          $ref: '#/components/responses/ProductLocator'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
  /inventory_transfers:
    get:
      description: Get a list of all transfers.
      parameters:
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/limit'
        - $ref: '#/components/parameters/sort'
        - $ref: '#/components/parameters/filter'
      responses:
        '200':
          $ref: '#/components/responses/InventoryTransferList'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
    post: 
      summary: Create a transfer
      operationId: addTransfer
      tags:
        - inventory
        - transfer
      requestBody:
        $ref: '#/components/requestBodies/InventoryTransferRequest'
      responses:
        '200':
          $ref: '#/components/responses/InventoryTransfer'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
  /inventory_transfers/count:
    get:
      description: Get a count of all transfers.
      responses:
        '200':
          $ref: '#/components/responses/Count'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
  /inventory_transfers/{inventory_transfer_id}:
    parameters:
      - $ref: '#/components/parameters/inventory_transfer_id'
    get:
      description: Get a transfer.
      responses:
        '200':
          $ref: '#/components/responses/InventoryTransfer'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
    put: 
      summary: Edit a transfer
      operationId: editTransfer
      tags:
        - inventory
        - transfer
      requestBody:
        $ref: '#/components/requestBodies/InventoryTransferRequest'
      responses:
        '200': 
          $ref: '#/components/responses/InventoryTransfer'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
    delete: 
      description: Delete a transfer.
      responses:
        '200':
          $ref: '#/components/responses/Status'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
  /inventory_transfers/{inventory_transfer_id}/receive:
    parameters:
      - $ref: '#/components/parameters/inventory_transfer_id'
    post:
      description: Receive a transfer.
      responses:
        '200':
          $ref: '#/components/responses/InventoryTransfer'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
  /couriers:
    get:
      parameters:
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/limit'
        - $ref: '#/components/parameters/sort'
        - $ref: '#/components/parameters/filter'
      responses:
        '200':
          $ref: '#/components/responses/CourierList'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
  /couriers/{courier_id}:
    parameters:
      - $ref: '#/components/parameters/courier_id'
    get:
      responses:
        '200':
          $ref: '#/components/responses/Courier'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
  /couriers/count:
    get:
      description: Get count of all couriers.
      responses:
        '200':
          $ref: '#/components/responses/Count'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
  /orders/{order_id}/items/{order_item_id}:
    parameters:
      - $ref: '#/components/parameters/order_id'
      - $ref: '#/components/parameters/order_item_id'
    get:
      description: Get a specific item for specific order.
      responses:
        '200':
          $ref: '#/components/responses/OrderItem'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
    put: 
      description: Edit an item.
      responses:
        '200': 
          $ref: '#/components/responses/OrderItem'
  /orders/{order_id}/items:
    parameters:
      - $ref: '#/components/parameters/order_id'
    get:
      parameters:
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/limit'
        - $ref: '#/components/parameters/sort'
        - $ref: '#/components/parameters/filter'
      responses:
        '200':
          $ref: '#/components/responses/OrderItemList'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
    post: 
      description: Add an item to an order.
      responses:
        '200': 
          $ref: '#/components/responses/OrderItemList'
  /orders/{order_id}/refunds/{refund_id}:
    parameters:
      - $ref: '#/components/parameters/order_id'
      - $ref: '#/components/parameters/refund_id'
    get:
      description: Get a specific refund for a specific order.
      responses:
        '200':
          $ref: '#/components/responses/Refund'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
  /orders/{order_id}/refunds:
    parameters:
      - $ref: '#/components/parameters/order_id'
    get:
      parameters:
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/limit'
        - $ref: '#/components/parameters/sort'
        - $ref: '#/components/parameters/filter'
      description: Get all refunds for a specific order.
      responses:
        '200':
          $ref: '#/components/responses/RefundList'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
    post: 
      description: Create a refund.
      responses:
        '200': 
          $ref: '#/components/responses/RefundList'
  /orders/{order_id}/fulfillments/{fulfillment_id}:
    parameters:
      - $ref: '#/components/parameters/order_id'
      - $ref: '#/components/parameters/fulfillment_id'
    get:
      description: Get a specific fullfilment for specific order.
      responses:
        '200':
          $ref: '#/components/responses/Fulfillment'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
  /orders/{order_id}/fulfillments:
    parameters:
      - $ref: '#/components/parameters/order_id'
    get:
      parameters:
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/limit'
        - $ref: '#/components/parameters/sort'
        - $ref: '#/components/parameters/filter'
      description: Get all fullfilments for specific order.
      responses:
        '200':
          $ref: '#/components/responses/FulfillmentList'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
    post: 
      summary: Create a fulfillment
      operationId: addFulfillment
      tags:
        - order
        - fulfillment
      requestBody:
        $ref: '#/components/requestBodies/FulfillmentRequest'
      responses:
        '200':
          $ref: '#/components/responses/Fulfillment'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeError'
        default:
          $ref: '#/components/responses/Error'
  /orders/{order_id}:
    parameters:
      - $ref: '#/components/parameters/order_id'
    get:
      description: Get specific order.
      responses:
        '200':
          $ref: '#/components/responses/Order'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
    put:
      summary: Edit an order
      operationId: editOrder
      tags:
        - Order
      requestBody:
        $ref: '#/components/requestBodies/OrderRequest'
      responses:
        '200':
          $ref: '#/components/responses/Order'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeError'
        default:
          $ref: '#/components/responses/Error'
  /orders:
    get:
      parameters:
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/limit'
        - $ref: '#/components/parameters/sort'
        - $ref: '#/components/parameters/filter'
      description: Get all orders.
      responses:
        '200':
          $ref: '#/components/responses/OrderList'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
    post: 
      description: Create an order.
      requestBody:
        $ref: '#/components/requestBodies/OrderRequest'
      responses:
        '200':
          $ref: '#/components/responses/Order'
  /orders/count:
    get:
      description: Get count of all orders.
      responses:
        '200':
          $ref: '#/components/responses/Count'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
  '/territories':
    parameters:
      - $ref: '#/components/parameters/page'
      - $ref: '#/components/parameters/limit'
      - $ref: '#/components/parameters/sort'
      - $ref: '#/components/parameters/filter'
    get:
      summary: Get all territories
      operationId: listTerritories
      tags:
        - territories
      responses:
        '200':
          $ref: '#/components/responses/TerritoryList'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
        default:
          $ref: '#/components/responses/Error'
  '/territories/count':
    get:
      summary: Get count of all territories
      operationId: countTerritories
      tags:
        - territories
      responses:
        '200':
          $ref: '#/components/responses/Count'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
        default:
          $ref: '#/components/responses/Error'
  '/territories/{territory_id}':
    parameters:
      - $ref: '#/components/parameters/territory_id'
    get:
      summary: Get details for specific territory
      operationId: getTerritory
      tags:
        - territories
      responses:
        '200':
          $ref: '#/components/responses/Territory'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
        default:
          $ref: '#/components/responses/Error'
  '/sales_reps':
    parameters:
      - $ref: '#/components/parameters/page'
      - $ref: '#/components/parameters/limit'
      - $ref: '#/components/parameters/sort'
      - $ref: '#/components/parameters/filter'
    get:
      summary: Get all sales reps
      operationId: listSalesReps
      tags:
        - sales reps
      responses:
        '200':
          $ref: '#/components/responses/SalesRepList'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
        default:
          $ref: '#/components/responses/Error'
  '/sales_reps/count':
    get:
      summary: Get count of all sales reps
      operationId: countSalesReps
      tags:
        - sales reps
      responses:
        '200':
          $ref: '#/components/responses/Count'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
        default:
          $ref: '#/components/responses/Error'
  '/sales_reps/{sales_rep_id}':
    parameters:
      - $ref: '#/components/parameters/sales_rep_id'
    get:
      summary: Get details for specific sales rep
      operationId: getSalesRep
      tags:
        - sales reps
      responses:
        '200':
          $ref: '#/components/responses/SalesRep'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
        default:
          $ref: '#/components/responses/Error'
    put:
      summary: Edit a sales rep
      operationId: editSalesRep
      tags:
        - sales reps
      requestBody:
        $ref: '#/components/requestBodies/SalesRepRequest'
      responses:
        '200':
          $ref: '#/components/responses/SalesRep'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeError'
        default:
          $ref: '#/components/responses/Error'
  '/products':
    parameters:
      - $ref: '#/components/parameters/page'
      - $ref: '#/components/parameters/limit'
      - $ref: '#/components/parameters/sort'
      - $ref: '#/components/parameters/filter'
    get:
      summary: Get details for all products
      operationId: listProducts
      tags:
        - product
      responses:
        '200':
          $ref: '#/components/responses/ProductList'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
        default:
          $ref: '#/components/responses/Error'
  '/products/count':
    get:
      summary: Get count of all products
      operationId: countProducts
      tags:
        - product
      responses:
        '200':
          $ref: '#/components/responses/Count'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
        default:
          $ref: '#/components/responses/Error'
  '/products/{product_id}':
    parameters:
      - $ref: '#/components/parameters/product_id'
    get:
      summary: Get details for specific product
      operationId: getProduct
      tags:
        - product
      responses:
        '200':
          $ref: '#/components/responses/Product'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
        default:
          $ref: '#/components/responses/Error'
  '/variants':
    parameters:
      - $ref: '#/components/parameters/page'
      - $ref: '#/components/parameters/limit'
      - $ref: '#/components/parameters/sort'
      - $ref: '#/components/parameters/filter'
    get:
      summary: Get details for all variants
      operationId: listVariants
      tags:
        - product
        - variant
      responses:
        '200':
          $ref: '#/components/responses/VariantList'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
        default:
          $ref: '#/components/responses/Error'
  '/variants/count':
    get:
      summary: Get count of all variants
      operationId: countVariants
      tags:
        - product
        - variant
      responses:
        '200':
          $ref: '#/components/responses/Count'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
        default:
          $ref: '#/components/responses/Error'
  '/variants/{variant_id}':
    parameters:
      - $ref: '#/components/parameters/variant_id'
    get:
      summary: Get details for specific variant
      operationId: getVariant
      tags:
        - product
        - variant
      responses:
        '200':
          $ref: '#/components/responses/Variant'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
        default:
          $ref: '#/components/responses/Error'
  '/variants/{variant_id}/pricing_tiers':
    parameters:
      - $ref: '#/components/parameters/variant_id'
    get:
      parameters:
        - $ref: '#/components/parameters/page'
        - $ref: '#/components/parameters/limit'
        - $ref: '#/components/parameters/sort'
        - $ref: '#/components/parameters/filter'
      summary: Get pricing details for a specfic variant.
      operationId: listVariantPricingTier
      tags:
        - product
        - variant
      responses:
        '200':
          $ref: '#/components/responses/VariantTierList'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
        default:
          $ref: '#/components/responses/Error'
    post:
      summary: Add pricing tier details for a specfic variant and pricing tier
      operationId: addVariantPricingTier
      tags:
        - product
        - variant
      requestBody:
        $ref: '#/components/requestBodies/VariantTierRequest'
      responses:
        '200':
          $ref: '#/components/responses/VariantTier'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeError'
        default:
          $ref: '#/components/responses/Error'
  '/variants/{variant_id}/pricing_tiers/{pricing_tier_id}':
    parameters:
      - $ref: '#/components/parameters/variant_id'
      - $ref: '#/components/parameters/pricing_tier_id'
    get:
      summary: Get pricing tier details for a specfic variant and pricing tier
      operationId: getVariantPricingTier
      tags:
        - product
        - variant
      responses:
        '200':
          $ref: '#/components/responses/VariantTier'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
        default:
          $ref: '#/components/responses/Error'
    put:
      summary: Edit pricing tier details for a specfic variant and pricing tier
      operationId: editVariantPricingTier
      tags:
        - product
        - variant
      requestBody:
        $ref: '#/components/requestBodies/VariantTierRequest'
      responses:
        '200':
          $ref: '#/components/responses/VariantTier'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeError'
        default:
          $ref: '#/components/responses/Error'
  '/variants/{variant_id}/retail_inventory':
    parameters:
      - $ref: '#/components/parameters/page'
      - $ref: '#/components/parameters/limit'
      - $ref: '#/components/parameters/sort'
      - $ref: '#/components/parameters/filter'
      - $ref: '#/components/parameters/variant_id'
    get:
      summary: Get inventory details for specific variant
      operationId: getVariantInventory
      tags:
        - product
        - variant
        - inventory
      responses:
        '200':
          $ref: '#/components/responses/RetailInventoryList'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
        default:
          $ref: '#/components/responses/Error'
  '/variants/{variant_id}/retail_inventory/count':
    parameters:
      - $ref: '#/components/parameters/variant_id'
    get:
      summary: Get count of all retailers with variant in stock
      operationId: countVariantInventory
      tags:
        - product
        - variant
        - inventory
        - retailer
      responses:
        '200':
          $ref: '#/components/responses/Count'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
        default:
          $ref: '#/components/responses/Error'
  '/variants/{variant_id}/retail_inventory/{retailer_id}':
    parameters:
      - $ref: '#/components/parameters/variant_id'
      - $ref: '#/components/parameters/retailer_id'
    get:
      summary: Get inventory details of specific retailer for specific variant
      operationId: getVariantInventoryForRetailer
      tags:
        - product
        - variant
        - inventory
        - retailer
      responses:
        '200':
          $ref: '#/components/responses/RetailInventory'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
        default:
          $ref: '#/components/responses/Error'
  '/retailers':
    parameters:
      - $ref: '#/components/parameters/page'
      - $ref: '#/components/parameters/limit'
      - $ref: '#/components/parameters/sort'
      - $ref: '#/components/parameters/filter'
    get:
      summary: Get details for all retailers
      operationId: listRetailers
      tags:
        - retailer
      responses:
        '200':
          $ref: '#/components/responses/RetailerList'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
        default:
          $ref: '#/components/responses/Error'
  '/retailers/count':
    get:
      summary: Get count of all retailers
      operationId: countRetailers
      tags:
        - retailer
      responses:
        '200':
          $ref: '#/components/responses/Count'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
        default:
          $ref: '#/components/responses/Error'
  '/retailers/{retailer_id}':
    parameters:
      - $ref: '#/components/parameters/retailer_id'
    get:
      summary: Get details for specific retailer
      operationId: getRetailer
      tags:
        - retailer
      responses:
        '200':
          $ref: '#/components/responses/Retailer'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
        default:
          $ref: '#/components/responses/Error'
    put:
      summary: Edit a retailer
      operationId: editRetailer
      tags:
        - retailer
      requestBody:
        $ref: '#/components/requestBodies/RetailerRequest'
      responses:
        '200':
          $ref: '#/components/responses/Retailer'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeError'
        default:
          $ref: '#/components/responses/Error'
  '/retailers/{retailer_id}/branches':
    parameters:
      - $ref: '#/components/parameters/retailer_id'
    get:
      summary: List all branches for specific retailer
      operationId: listRetailerBranches
      tags:
        - retailer
        - branch
      responses:
        '200':
          $ref: '#/components/responses/RetailerBranchList'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
        default:
          $ref: '#/components/responses/Error'
  '/retailers/{retailer_id}/branches/{branch_id}':
    parameters:
      - $ref: '#/components/parameters/retailer_id'
      - $ref: '#/components/parameters/branch_id'
    get:
      summary: Get a specific branch for a retailer
      operationId: getRetailerBranch
      tags:
        - retailer
        - branch
      responses:
        '200':
          $ref: '#/components/responses/RetailerBranch'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
        default:
          $ref: '#/components/responses/Error'
  '/retailers/{retailer_id}/credits':
    parameters:
      - $ref: '#/components/parameters/retailer_id'
    get:
      summary: Get all credits for specific retailer
      operationId: getRetailerCredits
      tags:
        - retailer
        - credit
      responses:
        '200':
          $ref: '#/components/responses/RetailerCreditList'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
        default:
          $ref: '#/components/responses/Error'
    put:
      summary: Edit a retailer branch
      operationId: editRetailerBranch
      tags:
        - retailer
        - branches
      requestBody:
        $ref: '#/components/requestBodies/RetailerRequest'
      responses:
        '200':
          $ref: '#/components/responses/RetailerBranch'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeError'
        default:
          $ref: '#/components/responses/Error'
    post:
      summary: create a retailer
      operationId: createRetailerCredit
      tags:
        - retailer
        - credit
      requestBody:
        $ref: '#/components/requestBodies/RetailerCreditCreateRequest'
      responses:
        '200':
          $ref: '#/components/responses/RetailerCredit'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeError'
        default:
          $ref: '#/components/responses/Error'
  '/retailers/{retailer_id}/credits/count':
    get:
      summary: Get count of credits for a specific retailer
      operationId: countRetailerCredits
      tags:
        - retailer
        - credits
      responses:
        '200':
          $ref: '#/components/responses/Count'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
        default:
          $ref: '#/components/responses/Error'
  '/retailers/{retailer_id}/credits/{credit_id}':
    parameters:
      - $ref: '#/components/parameters/retailer_id'
      - $ref: '#/components/parameters/credit_id'
    get:
      summary: Get details for specific retailer credit
      operationId: getRetailer
      tags:
        - retailer
        - credits
      responses:
        '200':
          $ref: '#/components/responses/RetailerCredit'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
        default:
          $ref: '#/components/responses/Error'
    put:
      summary: Edit a retailer credit
      operationId: editRetailerCredit
      tags:
        - retailer
        - credits
      requestBody:
        $ref: '#/components/requestBodies/RetailerCreditEditRequest'
      responses:
        '200':
          $ref: '#/components/responses/RetailerCredit'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeError'
        default:
          $ref: '#/components/responses/Error'
  '/retailers/{retailer_id}/credits/{credit_id}/items':
    parameters:
      - $ref: '#/components/parameters/retailer_id'
      - $ref: '#/components/parameters/credit_id'
    get:
      summary: Get all items for specific retailer credit
      operationId: listRetailerCreditItems
      tags:
        - retailer
        - credits
      responses:
        '200':
          $ref: '#/components/responses/RetailerCreditItemList'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
        default:
          $ref: '#/components/responses/Error'
  '/retailers/{retailer_id}/credits/{credit_id}/items/{item_id}':
    parameters:
      - $ref: '#/components/parameters/retailer_id'
      - $ref: '#/components/parameters/credit_id'
      - $ref: '#/components/parameters/item_id'
    get:
      summary: Get details for specific retailer credit item
      operationId: getRetailerCreditItem
      tags:
        - retailer
      responses:
        '200':
          $ref: '#/components/responses/RetailerCreditItem'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
        default:
          $ref: '#/components/responses/Error'
  '/retailers/{retailer_id}/credits/{credit_id}/items/count':
    parameters:
      - $ref: '#/components/parameters/retailer_id'
      - $ref: '#/components/parameters/credit_id'
    get:
      summary: Get count of items for specific retailer credit
      operationId: countRetailerCreditItems
      tags:
        - retailer
        - credits
      responses:
        '200':
          $ref: '#/components/responses/Count'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
        default:
          $ref: '#/components/responses/Error'
  '/retailers/{retailer_id}/credits/{credit_id}/payments':
    parameters:
      - $ref: '#/components/parameters/retailer_id'
      - $ref: '#/components/parameters/credit_id'
    get:
      summary: Get payments for specific retailer credit
      operationId: listRetailerCreditPayments
      tags:
        - retailer
        - credits
      responses:
        '200':
          $ref: '#/components/responses/RetailerCreditPaymentList'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
        default:
          $ref: '#/components/responses/Error'
    post:
      summary: create a retailer credit payment
      operationId: createRetailerCreditPayment
      tags:
        - retailer
        - credit
      requestBody:
        $ref: '#/components/requestBodies/RetailerCreditPaymentRequest'
      responses:
        '200':
          $ref: '#/components/responses/RetailerCredit'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeError'
        default:
          $ref: '#/components/responses/Error'
  '/retailers/{retailer_id}/credits/{credit_id}/payments/{payment_id}':
    parameters:
      - $ref: '#/components/parameters/retailer_id'
      - $ref: '#/components/parameters/credit_id'
      - $ref: '#/components/parameters/payment_id'
    get:
      summary: Get details for a specific retailer credit payment
      operationId: getRetailerCreditPayment
      tags:
        - retailer
        - credit
      responses:
        '200':
          $ref: '#/components/responses/RetailerCreditPayment'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
        default:
          $ref: '#/components/responses/Error'
  '/retailers/{retailer_id}/credits/{credit_id}/payments/count':
    get:
      summary: Get count of payments for a specific retailer credit
      operationId: countRetailerCreditPayments
      tags:
        - retailer
        - credits
      responses:
        '200':
          $ref: '#/components/responses/Count'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
        default:
          $ref: '#/components/responses/Error'
  '/retailers/{retailer_id}/inventory':
    parameters:
      - $ref: '#/components/parameters/page'
      - $ref: '#/components/parameters/limit'
      - $ref: '#/components/parameters/sort'
      - $ref: '#/components/parameters/filter'
      - $ref: '#/components/parameters/retailer_id'
    get:
      summary: Get inventory details for specific retailer
      operationId: getRetailInventory
      tags:
        - retailer
        - variant
        - inventory
      responses:
        '200':
          $ref: '#/components/responses/RetailInventoryList'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
        default:
          $ref: '#/components/responses/Error'
  '/retailers/{retailer_id}/inventory/count':
    parameters:
      - $ref: '#/components/parameters/retailer_id'
    get:
      summary: Get count of all variants retailer has in inventory
      operationId: countRetailInventoryVariants
      tags:
        - retailer
        - variant
        - inventory
      responses:
        '200':
          $ref: '#/components/responses/Count'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
        default:
          $ref: '#/components/responses/Error'
  '/retailers/{retailer_id}/inventory/{variant_id}':
    parameters:
      - $ref: '#/components/parameters/variant_id'
      - $ref: '#/components/parameters/retailer_id'
    get:
      summary: Get inventory details of specific retailer for specific variant
      operationId: getRetailInventoryForVariant
      tags:
        - product
        - variant
        - inventory
        - retailer
      responses:
        '200':
          $ref: '#/components/responses/RetailInventory'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
        default:
          $ref: '#/components/responses/Error'
  '/retail_inventory':
    parameters:
      - $ref: '#/components/parameters/page'
      - $ref: '#/components/parameters/limit'
      - $ref: '#/components/parameters/sort'
      - $ref: '#/components/parameters/filter'
    get:
      summary: Get inventory details for all retailers
      operationId: getAllRetailerInventories
      tags:
        - retailer
        - variant
        - inventory
      responses:
        '200':
          $ref: '#/components/responses/RetailInventoryList'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
        default:
          $ref: '#/components/responses/Error'
  '/retail_inventory/count':
    get:
      summary: Get count of all variants in retailer inventories
      operationId: countAllRetailInventoryVariants
      tags:
        - retailer
        - variant
        - inventory
      responses:
        '200':
          $ref: '#/components/responses/Count'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
        default:
          $ref: '#/components/responses/Error'
  '/pricing_tiers':
    parameters:
      - $ref: '#/components/parameters/page'
      - $ref: '#/components/parameters/limit'
      - $ref: '#/components/parameters/sort'
      - $ref: '#/components/parameters/filter'
    get:
      summary: Get details for all pricing tiers.
      operationId: listPricingTiers
      tags:
        - pricing tier
      responses:
        '200':
          $ref: '#/components/responses/PricingTierList'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
        default:
          $ref: '#/components/responses/Error'
    post:
      summary: Add a pricing tier
      operationId: addPricingTier
      tags:
        - product
      requestBody:
        $ref: '#/components/requestBodies/PricingTierRequest'
      responses:
        '200':
          $ref: '#/components/responses/PricingTier'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeError'
        default:
          $ref: '#/components/responses/Error'
  '/pricing_tiers/count':
    get:
      summary: Get count of all pricing tiers
      operationId: countPricingTiers
      tags:
        - pricing tier
      responses:
        '200':
          $ref: '#/components/responses/Count'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
        default:
          $ref: '#/components/responses/Error'
  '/pricing_tiers/{pricing_tier_id}':
    parameters:
      - $ref: '#/components/parameters/pricing_tier_id'
    get:
      summary: Get details for specific pricing tier
      operationId: getPricingTier
      tags:
        - product
      responses:
        '200':
          $ref: '#/components/responses/PricingTier'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
        default:
          $ref: '#/components/responses/Error'
    put:
      summary: Edit a pricing tier
      operationId: editPricingTier
      tags:
        - product
      requestBody:
        $ref: '#/components/requestBodies/PricingTierRequest'
      responses:
        '200':
          $ref: '#/components/responses/PricingTier'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeError'
        default:
          $ref: '#/components/responses/Error'
  '/warehouses/{warehouse_id}':
    parameters:
      - $ref: '#/components/parameters/warehouse_id'
    get:
      summary: Get details for specific warehouse.
      operationId: getWarehouse
      tags:
        - warehouse
      responses:
        '200':
          $ref: '#/components/responses/Warehouse'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
        default:
          $ref: '#/components/responses/Error'
  '/warehouses/{warehouse_id}/inventory_transfers':
    parameters:
      - $ref: '#/components/parameters/warehouse_id'
    get:
      summary: Get inventory transfers for specific warehouse.
      operationId: getWarehouseInventoryTransfers
      tags:
        - warehouse
      responses:
        '200':
          $ref: '#/components/responses/InventoryTransferList'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
        default:
          $ref: '#/components/responses/Error'
  '/warehouses':
    parameters:
      - $ref: '#/components/parameters/page'
      - $ref: '#/components/parameters/limit'
      - $ref: '#/components/parameters/sort'
      - $ref: '#/components/parameters/filter'
    get:
      summary: Get list of all warehouse.
      operationId: listWarehouse
      tags:
        - warehouse
      responses:
        '200':
          $ref: '#/components/responses/WarehouseList'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
        default:
          $ref: '#/components/responses/Error'
  '/warehouses/count':
    get:
      summary: Get count of all warehouses
      operationId: countWarehouses
      tags:
        - warehouse
      responses:
        '200':
          $ref: '#/components/responses/Count'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '429':
          $ref: '#/components/responses/TooManyRequestsError'
        default:
          $ref: '#/components/responses/Error'
components:
  requestBodies:
    PricingTierRequest:
      description: edit pricing tier request body
      content:
        'application/json':
          schema:
            $ref: '#/components/schemas/PricingTierRequest'
          example:
            name: 'Tier 1 Pricing'
            currency_code: 'USD'
    VariantTierRequest:
      description: edit variant pricing tier request body
      content:
        'application/json':
          schema:
            $ref: '#/components/schemas/VariantTierRequest'
          example:
            dealer_price: 350.00
            ship_to_store_price: 435.50
    SalesRepRequest:
      description: edit sales rep request body
      content:
        'application/json':
          schema:
            $ref: '#/components/schemas/SalesRepRequest'
          example:
            descriptor: 'Company A sales rep.'
            company_name: 'Company A'
            first_name: 'Bob'
            last_name: 'Smith'
            phone: '************'
            is_distributor: false
    RetailerRequest:
      description: retailer edit request body
      content:
        'application/json':
          schema:
            $ref: '#/components/schemas/RetailerRequest'
          example:
            pricing_tier_id: 27873
            account_id: '**********'
            territory_id: 2314
            default_warehouse_id: 53433
            billing_company: 'Company A'
            billing_email: '<EMAIL>'
            dealer_protect_radius: '300'
            local_delivery_radius: '150'
            credit_limit: '80000'
            enable_credit_card: true
            enable_on_file_payment: false
    OrderRequest:
      description: New order to create.
      content:
        'application/json':
          schema:
            $ref: '#/components/schemas/OrderRequest'
          example:
            requested_ship_date: '2020-04-13'
            internal_order_number: '#2826109'
    FulfillmentRequest:
      description: Fulfillment details.
      content:
        'application/json':
          schema:
            $ref: '#/components/schemas/FulfillmentRequest'
          example:
            warehouse_id: 4373
            courier_id: 3445
            tracking_number: 'CourierTrackingNumber12345'
            tracking_url: 'https://tracking.courier.com/CourierTrackingNumber12345'
            add_to_ecommerce: true
            items:
              - order_item_id: 762372
                quantity: 5
              - order_item_id: 837223
                quantity: 10
    InventoryTransferRequest:
      description: Inventory transfer details.
      content:
        'application/json':
          schema:
            $ref: '#/components/schemas/InventoryTransferRequest'
          example:
            reference_id: 'TRAN10293'
            expected_arrival_date: '2021-07-20'
            destination_warehouse_id: 4
            products:
            - variant_id: 36804550000803
              quantity: 1000
            - variant_id: 23450892993843
              quantity: 2500
    RetailerCreditCreateRequest:
      description: Retailer Credit details with required create fields.
      content:
        'application/json':
          schema:
            $ref: '#/components/schemas/RetailerCreditCreateRequest'
          example:
            invoice_number: IN#002312
            credit_date: '2022-01-12'
            credit_term_id: 64
            total: 1423.56
            description: Invoice for Company A restock
    RetailerCreditEditRequest:
      description: Retailer Credit details with only editable fields.
      content:
        'application/json':
          schema:
            $ref: '#/components/schemas/RetailerCreditEditRequest'
          example:
            invoice_number: IN#002312
            due_date: '2022-02-12'
    RetailerCreditPaymentRequest:
      description: Retailer Credit Payment details.
      content:
        'application/json':
          schema:
            $ref: '#/components/schemas/RetailerCreditPaymentRequest'
          example:
            amount: 100.00
  responses:
    Error:
      description: error
      headers:
        'RateLimit-Limit':
          $ref: '#/components/headers/RateLimit-Limit'
        'RateLimit-Remaining':
          $ref: '#/components/headers/RateLimit-Remaining'
        'RateLimit-Reset':
          $ref: '#/components/headers/RateLimit-Reset'
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            name: 'Authentication Error'
            message: 'Authentication Error'
            url: '/api/v1/products'
            data:
              Authentication Error: 'Authentication Error'
    BadRequestError:
      description: error
      headers:
        'RateLimit-Limit':
          $ref: '#/components/headers/RateLimit-Limit'
        'RateLimit-Remaining':
          $ref: '#/components/headers/RateLimit-Remaining'
        'RateLimit-Reset':
          $ref: '#/components/headers/RateLimit-Reset'
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            name: 'Unauthorized Error'
            message: 'Unauthorized Error'
            url: '/api/v1/products'
            data:
              invalid argument: 'Invalid argument: foo Value: bar'
    UnauthorizedError:
      description: error
      headers:
        'RateLimit-Limit':
          $ref: '#/components/headers/RateLimit-Limit'
        'RateLimit-Remaining':
          $ref: '#/components/headers/RateLimit-Remaining'
        'RateLimit-Reset':
          $ref: '#/components/headers/RateLimit-Reset'
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            name: 'Unauthorized Error'
            message: 'Unauthorized Error'
            url: '/api/v1/products'
            data:
              Unauthorized Error: 'Unauthorized Error'
    ForbiddenError:
      description: error
      headers:
        'RateLimit-Limit':
          $ref: '#/components/headers/RateLimit-Limit'
        'RateLimit-Remaining':
          $ref: '#/components/headers/RateLimit-Remaining'
        'RateLimit-Reset':
          $ref: '#/components/headers/RateLimit-Reset'
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            name: 'Forbidden Error'
            message: 'Forbidden Error'
            url: '/api/v1/products'
            data:
              Forbidden Error: 'Forbidden Error'
    NotFoundError:
      description: error
      headers:
        'RateLimit-Limit':
          $ref: '#/components/headers/RateLimit-Limit'
        'RateLimit-Remaining':
          $ref: '#/components/headers/RateLimit-Remaining'
        'RateLimit-Reset':
          $ref: '#/components/headers/RateLimit-Reset'
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            name: 'Not Found Error'
            message: 'Not Found Error'
            url: '/api/v1/products'
            data:
              Not Found Error: 'Not Found Error'
    TooManyRequestsError:
      description: error
      headers:
        'RateLimit-Limit':
          $ref: '#/components/headers/RateLimit-Limit'
        'RateLimit-Remaining':
          $ref: '#/components/headers/RateLimit-Remaining'
        'RateLimit-Reset':
          $ref: '#/components/headers/RateLimit-Reset'
        'Retry-After':
          $ref: '#/components/headers/Retry-After'
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            name: 'Too Many Requests Error'
            message: 'Too Many Requests Error'
            url: '/api/v1/products'
            data:
              Too Many Requests Error: 'Too Many Requests Error'
    UnsupportedMediaTypeError:
      description: error
      headers:
        'RateLimit-Limit':
          $ref: '#/components/headers/RateLimit-Limit'
        'RateLimit-Remaining':
          $ref: '#/components/headers/RateLimit-Remaining'
        'RateLimit-Reset':
          $ref: '#/components/headers/RateLimit-Reset'
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            name: 'Unsupported Media Type Error'
            message: 'Unsupported Media Type Error'
            url: '/api/v1/products'
            data:
              Unsupported Media Type Error: 'Unsupported Media Type Error'
    Refund:
      description: Details of a specific Refund.
      headers:
        'RateLimit-Limit':
          $ref: '#/components/headers/RateLimit-Limit'
        'RateLimit-Remaining':
          $ref: '#/components/headers/RateLimit-Remaining'
        'RateLimit-Reset':
          $ref: '#/components/headers/RateLimit-Reset'
      content:
        application/json:
          schema:
            type: object
            additionalProperties: false
            minProperties: 1
            properties:
              data:
                $ref: '#/components/schemas/Refund'
          example:
            id: '23732'
            items:
              - id: '82933'
                product_id: '43452'
                variant_id: '65562'
                sku: '82938278123'
                upc: '92000182633'
                title: 'product'
                quantity_refunded: '4'
            amount: '802.00'
            shipping_amount: '0.00'
            tax_amount: '0.00'
            created_at: '2020-04-13 12:30:09'
    RefundList:
      description: List of refunds.
      headers:
        'RateLimit-Limit':
          $ref: '#/components/headers/RateLimit-Limit'
        'RateLimit-Remaining':
          $ref: '#/components/headers/RateLimit-Remaining'
        'RateLimit-Reset':
          $ref: '#/components/headers/RateLimit-Reset'
      content:
        application/json:
          schema:
            additionalProperties: false
            minProperties: 1
            type: object
            properties:
              data:
                maxItems: 250
                type: array
                items:
                  $ref: '#/components/schemas/Refund'
          example:
            - id: '23732'
              items:
                - id: '82933'
                  product_id: '43452'
                  variant_id: '65562'
                  sku: '82938278123'
                  upc: '92000182633'
                  title: 'product1'
                  quantity_refunded: '4'
                  price: '200.50'
                  total_refunded: '802.00'
              amount: '802.00'
              shipping_amount: '0.00'
              tax_amount: '0.00'
              created_at: '2020-04-13 12:30:09'
            - id: '23733'
              items:
                - id: '82842'
                  product_id: '43453'
                  variant_id: '65564'
                  sku: '29481241241'
                  upc: '63627844562'
                  title: 'product2'
                  quantity_refunded: '2'
                  price: '25.00'
                  total_refunded: '50.00'
              amount: '75.00'
              shipping_amount: '15.00'
              tax_amount: '10.00'
              created_at: '2020-04-13 12:30:09'
    OrderItem:
      description: Details of a specific order item.
      headers:
        'RateLimit-Limit':
          $ref: '#/components/headers/RateLimit-Limit'
        'RateLimit-Remaining':
          $ref: '#/components/headers/RateLimit-Remaining'
        'RateLimit-Reset':
          $ref: '#/components/headers/RateLimit-Reset'
      content:
        application/json:
          schema:
            type: object
            additionalProperties: false
            minProperties: 1
            properties:
              data:
                $ref: '#/components/schemas/OrderItem'
          example:
            id: '167935'
            product_id: '891272'
            variant_id: '891272'
            sku: '*********'
            upc: '*********'
            title: 'product'
            quantity: '5'
            quantity_to_fulfill: '5'
            total_discount: '0.00'
            price: '250.00'
            tax_amount: '20.00'
            total_price: '270.00'
            created_at: '2020-04-13 12:31:53'
            updated_at: '2020-04-13 12:31:53'
    OrderItemList:
      description: List of order items.
      headers:
        'RateLimit-Limit':
          $ref: '#/components/headers/RateLimit-Limit'
        'RateLimit-Remaining':
          $ref: '#/components/headers/RateLimit-Remaining'
        'RateLimit-Reset':
          $ref: '#/components/headers/RateLimit-Reset'
      content:
        application/json:
          schema:
            additionalProperties: false
            minProperties: 1
            type: object
            properties:
              data:
                type: array
                maxItems: 250
                items: 
                  $ref: '#/components/schemas/OrderItem'
          example:
            - id: '167935'
              product_id: '891272'
              variant_id: '891272'
              sku: '*********'
              upc: '*********'
              title: 'product1'
              quantity: '5'
              quantity_to_fulfill: '5'
              total_discount: '0.00'
              price: '250.00'
              tax_amount: '20.00'
              total_price: '1270.00'
              created_at: '2020-04-13 12:31:53'
              updated_at: '2020-04-13 12:31:53'
            - id: '562363'
              product_id: '4223457'
              variant_id: '3452346'
              sku: '*********'
              upc: '*********'
              title: 'product2'
              quantity: '6'
              quantity_to_fulfill: '3'
              total_discount: '10.00'
              price: '150.00'
              tax_amount: '15.00'
              total_price: '455.00'
              created_at: '2020-04-13 12:32:53'
              updated_at: '2020-04-13 12:32:53'
    FulfillmentList:
      description: List of fulfillments.
      headers:
        'RateLimit-Limit':
          $ref: '#/components/headers/RateLimit-Limit'
        'RateLimit-Remaining':
          $ref: '#/components/headers/RateLimit-Remaining'
        'RateLimit-Reset':
          $ref: '#/components/headers/RateLimit-Reset'
      content:
        application/json:
          schema:
            additionalProperties: false
            minProperties: 1
            type: object
            properties:
              data:
                maxItems: 250
                type: array
                items:
                  $ref: '#/components/schemas/Fulfillment'
          example:
            - id: '128273'
              warehouse_id: '28328'
              courier: 'Fedex'
              tracking_number: 'RA972372788US'
              items:
                - id: '128974'
                  product_id: '4223457'
                  variant_id: '3452346'
                  sku: '*********'
                  upc: '*********'
                  title: 'product2'
                  fulfilled_quantity: '2'
              created_at: '2020-04-13 12:31:53'
            - id: '128274'
              warehouse_id: '28328'
              courier: 'Fedex'
              tracking_number: 'RA328517597US'
              items:
                - id: '128975'
                  product_id: '1245133'
                  variant_id: '5347383'
                  sku: '983459834'
                  upc: '290302445'
                  title: 'product3'
                  fulfilled_quantity: '2'
                - id: '927832'
                  product_id: '4624356'
                  variant_id: '3783456'
                  sku: '23457227'
                  upc: '2372373'
                  title: 'product4'
                  fulfilled_quantity: '2'
              created_at: '2020-04-13 12:32:53'
    Fulfillment:
      description: Details of a specific fulfillment.
      headers:
        'RateLimit-Limit':
          $ref: '#/components/headers/RateLimit-Limit'
        'RateLimit-Remaining':
          $ref: '#/components/headers/RateLimit-Remaining'
        'RateLimit-Reset':
          $ref: '#/components/headers/RateLimit-Reset'
      content:
        application/json:
          schema:
            type: object
            additionalProperties: false
            minProperties: 1
            properties:
              data:
                $ref: '#/components/schemas/Fulfillment'
          example:
            id: '128274'
            warehouse_id: '28328'
            courier: 'Fedex'
            tracking_number: 'RA328517597US'
            items:
              - id: '128975'
                product_id: '1245133'
                variant_id: '5347383'
                sku: '983459834'
                upc: '290302445'
                title: 'product3'
                fulfilled_quantity: '2'
              - id: '927832'
                product_id: '4624356'
                variant_id: '3783456'
                sku: '23457227'
                upc: '2372373'
                title: 'product4'
                fulfilled_quantity: '2'
            created_at: '2020-04-13 12:32:53'
    Order:
      description: Details of a specific order.
      headers:
        'RateLimit-Limit':
          $ref: '#/components/headers/RateLimit-Limit'
        'RateLimit-Remaining':
          $ref: '#/components/headers/RateLimit-Remaining'
        'RateLimit-Reset':
          $ref: '#/components/headers/RateLimit-Reset'
      content:
        application/json:
          schema:
            type: object
            additionalProperties: false
            minProperties: 1
            properties:
              data:
                $ref: '#/components/schemas/Order'
          example: 
            id: '1290837'
            customer:
              first_name: 'Andy'
              last_name: 'Hamilton'
              email_address: '<EMAIL>'
              accepts_marketing: false
              order_count: '5'
              total_spent: '482.72'
              average_order: '96.54'
            billing_address:
              first_name: 'Andy'
              last_name: 'Hamilton'
              company_name: 'AHam Industries'
              address1: '2702 Columbia Boulevard'
              address2: null
              city: 'Baltimore'
              state: 'Maryland'
              country: 'United States'
              zip_code: '21202'
              latitude: '39.237093'
              longitude: '-76.682579'
            shipping_address:
              first_name: 'Andy'
              last_name: 'Hamilton'
              company_name: 'AHam Industries'
              address1: '2702 Columbia Boulevard'
              address2: null
              city: 'Baltimore'
              state: 'Maryland'
              country: 'United States'
              zip_code: '21202'
              latitude: '39.237093'
              longitude: '-76.682579'
            retailer:
              id: '25600'
              account_id: '********'
              status: 'Connected'
              company_name: 'Company A'
              address1: '4094 Garfield Road'
              address2: 'Suite 309'
              city: 'Chicago'
              state_code: 'IL'
              zip_code: '61614'
              country_code: 'US'
              latitude: '41.853196'
              longitude: '-87.601958'
              pricing_tier_id: '7283'
              sales_reps:
                - '28767'
                - '82632'
              territory_id: '782763'
              created_at: '2020-04-13 05:06:37'
              updated_at: '2020-11-30 22:42:11'
              dealer_protect_radius: '50'
              billing_email: '<EMAIL>'
              billing_company: 'Company A'
              credit_limit: '5000'
              enable_credit_card: true
            store: 'James Bike Shop'
            order_type: 'wholesale'
            order_status: 'Shipped'
            invoice_number: '#828182'
            ecommerce_id: '#92734'
            order_number: '#SE0019257'
            discount_code: null
            verification_code: 'AJD47S'
            risk_level: 'normal'
            requested_ship_date: '2020-04-20 00:00:00'
            shipped_date: null
            delivery_date: null
            fulfillment_status: 'fulfilled'
            order_items:
              - id: '167935'
                product_id: '891272'
                variant_id: '891272'
                sku: '*********'
                upc: '*********'
                title: 'product1'
                quantity: '5'
                quantity_to_fulfill: '5'
                total_discount: '0.00'
                price: '250.00'
                tax_amount: '20.00'
                total_price: '1270.00'
                created_at: '2020-04-13 12:31:53'
                updated_at: '2020-04-13 12:31:53'
              - id: '562363'
                product_id: '4223457'
                variant_id: '3452346'
                sku: '*********'
                upc: '*********'
                title: 'product2'
                quantity: '6'
                quantity_to_fulfill: '3'
                total_discount: '10.00'
                price: '150.00'
                tax_amount: '15.00'
                total_price: '455.00'
                created_at: '2020-04-13 12:32:53'
                updated_at: '2020-04-13 12:32:53'
            fulfillments:
              - id: '128273'
                warehouse_id: '28328'
                courier: 'Fedex'
                tracking_number: 'RA972372788US'
                items:
                  - id: '128974'
                    product_id: '891272'
                    variant_id: '891272'
                    sku: '*********'
                    upc: '*********'
                    title: 'product1'
                    fulfilled_quantity: '2'
                created_at: '2020-04-13 12:31:53'
              - id: '128274'
                warehouse_id: '28328'
                courier: 'Fedex'
                tracking_number: 'RA328517597US'
                items:
                  - id: '128975'
                    product_id: '891272'
                    variant_id: '891272'
                    sku: '*********'
                    upc: '*********'
                    title: 'product1'
                    fulfilled_quantity: '3'
                  - id: '927832'
                    product_id: '4624356'
                    variant_id: '3783456'
                    sku: '23457227'
                    upc: '2372373'
                    title: 'product2'
                    fulfilled_quantity: '3'
                created_at: '2020-04-13 12:32:53'
            refunds:
              - id: '23732'
                items:
                  - id: '82933'
                    product_id: '4624356'
                    variant_id: '3783456'
                    sku: '23457227'
                    upc: '2372373'
                    title: 'product2'
                    quantity_refunded: '3'
                    price: '150.00'
                    total_refunded: '450.00'
                amount: '450.00'
                shipping_amount: '0.00'
                tax_amount: '0.00'
                created_at: '2020-04-13 12:30:09'
            dealer_order:
              id: '242634'
              subtotal: '1550.50'
              total_price: '1695.57'
              total_discount: '100.00'
              total_shipping: '50.00'
              total_tax: '195.07'
              total_refund: '35.56'
              net_total: '1660.01'
              order_items:
                - id: '167935'
                  product_id: '891272'
                  variant_id: '891272'
                  sku: '*********'
                  upc: '*********'
                  title: 'product1'
                  quantity: '5'
                  quantity_to_fulfill: '5'
                  total_discount: '0.00'
                  price: '250.00'
                  tax_amount: '20.00'
                  total_price: '1270.00'
                  created_at: '2020-04-13 12:31:53'
                  updated_at: '2020-04-13 12:31:53'
                - id: '562363'
                  product_id: '4223457'
                  variant_id: '3452346'
                  sku: '*********'
                  upc: '*********'
                  title: 'product2'
                  quantity: '6'
                  quantity_to_fulfill: '3'
                  total_discount: '10.00'
                  price: '150.00'
                  tax_amount: '15.00'
                  total_price: '455.00'
                  created_at: '2020-04-13 12:32:53'
                  updated_at: '2020-04-13 12:32:53'
              fulfillments: 
                - id: '128273'
                  warehouse_id: '28328'
                  courier: 'Fedex'
                  tracking_number: 'RA972372788US'
                  items:
                    - id: '128974'
                      product_id: '891272'
                      variant_id: '891272'
                      sku: '*********'
                      upc: '*********'
                      title: 'product1'
                      fulfilled_quantity: '2'
                  created_at: '2020-04-13 12:31:53'
                - id: '128274'
                  warehouse_id: '28328'
                  courier: 'Fedex'
                  tracking_number: 'RA328517597US'
                  items:
                    - id: '128975'
                      product_id: '891272'
                      variant_id: '891272'
                      sku: '*********'
                      upc: '*********'
                      title: 'product1'
                      fulfilled_quantity: '3'
                    - id: '927832'
                      product_id: '4624356'
                      variant_id: '3783456'
                      sku: '23457227'
                      upc: '2372373'
                      title: 'product2'
                      fulfilled_quantity: '3'
                  created_at: '2020-04-13 12:32:53'
              refunds: 
                - id: '23732'
                  items:
                    - id: '82933'
                      product_id: '4624356'
                      variant_id: '3783456'
                      sku: '23457227'
                      upc: '2372373'
                      title: 'product2'
                      quantity_refunded: '3'
                      price: '150.00'
                      total_refunded: '450.00'
                  amount: '450.00'
                  shipping_amount: '0.00'
                  tax_amount: '0.00'
                  created_at: '2020-04-13 12:30:09'
              created_at: '2020-04-13 12:32:53'
              updated_at: '2020-04-13 12:32:53'
            payment_method: 'Stripe'
            payment_status: 'Paid'
            credit_term: null
            currency_code: 'USD'
            total_discount: '0.00'
            subtotal: '1725.00'
            total_shipping: '120.00'
            total_tax: '150.00'
            total_price: '1995.00'
            created_at: '2020-04-13 05:06:37'
            updated_at: '2020-11-30 22:42:11'
    OrderList:
      description: List of orders.
      headers:
        'RateLimit-Limit':
          $ref: '#/components/headers/RateLimit-Limit'
        'RateLimit-Remaining':
          $ref: '#/components/headers/RateLimit-Remaining'
        'RateLimit-Reset':
          $ref: '#/components/headers/RateLimit-Reset'
      content:
        application/json:
          schema:
            type: object
            additionalProperties: false
            minProperties: 1
            properties:
              data:
                maxItems: 250
                type: array
                items:
                  $ref: '#/components/schemas/Order'
          example:
            - id: '1290837'
              customer:
                first_name: 'Andy'
                last_name: 'Hamilton'
                email_address: '<EMAIL>'
                accepts_marketing: false
                order_count: '5'
                total_spent: '482.72'
                average_order: '96.54'
              billing_address:
                first_name: 'Andy'
                last_name: 'Hamilton'
                company_name: 'AHam Industries'
                address1: '2702 Columbia Boulevard'
                address2: null
                city: 'Baltimore'
                state: 'Maryland'
                country: 'United States'
                zip_code: '21202'
                latitude: '39.237093'
                longitude: '-76.682579'
              shipping_address:
                first_name: 'Andy'
                last_name: 'Hamilton'
                company_name: 'AHam Industries'
                address1: '2702 Columbia Boulevard'
                address2: null
                city: 'Baltimore'
                state: 'Maryland'
                country: 'United States'
                zip_code: '21202'
                latitude: '39.237093'
                longitude: '-76.682579'
              retailer:
                id: '25600'
                account_id: '********'
                status: 'Connected'
                company_name: 'Company A'
                address1: '4094 Garfield Road'
                address2: 'Suite 309'
                city: 'Chicago'
                state_code: 'IL'
                zip_code: '61614'
                country_code: 'US'
                latitude: '41.853196'
                longitude: '-87.601958'
                pricing_tier_id: '7283'
                sales_reps:
                  - '28767'
                  - '82632'
                territory_id: '782763'
                created_at: '2020-04-13 05:06:37'
                updated_at: '2020-11-30 22:42:11'
                dealer_protect_radius: '50'
                billing_email: '<EMAIL>'
                billing_company: 'Company A'
                credit_limit: '5000'
                enable_credit_card: true
              store: 'James Bike Shop'
              order_type: 'wholesale'
              order_status: 'Shipped'
              invoice_number: '#828182'
              ecommerce_id: '#92734'
              order_number: '#SE0019257'
              discount_code: null
              verification_code: 'AJD47S'
              risk_level: 'normal'
              requested_ship_date: '2020-04-20 00:00:00'
              shipped_date: null
              delivery_date: null
              fulfillment_status: 'fulfilled'
              order_items:
                - id: '167935'
                  product_id: '891272'
                  variant_id: '891272'
                  sku: '*********'
                  upc: '*********'
                  title: 'product1'
                  quantity: '5'
                  quantity_to_fulfill: '5'
                  total_discount: '0.00'
                  price: '250.00'
                  tax_amount: '20.00'
                  total_price: '1270.00'
                  created_at: '2020-04-13 12:31:53'
                  updated_at: '2020-04-13 12:31:53'
                - id: '562363'
                  product_id: '4223457'
                  variant_id: '3452346'
                  sku: '*********'
                  upc: '*********'
                  title: 'product2'
                  quantity: '6'
                  quantity_to_fulfill: '3'
                  total_discount: '10.00'
                  price: '150.00'
                  tax_amount: '15.00'
                  total_price: '455.00'
                  created_at: '2020-04-13 12:32:53'
                  updated_at: '2020-04-13 12:32:53'
              fulfillments:
                - id: '128273'
                  warehouse_id: '28328'
                  courier: 'Fedex'
                  tracking_number: 'RA972372788US'
                  items:
                    - id: '128974'
                      product_id: '891272'
                      variant_id: '891272'
                      sku: '*********'
                      upc: '*********'
                      title: 'product1'
                      fulfilled_quantity: '2'
                  created_at: '2020-04-13 12:31:53'
                - id: '128274'
                  warehouse_id: '28328'
                  courier: 'Fedex'
                  tracking_number: 'RA328517597US'
                  items:
                    - id: '128975'
                      product_id: '891272'
                      variant_id: '891272'
                      sku: '*********'
                      upc: '*********'
                      title: 'product1'
                      fulfilled_quantity: '3'
                    - id: '927832'
                      product_id: '4624356'
                      variant_id: '3783456'
                      sku: '23457227'
                      upc: '2372373'
                      title: 'product2'
                      fulfilled_quantity: '3'
                  created_at: '2020-04-13 12:32:53'
              refunds:
                - id: '23732'
                  items:
                    - id: '82933'
                      product_id: '4624356'
                      variant_id: '3783456'
                      sku: '23457227'
                      upc: '2372373'
                      title: 'product2'
                      quantity_refunded: '3'
                      price: '150.00'
                      total_refunded: '450.00'
                  amount: '450.00'
                  shipping_amount: '0.00'
                  tax_amount: '0.00'
                  created_at: '2020-04-13 12:30:09'
              dealer_order:
                id: '242634'
                subtotal: '1550.50'
                total_price: '1695.57'
                total_discount: '100.00'
                total_shipping: '50.00'
                total_tax: '195.07'
                total_refund: '35.56'
                net_total: '1660.01'
                order_items:
                  - id: '167935'
                    product_id: '891272'
                    variant_id: '891272'
                    sku: '*********'
                    upc: '*********'
                    title: 'product1'
                    quantity: '5'
                    quantity_to_fulfill: '5'
                    total_discount: '0.00'
                    price: '250.00'
                    tax_amount: '20.00'
                    total_price: '1270.00'
                    created_at: '2020-04-13 12:31:53'
                    updated_at: '2020-04-13 12:31:53'
                  - id: '562363'
                    product_id: '4223457'
                    variant_id: '3452346'
                    sku: '*********'
                    upc: '*********'
                    title: 'product2'
                    quantity: '6'
                    quantity_to_fulfill: '3'
                    total_discount: '10.00'
                    price: '150.00'
                    tax_amount: '15.00'
                    total_price: '455.00'
                    created_at: '2020-04-13 12:32:53'
                    updated_at: '2020-04-13 12:32:53'
                fulfillments: 
                  - id: '128273'
                    warehouse_id: '28328'
                    courier: 'Fedex'
                    tracking_number: 'RA972372788US'
                    items:
                      - id: '128974'
                        product_id: '891272'
                        variant_id: '891272'
                        sku: '*********'
                        upc: '*********'
                        title: 'product1'
                        fulfilled_quantity: '2'
                    created_at: '2020-04-13 12:31:53'
                  - id: '128274'
                    warehouse_id: '28328'
                    courier: 'Fedex'
                    tracking_number: 'RA328517597US'
                    items:
                      - id: '128975'
                        product_id: '891272'
                        variant_id: '891272'
                        sku: '*********'
                        upc: '*********'
                        title: 'product1'
                        fulfilled_quantity: '3'
                      - id: '927832'
                        product_id: '4624356'
                        variant_id: '3783456'
                        sku: '23457227'
                        upc: '2372373'
                        title: 'product2'
                        fulfilled_quantity: '3'
                    created_at: '2020-04-13 12:32:53'
                refunds: 
                  - id: '23732'
                    items:
                      - id: '82933'
                        product_id: '4624356'
                        variant_id: '3783456'
                        sku: '23457227'
                        upc: '2372373'
                        title: 'product2'
                        quantity_refunded: '3'
                        price: '150.00'
                        total_refunded: '450.00'
                    amount: '450.00'
                    shipping_amount: '0.00'
                    tax_amount: '0.00'
                    created_at: '2020-04-13 12:30:09'
                created_at: '2020-04-13 12:32:53'
                updated_at: '2020-04-13 12:32:53'
              payment_method: 'Stripe'
              payment_status: 'Paid'
              credit_term: null
              currency_code: 'USD'
              total_discount: '0.00'
              subtotal: '1725.00'
              total_shipping: '120.00'
              total_tax: '150.00'
              total_price: '1995.00'
              created_at: '2020-04-13 05:06:37'
              updated_at: '2020-11-30 22:42:11'
            - id: '1290837'
              customer:
                first_name: 'Micheal'
                last_name: 'Peltier'
                email_address: '<EMAIL>'
                accepts_marketing: true
                order_count: '9'
                total_spent: '1005.62'
                average_order: '111.73'
              billing_address:
                first_name: 'Micheal'
                last_name: 'Peltier'
                company_name: 'Peltier and Sons'
                address1: '1876  Anmoore Road'
                address2: 'Suite 304'
                city: 'Queens'
                state: 'New York'
                country: 'United States'
                zip_code: '11693'
                latitude: '40.725796'
                longitude: '-73.793028'
              shipping_address:
                first_name: 'Micheal'
                last_name: 'Peltier'
                company_name: 'Peltier and Sons'
                address1: '3267  Grim Avenue'
                address2: 'PO Box 4432'
                city: 'Queens'
                state: 'New York'
                country: 'United States'
                zip_code: '11680'
                latitude: '40.712672'
                longitude: '-73.814454'
              retailer:
                id: '25600'
                account_id: '********'
                status: 'Connected'
                company_name: 'Company A'
                address1: '4094 Garfield Road'
                address2: 'Suite 309'
                city: 'Chicago'
                state_code: 'IL'
                zip_code: '61614'
                country_code: 'US'
                latitude: '41.853196'
                longitude: '-87.601958'
                pricing_tier_id: '7283'
                sales_reps:
                  - '28767'
                  - '82632'
                territory_id: '782763'
                created_at: '2020-04-13 05:06:37'
                updated_at: '2020-11-30 22:42:11'
                dealer_protect_radius: '50'
                billing_email: '<EMAIL>'
                billing_company: 'Company A'
                credit_limit: '5000'
                enable_credit_card: true
              store: 'James Bike Shop'
              order_type: 'Local_delivery'
              order_status: 'Need To Confirm'
              invoice_number: '#828182'
              ecommerce_id: '#28927'
              order_number: '#SE0028362'
              discount_code: 'DOLLARSOFF'
              verification_code: 'PASJ2'
              risk_level: 'normal'
              requested_ship_date: null
              shipped_date: null
              delivery_date: null
              fulfillment_status: 'unfulfilled'
              order_items:
                - id: '167935'
                  product_id: '891272'
                  variant_id: '891272'
                  sku: '*********'
                  upc: '*********'
                  title: 'product1'
                  quantity: '10'
                  quantity_to_fulfill: '10'
                  total_discount: '100.00'
                  price: '250.00'
                  tax_amount: '40.00'
                  total_price: '2440.00'
                  created_at: '2020-04-13 12:31:53'
                  updated_at: '2020-04-13 12:31:53'
              fulfillments: []
              refunds: []
              dealer_order: null
              payment_method: 'Stripe'
              payment_status: 'Paid'
              credit_term: null
              currency_code: 'USD'
              total_discount: '100.00'
              subtotal: '2440.00'
              total_shipping: '120.00'
              total_tax: '150.00'
              total_price: '2710.00'
              created_at: '2020-04-13 05:06:37'
              updated_at: '2020-04-13 05:06:37'
    Count:
      description: Count of all Items
      headers:
        'RateLimit-Limit':
          $ref: '#/components/headers/RateLimit-Limit'
        'RateLimit-Remaining':
          $ref: '#/components/headers/RateLimit-Remaining'
        'RateLimit-Reset':
          $ref: '#/components/headers/RateLimit-Reset'
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Count'
          example:
            count: '6'
    PricingTierList:
      description: Details about all pricing tiers.
      headers:
        'RateLimit-Limit':
          $ref: '#/components/headers/RateLimit-Limit'
        'RateLimit-Remaining':
          $ref: '#/components/headers/RateLimit-Remaining'
        'RateLimit-Reset':
          $ref: '#/components/headers/RateLimit-Reset'
      content:
        application/json:
          schema:
            type: object
            additionalProperties: false
            minProperties: 1
            properties:
              data:
                maxItems: 250
                type: array
                items:
                  $ref: '#/components/schemas/PricingTier'
          example:
            data:
              - id: '7283'
                name: 'Tier 1 Pricing'
                currency_code: 'CAD'
                created_at: '2020-01-13 15:24:22'
                updated_at: '2021-01-26 22:26:36'
              - id: '5231'
                name: 'Tier 2 Pricing'
                currency_code: 'USD'
                created_at: '2020-04-13 12:30:09'
                updated_at: '2021-02-22 15:26:36'
    PricingTier:
      description: Details about specific pricing tier.
      headers:
        'RateLimit-Limit':
          $ref: '#/components/headers/RateLimit-Limit'
        'RateLimit-Remaining':
          $ref: '#/components/headers/RateLimit-Remaining'
        'RateLimit-Reset':
          $ref: '#/components/headers/RateLimit-Reset'
      content:
        application/json:
          schema:
            type: object
            additionalProperties: false
            minProperties: 1
            properties:
              data:
                $ref: '#/components/schemas/PricingTier'
          example:
            data:
              id: '7283'
              name: 'Tier 1 Pricing'
              currency_code: 'CAD'
              created_at: '2020-01-13 15:24:22'
              updated_at: '2021-01-26 22:26:36'
    ProductList:
      description: Details about all products.
      headers:
        'RateLimit-Limit':
          $ref: '#/components/headers/RateLimit-Limit'
        'RateLimit-Remaining':
          $ref: '#/components/headers/RateLimit-Remaining'
        'RateLimit-Reset':
          $ref: '#/components/headers/RateLimit-Reset'
      content:
        application/json:
          schema:
            type: object
            additionalProperties: false
            minProperties: 1
            properties:
              data:
                maxItems: 250
                type: array
                items:
                  $ref: '#/components/schemas/Product'
          example:
            data:
              - id: '1823719'
                title: 'Over Ear Headphones'
                total_warehouse_inventory: '823'
                total_retail_inventory: '273'
                created_at: '2020-01-13 15:24:22'
                updated_at: '2021-01-26 22:26:36'
                variants:
                  - id: '28374627'
                    product_id: '1823719'
                    sku: '1987123'
                    upc: '2012876123'
                    title: 'Over Ear Headphones - Red'
                    is_in_stock_only: false
                    total_warehouse_inventory: '135'
                    total_retail_inventory: '338'
                    created_at: '2020-07-22 14:15:09'
                    updated_at: '2021-04-11 15:26:36'
                    pricing_tiers:
                    - pricing_tier_id: '5231'
                      dealer_price: '120.00'
                      ship_to_store_price: '135.00'
                      created_at: '2020-09-13 12:30:09'
                      updated_at: '2021-02-22 15:26:36'
                    - pricing_tier_id: '7283'
                      dealer_price: '140.00'
                      ship_to_store_price: '150.00'
                      created_at: '2020-09-13 12:34:00'
                      updated_at: '2021-02-22 15:30:07'
                  - id: '9189072'
                    product_id: '1823719'
                    sku: '23456874'
                    upc: '24824545'
                    title: 'Over Ear Headphones - Blue'
                    is_in_stock_only: false
                    total_warehouse_inventory: '103'
                    total_retail_inventory: '205'
                    created_at: '2020-07-22 15:01:08'
                    updated_at: '2021-04-12 08:34:09'
                    pricing_tiers:
                    - pricing_tier_id: '5231'
                      dealer_price: '120.00'
                      ship_to_store_price: '135.00'
                      created_at: '2020-09-13 12:30:09'
                      updated_at: '2021-02-22 15:26:36'
              - id: '464356'
                title: 'Wireless Speaker'
                total_warehouse_inventory: '907'
                total_retail_inventory: '187'
                created_at: '2020-01-13 15:24:22'
                updated_at: '2021-01-26 22:26:36'
                variants:
                  - id: '827932'
                    product_id: '464356'
                    sku: '198723423'
                    upc: '239847234'
                    title: 'Wireless Speaker - Red'
                    is_in_stock_only: false
                    total_warehouse_inventory: '456'
                    total_retail_inventory: '123'
                    created_at: '2020-07-22 14:15:09'
                    updated_at: '2021-04-11 15:26:36'
                    pricing_tiers:
                    - pricing_tier_id: '5231'
                      dealer_price: '150.00'
                      ship_to_store_price: '180.00'
                      created_at: '2020-09-13 12:30:09'
                      updated_at: '2021-02-22 15:26:36'
                    - pricing_tier_id: '7283'
                      dealer_price: '160.00'
                      ship_to_store_price: '190.00'
                      created_at: '2020-09-13 12:34:00'
                      updated_at: '2021-02-22 15:30:07'
                  - id: '198223'
                    product_id: '464356'
                    sku: '37861234'
                    upc: '23789462'
                    title: 'Wireless Speaker - Blue'
                    is_in_stock_only: false
                    total_warehouse_inventory: '451'
                    total_retail_inventory: '64'
                    created_at: '2020-07-22 15:01:08'
                    updated_at: '2021-04-12 08:34:09'
                    pricing_tiers:
                    - pricing_tier_id: '5231'
                      dealer_price: '150.00'
                      ship_to_store_price: '180.00'
                      created_at: '2020-09-13 12:30:09'
                      updated_at: '2021-02-22 15:26:36'
    Product:
      description: Details about specific product.
      headers:
        'RateLimit-Limit':
          $ref: '#/components/headers/RateLimit-Limit'
        'RateLimit-Remaining':
          $ref: '#/components/headers/RateLimit-Remaining'
        'RateLimit-Reset':
          $ref: '#/components/headers/RateLimit-Reset'
      content:
        application/json:
          schema:
            type: object
            additionalProperties: false
            minProperties: 1
            properties:
              data:
                $ref: '#/components/schemas/Product'
          example:
            id: '1823719'
            title: 'Over Ear Headphones'
            total_warehouse_inventory: '823'
            total_retail_inventory: '273'
            created_at: '2020-01-13 15:24:22'
            updated_at: '2021-01-26 22:26:36'
            variants:
              - id: '28374627'
                product_id: '1823719'
                sku: '1987123'
                upc: '2012876123'
                title: 'Over Ear Headphones - Red'
                is_in_stock_only: false
                total_warehouse_inventory: '135'
                total_retail_inventory: '338'
                created_at: '2020-07-22 14:15:09'
                updated_at: '2021-04-11 15:26:36'
                pricing_tiers:
                - pricing_tier_id: '5231'
                  dealer_price: '120.00'
                  ship_to_store_price: '135.00'
                  created_at: '2020-09-13 12:30:09'
                  updated_at: '2021-02-22 15:26:36'
                - pricing_tier_id: '7283'
                  dealer_price: '140.00'
                  ship_to_store_price: '150.00'
                  created_at: '2020-09-13 12:34:00'
                  updated_at: '2021-02-22 15:30:07'
              - id: '9189072'
                product_id: '1823719'
                sku: '23456874'
                upc: '24824545'
                title: 'Over Ear Headphones - Blue'
                is_in_stock_only: false
                total_warehouse_inventory: '103'
                total_retail_inventory: '205'
                created_at: '2020-07-22 15:01:08'
                updated_at: '2021-04-12 08:34:09'
                pricing_tiers:
                - pricing_tier_id: '5231'
                  dealer_price: '120.00'
                  ship_to_store_price: '135.00'
                  created_at: '2020-09-13 12:30:09'
                  updated_at: '2021-02-22 15:26:36'
    RetailerList:
      description: Details about all retailers.
      headers:
        'RateLimit-Limit':
          $ref: '#/components/headers/RateLimit-Limit'
        'RateLimit-Remaining':
          $ref: '#/components/headers/RateLimit-Remaining'
        'RateLimit-Reset':
          $ref: '#/components/headers/RateLimit-Reset'
      content:
        application/json:
          schema:
            type: object
            additionalProperties: false
            minProperties: 1
            properties:
              data:
                maxItems: 250
                type: array
                items:
                  $ref: '#/components/schemas/Retailer'
          example:
            data:
              - id: '25600'
                account_id: '********'
                status: 'Connected'
                company_name: 'Company A'
                address1: '4094 Garfield Road'
                address2: 'Suite 309'
                city: 'Chicago'
                state_code: 'IL'
                zip_code: '61614'
                country_code: 'US'
                latitude: '41.853196'
                longitude: '-87.601958'
                pricing_tier_id: '7283'
                sales_reps:
                  - '28767'
                  - '82632'
                territory_id: '782763'
                default_warehouse_id: '53433'
                created_at: '2020-04-13 05:06:37'
                updated_at: '2020-11-30 22:42:11'
                dealer_protect_radius: '50'
                local_delivery_radius: '150'
                billing_email: '<EMAIL>'
                billing_company: 'Company A'
                credit_limit: '5000'
                enable_credit_card: true
                enable_on_file_payment: false
              - id: '52345'
                account_id: '0817822'
                status: 'Pending'
                company_name: 'Company B'
                address1: '805 Colony Street'
                address2: ''
                city: 'North Haven'
                state_code: 'CT'
                zip_code: '06473'
                country_code: 'US'
                latitude: '41.385205'
                longitude: '-72.85967'
                pricing_tier_id: '5231'
                sales_reps:
                  - '25602'
                  - '76845'
                territory_id: '782763'
                default_warehouse_id: '53433'
                created_at: '2020-04-13 05:06:37'
                updated_at: '2020-11-30 22:42:11'
                dealer_protect_radius: '50'
                local_delivery_radius: '150'
                billing_email: '<EMAIL>'
                billing_company: 'Company B'
                credit_limit: '2500'
                enable_credit_card: false
                enable_on_file_payment: false
    Retailer:
      description: Details about specific retailer.
      headers:
        'RateLimit-Limit':
          $ref: '#/components/headers/RateLimit-Limit'
        'RateLimit-Remaining':
          $ref: '#/components/headers/RateLimit-Remaining'
        'RateLimit-Reset':
          $ref: '#/components/headers/RateLimit-Reset'
      content:
        application/json:
          schema:
            type: object
            additionalProperties: false
            minProperties: 1
            properties:
              data:
                $ref: '#/components/schemas/Retailer'
          example:
            id: '25600'
            account_id: '********'
            status: 'Connected'
            company_name: 'Company A'
            address1: '4094 Garfield Road'
            address2: 'Suite 309'
            city: 'Chicago'
            state_code: 'IL'
            zip_code: '61614'
            country_code: 'US'
            latitude: '41.853196'
            longitude: '-87.601958'
            pricing_tier_id: '7283'
            sales_reps:
              - '28767'
              - '82632'
            territory_id: '782763'
            default_warehouse_id: '53433'
            dealer_protect_radius: '50'
            local_delivery_radius: '150'
            billing_email: '<EMAIL>'
            billing_company: 'Company A'
            credit_limit: '5000'
            enable_credit_card: true
            enable_on_file_payment: false
            created_at: '2020-04-13 05:06:37'
            updated_at: '2020-11-30 22:42:11'
    RetailerBranchList:
      description: Details about all branches of a retailer.
      headers:
        'RateLimit-Limit':
          $ref: '#/components/headers/RateLimit-Limit'
        'RateLimit-Remaining':
          $ref: '#/components/headers/RateLimit-Remaining'
        'RateLimit-Reset':
          $ref: '#/components/headers/RateLimit-Reset'
      content:
        application/json:
          schema:
            type: object
            additionalProperties: false
            minProperties: 1
            properties:
              data:
                maxItems: 250
                type: array
                items:
                  $ref: '#/components/schemas/RetailerBranch'
          example:
            data:
            - address1: 4280 50th Avenue
              address2: ''
              state_code: AB
              country_code: CA
              status: Connected
              account_id: ACCT# A1
              pricing_tier_id: '6'
              dealer_protect_radius: '0'
              local_delivery_radius: '5001'
              created_at: '2020-12-17 09:13:01'
              updated_at: '2022-01-13 08:36:09'
              id: '23453'
              company_name: Company A Branch 1
              city: Whitecourt
              zip_code: T7S 1A1
              latitude: '54.129940'
              longitude: "--115.669860"
            - address1: 3608 rue de la Gauchetière
              address2: ''
              state_code: QC
              country_code: CA
              status: Connected
              account_id: ACCT# A2
              pricing_tier_id: '6'
              dealer_protect_radius: '0'
              local_delivery_radius: '500'
              created_at: '2020-12-17 15:51:15'
              updated_at: '2022-01-12 14:41:49'
              id: '32453'
              company_name: Company A Branch 2
              city: Montreal
              zip_code: H3B 2M3
              latitude: '45.510386'
              longitude: "-73.618025"
    RetailerBranch:
      description: Details about specific retailer branch.
      headers:
        'RateLimit-Limit':
          $ref: '#/components/headers/RateLimit-Limit'
        'RateLimit-Remaining':
          $ref: '#/components/headers/RateLimit-Remaining'
        'RateLimit-Reset':
          $ref: '#/components/headers/RateLimit-Reset'
      content:
        application/json:
          schema:
            type: object
            additionalProperties: false
            minProperties: 1
            properties:
              data:
                $ref: '#/components/schemas/RetailerBranch'
          example:
            data:
              address1: 4280 50th Avenue
              address2: ''
              state_code: AB
              country_code: CA
              status: Connected
              account_id: ACCT# A1
              pricing_tier_id: '6'
              dealer_protect_radius: '0'
              local_delivery_radius: '5001'
              created_at: '2020-12-17 09:13:01'
              updated_at: '2022-01-13 08:36:09'
              id: '23453'
              company_name: Company A Branch 1
              city: Whitecourt
              zip_code: T7S 1A1
              latitude: '54.129940'
              longitude: "--115.669860"
    RetailerCreditList:
      description: Details about all credits for a specific retailer.
      headers:
        'RateLimit-Limit':
          $ref: '#/components/headers/RateLimit-Limit'
        'RateLimit-Remaining':
          $ref: '#/components/headers/RateLimit-Remaining'
        'RateLimit-Reset':
          $ref: '#/components/headers/RateLimit-Reset'
      content:
        application/json:
          schema:
            type: object
            additionalProperties: false
            minProperties: 1
            properties:
              data:
                maxItems: 250
                type: array
                items:
                  $ref: '#/components/schemas/RetailerCredit'
          example:
            data:
            - credit_term_id: '64'
              id: '5'
              invoice_number: 'IN#1234512'
              description:
              due_date: '2022-01-01'
              credit_date: '2022-01-20'
              total: '276.00'
              paid: '200.00'
              outstanding: '76.00'
              payments:
              - id: '2'
                amount: '200.00'
              items:
              - id: '5'
                type: fulfillment
                amount: '226.00'
                fulfillment_id: '354'
                products:
                - id: '399'
                  quantity: '1'
                  amount: '226.00'
                  title: Test Product 5
                  variant_id: '36804808474787'
              - id: '6'
                type: shipping
                amount: '50.00'
              status: overdue
            - credit_term_id: '64'
              id: '20'
              invoice_number: 'IN#542763'
              description: Asperiores et dolorum.
              due_date: '2022-02-01'
              credit_date: '2022-01-17'
              total: '46.97'
              paid: '0.00'
              outstanding: '46.97'
              payments: []
              items:
              - id: '22'
                type: manual
                amount: '46.97'
              status: pending
            - credit_term_id: '64'
              id: '21'
              invoice_number: 'IN#4651246'
              description: Asperiores expedita ut illo incidunt earum quia fuga similique et.
              due_date: '2022-01-07'
              credit_date: '2022-01-17'
              total: '106.16'
              paid: '106.16'
              outstanding: '0.00'
              payments:
              - id: '6'
                amount: '1.00'
              - id: '7'
                amount: '105.16'
              items:
              - id: '23'
                type: manual
                amount: '106.16'
              status: paid
            - credit_term_id: '64'
              id: '23'
              invoice_number: 'IN#8654234'
              description: Provident reiciendis est dolores laboriosam vel exercitationem.
              due_date: '2022-02-01'
              credit_date: '2022-01-17'
              total: '870.58'
              paid: '0.00'
              outstanding: '870.58'
              payments: []
              items:
              - id: '24'
                type: manual
                amount: '870.58'
              status: pending
    RetailerCredit:
      description: Details about specific retailer credit.
      headers:
        'RateLimit-Limit':
          $ref: '#/components/headers/RateLimit-Limit'
        'RateLimit-Remaining':
          $ref: '#/components/headers/RateLimit-Remaining'
        'RateLimit-Reset':
          $ref: '#/components/headers/RateLimit-Reset'
      content:
        application/json:
          schema:
            type: object
            additionalProperties: false
            minProperties: 1
            properties:
              data:
                $ref: '#/components/schemas/RetailerCredit'
          example:
            data:
              credit_term_id: '64'
              id: '5'
              invoice_number: 'IN#1234512'
              description:
              due_date: '2022-01-01'
              credit_date: '2022-01-20'
              total: '276.00'
              paid: '200.00'
              outstanding: '76.00'
              payments:
              - id: '2'
                amount: '200.00'
              items:
              - id: '5'
                type: fulfillment
                amount: '226.00'
                fulfillment_id: '354'
                products:
                - id: '399'
                  quantity: '1'
                  amount: '226.00'
                  title: Test Product 5
                  variant_id: '36804808474787'
              - id: '6'
                type: shipping
                amount: '50.00'
              status: overdue
    RetailerCreditPaymentList:
      description: Details about all payments for a specific retailer credit.
      headers:
        'RateLimit-Limit':
          $ref: '#/components/headers/RateLimit-Limit'
        'RateLimit-Remaining':
          $ref: '#/components/headers/RateLimit-Remaining'
        'RateLimit-Reset':
          $ref: '#/components/headers/RateLimit-Reset'
      content:
        application/json:
          schema:
            type: object
            additionalProperties: false
            minProperties: 1
            properties:
              data:
                maxItems: 250
                type: array
                items:
                  $ref: '#/components/schemas/RetailerCreditPayment'
          example:
            data:
            - id: '2'
              amount: '200.00'
              payment_date: '2022-01-01'
            - id: '40'
              amount: '25.55'
              payment_date: '2022-01-05'
    RetailerCreditPayment:
      description: Details about specific retailer credit payment.
      headers:
        'RateLimit-Limit':
          $ref: '#/components/headers/RateLimit-Limit'
        'RateLimit-Remaining':
          $ref: '#/components/headers/RateLimit-Remaining'
        'RateLimit-Reset':
          $ref: '#/components/headers/RateLimit-Reset'
      content:
        application/json:
          schema:
            type: object
            additionalProperties: false
            minProperties: 1
            properties:
              data:
                $ref: '#/components/schemas/RetailerCreditPayment'
          example:
            data:
              id: '2'
              amount: '200.00'
              payment_date: '2022-01-01'
    RetailerCreditItemList:
      description: Details about all items for a specific retailer credit.
      headers:
        'RateLimit-Limit':
          $ref: '#/components/headers/RateLimit-Limit'
        'RateLimit-Remaining':
          $ref: '#/components/headers/RateLimit-Remaining'
        'RateLimit-Reset':
          $ref: '#/components/headers/RateLimit-Reset'
      content:
        application/json:
          schema:
            type: object
            additionalProperties: false
            minProperties: 1
            properties:
              data:
                maxItems: 250
                type: array
                items:
                  $ref: '#/components/schemas/RetailerCreditItem'
          example:
            data:
            - id: '5'
              type: fulfillment
              amount: '226.00'
              fulfillment_id: '354'
              products:
              - id: '399'
                quantity: '1'
                amount: '226.00'
                title: Test Product 5
                variant_id: '36804808474787'
            - id: '6'
              type: shipping
              amount: '50.00'
    RetailerCreditItem:
      description: Details about specific retailer credit item.
      headers:
        'RateLimit-Limit':
          $ref: '#/components/headers/RateLimit-Limit'
        'RateLimit-Remaining':
          $ref: '#/components/headers/RateLimit-Remaining'
        'RateLimit-Reset':
          $ref: '#/components/headers/RateLimit-Reset'
      content:
        application/json:
          schema:
            type: object
            additionalProperties: false
            minProperties: 1
            properties:
              data:
                $ref: '#/components/schemas/RetailerCreditItem'
          example:
            data:
              id: '5'
              type: fulfillment
              amount: '226.00'
              fulfillment_id: '354'
              products:
              - id: '399'
                quantity: '1'
                amount: '226.00'
                title: Test Product 5
                variant_id: '36804808474787'
    RetailInventoryList:
      description: Inventory details for specific retailer.
      headers:
        'RateLimit-Limit':
          $ref: '#/components/headers/RateLimit-Limit'
        'RateLimit-Remaining':
          $ref: '#/components/headers/RateLimit-Remaining'
        'RateLimit-Reset':
          $ref: '#/components/headers/RateLimit-Reset'
      content:
        application/json:
          schema:
            type: object
            additionalProperties: false
            minProperties: 1
            properties:
              data:
                maxItems: 250
                type: array
                items:
                  $ref: '#/components/schemas/RetailInventory'
          example:
            data:
              - retailer_id: '25600'
                variant_id: '28374627'
                quantity: '200'
                updated_at: '2020-12-31 23:44:12'
              - retailer_id: '25600'
                variant_id: '9189072'
                quantity: '198'
                updated_at: '2021-01-05 02:28:56'
              - retailer_id: '25601'
                variant_id: '9189072'
                quantity: '90'
                updated_at: '2021-01-06 04:48:26'
    RetailInventory:
      description: Inventory details of specific retailer for specific variant.
      headers:
        'RateLimit-Limit':
          $ref: '#/components/headers/RateLimit-Limit'
        'RateLimit-Remaining':
          $ref: '#/components/headers/RateLimit-Remaining'
        'RateLimit-Reset':
          $ref: '#/components/headers/RateLimit-Reset'
      content:
        application/json:
          schema:
            type: object
            additionalProperties: false
            minProperties: 1
            properties:
              data:
                $ref: '#/components/schemas/RetailInventory'
          example:
            data:
              retailer_id: '25600'
              variant_id: '28374627'
              quantity: '200'
              updated_at: '2020-12-31 23:44:12'
    RetailInventoryAsRetailerList:
      description: Retailer endpoint for inventory data.
      headers:
        'RateLimit-Limit':
          $ref: '#/components/headers/RateLimit-Limit'
        'RateLimit-Remaining':
          $ref: '#/components/headers/RateLimit-Remaining'
        'RateLimit-Reset':
          $ref: '#/components/headers/RateLimit-Reset'
      content:
        application/json:
          schema:
            type: object
            additionalProperties: false
            minProperties: 1
            properties:
              data:
                maxItems: 250
                type: array
                items:
                  $ref: '#/components/schemas/RetailInventoryAsRetailer'
          example:
            data:
              - location_id: '25600'
                variant_id: '28374627'
                upc: '2012876123'
                product_title: 'Over Ear Headphones - Red'
                location_name: 'Company A'
                manufacturer: 'Manufacturer A'
                manufacturer_sku: '1987123'
                quantity: '200'
                updated_at: '2020-12-31 23:44:12'
    RetailInventoryAsRetailer:
      description: Retailer endpoint for inventory data for a specific location.
      headers:
        'RateLimit-Limit':
          $ref: '#/components/headers/RateLimit-Limit'
        'RateLimit-Remaining':
          $ref: '#/components/headers/RateLimit-Remaining'
        'RateLimit-Reset':
          $ref: '#/components/headers/RateLimit-Reset'
      content:
        application/json:
          schema:
            type: object
            additionalProperties: false
            minProperties: 1
            properties:
              data:
                $ref: '#/components/schemas/RetailInventoryAsRetailer'
          example:
            data:
              location_id: '25600'
              variant_id: '28374627'
              upc: '2012876123'
              product_title: 'Over Ear Headphones - Red'
              location_name: 'Company A'
              manufacturer: 'Manufacturer A'
              manufacturer_sku: '1987123'
              quantity: '200'
              updated_at: '2020-12-31 23:44:12'
    SalesRepList:
      description: Details about all sales reps.
      headers:
        'RateLimit-Limit':
          $ref: '#/components/headers/RateLimit-Limit'
        'RateLimit-Remaining':
          $ref: '#/components/headers/RateLimit-Remaining'
        'RateLimit-Reset':
          $ref: '#/components/headers/RateLimit-Reset'
      content:
        application/json:
          schema:
            type: object
            additionalProperties: false
            minProperties: 1
            properties:
              data:
                maxItems: 250
                type: array
                items:
                  $ref: '#/components/schemas/SalesRep'
          example:
            data: 
              - id: '28767'
                is_distributor: false
                descriptor: 'Sales rep for Company A'
                first_name: 'Bob'
                last_name: 'Johnson'
                company_name: 'Company A'
                phone: '************'
                email: '<EMAIL>'
                retailers: ['25600', '25601', '25602']
                created_at: '2020-04-13 05:06:37'
                updated_at: '2020-11-30 22:42:11'
              - id: '82634'
                is_distributor: false
                descriptor: 'Sales rep for Company B'
                first_name: 'Gerald'
                last_name: 'Sullivan'
                company_name: 'Company B'
                phone: '************'
                email: '<EMAIL>'
                retailers: ['25601', '25602']
                created_at: '2020-04-13 05:06:37'
                updated_at: '2020-11-30 22:42:11'
    SalesRep:
      description: Details for specific sales rep.
      headers:
        'RateLimit-Limit':
          $ref: '#/components/headers/RateLimit-Limit'
        'RateLimit-Remaining':
          $ref: '#/components/headers/RateLimit-Remaining'
        'RateLimit-Reset':
          $ref: '#/components/headers/RateLimit-Reset'
      content:
        application/json:
          schema:
            type: object
            additionalProperties: false
            minProperties: 1
            properties:
              data:
                $ref: '#/components/schemas/SalesRep'
          example:
            id: '28767'
            is_distributor: false
            descriptor: 'Sales rep for Company A'
            first_name: 'Bob'
            last_name: 'Johnson'
            company_name: 'Company A'
            phone: '************'
            email: '<EMAIL>'
            retailers: ['25600', '25601', '25602']
            created_at: '2020-04-13 05:06:37'
            updated_at: '2020-11-30 22:42:11'
    TerritoryList:
      description: Details about all territories.
      headers:
        'RateLimit-Limit':
          $ref: '#/components/headers/RateLimit-Limit'
        'RateLimit-Remaining':
          $ref: '#/components/headers/RateLimit-Remaining'
        'RateLimit-Reset':
          $ref: '#/components/headers/RateLimit-Reset'
      content:
        application/json:
          schema:
            type: object
            additionalProperties: false
            minProperties: 1
            properties:
              data:
                maxItems: 250
                type: array
                items:
                  $ref: '#/components/schemas/Territory'
          example:
            data:
              - id: '29797'
                name: 'Northern Territory'
                created_at: '2020-09-13 12:30:09'
                updated_at: '2021-02-22 15:26:36'
              - id: '29798'
                name: 'Southern Territory'
                created_at: '2020-05-24 04:24:09'
                updated_at: '2021-01-12 05:05:29'
    Territory:
      description: Details for specific territory.
      headers:
        'RateLimit-Limit':
          $ref: '#/components/headers/RateLimit-Limit'
        'RateLimit-Remaining':
          $ref: '#/components/headers/RateLimit-Remaining'
        'RateLimit-Reset':
          $ref: '#/components/headers/RateLimit-Reset'
      content:
        application/json:
          schema:
            type: object
            additionalProperties: false
            minProperties: 1
            properties:
              data:
                $ref: '#/components/schemas/Territory'
          example:
            id: '29797'
            name: 'Northern Territory'
            created_at: '2020-09-13 12:30:09'
            updated_at: '2021-02-22 15:26:36'
    VariantList:
      description: Details about all variants.
      headers:
        'RateLimit-Limit':
          $ref: '#/components/headers/RateLimit-Limit'
        'RateLimit-Remaining':
          $ref: '#/components/headers/RateLimit-Remaining'
        'RateLimit-Reset':
          $ref: '#/components/headers/RateLimit-Reset'
      content:
        application/json:
          schema:
            type: object
            additionalProperties: false
            minProperties: 1
            properties:
              data:
                maxItems: 250
                type: array
                items:
                  $ref: '#/components/schemas/Variant'
          example:
            data:
              - id: '28374627'
                product_id: '1823719'
                sku: '1987123'
                upc: '2012876123'
                title: 'Over Ear Headphones - Red'
                is_in_stock_only: false
                total_warehouse_inventory: '135'
                total_retail_inventory: '338'
                created_at: '2020-07-22 14:15:09'
                updated_at: '2021-04-11 15:26:36'
                pricing_tiers:
                - pricing_tier_id: '5231'
                  dealer_price: '120.00'
                  ship_to_store_price: '135.00'
                  created_at: '2020-09-13 12:30:09'
                  updated_at: '2021-02-22 15:26:36'
                - pricing_tier_id: '7283'
                  dealer_price: '140.00'
                  ship_to_store_price: '150.00'
                  created_at: '2020-09-13 12:34:00'
                  updated_at: '2021-02-22 15:30:07'
              - id: '9189072'
                product_id: '1823719'
                sku: '23456874'
                upc: '24824545'
                title: 'Over Ear Headphones - Blue'
                is_in_stock_only: false
                total_warehouse_inventory: '103'
                total_retail_inventory: '205'
                created_at: '2020-07-22 15:01:08'
                updated_at: '2021-04-12 08:34:09'
                pricing_tiers:
                - pricing_tier_id: '5231'
                  dealer_price: '120.00'
                  ship_to_store_price: '135.00'
                  created_at: '2020-09-13 12:30:09'
                  updated_at: '2021-02-22 15:26:36'
    Variant:
      description: Details about specific variant.
      headers:
        'RateLimit-Limit':
          $ref: '#/components/headers/RateLimit-Limit'
        'RateLimit-Remaining':
          $ref: '#/components/headers/RateLimit-Remaining'
        'RateLimit-Reset':
          $ref: '#/components/headers/RateLimit-Reset'
      content:
        application/json:
          schema:
            type: object
            additionalProperties: false
            minProperties: 1
            properties:
              data:
                $ref: '#/components/schemas/Variant'
          example:
            id: '28374627'
            product_id: '1823719'
            sku: '1987123'
            upc: '2012876123'
            title: 'Over Ear Headphones - Red'
            is_in_stock_only: false
            total_warehouse_inventory: '135'
            total_retail_inventory: '338'
            created_at: '2020-07-22 14:15:09'
            updated_at: '2021-04-11 15:26:36'
            pricing_tiers:
              - pricing_tier_id: '5231'
                dealer_price: '120.00'
                ship_to_store_price: '135.00'
                created_at: '2020-09-13 12:30:09'
                updated_at: '2021-02-22 15:26:36'
              - pricing_tier_id: '7283'
                dealer_price: '140.00'
                ship_to_store_price: '150.00'
                created_at: '2020-09-13 12:34:00'
                updated_at: '2021-02-22 15:30:07'
    VariantTier:
      description: Pricing details about specific variant.
      headers:
        'RateLimit-Limit':
          $ref: '#/components/headers/RateLimit-Limit'
        'RateLimit-Remaining':
          $ref: '#/components/headers/RateLimit-Remaining'
        'RateLimit-Reset':
          $ref: '#/components/headers/RateLimit-Reset'
      content:
        application/json:
          schema:
            type: object
            additionalProperties: false
            minProperties: 1
            properties:
              data:
                $ref: '#/components/schemas/VariantTier'
          example:
            data:
              pricing_tier_id: '5231'
              dealer_price: '120.00'
              ship_to_store_price: '135.00'
              created_at: '2020-09-13 12:30:09'
              updated_at: '2021-02-22 15:26:36'
              variant_id: '28374627'
    VariantTierList:
      description: Pricing details about specific variant.
      headers:
        'RateLimit-Limit':
          $ref: '#/components/headers/RateLimit-Limit'
        'RateLimit-Remaining':
          $ref: '#/components/headers/RateLimit-Remaining'
        'RateLimit-Reset':
          $ref: '#/components/headers/RateLimit-Reset'
      content:
        application/json:
          schema:
            type: object
            additionalProperties: false
            minProperties: 1
            properties:
              data:
                maxItems: 250
                type: array
                items:
                  $ref: '#/components/schemas/VariantTier'
          example:
            data:
              - pricing_tier_id: '5231'
                dealer_price: '120.00'
                ship_to_store_price: '135.00'
                created_at: '2020-09-13 12:30:09'
                updated_at: '2021-02-22 15:26:36'
                variant_id: '28374627'
              - pricing_tier_id: '7283'
                dealer_price: '140.00'
                ship_to_store_price: '150.00'
                created_at: '2020-09-13 12:34:00'
                updated_at: '2021-02-22 15:30:07'
                variant_id: '28374627'
    Warehouse:
      description: Details about a specific warehouse.
      headers:
        'RateLimit-Limit':
          $ref: '#/components/headers/RateLimit-Limit'
        'RateLimit-Remaining':
          $ref: '#/components/headers/RateLimit-Remaining'
        'RateLimit-Reset':
          $ref: '#/components/headers/RateLimit-Reset'
      content:
        application/json:
          schema:
            type: object
            additionalProperties: false
            minProperties: 1
            properties:
              data:
                $ref: '#/components/schemas/Warehouse'
          example:
            data:
              id: '5231'
              name: 'East'
              created_at: '2020-09-13 12:30:09'
              updated_at: '2021-02-22 15:26:36'
    WarehouseList:
      description: Details about all warehouses.
      headers:
        'RateLimit-Limit':
          $ref: '#/components/headers/RateLimit-Limit'
        'RateLimit-Remaining':
          $ref: '#/components/headers/RateLimit-Remaining'
        'RateLimit-Reset':
          $ref: '#/components/headers/RateLimit-Reset'
      content:
        application/json:
          schema:
            type: object
            additionalProperties: false
            minProperties: 1
            properties:
              data:
                maxItems: 250
                type: array
                items:
                  $ref: '#/components/schemas/Warehouse'
          example:
            data:
              - id: '5231'
                name: 'East'
                created_at: '2020-09-13 12:30:09'
                updated_at: '2021-02-22 15:26:36'
              - id: '5232'
                name: 'West'
                created_at: '2020-09-13 12:31:00'
                updated_at: '2020-09-13 12:31:00'
    Courier:
      description: Details about a courier
      headers:
        'RateLimit-Limit':
          $ref: '#/components/headers/RateLimit-Limit'
        'RateLimit-Remaining':
          $ref: '#/components/headers/RateLimit-Remaining'
        'RateLimit-Reset':
          $ref: '#/components/headers/RateLimit-Reset'
      content:
        application/json:
          schema:
            type: object
            additionalProperties: false
            minProperties: 1
            properties:
              data:
                $ref: '#/components/schemas/Courier'
          example:
            data:
              id: '432'
              name: 'Fedex'
    CourierList:
      description: A list of couriers
      headers:
        'RateLimit-Limit':
          $ref: '#/components/headers/RateLimit-Limit'
        'RateLimit-Remaining':
          $ref: '#/components/headers/RateLimit-Remaining'
        'RateLimit-Reset':
          $ref: '#/components/headers/RateLimit-Reset'
      content:
        application/json:
          schema:
            type: object
            additionalProperties: false
            minProperties: 1
            properties:
              data:
                maxItems: 250
                type: array
                items:
                  $ref: '#/components/schemas/Courier'
          example:
            data:
              - id: '432'
                name: 'Fedex'
              - id: '64312'
                name: 'UPS'
    InventoryTransferList:
      description: A list of inventory transfers
      headers:
        'RateLimit-Limit':
          $ref: '#/components/headers/RateLimit-Limit'
        'RateLimit-Remaining':
          $ref: '#/components/headers/RateLimit-Remaining'
        'RateLimit-Reset':
          $ref: '#/components/headers/RateLimit-Reset'
      content:
        application/json:
          schema:
            type: object
            additionalProperties: false
            minProperties: 1
            properties:
              data:
                maxItems: 250
                type: array
                items:
                  $ref: '#/components/schemas/InventoryTransfer'
    InventoryTransfer:
      description: Details of an inventory transfer
      headers:
        'RateLimit-Limit':
          $ref: '#/components/headers/RateLimit-Limit'
        'RateLimit-Remaining':
          $ref: '#/components/headers/RateLimit-Remaining'
        'RateLimit-Reset':
          $ref: '#/components/headers/RateLimit-Reset'
      content:
        application/json:
          schema:
            type: object
            additionalProperties: false
            minProperties: 1
            properties:
              data:
                $ref: '#/components/schemas/InventoryTransfer'
    Status:
      description: result of an operation when no data is returned. e.g deletes.
      headers:
        'RateLimit-Limit':
          $ref: '#/components/headers/RateLimit-Limit'
        'RateLimit-Remaining':
          $ref: '#/components/headers/RateLimit-Remaining'
        'RateLimit-Reset':
          $ref: '#/components/headers/RateLimit-Reset'
      content:
        application/json:
          schema:
            type: object
            additionalProperties: false
            minProperties: 1
            properties:
              data:
                $ref: '#/components/schemas/Status'
    ProductLocator:
      description: Result of a product locator query
      headers:
        'RateLimit-Limit':
          $ref: '#/components/headers/RateLimit-Limit'
        'RateLimit-Remaining':
          $ref: '#/components/headers/RateLimit-Remaining'
        'RateLimit-Reset':
          $ref: '#/components/headers/RateLimit-Reset'
      content:
        application/json:
          schema:
            type: object
            additionalProperties: false
            minProperties: 1
            properties:
              data:
                maxItems: 250
                type: array
                items:
                  $ref: '#/components/schemas/ProductLocatorRetailer'
  schemas:
    IdString:
      type: string
      pattern: '^[0-9]*$'
      maxLength: 20
    IdStringNullable:
      type: [ string, 'null' ]
      pattern: '^[0-9]*$'
      maxLength: 20
    IntegerString:
      type: string
      pattern: '^-?[0-9]*$'
      maxLength: 20
    IntegerNullable:
      type: [ string, 'null' ]
      pattern: '^-?[0-9]*$'
      maxLength: 20
    DecimalString:
      type: string
      pattern: '^[-+]?[0-9]+[.]?[0-9]*([eE][-+]?[0-9]+)?$'
      maxLength: 20
    DecimalStringNullable:
      type: [ string, 'null' ]
      pattern: '^[-+]?[0-9]+[.]?[0-9]*([eE][-+]?[0-9]+)?$'
      maxLength: 20
    DateTimeString:
      type: string
      example: '2021-02-22 15:30:07'
      pattern: '\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}'
      maxLength: 19
    DateTimeStringNullable:
      type: [ string, 'null' ]
      example: '2021-02-22 15:30:07'
      pattern: '\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}'
      maxLength: 19
    DateString:
      type: string
      example: '2021-02-22 15:30:07'
      pattern: '\d{4}-\d{2}-\d{2}'
      maxLength: 10
    DateStringNullable:
      type: [ string, 'null' ]
      example: '2021-02-22 15:30:07'
      pattern: '\d{4}-\d{2}-\d{2}'
      maxLength: 10
    PriceDecimal:
      type: number
      maximum: 3.402823466E+38
      minimum: 1.175494351E-38
      format: float
    PageInteger:
      type: integer
      maximum: 2147483647
      minimum: 1
      default: 1
      format: int32
    LimitInteger:
      type: integer
      maximum: 2147483647
      minimum: 1
      default: 1
      format: int32
    IdInteger:
      type: integer
      maximum: 2147483647
      minimum: 1
      format: int32
    WholeNumber:
      type: integer
      maximum: 2147483647
      minimum: 0
      format: int32
    Integer:
      type: integer
      maximum: 2147483647
      minimum: -2147483648
      format: int32
    Product:
      type: object
      additionalProperties: false
      minProperties: 12
      properties:
        id:
          $ref: '#/components/schemas/IdString'
        source_id:
          type: [ string, 'null' ]
          maxLength: 255
        source_type:
          type: [ string, 'null' ]
          enum: [ null, 'shopify', 'woocommerce', 'magento' ]
        handle:
          type: [ string, 'null' ]
          maxLength: 255
        title:
          type: string
          maxLength: 255
        description_html:
          type: [ string, 'null' ]
        total_warehouse_inventory:
          $ref: '#/components/schemas/IntegerString'
        total_retail_inventory:
          $ref: '#/components/schemas/IntegerString'
        created_at:
          $ref: '#/components/schemas/DateTimeString'
        updated_at:
          $ref: '#/components/schemas/DateTimeString'
        variant_options:
          type: array
          items:
            $ref: '#/components/schemas/VariantOption'
        variants:
          type: array
          items:
            $ref: '#/components/schemas/Variant'
    Variant:
      type: object
      additionalProperties: false
      minProperties: 25
      properties:
        id:
          $ref: '#/components/schemas/IntegerString'
        product_id:
          $ref: '#/components/schemas/IdStringNullable'
        sku:
          type: string
          maxLength: 50
        upc:
          type: string
          maxLength: 25
        title:
          type: string
          maxLength: 200
        status:
          type: string
          enum: [ 'Active', 'Deactivated', 'Ordered', 'Discontinued', 'Pre-order', 'Incomplete' ]
        is_in_stock_only:
          type: boolean
        is_non_stocking:
          type: boolean
        sell_direct_method:
          type: string
          enum: [ 'SELL_DIRECT_EXCLUSIVELY', 'SELL_DIRECT_UNLESS_BUNDLED', 'ALWAYS_SELL_DIRECT', 'ONLY_IN_UNPROTECTED_TERRITORIES', 'RETAIL_EXCLUSIVE_PRODUCT' ]
        enable_b2b_oversell:
          type: boolean
        b2b_min_order_quantity:
          $ref: '#/components/schemas/IntegerNullable'
        b2b_max_order_quantity:
          $ref: '#/components/schemas/IntegerNullable'
        enable_store_pickup:
          type: boolean
        enable_local_delivery:
          type: boolean
        enable_ship_from_store:
          type: boolean
        weight:
          $ref: '#/components/schemas/DecimalString'
        weight_unit:
          type: string
          enum: [ 'g', 'kg', 'oz', 'lb' ]
        total_warehouse_inventory:
          $ref: '#/components/schemas/IntegerString'
        total_retail_inventory:
          $ref: '#/components/schemas/IntegerString'
        published_at:
          $ref: '#/components/schemas/DateTimeStringNullable'
        created_at:
          $ref: '#/components/schemas/DateTimeString'
        updated_at:
          $ref: '#/components/schemas/DateTimeString'
        variant_values:
          type: array
          items:
            $ref: '#/components/schemas/VariantValue'
        pricing_tiers:
          type: array
          items:
            $ref: '#/components/schemas/VariantTier'
        variant_prices:
          type: array
          items:
            $ref: '#/components/schemas/VariantPrice'
    VariantAbbreviated:
      type: object
      additionalProperties: false
      minProperties: 5
      properties:
        id:
          $ref: '#/components/schemas/IdString'
        product_id:
          $ref: '#/components/schemas/IdString'
        sku:
          type: string
          maxLength: 50
        upc:
          type: string
          maxLength: 25
        title:
          type: string
          maxLength: 200
    VariantOption:
      type: object
      additionalProperties: false
      minProperties: 7
      properties:
        id:
          $ref: '#/components/schemas/IdString'
        product_id:
          $ref: '#/components/schemas/IdString'
        source_id:
          type: [ string, 'null' ]
          maxLength: 255
        name:
          type: string
          maxLength: 255
        position:
          $ref: '#/components/schemas/IntegerString'
        created_at:
          $ref: '#/components/schemas/DateTimeString'
        updated_at:
          $ref: '#/components/schemas/DateTimeString'
    VariantValue:
      type: object
      additionalProperties: false
      minProperties: 6
      properties:
        variant_id:
          $ref: '#/components/schemas/IdString'
        variant_option_id:
          $ref: '#/components/schemas/IdString'
        name:
          type: string
          maxLength: 255
        value:
          type: string
          maxLength: 255
        created_at:
          $ref: '#/components/schemas/DateTimeString'
        updated_at:
          $ref: '#/components/schemas/DateTimeString'
    VariantTier:
      type: object
      additionalProperties: false
      minProperties: 6
      properties:
        id:
          $ref: '#/components/schemas/IdString'
        variant_id:
          $ref: '#/components/schemas/IdString'
        dealer_price:
          $ref: '#/components/schemas/DecimalStringNullable'
        ship_to_store_price:
          $ref: '#/components/schemas/DecimalStringNullable'
        commission_price:
          $ref: '#/components/schemas/DecimalString'
        created_at:
          $ref: '#/components/schemas/DateTimeString'
        updated_at:
          $ref: '#/components/schemas/DateTimeString'
    VariantPrice:
      type: object
      additionalProperties: false
      properties:
        currency_code:
          type: string
          maxLength: 3
        price:
          $ref: '#/components/schemas/DecimalString'
        compare_at_price:
          $ref: '#/components/schemas/DecimalStringNullable'
    VariantTierRequest:
      type: object
      additionalProperties: false
      required:
        - 'dealer_price'
        - 'ship_to_store_price'
      properties:
        id:
          $ref: '#/components/schemas/IdInteger'
        dealer_price:
          $ref: '#/components/schemas/PriceDecimal'
        ship_to_store_price:
          $ref: '#/components/schemas/PriceDecimal'
    RetailInventory:
      type: object
      additionalProperties: false
      minProperties: 4
      properties:
        retailer_id:
          $ref: '#/components/schemas/IdString'
        variant_id:
          $ref: '#/components/schemas/IdString'
        quantity:
          $ref: '#/components/schemas/IntegerString'
        updated_at:
          $ref: '#/components/schemas/DateTimeString'
    RetailInventoryAsRetailer:
      type: object
      additionalProperties: false
      minProperties: 8
      properties:
        location_id:
          $ref: '#/components/schemas/IdString'
        variant_id:
          $ref: '#/components/schemas/IdString'
        upc:
          type: [ string, 'null' ]
          maxLength: 25
        product_title:
          type: [ string, 'null' ]
          maxLength: 200
        location_name:
          type: [ string, 'null' ]
          maxLength: 50
        manufacturer:
          type: [ string, 'null' ]
          maxLength: 50
        manufacturer_sku:
          type: [ string, 'null' ]
          maxLength: 50
        quantity:
          $ref: '#/components/schemas/IntegerString'
        updated_at:
          $ref: '#/components/schemas/DateTimeString'
    Retailer:
      type: [ object, 'null' ]
      additionalProperties: false
      minProperties: 33
      properties:
        id:
          $ref: '#/components/schemas/IdString'
        account_id:
          type: [ string, 'null' ]
          maxLength: 50
        billing_company:
          type: [ string, 'null' ]
          maxLength: 50
        billing_email:
          type: [ string, 'null' ]
          format: email
          maxLength: 200
        status:
          type: string
          maxLength: 20
        company_name:
          type: [ string, 'null' ]
          maxLength: 50
        address1:
          type: [ string, 'null' ]
          maxLength: 200
        address2:
          type: [ string, 'null' ]
          maxLength: 200
        city:
          type: [ string, 'null' ]
          maxLength: 100
        state_code:
          type: [ string, 'null' ]
          maxLength: 10
        zip_code:
          type: [ string, 'null' ]
          maxLength: 10
        country_code:
          type: [ string, 'null' ]
          maxLength: 2
        latitude:
          type: [ string, 'null' ]
          maxLength: 15
        longitude:
          type: [ string, 'null' ]
          maxLength: 15
        pricing_tier_id:
          $ref: '#/components/schemas/IdStringNullable'
        dealer_protect_radius:
          $ref: '#/components/schemas/DecimalString'
        local_delivery_radius:
          $ref: '#/components/schemas/DecimalString'
        credit_limit:
          $ref: '#/components/schemas/DecimalString'
        ship_from_store_distance:
          $ref: '#/components/schemas/IntegerString'
        enable_credit_card:
          type: boolean
        enable_on_file_payment:
          type: boolean
        has_active_inventory_integration:
          type: boolean
        is_non_stocking:
          type: boolean
        b2b_minimum_order_value:
          $ref: '#/components/schemas/DecimalString'
        is_installer:
          type: boolean
        b2b_tax_rate:
          $ref: '#/components/schemas/DecimalString'
        default_warehouse_id:
          $ref: '#/components/schemas/IdStringNullable'
        territory:
          type: [ string, 'null' ]
          maxLength: 255
        sales_reps:
          type: array
          items: 
            $ref: '#/components/schemas/IdString'
          maxItems: 250
        credit_terms:
          type: array
          items:
            $ref: '#/components/schemas/IdString'
        branches:
          type: array
          items: 
            $ref: '#/components/schemas/RetailerBranch'
          maxItems: 250
        created_at:
          $ref: '#/components/schemas/DateTimeString'
        updated_at:
          $ref: '#/components/schemas/DateTimeString'
    RetailerRequest:
      type: object
      additionalProperties: false
      minProperties: 1
      maxProperties: 19
      properties:
        pricing_tier_id:
          $ref: '#/components/schemas/IdInteger'
        account_id:
          type: string
          maxLength: 50
        territory_id:
          $ref: '#/components/schemas/IdInteger'
        billing_company:
          type: string
          maxLength: 50
        billing_email:
          type: string
          maxLength: 200
          format: email
        dealer_protect_radius:
          $ref: '#/components/schemas/IdInteger'
        credit_limit:
          $ref: '#/components/schemas/PriceDecimal'
        b2b_tax_rate:
          $ref: '#/components/schemas/PriceDecimal'
        enable_credit_card:
          type: boolean
        is_installer:
          type: boolean
        local_delivery_radius:
          $ref: '#/components/schemas/IdInteger'
        is_non_stocking:
          type: boolean
        state_code:
          type: string
        country_code:
          type: string
        address1:
          type: string
        address2:
          type: string
        city:
          type: string
        zipcode:
          type: string
        status:
          type: string
    RetailerBranch:
      type: [ object, 'null' ]
      additionalProperties: false
      minProperties: 21
      properties:
        id:
          $ref: '#/components/schemas/IdString'
        account_id:
          type: [ string, 'null' ]
          maxLength: 50
        status:
          type: string
          maxLength: 20
        company_name:
          type: [ string, 'null' ]
          maxLength: 50
        address1:
          type: [ string, 'null' ]
          maxLength: 200
        address2:
          type: [ string, 'null' ]
          maxLength: 200
        city:
          type: [ string, 'null' ]
          maxLength: 100
        state_code:
          type: [ string, 'null' ]
          maxLength: 10
        zip_code:
          type: [ string, 'null' ]
          maxLength: 10
        country_code:
          type: [ string, 'null' ]
          maxLength: 2
        latitude:
          type: [ string, 'null' ]
          maxLength: 15
        longitude:
          type: [ string, 'null' ]
          maxLength: 15
        pricing_tier_id: 
          $ref: '#/components/schemas/IdString'
          maxLength: 5
        dealer_protect_radius:
          $ref: '#/components/schemas/DecimalString'
        local_delivery_radius:
          $ref: '#/components/schemas/DecimalString'
        ship_from_store_distance:
          $ref: '#/components/schemas/IdString'
        is_non_stocking:
          type: boolean
        is_installer:
          type: boolean
        sales_reps:
          type: array
          items: 
            $ref: '#/components/schemas/IdString'
          maxItems: 250
        created_at:
          $ref: '#/components/schemas/DateTimeString'
        updated_at:
          $ref: '#/components/schemas/DateTimeString'
    RetailerCredit:
      type: object
      additionalProperties: false
      minProperties: 13
      properties:
        id:
          $ref: '#/components/schemas/IdString'
        credit_term_id:
          $ref: '#/components/schemas/IdString'
        order_id:
          $ref: '#/components/schemas/IdStringNullable'
        invoice_number:
          type: string
        description:
          type: string
        credit_date:
          $ref: '#/components/schemas/DateString'
        due_date:
          $ref: '#/components/schemas/DateString'
        status:
          type: string
        total:
          $ref: '#/components/schemas/DecimalString'
        paid:
          $ref: '#/components/schemas/DecimalString'
        outstanding:
          $ref: '#/components/schemas/DecimalString'
        payments:
          type: array
          items:
            $ref: '#/components/schemas/RetailerCreditPayment'
        items:
          type: array
          items:
            $ref: '#/components/schemas/RetailerCreditItem'
    RetailerCreditCreateRequest:
      type: object
      additionalProperties: false
      minProperties: 5
      properties:
        invoice_number:
          type: string
        credit_date:
          $ref: '#/components/schemas/DateString'
        credit_term_id:
          $ref: '#/components/schemas/IdInteger'
        total:
          $ref: '#/components/schemas/PriceDecimal'
        description:
          type: string
    RetailerCreditEditRequest:
      type: object
      additionalProperties: false
      minProperties: 2
      properties:
        invoice_number:
          type: string
        due_date:
          $ref: '#/components/schemas/DateString'
    RetailerCreditItem:
      type: object
      additionalProperties: true
      minProperties: 3
      properties:
        id:
          $ref: '#/components/schemas/IdInteger'
        type:
          type: string
        amount:
          $ref: '#/components/schemas/PriceDecimal'
        fulfillment_id:
          $ref: '#/components/schemas/IdInteger'
        products:
          type: array
          items:
            additionalProperties: false
            minProperties: 5
            properties:
              id:
                $ref: '#/components/schemas/IdInteger'
              quantity:
                $ref: '#/components/schemas/IdInteger'
              amount:
                $ref: '#/components/schemas/DecimalString'
              variant_id:
                $ref: '#/components/schemas/IdInteger'
              title:
                type: string
    RetailerCreditPayment:
      type: object
      additionalProperties: false
      minProperties: 3
      properties:
        id:
          $ref: '#/components/schemas/IdInteger'
        type:
          type: string
          enum: [ 'credit', 'bank debit', 'credit card', 'cheque', 'cash', 'other' ]
        amount:
          $ref: '#/components/schemas/DecimalString'
        payment_date:
          $ref: '#/components/schemas/DateString'
    RetailerCreditPaymentRequest:
      type: object
      additionalProperties: false
      minProperties: 1
      properties:
        amount:
          $ref: '#/components/schemas/PriceDecimal'
    Count:
      type: object
      additionalProperties: false
      minProperties: 1
      properties:
        count:
          $ref: '#/components/schemas/IdInteger'
    PricingTier:
      type: object
      additionalProperties: false
      minProperties: 5
      properties:
        id:
          $ref: '#/components/schemas/IdString'
        name:
          type: string
          maxLength: 100
        currency_code:
          type: string
          maxLength: 25
        created_at:
          $ref: '#/components/schemas/DateTimeString'
        updated_at:
          $ref: '#/components/schemas/DateTimeString'
    PricingTierRequest:
      additionalProperties: false
      example:
        name: Tier 1 Pricing
        currency_code: USD
      type: object
      properties:
        name:
          type: string
          maxLength: 100
        currency_code:
          type: string
          maxLength: 25
          maxItems: 250
    SalesRepRequest:
      type: object
      additionalProperties: false
      minProperties: 6
      properties:
        descriptor:
          type: string
          maxLength: 255
        company_name:
          type: string
          maxLength: 50
        first_name:
          type: string
          maxLength: 50
        last_name:
          type: string
          maxLength: 50
        phone:
          type: string
          maxLength: 100
        is_distributor:
          type: boolean
    SalesRep:
      type: object
      additionalProperties: false
      minProperties: 10
      properties:
        id:
          $ref: '#/components/schemas/IdString'
        is_distributor:
          type: boolean
        descriptor:
          type: string
          maxLength: 255
        first_name:
          type: string
          maxLength: 50
        last_name:
          type: string
          maxLength: 50
        company_name:
          type: string
          maxLength: 50
        phone:
          type: string
          maxLength: 100
        email:
          type: string
          maxLength: 200
          format: email
        created_at:
          $ref: '#/components/schemas/DateTimeString'
        updated_at:
          $ref: '#/components/schemas/DateTimeString'
    Territory:
      type: object
      additionalProperties: false
      minProperties: 4
      properties:
        id:
          $ref: '#/components/schemas/IdString'
        name:
          type: string
          maxLength: 255
        created_at:
          $ref: '#/components/schemas/DateTimeString'
        updated_at:
          $ref: '#/components/schemas/DateTimeString'
    Refund:
      additionalProperties: false
      minProperties: 6
      type: object
      properties:
        id:
          $ref: '#/components/schemas/IntegerString'
        items:
          type: array
          items:
            $ref: '#/components/schemas/RefundItem'
        amount: 
          $ref: '#/components/schemas/DecimalString'
        shipping_amount: 
          $ref: '#/components/schemas/DecimalString'
        tax_amount: 
          $ref: '#/components/schemas/DecimalString'
        created_at:
          $ref: '#/components/schemas/DateTimeString'
    RefundItem:
      additionalProperties: false
      minProperties: 7
      type: object
      properties:
        id: 
          $ref: '#/components/schemas/IdString'
        product_id: 
          $ref: '#/components/schemas/IdString'
        variant_id: 
          $ref: '#/components/schemas/IdString'
        sku: 
          type: string
        upc: 
          type: string
        title: 
          type: string
        quantity_refunded: 
          $ref: '#/components/schemas/IntegerString'
    FulfillmentRequest:
      additionalProperties: false
      minProperties: 6
      type: object
      properties:
        warehouse_id:
          $ref: '#/components/schemas/IdInteger'
        courier_id:
          $ref: '#/components/schemas/IdInteger'
        tracking_number:
          type: [ string, 'null' ]
          maxLength: 100
        tracking_url:
          type: [ string, 'null' ]
        add_to_ecommerce:
          type: boolean
        items:
          type: array
          items:
            additionalProperties: false
            minProperties: 2
            properties:
              order_item_id:
                $ref: '#/components/schemas/IdInteger'
              quantity:
                $ref: '#/components/schemas/IdInteger'
    Fulfillment:
      additionalProperties: false
      minProperties: 6
      type: object
      properties:
        id:
          $ref: '#/components/schemas/IntegerString'
        warehouse_id:
          $ref: '#/components/schemas/IntegerString'
        courier:
          $ref: '#/components/schemas/Courier'
        tracking_number:
          type: [ string, 'null' ]
          maxLength: 100
        tracking_url:
          type: [ string, 'null' ]
          maxLength: 255
        items:
          type: array
          items:
            $ref: '#/components/schemas/FulfillmentItem'
        created_at: 
          $ref: '#/components/schemas/DateTimeString'
    FulfillmentItem:
      additionalProperties: false
      minProperties: 7
      type: object
      properties:
        id: 
          $ref: '#/components/schemas/IdString'
        order_item_id: 
          $ref: '#/components/schemas/IdString'
        product_id: 
          $ref: '#/components/schemas/IdString'
        variant_id: 
          $ref: '#/components/schemas/IdString'
        sku: 
          type: string
        upc: 
          type: string
        title: 
          type: string
        fulfilled_quantity: 
          $ref: '#/components/schemas/IntegerString'
    OrderShippingLine:
      type: object
      additionalProperties: false
      minProperties: 3
      properties:
        service:
          type: [ string, 'null' ]
          maxLength: 255
        price:
          $ref: '#/components/schemas/DecimalString'
        discount:
          $ref: '#/components/schemas/DecimalString'
        discounted_price:
          $ref: '#/components/schemas/DecimalString'
        total_tax:
          $ref: '#/components/schemas/DecimalString'
    OrderItem:
      additionalProperties: false
      minProperties: 18
      type: object
      properties:
        id:
          $ref: '#/components/schemas/IdString'
        order_id:
          $ref: '#/components/schemas/IdString'
        warehouse_id:
          $ref: '#/components/schemas/IdStringNullable'
        product_id:
          $ref: '#/components/schemas/IdString'
        variant_id:
          $ref: '#/components/schemas/IdString'
        sku:
          type: string
          maxLength: 50
        upc:
          type: string
          maxLength: 25
        title:
          type: string
          maxLength: 200
        quantity:
          $ref: '#/components/schemas/IntegerString'
        quantity_to_fulfill:
          $ref: '#/components/schemas/IntegerString'
        total_discount:
          $ref: '#/components/schemas/DecimalString'
        price:
          $ref: '#/components/schemas/DecimalString'
        subtotal:
          $ref: '#/components/schemas/DecimalString'
        total_price:
          $ref: '#/components/schemas/DecimalString'
        inventory_transfer_id:
          $ref: "#/components/schemas/IdStringNullable"
        estimated_ship_date:
          $ref: "#/components/schemas/DateStringNullable"
        created_at:
          $ref: '#/components/schemas/DateTimeString'
        updated_at:
          $ref: '#/components/schemas/DateTimeString'
    OrderRequest:
      additionalProperties: false
      minProperties: 2
      type: object
      properties:
        requested_ship_date:
          $ref: '#/components/schemas/DateString'
        internal_order_number:
          type: string
    OrderCreateRequest:
      additionalProperties: false
      minProperties: 2
      type: object
      properties:
        order_type:
          type: string
        retailer_id:
          type: integer
        requested_ship_date:
          type: string
        total_shipping:
          type: number
        purchase_order_number:
          type: string
        currency_code:
          type: string
        notes:
          type: string
        shipping_address:
          type: object
          properties:
            email:
              type: string
            first_name:
              type: string
            last_name:
              type: string
            company_name:
              type: string
            address1:
              type: string
            address2:
              type: string
            city:
              type: string
            state:
              type: string
            country:
              type: string
            zip_code:
              type: string
            phone:
              type: string
        order_items:
          type: object
          properties:
            variant_id:
              type: integer
            warehouse_id:
              type: integer
            quantity:
              type: integer
            price:
              type: number
            inventory_transfer_id:
              type: integer
    Order:
      additionalProperties: false
      minProperties: 37
      type: object
      properties:
        id:
          $ref: '#/components/schemas/IdString'
        retailer_id:
          $ref: '#/components/schemas/IdStringNullable'
        credit_term_id:
          $ref: '#/components/schemas/IdStringNullable'
        customer:
          type: [ object, 'null' ]
          additionalProperties: false
          properties:
            first_name:
              type: string
            last_name:
              type: string
            email_address:
              type: string
            accepts_marketing:
              type: boolean
        billing_address:
          $ref: '#/components/schemas/Address'
        shipping_address:
          $ref: '#/components/schemas/Address'
        retailer:
          $ref: '#/components/schemas/Retailer'
        order_type:
          type: string
        order_status:
          type: string
        purchase_order_number:
          type: [ string, 'null' ]
        internal_order_number:
          type: [ string, 'null' ]
        ecommerce_id:
          type: [ string, 'null' ]
        order_number:
          type: string
        discount_code:
          type: string
        verification_code:
          type: string
        risk_level:
          type: string
          enum: [ 'unknown', 'not_assessed', 'normal', 'elevated', 'highest' ]
        requested_ship_date:
          $ref: '#/components/schemas/DateStringNullable'
        shipped_date:
          $ref: '#/components/schemas/DateTimeStringNullable'
        delivery_date:
          $ref: '#/components/schemas/DateTimeStringNullable'
        fulfillment_status:
          type: string
          enum: [ 'unfulfilled', 'fulfilled', 'partially_fulfilled', 'cancelled' ]
        shipping_lines:
          type: array
          items:
            $ref: '#/components/schemas/OrderShippingLine'
        order_items:
          type: array
          items:
            $ref: '#/components/schemas/OrderItem'
        fulfillments:
          type: array
          items:
            $ref: '#/components/schemas/Fulfillment'
        refunds:
          type: array
          items:
            $ref: '#/components/schemas/Refund'
        dealer_order:
          $ref: '#/components/schemas/DealerOrder'
        payment_method:
          type: string
          enum: [ 'stripe', 'credit', 'external' ]
        payment_status:
          type: string
          enum: [
            'authorized', 'requested', 'paid', 'voided',
            'Requested', 'Paid', 'On Hold',
          ]
        credit_term:
          type: [ string, 'null' ]
          enum: [ null, '15 Days', '30 Days', '45 Days', '60 Days', '90 Days', '120 Days', '180 Days' ]
        currency_code:
          type: string
          maxLength: 3
        total_discount:
          $ref: '#/components/schemas/DecimalStringNullable'
        subtotal:
          $ref: '#/components/schemas/DecimalString'
        total_shipping:
          $ref: '#/components/schemas/DecimalString'
        total_tax:
          $ref: '#/components/schemas/DecimalString'
        total_price:
          $ref: '#/components/schemas/DecimalString'
        total_refund:
          $ref: '#/components/schemas/DecimalString'
        net_total:
          $ref: '#/components/schemas/DecimalString'
        created_at:
          $ref: '#/components/schemas/DateTimeString'
        updated_at:
          $ref: '#/components/schemas/DateTimeString'
    Address:
      additionalProperties: false
      minProperties: 12
      type: object
      properties:
        first_name:
          type: string
        last_name:
          type: string
        company_name:
          type: string
        address1:
          type: string
        address2:
          type: string
        city:
          type: string
        state:
          type: string
        country:
          type: string
        zip_code:
          type: string
        phone:
          type: string
        latitude:
          type: string
        longitude:
          type: string
    Warehouse:
      type: object
      additionalProperties: false
      minProperties: 6
      properties:
        id:
          $ref: '#/components/schemas/IdString'
        source_id:
          type: [ string, 'null' ]
          maxLength: 100
        name:
          type: string
          maxLength: 100
        is_active:
          type: boolean
        created_at:
          $ref: '#/components/schemas/DateTimeString'
        updated_at:
          $ref: '#/components/schemas/DateTimeString'
    DealerOrder:
      type: [ object, 'null' ]
      additionalProperties: false
      minProperties: 14
      properties:
        id:
          $ref: '#/components/schemas/IdString'
        subtotal:
          $ref: '#/components/schemas/DecimalString'
        total_price:
          $ref: '#/components/schemas/DecimalString'
        total_discount:
          $ref: '#/components/schemas/DecimalString'
        total_shipping:
          $ref: '#/components/schemas/DecimalString'
        total_tax:
          $ref: '#/components/schemas/DecimalString'
        total_refund:
          $ref: '#/components/schemas/DecimalString'
        net_total:
          $ref: '#/components/schemas/DecimalString'
        fulfillment_status:
          type: string
          enum: [ 'unfulfilled', 'fulfilled', 'partially_fulfilled', 'cancelled' ]
        created_at:
          $ref: '#/components/schemas/DateTimeString'
        updated_at:
          $ref: '#/components/schemas/DateTimeString'
        shipping_lines:
          type: array
          items:
            $ref: '#/components/schemas/OrderShippingLine'
        order_items:
          type: array
          items:
            $ref: '#/components/schemas/OrderItem'
        fulfillments:
          type: array
          items:
            $ref: '#/components/schemas/Fulfillment'
        refunds:
          type: array
          items:
            $ref: '#/components/schemas/Refund'
    Courier:
      type: [ object, 'null' ]
      additionalProperties: false
      minProperties: 2
      properties:
        id:
          $ref: '#/components/schemas/IdString'
        name:
          type: string
    InventoryTransfer:
      type: [ object, 'null' ]
      additionalProperties: false
      minProperties: 10
      properties:
        id:
          $ref: '#/components/schemas/IdString'
        transfer_id:
          type: string
        reference_id:
          type: string
        status:
          type: string
        quantity:
          $ref: '#/components/schemas/IntegerString'
        destination_warehouse_id:
          $ref: '#/components/schemas/IntegerString'
        expected_arrival_date:
          type: string
        created_at:
          type: string
        updated_at:
          type: string
        products:
          type: array
          items:
            $ref: '#/components/schemas/InventoryTransferProduct'
    InventoryTransferRequest:
      additionalProperties: false
      minProperties: 6
      type: object
      properties:
        reference_id:
          type: string
        expected_arrival_date:
          $ref: '#/components/schemas/DateString'
        destination_warehouse_id:
          $ref: '#/components/schemas/IdInteger'
        products:
          type: array
          items:
            type: object
            properties:
              variant_id: 
                $ref: '#/components/schemas/IdInteger'
              quantity: 
                $ref: '#/components/schemas/IdInteger'
    InventoryTransferProduct:
      type: [ object, 'null' ]
      additionalProperties: false
      minProperties: 6
      properties:
        variant_id:
          $ref: '#/components/schemas/IdString'
        quantity:
          $ref: '#/components/schemas/IntegerString'
        quantity_received:
          $ref: '#/components/schemas/IntegerString'
        sku:
          type: string
          maxLength: 50
        upc:
          type: string
          maxLength: 25
        title:
          type: string
          maxLength: 200
    Error:
      additionalProperties: false
      minProperties: 3
      properties:
        name:
          type: string
        message:
          type: string
        url:
          type: string
        data:
          type: object
    Status:
      additionalProperties: false
      minProperties: 1
      properties:
        result:
          type: string
    ProductLocatorRetailer:
      type: [ object, 'null' ]
      additionalProperties: false
      minProperties: 16
      properties:
        id:
          $ref: '#/components/schemas/IdString'
        distance:
          $ref: '#/components/schemas/DecimalString'
        distance_unit:
          type: string
        delivery_methods:
          type: array
          items:
            type: string
        email_address:
          type: string
        company_name:
          type: string
        store_timing:
          type: array
          items:
            type: array
            items:
              properties:
                weekday:
                  type: string
                start:
                  type: string
                end:
                  type: string
                Closed:
                  type: boolean
        city:
          type: string
        state:
          type: string
        country:
          type: string
        state_code:
          type: string
        country_code:
          type: string
        zip_code:
          type: string
        latitude:
          type: string
        longitude:
          type: string
        address1:
          type: string
        address2:
          type: string
        telephone:
          type: string
  headers:
    RateLimit-Limit: 
      description: The number of allowed requests in the current period
      schema:
        type: integer
    RateLimit-Remaining: 
      description: The number of remaining requests in the current period
      schema:
        type: integer
    RateLimit-Reset: 
      description: The number of seconds left in the current period
      schema:
        type: integer
    Retry-After: 
      description: The number of allowed requests in the current period
      schema:
        type: integer
  parameters:
    count:
      name: count
      in: path
      required: true
      description: count action
      schema:
        type: string
        pattern: count
        example: count
        maxLength: 5
    retailer_id:
      name: retailer_id
      in: path
      required: true
      description: Id of a retailer
      schema:
        $ref: '#/components/schemas/IdInteger'
    branch_id:
      name: branch_id
      in: path
      required: true
      description: Id of a branch
      schema:
        $ref: '#/components/schemas/IdInteger'
    variant_id:
      name: variant_id
      in: path
      required: true
      description: Id of a product variant
      schema:
        $ref: '#/components/schemas/IdInteger'
    product_id:
      name: product_id
      in: path
      required: true
      description: Id of a product.
      example: 26980
      schema:
        $ref: '#/components/schemas/IdInteger'
    pricing_tier_id:
      name: pricing_tier_id
      in: path
      required: true
      description: Id of a pricing tier.
      example: 26463
      schema:
        $ref: '#/components/schemas/IdInteger'
    sales_rep_id:
      name: sales_rep_id
      in: path
      required: true
      description: Id of a sales rep.
      example: 28767
      schema:
        $ref: '#/components/schemas/IdInteger'
    territory_id:
      name: territory_id
      in: path
      required: true
      description: Id of a territory.
      example: 29797
      schema:
        $ref: '#/components/schemas/IdInteger'
    order_id:
      name: order_id
      in: path
      required: true
      description: Id of an order.
      example: 65345
      schema:
        $ref: '#/components/schemas/IdInteger'
    order_item_id:
      name: order_item_id
      in: path
      required: true
      description: Id of an order item.
      example: 92823
      schema:
        $ref: '#/components/schemas/IdInteger'
    refund_id:
      name: refund_id
      in: path
      required: true
      description: Id of a refund.
      example: 23451
      schema:
        $ref: '#/components/schemas/IdInteger'
    fulfillment_id:
      name: fulfillment_id
      in: path
      required: true
      description: Id of a fulfillment.
      example: 12252
      schema:
        $ref: '#/components/schemas/IdInteger'
    warehouse_id:
      name: warehouse_id
      in: path
      required: true
      description: Id of a warehouse.
      example: 5231
      schema:
        $ref: '#/components/schemas/IdInteger'
    courier_id:
      name: courier_id
      in: path
      required: true
      description: Id of a courier.
      example: 62435
      schema:
        $ref: '#/components/schemas/IdInteger'
    inventory_transfer_id:
      name: inventory_transfer_id
      in: path
      required: true
      description: Id of an inventory transfer.
      example: 462346
      schema:
        $ref: '#/components/schemas/IdInteger'
    credit_id:
      name: credit_id
      in: path
      required: true
      description: Id of an retailer credit.
      example: 45262
      schema:
        $ref: '#/components/schemas/IdInteger'
    payment_id:
      name: payment_id
      in: path
      required: true
      description: Id of an retailer credit payment.
      example: 98765
      schema:
        $ref: '#/components/schemas/IdInteger'
    item_id:
      name: item_id
      in: path
      required: true
      description: Id of an retailer credit item.
      example: 23452
      schema:
        $ref: '#/components/schemas/IdInteger'
    page:
      name: page
      in: query
      description: Page number to fetch.
      required: false
      allowEmptyValue: false
      example: 1
      schema:
        $ref: '#/components/schemas/PageInteger'
    limit:
      name: limit
      in: query
      description: Limit number of results fetched. Default is 50, maximum is 250.
      required: false
      allowEmptyValue: false
      example: 50
      schema:
        $ref: '#/components/schemas/LimitInteger'
    sort:
      name: sort
      in: query
      description: 'Order results by a field in the result set.
        Descending order can be achieved by adding a "-" in front of the field name.'
      allowEmptyValue: false
      required: false
      example: -updated_at
      schema:
        type: string
    filter:
      name: 'filter[updated_at >]'
      in: query
      description: 'Filter results by a field in the result set.
        More advanced filtering can be achieved by adding a space after the fields name and adding adding one of the following comparison operators: =, !=, <, >, <=, >=, LIKE.'
      example: '2020-01-01 00:00:00'
      schema:
        additionalProperties: false
        oneOf:
          - type: string
          - $ref: '#/components/schemas/PriceDecimal'
          - $ref: '#/components/schemas/Integer'
          - type: boolean
    zip_code:
      name: zip_code
      in: query
      required: false
      description: a 5 digit zip_code
      example: 90210
      schema:
        type: string
    postal_code:
      name: postal_code
      in: query
      required: false
      description: a 6 digit postal_code
      example: P7E4T9
      schema:
        type: string
    delivery_method:
      name: delivery_method
      in: query
      required: false
      description: a delivery method
      example: in_store
      schema:
        type: string
  securitySchemes:
    basicAuth:
      type: http
      scheme: basic
