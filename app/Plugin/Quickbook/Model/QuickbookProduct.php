<?php
App::uses('QuickbookAppModel', 'Quickbook.Model');

/**
 * Class QuickbookProduct
 */
class QuickbookProduct extends QuickbookAppModel
{
    public $useTable = 'products';

    public function getProductInfo(string $upc, int $retailerId): array
    {
        return (array)$this->find('first', [
            'conditions' => [
                "{$this->alias}.userId" => $retailerId,
                "{$this->alias}.upc" => $upc,
            ],
            'fields' => [
                'id',
                'userId',
                'listId',
                'upc',
                'price',
                'tax',
                'qty',
                'created',
                'updated',
            ],
        ]);
    }

    /**
     * @param int $userId
     * @param string $itemId
     * @param int $qty
     * @return bool
     */
    public function detectQty($userId, $itemId, $qty): bool
    {
        return $this->updateAllJoinless(["{$this->alias}.qty" => "{$this->alias}.qty - {$qty}"], [
            "{$this->alias}.userId" => $userId,
            "{$this->alias}.listId" => $itemId,
        ]);
    }

    /**
     * @param int $retailerId
     * @param string[] $upcList
     * @return int[] Map of id by upc
     */
    public function findForInventoryUpdate(int $retailerId, array $upcList): array
    {
        return (array)$this->find('list', [
            'conditions' => [
                "{$this->alias}.userId" => $retailerId,
                "{$this->alias}.upc" => $upcList,
            ],
            'fields' => ['upc', 'id'],
        ]);
    }
}
