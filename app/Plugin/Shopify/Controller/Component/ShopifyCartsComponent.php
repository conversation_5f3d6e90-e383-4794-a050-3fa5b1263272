<?php
App::uses('AppComponent', 'Controller/Component');
App::uses('Hash', 'Utility');
App::uses('ProductStatus', 'Utility');

/**
 * Shopify Cart Handler Component.
 *
 * Abstraction for the Shopify cart session.
 *
 * @property SessionComponent $Session
 *
 * @property Product $Product
 * @property ProductStateFee $ProductStateFee
 * @property User $User
 */
class ShopifyCartsComponent extends AppComponent
{
    public $components = [
        'Session',
    ];

    public $uses = [
        'Product',
        'ProductStateFee',
        'User',
    ];

    public $sessionKey = 'Shopify.cart';

    public $backupKey = 'Shopify.cart_backup';

    public static function getNewEmptyCart(?string $token, string $currency): array
    {
        return [
            'token' => $token ?? bin2hex(random_bytes(16)),
            'note' => null,
            'attributes' => [],
            'original_total_price' => 0,
            'total_price' => 0,
            'total_discount' => 0,
            'total_weight' => 0,
            'item_count' => 0,
            'items' => [],
            'requires_shipping' => false,
            'currency' => $currency,
            'items_subtotal_price' => 0,
            'cart_level_discount_applications' => [],
        ];
    }

    public static function getCartWithAddedItems(array $cart, array $newItems): array
    {
        $existingItems = (array)$cart['items'];

        $allVariantIds = array_keys(
            array_column($existingItems, 'variant_id', 'variant_id')
            + array_column($newItems, 'variant_id', 'variant_id')
        );

        $existingItemsByVariantId = Hash::combine($existingItems, '{n}.variant_id', '{n}');
        $newItemsByVariantId = Hash::combine($newItems, '{n}.variant_id', '{n}');

        $mergedItems = [];
        foreach ($allVariantIds as $variantId) {
            $existingItem = (array)($existingItemsByVariantId[$variantId] ?? []);
            $newItem = (array)($newItemsByVariantId[$variantId] ?? []);
            if ($existingItem) {
                $additiveIntFields = [
                    'quantity',
                    'line_price',
                    'original_line_price',
                    'final_line_price',
                ];
                foreach ($additiveIntFields as $field) {
                    $existingItem[$field] += (int)($newItem[$field] ?? 0);
                }
                $mergedItems[] = $existingItem;
            } else {
                $mergedItems[] = $newItem;
            }
        }

        return static::getCartWithReplacedItems($cart, $mergedItems);
    }

    public static function getCartWithReplacedItems(array $cart, array $items): array
    {
        $originalTotalPriceCents = (int)array_sum(array_column($items, 'original_line_price'));
        $totalPriceCents = (int)array_sum(array_column($items, 'line_price'));
        $totalDiscountCents = (int)array_sum(array_column($items, 'total_discount'));
        $totalWeightGrams = (int)array_sum(array_column($items, 'grams'));
        $itemCount = (int)array_sum(array_column($items, 'quantity'));
        $requiresShipping = (bool)array_sum(array_column($items, 'requires_shipping'));

        return array_merge($cart, [
            'original_total_price' => $originalTotalPriceCents,
            'total_price' => $totalPriceCents,
            'total_discount' => $totalDiscountCents,
            'total_weight' => $totalWeightGrams,
            'item_count' => $itemCount,
            'items' => $items,
            'requires_shipping' => $requiresShipping,
            'items_subtotal_price' => $totalPriceCents,
        ]);
    }

    public function getPreselectedRetailerId(): ?int
    {
        return (int)$this->getValue('attributes.shipearly_preselect_retailerId') ?: null;
    }

    public function getPreselectedDeliveryOption(): ?string
    {
        return (string)$this->getValue('attributes.shipearly_preselect_deliveryOption') ?: null;
    }

    public function resetPreselectedOptions(): ?array
    {
        $cart = (array)$this->get();
        if (!$cart) {
            return null;
        }

        unset(
            $cart['attributes']['shipearly_preselect_deliveryOption'],
            $cart['attributes']['shipearly_preselect_retailerId']
        );

        if (!$this->set($cart)) {
            return null;
        }

        return $cart;
    }

    /**
     * @param int $brandId
     * @param array $updates Map of quantity by variant_id compatible with the Shopify Ajax API cart/update.js endpoint.
     * @param array|null $cart
     * @return array
     * @see https://shopify.dev/docs/api/ajax/reference/cart#post-locale-cart-update-js
     */
    public function applyUpdates(int $brandId, array $updates, ?array $cart = null): array
    {
        $cart = (array)($cart ?? $this->get());

        $token = $cart['token'] ?? null;
        $currency = $cart['currency'] ?? (string)$this->User->fieldByConditions('currency_code', ['User.id' => $brandId]);

        $cart += static::getNewEmptyCart($token, $currency);

        $items = $this->findItemsForUpdates($brandId, $updates);

        $cart = static::getCartWithReplacedItems($cart, $items);

        $this->set($cart);

        return $cart;
    }

    public function findItemsForUpdates(int $brandId, array $quantityByVariantId): array
    {
        $this->Product->bindModel(['hasAndBelongsToMany' => ['VariantOption' => ['with' => 'ProductVariantOption', 'unique' => 'keepExisting']]], false);
        $products = (array)$this->Product->find('all', [
            'contain' => [
                'VariantOption' => [
                    'with' => ['ProductVariantOption' => ['id', 'product_id', 'variant_option_id', 'value']],
                    'fields' => ['id', 'name'],
                    'order' => ['VariantOption.position' => 'ASC'],
                ],
            ],
            'conditions' => [
                'Product.productID' => array_keys($quantityByVariantId),
                'Product.user_id' => $brandId,
                'Product.product_status' => ProductStatus::ENABLED,
                'Product.deleted' => false,
            ],
            'fields' => [
                'Product.id',
                'Product.source_product_id',
                'Product.productID',
                'Product.product_type',
                'Product.product_title',
                'Product.product_sku',
                'Product.product_price',
                'Product.compare_at_price',
                'Product.weight',
                'Product.weight_unit',
                'Product.product_image',
                'Product.product_description',
                'Product.vendor',
                'Product.product_handle',
                'Product.product_name',
            ],
        ]);
        $this->Product->unbindModel(['hasAndBelongsToMany' => ['VariantOption']], false);

        return array_map(function($product) use ($quantityByVariantId) {
            $variantId = (int)$product['Product']['productID'];
            $quantity = (int)$quantityByVariantId[$variantId];

            $priceCents = (int)round($product['Product']['product_price'] * 100);
            $linePriceCents = (int)round($priceCents * $quantity);
            $compareAtPriceCents = ($product['Product']['compare_at_price'] !== null)
                ? (int)round($product['Product']['compare_at_price'] * 100)
                : null;

            $title = (string)$product['Product']['product_title'];
            $productTitle = (string)$product['Product']['product_name'];
            $image = ((string)$product['Product']['product_image']) ?: null;

            $variantTitle = null;
            $optionsWithValues = [
                ['name' => 'Title', 'value' => 'Default Title'],
            ];
            if ($product['VariantOption']) {
                $variantTitle = implode(' / ', Hash::extract($product['VariantOption'], '{n}.ProductVariantOption.value'));
                $optionsWithValues = array_map(
                    fn(array $option): array => ['name' => $option['name'], 'value' => $option['ProductVariantOption']['value']],
                    $product['VariantOption']
                );
            }

            return [
                'id' => $variantId,
                'properties' => null,
                'quantity' => $quantity,
                'variant_id' => $variantId,
                'key' => $variantId . ':' . bin2hex(random_bytes(16)),
                'title' => $title,
                'price' => $priceCents,
                'original_price' => $priceCents,
                'discounted_price' => $priceCents,
                'line_price' => $linePriceCents,
                'original_line_price' => $linePriceCents,
                'total_discount' => 0,
                'discounts' => [],
                'sku' => $product['Product']['product_sku'],
                'grams' => (int)round(convertWeightToGrams((float)$product['Product']['weight'], (string)$product['Product']['weight_unit'])),
                'vendor' => $product['Product']['vendor'],
                'taxable' => true,
                'product_id' => (int)$product['Product']['source_product_id'],
                'product_has_only_default_variant' => ($variantTitle === null),
                'gift_card' => false,
                'final_price' => $priceCents,
                'final_line_price' => $linePriceCents,
                'url' => null,
                'featured_image' => [
                    'aspect_ratio' => null,
                    'alt' => ($image) ? $productTitle : null,
                    'height' => null,
                    'url' => $image,
                    'width' => null,
                ],
                'image' => $image,
                // The slug of product name (without variant) is the default 'handle' in Shopify
                'handle' => $product['Product']['product_handle'] ?: generate_slug($productTitle),
                'requires_shipping' => true,
                'product_type' => $product['Product']['product_type'],
                'product_title' => $productTitle,
                'product_description' => $product['Product']['product_description'],
                'variant_title' => $variantTitle,
                'variant_options' => array_column($optionsWithValues, 'value'),
                'options_with_values' => $optionsWithValues,
                'line_level_discount_allocations' => [],
                'line_level_total_discount' => 0,
                'quantity_rule' => [
                    'min' => 1,
                    'max' => null,
                    'increment' => 1,
                ],
                'has_components' => false,
                'compare_at_price' => $compareAtPriceCents,
            ];
        }, $products);
    }

    /**
     * @param array $attributes Key-value pairs compatible with the Shopify Ajax API cart/update.js endpoint.
     *                          Empty values clear the attribute.
     * @param array|null $cart
     * @return array
     * @see https://shopify.dev/docs/api/ajax/reference/cart#update-cart-attributes
     */
    public function applyAttributes(array $attributes, ?array $cart = null): array
    {
        $cart = (array)($cart ?? $this->get());

        $cart['attributes'] = array_filter(array_merge($cart['attributes'] ?? [], $attributes));

        return $cart;
    }

    public function applyStateCountryFee(int $userId, int $countryId, int $stateId): ?array
    {
        $cart = (array)$this->get();
        if (!$cart) {
            return null;
        }

        $cart = $this->ProductStateFee->applyFeeToShopifyCart($userId, $countryId, $stateId, $cart);
        if (!$this->set($cart)) {
            return null;
        }

        return $cart;
    }

    public function applyRegionalCurrencyPricing(int $brandId, ?string $regionalCurrency, ?string $brandCurrency): ?array
    {
        $cart = (array)$this->get();
        if (!$cart) {
            return null;
        }

        if (empty($regionalCurrency) || empty($cart['currency'])) {
            return $cart;
        }

        $products = $this->Product->findAllWithRegionalPricing($brandId, $regionalCurrency, [
            'conditions' => $this->Product->getConditionsForActiveProducts([
                'Product.productID' => array_column($cart['items'], 'variant_id')
            ]),
            'fields' => [
                'Product.id',
                'Product.productID',
                'Product.product_price',
                'Product.compare_at_price',
                'Product.currency'
            ],
        ], $brandCurrency);

        $regionalCurrency = current(Hash::extract($products, '{n}.RegionalPrice.currency_code'));
        $productPriceMap = Hash::combine($products, '{n}.Product.productID', '{n}.RegionalPrice.price');
        $compareAtPriceMap = Hash::combine($products, '{n}.Product.productID', '{n}.RegionalPrice.compare_at_price');

        $cart['items'] = array_map(function($item) use ($compareAtPriceMap) {
            return array_merge($item, [
                'compare_at_price' => isset($compareAtPriceMap[$item['variant_id']]) ? (int)round($compareAtPriceMap[$item['variant_id']] * 100) : null,
            ]);
        }, $cart['items']);

        $cart['items'] = array_map(function($item) use ($productPriceMap) {
            $priceCents = (int)round($productPriceMap[$item['variant_id']] * 100);
            $linePriceCents = (int)($priceCents * $item['quantity']);

            return array_merge($item, [
                'price' => $priceCents,
                'original_price' => $priceCents,
                'discounted_price' => $priceCents,
                'line_price' => $linePriceCents,
                'original_line_price' => $linePriceCents,
                'final_line_price' => $linePriceCents,
            ]);
        }, $cart['items']);

        $newSubtotalCents = (int)array_sum(array_column($cart['items'], 'line_price'));

        $cart = array_merge($cart, [
            'original_total_price' => $newSubtotalCents,
            'total_price' => $newSubtotalCents,
            'currency' => $regionalCurrency,
            'items_subtotal_price' => $newSubtotalCents,
        ]);

        if (!$this->set($cart)) {
            return null;
        }

        return $cart;
    }

    public function applyLocalization(int $brandId, ?string $locale = null): ?array
    {
        $cart = (array)$this->get();
        if (!$cart) {
            return null;
        }

        if ($locale !== null) {
            $this->Product->initializeTranslatedVirtualFields($locale);
        }

        $productMap = Hash::combine(
            $this->Product->find('all', [
                'recursive' => -1,
                'conditions' => [
                    'Product.user_id' => $brandId,
                    'Product.productID' => array_column($cart['items'], 'variant_id'),
                    'Product.product_status' => ProductStatus::ENABLED,
                    'Product.deleted' => false,
                ],
                'fields' => ['id', 'productID', 'product_title', 'product_name'],
            ]),
            '{n}.Product.productID',
            '{n}'
        );

        if ($locale !== null) {
            $this->Product->initializeTranslatedVirtualFields();
        }

        $cart['items'] = array_map(fn(array $item): array => array_merge($item, [
            'title' => $productMap[$item['variant_id']]['Product']['product_title'] ?? $item['title'],
            'product_title' => $productMap[$item['variant_id']]['Product']['product_name'] ?? $item['product_title'],
        ]), $cart['items']);

        if (!$this->set($cart)) {
            return null;
        }

        return $cart;
    }

    public function applyDiscount(array $itemDiscounts): ?array
    {
        $cart = (array)$this->get();
        if (!$cart) {
            return null;
        }

        $cart['items'] = array_map(function($item) use ($itemDiscounts) {
            $discount = $itemDiscounts[$item['variant_id']] ?? [];
            if (!$discount) {
                return $item;
            }

            $discountAmount = (int)round($discount['discount_amount'] * 100);

            $price = $item['line_price'];
            $priceDiscounted = (int)max($price - $discountAmount, 0);
            $discountAmount = (int)($price - $priceDiscounted);

            return array_merge($item, [
                'total_discount' => $discountAmount,
                'final_line_price' => $priceDiscounted,
            ]);
        }, $cart['items']);

        $discountAmount = (int)array_sum(array_column($cart['items'], 'total_discount'));

        $price = $cart['total_price'];
        $priceDiscounted = (int)max($price - $discountAmount, 0);
        $discountAmount = (int)($price - $priceDiscounted);

        $cart = array_merge($cart, [
            'total_price' => $priceDiscounted,
            'total_discount' => $discountAmount,
        ]);

        if (!$this->set($cart)) {
            return null;
        }

        return $cart;
    }

    public function backup(?array $cart = null): bool
    {
        $cart = (array)($cart ?? $this->get());

        if (!$cart || !$this->set($cart)) {
            return false;
        }

        return (bool)$this->Session->write($this->backupKey, $cart);
    }

    public function restore(): ?array
    {
        $backup = (array)$this->Session->read($this->backupKey);

        if (!$backup || !$this->set($backup)) {
            return null;
        }

        return $backup;
    }

    /**
     * Get a single value specified by $path from the cart.
     *
     * @param string|null $path
     * @return mixed Value from the cart
     */
    public function getValue(?string $path = null)
    {
        $value = $this->get();
        if ($path) {
            $value = Hash::get($value, $path);
        }
        return $value;
    }

    public function get(): ?array
    {
        return ((array)$this->Session->read($this->sessionKey)) ?: null;
    }

    public function set(?array $cart): bool
    {
        return (bool)$this->Session->write($this->sessionKey, $cart);
    }
}
