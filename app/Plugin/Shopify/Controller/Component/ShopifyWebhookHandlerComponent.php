<?php
App::uses('AppComponent', 'Controller/Component');
App::uses('Order', 'Model');
App::uses('OrderAction', 'Utility');
App::uses('OrderPaymentMethod', 'Utility');
App::uses('OrderPaymentStatus', 'Utility');
App::uses('OrderStatus', 'Utility');
App::uses('OrderType', 'Utility');
App::uses('ProductSourceType', 'Utility');

/**
 * ShopifyWebhookHandler Component.
 *
 * @property CurrencyComponent $Currency
 * @property FulfillmentLogicComponent $FulfillmentLogic
 * @property NotificationLogicComponent $NotificationLogic
 * @property OrderLogicComponent $OrderLogic
 * @property StripeComponent $Stripe
 * @property ShopifyComponent $Shopify
 *
 * @property Courier $Courier
 * @property Customer $Customer
 * @property DealerOrder $DealerOrder
 * @property Fulfillment $Fulfillment
 * @property Notification $Notification
 * @property Order $Order
 * @property OrderAddress $OrderAddress
 * @property OrderProduct $OrderProduct
 * @property OrderRefund $OrderRefund
 * @property Product $Product
 * @property AppModel $ProductImage
 * @property ProductTitle $ProductTitle
 * @property ProductVariantOption $ProductVariantOption
 * @property StripeUser $StripeUser
 * @property Tag $Tag
 * @property User $User
 * @property UserCategories $UserCategories
 * @property VariantOption $VariantOption
 * @property Warehouse $Warehouse
 * @property WarehouseProduct $WarehouseProduct
 */
class ShopifyWebhookHandlerComponent extends AppComponent
{
    const DEFAULT_TITLE = 'Default Title';

    public $components = [
        'Currency',
        'FulfillmentLogic',
        'NotificationLogic',
        'OrderLogic',
        'Stripe.Stripe',
        'Shopify.Shopify',
    ];

    public $uses = [
        'Courier',
        'Customer',
        'DealerOrder',
        'Fulfillment',
        'Notification',
        'Order',
        'OrderAddress',
        'OrderProduct',
        'OrderRefund',
        'Product',
        'ProductImage',
        'ProductTitle',
        'ProductVariantOption',
        'StripeUser',
        'Tag',
        'User',
        'UserCategories',
        'VariantOption',
        'Warehouse',
        'WarehouseProduct',
    ];

    public function handleWebhookTask(array $params): void
    {
        $params = $this->validateTaskParams($params);

        $user = $this->User->findById($params['user_id'], ['id'], null, -1);
        if (empty($user['User']['id'])) {
            throw new UnauthorizedException('User not found for user_id=' . json_encode($params['user_id']));
        }
        $userId = (int)$user['User']['id'];
        $topic = (string)$params['topic'];
        $payload = (array)$params['payload'];

        switch ($topic) {
        case 'fulfillments/create':
        case 'fulfillments/update':
            $this->syncFulfillment($userId, $payload);
            break;
        case 'inventory_levels/connect':
        case 'inventory_levels/update':
        case 'inventory_levels/disconnect':
            if (!$this->syncInventory($userId, $payload['inventory_item_id'], $payload['location_id'], $payload)) {
                throw new InternalErrorException(json_encode(['errors' => $this->WarehouseProduct->validationErrors, 'data' => $this->WarehouseProduct->data]));
            }
            break;
        case 'locations/create':
        case 'locations/update':
        case 'locations/delete':
            $payloadId = $payload['id'];
            if ($topic === 'locations/delete') {
                $payload = [];
            }
            if (!$this->Warehouse->syncShopifyLocation($userId, $payloadId, $payload)) {
                throw new InternalErrorException(json_encode(['errors' => $this->Warehouse->validationErrors, 'data' => $this->Warehouse->data]));
            }
            break;
        case 'orders/create':
        case 'orders/updated':
            $this->syncOrder($userId, $payload);
            break;
        case 'products/create':
        case 'products/update':
            $this->syncProduct($userId, $payload);
            break;
        case 'products/delete':
            $this->Product->removeAll(['Product.user_id' => $userId, 'Product.source_product_id' => $payload['id']]);
            break;
        case 'refunds/create':
            $this->syncRefund($userId, $payload);
            break;
        default:
            throw new BadRequestException("Shopify Webhook for '{$topic}' not supported");
        }
    }

    private function validateTaskParams(array $params)
    {
        $params = array_intersect_key($params, array_flip(['user_id', 'topic', 'api_version', 'payload']));
        $missingParams = array_diff_key($params, array_filter($params));
        if ($missingParams) {
            throw new InvalidArgumentException('Missing required params: ' . implode(', ', array_keys($missingParams)));
        }
        return $params;
    }

    public function handleUpdateCron(array $user, array $cron): void
    {
        $userId = (int)$cron['UpdateCron']['user_id'];
        $apiId = $cron['UpdateCron']['unique_id'];
        $type = $cron['UpdateCron']['type'];
        switch ($type) {
            case 'inventory_levels/connect':
            case 'inventory_levels/update':
            case 'inventory_levels/disconnect':
                $inventoryLevelId = json_decode($apiId, true);
                $inventoryItemId = $inventoryLevelId['inventory_item_id'];
                $inventoryLocationId = $inventoryLevelId['location_id'];
                $inventoryLevel = $this->Shopify->getInventoryLevel($inventoryItemId, $inventoryLocationId, $user['User']['api_key'], $user['User']['secret_key'], $user['User']['shop_url']);
                if (!$this->syncInventory($userId, $inventoryItemId, $inventoryLocationId, $inventoryLevel)) {
                    throw new InternalErrorException(json_encode(['errors' => $this->WarehouseProduct->validationErrors, 'data' => $this->WarehouseProduct->data]));
                }
                break;
            case 'locations/create':
            case 'locations/update':
            case 'locations/delete':
                $locationId = (int)$apiId;
                $location = $this->Shopify->getLocation($locationId, $user['User']['api_key'], $user['User']['secret_key'], $user['User']['shop_url']);
                if (!$this->Warehouse->syncShopifyLocation($userId, $locationId, $location)) {
                    throw new InternalErrorException(json_encode(['errors' => $this->Warehouse->validationErrors, 'data' => $this->Warehouse->data]));
                }
                break;
            case 'orders/create':
            case 'orders/updated':
                $order = $this->Shopify->getOrder($apiId, $user['User']['api_key'], $user['User']['secret_key'], $user['User']['shop_url']);
                if (empty($order)) {
                    throw new NotFoundException('Failed to retrieve API order: ' . json_encode($this->getErrorLog($order, $cron, $user)));
                }
                $this->syncOrder($userId, $order);
                break;
            case 'products/create':
            case 'products/update':
                $productId = (int)$apiId;
                $product = $this->Shopify->getProduct($productId, $user['User']['api_key'], $user['User']['secret_key'], $user['User']['shop_url']);
                if (empty($product)) {
                    throw new NotFoundException('Failed to retrieve API product: ' . json_encode($this->getErrorLog($product, $cron, $user)));
                }
                $this->syncProduct($userId, (array)$product);
                break;
            case 'products/delete':
                $productId = (int)$apiId;
                $product = $this->Shopify->getProduct($productId, $user['User']['api_key'], $user['User']['secret_key'], $user['User']['shop_url']);
                if ($product) {
                    throw new BadRequestException('API product was not deleted: ' . json_encode($this->getErrorLog($product, $cron, $user)));
                }
                $this->Product->removeAll(['Product.user_id' => $userId, 'Product.source_product_id' => $apiId]);
                break;
            case 'refunds/create':
                list($orderId, $refundId) = explode('/refunds/', $apiId, 2);
                $refund = $this->Shopify->getOrderRefund($orderId, $refundId, $user['User']['api_key'], $user['User']['secret_key'], $user['User']['shop_url']);
                if (empty($refund)) {
                    throw new NotFoundException('Failed to retrieve API refund: ' . json_encode($this->getErrorLog($refund, $cron, $user)));
                }
                $this->syncRefund($userId, $refund);
                break;
            default:
                throw new BadRequestException(json_encode($this->getErrorLog("Shopify Webhook for '{$type}' not supported", $cron, $user)));
        }
    }

    private function getErrorLog($message, array $cron, array $user): array
    {
        return [
            'message' => $message,
            'UpdateCron' => array_intersect_key($cron['UpdateCron'], array_flip(['id', 'type', 'unique_id', 'comments'])),
            'User' => array_intersect_key($user['User'], array_flip(['id', 'uuid', 'email_address', 'company_name', 'shop_url'])),
        ];
    }

    public function syncOrder($userId, $api_order, $ordtype = 'Create')
    {
        $existing = $this->Order->findExistingForWebhook((int)$userId, (string)$api_order['id'], (string)$api_order['order_number']);
        if (empty($existing['Order']['id'])) {
            CakeLog::debug(json_encode([
                'message' => 'Skipping order not created by this app',
                'api_order' => array_intersect_key($api_order, array_flip(['id', 'email', 'created_at', 'updated_at', 'name', 'order_number'])),
                'user_id' => $userId,
            ]));
            return true;
        }

        $user = $this->_getUserWithShopAppId($userId);

        $shopAppId = $user['User']['shop_app_id'];
        if ($api_order['app_id'] !== (int)$shopAppId) {
            CakeLog::debug(json_encode([
                'message' => 'Skipping order from a different app',
                'api_order' => array_intersect_key($api_order, array_flip(['id', 'email', 'created_at', 'updated_at', 'name', 'order_number', 'app_id'])),
                'User' => ['id' => $userId, 'shop_app_id' => $shopAppId],
            ]));
            return true;
        }

        $api_order = $this->_applyOrderLineItemTotalDiscounts((array)$api_order, (string)$user['User']['secret_key'], (string)$user['User']['shop_url']);

        if (!empty($existing['Order']['is_retail_order'])) {
            return $this->_syncRetailOrder((int)$userId, (array)$api_order, (array)$existing);
        }

        $tags = !empty($api_order['tags']) ? array_filter(array_map('trim', explode(',', $api_order['tags']))) : [];
        if (in_array('Wholesale', $tags, true)) {
            CakeLog::error(json_encode([
                'message' => 'Attempted to sync wholesale order as direct',
                'api_order' => array_intersect_key($api_order, array_flip(['id', 'email', 'created_at', 'updated_at', 'name', 'order_number', 'note_attributes', 'tags'])),
                'existing' => $existing,
            ]));
            return false;
        }

        $productIDs = array_column($api_order['line_items'], 'variant_id', 'variant_id');
        $productIDMap = Hash::combine(
            $this->Product->find('all', [
                'recursive' => -1,
                'conditions' => ['user_id' => $userId, 'productID' => $productIDs],
                'fields' => ['id', 'productID', 'sales_commission'],
            ]),
            '{n}.Product.productID',
            '{n}'
        );
        $unknownProductIDs = array_keys(array_diff_key($productIDs, $productIDMap));
        if (empty($productIDs) || $unknownProductIDs) {
            CakeLog::warning(json_encode([
                'message' => 'Skipping order with unknown products',
                'unknownProductIDs' => $unknownProductIDs,
                'api_order' => array_intersect_key($api_order, array_flip(['id', 'email', 'created_at', 'updated_at', 'name', 'order_number', 'note_attributes', 'tags', 'line_items'])),
                'existing' => $existing,
            ]));
            return true;
        }

        $orderinfo = array(
            'user_id' => $userId,
            'retailer_id' => null,
            'source_id' => $api_order['id'],
            'orderNO' => $api_order['order_number'],
            'source_order_name' => $api_order['name'],
            'tax_included' => (bool)$api_order['taxes_included'],
            'order_status' => $api_order['financial_status'],
            'created_at' => $api_order['created_at'],
            'updated_at' => $api_order['updated_at'],
            'currency_code' => $api_order['currency'],
            'total_qty_ordered' => array_sum(array_column($api_order['line_items'], 'quantity')),
            'total_tax' => $api_order['total_tax'],
        );
        if (!empty($api_order['discount_codes'][0])) {
            $orderinfo['total_discount'] = $api_order['discount_codes'][0]['amount'] + $existing['Order']['shipping_discount'];
            $orderinfo['discount_code'] = $api_order['discount_codes'][0]['code'];
        } else {
            $orderinfo['total_discount'] = $api_order['total_discounts'] + $existing['Order']['shipping_discount'];
        }
        // Adjust total_price for consistency with retailer orders total_price
        $orderinfo['total_price'] = $api_order['total_price'] + $orderinfo['total_discount'];
        $orderinfo['totalPriceConversion'] = $orderinfo['total_price'];

        if (isset($api_order['customer']) && $this->Customer->syncShopifyCustomer($userId, $api_order['customer'])) {
            $orderinfo = array_merge($orderinfo, array(
                'customerID' => $this->Customer->id,
                'customerEmail' => $api_order['customer']['email'],
            ));
        }
        if (!empty($api_order['shipping_lines'][0])) {
            $orderinfo = array_merge($orderinfo, array(
                'shipping_amount' => $api_order['shipping_lines'][0]['price'] + $existing['Order']['shipping_discount'],
                'order_type' => $api_order['shipping_lines'][0]['code'],
            ));
        }

        if (!empty($api_order['shipping_address'])) {
            $orderinfo = array_merge($orderinfo, $this->_getOrderShippingFromShopifyWebhook($api_order['shipping_address']));
        }

        if (
            !empty($existing['Order']['transactionID']) &&
            $existing['Order']['payment_method'] === OrderPaymentMethod::STRIPE &&
            $existing['Order']['payment_status'] === OrderPaymentStatus::AUTHORIZED &&
            in_array($api_order['financial_status'], ['paid', 'partially_paid'], true)
        ) {
            $orderinfo = array_merge($orderinfo, $this->_captureShopifyDirectCharge($userId, $api_order['id'], $existing));
        }

        if (isset($existing['Order']['id'])) {
            $this->Order->clear();
            $orderinfo['id'] = $existing['Order']['id'];
        } else {
            $this->Order->create();
        }
        $this->Order->save($orderinfo);
        $inc_oid = $this->Order->id;

        $order_sales_commission = 0;
        foreach ($api_order['line_items'] as $lineItem) {
            $quantity = $lineItem['quantity'];
            $totalPrice = $this->Currency->formatAsDecimal($lineItem['price'] * $quantity, $api_order['currency']);
            $totalTax = $this->Currency->formatAsDecimal(array_sum(array_column($lineItem['tax_lines'], 'price')), $api_order['currency']);

            $product = $productIDMap[$lineItem['variant_id']];
            $productId = $product['Product']['id'];

            $orderProduct = array(
                'order_id' => $inc_oid,
                'product_id' => $productId,
                'quantity' => $quantity,
                'total_tax' => $totalTax,
                'total_price' => $totalPrice,
                'totalPriceConversion' => $totalPrice,
                'total_discount' => $lineItem['total_discount'],
            );

            $poid = $this->OrderProduct->find('first', array(
                'recursive' => -1,
                'conditions' => array('order_id' => $inc_oid, 'product_id' => $productId, 'quantity' => $quantity),
                'fields' => array('id', 'total_sales_commission', 'warehouse_id'),
            ));

            $salesCommission = 0;

            if (!empty($poid['OrderProduct']['id'])) {
                $this->OrderProduct->clear();
                $orderProduct['id'] = $poid['OrderProduct']['id'];
                $salesCommission = $poid['OrderProduct']['total_sales_commission'];
            } else {
                $this->OrderProduct->create();

                if (!empty($existing['Order']['store_associate_id'])) {
                    $salesCommission = $product['Product']['sales_commission'] * $quantity;
                    $orderProduct['total_sales_commission'] = $salesCommission;
                }
            }

            $orderProduct['warehouse_id'] = $poid['OrderProduct']['warehouse_id'] ?? $this->Product->getBestWarehouseId($orderProduct['product_id']);
            $this->OrderProduct->save($orderProduct);
            $order_sales_commission += $salesCommission;
        }
        $this->Order->save(array('id' => $inc_oid, 'total_sales_commission' => $order_sales_commission));

        if (!empty($api_order['billing_address'])) {
            $this->OrderAddress->saveBillingFromShopifyWebhook($inc_oid, $api_order['billing_address']);
        }
        $this->_syncOrderFulfillments($userId, $api_order);

        if (empty($existing['Order']['id'])) {
            $this->OrderLogic->updateChargeForNewOrder((int)$inc_oid);
        }

        if ($ordtype == 'update') {
            $this->Notification->createForSellDirect($userId, $userId, $inc_oid);
        }
        return true;
    }

    /**
     * @param int $userId
     * @return array
     * @throws UnauthorizedException
     * @throws ShopifyApiException
     */
    private function _getUserWithShopAppId($userId): array
    {
        $user = $this->User->findById($userId, ['id', 'api_key', 'secret_key', 'shop_url', 'shop_app_id'], null, -1);
        if (empty($user['User']['id'])) {
            throw new UnauthorizedException('User not found for user_id=' . json_encode($userId));
        }

        if (!$user['User']['shop_app_id']) {
            $shopAppId = $this->Shopify->getAppId((string)$user['User']['secret_key'], (string)$user['User']['shop_url']);
            if ($shopAppId) {
                $user['User']['shop_app_id'] = $shopAppId;

                $this->User->updateAll(['User.shop_app_id' => $this->User->value($shopAppId)], ['User.id' => $userId]);
            }
        }

        return $user;
    }

    /**
     * @param int $brandId User id for the owner of the Shopify store
     * @param array $api_order Shopify API order
     * @param array $existing Order with associations matching $api_order
     * @return bool Success
     */
    private function _syncRetailOrder(int $brandId, array $api_order, array $existing)
    {
        if (!empty($existing['Order']['is_commission_retailer'])) {
            return $this->_syncCommissionOrder($brandId, $api_order, $existing);
        } elseif (!empty($existing['DealerOrder']['id'])) {
            return $this->_syncDealerOrder($brandId, $api_order, $existing);
        }

        CakeLog::warning('Skipping unexpected retail order ' . json_encode($existing));

        return false;
    }

    private function _syncCommissionOrder(int $brandId, array $api_order, array $existing): bool
    {
        $existing = $this->_syncAnyMissingApiOrderKeys($this->Order, $existing, $api_order);

        if (
            !empty($existing['Order']['transactionID']) &&
            $existing['Order']['payment_method'] === OrderPaymentMethod::STRIPE &&
            $existing['Order']['payment_status'] === OrderPaymentStatus::AUTHORIZED &&
            in_array($api_order['financial_status'], ['paid', 'partially_paid'], true)
        ) {
            $orderinfo = $this->_captureShopifyDirectCharge($brandId, $api_order['id'], $existing);

            $this->Order->save(['id' => $existing['Order']['id']] + $orderinfo);
        }

        $this->_syncOrderFulfillments($brandId, $api_order);

        return true;
    }

    private function _syncDealerOrder(int $brandId, array $api_order, array $existing): bool
    {
        $this->_syncAnyMissingApiOrderKeys($this->DealerOrder, $existing, $api_order);

        $this->_syncOrderFulfillments($brandId, $api_order);

        return true;
    }

    /**
     * Save any missing ecommerce API order reference fields to the specified model.
     *
     * Keeps legacy orders up-to-date as we add more API orders reference fields.
     * Note that the DealerOrder and Order models have identical api reference columns.
     *
     * @param DealerOrder|Order $Model
     * @param array $existing
     * @param array $api_order
     * @return array $existing merged with synchronized fields
     * @throws Exception
     * @see OrderPlacerComponent::createEcommerceDealerOrder
     * @see ShopifyController::_directorder
     */
    private function _syncAnyMissingApiOrderKeys($Model, array $existing, array $api_order)
    {
        if (empty($existing[$Model->alias][$Model->primaryKey])) {
            return $existing;
        }

        $apiFields = [
            'source_id' => $api_order['id'],
            'orderNO' => $api_order['order_number'],
            'source_order_name' => $api_order['name'],
        ];
        $nonEmptyFields = array_filter($existing[$Model->alias]);
        $missingFields = array_diff_key($apiFields, $nonEmptyFields);

        if ($missingFields) {
            $success = $Model->save([$Model->primaryKey => $existing[$Model->alias][$Model->primaryKey]] + $missingFields);
            if ($success) {
                $existing[$Model->alias] = array_merge($existing[$Model->alias], $missingFields);
                CakeLog::debug("Synced legacy {$Model->alias} " . json_encode($existing[$Model->alias]));
            } else {
                CakeLog::warning("Failed to sync legacy {$Model->alias} " . json_encode(['errors' => $Model->validationErrors, 'data' => $Model->data]));
            }
        }

        return $existing;
    }

    private function _syncOrderFulfillments(int $brandId, array $api_order): bool
    {
        foreach ($api_order['fulfillments'] ?? [] as $api_fulfillment) {
            try {
                $this->syncFulfillment($brandId, $api_fulfillment);
            } catch (Exception $e) {
                CakeLog::error($e);

                continue;
            }
        }

        return true;
    }

    private function _findCourierForFulfillment(array $fulfillment): ?array
    {
        $original_slug = generate_slug($fulfillment['tracking_company']);
        $tracking_url = $fulfillment['tracking_url'];

        $slugLookupMethods = [
            'raw' => function() use ($original_slug) {
                return $original_slug;
            },
            'lookup_table' => function() use ($original_slug) {
                $lookupTable = [
                    'fedexground' => 'fedex',
                    'customer' => 'tnt-reference',
                ];

                return $lookupTable[$original_slug] ?? null;
            },
            'contains' => function() use ($original_slug) {
                $contains = ['fedex'];
                foreach ($contains as $needle) {
                    if (strpos($original_slug, $needle) !== false) {
                        return $needle;
                    }
                }

                return null;
            },
            'tracking_url_domain' => function() use ($tracking_url) {
                $host = parse_url($tracking_url, PHP_URL_HOST);

                // Works for patterns like 'www.example.com', 'example.com', and 'www.example.co.uk'
                // but not 'example.co.uk' or 'multiple.subdomains.example.com'
                $host_parts = explode('.', $host, 3);
                $host_name = $host_parts[count($host_parts) - 2] ?? null;

                return $host_name ? generate_slug($host_name) : null;
            },
        ];

        foreach ($slugLookupMethods as $lookup_method => $callback) {
            $matched_slug = call_user_func($callback);
            if (!$matched_slug) {
                continue;
            }

            $courier = $this->Courier->findBySlug($matched_slug, ['id', 'slug'], null, -1);
            if (!empty($courier['Courier']['id'])) {
                CakeLog::info(json_encode(compact('original_slug', 'tracking_url', 'matched_slug', 'lookup_method')));

                return (array)$courier;
            }
        }

        return null;
    }

    private function _captureShopifyDirectCharge($userId, $api_order_id, $existing): array
    {
        $user = $this->User->findById($userId, [
            'id',
            'api_key',
            'secret_key',
            'shop_url',
            'brand_revenue_model',
            'brand_direct_default_amount',
            'brand_revenue_maximum',
            'revenue_model',
            'retailer_default_amount',
            'retailer_revenue_maximum',
        ], null, -1);
        $transactions = $this->Shopify->getAllOrderTransactions($api_order_id, $user['User']['api_key'], $user['User']['secret_key'], $user['User']['shop_url']);
        $transactions = Hash::combine($transactions, '{n}.id', '{n}');
        foreach ($transactions as $transaction) {
            if (
                $transaction['gateway'] === 'stripe' &&
                $transaction['kind'] === 'capture' &&
                $transaction['status'] === 'success' &&
                $transactions[$transaction['parent_id']]['authorization'] === $existing['Order']['transactionID']
            ) {
                $stripe_account = $this->StripeUser->getOrderAccountId($existing['Order']['id'], $existing['Order']);
                $charge = $this->Stripe->retrieveCharge($stripe_account, $existing['Order']['transactionID']);

                $currencyConversion = 1;
                $stripeCurrency = strtoupper($charge->currency);
                if ($transaction['currency'] !== $stripeCurrency) {
                    $currencyConversion = $this->controller->currencyConversion(1, $transaction['currency'], $stripeCurrency);
                }

                $amount = $transaction['amount'] * $currencyConversion;

                if ($existing['Order']['store_associate_id']) {
                    $application_fee = 0;
                } elseif ($existing['Order']['is_commission_retailer']) {
                    $application_fee = $this->OrderLogic->CalculateFees($amount, 'retailer', $user['User']['revenue_model'], $user['User']['retailer_default_amount'], $user['User']['retailer_revenue_maximum']);
                } else {
                    $application_fee = $this->OrderLogic->CalculateFees($amount, 'brand', $user['User']['brand_revenue_model'], $user['User']['brand_direct_default_amount'], $user['User']['brand_revenue_maximum']);
                }

                $charge = $this->Stripe->captureCharge($stripe_account, $charge, [
                    'amount' => round($amount * 100),
                    'application_fee_amount' => round($application_fee * 100),
                ]);

                return [
                    'payment_status' => OrderPaymentStatus::PAID,
                    'payment_captured_at' => date('Y-m-d H:i:s'),
                    'balance_transaction_id' => $charge->balance_transaction,
                    'stripe_fees' => !empty($charge->balance_transaction)
                        ? $this->Stripe->retrieveStripeFees($stripe_account, $charge)
                        : $this->Stripe->estimateStripeFees($amount),
                ];
            }
        }
        return [];
    }

    private function _getOrderShippingFromShopifyWebhook(array $shipping_address): array
    {
        $countryCode = $shipping_address['country_code'];
        $stateCode = get_shopify_state_code((string)$countryCode, (string)($shipping_address['province_code']));

        $orderShipping = [
            'billing_firstname' => $shipping_address['first_name'],
            'billing_lastname' => $shipping_address['last_name'],
            'shipping_company_name' => $shipping_address['company'],
            'shipping_address1' => $shipping_address['address1'],
            'shipping_address2' => $shipping_address['address2'],
            'shipping_city' => $shipping_address['city'],
            'shipping_state' => $shipping_address['province'],
            'shipping_country' => $shipping_address['country'],
            'shipping_zipcode' => $shipping_address['zip'],
            'shipping_telephone' => $shipping_address['phone'],
            'shipping_statecode' => $stateCode,
            'shipping_countrycode' => $countryCode,
        ];
        if (!empty($shipping_address['latitude'])) {
            $orderShipping['latitude'] = (string)$shipping_address['latitude'];
        }
        if (!empty($shipping_address['longitude'])) {
            $orderShipping['longitude'] = (string)$shipping_address['longitude'];
        }

        return $orderShipping;
    }

    /**
     * @param array $api_order
     * @param string $pass
     * @param string $domain
     * @return array $api_order
     */
    private function _applyOrderLineItemTotalDiscounts(array $api_order, string $pass, string $domain): array
    {
        if ($api_order['discount_codes']) {
            try {
                $lineItemTotalDiscounts = $this->Shopify->getOrderLineItemTotalDiscounts($api_order['id'], $pass, $domain);
            } catch (ShopifyApiException $e) {
                CakeLog::warning($e);
                $lineItemTotalDiscounts = [];
            }

            if (!$lineItemTotalDiscounts) {
                $lineItemTotalDiscounts = $this->_getLegacyOrderLineItemTotalDiscounts($api_order, $pass, $domain);
            }

            $api_order['line_items'] = $this->_assignTotalDiscountsToLineItems($api_order['line_items'], $api_order['currency'], $lineItemTotalDiscounts);
        }

        return $api_order;
    }

    private function _getLegacyOrderLineItemTotalDiscounts(array $api_order, string $pass, string $domain): array
    {
        $tags = $api_order['tags'] ? array_filter(array_map('trim', explode(',', $api_order['tags']))) : [];
        $lineItemTotalDiscounts = $this->_buildLineItemTotalDiscountsFromTags($tags, $api_order['line_items'], $api_order['currency']);

        if ($lineItemTotalDiscounts) {
            try {
                $this->Shopify->createOrderLineItemTotalDiscounts($api_order['id'], $pass, $domain, $lineItemTotalDiscounts);
            } catch (ShopifyApiException $e) {
                CakeLog::warning($e);
            }
        }

        return $lineItemTotalDiscounts;
    }

    private function _buildLineItemTotalDiscountsFromTags(array $tags, array $lineItems, string $currency): array
    {
        $lineItemTotalDiscounts = [];

        $product_discounts = array_values(array_filter($tags, 'is_numeric'));
        foreach ($lineItems as $index => $lineItem) {
            $lineItemTotalDiscounts[] = [
                'variant_id' => $lineItem['variant_id'],
                'price' => $lineItem['price'],
                'quantity' => $lineItem['quantity'],
                'total_discount' => $this->Currency->formatAsDecimal(($product_discounts[$index] ?? 0) * $lineItem['quantity'], $currency),
            ];
        }

        return $lineItemTotalDiscounts;
    }

    private function _assignTotalDiscountsToLineItems(array $lineItems, string $currency, array $lineItemTotalDiscounts): array
    {
        $totalDiscountsMap = [];
        foreach ($lineItemTotalDiscounts as $index => $discountItem) {
            // Add an index key field because the decimal price value would be coerced to an integer
            $discountItem = compact('index') + $discountItem;

            $totalDiscountsMap[$discountItem['variant_id']][$discountItem['quantity']][$discountItem['index']] = $discountItem;
        }

        return array_map(function($lineItem) use (&$totalDiscountsMap, $currency) {
            $variantId = $lineItem['variant_id'];
            $quantity = $lineItem['quantity'];

            $allCandidates = $totalDiscountsMap[$variantId][$quantity] ?? [];
            $preciseCandidates = array_filter($allCandidates, function($candidate) use ($lineItem) {
                return ($candidate['price'] === $lineItem['price']);
            });

            $match = current($preciseCandidates);
            if (!$match) {
                $log = [
                    'order.line_item' => $lineItem,
                    'candidates' => array_values($preciseCandidates),
                ];
                CakeLog::notice('Unable to find a precise match for ' . json_encode($log));

                $match = current($allCandidates);
            }

            if ($match) {
                $lineItem['total_discount'] = $match['total_discount'];

                unset($totalDiscountsMap[$variantId][$quantity][$match['index']]);
            } else {
                $lineItem['total_discount'] = $this->Currency->formatAsDecimal(0, $currency);

                $log = [
                    'order.line_item' => $lineItem,
                    'candidates' => array_values($allCandidates),
                ];
                CakeLog::warning('Unable to find any match for ' . json_encode($log));
            }

            return $lineItem;
        }, $lineItems);
    }

    public function syncProduct(int $userId, array $api_product): bool
    {
        $simple = (Hash::extract($api_product['options'], '{n}.values.{n}') === [static::DEFAULT_TITLE]);
        $api_variant_ids = array_column($api_product['variants'], 'id', 'id');
        $api_image_url_by_id = array_column($api_product['images'], 'src', 'id');
        $tags = array_filter(array_map('trim', explode(',', $api_product['tags'])));

        $productTitleId = $this->syncProductTitle($userId, $api_product);

        $existingProducts = $this->Product->findAllExistingForWebhookByVariantId($userId, $productTitleId, [
            'id',
            'productID',
            'deleted',
        ]);
        $archivedProducts = array_filter($existingProducts, fn($existing) => $existing['Product']['deleted']);
        if ($archivedProducts) {
            CakeLog::warning('Syncing archived products ' . json_encode(array_column($archivedProducts, 'Product')));
        }
        $productIdByVariantId = array_map(fn($existing) => $existing['Product']['id'], $existingProducts);
        $productDeleteConditions = [
            'Product.id' => array_values(array_diff_key($productIdByVariantId, $api_variant_ids)),
        ];

        $imageIdByUrlAndProductId = $this->ProductImage->find('list', [
            'recursive' => -1,
            'conditions' => [
                'ProductImage.product_id' => array_values($productIdByVariantId),
            ],
            'fields' => ['product_id', 'id', 'image_url'],
        ]);
        $imageDeleteConditions = [
            'ProductImage.id' => array_values(Hash::flatten(array_diff_key($imageIdByUrlAndProductId, array_flip($api_image_url_by_id)))),
        ];

        $variantOptionIdByPosition = (array)$this->VariantOption->find('list', [
            'conditions' => ['VariantOption.product_title_id' => $productTitleId],
            'fields' => ['position', 'id'],
            'order' => false,
        ]);
        $variantValueIdByProductIdAndVariantOptionId = $this->ProductVariantOption->find('list', [
            'conditions' => ['ProductVariantOption.product_id' => array_values($productIdByVariantId)],
            'fields' => ['variant_option_id', 'id', 'product_id'],
            'order' => false,
        ]);

        if (!$this->Tag->addTagsToSet($userId, $tags)) {
            CakeLog::warning(json_encode(['message' => 'Failed to add new tags', 'errors' => $this->Tag->validationErrors, 'data' => $this->Tag->data]));
        }
        $tagIds = array_values($this->Tag->listIdsByName($userId, $tags));

        $defaultCategoryIds = $this->UserCategories->getUserCategories($userId);
        $defaultRetailerIds = $this->UserCategories->getRetailers($defaultCategoryIds, $userId);

        $saveMany = array_map(function($variants) use (
            $userId,
            $api_product,
            $api_image_url_by_id,
            $simple,
            $productTitleId,
            $productIdByVariantId,
            $variantOptionIdByPosition,
            $variantValueIdByProductIdAndVariantOptionId,
            $imageIdByUrlAndProductId,
            $tagIds,
            $defaultCategoryIds,
            $defaultRetailerIds
        ) {
            $productId = $productIdByVariantId[$variants['id']] ?? null;
            $created = !$productId;

            $variantValueIdByOptionId = $variantValueIdByProductIdAndVariantOptionId[$productId] ?? [];
            $productVariantOptions = array_map(function(int $position) use ($variantValueIdByOptionId, $variantOptionIdByPosition, $variants) {
                $variantOptionId = $variantOptionIdByPosition[$position] ?? null;

                return [
                    'id' => $variantValueIdByOptionId[$variantOptionId] ?? null,
                    'variant_option_id' => $variantOptionId,
                    'value' => $variants["option{$position}"],
                ];
            }, range(1, 3));
            $productVariantOptions = array_filter($productVariantOptions, fn($option) => ($option['variant_option_id'] && $option['value']));

            // 2024-07 webhooks replaced inventory_management with inventory_item.tracked.
            // Handle by assuming inventory_item.tracked is true rather making an additional API call
            // because, in practise, all of our production Shopify users track their inventory.
            $tracked = array_key_exists('inventory_management', $variants)
                ? ($variants['inventory_management'] !== null)
                : true;

            $productinfo = [
                'id' => $productId,
                'user_id' => $userId,
                'product_title_id' => $productTitleId,
                'source_product_id' => $api_product['id'],
                'productID' => $variants['id'],
                'inventory_item_id' => $variants['inventory_item_id'],
                'product_type' => $api_product['product_type'],
                'product_title' => ($simple)
                    ? $api_product['title']
                    : $api_product['title'] . ' - ' . $variants['title'],
                'product_sku' => !empty($variants['sku']) ? $variants['sku'] : null,
                'product_upc' => $variants['barcode'],
                'product_price' => !empty($variants['price']) ? $variants['price'] : 0,
                'compare_at_price' => $variants['compare_at_price'],
                'brand_inventory' => ($tracked) ? $variants['inventory_quantity'] : null,
                'enable_oversell' => (!$tracked || $variants['inventory_policy'] === 'continue'),
                'product_image' => $api_image_url_by_id[$variants['image_id']] ?? $api_product['image']['src'] ?? null,
                'created_at' => $variants['created_at'],
                'updated_at' => $variants['updated_at'],
                'deleted' => false,
            ];

            // 2024-07 webhooks removed weight and weight_unit with no known replacements in webhooks.
            // Handle by leaving fields unset to keep any existing values or initialize with schema defaults.
            if (isset($variants['weight'])) {
                $productinfo['weight'] = format_number($variants['weight']);
            }
            if (isset($variants['weight_unit'])) {
                $productinfo['weight_unit'] = $variants['weight_unit'];
            }

            $productinfo = array_merge($productinfo, [
                'ProductImage' => array_map(fn($imageUrl) => [
                    'id' => $imageIdByUrlAndProductId[$imageUrl][$productId] ?? null,
                    'image_url' => $imageUrl,
                ], array_values($api_image_url_by_id)),
                'ProductVariantOption' => $productVariantOptions,
                // See: https://book.cakephp.org/2.0/en/models/saving-your-data.html#saving-related-model-data-habtm
                'Tag' => ['Tag' => $tagIds],
            ]);
            if ($created) {
                $productinfo = array_merge($productinfo, [
                    'no_of_retailers' => count($defaultRetailerIds),
                    'Category' => ['Category' => $defaultCategoryIds],
                    'Retailer' => ['Retailer' => $defaultRetailerIds],
                ]);
            }

            return $productinfo;
        }, $api_product['variants']);

        $this->Product->addAssociations([
            'hasMany' => [
                'ProductImage',
                'ProductVariantOption',
            ],
            'belongsToMany' => [
                'Category' => ['with' => 'ProductCategory', 'associationForeignKey' => 'cat_id', 'unique' => 'keepExisting'],
                'Retailer' => ['className' => 'User', 'with' => 'ProductRetailer', 'unique' => 'keepExisting'],
                'Tag' => ['with' => 'ProductTag', 'unique' => 'keepExisting'],
            ],
        ]);

        $success = (bool)$this->Product->saveMany($saveMany, ['deep' => true]);

        $this->Product->unbindModel([
            'hasMany' => [
                'ProductImage',
                'ProductVariantOption',
            ],
            'hasAndBelongsToMany' => [
                'Category',
                'Retailer',
                'Tag',
            ],
        ], false);

        if (!$success) {
            throw new InternalErrorException(json_encode(['message' => 'Failed to save variants', 'errors' => $this->Product->validationErrors, 'data' => $this->Product->data]));
        }

        if (!$this->Product->removeAll($productDeleteConditions)) {
            CakeLog::warning(json_encode(['message' => 'Failed to delete orphaned variants', 'conditions' => $productDeleteConditions]));
        }

        if (!$this->ProductImage->deleteAllJoinless($imageDeleteConditions, false)) {
            CakeLog::warning(json_encode(['message' => 'Failed to delete orphaned images', 'conditions' => $imageDeleteConditions]));
        }

        $productVariantOptionDeleteConditions = [
            'ProductVariantOption.id' => array_values(array_diff(
                Hash::flatten($variantValueIdByProductIdAndVariantOptionId),
                array_filter(Hash::extract($saveMany, '{n}.ProductVariantOption.{n}.id'))
            )),
        ];
        if (!$this->ProductVariantOption->deleteAllJoinless($productVariantOptionDeleteConditions, false)) {
            CakeLog::warning(json_encode(['message' => 'Failed to delete orphaned product variant options', 'conditions' => $productVariantOptionDeleteConditions]));
        }

        if (!$this->Tag->deleteAllOrphansWithUserId($userId)) {
            CakeLog::warning(json_encode(['message' => 'Failed to delete orphaned tags', 'conditions' => ['Tag.user_id' => $userId]]));
        }

        return true;
    }

    public function syncProductTitle(int $userId, array $api_product): int
    {
        $sourceId = (string)$api_product['id'];
        $sourceType = ProductSourceType::SHOPIFY;
        $handle = (string)$api_product['handle'] ?: null;

        $existingTitle = $this->ProductTitle->findExistingForWebhook($userId, $sourceId, $sourceType, $handle, [
            'id',
            'source_id',
            'deleted',
        ]);
        if ($existingTitle['ProductTitle']['deleted'] ?? false) {
            CakeLog::warning(json_encode(['message' => 'Syncing an archived product title'] + $existingTitle));
        }
        $productTitleId = isset($existingTitle['ProductTitle']['id']) ? (int)$existingTitle['ProductTitle']['id'] : null;

        if ($handle) {
            $handle = $this->ProductTitle->buildUniqueHandleFromWebhook($handle, $userId, $productTitleId, [
                ['prefix' => $sourceType],
                ['suffix' => $sourceId],
            ]);
        }

        $simple = (Hash::extract($api_product['options'], '{n}.values.{n}') === [static::DEFAULT_TITLE]);
        $api_options = (!$simple) ? $api_product['options'] : [];
        $api_option_ids = array_column($api_options, 'id', 'id');

        $variantOptionIdBySourceId = [];
        if ($productTitleId) {
            $variantOptionIdBySourceId = (array)$this->VariantOption->find('list', [
                'conditions' => ['VariantOption.product_title_id' => $productTitleId],
                'fields' => ['source_id', 'id'],
                'order' => false,
            ]);
        }
        $variantOptionDeleteConditions = [
            'VariantOption.id' => array_values(array_diff_key($variantOptionIdBySourceId, $api_option_ids)),
        ];

        $variantValueIdByOptionIdAndLcValue = (array)$this->VariantOption->VariantOptionValue->find('list', [
            'conditions' => ['VariantOptionValue.variant_option_id' => array_values($variantOptionIdBySourceId)],
            'fields' => ['lower_case', 'id', 'variant_option_id'],
        ]);

        $saveAssociated = [
            'ProductTitle' => [
                'id' => $productTitleId,
                'user_id' => $userId,
                'source_id' => $sourceId,
                'source_type' => $sourceType,
                'handle' => $handle,
                'title' => $api_product['title'],
                'description' => $api_product['body_html'],
                'deleted' => false,
            ],
            'VariantOption' => array_map(function($option) use ($variantOptionIdBySourceId, $variantValueIdByOptionIdAndLcValue) {
                $id = $variantOptionIdBySourceId[$option['id']] ?? null;
                $values = $option['values'];

                // Remove any case-insensitive duplicates
                $values = array_intersect_key($values, array_unique(array_map('mb_strtolower', $values)));

                return [
                    'id' => $id,
                    'source_id' => $option['id'],
                    'name' => $option['name'],
                    'position' => $option['position'],
                    'VariantOptionValue' => array_map(fn($value) => [
                        'id' => $variantValueIdByOptionIdAndLcValue[$id][mb_strtolower($value)] ?? null,
                        'value' => $value,
                    ], $values),
                ];
            }, $api_options),
        ];
        if (!$this->ProductTitle->saveAssociated($saveAssociated, ['deep' => true])) {
            throw new InternalErrorException(json_encode(['message' => 'Failed to save product title', 'errors' => $this->ProductTitle->validationErrors, 'data' => $this->ProductTitle->data]));
        }
        $productTitleId = (int)$this->ProductTitle->id;

        if (!$this->VariantOption->deleteAllJoinless($variantOptionDeleteConditions)) {
            CakeLog::warning(json_encode(['message' => 'Failed to delete orphaned variant options', 'conditions' => $variantOptionDeleteConditions]));
        }

        $variantOptionValueDeleteConditions = [
            'VariantOptionValue.id' => array_values(array_diff(
                Hash::flatten($variantValueIdByOptionIdAndLcValue),
                array_filter(Hash::extract($saveAssociated['VariantOption'], '{n}.VariantOptionValue.{n}.id'))
            )),
        ];
        if (!$this->VariantOption->VariantOptionValue->deleteAllJoinless($variantOptionValueDeleteConditions, false)) {
            CakeLog::warning(json_encode(['message' => 'Failed to delete orphaned variant option values', 'conditions' => $variantOptionValueDeleteConditions]));
        }

        if (!$this->Product->assignProductTitleToLegacyProducts($userId, $sourceId, $productTitleId)) {
            CakeLog::warning(json_encode(['message' => 'Failed to assign title to legacy products', 'ProductTitle' => ['id' => $productTitleId, 'user_id' => $userId, 'source_id' => $sourceId]]));
        }

        return $productTitleId;
    }

    public function syncInventory($userId, $inventoryItemId, $locationId, $inventoryLevel): bool
    {
        $warehouseId = $this->Warehouse->fieldByConditions('id', [
            'user_id' => $userId,
            'source_id' => $locationId,
        ], [
            'includeDeleted' => true,
        ]);
        $productId = $this->Product->fieldByConditions('id', [
            'user_id' => $userId,
            'inventory_item_id' => $inventoryItemId,
        ]);

        $data = [];
        if (array_key_exists('available', $inventoryLevel)) {
            $data = [
                'quantity' => $inventoryLevel['available'],
                'updated_at' => format_datetime($inventoryLevel['updated_at']),
            ];
        }

        return $this->WarehouseProduct->syncEcommerceInventory($warehouseId, $productId, $data);
    }

    public function syncFulfillment(int $userId, array $api_fulfillment): bool
    {
        $userLog = $this->User->findById($userId, ['User.id', 'User.email_address', 'User.company_name'], null, -1);

        $orderSourceId = (string)$api_fulfillment['order_id'];
        $fulfillmentSourceId = (string)$api_fulfillment['id'];

        $order = (
            $this->DealerOrder->findForEcommerceFulfillmentSync($userId, $orderSourceId, $fulfillmentSourceId) ?:
            $this->Order->findForEcommerceFulfillmentSync($userId, $orderSourceId, $fulfillmentSourceId)
        );
        if (empty($order['Order']['orderID'])) {
            CakeLog::debug('Order not found where ' . json_encode($api_fulfillment + $userLog));

            return false;
        }

        $fulfillmentId = $order['Fulfillment']['id'];

        if ($fulfillmentId) {
            CakeLog::debug('Skipping already applied fulfillment ' . json_encode(['Fulfillment' => $order['Fulfillment']] + $userLog));

            return true;
        }

        $isDealerFulfillment = !empty($order['DealerOrder']['id']);

        if ($isDealerFulfillment) {
            $orderIdField = 'dealer_order_id';
            $orderProductIdField = 'dealer_order_product_id';
            $orderId = $order['DealerOrder']['id'];
            $orderProducts = $order['DealerOrderProduct'];
        } else {
            $orderIdField = 'order_id';
            $orderProductIdField = 'order_product_id';
            $orderId = $order['Order']['id'];
            $orderProducts = $order['OrderProduct'];
        }

        $warehouseId = $this->Warehouse->getIdForEcommerceSync($userId, $api_fulfillment['location_id']);
        $courier = $this->_findCourierForFulfillment($api_fulfillment);
        $createdAt = format_datetime_from(DATE_W3C, $api_fulfillment['created_at']);

        $orderProducts = $this->_assignFulfillmentLineItemSourceIds($api_fulfillment, $orderProducts, $warehouseId);
        $orderProducts = array_map(function($item) {
            return array_merge($item, [
                'FulfillmentProduct' => Hash::combine($item['FulfillmentProduct'], '{n}.fulfillment_id', '{n}'),
            ]);
        }, $orderProducts);
        $orderProducts = Hash::combine($orderProducts, '{n}.source_id', '{n}');

        $saveData = [
            'Fulfillment' => [
                'id' => $fulfillmentId,
                $orderIdField => $orderId,
                'warehouse_id' => $warehouseId,
                'source_id' => $fulfillmentSourceId,
                'name' => (
                    $order['Fulfillment']['name'] ??
                    $this->Fulfillment->findNextName($orderId, $orderIdField)
                ),
                'courier_id' => $courier['Courier']['id'],
                'tracking_number' => $api_fulfillment['tracking_number'],
                'tracking_url' => $api_fulfillment['tracking_url'],
                'created_at' => $createdAt,
            ],
            'FulfillmentProduct' => array_map(function($api_line_item) use ($orderProductIdField, $orderProducts, $fulfillmentId, $createdAt) {
                $orderProduct = $orderProducts[$api_line_item['id']] ?? [];

                return [
                    'id' => $orderProduct['FulfillmentProduct'][$fulfillmentId]['id'] ?? null,
                    $orderProductIdField => $orderProduct['id'],
                    'quantity' => $api_line_item['quantity'],
                    'created_at' => $createdAt,
                ];
            }, $api_fulfillment['line_items']),
        ];

        if (!$this->Fulfillment->saveFromEcommerceSync($saveData)) {
            throw new InternalErrorException(json_encode(['errors' => $this->Fulfillment->validationErrors, 'data' => $this->Fulfillment->data]));
        }
        $fulfillmentId = $this->Fulfillment->id;

        if ($isDealerFulfillment) {
            $this->FulfillmentLogic->afterDealerFulfillment($orderId, $fulfillmentId);
        } else {
            $this->FulfillmentLogic->afterConsumerFulfillment($orderId, $fulfillmentId);
        }

        return true;
    }

    private function _assignFulfillmentLineItemSourceIds(array $api_fulfillment, array $orderProducts, int $preferredWarehouseId): array
    {
        $orderProducts = Hash::combine(array_values($orderProducts), '{n}.id', '{n}');
        $orderProducts = array_map(function($item) {
            $item['source_id'] = $item['source_id'] ?? null;

            return $item;
        }, $orderProducts);

        $fulfillmentProductMap = Hash::combine(array_values($orderProducts), '{n}.warehouse_id', '{n}', '{n}.Product.productID');

        foreach ($api_fulfillment['line_items'] as $api_line_item) {
            $allCandidatesByWarehouseId = $fulfillmentProductMap[$api_line_item['variant_id']] ?? [];

            $lowPriorityCandidatesByWarehouseId = array_filter($allCandidatesByWarehouseId, function($item) use ($orderProducts, $api_line_item) {
                return (
                    !$orderProducts[$item['id']]['source_id'] &&
                    $api_line_item['quantity'] <= $item['unfulfilled_quantity']
                );
            });

            // Shopify retroactively updates the fulfillment_status and fulfilled_quantity of old fulfillments when new
            // ones are added to an order so matching them only works when syncing the latest fulfillment
            $highPriorityCandidatesByWarehouseId = array_filter($lowPriorityCandidatesByWarehouseId, function($item) use ($api_line_item) {
                $fulfillment_quantity = (int)$api_line_item['quantity'];

                // Shopify does not deduct refunded items when determining fulfillment_status
                $unfulfilled_quantity = ($item['quantity'] - $item['fulfilled_quantity']);
                $expected_fulfillment_status = ($fulfillment_quantity === $unfulfilled_quantity)
                    ? 'fulfilled'
                    : 'partial';
                if ($api_line_item['fulfillment_status'] !== $expected_fulfillment_status) {
                    return false;
                }

                $expected_fulfillable_quantity = ($item['unfulfilled_quantity'] - $fulfillment_quantity);
                if ((int)$api_line_item['fulfillable_quantity'] !== $expected_fulfillable_quantity) {
                    return false;
                }

                return true;
            });

            $matchedItem = (
                // Prefer the warehouse matching the fulfillment
                $highPriorityCandidatesByWarehouseId[$preferredWarehouseId] ??
                // Followed by the legacy case where warehouse_id is NULL
                $highPriorityCandidatesByWarehouseId[0] ??
                // Finally, pick from whatever remains
                current($highPriorityCandidatesByWarehouseId)
            );

            if (empty($matchedItem['id'])) {
                $log = [
                    'fulfillment.line_item' => $api_line_item,
                    'candidates' => array_values($lowPriorityCandidatesByWarehouseId),
                ];
                CakeLog::notice('Unable to find a precise match for ' . json_encode($log));

                $matchedItem = (
                    $lowPriorityCandidatesByWarehouseId[$preferredWarehouseId] ??
                    $lowPriorityCandidatesByWarehouseId[0] ??
                    current($lowPriorityCandidatesByWarehouseId)
                );
            }

            if (empty($matchedItem['id'])) {
                $log = [
                    'fulfillment.line_item' => $api_line_item,
                    'candidates' => array_values($allCandidatesByWarehouseId),
                ];
                CakeLog::warning('Unable to find any match for ' . json_encode($log));

                // Unmatched items will be rejected by the model when attempting to save.
                // Continuing allows the possibility for another method to assign source_id before the save.
                continue;
            }

            $orderProducts[$matchedItem['id']]['source_id'] = $api_line_item['id'];
        }

        return array_values($orderProducts);
    }

    public function syncRefund($userId, $api_refund)
    {
        CakeLog::debug(json_encode(['refund' => $api_refund, 'userId' => $userId]));

        $order = $this->Order->findForEcommerceRefundSync((int)$userId, (string)$api_refund['order_id']);
        if (empty($order['Order']['id'])) {
            CakeLog::debug('Consumer order not found where ' . json_encode(['user_id' => $userId, 'source_id' => $api_refund['order_id']]));

            return false;
        }

        if (
            !$order['Order']['is_commission_retailer'] &&
            OrderType::filterOrderType($order['Order']['order_type']) !== OrderType::SELL_DIRECT
        ) {
            CakeLog::warning('Skipping retail order: ' . json_encode($order));

            return false;
        }

        foreach ($order['OrderRefund'] as $orderRefund) {
            if ($orderRefund['source_id'] == $api_refund['id']) {
                CakeLog::warning('Refund already applied ' . json_encode(compact('orderRefund')));
                return false;
            }
        }
        $orderId = $order['Order']['id'];

        $orderProducts = $this->_assignRefundLineItemSourceIds($api_refund['refund_line_items'], $order['OrderProduct']);
        $orderProducts = Hash::combine($orderProducts, '{n}.source_id', '{n}');

        $request = [
            'orderID' => $orderId,
            'OrderRefund' => [
                'restocking_fees' => 0,
                'shipping_portion' => array_sum(array_map(
                    // Convert negative API value to positive
                    fn($adjustment) => -$adjustment['amount'],
                    array_filter($api_refund['order_adjustments'], fn($adjustment) => $adjustment['kind'] === 'shipping_refund')
                )),
                'tax_portion' => array_sum(array_column($api_refund['refund_line_items'], 'total_tax')),
                'amount' => $api_refund['transactions'][0]['amount'],
                'reason' => (string)$api_refund['note'],
                'source_id' => $api_refund['id'],
            ],
            'OrderRefundProduct' => array_map(fn($refundLineItem) => [
                'order_product_id' => $orderProducts[$refundLineItem['line_item']['id']]['id'],
                'quantity' => $refundLineItem['quantity'],
            ], $api_refund['refund_line_items']),
        ];
        $response = json_decode($this->OrderLogic->orderRefund($request), true);

        if ($response['status'] === 'ok') {
            if (!$this->OrderRefund->createFromRefundPopup($response['message']['request'])) {
                throw new InternalErrorException(json_encode(['errors' => $this->OrderRefund->validationErrors, 'data' => $this->OrderRefund->data]));
            }

            $this->FulfillmentLogic->afterConsumerRefund((int)$orderId, (int)$this->OrderRefund->id);

            return true;
        } else {
            throw new InternalErrorException('Refund failed with response ' . json_encode($response));
        }
    }

    private function _assignRefundLineItemSourceIds(array $refundLineItems, array $orderProducts): array
    {
        $orderProducts = array_map(
            fn($item) => array_merge($item, ['source_id' => ($item['source_id'] ?? null)]),
            Hash::combine(array_values($orderProducts), '{n}.id', '{n}')
        );

        $orderProductMap = Hash::combine(array_values($orderProducts), '{n}.quantity', '{n}', '{n}.Product.productID');
        $quantitiesByRefundedItemId = Hash::combine($refundLineItems, '{n}.line_item.id', '{n}.line_item.quantity');

        foreach ($refundLineItems as $refundLineItem) {
            $refundedItemId = $refundLineItem['line_item']['id'];
            $productID = $refundLineItem['line_item']['variant_id'];

            $allCandidatesByQuantity = array_filter($orderProductMap[$productID] ?? [], fn($item) => !$orderProducts[$item['id']]['source_id']);

            $quantity = (int)($quantitiesByRefundedItemId[$refundedItemId] ?? 0);

            $matchedItem = (array)(
                // Prefer an exact quantity match
                $allCandidatesByQuantity[$quantity] ??
                // Otherwise, pick from whatever remains
                (current($allCandidatesByQuantity) ?: [])
            );

            if (empty($matchedItem['id'])) {
                $log = [
                    'refund.refund_line_item' => $refundLineItem,
                    'candidates' => array_values($allCandidatesByQuantity),
                ];
                CakeLog::warning('Unable to find any match for ' . json_encode($log));

                // Unmatched items will be rejected by the model when attempting to save.
                // Continuing allows the possibility for another method to assign source_id before the save.
                continue;
            }

            $orderProducts[$matchedItem['id']]['source_id'] = $refundedItemId;
        }

        return array_values($orderProducts);
    }
}
