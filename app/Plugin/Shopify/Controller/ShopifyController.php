<?php

use ShipEarlyApp\Lib\Integrations\Klaviyo\KlaviyoFactory;
use ShipEarlyApp\Lib\Utility\UserSiteType;

App::uses('AppController', 'Controller');
App::uses('AppModelValidationsTrait', 'Model/Behavior/Trait');
App::uses('ShippingMethod', 'Utility');
App::uses('OrderPaymentMethod', 'Utility');
App::uses('OrderPaymentMethodSubtype', 'Utility');
App::uses('OrderPaymentStatus', 'Utility');
App::uses('OrderStatus', 'Utility');
App::uses('DiscountOptions', 'Utility/Discount');
App::uses('OrderType', 'Utility');
App::uses('PreorderType', 'Utility');
App::uses('ProductSellDirect', 'Utility');
App::uses('ProductStatus', 'Utility');
App::uses('StripePaymentType', 'Stripe.Enum');

/**
 * Class ShopifyController
 *
 * @property AccessTokenHandlerComponent $AccessTokenHandler
 * @property AddressValidatorComponent $AddressValidator
 * @property CurrencyComponent $Currency
 * @property OrderPlacerComponent $OrderPlacer
 * @property ShippingCalculatorComponent $ShippingCalculator
 * @property ShopifyAPIComponent $ShopifyAPI
 * @property ShopifyCartsComponent $ShopifyCarts
 * @property ShopifyComponent $Shopify
 * @property OrderLogicComponent $OrderLogic
 * @property NotificationLogicComponent $NotificationLogic
 * @property LightspeedComponent $Lightspeed
 * @property SquarePosComponent $SquarePos
 * @property AvataxComponent $Avatax
 * @property ShopifyPOSComponent $ShopifyPOS
 * @property StripeComponent $Stripe
 * @property QuickbookComponent $Quickbook
 * @property VendPOSComponent $VendPOS
 *
 * @property User $User
 * @property Preorder $Preorder
 * @property Customer $Customer
 * @property CustomerAddress $CustomerAddress
 * @property Order $Order
 * @property OrderAddress $OrderAddress
 * @property OrderProduct $OrderProduct
 * @property EmailTemplate $EmailTemplate
 * @property Store $Store
 * @property ProductRetailer $ProductRetailer
 * @property ProductStateFee $ProductStateFee
 * @property AppModel $Notifycustomer
 * @property Page $Page
 * @property Btask $Btask
 * @property StripeUser $StripeUser
 * @property StripeUserCapability $StripeUserCapability
 * @property QuickbookProduct $QuickbookProduct
 * @property AbandonCart $AbandonCart
 * @property UserSetting $UserSetting
 * @property SuccessDetails $SuccessDetails
 * @property ErrorDetails $ErrorDetails
 * @property ManufacturerRetailer $ManufacturerRetailer
 * @property Discount $Discount
 * @property DiscountUsage $DiscountUsage
 * @property WarehouseProductReservation $WarehouseProductReservation
 */
class ShopifyController extends AppController
{
    /**
     * @var string
     */
    public $name = 'Shopify';

    public $components = [
        'AccessTokenHandler',
        'AddressValidator',
        'Currency',
        'OrderPlacer',
        'ShippingCalculator',
        'Shopify.ShopifyAPI',
        'Shopify.ShopifyCarts',
        'Shopify.Shopify',
        'OrderLogic',
        'NotificationLogic',
        'Lightspeed',
        'SquarePos',
        'Avatax',
        'Shopifypos.ShopifyPOS',
        'Stripe.Stripe',
        'Quickbook.Quickbook',
        'Vendpos.VendPOS',
    ];

    /**
     * @var array
     */
    public $uses = array('User', 'Preorder', 'Customer', 'CustomerAddress', 'Order', 'OrderAddress', 'OrderProduct', 'EmailTemplate', 'Store', 'ProductRetailer', 'ProductStateFee', 'Notifycustomer', 'Page', 'Btask', 'StripeUser', 'StripeUserCapability', 'Quickbook.QuickbookProduct', 'AbandonCart', 'UserSetting', 'SuccessDetails', 'ErrorDetails', 'ManufacturerRetailer', 'Discount', 'DiscountUsage', 'WarehouseProductReservation');
    /**
     *
     */
    public function beforeFilter()
    {
        parent::beforeFilter();

        // Override 'Config.language' if it was set to the session Auth.User language_code by AppController.
        // Assumes that endpoints in this controller using the session Auth user will not translate anything.
        Configure::write('Config.language', $this->Translate->getBrowserLanguage());

        $this->Auth->allow([
            'checkoutPage',
            'waiting_room',
            'editPage',
            'payment_request_shipping_options',
            'payment_request_cancel',
            'paymentPage',
            'preorders',
            'placeOrder',
            'success',
            'error',
            'displaySuccess',
            'createBasicSetup',
            'getInStoreRetailers',
            'getLocalDeliveryRetailers',
            'getShipRetailers',
            'stripePayment',
            'clone_platform_payment_method',
            'displayError',
            'getSellDirect',
            'getOrderSuccessDetails',
            'getErrorDetails',
            'verifyDiscountCode',
            'verifyStoreAssociatePin',
            'ancillary_fee',
            'state_id_from_codes',
            'get_states',
            'validate_address',
        ]);

        $this->AccessTokenHandler->accessLimit = CHECKOUT_ACCESS_LIMIT;
        $this->AccessTokenHandler->accessExpires = CHECKOUT_ACCESS_EXPIRES;
    }

    public function checkoutPage()
    {
        if((bool)strpos($this->request->url, 'getShopifyBilling'))
        {
            CakeLog::warning('Request using Deprecated Route. Request:' . json_encode(['request' => json_encode($this->request)]));
        }

        $referer = $_SERVER['HTTP_REFERER'] ?? $this->request->data('referer');
        if (empty($referer)) {
            throw new BadRequestException('Missing HTTP_REFERER');
        }
        $check = parse_url($referer);
        $domainUrl = "{$check['scheme']}://{$check['host']}";

        $this->request->data['cart'] = $this->request->data('cart') ?: $this->request->data['items'];
        unset($this->request->data['items']);
        $this->request->data['cart'] = json_decode($this->request->data['cart'], true);

        CakeLog::debug(json_encode(['domainUrl' => $domainUrl, 'data' => $this->request->data]));

        // Set session vars dependent on the request ASAP to be used in the error redirect
        $this->Session->write('Shopify.domainUrl', $domainUrl);
        $this->Session->write('Shopify.trackingId', $this->request->data['trackingId']);
        $this->Session->write('Shopify.trackingCountry', $this->request->data('trackingCountry') ?: 'US');
        $this->Session->write('Shopify.gtmId', $this->request->data['gtmId']);
        $this->Session->write('Shopify.customer', $this->request->data['customer']);
        // Clear any persisted discount data in case the cart has changed
        $this->Session->delete('Shopify.discount');

        $token = $this->request->data('token');
        if (!$token || $token !== $this->request->param('token')) {
            CakeLog::error(json_encode(['postToken' => $this->request->data('token'), 'urlToken' => $this->request->param('token')]));
            $this->_saveSystemErrorMessage();
            return $this->redirect('/shopifyerror');
        }
        $this->Session->write('Shopify.token', $token);

        $user = $this->_checkManUserToken($token, [
            'User.id',
            'User.email_address',
            'User.gtm_container_id',
            'User.enable_ga_tracking',
            'User.ga_tracking_id',
            'User.google_conversion_id',
            'User.fbpixelId',
            'User.country_id',
        ]);
        if (empty($user['User']['id'])) {
            CakeLog::error('User not found for token: ' . $token);
            $this->_saveSystemErrorMessage();
            return $this->redirect('/shopifyerror');
        }

        // Set layout session vars dependent on the brand
        if (!empty($this->request->query['updates']) && is_array($this->request->query['updates'])) {
            $this->request->data['cart'] = $this->ShopifyCarts->applyUpdates((int)$user['User']['id'], $this->request->query['updates'], (array)$this->request->data['cart']);
        }
        if (!empty($this->request->query['attributes']) && is_array($this->request->query['attributes'])) {
            $this->request->data['cart'] = $this->ShopifyCarts->applyAttributes($this->request->query['attributes'], (array)$this->request->data['cart']);
        }
        $this->ShopifyCarts->backup($this->request->data['cart']);

        if ($user['User']['gtm_container_id']) {
            $this->Session->write('Shopify.gtmId', $user['User']['gtm_container_id']);
        }
        if ($user['User']['ga_tracking_id']) {
            $this->Session->write('Shopify.trackingId', $user['User']['ga_tracking_id']);
        }
        if ($user['User']['country_id'] && !$this->request->data('trackingCountry')) {
            $this->Session->write('Shopify.trackingCountry', $this->Country->getCountryCode($user['User']['country_id']));
        }
        if ($user['User']['google_conversion_id']) {
            $this->Session->write('Shopify.googleAdsConversionId', $user['User']['google_conversion_id']);
        }
        if (!$user['User']['enable_ga_tracking']) {
            $this->Session->delete('Shopify.trackingId');
        }
        $this->Session->write('Shopify.fbpixelId', $user['User']['fbpixelId']);
        $this->Session->write('Shopify.checkoutHeadSnippet', $this->User->findCheckoutHeadSnippet($token));
        $this->Session->write('Shopify.brandPolicies', $this->_brandPolicies($user));

        if ($this->AccessTokenHandler->requestAccess()) {
            $redirectUrl = ['plugin' => 'Shopify', 'controller' => 'Shopify', 'action' => 'editPage', '?' => $this->request->query];
        } else {
            $redirectUrl = ['plugin' => 'shopify', 'controller' => 'shopify', 'action' => 'waiting_room', '?' => $this->request->query];
        }

        return $this->redirect($redirectUrl);
    }

    public function waiting_room()
    {
        $this->set(array_fill_keys(['total', 'position', 'redirect'], null));

        $token = $this->Session->read('Shopify.token');
        $user = $this->_checkManUserToken($token, [
            'User.id',
            'User.company_name',
            'User.avatar',
            'User.shop_home_url',
            'User.shop_cart_url',
            'User.brand_accent_color',
            'User.checkout_font_family',
        ]);
        if (empty($user['User']['id'])) {
            CakeLog::error('User not found for token: ' . $token);
            $this->_saveSystemErrorMessage();
            $this->set('redirect', Router::url(['plugin' => 'Shopify', 'controller' => 'Shopify', 'action' => 'error']));

            return (!$this->request->is('ajax')) ? $this->redirect($this->viewVars['redirect']) : null;
        }

        // Check for oversold items after Customer Info inputs are saved to the session
        if ($this->_saveMessageIfOutOfStockError((int)$user['User']['id'], true)) {
            $this->set('redirect', Router::url(['plugin' => 'Shopify', 'controller' => 'Shopify', 'action' => 'error']));

            return (!$this->request->is('ajax')) ? $this->redirect($this->viewVars['redirect']) : null;
        }

        $accessGranted = $this->AccessTokenHandler->requestAccess();

        $position = $this->AccessTokenHandler->getPosition();
        $this->set('total', $this->request->query['total'] ?? $position);
        $this->set('position', $position);

        if ($accessGranted) {
            $this->set('redirect', Router::url(['plugin' => 'Shopify', 'controller' => 'Shopify', 'action' => 'editPage', '?' => $this->request->query]));

            return (!$this->request->is('ajax')) ? $this->redirect($this->viewVars['redirect']) : null;
        }

        $this->setLayoutViewVars($user);
        $this->set('homeUrl', 'javascript:void(0);');
    }

    public function editPage()
    {
        $token = $this->Session->read('Shopify.token');
        $user = $this->_checkManUserToken($token);
        if (empty($user['User']['id'])) {
            CakeLog::error('User not found for token: ' . $token);
            $this->_saveSystemErrorMessage();
            return $this->redirect('/shopifyerror');
        }

        if (!$this->AccessTokenHandler->requestAccess()) {
            return $this->redirect(['plugin' => 'shopify', 'controller' => 'shopify', 'action' => 'waiting_room', '?' => $this->request->query]);
        }

        $this->Session->write('Shopify.Next', 'payment');
        $this->Session->write('Shopify.User', $user);
        $this->Session->write('Shopify.checkoutHeadSnippet', $this->User->findCheckoutHeadSnippet($token));
        $this->Session->write('Shopify.brandPolicies', $this->_brandPolicies($user));

        $cart = $this->ShopifyCarts->restore();
        if($this->request->query('resetPreselectedOptions')){
            $cart = $this->ShopifyCarts->resetPreselectedOptions();
        }
        $cart = $this->ShopifyCarts->applyRegionalCurrencyPricing((int)$user['User']['id'], $cart['currency'], $user['User']['currency_code']);
        $cart = $this->ShopifyCarts->applyLocalization((int)$user['User']['id']);

        $this->Session->write('Shopify.productStatus', $this->productCheck($user, $cart['items']));

        $discountInfo = (array)(
            $this->Session->read('Shopify.discount') ?:
            $this->Discount->findForEcommerceByAuto((int)$user['User']['id'], (array)$cart['items'])
        );
        $this->Session->write('Shopify.discount', $discountInfo);

        $currencyCode = (string)($cart['currency'] ?: $user['User']['currency_code']);

        $shippingAddress = (array)$this->Session->read('Shopify.shipping');
        $shippingAddress['email'] = $shippingAddress['email'] ?? $discountInfo['email'] ?? null;

        $userMarketing = $this->User->findEcommerceCustomContent($user['User']['id'], $user['User']['email_address'])['customer_accepts_marketing_label'];

        $shippingCountries = $this->Country->findAllShippingCountriesById((int)$user['User']['id']);
        if (!isset($shippingCountries[$shippingAddress['country'] ?? null])) {
            $shippingAddress['country'] = $this->Country->getDefaultSelectedCountryId($currencyCode, $shippingCountries);
        }

        $this->set('title_for_layout', 'Customer Information');
        $this->setLayoutViewVars($user);
        $this->set('marketingLabel', $userMarketing);
        $this->set('productStatus', ($this->Session->read('Shopify.productStatus.status') == false));
        $this->set('cart', $cart);
        $this->set('customer', $this->Session->read('Shopify.customer'));
        $this->set('token', $token);
        $this->set('trackingId', $this->Session->read('Shopify.trackingId'));
        $this->set('gtmId', $this->Session->read('Shopify.gtmId'));
        $this->set('analyticsCheckoutEvent', 'begin_checkout');
        $this->set('gtmFunnelStep', 1);
        $this->set('fbpixelId', $this->Session->read('Shopify.fbpixelId'));
        $this->set('discountInfo', $discountInfo);
        $this->set('shipping', $shippingAddress);
        $this->set('shippingCountries', $shippingCountries);
        $this->set('states', $this->State->findAllShippingStatesById((int)$user['User']['id'], (int)$shippingAddress['country']));
        $this->set('currency', $currencyCode);
        $this->set('enable_stripe_payment_request', $user['User']['enable_stripe_payment_request']);
        $this->set('enable_google_pay', $user['User']['enable_google_pay_payment_request']);
        $this->set('enable_apple_pay', $user['User']['enable_apple_pay_payment_request']);

        $this->render('checkoutPage');
    }

    protected function productCheck(array $user, array $items): array
    {
        $returnPolicy = nl2br($this->UserSetting->getReturnPolicy($user['User']['id'], $user['User']['email_address']));
        $fbpixelId = $user['User']['fbpixelId'];
        $allProductsAreEnabled = !$this->Product->exists([
            'Product.productID' => array_column($items, 'variant_id'),
            'Product.user_id' => $user['User']['id'],
            'Product.product_status !=' => ProductStatus::ENABLED,
        ]);

        return [
            'return-policy' => $returnPolicy,
            'fbpixelId' => $fbpixelId,
            'status' => $allProductsAreEnabled,
        ];
    }

    public function verifyDiscountCode()
    {
        $userId = (int)$this->Session->read('Shopify.User.User.id');
        if (empty($userId)) {
            $this->response->body(json_encode(['error' => true, 'message' => __('Your session has expired')]));
            return $this->response;
        }

        $items = (array)$this->ShopifyCarts->getValue('items');
        $sessionDiscount = $this->Session->read('Shopify.discount');
        $enteredCode = strtoupper(trim((string)$this->request->data('code')));
        $sessionCode = strtoupper((string)($sessionDiscount['code'] ?? ''));

        $isManualOverride = $enteredCode && $enteredCode !== $sessionCode;

        if ($isManualOverride) {
            $this->Session->delete('Shopify.discount');

            if (
                !$this->request->data('email_address') ||
                !AppModelValidationsTrait::email($this->request->data['email_address'], true)
            ) {
                $this->response->body(json_encode(['error' => true, 'message' => __('Please provide a valid email before applying code')]));
                return $this->response;
            }

            $discount_info = $this->Discount->findForEcommerceByCode($userId, trim($this->request->data['code']), $items);
            if (empty($discount_info)) {
                $this->response->body(json_encode(['error' => true, 'message' => __('Invalid discount code')]));
                return $this->response;
            }

            // Save to session in case shipping address is empty on reload
            $discount_info['email'] = $this->request->data['email_address'];

            if ($discount_info['limit_per_customer'] == 1 && $this->DiscountUsage->hasCustomerUsage($userId, $discount_info['id'], $discount_info['email'])) {
                $this->response->body(json_encode(['error' => true, 'message' => __('Coupon code was used on a previous order')]));
                return $this->response;
            }

            $this->Session->write('Shopify.discount', $discount_info);
        } else {
            $discount_info = $sessionDiscount;

            if (empty($discount_info)) {
                $this->response->body(json_encode(['error' => true, 'message' => __('No discount available')]));
                return $this->response;
            }
        }

        $cart = $this->ShopifyCarts->get();

        $autoAddQuantitiesByVariantId = $this->Discount->findEcommerceAutoAddQuantitiesByVariantId($userId, (array)$discount_info['DiscountRule'], $items);
        if ($autoAddQuantitiesByVariantId) {
            $autoAddItems = $this->ShopifyCarts->findItemsForUpdates($userId, $autoAddQuantitiesByVariantId);
            $cart = $this->ShopifyCarts->getCartWithAddedItems($cart, $autoAddItems);
            $items = (array)$cart['items'];

            // Permanently add the items to the cart
            $this->ShopifyCarts->backup($cart);
        }

        $itemDiscounts = $this->Discount->calculateEcommerceItemDiscounts($userId, $discount_info, $items);

        $this->set([
            'cart' => $cart,
            'discountInfo' => $discount_info,
            'itemDiscounts' => $itemDiscounts,
            // Prevent unnecessary re-rendering of order summary items
            'itemsChanged' => (bool)$autoAddQuantitiesByVariantId,
        ]);

        return null;
    }

    public function verifyStoreAssociatePin()
    {
        $this->autoRender = false;
        if (!$this->request->is('AJAX')) {
            $this->Session->delete('Shopify.StoreAssociate');
        }
        if ($this->request->is('GET') && isset($this->request->query['data'])) {
            $this->request->data = $this->request->query['data'];
        }
        $associate = [];
        if (!empty($this->request->data['User']['store_associate_pin'])) {
            $associate = $this->requestAction('/ws/verifyStoreAssociatePin', ['data' => $this->request->data['User']]);
            $associate = json_decode($associate, true);
        }
        if ($associate) {
            if ($this->request->isAll(['AJAX', 'GET'])) {
                $this->response->body(json_encode($associate));
            } elseif ($this->request->is('POST')) {
                $this->Session->write('Shopify.StoreAssociate', $associate);
            }
        }
        if (!$this->request->is('AJAX')) {
            $this->redirect('/customer_information');
        }
    }

    public function ancillary_fee()
    {
        $userId = (int)$this->Session->read('Shopify.User.User.id');
        if (empty($userId)) {
            return $this->_exceptionResponse(new UnauthorizedException(), 'Session has expired');
        }

        // Query string is easier to set from JS
        $stateId = (int)$this->request->query('state_id');
        if (empty($stateId)) {
            return $this->_exceptionResponse(new BadRequestException(), 'Missing required state/province');
        }
        $countryId = (int)$this->State->fieldByConditions('country_id', ['State.id' => $stateId]);

        $cart = $this->ShopifyCarts->get();

        $cartWithFee = $this->ProductStateFee->applyFeeToShopifyCart($userId, $countryId, $stateId, $cart);
        $feeVariantIds = (array)$this->Product->find('list', [
            'recursive' => -1,
            'conditions' => [
                'Product.is_fee_product' => true,
                'Product.productID' => array_column($cartWithFee['items'], 'variant_id'),
                'Product.user_id' => $userId,
                'Product.deleted' => false,
            ],
            'fields' => ['productID', 'productID'],
            'order' => false,
        ]);
        $feeItems = array_filter($cartWithFee['items'], fn(array $item): bool => array_key_exists($item['variant_id'], $feeVariantIds));

        $prevLinePriceByVariantId = array_intersect_key(array_column($cart['items'], 'line_price', 'variant_id'), $feeVariantIds);
        $feeItems = array_values(array_filter($feeItems, function(array $feeItem) use ($prevLinePriceByVariantId): bool {
            $feeVariantId = (int)$feeItem['variant_id'];
            $prev_line_price = (int)($prevLinePriceByVariantId[$feeVariantId] ?? 0);
            $line_price = (int)$feeItem['line_price'];

            return $line_price > 0 && $line_price !== $prev_line_price;
        }));

        if ($feeItems) {
            $this->set('currency', $cart['currency'] ?: $this->Session->read('Shopify.User.User.currency_code'));
            $this->set('feeItems', $feeItems);

            App::uses('AppView', 'View');
            $this->set('html', (new AppView($this))->render('Shopify./Elements/checkout_page/ancillary_fee', false));
        }

        $this->set(['success' => true]);
        $this->set('_serialize', array_keys($this->viewVars));
    }

    public function payment_request_shipping_options()
    {
        $this->request->allowMethod('POST');

        $this->Session->write('Shopify.Next', 'payment');
        $this->paymentPage();

        $retailerIdsByType = [
            'instore' => $this->viewVars['instore'],
            'local_install' => $this->viewVars['localInstall'],
            'local_delivery' => $this->viewVars['localDelivery'],
            'shipFromStore' => $this->viewVars['shipfromstore'],
            'sellDirect' => array_filter(array($this->viewVars['selldirect'])),
        ];
        $retailerTypes = array_keys($retailerIdsByType);
        $retailerTypes = array_combine($retailerTypes, $retailerTypes);

        $customContent = $this->viewVars['brandContent'];
        $shippingMethodLabels = [
            'instore' => $customContent['instore_pickup_title'],
            'local_install' => $customContent['local_install_title'],
            'local_delivery' => $customContent['local_delivery_title'],
            'shipFromStore' => $customContent['ship_from_store_title'],
            'sellDirect' => $customContent['ship_to_door_title'],
        ];

        $retailerUrls = [
            'instore' => Router::url(['plugin' => 'Shopify', 'controller' => 'Shopify', 'action' => 'getInStoreRetailers']),
            'local_install' => Router::url(['plugin' => 'Shopify', 'controller' => 'Shopify', 'action' => 'getInStoreRetailers']),
            'local_delivery' => Router::url(['plugin' => 'shopify', 'controller' => 'shopify', 'action' => 'getInStoreRetailers']),
            'shipFromStore' => Router::url(['plugin' => 'Shopify', 'controller' => 'Shopify', 'action' => 'getShipRetailers']),
            'sellDirect' => Router::url(['plugin' => 'Shopify', 'controller' => 'Shopify', 'action' => 'getSellDirect']),
        ];

        $retailerInfo = array_map(function($type) use ($retailerIdsByType, $retailerUrls) {
            $retailerIds = $retailerIdsByType[$type];
            $retailerUrl = $retailerUrls[$type];

            // Only load the closest retailer for each shipping method
            $retailerLimit = 1; //count($retailerIds);

            $map = array();
            foreach ($retailerIds as $retailerId) {
                $data = array(
                    'retailer_id' => $retailerId,
                    'type' => $type,
                    'count' => $retailerLimit,
                );

                CakeLog::debug(json_encode(compact('retailerUrl', 'data')));
                $result = json_decode($this->requestAction($retailerUrl, array('return', 'data' => $data)), true);

                $map[$retailerId] = isset($result['rawResponse']) ? $result['rawResponse'] : $result;

                if (!empty($result['stopLoading'])) {
                    break;
                }
            }
            return $map;
        }, $retailerTypes);

        $shippingOptions = array_reduce($retailerTypes, function($list, $type) use ($retailerInfo, $shippingMethodLabels) {
            $retailers = $retailerInfo[$type];
            $label = $shippingMethodLabels[$type];

            // $shippingId structured with other data associated with the shipping option
            $shippingId = [
                // Request data for [$this, 'stripePayment']
                'payment_data' => [
                    'type' => $type,
                    'retailer_id' => null,
                ],
                // Update fields for payment request
                'payment_items' => [
                    'line_items' => array_map(
                        function($item) {
                            return [
                                'amount' => $item['line_price'],
                                'label' => "x{$item['quantity']} {$item['title']}",
                                'pending' => false,
                            ];
                        },
                        (array)$this->ShopifyCarts->getValue('items')
                    ),
                    'discount' => [],
                    'shipping' => [],
                    'tax' => [],
                ],
            ];

            return array_merge($list, array_reduce(array_keys($retailers), function($list, $retailerId) use ($type, $retailers, $label, $shippingId) {
                $retailer = $retailers[$retailerId];

                $sellExclusiveResult = $this->Session->read('Shopify.sellExclusiveResult');
                $sellExclusiveDiscount = $sellExclusiveResult->discountAmount ?? 0;
                $sellExclusiveShipping = $sellExclusiveResult->shippingAmount ?? 0;
                $sellExclusiveTax = $sellExclusiveResult->taxamtdiscount ?? $sellExclusiveResult->taxamt ?? 0;

                $shippingId['payment_data']['retailer_id'] = $retailerId;
                if ($type === 'local_delivery') {
                    $shippingId['payment_data']['velofix'] = $retailer['velofix'];
                }

                $shippingAmount = $retailer['shippingAmount'];
                $taxamt = $retailer['taxamtdiscount'];
                if ($type === 'local_install') {
                    $shippingAmount = $retailer['shippingAmount_localinstall'];
                    $taxamt = $retailer['taxamtdiscount_localinstall'];
                } elseif ($type === 'local_delivery') {
                    $shippingAmount = $retailer['shippingAmount_localdelivery'];
                    $taxamt = $retailer['taxamtdiscount_localdelivery'];
                }

                $shippingId['payment_items']['discount']['amount'] = (int)round(-($retailer['discountAmount'] + $sellExclusiveDiscount) * 100);
                $shippingId['payment_items']['tax']['amount'] = (int)round(($taxamt + $sellExclusiveTax) * 100);

                if ($type !== 'sellDirect') {
                    //FIXME show km when appropriate
                    $label .= " ({$retailer['distance']} mi.)";

                    $detail = ($retailer['type'] === 'nonstock')
                        ? __('Available in 5 to 7 Business Days')
                        : __('Available Immediately');
                } else {
                    $detail = $retailer['shippingName'];
                }

                if ($type === 'sellDirect' && !empty($retailer['shippingRates'])) {
                    $calcTaxWithShipping = function($shippingAmount) use ($retailer, $taxamt) {
                        $shippingTax = $shippingAmount * $retailer['shipping_tax_rate'];
                        if (!$retailer['shipping_tax_option']) {
                            $shippingTax = 0;
                        }

                        return ($taxamt - $retailer['shippingTax']) + $shippingTax;
                    };

                    $list = array_merge($list, array_map(
                        function($rate) use ($shippingId, $label, $calcTaxWithShipping) {
                            $shippingId['payment_data']['courier_option'] = ['id' => $rate['id']];
                            $shippingId['payment_items']['tax']['amount'] = (int)round($calcTaxWithShipping($rate['amount']) * 100);

                            return [
                                'id' => json_encode($shippingId),
                                'label' => $label,
                                'detail' => $rate['name'],
                                'amount' => (int)round($rate['amount'] * 100),
                            ];
                        },
                        array_values($retailer['shippingRates'])
                    ));
                } else {
                    $list[] = [
                        'id' => json_encode($shippingId),
                        'label' => $label,
                        'detail' => $detail,
                        'amount' => (int)round(($shippingAmount + $sellExclusiveShipping) * 100),
                    ];
                }

                return $list;
            }, []));
        }, []);

        $this->set($shippingOptions);
        $this->set('_serialize', array_keys($shippingOptions));
    }

    public function payment_request_cancel()
    {
        $this->request->allowMethod('post');

        $this->Session->write('Shopify.Next', 'payment');

        return $this->response;
    }

    public function paymentPage()
    {
        $isValidGetRequest = !empty($this->request->params['abandonid']);
        $isValidPostRequest = ($this->request->is('POST') && !empty($this->request->data) && $this->Session->read('Shopify.Next') == 'payment');

        if (!$isValidGetRequest && !$isValidPostRequest) {
            $this->redirect(['action' => 'editPage']);
        }
        $this->Session->write('Shopify.Next', 'result');

        $this->Session->delete('Shopify.retailerInfo');

        if (!empty($this->request->params['abandonid'])) {
            $abandon = $this->AbandonCart->findEmailedCartByToken($this->request->params['abandonid']);
            if (empty($abandon)) {
                echo "Hope you already purchased, the link has expired!";
                exit;
            }
            $this->Session->write('Shopify.abandon_cart_id', $abandon['AbandonCart']['id']);

            $cart = (array)json_decode($abandon['AbandonCart']['cart'], true);
            $this->ShopifyCarts->backup($cart);

            $this->request->data['customer'] = $abandon['AbandonCart']['customer'];
            $this->request->data['token'] = $abandon['AbandonCart']['token'];
            $this->request->data['preferred_language'] = $abandon['AbandonCart']['preferred_language'];
            $this->request->data['trackingId'] = $abandon['AbandonCart']['trackingId'];
            $this->request->data['fbpixelId'] = $abandon['AbandonCart']['fbpixelId'];

            $log = json_decode($abandon['AbandonCart']['address'], true);

            $shippingAddress = (array)$log['address'];
            $acceptsMarketing = (bool)$log['accepts_marketing'];
            $brand = $this->_checkManUserToken($this->request->data['token']);

            $this->Session->write('Shopify.domainUrl', $log['domainUrl']);
            $this->Session->write('Shopify.checkoutHeadSnippet', $this->User->findCheckoutHeadSnippet($this->request->data['token']));
            $this->Session->write('Shopify.brandPolicies', $this->_brandPolicies($brand));
            $this->Session->write('Shopify.productStatus', $log['productStatus']);
        } else {
            $shippingAddress = $this->_processAddressFormFields(
                array_merge($this->request->data['check']['shipping_address'], $this->request->data['checkemailv']['shipping_address'])
            );
            if ($this->request->param('action') !== 'payment_request_shipping_options' && !$this->_validateAddress($shippingAddress)) {
                return $this->redirect(['plugin' => 'Shopify', 'controller' => 'Shopify', 'action' => 'editPage']);
            }

            $acceptsMarketing = (bool)$this->request->data['Customer']['accepts_marketing'];
            $brand = $this->_checkManUserToken($this->request->data['token']);

            // Going back and forth should not change the abandoned cart
            $cart = $this->ShopifyCarts->restore();

            if ($brand['User']['brand_abandon_cart'] == 1) {
                if (!empty($this->request->data['customer'])) {
                    $customer = $this->Shopify->getShopifyCustomer(
                        $this->request->data['customer'],
                        $brand['User']['api_key'],
                        $brand['User']['secret_key'],
                        rtrim($brand['User']['shop_url'], "/")
                    );
                    $customerEmail = $customer['email'];
                } else {
                    $customerEmail = $shippingAddress['email'];
                }
                if (!empty($customerEmail)) {
                    $domainUrl = $this->Session->read('Shopify.domainUrl');
                    $log = array(
                        'address' => $shippingAddress,
                        'users' => $brand,
                        'domainUrl' => $domainUrl,
                        'cartUrl' => $brand['User']['shop_cart_url'] ?: ($domainUrl . '/cart'),
                        'productStatus' => $this->Session->read('Shopify.productStatus'),
                        'accepts_marketing' => $acceptsMarketing,
                    );
                    $abandon = array(
                        'id' => $this->AbandonCart->findNewCartIdByCustomer($brand['User']['id'], $customerEmail),
                        'user_id' => $brand['User']['id'],
                        'abandon_cart_token' => $cart['token'],
                        'token' => $this->request->data['token'],
                        'email_address' => $customerEmail,
                        'preferred_language' => $this->request->data['preferred_language'],
                        'customer' => $this->request->data['customer'],
                        'cart' => json_encode($cart),
                        'trackingId' => $this->request->data['trackingId'],
                        'fbpixelId' => $this->request->data['fbpixelId'],
                        'address' => json_encode($log),
                        'preOrderId' => null,
                        'status' => 0,
                    );
                    $this->_webServiceLog("Saving abandoned cart " . json_encode($abandon));
                    try {
                        if ($this->AbandonCart->save($abandon)) {
                            $this->Session->write('Shopify.abandon_cart_id', $this->AbandonCart->id);
                            $klaviyo = KlaviyoFactory::new($this->User, $brand['User']['id']);
                            $klaviyo->trackStartedCheckout($this->AbandonCart, $this->AbandonCart->id);
                        }
                    } catch (\GuzzleHttp\Exception\GuzzleException $e) {
                        CakeLog::warning($e, ['klaviyo']);
                    } catch (Exception $e) {
                        CakeLog::warning($e);
                    }
                }
            }
        }

        $this->Session->write('Shopify.customer', $this->request->data['customer']);
        $this->Session->write('Shopify.token', $this->request->data['token']);
        $this->Session->write('Shopify.fbpixelId', $this->request->data['fbpixelId']);

        $this->Session->write('Shopify.shipping', $shippingAddress);
        $this->Session->write('Shopify.User', $brand);
        $this->Session->write('Shopify.accepts_marketing', $acceptsMarketing);

        if ($brand['User']['enable_ga_tracking']) {
            $this->Session->write('Shopify.trackingId', $this->request->data['trackingId']);
        } else {
            $this->Session->delete('Shopify.trackingId');
        }

        $discountInfo = (array)$this->Session->read('Shopify.discount');
        if (Hash::get($discountInfo, 'code') !== trim($this->request->data('discount_code'))) {
            $this->Session->delete('Shopify.discount');
            $discountInfo = array();
        }
        CakeLog::debug(json_encode(compact('discountInfo')));

        // Refresh expiry and catch any abandoned cart redemptions
        if (!$this->AccessTokenHandler->requestAccess()) {
            return $this->redirect(['plugin' => 'shopify', 'controller' => 'shopify', 'action' => 'waiting_room', '?' => $this->request->query]);
        }

        $forceSellDirectExclusive = (Hash::get($discountInfo, 'retailer_option') === 'direct_exclusive');
        $discountRetailerIds = !empty($discountInfo['id'])
            ? $this->User->listDiscountRetailerIds((int)$brand['User']['id'], $discountInfo['retailer_option'], $discountInfo['retailer_values'])
            : null;

        $this->set('title_for_layout', 'Delivery Method');
        $this->setLayoutViewVars($brand);
        $this->set('brandEmail', $brand['User']['email_address']);
        $this->set('shippingInfographic', $brand['User']['shipping_infographic']);
        $this->set('ship_to_store_noretailer_message', $brand['User']['ship_to_store_noretailer_message']);
        $this->set('local_delivery_no_retailer_listing', $brand['User']['local_delivery_no_retailer_listing']);

        // Apply cart pricing changes before any price-based calculations
        $cart = $this->ShopifyCarts->applyRegionalCurrencyPricing((int)$brand['User']['id'], $this->_getRegionCurrency($shippingAddress), $brand['User']['currency_code']);
        $cart = $this->ShopifyCarts->applyLocalization((int)$brand['User']['id']);

        $currencyCode = (string)($cart['currency'] ?: $brand['User']['currency_code']);

        $associatePin = $this->Session->read('Shopify.StoreAssociate.User.store_associate_pin');
        $result = $this->requestAction('/ws/getRetailerIds', array('data' => array(
            'token' => $this->request->data['token'],
            'customer' => $this->request->data['customer'],
            'items' => json_encode($cart['items']),
            'address' => json_encode($shippingAddress),
            'store_associate_pin' => $associatePin,
            'force_sell_direct_exclusive' => $forceSellDirectExclusive,
            'discountRetailerIds' => $discountRetailerIds,
            'ecommerceViewId' => $this->Session->read('Shopify.ecommerceViewId'),
            'requestedRetailerId' => $this->ShopifyCarts->getPreselectedRetailerId(),
            'requestedDeliveryOption' => $this->ShopifyCarts->getPreselectedDeliveryOption(),
        )));
        $resultList = json_decode($result);

        $this->Session->write('Shopify.ecommerceViewId', Hash::get((array)$resultList, 'ecommerceViewId'));

        if (isset($resultList->geopoints)) {
            $this->set('geopoint', $resultList->geopoints);
            $this->Session->write('Shopify.customer_geopoints', $resultList->geopoints);
        }

        if (isset($resultList->map)) {
            $this->set('map', $resultList->map);
            $this->Session->write('Shopify.brand_mapoption', $resultList->map);
        }

        $inStore = $this->_extractRetailerIds($resultList, 'instore');
        $this->set('instore', $inStore);
        $this->set('showInStore', isset($resultList->instore));

        $localInstall = $this->_extractRetailerIds($resultList, 'local_install');
        $this->set('localInstall', $localInstall);
        $this->set('showLocalInstall', !empty($localInstall));

        $localDelivery = $this->_extractRetailerIds($resultList, 'local_delivery');
        if (!empty($resultList->enable_velofix) && isset($resultList->selldirect)) {
            $localDelivery[] = $resultList->selldirect;
        }
        $this->set('localDelivery', $localDelivery);
        $this->set('showLocalDelivery', !empty($localDelivery));

        $shipFromStore = $this->_extractRetailerIds($resultList, 'shipfromstore');
        $this->set('shipfromstore', $shipFromStore);
        $this->set('showShipFromStore', !empty($shipFromStore));

        if (!empty($resultList->selldirect)) {
            $this->set('selldirect', $resultList->selldirect);
        }
        $this->set('showSellDirect', !empty($resultList->selldirect));

        $addressHasRetailers = ($inStore || $localInstall || $localDelivery || $shipFromStore);
        $addressHasSellDirect = !empty($resultList->selldirect);

        if (isset($resultList->messages)) {
            $messages = json_decode($resultList->messages, true);
            $this->set('messages', $messages);
            $this->Session->write('Shopify.messages', $messages);
        }

        $shippingRates = $this->_calculateShippingRates((int)$brand['User']['id'], $currencyCode, $shippingAddress, (array)$cart['items'], $forceSellDirectExclusive, $discountInfo);
        CakeLog::debug(json_encode(compact('shippingRates')));
        $this->Session->write('Shopify.shippingRates', $shippingRates);
        // The 'Shopify.shippingRates' collection should be used instead of the below session keys
        $this->Session->write('Shopify.shippingAmount', $shippingRates[0]['amount'] ?? null);
        $this->Session->write('Shopify.shippingDiscount', $shippingRates[0]['discount'] ?? null);
        $this->Session->write('Shopify.shippingName', $shippingRates[0]['name'] ?? null);
        $this->Session->write('Shopify.shippingCode', $shippingRates[0]['code'] ?? null);
        $this->Session->write('Shopify.packageBoxWeight', $shippingRates[0]['box_weight'] ?? null);

        // Calculate fees after shipping because fees are independent
        $cart = $this->ShopifyCarts->applyStateCountryFee((int)$brand['User']['id'], (int)$shippingAddress['country'], (int)$shippingAddress['province']);

        $itemDiscounts = $this->Discount->calculateEcommerceItemDiscounts((int)$brand['User']['id'], $discountInfo, (array)$cart['items']);
        $this->Session->write('Shopify.ItemDiscounts', $itemDiscounts);

        $cart = $this->ShopifyCarts->applyDiscount((array)$itemDiscounts);

        $this->Session->delete('Shopify.sellExclusiveResult');
        if ($addressHasRetailers) {
            $sellExclusiveResult = $this->_checkSellExclusiveProduct((int)$brand['User']['id'], $currencyCode, $shippingAddress, (array)$cart['items'], $discountInfo);
            if (!empty($sellExclusiveResult)) {
                $this->Session->write('Shopify.sellExclusiveResult', $sellExclusiveResult);
                $this->set('sellExclusiveResult', json_encode($sellExclusiveResult));
                if (isset($sellExclusiveResult->buyDirectOnly)) {
                    $this->set('buyDirectOnly', $sellExclusiveResult->buyDirectOnly);
                }
            }
        }

        $this->set('currency', $currencyCode);
        $this->set('cart', $cart);
        $this->set('items', $cart['items']);
        $this->set('customer', $this->request->data['customer']);
        $this->set('token', $this->request->data['token']);
        $this->set('trackingId', $this->Session->read('Shopify.trackingId'));
        $this->set('gtmId', $this->Session->read('Shopify.gtmId'));
        $this->set('analyticsCheckoutEvent', 2);
        $this->set('gtmFunnelStep', 2);
        $this->set('fbpixelId', $this->request->data['fbpixelId']);
        $this->set('shipping', $shippingAddress);
        $this->set('shippingCountry', $this->Country->getCountryNameByCode($shippingAddress['countryName']));
        $this->set('preselectedRetailer', $this->ShopifyCarts->getPreselectedRetailerId());
        $this->set('preselectedDeliveryOption', $this->ShopifyCarts->getPreselectedDeliveryOption());
        $this->set('billingCountries', $this->Country->findAllBillingCountriesById());
        $this->set('states', $this->State->findAllBillingStatesById((int)$shippingAddress['country']));

        $brandContent = $this->User->findEcommerceCustomContent($brand['User']['id'], $brand['User']['email_address']);
        $hasWillCallShipping = (bool)Hash::extract($shippingRates, '{n}[type=will_call]');
        if ($hasWillCallShipping) {
            $brandContent['ship_to_door_title'] = __('Will Call');
        }
        $this->set('brandContent', $brandContent);
    }

    private function _getRegionCurrency(array $shipping): ?string
    {
        return !empty($shipping['countryCode']) ? $this->Currency->getCurrencyForCountry($shipping['countryCode']) : null;
    }

    private function _calculateShippingRates(int $brandId, string $currencyCode, array $shippingAddress, array $cartItems, bool $forceSellDirectExclusive, array $discount = []): array
    {
        if ($forceSellDirectExclusive && $this->ShippingCalculator->isToWarehouse($brandId, $shippingAddress)) {
            return $this->ShippingCalculator->getWillCallShippingRates($currencyCode);
        }

        $products = $this->_findAllProductsForShippingRates($brandId, $cartItems);

        return $this->ShippingCalculator->getRetailerShippingRates($brandId, $currencyCode, $shippingAddress, $products, $discount);
    }

    private function setLayoutViewVars($user)
    {
        $domainUrl = $this->Session->read('Shopify.domainUrl');
        $this->set('domainUrl', $domainUrl);
        $this->set('homeUrl', $user['User']['shop_home_url'] ?: $domainUrl);
        $this->set('cartUrl', $user['User']['shop_cart_url'] ?: ($domainUrl . '/cart'));

        $basePath = defined('MAIN_BASE_PATH') ? MAIN_BASE_PATH : BASE_PATH;
        $this->set('brandName', $user['User']['company_name']);
        $this->set('checkoutBackgroundImage', $user['User']['checkout_background_image']);
        $this->set('checkoutLogo', $user['User']['checkout_logo']);
        $this->set('displayCheckoutLogoLeft', $user['User']['display_checkout_logo_left']);
        $this->set('brandLogo', ($user['User']['avatar']) ? ($basePath . 'files/users/' . $user['User']['avatar']) : '');
        $this->set('brandAccentColor', $user['User']['brand_accent_color']);
        $this->set('checkoutFontFamily', $user['User']['checkout_font_family']);
        $this->set('checkoutHeadSnippet', $this->Session->read('Shopify.checkoutHeadSnippet'));
        $this->set('brandPolicies', $this->Session->read('Shopify.brandPolicies'));
    }

    /**
     * @param int $brandId
     * @param string $currencyCode
     * @param array $shippingAddress
     * @param array $cartItems
     * @return null|object
     */
    //TODO Speed up load times by extracting an ajax endpoint to defer the multiple shipping calculator calls
    public function _checkSellExclusiveProduct(int $brandId, string $currencyCode, array $shippingAddress, array $cartItems, array $discountInfo): ?object
    {
        $allProducts = Hash::combine(
            $this->_findAllProductsForShippingRates($brandId, $cartItems),
            '{n}.Product.id',
            '{n}'
        );
        $sellExclusiveProducts = array_filter($allProducts, function($product) {
            return ($product['Product']['sell_direct'] == ProductSellDirect::EXCLUSIVELY);
        });
        if (!$sellExclusiveProducts) {
            return null;
        }
        $retailProducts = array_diff_key($allProducts, $sellExclusiveProducts);

        $sellExclusiveShippingRates = $this->ShippingCalculator->getRetailerShippingRates($brandId, $currencyCode, $shippingAddress, $sellExclusiveProducts, $discountInfo);

        $sellExclusiveResult = $this->_getSellExclusive($sellExclusiveShippingRates[0]['amount'] ?? null, $sellExclusiveShippingRates[0]['discount'] ?? 0);
        $sellExclusiveResult = json_decode($sellExclusiveResult);
        if (empty($sellExclusiveResult->id)) {
            return null;
        }

        $retailShippingRates = $this->ShippingCalculator->getRetailerShippingRates($brandId, $currencyCode, $shippingAddress, $retailProducts, $discountInfo);
        $this->Session->write('Shopify.shippingAmount', $retailShippingRates[0]['amount'] ?? null);
        $this->Session->write('Shopify.shippingDiscount', $retailShippingRates[0]['discount'] ?? null);

        return (object)$sellExclusiveResult;
    }

    protected function _findAllProductsForShippingRates(int $brandId, array $cartItems): array
    {
        $cartItemByVariantId = array_map(
            function($item) {
                return [
                    'product_id' => $item['variant_id'],
                    'qty' => $item['quantity'],
                    'price' => format_number($item['price'] / 100),
                    'compare_at_price' => isset($item['compare_at_price']) ? format_number($item['compare_at_price'] / 100) : null,
                ];
            },
            array_combine(array_column($cartItems, 'variant_id'), $cartItems)
        );

        return $this->Product->findAllForEcommerceShippingRates($brandId, $cartItemByVariantId);
    }

    /**
     * @param object $resultList
     * @param string $key
     * @return array
     */
    protected function _extractRetailerIds($resultList, $key)
    {
        $retailerList = array();
        if (isset($resultList->{$key})) {
            $retailerList = $resultList->{$key};
        }
        return array_map(
            function($value) {
                return $value->u->retailer_id;
            },
            $retailerList
        );
    }

    public function getOrderSuccessDetails()
    {
        $start_time_function = microtime(true);
        $start_time = $start_time_function;

        $this->autoRender = false;

        if((bool)strpos($this->request->url, 'getShopifySuccess'))
        {
            CakeLog::warning('Request using Deprecated Route. Request:' . json_encode(['request' => json_encode($this->request)]));
        }

        $this->response->cors($this->request, '*', '*', 'X-Requested-With');

        $this->_webServiceLog(['query' => $this->request->query]);
        $orderNo = $this->request->query['id'];

        $successdetails = $this->SuccessDetails->findSuccessPage($this->request->query);
        if (empty($successdetails)) {
            $this->_webServiceLog(['response' => []]);
            $this->response->body(json_encode([]));
            return $this->response;
        }
        $this->_webServiceLog($successdetails);
        $start_time = $this->_logDeltaTime($start_time, 'Fetch SuccessDetails');

        $token = $successdetails['SuccessDetails']['user_token'];
        $type = $successdetails['SuccessDetails']['type'];
        $retailer = json_decode($successdetails['SuccessDetails']['retailer'], true);
        $shippingMethod = trim($successdetails['SuccessDetails']['shippingMethod'], "\"");
        $order = json_decode($successdetails['SuccessDetails']['orderData'], true);
        $cart = json_decode($successdetails['SuccessDetails']['cart'], true);
        $currency = $retailer['currency'];

        $shipping_method_ship_subtype = (string)Hash::get($retailer, 'shipping_method_ship_subtype');

        $order_data = array_map(
            function($item) use ($retailer) {
                $imageExe = ['.jpg', '.png'];
                $imageRep = ['_150x150_cropped.jpg', '_150x150_cropped.png'];

                $variantId = $item['variant_id'];

                return [
                    'product_id' => $item['product_id'],
                    'variant_id' => $variantId,
                    'sku' => $item['sku'],
                    'handle' => $item['handle'],
                    'title' => $item['title'],
                    'product_title' => $item['product_title'],
                    'variant_title' => (string)$item['variant_title'],
                    'category' => $item['product_type'],
                    'image' => str_replace($imageExe, $imageRep, $item['image']),
                    'product_quantity' => $item['quantity'],
                    'unformattedunitprice' => format_number($retailer['product'][$variantId]['unformatedunitprice']),
                    'discount' => format_number($retailer['product'][$variantId]['unformatedprice'] - $retailer['product'][$variantId]['unformatedpricediscount']),
                    // This is formatted in spite of the name
                    'price' => $retailer['product'][$variantId]['price'],
                ];
            },
            array_filter($cart['items'], function($item) use ($retailer) {
                return array_key_exists($item['variant_id'], $retailer['product']);
            })
        );
        $start_time = $this->_logDeltaTime($start_time, 'Extract SuccessDetails fields');

        $brand = $this->User->find('first', [
            'recursive' => -1,
            'conditions' => ['uuid' => $token],
            'fields' => ['id', 'email_address', 'google_conversion_id', 'google_conversion_label', 'country_id'],
        ]);
        $brandContent = $this->User->findEcommerceCustomContent($brand['User']['id'], $brand['User']['email_address']);

        $start_time = $this->_logDeltaTime($start_time, 'Fetch brandContent');

        $customer_state = $this->State->findWithCountryName($order['shipping_statecode']);

        $response = array(
            'customerEmail' => $order['customerEmail'],
            'shippingMethod' => $shippingMethod,
            'shipping_method_ship_subtype' => $shipping_method_ship_subtype,
            'orderNo' => $orderNo,
            'code' => Hash::get($order, 'code'),
            'geopoints' => json_decode($successdetails['SuccessDetails']['geopoints']),
            'retailer_id' => $retailer['retailer_id'] ?? $retailer['id'],
            'formatAddress' => $retailer['formatAddress'],
            'customer_address' => array(
                // Field names based on the OrderAddress model
                'first_name' => $order['billing_firstname'],
                'last_name' => $order['billing_lastname'],
                'address1' => $order['shipping_address1'],
                'address2' => $order['shipping_address2'],
                'city' => $order['shipping_city'],
                'country_code' => $customer_state['State']['country_code'],
                'country_name' => $customer_state['State']['country_name'],
                'state_code' => $customer_state['State']['state_code'],
                'state_name' => $customer_state['State']['state_name'],
                'zipcode' => $order['shipping_zipcode'],
                'telephone' => $order['shipping_telephone'],
                'latitude' => $order['latitude'],
                'longitude' => $order['longitude'],
            ),
            'price' => $retailer['totalproductamountdiscount'] ?? $retailer['totalproductamount'],
            'priceformat' => $retailer['priceformatdiscount'] ?? $retailer['priceformat'],
            'shippingAmount' => $retailer['shippingAmountDiscounted'],
            'shippingAmountFormat' => $retailer['shippingAmountDiscountedFormat'],
            'discount_code' => $order['discount_code'] ?? '',
            'discount' => $retailer['discountAmount'],
            'discountformat' => $this->Currency->formatCurrency($retailer['discountAmount'], $currency),
            'tax_included' => !empty($retailer['tax_included']) ? '(Inc.) ' : ' ',
            'taxamtwithShipping' => $retailer['taxamtwithShippingDiscount'] ?? Hash::get($retailer, 'taxamtwithShipping'),
            'taxformatwithShipping' => $retailer['taxformatwithShippingDiscount'] ?? Hash::get($retailer, 'taxformatwithShipping'),
            'totalwithshipping' => $retailer['totalwithshippingdiscount'] ?? Hash::get($retailer, 'totalwithshipping'),
            'totalwithshippingformat' => $retailer['totalwithshippingformatdiscount'] ?? Hash::get($retailer, 'totalwithshippingformat'),
            'currency' => $successdetails['SuccessDetails']['currency'],
            'type' => $type,
            'redirectUrl' => $successdetails['SuccessDetails']['redirect_url'],
            'order_data' => $order_data,
            'returnPolicy' => $successdetails['SuccessDetails']['returnPolicy'],
            'store_timing' => str_replace('class="tooltip"', '', (string)Hash::get((array)json_decode($retailer['store_timing'], true), 'currentTime')),
            'timing_list' => Hash::get((array)json_decode($retailer['store_timing'], true), 'timinglist'),
            'trackingId' => $successdetails['SuccessDetails']['trackingId'],
            'company_name' => $retailer['company_name'],
            'retailer_geopoints' => ['latitude' => $retailer['latitude'], 'longitude' => $retailer['longitude']],
            'google_conversion_id' => $brand['User']['google_conversion_id'],
            'google_conversion_label' => $brand['User']['google_conversion_label'],
            'brand_country_code' => $this->Country->getCountryCode($brand['User']['country_id']),
        );
        $start_time = $this->_logDeltaTime($start_time, 'Assign common response fields');

        if ($shippingMethod == 'selldirect_selldirect') {
            $response = array_merge($response, array(
                'brandsuccessmessage' => $brandContent['ship_to_door_success_message'],
                'shippingMethod_message' => $brandContent['ship_to_door_title'],
            ));
            if ($retailer['sellExclusiveOrder'] == 1) {
                $response = array_merge($response, array(
                    'priceformat' => isset($retailer['totalproductamountdiscount'])
                        ? $this->Currency->formatCurrency($retailer['totalproductamountdiscount'], $currency)
                        : $this->Currency->formatCurrency($retailer['totalproductamount'], $currency),
                    'taxformatwithShipping' => isset($retailer['taxamtwithShippingDiscount'])
                        ? $this->Currency->formatCurrency($retailer['taxamtwithShippingDiscount'], $currency)
                        : $this->Currency->formatCurrency($retailer['taxamtwithShipping'], $currency),
                    'totalwithshippingformat' => isset($retailer['totalwithshippingdiscount'])
                        ? $this->Currency->formatCurrency($retailer['totalwithshippingdiscount'], $currency)
                        : $this->Currency->formatCurrency($retailer['totalwithshipping'], $currency),
                ));
                $start_time = $this->_logDeltaTime($start_time, 'Assign sellExclusiveOrder response fields');
            }
            $start_time = $this->_logDeltaTime($start_time, 'selldirect_selldirect response');
        } elseif ($shippingMethod == 'shipearly_shipearly') {
            $response = array_merge($response, array(
                'brandsuccessmessage' => $brandContent['instore_pickup_success_message'],
                'shippingMethod_message' => $brandContent['instore_pickup_title'],
                'taxamtwithShipping' => $retailer['taxamtdiscount'] ?? $retailer['taxamt'],
                'taxformatwithShipping' => $retailer['taxformatdiscount'] ?? $retailer['taxformat'],
                'totalwithshipping' => $retailer['totalamountdiscount'] ?? $retailer['totalamount'],
                'totalwithshippingformat' => $retailer['totalformatdiscount'] ?? $retailer['totalformat'],
            ));
            $start_time = $this->_logDeltaTime($start_time, 'Assign instore response fields');

            if ($shipping_method_ship_subtype === 'localinstall') {
                $response = array_merge($response, array(
                    'shippingMethod_message' => $brandContent['local_install_title'],
                    'shippingAmount' => $retailer['shippingAmount_localinstall'],
                    'shippingAmountFormat' => $retailer['shippingAmountFormat_localinstall'],
                    'taxamtwithShipping' => $retailer['taxamtdiscount_localinstall'] ?? $retailer['taxamt_localinstall'],
                    'taxformatwithShipping' => $retailer['taxformatdiscount_localinstall'] ?? $retailer['taxformat_localinstall'],
                    'totalwithshipping' => $retailer['totalamountdiscount_localinstall'] ?? $retailer['totalamount_localinstall'],
                    'totalwithshippingformat' => $retailer['totalformatdiscount_localinstall'] ?? $retailer['totalformat_localinstall'],
                ));
                $start_time = $this->_logDeltaTime($start_time, 'Assign local_install response fields');
            } elseif ($shipping_method_ship_subtype === 'localdelivery') {
                $response = array_merge($response, array(
                    'brandsuccessmessage' => $brandContent['local_delivery_success_message'],
                    'shippingMethod_message' => $brandContent['local_delivery_title'],
                    'shippingAmount' => $retailer['shippingAmount_localdelivery'],
                    'shippingAmountFormat' => $retailer['shippingAmountFormat_localdelivery'],
                    'taxamtwithShipping' => $retailer['taxamtdiscount_localdelivery'] ?? $retailer['taxamt_localdelivery'],
                    'taxformatwithShipping' => $retailer['taxformatdiscount_localdelivery'] ?? $retailer['taxformat_localdelivery'],
                    'totalwithshipping' => $retailer['totalamountdiscount_localdelivery'] ?? $retailer['totalamount_localdelivery'],
                    'totalwithshippingformat' => $retailer['totalformatdiscount_localdelivery'] ?? $retailer['totalformat_localdelivery'],
                    'local_delivery_shipping_title' => $brandContent['local_delivery_shipping_title'],
                ));
                $start_time = $this->_logDeltaTime($start_time, 'Assign local_delivery response fields');
            }

            if ($response['shippingAmount'] <= 0) {
                $response['shippingAmountFormat'] = '-';
            }

            $response['deliveryTime'] = 'Available Now';
            if ($type === 'nonstock') {
                $backOrderStatus = $this->Product->exists([
                    'Product.user_id' => $brand['User']['id'],
                    'Product.productID' => array_filter(array_column($order_data, 'variant_id')),
                    'Product.brand_inventory <=' => 0,
                ]);
                $response['deliveryTime'] = ($backOrderStatus)
                    ? $brandContent['ship_to_store_backorder_message']
                    : $brandContent['ship_to_store_message'];
            }
            $start_time = $this->_logDeltaTime($start_time, 'shipearly_shipearly backorder message');
        } else { // if ($shippingMethod == 'shipfromstore_shipfromstore')
            $response = array_merge($response, array(
                'brandsuccessmessage' => $brandContent['ship_from_store_success_message'],
                'deliveryTime' => 'Available in 5-7 Business Days',
                'shippingMethod_message' => $brandContent['ship_from_store_title'],
            ));
            $start_time = $this->_logDeltaTime($start_time, 'shipfromstore_shipfromstore response');
        }

        $language = Configure::read('Config.language');
        if ($language && substr($language, 0, 2) !== 'en') {
            $response['translations'] = $this->_getOrderSuccessTranslations();
        }

        //TODO format these upstream instead
        $analyticsPriceFields = [
            'price',
            'shippingAmount',
            'taxamtwithShipping',
            'totalwithshipping',
        ];
        foreach ($analyticsPriceFields as $field) {
            $response[$field] = format_number($response[$field]);
        }

        $this->_webServiceLog(['response' => $response]);
        $this->_logDeltaTime($start_time_function, __METHOD__);
        $this->response->body(json_encode($response));
        return $this->response;
    }

    protected function _getOrderSuccessTranslations(): array
    {
        // Terms to translate from app/Locale/success.pot
        return [
            //#: Plugin/Shopify/View/templates/page.success.liquid.ctp:96
            'Order Confirmation' => __('Order Confirmation'),
            //#: Plugin/Shopify/View/templates/page.success.liquid.ctp:97
            'Order No' => __('Order No'),
            //#: Plugin/Shopify/View/templates/page.success.liquid.ctp:103
            'Your order has been received' => __('Your order has been received'),
            //#: Plugin/Shopify/View/templates/page.success.liquid.ctp:113
            'Verification Code' => __('Verification Code'),
            //#: Plugin/Shopify/View/templates/page.success.liquid.ctp:121
            'Your estimated delivery date is' => __('Your estimated delivery date is'),
            //#: Plugin/Shopify/View/templates/page.success.liquid.ctp:123
            'Your delivery method is' => __('Your delivery method is'),
            //#: Plugin/Shopify/View/templates/page.success.liquid.ctp:125
            'Store Hours' => __('Store Hours'),
            //#: Plugin/Shopify/View/templates/page.success.liquid.ctp:128
            'Your Order is Being Fulfilled by' => __('Your Order is Being Fulfilled by'),
            //#: Plugin/Shopify/View/templates/page.success.liquid.ctp:135
            'Order Details' => __('Order Details'),
            //#: Plugin/Shopify/View/templates/page.success.liquid.ctp:169
            'Discount' => __('Discount'),
            //#: Plugin/Shopify/View/templates/page.success.liquid.ctp:172
            'Subtotal' => __('Subtotal'),
            //#: Plugin/Shopify/View/templates/page.success.liquid.ctp:175;396
            'Shipping' => __('Shipping'),
            //#: Plugin/Shopify/View/templates/page.success.liquid.ctp:178
            'Taxes' => __('Taxes'),
            //#: Plugin/Shopify/View/templates/page.success.liquid.ctp:203
            'Total' => __('Total'),
            //#: Plugin/Shopify/View/templates/page.success.liquid.ctp:218
            'Return Policy' => __('Return Policy'),
            //#: Plugin/Shopify/View/templates/page.success.liquid.ctp:234
            'Store Directions' => __('Store Directions'),
            //#: Plugin/Shopify/View/templates/page.success.liquid.ctp:241
            'Total Distance' => __('Total Distance'),
            //#: Plugin/Shopify/View/templates/page.success.liquid.ctp:250
            'Continue Shopping' => __('Continue Shopping'),
            //#: Plugin/Shopify/View/templates/page.success.liquid.ctp:392
            'Installation' => __('Installation'),
        ];
    }

    public function displaySuccess()
    {
        $this->layout = 'success';
        $orderNo = $this->Session->read('Shopify.order.orderID');
        if(!empty($orderNo)) {
            $order = $this->Session->read('Shopify.order');
            if(!isset($order['code'])) {
                $order['code'] = '';
            }
            $shippingMethod = $this->Session->read('Shopify.shippingMethod');
            $sellExclusiveResult = $this->Session->read('Shopify.sellExclusiveResult');
            if (!empty($sellExclusiveResult)) {
                $this->set('sellExclusiveResult', json_encode($sellExclusiveResult));
            }
            $this->set('trackingId', $this->Session->read('Shopify.trackingId'));
            $cart = $this->ShopifyCarts->get();
            if ($shippingMethod == 'selldirect_selldirect') {
                $type = $order['subType'];
                $message = (array) $this->Session->read('Shopify.messages');
                $returnPolicy = $message['return-policy'];
                // $brandinstoresuccess = $message['brandinstoresuccess'];
                // $brandshipfromstoresuccess = $message['brandshipfromstoresuccess'];
                $brandselldirectsuccess =  $message['brandselldirectsuccess'];
                $retailer = $this->Session->read('Shopify.log');
                $this->set('geopoints', $this->Session->read('Shopify.geopoints'));
                $this->set('storepoint', (object) $retailer);
                $this->set('returnPolicy', $returnPolicy);
                // $this->set('brandinstoresuccess', $brandinstoresuccess);
                // $this->set('brandshipfromstoresuccess', $brandshipfromstoresuccess);
                $this->set('brandselldirectsuccess', $brandselldirectsuccess);
                $this->set('orderNo', $orderNo);
                $this->set('code', $order['code']);
                $this->set('shippingMethod', $shippingMethod);
                $this->set('items', $cart['items']);
                $this->set('cart', $cart);
                $this->set('currency', $order['currency_code']);
                $this->set('type', $type);
                $this->set('redirectUrl', $this->Session->read('Shopify.domainUrl'));
                $this->Session->destroy();
            } else {
                $type = $order['subType'];
                $message = (array) $this->Session->read('Shopify.messages');
                $returnPolicy = $message['return-policy'];
                $brandinstoresuccess = $message['brandinstoresuccess'];
                $brandshipfromstoresuccess = $message['brandshipfromstoresuccess'];

                $retailer = $this->Session->read('Shopify.log');
                $this->set('geopoints', $this->Session->read('Shopify.geopoints'));
                $this->set('mapOption', $this->Session->read('Shopify.brand_mapoption'));
                $this->set('storepoint', (object) $retailer);
                $this->set('returnPolicy', $returnPolicy);
                $this->set('brandinstoresuccess', $brandinstoresuccess);
                $this->set('brandshipfromstoresuccess', $brandshipfromstoresuccess);
                $this->set('orderNo', $orderNo);
                $this->set('code', $order['code']);
                $this->set('shippingMethod', $shippingMethod);
                $this->set('items', $cart['items']);
                $this->set('cart', $cart);
                $this->set('currency', $order['currency_code']);
                $this->set('type', $type);
                $this->set('redirectUrl', $this->Session->read('Shopify.domainUrl'));
                $this->Session->destroy();
            }
        } else {
            $this->error();
        }
    }

    protected function _saveSystemErrorMessage()
    {
        return $this->_saveErrorMessage(
            __('System Error'),
            __('Internal Error please try again')
        );
    }

    /**
     * @param int $brandId
     * @param bool $brandIsInventoryProvider
     * @return bool True if an Out-of-Stock error occurred and was saved
     */
    private function _saveMessageIfOutOfStockError(int $brandId, bool $brandIsInventoryProvider): bool
    {
        $items = (array)$this->ShopifyCarts->getValue('items');

        $quantityByProductID = array_column($items, 'quantity', 'variant_id');
        $oversoldInventoryByProductID = $this->Product->listOversoldInventoryByProductID($brandId, $quantityByProductID, !$brandIsInventoryProvider);
        if (!$oversoldInventoryByProductID) {
            return false;
        }

        foreach (array_keys($items) as $idx) {
            $items[$idx]['is_oversold'] = isset($oversoldInventoryByProductID[$items[$idx]['variant_id']]);
        }

        return (bool)$this->_saveErrorMessage(
            __('Out of Stock'),
            $this->_renderOutOfStockError($items)
        );
    }

    protected function _renderOutOfStockError(array $items): string
    {
        App::uses('AppView', 'View');
        $view = new AppView($this);
        $view->set(compact('items'));

        return (string)$view->render('Shopify./Elements/ErrorDetails/out_of_stock_error', false);
    }

    public function _saveErrorMessage($title, $message)
    {
        $uuid = CakeText::uuid();
        $this->Session->write('error.code', $uuid);
        $this->Session->write('error.title', $title);
        $this->Session->write('error.message', $message);

        //Save Error Message
        $data = array(
            'uuid' => $uuid,
            'title' => $title,
            'message' => $message,
            'baseurl' => $this->Session->read('Shopify.domainUrl'),
        );

        CakeLog::debug(json_encode(['ErrorDetails' => $data]));
        return $this->ErrorDetails->save($data);
    }

    public function getErrorDetails()
    {
        $this->autoRender = false;

        $this->response->cors($this->request, '*', '*', 'X-Requested-With');

        $this->_webServiceLog(['query' => $this->request->query]);
        $uuid = $this->request->query['id'];

        $errordetails = $this->ErrorDetails->findErrorPage($uuid);
        $this->_webServiceLog($errordetails);

        $response = array();
        if (!empty($errordetails)) {
            $response = array(
                'title' => $errordetails['ErrorDetails']['title'],
                'message' => $errordetails['ErrorDetails']['message'],
                'baseurl' => $errordetails['ErrorDetails']['baseurl'],
                'basepath' => BASE_PATH,
            );
        }

        $this->_webServiceLog(['response' => $response]);
        $this->response->body(json_encode($response));
    }

    public function displayError()
    {
        $this->layout = 'success';
        $title = $this->Session->read('error.title');
        $message = $this->Session->read('error.message');
        $domainUrl = $this->Session->read('Shopify.domainUrl');
        if(!empty($title) || !empty($message)) {
            $this->set('title', $title);
            $this->set('message', $message);
            $this->Session->write('error', '');
            $this->set('baseurl', $domainUrl);
        } else {
            $this->layout = 'redirect';
            $this->set('url', $domainUrl . '/cart');
        }
    }

    public function get_states(): ?CakeResponse
    {
        $userId = (int)$this->Session->read('Shopify.User.User.id');
        if (empty($userId)) {
            $message = __('Your session has expired');
            $redirectUrl = ['plugin' => 'Shopify', 'controller' => 'Shopify', 'action' => 'editPage'];

            return $this->_exceptionResponse(new UnauthorizedException($message), $message, false, $redirectUrl);
        }

        $countryId = (int)$this->request->data('country_id');
        $states = ($this->request->data('is_shipping'))
            ? $this->State->findAllShippingStatesById($userId, $countryId)
            : $this->State->findAllBillingStatesById($countryId);

        $response = [
            'states' => array_values($states),
        ];
        $this->set($response);
        $this->set('_serialize', array_keys($response));

        return null;
    }

    public function state_id_from_codes()
    {
        $countryCode = (string)$this->request->query('country_code');
        $stateCode = (string)$this->request->query('state_code');

        // Payment request country and region are usually codes, but it is not guaranteed.
        $state = $this->State->findStateAndCountryByVagueName($countryCode, $stateCode);
        if (empty($state['State']['id'])) {
            throw new NotFoundException('State not found for ' . json_encode($this->request->query));
        }

        $this->set(['country_id' => $state['Country']['id'], 'state_id' => $state['State']['id']]);
        $this->set('_serialize', ['country_id', 'state_id']);
    }

    protected function _saveSuccessDetails(): array
    {
        $start_time_function = microtime(true);

        $order = $this->Session->read('Shopify.order');

        $orderNO = ltrim($order['orderID'], '#');

        $this->Order->trackFbpixelPurchaseEvent($order['id'], $orderNO);

        $SuccessDetails = array(
            'user_token' => $this->Session->read('Shopify.token'),
            'orderID' => $orderNO,
            'orderData' => json_encode($order),
            'trackingId' => (string)$this->Session->read('Shopify.trackingId'),
            'returnPolicy' => (string)$this->Session->read('Shopify.brandPolicies.return_policy'),
            'retailer' => json_encode($this->Session->read('Shopify.log')),
            'geopoints' => json_encode($this->Session->read('Shopify.geopoints')),
            'shippingMethod' => $this->Session->read('Shopify.shippingMethod'),
            'cart' => json_encode($this->ShopifyCarts->get()),
            'currency' => $order['currency_code'],
            'type' => $order['subType'],
            'redirect_url' => $this->Session->read('Shopify.domainUrl'),
        );
        CakeLog::debug(json_encode(compact('SuccessDetails')));
        $this->SuccessDetails->save($SuccessDetails);

        // Save Discount Usage
        $discountId = $this->Session->read('Shopify.discount.id');
        if (!empty($discountId)) {
            $this->DiscountUsage->saveCustomerUsage($order['user_id'], $discountId, $order['customerEmail']);
        }

        $this->_logDeltaTime($start_time_function, __METHOD__);

        return $SuccessDetails;
    }

    public function success()
    {
        $this->_setAnalyticsViewVars();
        $this->set('url', $this->Session->read('Shopify.domainUrl') . '/pages/orderProcessing');
        $this->set('orderID', ltrim($this->Session->read('Shopify.order.orderID'), '#'));

        $this->AccessTokenHandler->revokeAccess();
        $this->Session->delete('Shopify');
        $this->Session->renew();

        $this->render('success', 'redirect');
    }

    public function error()
    {
        $this->_setAnalyticsViewVars();
        $this->set('url', $this->Session->read('Shopify.domainUrl') . '/pages/OrderError');
        $this->set('orderID', $this->Session->read('error.code'));

        if ($this->Session->read('Shopify.Next') == 'result') {
            $this->Session->delete('Shopify.Next');
        }

        $this->render('success', 'redirect');
    }

    public function manuCheckout()
    {
        $this->_setAnalyticsViewVars();
        $this->set('url', $this->Session->read('Shopify.domainUrl') . '/checkout');
        $this->set('orderID', null);

        if($this->Session->read('Shopify.Next') == 'result') {
            $this->Session->write('Shopify.Next', '');
        }

        $this->render('success', 'redirect');
    }

    private function _setAnalyticsViewVars()
    {
        $this->set('trackingId', $this->Session->read('Shopify.trackingId'));
        $this->set('googleAdsConversionId', $this->Session->read('Shopify.googleAdsConversionId'));
        $this->set('gtmId', $this->Session->read('Shopify.gtmId'));
        $this->set('fbpixelId', $this->Session->read('Shopify.fbpixelId'));
        $this->set('domainUrl', $this->Session->read('Shopify.domainUrl'));
        $this->set('cart', $this->Session->read('Shopify.cart'));
    }

    public function preorders()
    {
        $this->request->allowMethod('post');

        $brandId = (int)$this->Session->read('Shopify.User.User.id');
        if (empty($brandId)) {
            return $this->_exceptionResponse(new UnauthorizedException(), 'Session has expired');
        }

        try {
            $shipping_method = $this->request->data['shipping_method'];
            $shipping_method_subtype = $this->request->data['shipping_method_ship_subtype'];

            $type = $this->_extractOrderType($shipping_method, $shipping_method_subtype);
            if (empty($type)) {
                throw new BadRequestException('Unknown shipping method ' . json_encode(compact('shipping_method', 'shipping_method_subtype')));
            }
            if ($type === 'velofix') {
                $shipping_method_subtype = 'velofix';
                $this->request->data['shipping_method_ship_subtype'] = $shipping_method_subtype;
            }

            $this->request->data = $this->_normalizeRequestRetailerId($type, $this->request->data);
            $retailerId = (int)$this->request->data['retailer_id'];

            $currentStoreAmount = $this->_extractSelectedStore($type, $retailerId);
            if (empty($currentStoreAmount)) {
                throw new BadRequestException('Retailer not found where ' . json_encode(compact('type', 'retailerId')));
            }

            $this->request->data['shippingAmount'] = $this->_getShippingAmount($currentStoreAmount, $shipping_method, $shipping_method_subtype);
            $this->request->data['shippingDiscount'] = round($currentStoreAmount['shippingDiscount'], 2);

            $this->request->data['shippingName'] = $currentStoreAmount['shippingName'] ?? $this->Session->read('Shopify.shippingName');
            $this->request->data['shippingBoxWeight'] = $currentStoreAmount['packageBoxWeight'] ?? $this->Session->read('Shopify.packageBoxWeight');
            unset($currentStoreAmount['shippingName'], $currentStoreAmount['packageBoxWeight']);
            if ($type === 'velofix') {
                $this->request->data['shippingName'] = 'Velofix Delivery';
            }

            $this->request->data['tax'] = $this->Session->read('Shopify.tax');
            $this->request->data['customerID'] = $this->Session->read('Shopify.customer');

            // The Stripe payment request widget does not send the complete shipping address until now
            $shippingAddressForm = array_merge($this->request->data['check']['shipping_address'] ?? [], $this->request->data['checkemailv']['shipping_address'] ?? []);
            if ($shippingAddressForm) {
                $shippingAddress = $this->_processAddressFormFields($shippingAddressForm);
                if (!$this->_validateAddress($shippingAddress)) {
                    throw new BadRequestException('Address validation failed where ' . json_encode(compact('shippingAddress')));
                }
                $this->Session->write('Shopify.shipping', $shippingAddress);
            }
            $shippingAddress = $this->Session->read('Shopify.shipping');
            $this->request->data['shipping'] = json_encode($shippingAddress);

            $this->request->data['Customer']['accepts_marketing'] = (bool)$this->Session->read('Shopify.accepts_marketing');

            $billingAddress = $shippingAddress;
            if ($this->request->data['Billing']['inlineRadioOptions1'] === 'newBilling') {
                $billingAddress = $this->_processAddressFormFields($this->request->data['check']['billing_address']);
                if (!$this->_validateAddress($billingAddress)) {
                    throw new BadRequestException('Address validation failed where ' . json_encode(compact('billingAddress')));
                }
            }
            $this->request->data['billing'] = json_encode($billingAddress);

            $discountInfo = (array)$this->Session->read('Shopify.discount');
            CakeLog::debug(json_encode(compact('discountInfo')));
            $this->request->data['discount'] = $discountInfo;

            $sellExclusiveId = !empty($currentStoreAmount['sellExclusive']['id']) ? (int)$currentStoreAmount['sellExclusive']['id'] : null;
            $shippingCountryId = (int)$shippingAddress['country'];
            $shippingStateId = (int)$shippingAddress['province'];

            $stripeRetailer = $this->StripeUser->getEcommerceStripePayee($brandId, $retailerId, $shippingCountryId, $shippingStateId);
            if (empty($stripeRetailer['StripeUser']['stripe_user_id'])) {
                throw new NotFoundException(sprintf('Stripe account not found where user_id=%s and retailer_id=%s', $brandId, $retailerId));
            }
            $stripeAccount = (string)$stripeRetailer['StripeUser']['stripe_user_id'];
            $stripeRetailerId = (int)$stripeRetailer['StripeUser']['user_id'];
            $paymentId = (string)$this->Session->read("Shopify.accountPaymentIds.{$stripeAccount}") ?: null;

            $sellExclusiveStripePayee = $this->StripeUser->getSellExclusiveStripePayee($sellExclusiveId, $type, $shippingCountryId, $shippingStateId);
            $sellExclusiveStripeAccount = (string)($sellExclusiveStripePayee['StripeUser']['stripe_user_id'] ?? '') ?: null;
            $sellExclusiveStripeRetailerId = (int)($sellExclusiveStripePayee['StripeUser']['user_id'] ?? 0) ?: null;
            $sellExclusivePaymentId = (string)$this->Session->read("Shopify.accountSellExclusivePaymentIds.{$sellExclusiveStripeAccount}") ?: null;

            $isCommissionRetailer = (bool)$stripeRetailer['ManufacturerRetailer']['is_commission_tier'];

            $brandIsInventoryProvider = ($retailerId === $brandId || $currentStoreAmount['type'] === 'nonstock' || $isCommissionRetailer);
            if ($this->_saveMessageIfOutOfStockError($brandId, $brandIsInventoryProvider)) {
                $response = ['redirect' => Router::url(['plugin' => 'Shopify', 'controller' => 'Shopify', 'action' => 'error'], true)];
                CakeLog::debug(json_encode(compact('response')));

                $this->set($response);
                $this->set('_serialize', array_keys($response));

                return (!$this->request->is('ajax')) ? $this->redirect($this->viewVars['redirect']) : null;
            }

            // Refresh duration to allow more time to address any payment errors
            $this->AccessTokenHandler->requestAccess();

            $preorder = $this->Preorder->createForEcommerce(
                PreorderType::SHOPIFY,
                $brandId,
                $retailerId,
                $this->Session->read('Shopify.StoreAssociate.User.id'),
                (string)$paymentId,
                $currentStoreAmount['currency'],
                $this->_extractRetailerTotalPrice($type, $currentStoreAmount),
                $this->_extractRetailerTotalPriceDiscounted($type, $currentStoreAmount),
                array_column($currentStoreAmount['product'], 'id'),
                array_merge($this->request->data, [
                    'log' => $currentStoreAmount,
                    'stripe_retailer_id' => $stripeRetailerId,
                    'stripe_account' => $stripeAccount,
                    'payment_intent' => $paymentId,
                    'sell_exclusive_stripe_retailer_id' => $sellExclusiveStripeRetailerId,
                    'sell_exclusive_stripe_account' => $sellExclusiveStripeAccount,
                    'sell_exclusive_payment_intent' => $sellExclusivePaymentId,
                ])
            );
            if (empty($preorder['id'])) {
                throw new BadRequestException(json_encode(['errors' => $this->Preorder->validationErrors, 'data' => $this->Preorder->data]));
            }
            CakeLog::debug(json_encode(compact('preorder')));

            $response = [
                'id' => $preorder['id'],
                'url' => Router::url(['plugin' => 'shopify', 'controller' => 'shopify', 'action' => 'placeOrder', $preorder['id']], true),
            ];
            CakeLog::debug(json_encode(compact('response')));

            $this->set($response);
            $this->set('_serialize', array_keys($response));

            return null;
        } catch (Exception $e) {
            return $this->_exceptionResponse(($e instanceof HttpException) ? $e : null, null, $e);
        }
    }

    public function placeOrder($preorderId = null)
    {
        $start_time_function = microtime(true);
        $start_time = $start_time_function;

        $this->autoRender = false;

        if (!$preorderId) {
            $this->request->allowMethod('post');

            // Direct call is cheaper than requestAction given that request data is the same
            $preorderResponse = $this->preorders();
            if ($preorderResponse instanceof CakeResponse) {
                return $preorderResponse;
            }
            if (isset($this->viewVars['redirect'])) {
                return $this->redirect($this->viewVars['redirect']);
            }
            $preorderId = $this->viewVars['id'];
            $this->viewVars = [];

            $start_time = $this->_logDeltaTime($start_time, 'create Preorder');
        }

        try {
            $token = $this->Session->read('Shopify.token');
            $user = (array)$this->_checkManUserToken($token);
            if (empty($user['User']['id'])) {
                throw new Exception('User not found for token: ' . $token);
            }
            $brandId = $user['User']['id'];

            $preorder = $this->Preorder->findForPlaceOrder($preorderId, $brandId, PreorderType::SHOPIFY);

            $this->request->data = json_decode($preorder['log'], true);
            if (!$this->request->data) {
                throw new NotFoundException('Preorder.log data not found where id=' . json_encode($preorderId));
            }

            $shipping_method = $this->request->data['shipping_method'];
            $discountInfo = $this->request->data['discount'];
        } catch (Exception $e) {
            CakeLog::error(strval($e));
            $this->_saveSystemErrorMessage();
            return $this->redirect('/shopifyerror');
        }

        // Refresh duration to allow more time to address any payment errors
        $this->AccessTokenHandler->requestAccess();

        $start_time = $this->_logDeltaTime($start_time, 'extract inputs');

        if ($shipping_method == 'selldirect_selldirect' && $preorder['totalpricediscount'] <= 0) {
            $response = $this->_placeFreeOrder($preorder, $user, $discountInfo);
        } else {
            $response = $this->_shipearlyPay($preorder, $user, $discountInfo);
        }

        $start_time = $this->_logDeltaTime($start_time, '_shipearlyPay');

        $redirectUrl = isset($response['redirectUrl']) ? $response['redirectUrl'] : '/shopifyerror';
        if ($redirectUrl === '/shopifysuccess') {
            $this->_saveSuccessDetails();
        }
        return $this->redirect($redirectUrl);
    }

    /**
     * @param string $shipping_method
     * @param string $shipping_method_subtype
     * @return string
     */
    private function _extractOrderType($shipping_method, $shipping_method_subtype)
    {
        $type = '';
        if ($shipping_method == 'shipearly_shipearly') {
            if ($shipping_method_subtype === 'localdelivery') {
                $type = 'local_delivery';
            } elseif ($shipping_method_subtype === 'localinstall') {
                $type = 'local_install';
            } else {
                $type = 'instore';
            }
        } elseif ($shipping_method == 'selldirect_selldirect') {
            if ($shipping_method_subtype == 'localdelivery') {
                $type = 'velofix';
            } else {
                $type = 'sellDirect';
            }
        } elseif ($shipping_method == 'shipfromstore_shipfromstore') {
            $type = 'shipFromStore';
        }
        return $type;
    }

    /**
     * @param string $type
     * @param array $data
     * @return array
     */
    private function _normalizeRequestRetailerId($type, array $data)
    {
        $typeToRetailerInputNames = array(
            'instore' => 'retailer_id',
            'local_install' => 'local_install_id',
            'local_delivery' => 'local_retailer_id',
            'shipFromStore' => 'store_id',
            'sellDirect' => 'sell_direct_brand_id',
            'sellExclusive' => 'sell_exclusive_brand_id',
            'velofix' => 'local_retailer_id',
        );
        $retailerId = $data[$typeToRetailerInputNames[$type]];
        foreach ($typeToRetailerInputNames as $inputName) {
            unset($data[$inputName]);
        }
        $data['retailer_id'] = $retailerId;
        return $data;
    }

    /**
     * @param string $type
     * @param int $retailerId
     * @return array
     */
    private function _extractSelectedStore($type, $retailerId)
    {
        $currentStoreAmount = (array)json_decode($this->Session->read("Shopify.retailerInfo.{$type}.{$retailerId}"), true);
        if (empty($currentStoreAmount)) {
            return [];
        }

        $sellExclusiveOrder = 0;
        $sellExclusive = json_decode(json_encode($this->Session->read('Shopify.sellExclusiveResult')), true);
        if (!empty($sellExclusive)) {
            if ($type === 'sellDirect') {
                $sellExclusiveOrder = 1;
                $currentStoreAmount = $this->_combineSellDirectWithSellExclusive($retailerId, $currentStoreAmount, $sellExclusive);
            } else {
                $currentStoreAmount['sellExclusive'] = $sellExclusive;
            }
        }
        $currentStoreAmount['sellExclusiveOrder'] = $sellExclusiveOrder;

        if ($type === 'local_delivery') {
            $currentStoreAmount['shipping_method_ship_subtype'] = 'localdelivery';
        }

        return $currentStoreAmount;
    }

    /**
     * @param int $brandId
     * @param array $currentStoreAmount
     * @param array $sellExclusive
     * @return array
     */
    private function _combineSellDirectWithSellExclusive($brandId, $currentStoreAmount, $sellExclusive)
    {
        $shippingAmount = $currentStoreAmount['shippingAmount'];
        $shippingDiscount = $currentStoreAmount['shippingDiscount'];

        $result = $this->_getSellDirect($brandId, 'sellDirect', $shippingAmount, $shippingDiscount, true);

        return (array)json_decode($result, true);
    }

    /**
     * @param string $type
     * @param array $retailerInfo
     * @return float|string
     */
    private function _extractRetailerTotalPrice(string $type, array $retailerInfo)
    {
        if ($type === 'instore') {
            $totalprice = $retailerInfo['totalamount'];
        } elseif ($type === 'local_install') {
            $totalprice = $retailerInfo['totalamount_localinstall'];
        } elseif ($type === 'local_delivery') {
            $totalprice = $retailerInfo['totalamount_localdelivery'];
        } else {
            $totalprice = $retailerInfo['totalwithshipping'];
        }

        return $totalprice;
    }

    /**
     * @param string $type
     * @param array $retailerInfo
     * @return float|string
     */
    private function _extractRetailerTotalPriceDiscounted(string $type, array $retailerInfo)
    {
        if ($type === 'instore') {
            $totalpricediscount = $retailerInfo['totalamountdiscount'] ?? $retailerInfo['totalamount'];
        } elseif ($type === 'local_install') {
            $totalpricediscount = $retailerInfo['totalamountdiscount_localinstall'] ?? $retailerInfo['totalamount_localinstall'];
        } elseif ($type === 'local_delivery') {
            $totalpricediscount = $retailerInfo['totalamountdiscount_localdelivery'] ?? $retailerInfo['totalamount_localdelivery'];
        } else {
            $totalpricediscount = $retailerInfo['totalwithshippingdiscount'] ?? $retailerInfo['totalwithshipping'];
        }

        return $totalpricediscount;
    }

    /**
     * @param array $currentStoreAmount
     * @param string $shipping_method
     * @param string $shipping_method_subtype
     * @return float
     */
    private function _getShippingAmount($currentStoreAmount, $shipping_method, $shipping_method_subtype)
    {
        $shippingAmount = round($currentStoreAmount['shippingAmount'], 2);
        if ($shipping_method === 'shipearly_shipearly') {
            if ($currentStoreAmount['type'] !== 'nonstock') {
                $shippingAmount = 0.0;
            }
            if ($shipping_method_subtype === 'localdelivery') {
                $shippingAmount = round($currentStoreAmount['shippingAmount_localdelivery'], 2);
            }
        }
        return $shippingAmount;
    }

    /**
     * @param array $preorder
     * @param array $user
     * @param array $discountInfo
     * @return array ['redirectUrl' => '{url}']
     * @throws Exception
     */
    protected function _placeFreeOrder(array $preorder, array $user, array $discountInfo): array
    {
        $lastid = $preorder['id'];

        try {
            $this->Preorder->markPaymentSuccess($lastid);

            if ($this->AbandonCart->claimIfExists($this->Session->read('Shopify.abandon_cart_id'), $lastid)) {
                $this->_webServiceLog("Claiming abandoned cart " . json_encode($this->AbandonCart->read()));
            }

            // It is impossible to capture the order so always mark as captured
            $user['User']['sell_direct_authorize'] = false;

            $this->_paymentSuccess($user, ['Preorder' => $preorder], $discountInfo, '', '');

            return ['redirectUrl' => '/shopifysuccess'];
        } catch (Exception $e) {
            CakeLog::error($e);
            $this->Preorder->markOrderError($lastid);
            $this->_saveSystemErrorMessage();

            return ['redirectUrl' => '/shopifyerror'];
        }
    }

    /**
     * @param array $preorder
     * @param array $user
     * @param array $discountInfo
     * @return array ['redirectUrl' => '{url}']
     * @throws Exception
     */
    protected function _shipearlyPay(array $preorder, array $user, array $discountInfo): array
    {
        $start_time_function = microtime(true);
        $start_time = $start_time_function;

        $message = (array)$this->Session->read('Shopify.messages');

        $brandId = (int)$user['User']['id'];
        $lastid = (int)$preorder['id'];

        $log = (array)json_decode($preorder['log'], true);

        try {
            $stripe_account = (string)$log['stripe_account'];
            $payment = $this->Stripe->confirmPaymentIfNotConfirmed($stripe_account, (string)$log['payment_intent']);
            $sellExclusivePayment = !empty($log['sell_exclusive_stripe_account'])
                ? $this->Stripe->confirmPaymentIfNotConfirmed((string)$log['sell_exclusive_stripe_account'], (string)$log['sell_exclusive_payment_intent'])
                : null;

            try {
                $stripeRetailerId = (int)($log['stripe_retailer_id'] ?? $brandId);
                $shipping = (array)json_decode($log['shipping'], true);
                $billing = (array)json_decode($log['billing'], true);

                $this->Stripe->updatePaymentCustomerWithBilling($payment, $stripeRetailerId, $stripe_account, $billing, $shipping);
            } catch (Exception $e) {
                CakeLog::warning($e);
            }

            $paymentDetails = $this->Stripe->getPaymentMethodDetails($payment, $stripe_account);

            $this->Preorder->markPaymentSuccess($lastid, [
                'paykey' => $paymentDetails->transactionID,
                'card_type' => $paymentDetails->card_type,
                'last_four_digit' => $paymentDetails->last_four_digit,
                'fraud_check_address' => $paymentDetails->fraud_check_address,
                'fraud_check_postal_code' => $paymentDetails->fraud_check_postal_code,
                'fraud_check_cvc' => $paymentDetails->fraud_check_cvc,
            ]);

            $start_time = $this->_logDeltaTime($start_time, 'Preorder::payment_success');
        } catch (Exception $e) {
            if ($e instanceof \Stripe\Exception\ApiErrorException) {
                CakeLog::error('[' . get_class($e) . '] ' . json_encode($e->getJsonBody()));
            }
            CakeLog::error($e);

            $errorMessage = $message['payment_error'];
            if ($e instanceof \Stripe\Exception\CardException) {
                $errorMessage = $message[$e->getError()->code] ?? $e->getMessage();
                if ($e->getError()->decline_code === \Stripe\Charge::DECLINED_FRAUDULENT) {
                    $errorMessage = $message['brandfraudmessage'];
                }
            }

            $this->Preorder->markPaymentError($lastid);
            $this->_saveErrorMessage(__('Payment Error'), $errorMessage);

            return ['redirectUrl' => '/shopifyerror'];
        }

        try {
            if ($this->AbandonCart->claimIfExists($this->Session->read('Shopify.abandon_cart_id'), $lastid)) {
                CakeLog::debug('Claiming abandoned cart ' . json_encode($this->AbandonCart->read()));
            }

            $start_time = $this->_logDeltaTime($start_time, 'AbandonCart::claimIfExists');
        } catch (Exception $e) {
            CakeLog::error(strval($e));
        }

        try {
            $Preorder = $this->Preorder->findForPaymentSuccess($lastid);
            $sellExclusiveTransactionId = isset($sellExclusivePayment->id) ? $sellExclusivePayment->id : '';
            $this->_paymentSuccess($user, $Preorder, $discountInfo, $payment->id, $sellExclusiveTransactionId);

            $start_time = $this->_logDeltaTime($start_time, '_paymentSuccess');

            return array('redirectUrl' => '/shopifysuccess');
        } catch (Exception $e) {
            CakeLog::error(strval($e));
            $this->Preorder->markOrderError($lastid);
            return array('redirectUrl' => '/shopifyerror');
        }
    }

    /**
     * @param array $user
     * @param array $Preorder
     * @param array $discountInfo
     * @param string $TransactionID
     * @param string $sellExclusiveTransactionId
     * @return bool
     * @throws Exception
     */
    public function _paymentSuccess(array $user, array $Preorder, array $discountInfo, string $TransactionID, string $sellExclusiveTransactionId): bool
    {
        $start_time_function = microtime(true);
        $start_time = $start_time_function;

        $preorder = $Preorder['Preorder'];

        $logs = json_decode($preorder['log'], true);
        $this->Session->write('Shopify.shippingMethod', $logs['shipping_method']);

        $formShipping = json_decode($logs['shipping'], true);
        $formBilling = json_decode($logs['billing'], true);

        // Grab these values before $logs is overwritten
        $hasSellExclusive = !empty($logs['log']['sellExclusive']);

        if (isset($logs['shipping_method_ship_subtype'])) {
            $logs['log']['shipping_method_ship_subtype'] = $logs['shipping_method_ship_subtype'];
        }
        $this->Session->write('Shopify.log', $logs['log']);
        if ($hasSellExclusive) {
            $logs['log'] = $logs['log']['sellExclusive'];
        }
        $products = $logs['log'];

        $userId = $user['User']['id'];
        // Prevent a debug error caused by the retail order format not setting 'id'
        $retailerId = Hash::get($products, 'id');

        $distributorId = null;
        $stripeRetailerId = (int)($logs['stripe_retailer_id'] ?? 0);
        if ($this->User->isUserType($stripeRetailerId, User::TYPE_SALES_REP)) {
            $distributorId = $stripeRetailerId;
        }

        $start_time = $this->_logDeltaTime($start_time, 'process inputs');

        $currencyConversion = 1;
        $retailerCurrencyCode = $this->User->field('currency_code', ['id' => $retailerId]);
        if ($preorder['currency_code'] != $retailerCurrencyCode) {
            $currencyConversion = $this->currencyConversion(1, $preorder['currency_code'], $retailerCurrencyCode);

            $start_time = $this->_logDeltaTime($start_time, 'currencyConversion');
        }

        $cusId = $this->_addCustomerById($logs, $user['User'], $formShipping);

        $orderinfo = array(
            'subType' => OrderType::SELL_DIRECT,
            'user_id' => $userId,
            'preOrderId' => $preorder['id'],
            'customerID' => $cusId,
            'retailer_id' => $retailerId,
            'distributor_id' => $distributorId,
            'tax_included' => $products['tax_included'],
            'order_status' => OrderStatus::UNFULFILLED,
            'order_type' => OrderType::SELL_DIRECT,
            'payment_method' => OrderPaymentMethod::STRIPE,
            'payment_method_subtype' => $logs['Order']['payment_method_subtype'],
            'payment_status' => ($user['User']['sell_direct_authorize']) ? OrderPaymentStatus::AUTHORIZED : OrderPaymentStatus::PAID,
            'created_at' => $preorder['created'],
            'total_price' => $preorder['totalprice'],
            'totalPriceConversion' => $currencyConversion * $preorder['totalprice'],
            'currency_code' => $preorder['currency_code'],
            'shipping_amount' => $logs['shippingAmount'],
            'transactionID' => $TransactionID,
            'stripe_account' => $logs['stripe_account'],
            'shipearlyFees' => $this->Currency->formatAsDecimal(
                $this->OrderLogic->CalculateFees($preorder['totalpricediscount'], 'brand', $user['User']['brand_revenue_model'], $user['User']['brand_direct_default_amount'], $user['User']['brand_revenue_maximum']),
                $preorder['currency_code']
            ),
            'total_tax' => $products['taxamtdiscount'] ?? $products['taxamt'],
            'card_type' => $preorder['card_type'],
            'last_four_digit' => $preorder['last_four_digit'],
            'fraud_check_cvc' => $preorder['fraud_check_cvc'],
            'fraud_check_address' => $preorder['fraud_check_address'],
            'fraud_check_postal_code' => $preorder['fraud_check_postal_code'],
        );
        if (!empty($preorder['store_associate_id'])) {
            $orderinfo['store_associate_id'] = $preorder['store_associate_id'];
            $orderinfo['shipearly_commission_fee'] = $user['User']['store_associate_default_amount'];
            $orderinfo['shipearlyFees'] = 0;
        }
        $orderinfo['retailerAmount'] = $this->Currency->formatAsDecimal($orderinfo['total_price'] - $orderinfo['shipearlyFees'], $preorder['currency_code']);

        $start_time = $this->_logDeltaTime($start_time, 'build order data');

        $orderinfo += $this->_getOrderAddressFields($formShipping);

        $start_time = $this->_logDeltaTime($start_time, '_getOrderAddressFields');

        $orderinfo = array_merge($orderinfo, $this->_extractOrderDiscountFields($discountInfo, $products, $preorder['totalpricediscount']));
        $this->_logIfUnexpectedOrderDiscount($orderinfo, $products, $discountInfo);

        if ($hasSellExclusive) {
            $distributorId = null;
            $stripeRetailerId = (int)($logs['sell_exclusive_stripe_retailer_id'] ?? 0);
            if ($this->User->isUserType($stripeRetailerId, User::TYPE_SALES_REP)) {
                $distributorId = $stripeRetailerId;
            }

            $orderinfo = array_merge($orderinfo, [
                'distributor_id' => $distributorId,
                'transactionID' => $sellExclusiveTransactionId,
                'stripe_account' => $logs['sell_exclusive_stripe_account'],
                'total_price' => ($products['totalwithshippingdiscount'] ?? $products['totalwithshipping']) + ($orderinfo['total_discount'] ?? 0.00),
                'shipping_amount' => $products['shippingAmount'],
            ]);
            if (!$this->Order->createFromShopifyDirectPreorder($orderinfo, $formBilling, $products, (float)$currencyConversion)) {
                throw new InternalErrorException(json_encode(['errors' => $this->Order->validationErrors, 'data' => $this->Order->data]));
            }
            $newOrderId = (int)$this->Order->id;

            $this->_createEcommerceConsumerOrder($newOrderId);
            $this->_afterShopifyApiOrder($newOrderId);

            $start_time = $this->_logDeltaTime($start_time, 'create sellExclusive Order record');
        }
        if ($logs['shipping_method'] == 'selldirect_selldirect') {
            if (!$this->Order->createFromShopifyDirectPreorder($orderinfo, $formBilling, $products, (float)$currencyConversion)) {
                throw new InternalErrorException(json_encode(['errors' => $this->Order->validationErrors, 'data' => $this->Order->data]));
            }
            $newOrderId = (int)$this->Order->id;

            $this->_createEcommerceConsumerOrder($newOrderId);
            $orderinfo['orderID'] = $this->_afterShopifyApiOrder($newOrderId);
            $orderinfo = ['id' => $newOrderId] + $orderinfo;
            $this->Session->write('Shopify.order', $orderinfo);
            $this->Session->write('Shopify.shippingMethod', $logs['shipping_method']);
            $this->Session->write('Shopify.log', $logs['log']);

            $start_time = $this->_logDeltaTime($start_time, 'create direct Order record');
        } else {
            $orderId = json_decode($this->_directorder($Preorder['Preorder'], $user['User'], $discountInfo, $TransactionID), true); //$paydetails['PaymentInfo']['TransactionID']
            $this->Preorder->markSuccess($Preorder['Preorder']['id']);

            $start_time = $this->_logDeltaTime($start_time, '_directorder');
        }

        return true;
    }

    private function _afterShopifyApiOrder(int $newOrderId): string
    {
        $this->OrderLogic->updateChargeForNewOrder($newOrderId);
        try {
            $this->NotificationLogic->handleSellDirectNotifications($newOrderId);
        } catch (Exception $e) {
            CakeLog::error($e);
        }

        $newOrder = $this->Order->get($newOrderId, [
            'fields' => [
                'id',
                'preOrderId',
                'orderID',
                'orderNO',
                'source_order_name',
            ],
        ]);
        $this->Preorder->markSuccess($newOrder['Order']['preOrderId'], ['order_id' => $newOrder['Order']['orderNO']]);

        return (string)($this->Order->getSourceOrderName($newOrder) ?: $newOrder['Order']['orderID']);
    }

    /**
     * @param array $logs
     * @param array $user
     * @param array $shipping
     * @return int|null
     */
    public function _addCustomerById($logs, $user, $shipping): ?int
    {
        $userId = (int)$user['id'];
        $shipping = (array)$shipping;

        $cusId = null;
        if (!empty($logs['customerID'])) {
            try {
                $customer = (array)$this->Shopify->getShopifyCustomer($logs['customerID'], $user['api_key'], $user['secret_key'], rtrim($user['shop_url'], "/"));
                if (!empty($customer['id'])) {
                    $cusId = $this->Customer->syncShopifyCustomer($userId, $customer);
                }
            } catch (Exception $e) {
                CakeLog::warning($e);
            }
        }
        if (empty($cusId)) {
            $cusId = $this->Customer->addEcommerceCustomerByEmail($userId, $shipping);
        }

        if (!empty($cusId)) {
            $this->Customer->save([
                'id' => $cusId,
                'accepts_marketing' => $logs['Customer']['accepts_marketing'],
                'preferred_language' => $logs['preferred_language']
            ]);
        }

        return $cusId;
    }

    /**
     * @param array $preorder
     * @param array $user
     * @param array $discountInfo
     * @param string $transactionID
     * @return false|string
     * @throws Exception
     */
    public function _directorder(array $preorder, array $user, array $discountInfo, string $transactionID)
    {
        $start_time_function = microtime(true);
        $start_time = $start_time_function;

        $logs = json_decode($preorder['log'], true);
        $shipping = json_decode($logs['shipping'], true);
        $billing = json_decode($logs['billing'], true);

        $products = $logs['log'];
        $shippingMethod = $logs['shipping_method'];
        $shipping_method_subtype = (string)Hash::get($logs, 'shipping_method_ship_subtype');

        $products['shipping_method_ship_subtype'] = $shipping_method_subtype;

        $this->Session->write('Shopify.log', $products);
        $this->Session->write('Shopify.shippingMethod', $shippingMethod);

        $retailer_id = $products['retailer_id'];

        $start_time = $this->_logDeltaTime($start_time, 'process inputs');

        $cusId = $this->_addCustomerById($logs, $user, $shipping);

        $start_time = $this->_logDeltaTime($start_time, '_addCustomerById');

        $c_retailer = $this->User->findForEcommerceNotifications($retailer_id);

        $start_time = $this->_logDeltaTime($start_time, 'User::findForEcommerceNotifications');

        $currencyConversion = 1;
        if ($preorder['currency_code'] != $c_retailer['User']['currency_code']) {
            $currencyConversion = $this->currencyConversion(1, $preorder['currency_code'], $c_retailer['User']['currency_code']);

            $start_time = $this->_logDeltaTime($start_time, 'currencyConversion');
        }

        $type = ShippingMethod::getTypeByShippingMethod($shippingMethod, $shipping_method_subtype);

        $code = ($shippingMethod === ShippingMethod::SHIP_EARLY) ? $this->OrderLogic->generateCode() : '';

        $subType = $products['type'];

        $is_nonstock = ($subType === OrderType::SUB_TYPE_NONSTOCK);

        $orderStatus = OrderStatus::getOrderStatusObject((bool)$user['ship_from_store_double_ship'])->getNextOrderStatus(
            $type, 
            $subType
        );

        $totalPrice = $preorder['totalprice'];

        $orderinfo = array(
            'user_id' => $user['id'],
            'order_type' => $type,
            'subType' => $subType,
            'code' => $code,
            'secretcode' => $code,
            'customerID' => $cusId,
            'preOrderId' => $preorder['id'],
            'retailer_id' => $retailer_id,
            'store_associate_id' => $preorder['store_associate_id'],
            'is_commission_retailer' => $preorder['is_commission_retailer'],
            'order_status' => $orderStatus,
            'payment_method' => OrderPaymentMethod::STRIPE,
            'payment_method_subtype' => $logs['Order']['payment_method_subtype'],
            'payment_status' => OrderPaymentStatus::AUTHORIZED,
            'tax_included' => $products['tax_included'],
            'shipping_amount' => ($shippingMethod === 'shipearly_shipearly' && !$is_nonstock) ? 0 : $logs['shippingAmount'],
            'created_at' => $preorder['created'],
            'total_price' => $totalPrice,
            'totalPriceConversion' => $currencyConversion * $totalPrice,
            'currency_code' => $preorder['currency_code'],
            'total_tax' => isset($products['taxamtdiscount'])
                ? $products['taxamtdiscount']
                : $products['taxamt'],// + $products['shippingTax']
            'transactionID' => $transactionID,
            'stripe_account' => $logs['stripe_account'],
            'shipearlyFees' => $this->Currency->formatAsDecimal(
                $this->OrderLogic->CalculateFees($preorder['totalpricediscount'], 'retailer', $user['revenue_model'], $user['retailer_default_amount'], $user['retailer_revenue_maximum']),
                $preorder['currency_code']
            ),
            'card_type' => $preorder['card_type'],
            'last_four_digit' => $preorder['last_four_digit'],
            'fraud_check_cvc' => $preorder['fraud_check_cvc'],
            'fraud_check_address' => $preorder['fraud_check_address'],
            'fraud_check_postal_code' => $preorder['fraud_check_postal_code'],
        );

        if ($shipping_method_subtype === 'localinstall') {
            $orderinfo['is_install'] = true;
            $orderinfo['shipping_amount'] = $products['shippingAmount_localinstall'];
            $orderinfo['total_tax'] = isset($products['taxamtdiscount_localinstall'])
                ? $products['taxamtdiscount_localinstall']
                : $products['taxamt_localinstall'];
        } elseif ($shipping_method_subtype === 'localdelivery') {
            $orderinfo['shipping_amount'] = $products['shippingAmount_localdelivery'];
            $orderinfo['total_tax'] = isset($products['taxamtdiscount_localdelivery'])
                ? $products['taxamtdiscount_localdelivery']
                : $products['taxamt_localdelivery'];
        }

        if (!empty($preorder['store_associate_id'])) {
            $orderinfo['shipearly_commission_fee'] = $user['store_associate_default_amount'];
            $orderinfo['shipearlyFees'] = 0;
        }

        $orderinfo['retailerAmount'] = $this->Currency->formatAsDecimal($orderinfo['total_price'] - $orderinfo['shipearlyFees'], $preorder['currency_code']);

        if ($preorder['is_commission_retailer']) {
            $orderinfo['order_status'] = OrderStatus::PENDING;
            $orderinfo['payment_status'] = ($user['sell_direct_authorize']) ? OrderPaymentStatus::AUTHORIZED : OrderPaymentStatus::PAID;

            $stripeRetailerId = (int)($logs['stripe_retailer_id'] ?? 0);
            if ($this->User->isUserType($stripeRetailerId, User::TYPE_SALES_REP)) {
                // Note that `$orderinfo += $salesRepFields['Order'];` occurs later if this is not set now
                $orderinfo['distributor_id'] = $stripeRetailerId;
            }
        }

        $start_time = $this->_logDeltaTime($start_time, 'build order data');

        $orderinfo += $this->_getOrderAddressFields($shipping);

        $start_time = $this->_logDeltaTime($start_time, '_getOrderAddressFields');

        $orderinfo = array_merge($orderinfo, $this->_extractOrderDiscountFields($discountInfo, $products, $preorder['totalpricediscount']));
        $this->_logIfUnexpectedOrderDiscount($orderinfo, $products, $discountInfo);

        if (!$this->Order->createFromEcommercePreorderWithRetailer($orderinfo, $products['product'], $billing, $currencyConversion)) {
            throw new InternalErrorException(json_encode(['errors' => $this->Order->validationErrors, 'data' => $this->Order->data]));
        }

        $start_time = $this->_logDeltaTime($start_time, 'Order::createFromEcommercePreorderWithRetailer');

        $inc_oid = (int)$this->Order->getLastInsertID();
        $newOrder = $this->Order->get($inc_oid);

        $orderinfo['orderID'] = $newOrder['Order']['orderID'];
        $orderinfo = ['id' => $inc_oid] + $orderinfo;
        $this->Session->write('Shopify.order', $orderinfo);

        $order = array_merge($newOrder['Order'], array(
            'customer_email' => $shipping['email'],
            'customer_firstname' => $shipping['First_name'],
            'customer_lastname' => $shipping['Last_name'],
            'shipping_telephone' => $shipping['phone'],
            'order_type' => $type,
            'shipping_address' => array(
                'street1' => $shipping['address'],
                'street2' => $shipping['address2'],
                'city' => $shipping['city'],
                'state' => $shipping['regionName'],
                'country' => $shipping['countryName'],
                'zipcode' => $shipping['PostalCode'],
            ),
        ));

        $start_time = $this->_logDeltaTime($start_time, 'Retrieve new order');

        if ($orderinfo['is_commission_retailer']) {
            $this->_createEcommerceConsumerOrder((int)$inc_oid);

            $start_time = $this->_logDeltaTime($start_time, 'Create Shopify API commission order');
        } elseif ($preorder['is_ship_to_store_only']) {
            $this->OrderLogic->dealerOrderEvent($inc_oid);
            $this->NotificationLogic->handleShipToStoreOnlyNotification($inc_oid, $order, $shippingMethod, $shipping_method_subtype);

            $start_time = $this->_logDeltaTime($start_time, 'dealerOrderEvent');
        } elseif ($is_nonstock) {
            $this->WarehouseProductReservation->reserveLineItemSet($inc_oid, $this->OrderProduct->findAllForInventoryReservation($inc_oid));

            $start_time = $this->_logDeltaTime($start_time, 'WarehouseProductReservation::reserveLineItemSet');
        }

        if (!$preorder['is_ship_to_store_only']) {
            $this->NotificationLogic->handleNotification($shippingMethod, $inc_oid, $c_retailer, $user, $order, $shipping_method_subtype);

            $start_time = $this->_logDeltaTime($start_time, 'handleNotification');
        }

        $this->OrderLogic->updateChargeForNewOrder((int)$inc_oid);

        $start_time = $this->_logDeltaTime($start_time, 'updateChargeForNewOrder');

        if ($shippingMethod === 'shipfromstore_shipfromstore') {
            $this->Avatax->setTotalTaxFromEcommerce(
                $preorder['currency_code'], $retailer_id, $shipping, $newOrder['Order']['orderID'], $logs, $products['product']
            );

            $start_time = $this->_logDeltaTime($start_time, 'Avatax::setTotalTaxFromEcommerce');
        }

        $posShipping = $shipping;
        $posBilling = $billing;
        $this->_formatInput($posShipping, $posBilling);
        $this->OrderPlacer->createPosOrder($retailer_id, $newOrder['Order'], $products, $posShipping, $posBilling, $shippingMethod);

        $start_time = $this->_logDeltaTime($start_time, 'createPosOrder');

        return json_encode(array('id' => $inc_oid, 'orderId' => $this->formatOrderId($inc_oid, false), 'code' => $code));
    }

    private function _createEcommerceConsumerOrder(int $newOrderId): bool
    {
        try {
            return $this->OrderPlacer->createEcommerceConsumerOrder($newOrderId);
        } catch (Exception $e) {
            CakeLog::critical($e);
        }

        return false;
    }

    private function _extractOrderDiscountFields(array $discountInfo, array $products, float $totalPriceDiscounted): array
    {
        if (empty($discountInfo['code'])) {
            return [];
        }

        // Trigger Notice if preorder is missing expected fields
        $productDiscount = (float)($products['totalproductamount'] - $products['totalproductamountdiscount']);
        $shippingDiscount = (float)$products['shippingDiscount'];

        $totalDiscount = $productDiscount + $shippingDiscount;
        $totalPrice = $totalPriceDiscounted + $totalDiscount;

        return [
            'total_price' => $totalPrice,
            'total_discount' => $totalDiscount,
            'shipping_discount' => $shippingDiscount,
            'discount_code' => $discountInfo['code'],
        ];
    }

    private function _logIfUnexpectedOrderDiscount(array $orderinfo, array $products, array $discountInfo): void
    {
        $expected_shipping_discount = format_number($products['shippingAmount'] - $products['shippingAmountDiscounted']);
        $actual_shipping_discount = format_number($orderinfo['shipping_discount'] ?? 0);
        $expected_total_discount = format_number(($products['totalproductamount'] - $products['totalproductamountdiscount']) + (float)$expected_shipping_discount);
        $actual_total_discount = format_number($orderinfo['total_discount'] ?? 0);

        if ($expected_shipping_discount !== $actual_shipping_discount || $expected_total_discount !== $actual_total_discount) {
            triggerWarning(json_encode(compact('expected_shipping_discount', 'actual_shipping_discount', 'expected_total_discount', 'actual_total_discount', 'discountInfo')));
        }
    }

    /**
     * @param array $address
     * @return array
     */
    protected function _getOrderAddressFields(array $address)
    {
        $orderAddressFields = array(
            'customerEmail' => $address['email'],
            'billing_firstname' => $address['First_name'],
            'billing_lastname' => $address['Last_name'],
            'shipping_company_name' => $address['company'],
            'shipping_address1' => $address['address'],
            'shipping_address2' => $address['address2'],
            'shipping_city' => $address['city'],
            'shipping_state' => $address['regionName'],
            'shipping_country' => $address['countryName'],
            'shipping_zipcode' => $address['PostalCode'],
            'shipping_telephone' => $address['phone'],
            'shipping_statecode' => $address['province'],
            'shipping_countrycode' => $address['country'],
        );
        $geopoints = $this->Session->read('Shopify.customer_geopoints');
        if (empty($geopoints) && !empty($orderAddressFields['shipping_zipcode'])) {
            $geopoints = $this->_getLnt(
                $orderAddressFields['shipping_address1'],
                $orderAddressFields['shipping_city'],
                $orderAddressFields['shipping_zipcode'],
                $orderAddressFields['shipping_state'],
                $orderAddressFields['shipping_country']
            );
        }
        if (!empty($geopoints)) {
            $geopoints = array_map(function($lnt) { return format_number($lnt, 6); }, (array)$geopoints);
            $this->Session->write('Shopify.geopoints', $geopoints);
            $orderAddressFields['latitude'] = $geopoints['lat'];
            $orderAddressFields['longitude'] = $geopoints['lng'];
        }
        return $orderAddressFields;
    }

    public function _processAddressFormFields(array $addressForm): array
    {
        $addressForm = array_map('trim', $addressForm);

        $addressForm['PostalCode'] = preg_replace('/^([ABCEGHJKLMNPRSTVXY][0-9][ABCEGHJKLMNPRSTVWXYZ])([0-9][ABCEGHJKLMNPRSTVWXYZ][0-9])$/', '$1 $2', strtoupper($addressForm['PostalCode']));

        $addressForm = $this->_fillMissingAddressStateFields($addressForm);

        //FIXME Change to country_name when safe to do so
        $addressForm['countryName'] = strtolower($addressForm['countryCode']);

        return $addressForm;
    }

    private function _fillMissingAddressStateFields(array $addressForm): array
    {
        $fieldNames = ['regionName', 'regionCode', 'countryName', 'countryCode'];
        $fieldNames = array_combine($fieldNames, $fieldNames);

        $missing = array_diff_key($fieldNames, array_filter($addressForm));
        if (!$missing) {
            return $addressForm;
        }

        $record = $this->State->findWithCountryName($addressForm['province'])['State'];
        if ($record['country_id'] != $addressForm['country']) {
            $record['country_code'] = null;
            $record['country_name'] = null;
        }

        return array_merge($addressForm, [
            'regionName' => $record['state_name'],
            'regionCode' => $record['state_code'],
            'countryName' => $record['country_name'],
            'countryCode' => $record['country_code'],
        ]);
    }

    protected function _validateAddress($address)
    {
        $response = $this->AddressValidator->validate_address([
            'email_address' => $address['email'] ?? null,
            'fullname' => $address['First_name'] . ' ' . $address['Last_name'],
            'company' => $address['company'],
            'address' => $address['address'],
            'address2' => $address['address2'],
            'city' => $address['city'],
            'country_code' => $address['country'],
            'state_code' => $address['province'],
            'postal_code' => $address['PostalCode'],
            'phone' => $address['phone'],
        ]);

        if (in_array($response['status'], ['INVALID', 'ERROR'])) {
            $this->setFlash($response['message'], 'error');
            return false;
        }
        return true;
    }

    public function _formatInput(&$shipping, &$billing)
    {
        $email = $shipping['email'];

        $shipping['countryName'] = strtoupper($shipping['countryName']);
        $shipping = $this->OrderPlacer->formatAddress($shipping, $email);

        $billing['countryName'] = strtoupper($billing['countryName']);
        $billing = $this->OrderPlacer->formatAddress($billing, $email);
    }

    /**
     * @param string $token
     * @param array $fields
     * @return array
     */
    public function _checkManUserToken($token, array $fields = []): array
    {
        return (array)$this->User->find('first', [
            'recursive' => -1,
            'conditions' => ['status' => 'Active', 'uuid' => $token],
            'fields' => $fields,
        ]);
    }

    public function notifyRetailer()
    {
        $users = $this->_checkManUserToken($this->Session->read('Shopify.token'));
        $this->Session->write('Shopify.currentUser', $users);
        $shipping = $this->Session->read('Shopify.shipping');
        $items = $this->ShopifyCarts->getValue('items');

        $notify['userId'] = $users['User']['id'];
        $notify['zipCode'] = $shipping['PostalCode'];
        $customerName['firstname'] = $shipping['First_name'];
        $customerName['middlename'] = '';
        $customerName['lastname'] = $shipping['Last_name'];
        $customerName['email'] = $shipping['email'];
        $notify['customerData'] = json_encode($customerName);
        function productId($a)
        {
            return $a['variant_id'];
        }

        $pids = array_map('productId', $items);
        $ids = $this->Product->find('list', array(
            'fields' => array('id'),
            'conditions' => array('productID' => $pids, 'user_id' => $notify['userId'])
        ));
        $notify['productId'] = json_encode($ids);
        $this->Notifycustomer->save($notify);
    }


    public function createBasicSetup()
    {
        $this->autoRender = false;

        $token = $this->request->data('uuid');
        if (
            empty($token) &&
            $this->Auth->user('user_type') === User::TYPE_MANUFACTURER &&
            $this->Auth->user('site_type') === UserSiteType::SHOPIFY
        ) {
            $token = $this->Auth->user('uuid');
        }

        $brand = $this->User->findByUuid($token, [
            'id',
            'email_address',
            'company_name',
            'site_type',
            'api_key',
            'secret_key',
            'shop_url',
            'shop_cart_url',
            'gtm_container_id',
            'ga_tracking_id',
        ], null, -1);
        if (empty($brand['User']['id'])) {
            $message = 'User not found for token';
            throw new NotFoundException(json_encode(compact('message', 'token')));
        }

        $user = $brand['User']['api_key'];
        $pass = $brand['User']['secret_key'];
        $domain = $brand['User']['shop_url'];

        $brand['User'] = mask_secret_fields($brand['User'], ['api_key', 'secret_key']);

        $requiredCredentials = [$pass, $domain];
        if (count(array_filter($requiredCredentials)) !== count($requiredCredentials)) {
            $message = 'Missing request data';

            throw new BadRequestException(json_encode(compact('message') + $brand));
        }

        $permissions = $this->Shopify->listMissingAccessScopes($user, $pass, $domain, [
            'read_themes',
            'write_themes',
            'read_content',
            'write_content',
        ]);
        if ($permissions) {
            $message = 'Required Shopify API permissions are not enabled for this app';
            throw new ForbiddenException(json_encode(compact('message', 'permissions') + $brand));
        }

        App::uses('AppView', 'View');
        $templateView = new AppView($this);
        $templateView->layout = false;
        $templateView->set('cartUrl', $brand['User']['shop_cart_url'] ?: '/cart');
        $templateView->set('shipearlyBaseUrl', $this->_shipearlyBaseUrl((int)$brand['User']['id']));
        $templateView->set('token', $token);
        $templateView->set('gtmContainerId', $brand['User']['gtm_container_id']);
        $templateView->set('gaTrackingId', $brand['User']['ga_tracking_id']);
        $templateView->set('successPageSnippet', $this->User->findSuccessPageSnippet($token));

        $themesList = $this->Shopify->getAllThemes($user, $pass, $domain);
        $templates = array(
            'templates/page.shopify.liquid',
            'templates/page.success.liquid',
            'templates/page.shopifyRedirect.liquid',
            'templates/page.shopifyError.liquid',
        );

        foreach ($themesList as $theme) {
            foreach ($templates as $template) {
                $templateView->hasRendered = false;
                $asset = array(
                    'isDuplicate' => true,
                    'key' => $template,
                    'source-key' => 'templates/pages.liquid',
                    'theme_id' => $theme['id'],
                    'value' => $templateView->render($template),
                );
                $this->Shopify->createAssets($user, $pass, $domain, $theme['id'], array('asset' => $asset));
            }
        }

        $pages = [
            [
                'title' => 'Checkout',
                'handle' => 'checkout',
                'template_suffix' => 'shopify',
                'body_html' => '',
                'published' => true,
            ],
            [
                'title' => 'Success',
                'handle' => 'success',
                'template_suffix' => 'success',
                'body_html' => '',
                'published' => true,
            ],
            [
                'title' => 'OrderProcessing',
                'handle' => 'orderProcessing',
                'template_suffix' => 'shopifyRedirect',
                'body_html' => '',
                'published' => true,
            ],
            [
                'title' => 'OrderError',
                'handle' => 'OrderError',
                'template_suffix' => 'shopifyError',
                'body_html' => '',
                'published' => true,
            ],
        ];
        $this->Shopify->synchronizePages($user, $pass, $domain, $pages);

        return true;
    }

    protected function _shipearlyBaseUrl(int $userId): string
    {
        $this->User->bindModel(['belongsTo' => ['CheckoutDomain' => ['className' => 'UserDomain']]], false);
        $checkoutDomain = (string)$this->User->fieldByConditions('CheckoutDomain.domain', [
            'User.id' => $userId,
        ], ['contain' => ['CheckoutDomain']]);
        $this->User->unbindModel(['belongsTo' => ['CheckoutDomain']], false);
        if ($checkoutDomain) {
            return "https://{$checkoutDomain}/";
        }

        $subdomain = (string)$this->UserSubdomain->fieldByConditions('subdomain', [
            'UserSubdomain.user_id' => $userId,
        ]);

        return shopify_base_path($subdomain);
    }

    public function getInStoreRetailers()
    {
        $retailerId = (int)$this->request->data['retailer_id'];
        $type = (string)$this->request->data['type'];
        $count = (int)$this->request->data['count'];

        $shipping = $this->Session->read('Shopify.shipping');

        $result = $this->Session->read("Shopify.retailerInfo.{$type}.{$retailerId}");
        if (empty($result)) {
            $result = $this->_getRetailerInfo($retailerId, $type);
        }
        $this->Session->write("Shopify.retailerInfo.{$type}.{$retailerId}", $result);
        $Triggeredcount = count($this->Session->read("Shopify.retailerInfo.{$type}"));

        $value = json_decode($result);
        if (!empty($value->retailer_id)) {
            $value->sellExclusive = $this->Session->read('Shopify.sellExclusiveResult') ?: null;
        }

        $this->set('shippingType', $type);
        $this->set('distanceUnit', ($shipping['countryCode'] !== 'US') ? 'km' : 'miles');
        $this->set('value', $value);
        $this->set('stopLoading', ($Triggeredcount >= $count));
        $this->set('stripePaymentTypes', $this->_requestStripePaymentTypes($retailerId, $type));
    }

    public function getLocalDeliveryRetailers()
    {
        $retailerId = (int)$this->request->data['retailer_id'];
        $type = (string)$this->request->data['type'];
        $count = (int)$this->request->data['count'];

        $shipping = $this->Session->read('Shopify.shipping');

        $result = $this->Session->read("Shopify.retailerInfo.{$type}.{$retailerId}");
        if (empty($result)) {
            $result = $this->_getRetailerInfo($retailerId, $type);

            // Process Velofix retailer using selldirect response
            $velofixResult = json_decode($result, true);
            if (!empty($velofixResult['velofix_selldirect_result'])) {
                $this->Session->write("Shopify.retailerInfo.velofix.{$retailerId}", $velofixResult['velofix_selldirect_result']);
            }
        }
        $this->Session->write("Shopify.retailerInfo.{$type}.{$retailerId}", $result);
        $Triggeredcount = count($this->Session->read("Shopify.retailerInfo.{$type}"));

        $value = json_decode($result);
        if (!empty($value->retailer_id)) {
            $value->sellExclusive = $this->Session->read('Shopify.sellExclusiveResult') ?: null;
        }

        $this->set('shippingType', $type);
        $this->set('distanceUnit', ($shipping['countryCode'] !== 'US') ? 'km' : 'miles');
        $this->set('value', $value);
        $this->set('stopLoading', ($Triggeredcount >= $count));
        $this->set('stripePaymentTypes', $this->_requestStripePaymentTypes($retailerId, $type));

        return $this->render('get_in_store_retailers');
    }

    public function getShipRetailers()
    {
        $retailerId = (int)$this->request->data['retailer_id'];
        $type = (string)$this->request->data['type'];
        $count = (int)$this->request->data['count'];

        $result = $this->Session->read("Shopify.retailerInfo.{$type}.{$retailerId}");
        if (empty($result)) {
            $result = $this->_getRetailerInfo($retailerId, $type);
        }
        $this->Session->write("Shopify.retailerInfo.{$type}.{$retailerId}", $result);
        $Triggeredcount = count($this->Session->read("Shopify.retailerInfo.{$type}"));

        $value = json_decode($result);
        if (!empty($value->retailer_id)) {
            $value->sellExclusive = $this->Session->read('Shopify.sellExclusiveResult') ?: null;
        }

        $this->set('value', $value);
        $this->set('stopLoading', ($Triggeredcount >= $count));
        $this->set('stripePaymentTypes', $this->_requestStripePaymentTypes($retailerId, $type));
    }

    public function getSellDirect()
    {
        $brandId = (int)$this->request->data['retailer_id'];
        $type = (string)$this->request->data['type'];

        $shippingRates = (array)$this->Session->read('Shopify.shippingRates');
        $shippingAmount = $shippingRates[0]['amount'] ?? null;
        $shippingDiscount = $shippingRates[0]['discount'] ?? null;

        $sellExclusiveResult = $this->Session->read('Shopify.sellExclusiveResult');
        $buyDirectOnly = (!isset($sellExclusiveResult->buyDirectOnly) || $sellExclusiveResult->buyDirectOnly != 0);

        $result = $this->_getSellDirect($brandId, $type, $shippingAmount, $shippingDiscount, $buyDirectOnly);
        $this->Session->write("Shopify.retailerInfo.{$type}.{$brandId}", $result);

        $result_obj = json_decode($result, true);
        $result_obj['shippingRates'] = $shippingRates;
        $result_obj['shippingName'] = $shippingRates[0]['name'] ?? 'Free Shipping';
        $result_obj['sellExclusive'] = $sellExclusiveResult ? json_decode($this->_getSellExclusive(0)) : null;

        $this->set('data', $result_obj);
        $this->set('stripePaymentTypesByCourierId', array_map(
            fn(?string $courierId): array => $this->_requestStripePaymentTypes($brandId, $type, $courierId),
            array_column($shippingRates ?: [0 => ['id' => null]], 'id', 'id')
        ));
    }

    /**
     * @param int $retailerId
     * @param string $type
     * @return string retailerInfo JSON
     */
    protected function _getRetailerInfo($retailerId, $type): string
    {
        $retailerId = (int)$retailerId;
        $type = (string)$type;

        $userId = (int)$this->Session->read('Shopify.User.User.id');
        $cartItems = (array)$this->ShopifyCarts->getValue('items');
        $currencyCode = (string)($this->ShopifyCarts->getValue('currency') ?: $this->Session->read('Shopify.User.User.currency_code'));
        $shippingAddress = (array)$this->Session->read('Shopify.shipping');

        $discount_info = (array)$this->Session->read('Shopify.discount');
        $discountRetailerIds = !empty($discount_info['id'])
            ? $this->User->listDiscountRetailerIds($userId, $discount_info['retailer_option'], $discount_info['retailer_values'])
            : null;

        if ($this->ManufacturerRetailer->usesRetailerShippingRates($userId, $retailerId)) {
            $products = $this->_findAllProductsForShippingRates($userId, $cartItems);
            $shippingUserId = (int)$this->User->getMainRetailerId($retailerId);
            $shippingRates = $this->ShippingCalculator->getRetailerShippingRates($shippingUserId, $currencyCode, $shippingAddress, $products, $discount_info);
        } else {
            $shippingRates = (array)$this->Session->read('Shopify.shippingRates');
        }
        $shippingAmount = $shippingRates[0]['amount'] ?? null;
        $shippingDiscount = $shippingRates[0]['discount'] ?? null;
        // Currently only used for creating Shopify API orders.
        $extraFields = [
            'shippingName' => $shippingRates[0]['name'] ?? null,
            'shippingBoxWeight' => $shippingRates[0]['box_weight'] ?? null,
        ];

        $result = $this->requestAction('/ws/checkProduct', ['data' => [
            'token' => $this->Session->read('Shopify.token'),
            'customer' => $this->Session->read('Shopify.customer'),
            'items' => json_encode($cartItems),
            'currency_code' => $currencyCode,
            'address' => json_encode($shippingAddress),
            'shippingAmount' => $shippingAmount,
            'shippingDiscount' => $shippingDiscount,
            'discountRetailerIds' => $discountRetailerIds,
            'retailers' => $retailerId,
            'geopoints' => $this->Session->read('Shopify.customer_geopoints'),
            'type' => $type,
            'discounts' => json_encode($this->Session->read('Shopify.ItemDiscounts')),
        ]]);

        $result_obj = (array)json_decode($result, true);
        if (!empty($result_obj['retailer_id'])) {
            $result = json_encode(array_merge($result_obj, $extraFields));
        }

        return (string)$result;
    }

    /**
     * @param int|string $brandId
     * @param string $type
     * @param float|string|null $shippingAmount
     * @param bool $buyDirectOnly Flag to compute result of all cart items
     * @return false|mixed
     */
    protected function _getSellDirect($brandId, $type, $shippingAmount, $shippingDiscount, bool $buyDirectOnly)
    {
        return $this->requestAction('/ws/checkProduct', ['data' => [
            'token' => $this->Session->read('Shopify.token'),
            'customer' => $this->Session->read('Shopify.customer'),
            'items' => json_encode($this->ShopifyCarts->getValue('items')),
            'currency_code' => $this->ShopifyCarts->getValue('currency'),
            'address' => json_encode($this->Session->read('Shopify.shipping')),
            'shippingAmount' => $shippingAmount,
            'shippingDiscount' => $shippingDiscount,
            'brand' => $brandId,
            'buyDirectOnly' => ($buyDirectOnly) ? 1 : 0,
            'geopoints' => $this->Session->read('Shopify.customer_geopoints'),
            'type' => $type,
            'discounts' => json_encode($this->Session->read('Shopify.ItemDiscounts')),
        ]]);
    }

    /**
     * @param float|string|null $shippingAmount
     * @return false|mixed
     */
    private function _getSellExclusive($shippingAmount, $shippingDiscount = 0)
    {
        return $this->requestAction('/ws/checkSellExclusiveProduct', ['data' => [
            'token' => $this->Session->read('Shopify.token'),
            'customer' => $this->Session->read('Shopify.customer'),
            'items' => json_encode($this->ShopifyCarts->getValue('items')),
            'currency_code' => $this->ShopifyCarts->getValue('currency'),
            'address' => json_encode($this->Session->read('Shopify.shipping')),
            'shippingAmount' => $shippingAmount,
            'shippingDiscount' => $shippingDiscount,
            'geopoints' => $this->Session->read('Shopify.customer_geopoints'),
            'discounts' => json_encode($this->Session->read('Shopify.ItemDiscounts')),
        ]]);
    }

    /**
     * @param int $retailerId
     * @param string $type
     * @param string|null $courierId
     * @return string[] stripe_payment_types
     */
    protected function _requestStripePaymentTypes(int $retailerId, string $type, ?string $courierId = null): array
    {
        $stripePaymentMethodsResult = $this->requestAction(['plugin' => 'shopify', 'controller' => 'shopify', 'action' => 'stripePayment', 'filter' => 'payment_methods'], [
            'data' => ['retailer_id' => $retailerId, 'type' => $type, 'courier_option' => ['id' => $courierId]],
            'return',
        ]);

        //TODO Ideally, this would be added to the '/ws/checkProduct' response to be shared between eCom plugins
        return (array)(json_decode($stripePaymentMethodsResult, true)['stripe_payment_types'] ?? []);
    }

    public function stripePayment()
    {
        $this->autoRender = false;

        $user = $this->Session->read('Shopify.User');
        if (empty($user['User']['id'])) {
            return $this->_exceptionResponse(new UnauthorizedException(), 'Session has expired');
        }
        $brandId = (int)$user['User']['id'];

        $missingFieldNames = array_keys(array_diff_key(array_flip(['retailer_id', 'type']), array_filter($this->request->data)));
        if ($missingFieldNames) {
            $message = 'Missing required fields: ' . implode(', ', $missingFieldNames);

            return $this->_exceptionResponse(
                new BadRequestException($message),
                $message,
                true
            );
        }
        $retailer_id = (int)$this->request->data('retailer_id');
        $type = (string)$this->request->data('type');

        $sellExclusiveResult = $this->Session->read('Shopify.sellExclusiveResult');
        if ($type === 'sellDirect') {
            $courierId = $this->request->data('courier_option.id');
            $shippingRates = Hash::combine((array)$this->Session->read('Shopify.shippingRates'), '{n}.id', '{n}');
            $selectedRate = $shippingRates[$courierId] ?? (current($shippingRates) ?: []);
            if ($selectedRate || $sellExclusiveResult) {
                CakeLog::debug(json_encode(compact('selectedRate')));
                $this->Session->write('Shopify.shippingName', $selectedRate['name'] ?? null);
                $this->Session->write('Shopify.shippingCode', $selectedRate['code'] ?? null);
                $this->Session->write('Shopify.packageBoxWeight', $selectedRate['box_weight'] ?? null);

                $result = $this->_getSellDirect($retailer_id, $type, ($selectedRate['amount'] ?? 0), ($selectedRate['discount'] ?? 0), true);
                $this->Session->write("Shopify.retailerInfo.{$type}.{$retailer_id}", $result);

                $sellExclusiveResult = null;
            }
        }
        $retailerInfo = json_decode($this->Session->read("Shopify.retailerInfo.{$type}.{$retailer_id}"), true);
        if (!$retailerInfo) {
            return $this->_exceptionResponse(
                new NotFoundException(sprintf('Session retailer not found where type=%s and retailer_id=%s', $type, $retailer_id)),
                'Payee data not found',
                true
            );
        }

        $shippingAddress = $this->request->data('shipping.email')
            ? $this->_processAddressFormFields((array)$this->request->data['shipping'])
            : (array)$this->Session->read('Shopify.shipping');
        $shippingCountryId = (int)$shippingAddress['country'];
        $shippingStateId = (int)$shippingAddress['province'];

        $stripeRetailer = $this->StripeUser->getEcommerceStripePayee($brandId, $retailer_id, $shippingCountryId, $shippingStateId);
        if (empty($stripeRetailer['StripeUser']['stripe_user_id'])) {
            return $this->_exceptionResponse(
                new NotFoundException(sprintf('Stripe account not found where user_id=%s and retailer_id=%s', $brandId, $retailer_id)),
                'Payee account not found',
                true
            );
        }
        $stripe_account = (string)$stripeRetailer['StripeUser']['stripe_user_id'];
        $stripeRetailerId = (int)$stripeRetailer['StripeUser']['user_id'];
        $isCommissionRetailer = (bool)$stripeRetailer['ManufacturerRetailer']['is_commission_tier'];

        if ($this->request->param('filter') !== 'payment_methods') {
            $brandIsInventoryProvider = ($retailer_id === $brandId || $retailerInfo['type'] === 'nonstock' || $isCommissionRetailer);
            if ($this->_saveMessageIfOutOfStockError($brandId, $brandIsInventoryProvider)) {
                $response = ['redirect' => Router::url(['plugin' => 'Shopify', 'controller' => 'Shopify', 'action' => 'error'], true)];
                CakeLog::debug(json_encode(compact('response')));

                $this->set($response);
                $this->set('_serialize', array_keys($response));

                return (!$this->request->is('ajax')) ? $this->redirect($this->viewVars['redirect']) : $this->render();
            }
        }

        $sellExclusiveId = !empty($sellExclusiveResult->id) ? (int)$sellExclusiveResult->id : null;
        $sell_exclusive_stripe_account = $this->StripeUser->getSellExclusiveAccountId($sellExclusiveId, $type, $shippingCountryId, $shippingStateId);

        $isPaymentRequest = $this->request->data('payment_request');
        $isPlatformCustomer = (
            $isPaymentRequest
            || $stripeRetailer['User']['user_type'] === User::TYPE_RETAILER
            || ($sell_exclusive_stripe_account && $sell_exclusive_stripe_account !== $stripe_account)
        );
        if (!$isPlatformCustomer) {
            $cus_owner_user_id = $stripeRetailerId;
            $cus_owner_stripe_account = $stripe_account;
        } else {
            $cus_owner_user_id = null;
            $cus_owner_stripe_account = null;
        }

        $retailerName = ($isCommissionRetailer) ? $user['User']['company_name'] : $retailerInfo['company_name'];
        $currencyCode = (string)$retailerInfo['currency'];
        $amount = $this->_extractRetailerTotalPriceDiscounted($type, $retailerInfo);
        $sellExclusiveAmount = $sellExclusiveResult->totalwithshippingdiscount ?? $sellExclusiveResult->totalwithshipping ?? 0;
        $combinedAmount = $this->Currency->formatAsDecimal($amount + $sellExclusiveAmount, $currencyCode);

        $orderIsFree = ($type === 'sellDirect' && $combinedAmount <= 0);

        $stripeUserCapabilities = $this->StripeUserCapability->getUserCapabilities($stripeRetailer['StripeUser']['id']);
        $paymentMethodTypes = StripePaymentType::getPaymentMethods($stripeUserCapabilities, ($sellExclusiveResult || $isPaymentRequest || $orderIsFree));

        $paymentMethodTypeFilter = OrderPaymentMethodSubtype::toStripePaymentType((string)$this->request->data('paymentMethodSubtype'));
        if ($paymentMethodTypeFilter) {
            $paymentMethodTypes = array_values(array_intersect($paymentMethodTypes, [$paymentMethodTypeFilter]));
        }

        $cardIsSetupIntent = ($isPlatformCustomer || $sellExclusiveResult);

        if ($orderIsFree || $this->request->param('filter') === 'payment_methods') {
            $this->response->body(json_encode([
                'success' => true,
                'stripe_platform_key' => STRIPE_PUBLISHABLE_KEY,
                'stripe_api_version' => STRIPE_API_VERSION,
                'stripe_payment_types' => $this->Stripe->_filterPaymentMethodTypes($paymentMethodTypes, (float)$amount, $currencyCode),
                'stripe_payment_account' => $stripe_account,
                'stripe_payment_secret' => null,
                'stripe_card_account' => ($cardIsSetupIntent) ? $cus_owner_stripe_account : $stripe_account,
                'stripe_card_secret' => null,
                'rname' => $retailerName,
                'amount' => (int)round($combinedAmount * 100),
                'currency' => $currencyCode,
                'amount_format' => $this->Currency->formatCurrency($combinedAmount, $currencyCode, false, true),
            ]));

            return $this->response;
        }

        $stripeCustomerId = $this->Stripe->createPaymentCustomerIfNotExists((string)$shippingAddress['email'], $cus_owner_user_id, $cus_owner_stripe_account, $shippingAddress);

        $application_fee = $amount;
        $sell_exclusive_application_fee = null;
        if ($type === 'sellDirect' || $isCommissionRetailer || $retailerInfo['type'] !== 'nonstock' || $sellExclusiveResult) {
            $revenueModels = $this->User->findById($brandId, [
                'User.revenue_model',
                'User.retailer_default_amount',
                'User.retailer_revenue_maximum',
                'User.brand_revenue_model',
                'User.brand_direct_default_amount',
                'User.brand_revenue_maximum',
            ], null, -1)['User'];

            if ($type === 'sellDirect') {
                $application_fee = $this->OrderLogic->CalculateFees($amount, 'brand', $revenueModels['brand_revenue_model'], $revenueModels['brand_direct_default_amount'], $revenueModels['brand_revenue_maximum']);
            } elseif ($isCommissionRetailer || $retailerInfo['type'] !== 'nonstock') {
                $application_fee = $this->OrderLogic->CalculateFees($amount, 'retailer', $revenueModels['revenue_model'], $revenueModels['retailer_default_amount'], $revenueModels['retailer_revenue_maximum']);
            }
            if ($sellExclusiveResult) {
                $sell_exclusive_application_fee = $this->OrderLogic->CalculateFees($sellExclusiveAmount, 'brand', $revenueModels['brand_revenue_model'], $revenueModels['brand_direct_default_amount'], $revenueModels['brand_revenue_maximum']);
            }
        }

        $paymentParams = [
            'description' => 'Charge for Your Order',
            'metadata' => [],
            'payment_method_types' => $paymentMethodTypes,
            'shipping' => [
                'address' => [
                    'line1' => $shippingAddress['address'],
                    'line2' => $shippingAddress['address2'] ?: null,
                    'city' => $shippingAddress['city'],
                    'state' => $shippingAddress['regionName'],
                    'country' => $shippingAddress['countryCode'],
                    'postal_code' => $shippingAddress['PostalCode'],
                ],
                'name' => trim($shippingAddress['First_name'] . ' ' . $shippingAddress['Last_name']),
                'phone' => $shippingAddress['phone'],
            ],
            'application_fee_amount' => (int)round($application_fee * 100),
            'capture_method' => 'manual',
        ];
        if (!$isPlatformCustomer) {
            $paymentParams['customer'] = $stripeCustomerId;
        }
        $brand_stripe_account = $this->StripeUser->getAccountId($brandId);
        if ($stripe_account !== $brand_stripe_account) {
            $brandDescriptor = $this->Stripe->getStatementDescriptor($brand_stripe_account, $user['User']['company_name']);
            if ($brandDescriptor) {
                // 'card' payments use 'statement_descriptor_suffix' instead of 'statement_descriptor'
                $paymentParams = array_merge($paymentParams, [
                    'statement_descriptor' => $brandDescriptor,
                    'statement_descriptor_suffix' => $brandDescriptor,
                ]);
            }
        }

        $paymentIdKey = "Shopify.accountPaymentIds.{$stripe_account}";
        $paymentId = $this->Session->read($paymentIdKey);

        $payment = $this->Stripe->initEcommercePaymentIntent($paymentId, $amount, $currencyCode, $paymentParams, compact('stripe_account'));

        $paymentId = $payment->id;
        $this->Session->write($paymentIdKey, $paymentId);

        $sellExclusivePaymentId = null;
        if ($sellExclusiveResult) {
            $sellExclusivePaymentIdKey = "Shopify.accountSellExclusivePaymentIds.{$sell_exclusive_stripe_account}";
            $sellExclusivePaymentId = $this->Session->read($sellExclusivePaymentIdKey);

            $sellExclusivePaymentParams = array_merge($paymentParams, [
                'payment_method_types' => ['card'],
                'application_fee_amount' => (int)round($sell_exclusive_application_fee * 100),
            ]);
            if ($sell_exclusive_stripe_account === $brand_stripe_account) {
                unset(
                    $sellExclusivePaymentParams['statement_descriptor'],
                    $sellExclusivePaymentParams['statement_descriptor_suffix'],
                );
            }
            $sellExclusivePayment = $this->Stripe->initEcommercePaymentIntent($sellExclusivePaymentId, $sellExclusiveAmount, $currencyCode, $sellExclusivePaymentParams, ['stripe_account' => $sell_exclusive_stripe_account]);

            $sellExclusivePaymentId = $sellExclusivePayment->id;
            $this->Session->write($sellExclusivePaymentIdKey, $sellExclusivePaymentId);
        }

        $setup = null;
        if ($cardIsSetupIntent) {
            $setupIdKey = "Shopify.accountSetupId.{$cus_owner_stripe_account}";
            $setupId = $this->Session->read($setupIdKey);

            $setup = $this->Stripe->saveCardSetupIntent(
                $setupId,
                $cus_owner_stripe_account,
                $stripeCustomerId,
                $paymentId,
                $stripe_account,
                $sellExclusivePaymentId,
                $sell_exclusive_stripe_account
            );

            $setupId = $setup->id;
            $this->Session->write($setupIdKey, $setupId);
        }

        $stripePaymentAccount = $stripe_account;
        $stripePaymentSecret = $payment->client_secret;

        $stripeCardAccount = $stripePaymentAccount;
        $stripeCardSecret = $stripePaymentSecret;
        if ($setup) {
            $stripeCardAccount = $cus_owner_stripe_account;
            $stripeCardSecret = $setup->client_secret;
        }

        $this->response->body(json_encode([
            'success' => true,
            'stripe_platform_key' => STRIPE_PUBLISHABLE_KEY,
            'stripe_api_version' => STRIPE_API_VERSION,
            'stripe_payment_types' => $payment->payment_method_types,
            'stripe_payment_account' => $stripePaymentAccount,
            'stripe_payment_secret' => $stripePaymentSecret,
            'stripe_card_account' => $stripeCardAccount,
            'stripe_card_secret' => $stripeCardSecret,
            'rname' => $retailerName,
            'amount' => (int)round($combinedAmount * 100),
            'currency' => $currencyCode,
            'amount_format' => $this->Currency->formatCurrency($combinedAmount, $currencyCode, false, true),
        ]));

        return $this->response;
    }

    public function clone_platform_payment_method()
    {
        $this->request->allowMethod('post');

        $setupId = $this->request->data('setup_intent_id');
        $setupAccount = $this->request->data('setup_account');

        /** @var null|\Stripe\PaymentIntent $sellExclusivePayment */
        list($payment, $sellExclusivePayment) = $this->Stripe->cloneCardSetupToPayments($setupId, $setupAccount);

        $response = [
            'payment_client_secret' => $payment->client_secret,
            'payment_method_id' => $payment->payment_method,
            'sell_exclusive_payment_client_secret' => $sellExclusivePayment->client_secret ?? null,
            'sell_exclusive_payment_method_id' => $sellExclusivePayment->payment_method ?? null,
        ];
        $this->set($response);
        $this->set('_serialize', array_keys($response));

        return null;
    }

    /**
     * @param array $user
     * @return string[]
     */
    protected function _brandPolicies(array $user): array
    {
        return array_filter($this->UserSetting->listPolicies((int)$user['User']['id'], (string)$user['User']['email_address']));
    }

    public function validate_address()
    {
        $this->response->body(json_encode($this->AddressValidator->validate_address($this->request->data)));
        return $this->response;
    }
}
