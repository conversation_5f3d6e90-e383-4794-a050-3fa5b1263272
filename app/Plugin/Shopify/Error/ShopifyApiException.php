<?php
App::uses('Router', 'Routing');

/**
 * Class ShopifyApiException.
 *
 * @package Shopify.Error
 */
class ShopifyApiException extends Exception
{
    protected $method = '';
    protected $url = '';
    protected $params = [];
    protected $response_body;
    protected $response_headers = [];
    protected $request_headers = [];

    /**
     * @param CurlException $e
     * @param string $method
     * @param string $url
     * @param array $params
     * @param array $request_headers
     * @return ShopifyApiException
     */
    public static function fromCurlException(CurlException $e, string $method, string $url, array $params, array $request_headers): self
    {
        $response_body = $e->getMessage();

        return new static(json_encode(compact('method', 'url', 'params', 'request_headers', 'response_body')), 500, $e);
    }

    /**
     * @param string $method
     * @param string $url
     * @param array $params
     * @param mixed $response_body
     * @param array $response_headers
     * @param array $request_headers
     * @return ShopifyApiException
     */
    public static function constructFrom(string $method, string $url, array $params, $response_body, array $response_headers, array $request_headers): self
    {
        return new static(json_encode(compact('method', 'url', 'params', 'response_body', 'response_headers', 'request_headers')));
    }

    public function __construct($message = "", $code = 500, Throwable $previous = null)
    {
        $properties = json_decode($message, true);
        if (is_array($properties)) {
            $this->method = (string)($properties['method'] ?? '');
            $this->url = (string)($properties['url'] ?? '');
            $this->params = (array)($properties['params'] ?? []);
            $this->response_body = ($properties['response_body'] ?? null);
            $this->response_headers = (array)($properties['response_headers'] ?? []);
            $this->request_headers = (array)($properties['request_headers'] ?? []);

            $error_message = $this->response_body['errors'] ?? $this->response_body;
            $error_message = is_array($error_message) ? json_encode($error_message) : $error_message;

            $message = $properties['message'] ?? $error_message ?? $properties['response_headers']['http_status_message'] ?? '';
            $code = $properties['code'] ?? $properties['response_headers']['http_status_code'] ?? $code;
        }
        parent::__construct($message, $code, $previous);
    }

    public function getRequest(): array
    {
        return ['method' => $this->method, 'url' => $this->url, 'params' => $this->params, 'headers' => $this->request_headers];
    }

    public function getResponse(): array
    {
        return ['body' => $this->response_body, 'headers' => $this->response_headers];
    }

    public function getAttributes(): array
    {
        $response = $this->getResponse();
        $response['headers'] = array_intersect_key($response['headers'], array_flip([
            'http_status_code',
            'http_status_message',
            'retry-after',
            'x-shopify-shop-api-call-limit',
            'x-shopify-api-version',
            'content-type',
            'date',
        ]));
        $request = $this->getRequest();
        return compact('response', 'request');
    }
}
