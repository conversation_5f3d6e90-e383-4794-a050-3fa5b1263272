<?php
App::uses('ControllerTestCase', 'TestSuite');
App::uses('ShopifyPlaceOrderProvider', 'Shopify.Test/Provider');
App::uses('ShopifyController', 'Shopify.Controller');

/**
 * ShopifyController::placeOrder Test Case
 *
 * ! This hits the real API with real values.
 *
 * Run with the following console command:
 *
 * ./app/Console/cake test Shopify Controller/Shopify/ShopifyPlaceOrder --stderr
 *
 * @property ShopifyController|PHPUnit_Framework_MockObject_MockObject $controller
 * @property ShopifyPlaceOrderProvider $PlaceOrder
 */
class ShopifyPlaceOrderTest extends ControllerTestCase
{
    public $fixtures = [
        'app.user',
        'app.contactpersons',
        'app.contact',
        'app.manufacturer_retailer',
        'app.email_template',
        'app.user_email_template',
    ];

    public function __construct($name = NULL, array $data = array(), $dataName = '')
    {
        parent::__construct($name, $data, $dataName);

        // Set up that must happen before data providers
        $this->PlaceOrder = new ShopifyPlaceOrderProvider();
    }

    public function setUp()
    {
        parent::setUp();

        $this->controller = $this->generate('Shopify.Shopify', array(
            'methods' => ['_checkManUserToken'],
            'models' => array(
                'ManufacturerRetailer' => ['isCommissionRetailer'],
            ),
        ));
        $this->controller->startupProcess();
    }

    /**
     * @dataProvider providerSuccessfulOrder
     */
    public function testSuccessfulOrder(callable $requestCallback)
    {
        $this->setSession($this->PlaceOrder->defaultSession());
        $this->setIsCommissionRetailer(false);
        $request = $requestCallback();

        $this->_testAction('/placeOrder', array('method' => 'POST', 'data' => $request));

        $this->_assertRedirect('orderProcessing?id=');
    }

    /**
     * @dataProvider providerSuccessfulOrder
     */
    public function testSuccessfulSplitCartOrder(callable $requestCallback)
    {
        $this->setSession($this->PlaceOrder->splitCartSession());
        $this->setIsCommissionRetailer(false);
        $request = $requestCallback();

        $this->_testAction('/placeOrder', array('method' => 'POST', 'data' => $request));

        $this->_assertRedirect('orderProcessing?id=');
        //TODO $this->assertApiOrder($this->PlaceOrder->splitCartApiOrder());
        //TODO might need a different method of finding the api order
    }

    public function providerSuccessfulOrder()
    {
        return array(
            'In-Store Pickup' => array([$this->PlaceOrder, 'inStorePickupRequest']),
            'Ship To Store' => array([$this->PlaceOrder, 'shipToStoreRequest']),
            'Local Delivery instock' => array([$this->PlaceOrder, 'localDeliveryInStockRequest']),
            'Local Delivery nostock' => array([$this->PlaceOrder, 'localDeliveryNoStockRequest']),
            'Ship From Store' => array([$this->PlaceOrder, 'shipFromStoreRequest']),
        );
    }

    public function testShipToDoorOrder()
    {
        $this->setSession($this->PlaceOrder->defaultSession());
        $this->setIsCommissionRetailer(false);
        $request = $this->PlaceOrder->shipToDoorRequest();

        $this->_testAction('/placeOrder', array('method' => 'POST', 'data' => $request));

        $this->_assertRedirect('orderProcessing?id=');
        $this->assertApiOrder($this->PlaceOrder->defaultApiOrder());
    }

    public function testShipToDoorSplitCartOrder()
    {
        $this->setSession($this->PlaceOrder->splitCartSession());
        $this->setIsCommissionRetailer(false);
        $request = $this->PlaceOrder->shipToDoorRequest();

        $this->_testAction('/placeOrder', array('method' => 'POST', 'data' => $request));

        $this->_assertRedirect('orderProcessing?id=');
        //TODO $this->assertApiOrder($this->PlaceOrder->splitCartDirectApiOrder());
    }

    public function testVelofixOrder()
    {
        $this->setSession($this->PlaceOrder->velofixSession());
        $this->setIsCommissionRetailer(false);
        $request = $this->PlaceOrder->velofixRequest();

        $this->_testAction('/placeOrder', array('method' => 'POST', 'data' => $request));

        $this->_assertRedirect('orderProcessing?id=');
        $this->assertApiOrder($this->PlaceOrder->velofixApiOrder());
    }

    public function testVelofixSplitCartOrder()
    {
        $this->setSession($this->PlaceOrder->velofixSplitCartSession());
        $this->setIsCommissionRetailer(false);
        $request = $this->PlaceOrder->velofixRequest();

        $this->_testAction('/placeOrder', array('method' => 'POST', 'data' => $request));

        $this->_assertRedirect('orderProcessing?id=');
    }

    public function testCommissionRetailerOrder()
    {
        $this->setSession($this->PlaceOrder->commissionRetailerSession());
        $this->setIsCommissionRetailer(true);
        $request = $this->PlaceOrder->inStorePickupRequest();

        $this->_testAction('/placeOrder', array('method' => 'POST', 'data' => $request));

        $this->_assertRedirect('orderProcessing?id=');
    }

    public function testCommissionRetailerSplitCartOrder()
    {
        $this->setSession($this->PlaceOrder->commissionRetailerSplitCartSession());
        $this->setIsCommissionRetailer(true);
        $request = $this->PlaceOrder->inStorePickupRequest();

        $this->_testAction('/placeOrder', array('method' => 'POST', 'data' => $request));

        $this->_assertRedirect('orderProcessing?id=');
    }

    public function testFreeShipToDoorOrder()
    {
        $this->setSession($this->PlaceOrder->freeShipToDoorSession());
        $this->setIsCommissionRetailer(false);
        $request = $this->PlaceOrder->shipToDoorRequest();

        $this->_testAction('/placeOrder', array('method' => 'POST', 'data' => $request));

        $this->_assertRedirect('orderProcessing?id=');
        $this->assertApiOrder($this->PlaceOrder->freeApiOrder());
    }

    /**
     * @param array $session
     */
    protected function setSession(array $session)
    {
        $this->controller->Session->write('Shopify', $session);
        $this->controller
            ->expects($this->any())
            ->method('_checkManUserToken')
            ->will($this->returnValue($session['User']));
    }

    /**
     * @param bool $isCommission
     */
    protected function setIsCommissionRetailer($isCommission)
    {
        /** @var ManufacturerRetailer|PHPUnit_Framework_MockObject_MockObject $mock */
        $mock = $this->controller->ManufacturerRetailer;
        $mock
            ->expects($this->any())
            ->method('isCommissionRetailer')
            ->will($this->returnValue($isCommission));
    }

    /**
     * @param string $expected Response location basename
     */
    protected function _assertRedirect($expected)
    {
        $redirect = basename($this->controller->response->location());
        $this->assertContains($expected, $redirect);
    }

    protected function assertApiOrder($expected)
    {
        list($path, $orderNO) = explode('id=', basename($this->controller->response->location()), 2);

        $this->controller->Order->belongsTo('User');
        $user = $this->controller->Order->find('first', [
            'contain' => ['User'],
            'conditions' => ['Order.orderNO' => $orderNO],
            'fields' => ['User.api_key', 'User.secret_key', 'User.shop_url'],
        ]);
        $this->controller->Order->unbindModel(['belongsTo' => ['User']], false);

        $apiOrder = $this->_findApiOrder($orderNO, $user);

        list($expected, $apiOrder) = array_map([$this, '_removeUnstableApiOrderFields'], array($expected, $apiOrder));
        $this->assertEquals($expected, $apiOrder);
    }

    /**
     * @param $orderNO
     * @param $user
     * @return mixed
     */
    protected function _findApiOrder($orderNO, $user)
    {
        $orderList = array_filter(
            $this->controller->Shopify->getAllOrders($user['User']['api_key'], $user['User']['secret_key'], $user['User']['shop_url'], ['limit' => 1]),
            function($order) use ($orderNO) { return ((int)$orderNO === $order['order_number']); }
        );
        $this->assertCount(1, $orderList, "Order where 'order_number'=='{$orderNO}' not found in the Shopify API.");
        return current($orderList);
    }

    protected function _removeUnstableApiOrderFields($apiOrder)
    {
        unset($apiOrder['id']);
        unset($apiOrder['created_at']);
        unset($apiOrder['updated_at']);
        unset($apiOrder['number']);
        unset($apiOrder['token']);
        unset($apiOrder['name']);
        unset($apiOrder['processed_at']);
        unset($apiOrder['order_number']);
        unset($apiOrder['order_status_url']);
        unset($apiOrder['total_price_usd']);
        unset($apiOrder['admin_graphql_api_id']);

        list($apiOrder['line_items'], $apiOrder['shipping_lines']) = array_map(
            function($collection) {
                return array_map(function($item) {
                    unset($item['id']);
                    unset($item['admin_graphql_api_id']);
                    return $item;
                }, $collection);
            },
            array($apiOrder['line_items'], $apiOrder['shipping_lines'])
        );

        unset($apiOrder['customer']['updated_at']);
        unset($apiOrder['customer']['orders_count']);
        unset($apiOrder['customer']['total_spent']);
        unset($apiOrder['customer']['last_order_id']);
        unset($apiOrder['customer']['last_order_name']);
        unset($apiOrder['customer']['admin_graphql_api_id']);

        $apiOrder['note_attributes'] = array_map(function($note) {
            $note['value'] = str_replace(["\r\n", "\n", "\r"], "\n", $note['value']);
            return $note;
        }, $apiOrder['note_attributes']);

        return $apiOrder;
    }

}
