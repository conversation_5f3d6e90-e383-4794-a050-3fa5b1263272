<?php
App::uses('IntegrationTestCase', 'TestSuite');
App::uses('ShopifyController', 'Shopify.Controller');
App::uses('ManufacturerRetailer', 'Model');

/**
 * ShopifyController::stripePayment Test Case
 *
 * Run with the following console command:
 *
 * ./app/Console/cake test Shopify Controller/Shopify/ShopifyStripePayment --stderr
 *
 * @property ShopifyController|PHPUnit_Framework_MockObject_MockObject $controller
 * @see ShopifyController::stripePayment
 */
class ShopifyStripePaymentTest extends IntegrationTestCase
{
    const COURIER_ID = '92d35994331943d9a8363da4b109059d';

    const STRIPE_ACCOUNT_BRAND = 'acct_194DCmJKdns8gzMQ';
    const STRIPE_ACCOUNT_RETAILER = 'acct_17FVVxLBFYPhk3Kp';

    const STRIPE_PUBLISHABLE_KEY = 'pk_test_wiTsJxFby411xui9GhzZ7aVk';

    const AMOUNT = [
        'instore' => 101700,
        'local_delivery' => 102076,
        'shipFromStore' => 102453,
        'sellDirect' => 102453,
        'sellExclusive' => 50232,
    ];

    const FEE_AMOUNT = [
        'instore' => 4167,
        'local_delivery' => 4182,
        'shipFromStore' => 4197,
        'sellDirect' => 2148,
        'sellExclusive' => 0,
    ];

    const DISCOUNT_WITH_TAX_AMOUNT = 33900;

    public $fixtures = [
        'app.brand_staff_permission',
        'app.configuration',
        'app.error_detail',
        'app.manufacturer_retailer',
        'app.order',
        'app.order_comment',
        'app.order_customer_message',
        'app.product',
        'app.product_non_applicable_order_type',
        'app.product_state_fee',
        'app.product_title',
        'app.product_variant_option',
        'app.shipping_zone',
        'app.staff_permission',
        'app.stripe_user',
        'app.stripe_user_capability',
        'app.user',
        'app.warehouse',
        'app.warehouse_product',
    ];

    private $session = [];

    private array $paymentParamsHistory = [];

    public function setUp()
    {
        parent::setUp();

        $this->generate('Shopify.Shopify', [
            'methods' => [
                // Methods called in AppController::beforeFilter that add unnecessary dependencies
                '_headerNotifications', '_checkCron', 'beforeRender',
                '_getSellDirect',
                '_renderOutOfStockError',
            ],
            'components' => [
                'Stripe.Stripe' => [
                    'getStatementDescriptor',
                    'createPlatformCustomer',
                    'saveSetupIntent',
                    'initEcommercePaymentIntent',
                    '_filterPaymentMethodTypes',
                ],
            ],
        ]);

        $this->session = ['Shopify' => $this->defaultSession()];
        $this->session($this->session);
        $this->configRequest(['headers' => [
            'Accept' => 'application/json',
            'X-Requested-With' => 'XMLHttpRequest',
        ]]);

        $this->controller->expects($this->any())->method('_getSellDirect')->with(
            '8',
            'sellDirect',
            $this->session['Shopify']['shippingRates'][0]['amount'],
            $this->anything(),
            true
        )->willReturnCallback(function($brandId, $type, $shippingAmount, $shippingDiscount, $buyDirectOnly) {
            $total = static::AMOUNT['sellDirect'];
            if (Hash::get($this->session, 'Shopify.sellExclusiveResult')) {
                $total += static::AMOUNT['sellExclusive'];
            }
            $total_no_discount = $total + static::DISCOUNT_WITH_TAX_AMOUNT;

            return json_encode([
                'company_name' => 'My Local Brand',
                'currency' => 'CAD',
                'totalwithshipping' => format_number($total_no_discount / 100),
                'totalwithshippingdiscount' => ($shippingDiscount) ? format_number($total / 100) : null,
            ]);
        });

        /**
         * @var StripeComponent|PHPUnit_Framework_MockObject_MockObject $Stripe
         * @see StripeComponent::getStatementDescriptor()
         * @see StripeComponent::createPlatformCustomer()
         * @see StripeComponent::saveSetupIntent()
         * @see StripeComponent::initEcommercePaymentIntent()
         * @see StripeComponent::_filterPaymentMethodTypes()
         */
        $Stripe = $this->controller->Stripe;
        $Stripe->expects($this->atMost(1))->method('getStatementDescriptor')->willReturnCallback(
            function(string $stripe_account, string $default = ''): string {
                return mb_strtoupper(mb_substr($this->getAccountType($stripe_account) . ' descriptor', 0, 22));
            }
        );
        $Stripe->expects($this->atMost(1))->method('createPlatformCustomer')->willReturnCallback(
            function(?string $token, array $billing, array $shipping, string $customerId = null, string $stripe_account = null) {
                if (!$customerId) {
                    $customerId = 'cus_fake_new_' . $this->getAccountType($stripe_account);
                }
                if (!str_starts_with($customerId, 'cus_')) {
                    throw new InvalidArgumentException("id={$customerId}");
                }

                return \Stripe\Customer::constructFrom([
                    'id' => $customerId,
                    'email' => $shipping['email'],
                ]);
            }
        );
        $Stripe->expects($this->atMost(1))->method('saveSetupIntent')->willReturnCallback(function($id, $params, $options) {
            if (!$id) {
                $id = 'seti_fake_new_' . $this->getAccountType($options['stripe_account']);
            }
            if (!str_starts_with($id, 'seti_')) {
                throw new InvalidArgumentException("id={$id}");
            }

            return \Stripe\SetupIntent::constructFrom([
                'id' => $id,
                'client_secret' => $id . '_secret',
                'payment_method_types' => $params['payment_method_types'],
            ]);
        });
        $this->paymentParamsHistory = [];
        $Stripe->expects($this->atMost(2))->method('initEcommercePaymentIntent')->willReturnCallback(function($id, $amount, $currency, $params, $options) {
            if (!$id) {
                $id = 'pi_fake_new_' . $this->getAccountType($options['stripe_account']);
            }
            if (!str_starts_with($id, 'pi_')) {
                throw new InvalidArgumentException("id={$id}");
            }

            $this->paymentParamsHistory[] = ['amount' => (int)round($amount * 100), 'currency' => $currency] + $params;

            return \Stripe\PaymentIntent::constructFrom([
                'id' => $id,
                'client_secret' => $id . '_secret',
                'payment_method_types' => $params['payment_method_types'],
            ]);
        });
        $Stripe->expects($this->atMost(1))->method('_filterPaymentMethodTypes')->willReturnArgument(0);
    }

    /**
     * @dataProvider providerShippingTypes
     */
    public function testStripePayment($url, $request, $expected)
    {
        $this->post($url, $request);
        $actual = $this->_response->body();

        $expected = json_encode($expected);
        $this->assertJsonStringEqualsJsonString($expected, $actual);
    }

    /**
     * @dataProvider providerShippingTypes
     */
    public function testNoDiscountStripePayment($url, $request, $expected)
    {
        $this->_removeDiscountsFromSession("Shopify.retailerInfo.{$request['type']}.{$request['retailer_id']}");
        $expected['amount'] += static::DISCOUNT_WITH_TAX_AMOUNT;

        $expected['amount_format'] = $expected['currency'] . ' ' . number_format($expected['amount'] / 100, 2);

        $this->post($url, $request);
        $actual = $this->_response->body();

        $expected = json_encode($expected);
        $this->assertJsonStringEqualsJsonString($expected, $actual);
    }

    /**
     * @dataProvider providerShippingTypes
     */
    public function testSplitCartStripePayment($url, $request, $expected)
    {
        $this->_addSellExclusiveToSession();
        $expected['stripe_payment_types'] = ($request['type'] === 'sellDirect') ? ['card', 'klarna', 'affirm'] : ['card'];
        $expected['amount'] += static::AMOUNT['sellExclusive'];

        $expected['amount_format'] = $expected['currency'] . ' ' . number_format($expected['amount'] / 100, 2);

        $this->post($url, $request);
        $actual = $this->_response->body();

        $expected = json_encode($expected);
        $this->assertJsonStringEqualsJsonString($expected, $actual);
    }

    /**
     * @dataProvider providerShippingTypes
     */
    public function testSplitCartNoDiscountStripePayment($url, $request, $expected)
    {
        $this->_addSellExclusiveToSession();
        $expected['stripe_payment_types'] = ($request['type'] === 'sellDirect') ? ['card', 'klarna', 'affirm'] : ['card'];
        $expected['amount'] += static::AMOUNT['sellExclusive'];
        $this->_removeDiscountsFromSession("Shopify.retailerInfo.{$request['type']}.{$request['retailer_id']}");
        $expected['amount'] += static::DISCOUNT_WITH_TAX_AMOUNT;

        $expected['amount_format'] = $expected['currency'] . ' ' . number_format($expected['amount'] / 100, 2);

        $this->post($url, $request);
        $actual = $this->_response->body();

        $expected = json_encode($expected);
        $this->assertJsonStringEqualsJsonString($expected, $actual);
    }

    /**
     * @dataProvider providerPaymentParams
     */
    public function testPaymentParams($url, $request, $response)
    {
        $this->post($url, $request);
        $actual = $this->paymentParamsHistory;

        $expectedPaymentParams = [
            'amount' => $response['amount'],
            'currency' => 'CAD',
            'description' => 'Charge for Your Order',
            'metadata' => [],
            'payment_method_types' => ['card', 'klarna', 'affirm'],
            'shipping' => [
                'address' => [
                    'line1' => '2400 Nipigon Rd.',
                    'line2' => null,
                    'city' => 'Thunder Bay',
                    'state' => 'Ontario',
                    'country' => 'CA',
                    'postal_code' => 'P7C 4W1',
                ],
                'name' => 'Aron Schmidt',
                'phone' => '(*************',
            ],
            'application_fee_amount' => static::FEE_AMOUNT[$request['type']],
            'capture_method' => 'manual',
        ];
        if ($response['stripe_payment_account'] !== static::STRIPE_ACCOUNT_RETAILER) {
            $expectedPaymentParams['customer'] = 'cus_fake_new_brand';
        }
        if ($response['stripe_payment_account'] !== static::STRIPE_ACCOUNT_BRAND) {
            $expectedPaymentParams = array_merge($expectedPaymentParams, [
                'statement_descriptor' => 'BRAND DESCRIPTOR',
                'statement_descriptor_suffix' => 'BRAND DESCRIPTOR',
            ]);
        }

        $expected = [$expectedPaymentParams];
        $this->assertEquals($expected, $actual);
    }

    public function providerPaymentParams(): array
    {
        return array_filter($this->providerShippingTypes(), function($case) {
            return empty($case['url']['filter']);
        });
    }

    /**
     * @dataProvider providerShippingTypes
     */
    public function testReuseStripePaymentIntent($url, $request, $expected)
    {
        if ($request['type'] === 'sellDirect') {
            $expected['stripe_card_secret'] = str_replace('_new_', '_session_', $expected['stripe_card_secret']);
        }
        $expected['stripe_payment_secret'] = str_replace('_new_', '_session_', $expected['stripe_payment_secret']);

        $this->session = Hash::insert($this->session, 'Shopify.accountPaymentIds', [
            static::STRIPE_ACCOUNT_RETAILER => 'pi_fake_session_retailer',
            static::STRIPE_ACCOUNT_BRAND => 'pi_fake_session_brand',
        ]);
        $this->session($this->session);

        $this->post($url, $request);
        $actual = $this->_response->body();

        $expected = json_encode($expected);
        $this->assertJsonStringEqualsJsonString($expected, $actual);
    }

    /**
     * @dataProvider providerShippingTypes
     */
    public function testReuseStripeSetupIntent($url, $request, $expected)
    {
        if ($request['type'] !== 'sellDirect') {
            $expected['stripe_card_secret'] = str_replace('_new_', '_session_', $expected['stripe_card_secret']);
        }

        $this->session = Hash::insert($this->session, 'Shopify.accountSetupId', [
            '' => 'seti_fake_session_platform',
            static::STRIPE_ACCOUNT_BRAND => 'pi_fake_session_brand',
        ]);
        $this->session($this->session);

        $this->post($url, $request);
        $actual = $this->_response->body();

        $expected = json_encode($expected);
        $this->assertJsonStringEqualsJsonString($expected, $actual);
    }

    public function providerShippingTypes(): array
    {
        $requestDefaults = [
            'retailer_id' => '7',
            //'type' => '',
            'courier_option' => '',
        ];
        $responseDefaults = [
            'success' => true,
            'stripe_platform_key' => static::STRIPE_PUBLISHABLE_KEY,
            'stripe_api_version' => STRIPE_API_VERSION,
            'stripe_payment_types' => ['card', 'klarna', 'affirm'],
            'stripe_payment_account' => static::STRIPE_ACCOUNT_RETAILER,
            'stripe_payment_secret' => 'pi_fake_new_retailer_secret',
            'stripe_card_account' => null,
            'stripe_card_secret' => 'seti_fake_new_platform_secret',
            'rname' => 'Localhost Code Shop',
            //'amount' => 0,
            'currency' => 'CAD',
            //'amount_format' => 'CAD 0.00',
        ];

        $cases = [
            'instore' => [
                'request' => array_merge($requestDefaults, [
                    'type' => 'instore',
                ]),
                'expected' => array_merge($responseDefaults, [
                    'amount' => static::AMOUNT['instore'],
                ]),
            ],
            'local_delivery' => [
                'request' => array_merge($requestDefaults, [
                    'type' => 'local_delivery',
                ]),
                'expected' => array_merge($responseDefaults, [
                    'amount' => static::AMOUNT['local_delivery'],
                ]),
            ],
            'shipFromStore' => [
                'request' => array_merge($requestDefaults, [
                    'type' => 'shipFromStore',
                ]),
                'expected' => array_merge($responseDefaults, [
                    'amount' => static::AMOUNT['shipFromStore'],
                ]),
            ],
            'sellDirect' => [
                'request' => array_merge($requestDefaults, [
                    'retailer_id' => '8',
                    'type' => 'sellDirect',
                    'courier_option' => ['id' => static::COURIER_ID],
                ]),
                'expected' => array_merge($responseDefaults, [
                    'stripe_payment_types' => ['card', 'klarna', 'affirm'],
                    'stripe_payment_account' => static::STRIPE_ACCOUNT_BRAND,
                    'stripe_payment_secret' => 'pi_fake_new_brand_secret',
                    'stripe_card_account' => static::STRIPE_ACCOUNT_BRAND,
                    'stripe_card_secret' => 'pi_fake_new_brand_secret',
                    'rname' => 'My Local Brand',
                    'amount' => static::AMOUNT['sellDirect'],
                ]),
            ],
        ];
        $cases['sellDirect_no_courier'] = $cases['sellDirect'];
        $cases['sellDirect_no_courier']['request']['courier_option'] = '';

        $cases = array_map(function($case) {
            $case['expected']['amount_format'] = $case['expected']['currency'] . ' ' . number_format($case['expected']['amount'] / 100, 2);

            return $case;
        }, $cases);

        $urls = [
            ['plugin' => 'Shopify', 'controller' => 'Shopify', 'action' => 'stripePayment'],
            ['plugin' => 'shopify', 'controller' => 'shopify', 'action' => 'stripePayment', 'filter' => 'payment_methods'],
        ];

        return array_reduce($urls, function($map, $url) use ($cases) {
            $urlName = $url['filter'] ?? $url['action'];

            $paymentIntentIdFields = [
                'stripe_payment_secret',
                'stripe_card_secret',
            ];
            foreach ($cases as $name => $case) {
                if ($urlName === 'payment_methods') {
                    $case['expected'] = array_merge($case['expected'], array_fill_keys($paymentIntentIdFields, null));
                }

                $map += [($urlName . ' ' . $name) => compact('url') + $case];
            }

            return $map;
        }, []);
    }

    /**
     * @dataProvider providerCommissionShippingTypes
     */
    public function testCommissionStripePayment($url, $request, $expected)
    {
        $this->controller->ManufacturerRetailer->updateAllJoinless(
            ['pricingtierid' => null, 'is_commission_tier' => true],
            ['user_id' => '8', 'retailer_id' => '7']
        );

        $this->post($url, $request);
        $actual = $this->_response->body();

        $expected = json_encode(array_merge($expected, [
            'stripe_payment_types' => ['card', 'klarna', 'affirm'],
            'stripe_payment_account' => static::STRIPE_ACCOUNT_BRAND,
            'stripe_payment_secret' => ($expected['stripe_payment_secret']) ? 'pi_fake_new_brand_secret' : null,
            'stripe_card_account' => static::STRIPE_ACCOUNT_BRAND,
            'stripe_card_secret' => ($expected['stripe_card_secret']) ? 'pi_fake_new_brand_secret' : null,
            'rname' => 'My Local Brand',
        ]));
        $this->assertJsonStringEqualsJsonString($expected, $actual);
    }

    /**
     * @dataProvider providerCommissionShippingTypes
     */
    public function testCommissionSplitCartStripePayment($url, $request, $expected)
    {
        $this->controller->ManufacturerRetailer->updateAllJoinless(
            ['pricingtierid' => null, 'is_commission_tier' => true],
            ['user_id' => '8', 'retailer_id' => '7']
        );

        $this->_addSellExclusiveToSession();
        $expected['amount'] += static::AMOUNT['sellExclusive'];

        $expected['amount_format'] = $expected['currency'] . ' ' . number_format($expected['amount'] / 100, 2);

        $this->post($url, $request);
        $actual = $this->_response->body();

        $expected = json_encode(array_merge($expected, [
            'stripe_payment_types' => ['card'],
            'stripe_payment_account' => static::STRIPE_ACCOUNT_BRAND,
            'stripe_payment_secret' => ($expected['stripe_payment_secret']) ? 'pi_fake_new_brand_secret' : null,
            'stripe_card_account' => static::STRIPE_ACCOUNT_BRAND,
            'stripe_card_secret' => ($expected['stripe_card_secret']) ? 'seti_fake_new_brand_secret' : null,
            'rname' => 'My Local Brand',
        ]));
        $this->assertJsonStringEqualsJsonString($expected, $actual);
    }

    public function providerCommissionShippingTypes(): array
    {
        return array_filter($this->providerShippingTypes(), function($case) {
            return $case['request']['type'] !== 'sellDirect';
        });
    }

    /**
     * @dataProvider providerOutOfStockError
     */
    public function testOutOfStockError(callable $setup, array $request, array $expectedViewItems)
    {
        $this->controller->WarehouseProductReservation->WarehouseProduct->updateAllJoinless(['WarehouseProduct.quantity' => 0]);
        call_user_func($setup, $this);

        $this->controller->expects($this->once())->method('_renderOutOfStockError')
            ->willReturnCallback(fn(array $items): string => json_encode($items));

        $this->post('/stripe_payment', $request);

        $responseBody = $this->_response->body();
        $this->assertJsonStringEqualsJsonString(json_encode(['redirect' => Router::url(['plugin' => 'Shopify', 'controller' => 'Shopify', 'action' => 'error'], true)]), $responseBody);

        $errorCode = $this->_requestSession->read('error.code');
        $this->assertRegExp('/' . Router::UUID . '/', $errorCode, 'Error code is not a UUID');

        $errorDetails = $this->controller->ErrorDetails->find('first', [
            'conditions' => ['ErrorDetails.uuid' => $errorCode],
            'fields' => ['title', 'message'],
        ]);
        $expectedErrorDetails = [
            'ErrorDetails' => [
                'title' => 'Out of Stock',
                'message' => json_encode($expectedViewItems),
            ],
        ];
        $this->assertEquals($expectedErrorDetails, $errorDetails, 'Error details are incorrect');
    }

    public function providerOutOfStockError(): array
    {
        $request = [
            'retailer_id' => '7',
            'type' => 'instore',
            'courier_option' => '',
            'paymentMethodSubtype' => 'card',
        ];
        $expectedViewItems = Hash::insert($this->defaultSession()['cart']['items'], '{n}.is_oversold', true);

        return [
            'sellDirect' => [
                'setup' => function(self $test) {
                },
                'request' => array_merge($request, [
                    'retailer_id' => '8',
                    'type' => 'sellDirect',
                ]),
                'expectedViewItems' => $expectedViewItems,
            ],
            'out_of_stock_retailer' => [
                'setup' => function(self $test) {
                    $retailerInfo = json_decode(Hash::get($test->session, 'Shopify.retailerInfo.instore.7'), true);
                    $retailerInfo['type'] = 'nonstock';
                    $test->session = Hash::insert($test->session, 'Shopify.retailerInfo.instore.7', json_encode($retailerInfo));
                    $test->session($test->session);
                },
                'request' => $request,
                'expectedViewItems' => $expectedViewItems,
            ],
            'commission_retailer' => [
                'setup' => function(self $test) {
                    $test->controller->ManufacturerRetailer->updateAllJoinless(['is_commission_tier' => true], ['user_id' => '8', 'retailer_id' => '7']);
                },
                'request' => $request,
                'expectedViewItems' => $expectedViewItems,
            ],
            'split_cart_brand' => [
                'setup' => function(self $test) {
                    $test->_addSellExclusiveToSession();
                },
                'request' => $request,
                'expectedViewItems' => array_merge(
                    Hash::insert($expectedViewItems, '{n}.is_oversold', false),
                    Hash::insert($this->sellExclusiveCartItems(), '{n}.is_oversold', true)
                ),
            ],
        ];
    }

    public function testFreeSellDirect()
    {
        $this->_setupFreeSession('Shopify.retailerInfo.sellDirect.8');

        $this->post('/stripe_payment', [
            'retailer_id' => '8',
            'type' => 'sellDirect',
            'courier_option' => '',
        ]);
        $actual = $this->_response->body();

        $expected = json_encode([
            'success' => true,
            'stripe_platform_key' => static::STRIPE_PUBLISHABLE_KEY,
            'stripe_api_version' => STRIPE_API_VERSION,
            'stripe_payment_types' => ['card'],
            'stripe_payment_account' => static::STRIPE_ACCOUNT_BRAND,
            'stripe_payment_secret' => null,
            'stripe_card_account' => static::STRIPE_ACCOUNT_BRAND,
            'stripe_card_secret' => null,
            'rname' => 'My Local Brand',
            'amount' => 0,
            'currency' => 'CAD',
            'amount_format' => 'CAD 0.00',
        ]);
        $this->assertJsonStringEqualsJsonString($expected, $actual);
    }

    public function testNoRetailerIsNoOp()
    {
        $this->post('/stripe_payment', [
            'retailer_id' => '',
            'type' => 'sellDirect',
            'courier_option' => '',
        ]);
        $actual = $this->_response->body();
        $this->assertAttributeEquals('Bad Request', 'name', json_decode($actual));
    }

    protected function _addSellExclusiveToSession()
    {
        $this->session['Shopify']['cart']['items'] = array_merge($this->session['Shopify']['cart']['items'], $this->sellExclusiveCartItems());
        $this->session = Hash::insert($this->session, 'Shopify.sellExclusiveResult', $this->sellExclusiveResult());
        $this->session($this->session);
    }

    protected function _removeDiscountsFromSession($retailerInfoKey)
    {
        $retailerInfo = json_decode(Hash::get($this->session, $retailerInfoKey), true);
        $retailerInfo = array_diff_key($retailerInfo, array_flip([
            'totalamountdiscount',
            'totalamountdiscount_localdelivery',
            'totalwithshippingdiscount',
        ]));
        $this->session = Hash::insert($this->session, $retailerInfoKey, json_encode($retailerInfo));

        $this->session = Hash::remove($this->session, 'Shopify.sellExclusiveResult.totalamountdiscount');
        $this->session = Hash::remove($this->session, 'Shopify.shippingRates.{n}.discount');

        $this->session($this->session);
    }

    protected function _setupFreeSession($retailerInfoKey)
    {
        $retailerInfo = json_decode(Hash::get($this->session, $retailerInfoKey), true);
        $retailerInfo['totalamountdiscount'] = '0.00';
        $retailerInfo['totalamountdiscount_localdelivery'] = '0.00';
        $retailerInfo['totalwithshippingdiscount'] = '0.00';
        $this->session = Hash::insert($this->session, $retailerInfoKey, json_encode($retailerInfo));

        $this->session = Hash::remove($this->session, 'Shopify.shippingRates');

        $this->session($this->session);
    }

    protected function defaultSession()
    {
        return [
            'domainUrl' => 'https://aron-shipearly-2.myshopify.com',
            'token' => '5666ffac-5964-4f7a-9048-1cc291c2de43',
            'cart' => [
                'items' => [
                    [
                        'id' => 17532934023,
                        'properties' => null,
                        'quantity' => 1,
                        'variant_id' => 17532934023,
                        'key' => '17532934023:4e1ff7977e78345c262af31534df7b36',
                        'title' => 'Super Bike - Red',
                        'price' => 120000,
                        'original_price' => 120000,
                        'discounted_price' => 120000,
                        'line_price' => 120000,
                        'original_line_price' => 120000,
                        'total_discount' => 0,
                        'discounts' => [],
                        'sku' => 'BICYCLE-1',
                        'grams' => 4536,
                        'vendor' => 'aron-shipearly',
                        'taxable' => true,
                        'product_id' => 5572298951,
                        'gift_card' => false,
                        'url' => '/products/super-bike?variant=17532934023',
                        'image' => 'https://cdn.shopify.com/s/files/1/1205/5522/products/RedBike.jpg?v=1513192497',
                        'handle' => 'super-bike',
                        'requires_shipping' => true,
                        'product_type' => 'Bikes',
                        'product_title' => 'Super Bike',
                        'product_description' => 'Elegant, Feminine, &amp; Independent This is the classic steel road bike you\'ve searched for but struggled to find, until now. The upright riding position makes it very comfortable for everyday use, and the low step-through frame allows for easy on and off. If there ever was a frame designed for trips to the flower market or picking up fresh baked bread, this is it. AVAILABLE IN A SELECTION OFÂ COLORS: Red Blue Yellow Green Black ',
                        'variant_title' => 'Red',
                        'variant_options' => [0 => 'Red'],
                    ],
                ],
            ],
            'User' => ['User' => ['id' => '8', 'company_name' => 'My Local Brand']],
            'retailerInfo' => [
                'sellDirect' => [
                    8 => json_encode([
                        'id' => '8',
                        'uuid' => '5666ffac-5964-4f7a-9048-1cc291c2de43',
                        'email_address' => '<EMAIL>',
                        'password' => '3be70f15222270d5c843692c1247b57b',
                        'company_name' => 'My Local Brand',
                        'company_description' => '',
                        'company_code' => '',
                        'status' => 'Active',
                        'Branch' => '0',
                        'user_type' => 'Manufacturer',
                        'minOrderAmount' => '0',
                        'avatar' => null,
                        'shipping_infographic' => null,
                        'flatshipping' => '0',
                        'site_type' => 'Shopify',
                        'shop_url' => 'aron-shipearly-2.myshopify.com',
                        'fbpixelId' => '904599178183609',
                        'currency_code' => 'CAD',
                        'api_key' => 'e8d27b84d0d3e8549cd2faa84309c75d',
                        'secret_key' => '723213fc099cd21a806692052d3c8949',
                        'inventory_apiuser' => '',
                        'inventory_password' => '',
                        'Inventory_Emp_ID' => null,
                        'Inventory_Reg_ID' => null,
                        'Inventory_Store_ID' => null,
                        'defaultTax' => null,
                        'otherInventory' => null,
                        'created' => '2015-12-08 17:05:00',
                        'modified' => '2017-04-20 22:33:27',
                        'address' => '123 Localhost St._,_',
                        'store_timing' => '',
                        'country_id' => '39',
                        'state_id' => '611',
                        'city' => 'Thunder Bay',
                        'zipcode' => 'P7C 4W1',
                        'latitude' => '48.407966',
                        'longitude' => '-89.2565708',
                        'inventory_type' => null,
                        'permission' => '',
                        'instore' => true,
                        'shipment' => true,
                        'shipment_type' => '',
                        'shipment_option' => '',
                        'free_shipping' => null,
                        'shiptostore_free_shipping' => '1000000000',
                        'shiptostore_tax' => '0',
                        'local_delivery' => '1',
                        'local_delivery_shipping' => '3.33',
                        'local_delivery_title' => '',
                        'subscription_plan' => 'core',
                        'coupon_code' => null,
                        'store_associate_pin' => null,
                        'setup_status' => true,
                        'revenue_model' => null,
                        'retailer_default_amount' => '0.99',
                        'store_associate_default_amount' => '0.00',
                        'in-store_radius' => '50',
                        'ship_from_store_radius' => '250',
                        'in-store-pickup-radius' => null,
                        'admin_sell_direct' => '1',
                        'sell_direct_percentage' => null,
                        'sell_direct' => '1',
                        'enable_velofix' => true,
                        'brand_revenue_model' => '2',
                        'brand_direct_default_amount' => '0.99',
                        'splitpayment_percentage' => '2.00',
                        'brand_abandon_cart' => true,
                        'brand_abandon_cart_message' => 'Abandoned cart message.',
                        'brand_abandon_cart_subject' => 'Abandoned cart subject',
                        'vend_access_token_expires' => null,
                        'vend_refresh_access_token' => null,
                        'ship_to_door_message' => '',
                        'ship_to_door_backorder_message' => '',
                        'instore_pickup_message' => '',
                        'ship_to_store_message' => '',
                        'ship_to_store_backorder_message' => '',
                        'ship_to_store_noretailer_message' => '',
                        'local_delivery_in_stock_message' => '',
                        'local_delivery_no_stock_message' => '',
                        'local_delivery_backorder_message' => '',
                        'local_delivery_noretailer_message' => '',
                        'state' => 'Ontario',
                        'product' => [
                            17532934023 => [
                                'inventory' => 6,
                                'id' => '21',
                                'inventoryId' => 17532934023,
                                'taxcode' => '',
                                'currency' => 'USD',
                                'unformatedprice' => 1200,
                                'price' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 1,200.00</span>',
                                'unitprice' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 1,200.00</span>',
                                'unformatedunitprice' => '1200.00',
                                'qty' => 1,
                                'weight' => 4536,
                                'isTax' => true,
                                'assembly_option' => '1',
                                'unformatedpricediscount' => 900,
                                'pricediscount' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 900.00</span>',
                                'unitpricediscount' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 900.00</span>',
                                'unformatedunitpricediscount' => 900,
                                'tax' => 156,
                                'taxdiscount' => 117,
                            ],
                            'shopifyshippingtax' => 0.13000000000000000444089209850062616169452667236328125,
                        ],
                        'currency' => 'CAD',
                        'price' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 1,200.00</span>',
                        'priceformat' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 1,200.00</span>',
                        'pricediscount' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 900.00</span>',
                        'priceformatdiscount' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 900.00</span>',
                        'shippingAmount' => 6.660000000000000142108547152020037174224853515625,
                        'shippingAmountFormat' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 6.66</span>',
                        'shipping_tax_option' => '1',
                        'taxamt' => 156.865800000000007230482879094779491424560546875,
                        'taxamtdiscount' => 117.8657999999999930196281638927757740020751953125,
                        'shippingTax' => 0.86580000000000001403321903126197867095470428466796875,
                        'taxamtwithShipping' => 156.865800000000007230482879094779491424560546875,
                        'totalproductamount' => 1200,
                        'totalamount' => 1356.8699999999998908606357872486114501953125,
                        'taxamtwithShippingDiscount' => 117.8657999999999930196281638927757740020751953125,
                        'totalproductamountdiscount' => 900,
                        'totalamountdiscount' => 1017.8700000000000045474735088646411895751953125,
                        'discountAmount' => 300,
                        'discountAmountFormat' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 300.00</span>',
                        'tax_included' => false,
                        'taxformatwithShipping' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 156.87</span>',
                        'totalwithshipping' => 1363.529999999999972715158946812152862548828125,
                        'taxformatwithShippingDiscount' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 117.87</span>',
                        'totalwithshippingdiscount' => 1024.529999999999972715158946812152862548828125,
                        'totalformat' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 1,356.87</span>',
                        'totalwithshippingformat' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 1,363.53</span>',
                        'totalformatdiscount' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 1,017.87</span>',
                        'totalwithshippingformatdiscount' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 1,024.53</span>',
                        'formatAddress' => '<b>My Local Brand</b><br/>123 Localhost St.<br/>Thunder Bay, Ontario<br/>Canada, P7C 4W1<br/>Ph:807-123-1234',
                    ]),
                ],
                'instore' => [
                    7 => json_encode([
                        'retailer_id' => '7',
                        'company_name' => 'Localhost Code Shop',
                        'instore' => true,
                        'shipment' => true,
                        'shipment_type' => '0',
                        'shipment_option' => '',
                        'free_shipping' => '10000',
                        'shiptostore_tax' => '1',
                        'defaultTax' => '13.000000',
                        'currency' => 'CAD',
                        'address' => '955 Oliver Rd_,_',
                        'inventory_type' => 'lightspeed_cloud',
                        'site_type' => null,
                        'city' => 'Thunder Bay',
                        'store_timing' => '{"currentTime":"<span><b><a href=\\"\\" class=\\"tooltip tooltip7\\" id=\\"tooltip7\\" onclick=\\"return false;\\" style=\\"display: inline-block;color: blue; opacity:1; font-weight: inherit;font-size: inherit; position: relative; z-index: 1\\"><span>9:00 AM &nbsp;-&nbsp; 7:00 PM<\\/span><\\/a><\\/b><\\/span>","timinglist":"<div style=\\"width: 220px;\\"><span style=\\"display: inline-block; width: 15%; text-align: left;\\"> Mon <\\/span> <span style=\\"display: inline-block; width: 83%; text-align: left;\\"> &nbsp;-&nbsp;&nbsp;9:00 AM &nbsp;-&nbsp; 7:00 PM<\\/span><br\\/><span style=\\"display: inline-block; width: 15%; text-align: left;\\"> Tue <\\/span> <span style=\\"display: inline-block; width: 83%; text-align: left;\\"> &nbsp;-&nbsp;&nbsp;9:00 AM &nbsp;-&nbsp; 7:00 PM<\\/span><br\\/><span style=\\"display: inline-block; width: 15%; text-align: left;\\"> Wed <\\/span> <span style=\\"display: inline-block; width: 83%; text-align: left;\\"> &nbsp;-&nbsp;&nbsp;9:00 AM &nbsp;-&nbsp; 7:00 PM<\\/span><br\\/><span style=\\"display: inline-block; width: 15%; text-align: left;\\"> Thu <\\/span> <span style=\\"display: inline-block; width: 83%; text-align: left;\\"> &nbsp;-&nbsp;&nbsp;9:00 AM &nbsp;-&nbsp; 7:00 PM<\\/span><br\\/><span style=\\"display: inline-block; width: 15%; text-align: left;\\"> Fri <\\/span> <span style=\\"display: inline-block; width: 83%; text-align: left;\\"> &nbsp;-&nbsp;&nbsp;9:00 AM &nbsp;-&nbsp; 7:00 PM<\\/span><br\\/><span style=\\"display: inline-block; width: 15%; text-align: left;\\"> Sat <\\/span> <span style=\\"display: inline-block; width: 83%; text-align: left;\\"> &nbsp;-&nbsp;&nbsp;9:00 AM &nbsp;-&nbsp; 7:00 PM<\\/span><br\\/><span style=\\"display: inline-block; width: 15%; text-align: left;\\"> Sun <\\/span> <span style=\\"display: inline-block; width: 83%; text-align: left;\\"> &nbsp;-&nbsp;&nbsp;9:00 AM &nbsp;-&nbsp; 7:00 PM<\\/span><br\\/><\\/div>"}',
                        'state_id' => '611',
                        'country_id' => '39',
                        'zipcode' => 'P7B 5E1',
                        'longitude' => '-89.2616196',
                        'latitude' => '48.4235761',
                        'velofix' => false,
                        'velofix_selldirect_result' => [],
                        'type' => 'stock',
                        'local_delivery_shipping' => '3.33',
                        'telephone' => '************',
                        'formatAddress' => '<b>Localhost Code Shop</b><br/>955 Oliver Rd<br/>Thunder Bay, Ontario<br/>Canada, P7B 5E1<br/>Ph:************',
                        'state' => 'Ontario',
                        'distance' => 1.770000000000000017763568394002504646778106689453125,
                        'rating' => 6,
                        'product' => [
                            17532934023 => [
                                'id' => '21',
                                'inventoryId' => 76,
                                'inventory' => 710,
                                'taxcode' => '',
                                'currency' => 'CAD',
                                'qty' => 1,
                                'unformatedunitprice' => '1200.00',
                                'unformatedprice' => 1200,
                                'unitprice' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 1,200.00</span>',
                                'price' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 900.00</span>',
                                'unformatedunitpricediscount' => 900,
                                'unformatedpricediscount' => 900,
                                'unitpricediscount' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 900.00</span>',
                                'pricediscount' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 900.00</span>',
                                'isTax' => 'true',
                                'TaxCategoryID' => 0,
                                'taxClassID' => 11,
                                'tax' => 156,
                                'taxdiscount' => 117,
                                'assembly_option' => '1',
                                'brand_id' => '8',
                                'local_delivery_shipping' => '3.33',
                                'local_delivery_radius' => '1000',
                            ],
                        ],
                        'totalproductamount' => 1200,
                        'priceformat' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 1,200.00</span>',
                        'price' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 1,200.00</span>',
                        'totalproductamountdiscount' => 900,
                        'priceformatdiscount' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 900.00</span>',
                        'discountAmount' => 300,
                        'discountAmountFormat' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 300.00</span>',
                        'shippingAmount' => 0,
                        'shippingAmountFormat' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 0.00</span>',
                        'shippingAmount_localdelivery' => 3.3300000000000000710542735760100185871124267578125,
                        'shippingAmountFormat_localdelivery' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 3.33</span>',
                        'shippingTax' => 0,
                        'shippingTax_localdelivery' => 0.429999999999999993338661852249060757458209991455078125,
                        'taxamt' => 156,
                        'taxformat' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 156.00</span>',
                        'taxamt_localdelivery' => 156.43000000000000682121026329696178436279296875,
                        'taxformat_localdelivery' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 156.43</span>',
                        'tax_included' => false,
                        'totalamount' => 1356,
                        'totalamount_localdelivery' => 1359.759999999999990905052982270717620849609375,
                        'totalformat' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 1,356.00</span>',
                        'totalformat_localdelivery' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 1,359.76</span>',
                        'taxamtdiscount' => 117,
                        'taxformatdiscount' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 117.00</span>',
                        'taxamtdiscount_localdelivery' => 117.43000000000000682121026329696178436279296875,
                        'taxformatdiscount_localdelivery' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 117.43</span>',
                        'totalamountdiscount' => 1017,
                        'totalamountdiscount_localdelivery' => 1020.759999999999990905052982270717620849609375,
                        'totalformatdiscount' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 1,017.00</span>',
                        'totalformatdiscount_localdelivery' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 1,020.76</span>',
                    ]),
                ],
                'local_delivery' => [
                    7 => json_encode([
                        'retailer_id' => '7',
                        'company_name' => 'Localhost Code Shop',
                        'instore' => true,
                        'shipment' => true,
                        'shipment_type' => '0',
                        'shipment_option' => '',
                        'free_shipping' => '10000',
                        'shiptostore_tax' => '1',
                        'defaultTax' => '13.000000',
                        'currency' => 'CAD',
                        'address' => '955 Oliver Rd_,_',
                        'inventory_type' => 'lightspeed_cloud',
                        'site_type' => null,
                        'city' => 'Thunder Bay',
                        'store_timing' => '{"currentTime":"<span><b><a href=\\"\\" class=\\"tooltip tooltip7\\" id=\\"tooltip7\\" onclick=\\"return false;\\" style=\\"display: inline-block;color: blue; opacity:1; font-weight: inherit;font-size: inherit; position: relative; z-index: 1\\"><span>9:00 AM &nbsp;-&nbsp; 7:00 PM<\\/span><\\/a><\\/b><\\/span>","timinglist":"<div style=\\"width: 220px;\\"><span style=\\"display: inline-block; width: 15%; text-align: left;\\"> Mon <\\/span> <span style=\\"display: inline-block; width: 83%; text-align: left;\\"> &nbsp;-&nbsp;&nbsp;9:00 AM &nbsp;-&nbsp; 7:00 PM<\\/span><br\\/><span style=\\"display: inline-block; width: 15%; text-align: left;\\"> Tue <\\/span> <span style=\\"display: inline-block; width: 83%; text-align: left;\\"> &nbsp;-&nbsp;&nbsp;9:00 AM &nbsp;-&nbsp; 7:00 PM<\\/span><br\\/><span style=\\"display: inline-block; width: 15%; text-align: left;\\"> Wed <\\/span> <span style=\\"display: inline-block; width: 83%; text-align: left;\\"> &nbsp;-&nbsp;&nbsp;9:00 AM &nbsp;-&nbsp; 7:00 PM<\\/span><br\\/><span style=\\"display: inline-block; width: 15%; text-align: left;\\"> Thu <\\/span> <span style=\\"display: inline-block; width: 83%; text-align: left;\\"> &nbsp;-&nbsp;&nbsp;9:00 AM &nbsp;-&nbsp; 7:00 PM<\\/span><br\\/><span style=\\"display: inline-block; width: 15%; text-align: left;\\"> Fri <\\/span> <span style=\\"display: inline-block; width: 83%; text-align: left;\\"> &nbsp;-&nbsp;&nbsp;9:00 AM &nbsp;-&nbsp; 7:00 PM<\\/span><br\\/><span style=\\"display: inline-block; width: 15%; text-align: left;\\"> Sat <\\/span> <span style=\\"display: inline-block; width: 83%; text-align: left;\\"> &nbsp;-&nbsp;&nbsp;9:00 AM &nbsp;-&nbsp; 7:00 PM<\\/span><br\\/><span style=\\"display: inline-block; width: 15%; text-align: left;\\"> Sun <\\/span> <span style=\\"display: inline-block; width: 83%; text-align: left;\\"> &nbsp;-&nbsp;&nbsp;9:00 AM &nbsp;-&nbsp; 7:00 PM<\\/span><br\\/><\\/div>"}',
                        'state_id' => '611',
                        'country_id' => '39',
                        'zipcode' => 'P7B 5E1',
                        'longitude' => '-89.2616196',
                        'latitude' => '48.4235761',
                        'velofix' => false,
                        'velofix_selldirect_result' => [],
                        'type' => 'stock',
                        'local_delivery_shipping' => '3.33',
                        'telephone' => '************',
                        'formatAddress' => '<b>Localhost Code Shop</b><br/>955 Oliver Rd<br/>Thunder Bay, Ontario<br/>Canada, P7B 5E1<br/>Ph:************',
                        'state' => 'Ontario',
                        'distance' => 1.770000000000000017763568394002504646778106689453125,
                        'rating' => 6,
                        'product' => [
                            17532934023 => [
                                'id' => '21',
                                'inventoryId' => 76,
                                'inventory' => 710,
                                'taxcode' => '',
                                'currency' => 'CAD',
                                'qty' => 1,
                                'unformatedunitprice' => '1200.00',
                                'unformatedprice' => 1200,
                                'unitprice' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 1,200.00</span>',
                                'price' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 900.00</span>',
                                'unformatedunitpricediscount' => 900,
                                'unformatedpricediscount' => 900,
                                'unitpricediscount' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 900.00</span>',
                                'pricediscount' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 900.00</span>',
                                'isTax' => 'true',
                                'TaxCategoryID' => 0,
                                'taxClassID' => 11,
                                'tax' => 156,
                                'taxdiscount' => 117,
                                'assembly_option' => '1',
                                'brand_id' => '8',
                                'local_delivery_shipping' => '3.33',
                                'local_delivery_radius' => '1000',
                            ],
                        ],
                        'totalproductamount' => 1200,
                        'priceformat' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 1,200.00</span>',
                        'price' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 1,200.00</span>',
                        'totalproductamountdiscount' => 900,
                        'priceformatdiscount' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 900.00</span>',
                        'discountAmount' => 300,
                        'discountAmountFormat' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 300.00</span>',
                        'shippingAmount' => 0,
                        'shippingAmountFormat' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 0.00</span>',
                        'shippingAmount_localdelivery' => 3.3300000000000000710542735760100185871124267578125,
                        'shippingAmountFormat_localdelivery' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 3.33</span>',
                        'shippingTax' => 0,
                        'shippingTax_localdelivery' => 0.429999999999999993338661852249060757458209991455078125,
                        'taxamt' => 156,
                        'taxformat' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 156.00</span>',
                        'taxamt_localdelivery' => 156.43000000000000682121026329696178436279296875,
                        'taxformat_localdelivery' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 156.43</span>',
                        'tax_included' => false,
                        'totalamount' => 1356,
                        'totalamount_localdelivery' => 1359.759999999999990905052982270717620849609375,
                        'totalformat' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 1,356.00</span>',
                        'totalformat_localdelivery' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 1,359.76</span>',
                        'taxamtdiscount' => 117,
                        'taxformatdiscount' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 117.00</span>',
                        'taxamtdiscount_localdelivery' => 117.43000000000000682121026329696178436279296875,
                        'taxformatdiscount_localdelivery' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 117.43</span>',
                        'totalamountdiscount' => 1017,
                        'totalamountdiscount_localdelivery' => 1020.759999999999990905052982270717620849609375,
                        'totalformatdiscount' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 1,017.00</span>',
                        'totalformatdiscount_localdelivery' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 1,020.76</span>',
                    ]),
                ],
                'shipFromStore' => [
                    7 => json_encode([
                        'retailer_id' => '7',
                        'company_name' => 'Localhost Code Shop',
                        'instore' => true,
                        'shipment' => true,
                        'shipment_type' => '0',
                        'shipment_option' => '',
                        'free_shipping' => '10000',
                        'shiptostore_tax' => '1',
                        'defaultTax' => '13.000000',
                        'currency' => 'CAD',
                        'address' => '955 Oliver Rd_,_',
                        'inventory_type' => 'lightspeed_cloud',
                        'site_type' => null,
                        'city' => 'Thunder Bay',
                        'store_timing' => '{"currentTime":"<span><b><a href=\\"\\" class=\\"tooltip tooltip7\\" id=\\"tooltip7\\" onclick=\\"return false;\\" style=\\"display: inline-block;color: blue; opacity:1; font-weight: inherit;font-size: inherit; position: relative; z-index: 1\\"><span>9:00 AM &nbsp;-&nbsp; 7:00 PM<\\/span><\\/a><\\/b><\\/span>","timinglist":"<div style=\\"width: 220px;\\"><span style=\\"display: inline-block; width: 15%; text-align: left;\\"> Mon <\\/span> <span style=\\"display: inline-block; width: 83%; text-align: left;\\"> &nbsp;-&nbsp;&nbsp;9:00 AM &nbsp;-&nbsp; 7:00 PM<\\/span><br\\/><span style=\\"display: inline-block; width: 15%; text-align: left;\\"> Tue <\\/span> <span style=\\"display: inline-block; width: 83%; text-align: left;\\"> &nbsp;-&nbsp;&nbsp;9:00 AM &nbsp;-&nbsp; 7:00 PM<\\/span><br\\/><span style=\\"display: inline-block; width: 15%; text-align: left;\\"> Wed <\\/span> <span style=\\"display: inline-block; width: 83%; text-align: left;\\"> &nbsp;-&nbsp;&nbsp;9:00 AM &nbsp;-&nbsp; 7:00 PM<\\/span><br\\/><span style=\\"display: inline-block; width: 15%; text-align: left;\\"> Thu <\\/span> <span style=\\"display: inline-block; width: 83%; text-align: left;\\"> &nbsp;-&nbsp;&nbsp;9:00 AM &nbsp;-&nbsp; 7:00 PM<\\/span><br\\/><span style=\\"display: inline-block; width: 15%; text-align: left;\\"> Fri <\\/span> <span style=\\"display: inline-block; width: 83%; text-align: left;\\"> &nbsp;-&nbsp;&nbsp;9:00 AM &nbsp;-&nbsp; 7:00 PM<\\/span><br\\/><span style=\\"display: inline-block; width: 15%; text-align: left;\\"> Sat <\\/span> <span style=\\"display: inline-block; width: 83%; text-align: left;\\"> &nbsp;-&nbsp;&nbsp;9:00 AM &nbsp;-&nbsp; 7:00 PM<\\/span><br\\/><span style=\\"display: inline-block; width: 15%; text-align: left;\\"> Sun <\\/span> <span style=\\"display: inline-block; width: 83%; text-align: left;\\"> &nbsp;-&nbsp;&nbsp;9:00 AM &nbsp;-&nbsp; 7:00 PM<\\/span><br\\/><\\/div>"}',
                        'state_id' => '611',
                        'country_id' => '39',
                        'zipcode' => 'P7B 5E1',
                        'longitude' => '-89.2616196',
                        'latitude' => '48.4235761',
                        'velofix' => false,
                        'velofix_selldirect_result' => [],
                        'type' => 'stock',
                        'local_delivery_shipping' => '3.33',
                        'distance' => 1.770000000000000017763568394002504646778106689453125,
                        'state' => 'Ontario',
                        'telephone' => '************',
                        'product' => [
                            17532934023 => [
                                'id' => '21',
                                'inventoryId' => 76,
                                'inventory' => 710,
                                'taxcode' => '',
                                'currency' => 'CAD',
                                'qty' => 1,
                                'unformatedunitprice' => '1200.00',
                                'unformatedprice' => 1200,
                                'unitprice' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 1,200.00</span>',
                                'price' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 900.00</span>',
                                'unformatedunitpricediscount' => 900,
                                'unformatedpricediscount' => 900,
                                'unitpricediscount' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 900.00</span>',
                                'pricediscount' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 900.00</span>',
                                'isTax' => 'true',
                                'TaxCategoryID' => 0,
                                'taxClassID' => 11,
                                'tax' => 156,
                                'taxdiscount' => 117,
                            ],
                        ],
                        'price' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 1,200.00</span>',
                        'priceformat' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 1,200.00</span>',
                        'pricediscount' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 900.00</span>',
                        'priceformatdiscount' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 900.00</span>',
                        'tax_included' => false,
                        'shippingAmount' => 6.660000000000000142108547152020037174224853515625,
                        'shippingAmount_localdelivery' => 3.3300000000000000710542735760100185871124267578125,
                        'shippingAmountFormat' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 6.66</span>',
                        'shippingAmountFormat_localdelivery' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 3.33</span>',
                        'shippingTax' => 0.86580000000000001403321903126197867095470428466796875,
                        'shippingTax_localdelivery' => 0.432900000000000007016609515630989335477352142333984375,
                        'taxamt' => 156.865800000000007230482879094779491424560546875,
                        'taxamt_localdelivery' => 156.432899999999989404386724345386028289794921875,
                        'taxamtdiscount' => 117.8657999999999930196281638927757740020751953125,
                        'taxamtdiscount_localdelivery' => 117.4329000000000036152414395473897457122802734375,
                        'taxamtwithShipping' => 156.865800000000007230482879094779491424560546875,
                        'taxamtwithShipping_localdelivery' => 156.432899999999989404386724345386028289794921875,
                        'taxamtwithShippingDiscount' => 117.8657999999999930196281638927757740020751953125,
                        'taxamtwithShippingDiscount_localdelivery' => 117.4329000000000036152414395473897457122802734375,
                        'taxformatwithShipping' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 156.87</span>',
                        'taxformatwithShipping_localdelivery' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 156.43</span>',
                        'taxformatwithShippingDiscount' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 117.87</span>',
                        'taxformatwithShippingDiscount_localdelivery' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 117.43</span>',
                        'totalamount' => 1356.8699999999998908606357872486114501953125,
                        'totalamount_localdelivery' => 1356.430000000000063664629124104976654052734375,
                        'totalamountdiscount' => 1056.8699999999998908606357872486114501953125,
                        'totalamountdiscount_localdelivery' => 1056.430000000000063664629124104976654052734375,
                        'totalwithshipping' => 1363.529999999999972715158946812152862548828125,
                        'totalwithshipping_localdelivery' => 1359.759999999999990905052982270717620849609375,
                        'totalwithshippingdiscount' => 1024.529999999999972715158946812152862548828125,
                        'totalwithshippingdiscount_localdelivery' => 1020.759999999999990905052982270717620849609375,
                        'totalformat' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 1,356.87</span>',
                        'totalformat_localdelivery' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 1,356.43</span>',
                        'totalformatdiscount' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 1,056.87</span>',
                        'totalformatdiscount_localdelivery' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 1,056.43</span>',
                        'totalwithshippingformat' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 1,363.53</span>',
                        'totalwithshippingformat_localdelivery' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 1,359.76</span>',
                        'totalwithshippingformatdiscount' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 1,024.53</span>',
                        'totalwithshippingformatdiscount_localdelivery' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 1,020.76</span>',
                        'formatAddress' => '<b>Localhost Code Shop</b><br/>955 Oliver Rd<br/>Thunder Bay, Ontario<br/>Canada, P7B 5E1<br/>Ph:************',
                        'rating' => 6,
                    ]),
                ],
            ],
            'shipping' => [
                'First_name' => 'Aron',
                'Last_name' => 'Schmidt',
                'company' => 'ShipEarly',
                'address' => '2400 Nipigon Rd.',
                'address2' => '',
                'city' => 'Thunder Bay',
                'country' => '39',
                'province' => '611',
                'PostalCode' => 'P7C 4W1',
                'phone' => '(*************',
                'email' => '<EMAIL>',
                'regionName' => 'Ontario',
                'regionCode' => 'ON',
                'countryName' => 'Canada',
                'countryCode' => 'CA',
            ],
            'shippingRates' => [
                0 => [
                    'id' => static::COURIER_ID,
                    'type' => 'carrier',
                    'name' => 'USPS Priority Mail International',
                    'code' => 'usps_priority_mail_international',
                    'amount' => 70.35,
                    'discount' => 17.59,
                    'currency' => 'CAD',
                    'box_weight' => '0.01',
                ],
            ],
            'sellExclusiveResult' => '',
        ];
    }

    protected function sellExclusiveResult()
    {
        return (object) [
            'id' => '8',
            'uuid' => '5666ffac-5964-4f7a-9048-1cc291c2de43',
            'email_address' => '<EMAIL>',
            'password' => '3be70f15222270d5c843692c1247b57b',
            'company_name' => 'My Local Brand',
            'company_description' => '',
            'company_code' => '',
            'status' => 'Active',
            'Branch' => '0',
            'user_type' => 'Manufacturer',
            'minOrderAmount' => '0',
            'avatar' => null,
            'shipping_infographic' => null,
            'flatshipping' => '0',
            'site_type' => 'Shopify',
            'shop_url' => 'aron-shipearly-2.myshopify.com',
            'fbpixelId' => '904599178183609',
            'google_conversion_label' => '',
            'currency_code' => 'CAD',
            'api_key' => 'e8d27b84d0d3e8549cd2faa84309c75d',
            'secret_key' => '723213fc099cd21a806692052d3c8949',
            'inventory_apiuser' => '',
            'inventory_password' => '',
            'Inventory_Emp_ID' => null,
            'Inventory_Reg_ID' => null,
            'Inventory_Store_ID' => null,
            'defaultTax' => null,
            'otherInventory' => null,
            'created' => '2015-12-08 17:05:00',
            'modified' => '2017-08-14 20:09:33',
            'address' => '123 Localhost St._,_',
            'store_timing' => '',
            'country_id' => '39',
            'state_id' => '611',
            'city' => 'Thunder Bay',
            'zipcode' => 'P7C 4W1',
            'latitude' => '48.407966',
            'longitude' => '-89.2565708',
            'inventory_type' => null,
            'permission' => '',
            'instore' => true,
            'shipment' => true,
            'shipment_type' => '',
            'shipment_option' => '',
            'free_shipping' => null,
            'shiptostore_free_shipping' => '1000000000',
            'shiptostore_tax' => '0',
            'local_delivery' => '1',
            'local_delivery_shipping' => '3.33',
            'local_delivery_title' => '',
            'subscription_plan' => 'core',
            'coupon_code' => null,
            'store_associate_pin' => null,
            'setup_status' => true,
            'revenue_model' => null,
            'retailer_default_amount' => '0.99',
            'store_associate_default_amount' => '0.00',
            'in-store_radius' => '50',
            'ship_from_store_radius' => '250',
            'in-store-pickup-radius' => '50',
            'admin_sell_direct' => '1',
            'sell_direct_percentage' => null,
            'sell_direct' => '1',
            'enable_velofix' => true,
            'brand_revenue_model' => '2',
            'brand_direct_default_amount' => '0.99',
            'splitpayment_percentage' => '2.00',
            'brand_abandon_cart' => true,
            'brand_abandon_cart_message' => 'Abandoned cart message.',
            'brand_abandon_cart_subject' => 'Abandoned cart subject',
            'vend_access_token_expires' => null,
            'vend_refresh_access_token' => null,
            'ship_to_door_message' => '',
            'ship_to_door_backorder_message' => '',
            'instore_pickup_message' => '',
            'ship_to_store_message' => '',
            'ship_to_store_backorder_message' => '',
            'ship_to_store_noretailer_message' => '',
            'local_delivery_in_stock_message' => '',
            'local_delivery_no_stock_message' => '',
            'local_delivery_backorder_message' => '',
            'local_delivery_noretailer_message' => '',
            'buyDirectOnly' => 0,
            'state' => 'Ontario',
            'product' =>
                (object) [
                    '***********' =>
                        (object) [
                            'inventory' => -127,
                            'id' => '20',
                            'inventoryId' => ***********,
                            'taxcode' => '',
                            'currency' => 'USD',
                            'unformatedprice' => 599.990000000000009094947017729282379150390625,
                            'price' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 599.99</span>',
                            'unitprice' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 599.99</span>',
                            'unformatedunitprice' => '599.99',
                            'qty' => 1,
                            'weight' => 0,
                            'isTax' => true,
                            'assembly_option' => '0',
                            'unformatedpricediscount' => 449.99250000000000682121026329696178436279296875,
                            'pricediscount' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 449.99</span>',
                            'unitpricediscount' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 449.99</span>',
                            'unformatedunitpricediscount' => 449.99250000000000682121026329696178436279296875,
                            'tax' => 59.99900000000000233058017329312860965728759765625,
                            'taxdiscount' => 44.9992500000000035242919693700969219207763671875,
                        ],
                    'shopifyshippingtax' => 0.1000000000000000055511151231257827021181583404541015625,
                ],
            'currency' => 'CAD',
            'price' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 599.99</span>',
            'priceformat' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 599.99</span>',
            'pricediscount' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 449.99</span>',
            'priceformatdiscount' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 449.99</span>',
            'shippingAmount' => 6.660000000000000142108547152020037174224853515625,
            'shippingAmountFormat' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 6.66</span>',
            'shipping_tax_option' => '1',
            'taxamt' => 60.66499999999999914734871708787977695465087890625,
            'taxamtdiscount' => 45.6652500000000003410605131648480892181396484375,
            'shippingTax' => 0.66600000000000003641531520770513452589511871337890625,
            'taxamtwithShipping' => 60.66499999999999914734871708787977695465087890625,
            'totalproductamount' => 599.990000000000009094947017729282379150390625,
            'totalamount' => 660.6599999999999681676854379475116729736328125,
            'taxamtwithShippingDiscount' => 45.6652500000000003410605131648480892181396484375,
            'totalproductamountdiscount' => 449.990000000000009094947017729282379150390625,
            'totalamountdiscount' => 495.66000000000002501110429875552654266357421875,
            'discountAmount' => 150,
            'discountAmountFormat' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 150.00</span>',
            'tax_included' => false,
            'taxformatwithShipping' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 60.67</span>',
            'totalwithshipping' => 667.3200000000000500222085975110530853271484375,
            'taxformatwithShippingDiscount' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 45.33</span>',
            'totalwithshippingdiscount' => 502.31999999999999317878973670303821563720703125,
            'totalformat' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 660.66</span>',
            'totalwithshippingformat' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 667.32</span>',
            'totalformatdiscount' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 495.66</span>',
            'totalwithshippingformatdiscount' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 502.32</span>',
            'formatAddress' => '<b>My Local Brand</b><br/>123 Localhost St.<br/>Thunder Bay, Ontario<br/>Canada, P7C 4W1<br/>Ph:807-123-1234',
            'shopifyshippingtax' => 0.1000000000000000055511151231257827021181583404541015625,
        ];
    }

    protected function sellExclusiveCartItems(): array
    {
        return [
            [
                'id' => ***********,
                'properties' => null,
                'quantity' => 1,
                'variant_id' => ***********,
                'key' => '***********:8799e5c50c490ac93e9421da9e49100c',
                'title' => 'Super Bell - Gold',
                'price' => 59999,
                'original_price' => 59999,
                'discounted_price' => 59999,
                'line_price' => 59999,
                'original_line_price' => 59999,
                'total_discount' => 0,
                'discounts' => [],
                'sku' => 'BELL-1',
                'grams' => 0,
                'vendor' => 'aron-shipearly',
                'taxable' => true,
                'product_id' => 5572320903,
                'gift_card' => false,
                'url' => '/products/super-bell?variant=***********',
                'image' => null,
                'handle' => 'super-bell',
                'requires_shipping' => true,
                'product_type' => 'Accessories',
                'product_title' => 'Super Bell',
                'product_description' => '',
                'variant_title' => 'Gold',
                'variant_options' => ['Gold'],
            ],
        ];
    }

    private function getAccountType(?string $stripe_account): string
    {
        if (!$stripe_account) {
            $account = 'platform';
        } elseif ($stripe_account === static::STRIPE_ACCOUNT_BRAND) {
            $account = 'brand';
        } elseif ($stripe_account === static::STRIPE_ACCOUNT_RETAILER) {
            $account = 'retailer';
        } else {
            $account = 'other';
        }

        return $account;
    }
}
