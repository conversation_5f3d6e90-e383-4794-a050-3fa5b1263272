<?php

/**
 * A CakeTestSuite containing all ShopifyController test cases.
 *
 * Run with the following console command:
 *
 * ./app/Console/cake test Shopify Controller/ShopifyController
 */
class ShopifyControllerTest extends CakeTestSuite {
    public static function suite() {
        $suite = new CakeTestSuite('ShopifyController tests');
        $suite->addTestDirectoryRecursive(CakePlugin::path('Shopify') . DS . 'Test' . DS . 'Case' . DS . 'Controller' . DS . 'Shopify');
        return $suite;
    }
}
