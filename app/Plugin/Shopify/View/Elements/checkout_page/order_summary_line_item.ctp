<?php
/**
 * @var AppView $this
 * @var array $value
 * @var string $currency
 */

/** @var string[] $imageExe */
$imageExe = array('.jpg', '.png', '.gif');
/** @var string[] $imageRep */
$imageRep = array('_150x150_cropped.jpg', '_150x150_cropped.png', '_150x150_cropped.gif');
?>
<div class="row order_summary_padd">
	<div class="col-xs-3 col-md-2 col-sm-3 pdt_img paddoff padding-text">
		<div class="order_body ">
			<div class="order_details">
				<div class="order_details_product">
					<div class="img-cart imgCart-design">
						<img alt="product" src="<?php echo str_replace($imageExe, $imageRep, $value['image']); ?>">
					</div>
					<div class="cart cart-align-count">
						<span class="cart-count"><?php echo $value['quantity']; ?></span>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="col-xs-9 col-md-10 col-sm-9 pdt_decr paddoff img-amt">
		<div class="col-xs-6 col-md-6 col-sm-8 order_details_description padding-contnet">
			<label class="pdt_decr-title"><?php echo $value['product_title']; ?></label>
			<label class="pdt_decr-variant"><?php echo $value['variant_title']; ?></label>
		</div>
		<div class="col-xs-6 col-md-6 col-sm-4 order_cost amt-align paddoff">
			<?php echo $this->Currency->formatCurrency($value['line_price'] / 100, $currency); ?>
		</div>
	</div>
</div>
