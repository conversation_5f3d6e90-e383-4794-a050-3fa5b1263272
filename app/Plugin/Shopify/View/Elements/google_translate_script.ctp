<?php
/**
 * @var AppView $this
 * @var string $trackingId for Google Analytics
 */

if (empty($trackingId)) {
	$trackingId = $this->Session->read('Shopify.trackingId');
}
?>
<script type="text/javascript">
function googleTranslateElementInit() {
	var language = (navigator.languages && navigator.languages[0]) ? navigator.languages[0] : (navigator.language || navigator.userLanguage);
	if (language.substring(0, 2) !== 'en') {
		new google.translate.TranslateElement({
		<?php if (!empty($trackingId)) { ?>
			gaTrack: true,
			gaId: '<?php echo $trackingId; ?>',
		<?php } ?>
			pageLanguage: 'en',
			includedLanguages: 'da,de,en,es,fi,fr,it,ja,nl,no,pt,ru,sv,zh-CN',
			layout: google.translate.TranslateElement.InlineLayout.SIMPLE,
			multilanguagePage: true
		}, 'google_translate_element');
	}
}
</script>
<script type="text/javascript" src="//translate.google.com/translate_a/element.js?cb=googleTranslateElementInit"></script>
