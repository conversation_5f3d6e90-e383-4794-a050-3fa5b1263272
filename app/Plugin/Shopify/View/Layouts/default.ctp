<?php
/**
 * @var AppView $this
 * @var array $subdomain_settings
 * @var string $lang
 * @var string $domainUrl
 * @var string $homeUrl
 * @var string $cartUrl
 * @var string $brandName
 * @var string $brandLogo
 * @var string $checkoutBackgroundImage
 * @var string $checkoutLogo
 * @var bool $displayCheckoutLogoLeft
 * @var string|null $brandAccentColor
 * @var string|null $checkoutFontFamily
 * @var string $checkoutHeadSnippet
 * @var string[] $brandPolicies
 */
?>
<?php
$brandAccentColor = $brandAccentColor ?: (string)($subdomain_settings['accent_color'] ?? '') ?: null;
$checkoutFontFamily = (!empty($checkoutFontFamily) ? $checkoutFontFamily . ',' : '') . '"Helvetica Neue",Helvetica,Arial,sans-serif';

$accent_color = $brandAccentColor ?? '#428bca';
$accent_color_rgb = implode(',', sscanf($accent_color, (strlen($accent_color) === 4) ? '#%1x%1x%1x' : '#%2x%2x%2x'));

$brandPolicies = !empty($brandPolicies) ? $brandPolicies : array_filter((array)$this->Session->read('Shopify.brandPolicies'));

$checkoutLogo = $checkoutLogo ?: $brandLogo;
/**
 * @param string $slug
 * @return string
 */
$getPolicyTitle = function($slug) {
	$translationMap = [
		'return_policy' => __('Return Policy'),
		'privacy_policy' => __('Privacy Policy'),
		'terms_of_service' => __('Terms Of Service'),
	];
	return $translationMap[$slug] ?? ucwords(str_replace('_', ' ', $slug));
};
?>
<!DOCTYPE html>
<html lang="<?= $lang ?>">
<head>
	<?= $this->Html->charset() . PHP_EOL ?>
	<meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1" />
	<title><?= $this->fetch('title') ?></title>
	<meta name="viewport" content="width=device-width, initial-scale=1" />
	<meta name="format-detection" content="telephone=no" />
	<?= $this->Html->meta('icon', ($subdomain_settings['favicon_url'] ?? '') ?: BASE_PATH . 'images/icons/favicon.jpg') ?>

	<style>
	:root {
		--accent-color: <?= $accent_color ?>;
		--accent-color-rgb: <?= $accent_color_rgb ?>;
		--accent-color-hover: oklch(from var(--accent-color) calc(l * .80) c h);
		--font-family: <?= $checkoutFontFamily ?>;
	}
	</style>
	<?= $this->Html->css([
		'dist/tailwind.min.css',
		'https://maxcdn.bootstrapcdn.com/bootstrap/3.3.1/css/bootstrap.min.css',
		'font-awesome/css/all.min.css',
		'/tooltipster-4.2.8/dist/css/tooltipster.bundle.min.css',
		'/tooltipster-4.2.8/dist/css/plugins/tooltipster/sideTip/themes/tooltipster-sideTip-shadow.min.css',
		'tooltipster.shipearly.css',
	], ['plugin' => false]) ?>
	<?= $this->Html->css([
		'Shopify.style.css',
		'Shopify.shopify.css',
	]) ?>
	<?= ($brandAccentColor) ? $this->Html->css('Shopify.accent-color.css') : '' ?>

	<?= $this->Html->script([
		'//ajax.googleapis.com/ajax/libs/jquery/1.11.2/jquery.min.js',
		'https://maxcdn.bootstrapcdn.com/bootstrap/3.3.1/js/bootstrap.min.js',
		'/tooltipster-4.2.8/dist/js/tooltipster.bundle.min.js',
		'tooltipster.shipearly.js',
		'https://js.stripe.com/v3/',
	], ['plugin' => false]) ?>
	<?= $this->Html->script([
		'Shopify.jquery.shipearly.ecommerce.js',
	]) ?>
	<?= $this->Html->script([
		'bootbox.min.js',
		'jquery.mask.min.js',
	], ['inline' => false, 'plugin' => false]) ?>
	<?= $this->element('analyticstracking', compact('domainUrl')) ?>

<?php if (!empty($checkoutHeadSnippet)) {
	echo $checkoutHeadSnippet;
} ?>

<?php
	echo $this->fetch('meta');
	echo $this->fetch('css');
	echo $this->fetch('head_script');
?>

<?php if ($checkoutBackgroundImage) { ?>
  <style>
      #header {
          background-image: url("<?= $checkoutBackgroundImage ?>");
          background-size: cover;
          background-repeat: no-repeat;
          background-position: center;
      }
  </style>
<?php } ?>
</head>
<body>

<?php echo $this->element('analyticstracking_body'); ?>

<header id="header">
	<div><?php echo $this->element('display_message'); ?></div>
	<?php if ($this->Session->check('Shopify.StoreAssociate.User.company_name')) { ?>
		<div class="text-right">
			<?php echo __('Logged in as'); ?> <span class="notranslate"><?php echo $this->Session->read('Shopify.StoreAssociate.User.company_name'); ?></span>
			(<?php echo $this->Html->link(__('Logout'), ['plugin' => 'Shopify', 'controller' => 'Shopify', 'action' => 'verifyStoreAssociatePin']); ?>)
		</div>
	<?php } ?>
	<div class="container <?= ($displayCheckoutLogoLeft) ? 'text-left' : 'text-center' ?>">
		<a class="heading-color" href="<?php echo $homeUrl; ?>">
			<?php if ($checkoutLogo) { ?>
				<img class="brand-logo" title="<?php echo $brandName; ?>" src="<?php echo $checkoutLogo; ?>" alt="<?php echo $brandName; ?>">
			<?php } else { ?>
				<h1 class="notranslate">
					<?php echo h($brandName); ?>
				</h1>
			<?php } ?>
		</a>
	</div>
</header>
<main id="checkout_body">
	<div class="container" style="margin-top:15px;">
		<nav aria-label="Breadcrumb">
			<?= $this->Html->getCrumbList(['class' => 'breadcrumb-list', 'firstClass' => false, 'lastClass' => false]) ?>
		</nav>
	</div>
	<?php echo $this->fetch('content'); ?>
</main>
<footer class="footer">
	<div class="wrap">
		<ul class="footer__nav">
		<?php foreach ($brandPolicies as $name => $policy) { ?>
			<li>
				<button
					type="button"
					data-modal="<?php echo 'policy__' . $name; ?>"
					class="footer__nav__link btn btn-link">
					<?php echo $getPolicyTitle($name); ?>
				</button>
			</li>
		<?php } ?>
			<li>
				<button
					type="button"
					class="store-associate-link footer__nav__link btn btn-link">
					PIN
				</button>
			</li>
			<li><div id="google_translate_element" style="display: inline-block;"></div></li>
		</ul>
	</div>
</footer>
<?php foreach ($brandPolicies as $name => $policy) { ?>
<div id="<?php echo 'policy__' . $name; ?>" class="modal policy hide">
	<div class="wrap">
		<div class="policy-header">
			<button class="modal__close" aria-label="Close">×</button>
			<a class="policy-header__shop-name notranslate" href="<?php echo $homeUrl; ?>"><?php echo $brandName; ?></a>
			<h2 class="policy-header__title"><?php echo $getPolicyTitle($name); ?></h2>
		</div>
		<div class="policy-body">
			<p><?php echo $policy; ?></p>
		</div>
	</div>
</div>
<?php } ?>
<div class="modal payment-loader-wrapper" aria-hidden="true">
	<div class="payment-loader">
		<h1><?= __('Thank You') ?></h1>
		<i style="font-size: 3em;" class="fas fa-spinner fa-spin"></i>
		<p>
			<?= __('Your Order is Processing') ?> ...<br />
			<?= __('Please Wait. Do Not Refresh the Page.') ?>
		</p>
	</div>
</div>
<script type="text/javascript">
jQuery(function($) {
	$('#checkout_shipping_address_country').on('change', function() {
		var countryCode = $('option:selected', this).data('code');
		$('#phone').maskPhone(countryCode);
		$('#Postalcode').maskPostalCode(countryCode, {
			'US': '<?= __('Zip') ?> (12345)',
			'CA': '<?= __('Postal Code') ?> (A1A 1A1)',
			'GB': '<?= __('Postal Code') ?>',
		});
	}).bindGetStatesOnChange({
		url: "<?= Router::url(['plugin' => 'shopify', 'controller' => 'shopify', 'action' => 'get_states']) ?>",
	}).trigger('change');
});
</script>
<script type="text/javascript">
$(function() {
	$('.store-associate-link').click(function() {
		bootbox.dialog({
			size: 'small',
			title: "<?php echo __('Enter 5 Digit PIN'); ?>",
			message: '<?php
				echo $this->Form->create('User', array(
					'id' => 'StoreAssociatePinForm',
					'url' => array('plugin' => 'Shopify', 'controller' => 'Shopify', 'action' => 'verifyStoreAssociatePin'),
					'class' => 'bootbox-form',
					'inputDefaults' => array('label' => false, 'div' => false),
				));
				echo $this->Form->input('store_associate_pin', array(
					'id' => 'StoreAssociatePin',
					'type' => 'text',
					'placeholder' => '00000',
					'class' => 'bootbox-input bootbox-input-text form-control',
					'required' => true,
					'label' => 'PIN',
					'pattern' => '^[0-9]{5}$',
					'oninvalid' => 'this.setCustomValidity("' . __('Pin must be a 5 digit number') . '");',
					'oninput' => 'this.setCustomValidity("");',
					'autocomplete' => 'off',
				));
				echo $this->Form->end(['style' => 'display: none;']);
			?>',
			buttons: {
				Cancel: function() {
					bootbox.hideAll();
				},
				OK: function() {
					$('#StoreAssociatePinForm input[type="submit"]').click();
					return false;
				}
			}
		}).on('shown.bs.modal', function() {
			$('#StoreAssociatePinForm').submit(function(e) {
				var pin = $('#StoreAssociatePin').val();
				if (!(/^[0-9]{5}$/.test(pin))) {
					alert("<?php echo __('Pin must be a 5 digit number'); ?>");
					return false;
				}
				var $form = $(this);
				$.ajax({
					type: 'GET',
					async: false,
					url: $form.attr('action'),
					data: $form.serialize(),
					success: function(found) {
						if (!found) {
							alert("<?php echo __('No match found for the provided PIN'); ?>");
							e.preventDefault();
						}
					}
				});
			});
		}).on('hidden.bs.modal', function() {
			// bootbox hides the scroll bar and doesn't clean up after closing
			$('body').removeAttr('style');
		});
	});
});
</script>
<script src="//maps.googleapis.com/maps/api/js?key=<?php echo GOOGLE_GEOCODING_API_KEY; ?>&libraries=places&v=3" type="text/javascript" async defer></script>
<script type="text/javascript">
// Google Autocomplete disables browser autofill on element so only load if needed
$('#address').focus($.initAutocomplete);
</script>
<?php echo $this->element('google_translate_script'); ?>
<script>
$(function() {
	$(window).on('resize', function() {
		$('#checkout_body').css({ 'min-height': $(this).outerHeight(true) - ($('#header').outerHeight(true) + $('.footer').outerHeight(true)) });
	}).trigger('resize');
});
</script>

	<?php echo $this->fetch('script'); ?>

	<?php echo $this->element('sql_dump'); ?>
</body>
</html>
