<?php
/**
 * @var AppView $this
 * @var string $domainUrl
 * @var string $cartUrl
 * @var string $currency
 * @var array $cart
 * @var string $customer
 * @var string $lang
 * @var string $token
 * @var string $trackingId
 * @var string $fbpixelId
 * @var string $marketingLabel
 * @var string[] $shipping
 * @var array $shippingCountries
 * @var array $states
 *
 * @var bool $productStatus
 * @var array $discountInfo
 * @var bool $enable_stripe_payment_request
 * @var bool $enable_google_pay
 * @var bool $enable_apple_pay
 */
?>
<?php
$this->Html
	->addCrumb(__('Cart'), $cartUrl)
	->addCrumb($this->Html->tag('span', __('Customer Information'), ['class' => 'active']))
	->addCrumb($this->Html->tag('span', __('Delivery Method'), ['class' => 'breadcrumb-item-future']));
?>
<!--*********************************body***************************************-->
<style type="text/css">
	input[type="text"].discountbox {
		border: 1px solid #ccc;
		padding: 15px;
		width: 100%;
		height: auto;
		text-transform: uppercase;
	}
	.discountbtn {
		width:100%;
		height: inherit;
	}

	@media (min-width: 975px) {
		.order-sum {
			display: none;
		}
	}
	#discount_code::placeholder {
        color: #767676;
    }
</style>
<div class="container-fluid wrapper">
<div class="wrapper_content">
	<div class="container">
		<form method="POST" action="<?php echo $this->Html->url(['plugin' => 'Shopify', 'controller' => 'Shopify', 'action' => 'paymentPage']); ?>" id="billingValidation">
			<input type="hidden" name="customer" value="<?php echo $customer; ?>" />
			<input type="hidden" name="preferred_language" value='<?php echo $lang; ?>' />
			<input type="hidden" name="token" value='<?php echo $token; ?>' />
			<input type="hidden" name="trackingId" value="<?php echo $trackingId; ?>" />
			<input type="hidden" name="fbpixelId" value="<?php echo $fbpixelId; ?>" />
			<div class="row">
			<?php if (!empty($productStatus)) { ?>
				<h3><?php echo __('Shipping'); ?></h3>
				<div id="noRetailer">
					<div>
						<b>
							<?php echo __('We currently do not have any authorized dealers carrying that product near you.'); ?>
							<br />
							<?php echo __('Please click continue to buy direct from our website.'); ?>
						</b>
					</div>
					<div class="action col-xs-12 col-sm-10 col-md-6 col-md-offset-0 col-lg-6 col-sm-offset-1 col-lg-offset-0 show">
						<a href="<?php echo $domainUrl . '/checkout'; ?>" id="redirectUrl" class="btn Subbtn"><?php echo __('Continue'); ?></a>
					</div>
				</div>
			<?php } else { ?>
				<div class="col-xs-12 col-sm-12">
					<div class="order-sum">
						<div class="checkout_ordersummary">
							<button type="button" class="close-intro address-align" style="display: none;">
								<i class="fas fa-shopping-cart"></i><?php echo __('Show Order Summary'); ?>
								<i class="fas fa-chevron-circle-up"></i>
							</button>
							<button type="button" class="open-intro address-align">
								<i class="fas fa-shopping-cart"></i><?php echo __('Hide Order Summary'); ?>
								<i class="fas fa-chevron-circle-down"></i>
							</button>
						</div>
						<div class="checkout_ordertotal">
							<span id="order-summary-total" class="curr-disp"><?php echo $this->Currency->formatCurrency($cart['total_price']/100, $currency); ?>
							</span>
						</div>
					</div>
				</div>

				<div class="col-xs-12 col-sm-12 col-md-6 col-md-push-6 order_container order-hide">
					<div class="order-hide" style="background-color: #fafafa;">
						<div class="order_summary border">
							<div class="order_summary_padd"></div>
							<div id="order-summary-items" class="order_body notranslate">
							<?php foreach ($cart['items'] as $value) {
								echo $this->element('Shopify.checkout_page/order_summary_line_item', compact('value', 'currency'));
							} ?>
							</div>
							<!-- Show Discount Code Entry -->
							<div class="row order_summary_padd">
								<div class="col-xs-8 col-sm-9 col-md-8 col-lg-8 paddoff">
									<?= $this->Form->input('Order.discount_code', [
										'id' => 'discount_code',
										'name' => 'discount_code',
										'placeholder' => __('Discount Code'),
										'value' => $discountInfo['code'] ?? '',
										'spellcheck' => 'false',
										'required' => false,
										'autocomplete' => 'off',
										'class' => 'form-control discountbox',
										'label' => ['text' => __('Discount Code'), 'class' => 'sr-only'],
										'div' => false,
										'aria-invalid' => 'false',
										'aria-describedby' => 'discount_code_error',
										'after' => '<p id="discount_code_error" class="field__error-message" role="alert"></p>',
									]) ?>
								</div>
								<div class="col-xs-4 col-sm-3 col-md-4 col-lg-4" style="height:52px;">
									<button type="button" id="discountApplyBtn" class="btn btn-primary discountbtn text-uppercase" aria-label="<?= __('Apply discount code') ?>" disabled><?= __('Apply') ?></button>
								</div>
							</div>
							<!-- End Show Discount Code Entry -->
							<div class="row order_summary_padd">
									<div class="col-xs-5 col-sm-9 col-md-6 col-lg-6 paddoff">
										<div class="row3 ">
											<div class="order_details">
												<div class="order_details_product roe4_block">
													<label id="subTotalLabel"><?php echo __('Subtotal'); ?></label>
												</div>
												<div class="order_details_product roe4_block hide">
													<label id="discountLabel"><?php echo __('Discount'); ?></label>
												</div>
												<div class="order_details_product roe4_block">
													<label><?php echo __('Shipping'); ?></label>
												</div>
												<div class="order_details_product roe4_block">
													<label><?php echo __('Taxes'); ?></label>
												</div>
											</div>
										</div>
									</div>
									<div class="col-xs-7 col-sm-3 col-md-6 col-lg-6 paddoff Amount_details">
										<div class="order_cost roe4_block totalPrice" id="subTotal">
											<label id="subTotalLabelValue"><?php echo $this->Currency->formatCurrency($cart['total_price']/100, $currency, false, true); ?></label>
										</div>
										<div class="order_cost roe4_block hide">
											<label id="discountLabelValue">-</label>
										</div>
										<div class="order_cost roe4_block" id="shippingTotal">
											<label><?php echo __('Calculated at next step'); ?></label>
										</div>
										<div class="order_cost roe4_block totalPrice" id="taxTotal">
											<label>-</label>
										</div>

									</div>
								</div>
							<div class="row order_summary_padd total">
								<div class="col-xs-4 col-sm-4 col-md-6 col-lg-6 paddoff">
									<div class="order_body ">
										<div class="order_details">


											<div class="order_details_description">
												<label style="font-weight: bold;"><?php echo __('Total'); ?></label>
											</div>
										</div>
									</div>
								</div>
								<div class="col-xs-8 col-sm-8 col-md-6 col-lg-6 paddoff total">
									<div class="order_cost">
										<label class="price currec-align" id="totalLabel"> <?php echo $this->Currency->formatCurrency($cart['total_price']/100, $currency); ?>
										</label>
									</div>
								</div>
							</div>
						</div>
						<div class="action action2">

						</div>
					</div>
				</div>

				<div class="col-xs-12 col-sm-12 col-md-6 col-md-pull-6 show rmvmargin">
					<div id="payment-request-button"><!-- A Payment Request button will be inserted here. --></div>
					<h3 style="font-weight: bold;"><?php echo __('Contact'); ?></h3>
					<div class="address-inputs fieldset" data-type="email">
						<div class="field field--focus field_Input">
							<?= $this->Form->input('Order.customerEmail', [
								'type' => 'email',
								'id' => 'checkout_email',
								'name' => 'checkemailv[shipping_address][email]',
								'placeholder' => __('Email'),
								'value' => $shipping['email'] ?? '',
								'spellcheck' => 'false',
								'required' => true,
								'autocomplete' => 'shipping email',
								'class' => 'inputbox validate',
								'label' => ['text' => __('Email'), 'class' => 'field_label'],
								'div' => false,
								'aria-invalid' => 'false',
								'aria-describedby' => 'checkout_email_error',
								'after' => '<p id="checkout_email_error" class="field__error-message" role="alert"></p>',
							]) ?>
						</div>
						<div class="checkbox">
							<label>
								<?php echo $this->Form->checkbox('Customer.accepts_marketing', array('checked' => false)); ?>
								<?php echo $marketingLabel; ?>
							</label>
						</div>
					</div>
					<h3 style="font-weight: bold;"><?php echo __('Your Shipping Address'); ?></h3>
					<?= $this->element('Shopify.address_inputs', [
						'type' => 'shipping',
						'address' => $shipping,
						'countries' => $shippingCountries,
						'states' => $states,
					]) ?>
					<div>
						<input type="submit" id="cus_info_submit" class="btn Subbtn" name="commit" value="<?php echo __('Continue to delivery method'); ?>">
						<div class="img-cart return-car" style="position: relative">
							<div class="fa-arrows-alt1">
								<i class="fas fa-chevron-circle-left"></i>
								<a class="address-align" href="<?php echo $cartUrl; ?>"><?php echo __('Return To Cart'); ?></a>
							</div>
						</div>
					</div>
				</div>
			<?php } ?>
			</div>
		</form>
	</div>
</div>
</div>
<script type="text/javascript">
$(function() {
	var originalTotalPrice = $('#totalLabel').html();

	var $discountCode = $('#discount_code');
	var $discountButton = $('#discountApplyBtn');

	$discountCode.keyup(
		enableDiscountApplyBtn
	).blur(function() {
		var $this = $(this);
		$this.val($this.val().toUpperCase());
		enableDiscountApplyBtn();
	}).keypress(function(e) {
		if (e.which === 13) {
			e.preventDefault();
			$discountButton.click();
		}
	});

    function enableDiscountApplyBtn() {
        $discountButton.prop('disabled', $discountCode.val().length <= 0);
        if ($discountCode.val().length === 0) {
            removeDiscountError();
            hideDiscountLabel();
            $('#totalLabel').html(originalTotalPrice);
        }
    }

	function setDiscountError(message) {
		var $error = $discountCode.siblings('.field__error-message');
		$error.text(message).addClass('addblock');
		$(`[aria-describedby~="${$error.attr('id')}"]`).attr('aria-invalid', 'true');

		var $email = $('#checkout_email');
		if (!$email.val()) {
			$email.focus();
		} else {
			$discountCode.focus();
		}
	}

	function removeDiscountError() {
		var $error = $discountCode.siblings('.field__error-message');
		$error.text('').removeClass('addblock');
		$(`[aria-describedby~="${$error.attr('id')}"]`).attr('aria-invalid', 'false');
	}

	function hideDiscountLabel() {
		$('#discountLabel').parent().addClass('hide');
		$('#discountLabelValue').parent().addClass('hide');
	}

	function showDiscountLabel(discountType) {
		if (discountType !== 'free_shipping') {
			$('#discountLabel').parent().removeClass('hide');
			$('#discountLabelValue').parent().removeClass('hide');
		}
	}

	function updateShippingText(discountType) {
		var $label = $("#shippingTotal > label");
		if (discountType === 'free_shipping') {
			$label.text("<?php echo __('FREE SHIPPING'); ?>");
		} else {
			$label.text("<?php echo __('Calculated at next step'); ?>");
		}
	}

	$discountButton.on('click', function(e) {
		e.preventDefault();
		var $this = $(this);
		if ($this.prop('disabled')) {
			return;
		}
		$this.prop('disabled', true);

		$.ajax({
			type: 'POST',
			url: '<?php echo Router::url(['plugin' => 'Shopify', 'controller' => 'Shopify', 'action' => 'verifyDiscountCode']); ?>',
			data: {
				code: $('#discount_code').val().toUpperCase(),
				email_address: $('#checkout_email').val(),
			},
			dataType: 'json',
			beforeSend: function() {
				removeDiscountError();
				hideDiscountLabel();
			},
			complete: enableDiscountApplyBtn,
			success: function(result) {
				if (!result.success) {
					if (!result.message) {
						result.message = "<?php echo __('Invalid discount code'); ?>";
					}
					setDiscountError(result.message);
					updateShippingText('');
					return;
				}
				var discount_option = result.discount_option;
				var discount_amount = parseFloat(result.discountTotal);
				var html = result.html;

				showDiscountLabel(discount_option);
				updateShippingText(discount_option);
				if (discount_amount > 0 && html) {
					if (html.order_summary_items) {
						$('#order-summary-items').html(html.order_summary_items);
					}
					$('#order-summary-total').html(html.total_price);

					$('#subTotalLabelValue').html(html.items_subtotal_price);
					$('#discountLabel').html(html.discount_label);
					$('#discountLabelValue').html(html.total_discount);
					$('#totalLabel').html(html.total_price);
				}
			},
			error: function (request, status, error) {
				console.error(error);
				setDiscountError("<?php echo __('An error occurred'); ?>");
			}
		});
	});

	function initialize() {
		if ($discountCode.val().length > 0) {
			$discountButton.prop('disabled', false);
		}
		$discountButton.click();
	}
	initialize();
});
</script>
<script type="text/javascript">
$(function() {
	$('#billingValidation').submit(function(e) {
		e.preventDefault();
		if (!navigator.cookieEnabled) {
			alert("<?php echo __('Cookies are not enabled on your browser. You must enable cookies by adjusting your browser\'s security settings to proceed to checkout.'); ?>");
			return;
		}

		var form = this;
		$.validateAddress({
			validation_url: "<?php echo Router::url(['plugin' => 'shopify', 'controller' => 'shopify', 'action' => 'validate_address']); ?>",
			onValid: function() {
				$.getJSON("<?php echo Router::url(['plugin' => 'shopify', 'controller' => 'shopify', 'action' => 'ancillary_fee']); ?>", {
					state_id: $('#checkout_shipping_address_province').val()
				}, function(data) {
					if (data.html) {
						bootbox.confirm({
							message: data.html,
							className: 'modal--vertical-middle modal--centered-footer modal--wide-footer modal--hidden-cancel',
							callback: function(confirmed) {
								if (confirmed) {
									form.submit();
								}
							}
						});
					} else {
						form.submit();
					}
				});
			}
		});
	});
});
</script>

<?php if ($enable_stripe_payment_request) { ?>

<script src="https://pay.google.com/gp/p/js/pay.js"></script>
<?php echo $this->Html->script('Shopify.jquery.shipearly.ecommerce.payment-request'); ?>
<script type="text/javascript">
$(function() {
	$('#payment-request-button').mountStripePaymentRequestButton({
		publishable_key: "<?= STRIPE_PUBLISHABLE_KEY ?>",
		stripe_api_version: "<?= STRIPE_API_VERSION ?>",
		country: $('#checkout_shipping_address_country').find('option:selected').data('code') || 'US',
		currency: "<?= !empty($currency) ? strtolower($currency) : 'usd' ?>",
		line_items: <?= json_encode(array_map(
			function($product) {
				return [
					'amount' => $product['line_price'],
					'label' => "x{$product['quantity']} {$product['title']}",
					'pending' => false,
				];
			},
			$cart['items']
		)) ?>,
		state_id_url: "<?= Router::url(['plugin' => 'Shopify', 'controller' => 'Shopify', 'action' => 'state_id_from_codes']) ?>",
		address_validation_url: "<?= Router::url(['plugin' => 'shopify', 'controller' => 'shopify', 'action' => 'validate_address']) ?>",
		shipping_options_url: "<?= Router::url(['plugin' => 'Shopify', 'controller' => 'Shopify', 'action' => 'payment_request_shipping_options']) ?>",
		stripe_payment_url: "<?= Router::url(['plugin' => 'Shopify', 'controller' => 'Shopify', 'action' => 'stripePayment']) ?>",
		clone_platform_payment_method_url: "<?= Router::url(['plugin' => 'shopify', 'controller' => 'shopify', 'action' => 'clone_platform_payment_method']) ?>",
		place_order_url: "<?= Router::url(['plugin' => 'Shopify', 'controller' => 'Shopify', 'action' => 'placeOrder']) ?>",
		cancel_url: "<?= Router::url(['plugin' => 'shopify', 'controller' => 'shopify', 'action' => 'payment_request_cancel']) ?>",
		enable_google_pay: <?= json_encode($enable_google_pay) ?>,
		enable_apple_pay: <?= json_encode($enable_apple_pay) ?>,
		label_subtotal: "<?= __('Subtotal') ?>",
		label_discount: "<?= __('Discount') ?>",
		label_shipping: "<?= __('Shipping') ?>",
		label_tax: "<?= __('Tax') ?>",
		label_total: "<?= __('Total') ?>"
	});
});
</script>

<?php } ?>
