<script>
  (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
  (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
  m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
  })(window,document,'script','https://www.google-analytics.com/analytics.js','ga');

<?php if (!empty($trackingId)) { ?>
  ga('create', '<?php echo $trackingId; ?>', 'auto', { 'allowLinker': true });
<?php } ?>
  ga('require', 'linker');
  ga('linker:autoLink', ['<?php echo !empty($redirectUrl) ? parse_url($redirectUrl, PHP_URL_HOST) : 'myshopify.com'; ?>', 'shipearlyapp.com']);

  ga('require', 'ecommerce');
<?php
$retailerSubtotal = 0;
foreach ($storepoint->product as $item) {
    $retailerSubtotal += $item['unformatedprice'];
}
$storepoint->shippingAmount = !empty($storepoint->shippingAmount) ? $storepoint->shippingAmount : 0;
echo <<<JS
  ga('ecommerce:addTransaction', {
    'id': '{$orderNo}',
    'affiliation': '{$storepoint->company_name}',
    'revenue': '{$retailerSubtotal}',
    'shipping': '{$storepoint->shippingAmount}',
    'tax': '{$storepoint->taxamt}'
  });
JS;
?>

<?php
foreach ($items as $item) {
  if (!isset($storepoint->product[$item['variant_id']])) {
      continue;
  }
  $storepointProduct = $storepoint->product[$item['variant_id']];
  echo <<<JS
  ga('ecommerce:addItem', {
    'id': '{$orderNo}',
    'name': '{$item['title']}',
    'sku': '{$item['sku']}',
    'category': '{$item['product_type']}',
    'price': '{$storepointProduct['unformatedunitprice']}',
    'quantity': '{$item['quantity']}'
  });
JS;
}
?>

  ga('ecommerce:send');
</script>
<?php if($shippingMethod == 'shipearly_shipearly') { ?>
  <script src="https://maps.googleapis.com/maps/api/js?v=3.exp&sensor=false"></script>
  <script>
    var rendererOptions = {
      draggable: true
    };
    var directionsDisplay = new google.maps.DirectionsRenderer(rendererOptions);;
    var directionsService = new google.maps.DirectionsService();
    var map;
    var mapcenter = new google.maps.LatLng (<?php echo $storepoint->latitude;  ?>, <?php echo $storepoint->longitude; ?>);

    function initialize() {

      var mapOptions = {
        zoom: 7,
        center: mapcenter
      };
      map = new google.maps.Map(document.getElementById('map-canvas'), mapOptions);
      directionsDisplay.setMap(map);
      directionsDisplay.setPanel(document.getElementById('directionsPanel'));
      google.maps.event.addListener(directionsDisplay, 'directions_changed', function() {
        computeTotalDistance(directionsDisplay.getDirections());
      });

      calcRoute();
    }

    function calcRoute() {

      var request = {
        origin: new google.maps.LatLng (<?php echo $geopoints['lat'];  ?>,<?php echo $geopoints['lng']; ?>),
        destination: new google.maps.LatLng (<?php echo $storepoint->latitude;  ?>, <?php echo $storepoint->longitude; ?>),
        //waypoints:[{location: 'Bourke, NSW'}, {location: 'Broken Hill, NSW'}],
        travelMode: google.maps.TravelMode.DRIVING
      };
      directionsService.route(request, function(response, status) {
        if (status == google.maps.DirectionsStatus.OK) {
          directionsDisplay.setDirections(response);
        }
      });
    }

    function computeTotalDistance(result) {
      var total = 0;
      var myroute = result.routes[0];
      for (var i = 0; i < myroute.legs.length; i++) {
        total += myroute.legs[i].distance.value;
      }
      total = total / 1000.0;
      document.getElementById('total').innerHTML = total + ' km';
    }

    //google.maps.event.addDomListener(window, 'load', initialize);
    jQuery(document).on("click", '#mapLoad', function() {
      initialize();
    });
  </script>
<?php } ?>
<div class="container success-container">
  <div>
    <div class="row pagetitle">
      <div class="col-xs-5 col-sm-6 col-md-6 col-lg-6 paddoff">
        <h1>Your order has been received.</h1>
      </div>
      <div class="col-xs-7 col-sm-6 col-md-6 col-lg-6 paddoff">
        <div style="text-align: right;">
          <h3>Order Confirmation</h3>
          <large>Order No: <?php echo $orderNo; ?></large>
        </div>
      </div>
    </div>
    <div class="row marginoff">
      <div
        class="col-xs-12 col-sm-12 col-md-12 col-lg-12 paddoff content1" style="padding-bottom: 20px;">
        <p>
          <?php if($shippingMethod == 'shipearly_shipearly') {
            echo $brandinstoresuccess;
          }else if($shippingMethod == 'selldirect_selldirect') {
            echo $brandselldirectsuccess;
          }else {
            echo $brandshipfromstoresuccess;
          } ?>
        </p>
        <?php if($shippingMethod == 'shipearly_shipearly') { ?>
          <div style="text-align: center;">
            <h3 class="istorepvc">Store Pickup Verification Code:</h3>
            <h3><?php echo $code; ?></h3>
          </div>
        <?php } ?>
      </div>
    </div>
    <div class="row marginoff content1">
      <div class="col-xs-12 col-sm-6 col-md-6 col-lg-6 paddoff delivery_p">
        <?php if($shippingMethod != 'selldirect_selldirect') { ?>
          <h4>Your estimated delivery date is:</h4>
            <?php if($shippingMethod == 'shipearly_shipearly') { ?>
              <?php if($type == 'stock') { ?>
                <h5><p>Available Now</p></h5>
              <?php } else { ?>
                <h5><p>Available within 7 Business Days</p></h5>
              <?php } ?>
            <?php } else { ?>
              <h5><p>Available in 5-7 Business Days</p></h5>
            <?php }?>
        <?php } ?>
        <h4>Your Shipping Method is:</h4>
          <?php if($shippingMethod == 'shipearly_shipearly') { ?>
            <h5><p>Store Pickup</p></h5>
            <p>Store Hours: <?php $timing = json_decode($storepoint->store_timing);  echo str_replace('class="tooltip"', '', $timing->currentTime) . "<br>"; ?></p>
          <?php } else if($shippingMethod == 'selldirect_selldirect') { ?>
            <h5><p>Ship to Door</p></h5>
          <?php } else { ?>
            <h5><p>Ship From Store</p></h5>
          <?php }?>
      </div>
      <div class="col-xs-12 col-sm-6 col-md-6 col-lg-6 paddoff delivery_p storeptaddress">
        <h4>Your Order is Being Fulfilled by:</h4>
          <address>
            <?php
              echo $storepoint->formatAddress . "<br>";
            ?>
          </address>
      </div>
      <h4 class="order_details">Order Details</h4>
    </div>
    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12 paddoff content1">
        <div class="order_summary border">
          <div class="order_body ">
            <?php foreach ($items as $key => $value) {
              $imageExe = array('.jpg', '.png');
              $imageRep = array('_150x150_cropped.jpg', '_150x150_cropped.png');
              $sellExclusiveProduct = '';
              $sellExclusiveProduct = json_decode($sellExclusiveResult);
              $sellExclusiveProductId = (isset($sellExclusiveProduct->product->$value['id']->inventoryId) ? $sellExclusiveProduct->product->$value['id']->inventoryId : 0);
              if($shippingMethod == 'selldirect_selldirect') {
                 $sellExclusiveProductId = 0;
              }
              if($value['variant_id'] != $sellExclusiveProductId) {
                $productVariantId = (string)$value['variant_id'];
            ?>
            <div class="row order_summary_padd">
              <div class="col-md-1 col-sm-1 col-xs-2 pdt_img paddoff">
                <div class="order_body ">
                  <div class="order_details">
                    <div class="order_details_product">
                      <img alt="product" src="<?php echo str_replace($imageExe, $imageRep, $value['image']); ?>">
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-md-11 col-sm-11 col-xs-10 pdt_decr paddoff">
                <div class="col-md-6 col-sm-6 col-xs-6 order_details_description align1">
                  <label><?php echo $value['product_title']; ?> <span> x <?php echo $value['quantity']; ?></span></label> <label><?php echo $value['title']; ?></label>
                </div>
                <div class="col-md-6 col-sm-6 col-xs-6 order_cost paddoff prod-align Product<?php echo $value['id']; ?>">
                  <label><?php echo $storepoint->product[$productVariantId]['price']; ?></label>
                </div>
              </div>
            </div>
          <?php
              } 
            }
            if($shippingMethod == 'selldirect_selldirect') {
              $sellExclusiveOrder = (isset($storepoint->sellExclusiveOrder) ? $storepoint->sellExclusiveOrder : 0);
              if($sellExclusiveOrder == 1) {
                $storepoint->priceformat = $this->Currency->formatCurrency($storepoint->totalproductamount, $storepoint->currency);
                $storepoint->taxformatwithShipping = $this->Currency->formatCurrency($storepoint->taxamtwithShipping, $storepoint->currency);
                $storepoint->totalwithshippingformat = $this->Currency->formatCurrency($storepoint->totalwithshipping, $storepoint->currency);
              }
            }
          ?>
          </div>
          <div class="row order_summary_padd">
            <div class="col-xs-8 col-sm-9 col-md-6 col-lg-6 paddoff">
              <div class="row3 ">
                <div class="order_details">
                  <div class="order_details_product roe4_block">
                    <label>Subtotal</label>
                  </div>
                  <div class="order_details_product roe4_block">
                    <label>Shipping</label>
                  </div>
                  <div class="order_details_product roe4_block">
                    <label>Taxes</label>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-xs-4 col-sm-3 col-md-6 col-lg-6 paddoff">
              <div class="order_cost roe4_block" id="subTotal">
                <label><?php echo $storepoint->priceformat; ?></label>
              </div>
              <?php if($shippingMethod == 'shipearly_shipearly') { ?>
                <div class="order_cost roe4_block" id="shippingTotal">
                  <label>
                  <?php 
                  if($storepoint->type == "nonstock") {
                    if($storepoint->shippingAmount != 0) {
                      echo $storepoint->shippingAmountFormat; 
                    } else {
                      echo "-";
                    }
                  } else {
                    echo "-";
                  }
                  ?>
                  </label>
                </div>
                <div class="order_cost roe4_block" id="taxTotal">
                  <label>
                  <?php
                    if(isset($storepoint->tax_included) && $storepoint->tax_included == true) {
                        echo "(Inc.) ";
                    }
                    echo $storepoint->taxformat; ?>
                  </label>
                </div>
                <?php } else if($shippingMethod == 'selldirect_selldirect') {
                  ?>
                  <div class="order_cost roe4_block" id="shippingTotal">
                    <label><?php echo $storepoint->shippingAmountFormat; ?></label>
                  </div>
                  <div class="order_cost roe4_block" id="taxTotal">
                    <label>
                    <?php
                      if(isset($storepoint->tax_included) && $storepoint->tax_included == true) {
                          echo "(Inc.) ";
                      }
                      echo $storepoint->taxformatwithShipping;?>
                    </label>
                  </div>
                <?php } else { ?>
                <div class="order_cost roe4_block" id="shippingTotal">
                  <label><?php echo $storepoint->shippingAmountFormat; ?></label>
                </div>
                <div class="order_cost roe4_block" id="taxTotal">
                  <label><?php echo $storepoint->taxformatwithShipping; ?></label>
                </div>
              <?php } ?>
            </div>
          </div>
          <div class="row order_summary_padd">
            <div class="col-xs-8 col-sm-9 col-md-6 col-lg-6 paddoff">
              <div class="order_body ">
                <div class="order_details">
                  <div class="order_details_description">
                    <label>Total</label>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-xs-4 col-sm-3 col-md-6 col-lg-6 paddoff">
              <div class="order_cost">
                <?php if($shippingMethod == 'shipearly_shipearly') { ?>
                  <label id="totalAmount"><?php echo $storepoint->totalformat; ?></label>
                <?php } else if($shippingMethod == 'selldirect_selldirect') { ?>
                  <label id="totalAmount"><?php echo $storepoint->totalwithshippingformat; ?></label>
                <?php } else { ?>
                  <label id="totalAmount"><?php echo $storepoint->totalwithshippingformat; ?></label>
                <?php } ?>
              </div>
            </div>
          </div>
        </div>
        <br/>
      <h4 class="return_policy">Return Policy</h4>
    </div>
    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12 paddoff content1">
      <div class="panel-body">
        <?php echo $returnPolicy; ?>
        <!--  three content-->
      </div>
    </div>
    <div class="accrodian"> 
      <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
        <div class="panel-group" id="accordion">
        <?php if($shippingMethod == 'shipearly_shipearly') { ?>
          <?php if($mapOption == 1) { ?>
            <div class="panel panel-default">
              <div class="panel-heading">
                <h4 class="panel-title">
                  <a id="mapLoad" data-toggle="collapse" data-parent="#accordion"
                    href="#collapseOne">Store Directions</a>
                </h4>
              </div>
              <div id="collapseOne" class="panel-collapse collapse ">
                <div class="panel-body">
                  <div id="map-canvas" style="float:left;width:65%; height:420px"></div>
                  <div id="directionsPanel" style="float:left;height:420px; width:35%; overflow-y: scroll">
                    <p>Total Distance: <span id="total"></span></p>
                  </div>
                </div>
              </div>
            </div>
          <?php } ?>
        <?php } ?>
        </div>
      </div>
    </div>
    <button class="btn btn-add-to-cart continue" name="add" type="submit" style="float: right;background-color: #204a80; color: white;">
      <span>Continue Shopping</span>
    </button>
  </div>
</div>

<?php if($shippingMethod == 'shipearly_shipearly') { ?>
  <script type="text/javascript">
    jQuery(document).ready(function() {
        addTooltip("<?php echo $storepoint->retailer_id; ?>", '<?php echo $timing->timinglist; ?>');
    });

    function addTooltip(id, message) {
        setTimeout(function(){
          jQuery('#tooltip'+id).tooltipster({
              content: jQuery(message)
          });
      }, 500);
    }
  </script>
<?php } ?>
<script type="text/javascript">
  $('.continue').click(function(){
      window.top.location.href = "<?php echo $redirectUrl; ?>";
  });
</script>
