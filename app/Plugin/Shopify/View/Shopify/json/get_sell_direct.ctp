<?php
/**
 * @var AppView $this
 * @var array $data Response from /ws/checkProduct
 * @var array<?string, string[]> $stripePaymentTypesByCourierId
 */

/** @var bool $stopLoading */
$stopLoading = true;
if (empty($data['id'])) {
    echo json_encode(compact('stopLoading'));
    return;
}

/**
 * @param string|int $id
 * @param string $shippingName
 * @param string|float $subtotal
 * @param string|float $shipping
 * @param string|float $taxWithShipping
 * @param string $formattedTotal
 * @param string $dataVal
 * @return string
 */
$_getBrandRowHtml = function($id, $shippingName, $subtotal, $shipping, $taxWithShipping, $formattedTotal, $dataVal = '') use ($stripePaymentTypesByCourierId) {
    /** @var AppView $this */

    $courierId = $dataVal ? (string)(json_decode($dataVal, true)['id']) : null;
    $shippingName = h($shippingName);
    $dataVal = h($dataVal);

    return <<<HTML
<tr class="tableRow odd sddtable">
    <td class="tableCell tablecell1">
        <input type="radio" id="sell_direct_brand_{$id}" name="sell_direct_brand_id" value="{$id}" data-val="{$dataVal}" class="radio_btn" />
        <label for="sell_direct_brand_{$id}" class="notranslate" style="font-weight: normal;">{$shippingName}</label>
    </td>
    <td class="tableCell table_details tab_hide">
        <span>{$subtotal}</span>
    </td>
    <td class="tableCell">
        <span class="shipping-amount">{$shipping}</span>
    </td>
    <td class="tableCell table_details tab_hide">
        <span>{$taxWithShipping}</span>
    </td>
    <td class="tableCell tablecell3">
        <span>{$formattedTotal}</span>
    </td>
    <td class="tableCell table_details">{$this->Ecommerce->stripePaymentTypeIconsDiv((array)$stripePaymentTypesByCourierId[$courierId])}</td>
</tr>
HTML;
};

$data['taxformatwithShipping'] = $data['taxformatwithShippingDiscount'] ?? $data['taxformatwithShipping'];
$data['taxamtwithShipping'] = $data['taxamtwithShippingDiscount'] ?? $data['taxamtwithShipping'];
$data['totalwithshippingformat'] = $data['totalwithshippingformatdiscount'] ?? $data['totalwithshippingformat'];
$data['priceformat'] = $data['priceformatdiscount'] ?? $data['priceformat'];

if ($data['tax_included']) {
    $data['taxformatwithShipping'] = '<span>(Inc.)</span>&nbsp;' . $data['taxformatwithShipping'];
}

/** @var string[] $htmlRows */
$htmlRows = [];
if ($data['shippingRates']) {
    /** @var string[] $htmlRows */
    $htmlRows = array_map(function($rate) use ($data, $_getBrandRowHtml) {
        /** @var AppView $this */

        $currencyCode = (string)$data['currency'];
        $taxIncluded = (bool)$data['tax_included'];
        $shippingTaxRate = (float)$data['shipping_tax_rate'];

        $subtotal = (float)$data['totalproductamountdiscount'];

        $discount = (float)($rate['discount'] ?? 0.00);
        $shipping = (float)$rate['amount'] - $discount;

        $subtotalTax = (float)($data['taxamtwithShipping'] - $data['shippingTax']);
        $shippingTax = calculate_tax_amount($shipping, $shippingTaxRate, $taxIncluded);
        $taxWithShipping = $subtotalTax + $shippingTax;

        $total = $subtotal + $shipping;
        if (!$taxIncluded) {
            $total += $taxWithShipping;
        }

        $courier_option = ['id' => $rate['id']];
        $dataVal = json_encode(array_merge($courier_option, [
            'subtotal' => $this->Currency->formatAsDecimal($subtotal, $currencyCode),
            'shipping' => $this->Currency->formatAsDecimal($shipping, $currencyCode),
            'taxes' => $this->Currency->formatAsDecimal($taxWithShipping, $currencyCode),
            'total' => $this->Currency->formatAsDecimal($total, $currencyCode),
        ]));

        return $_getBrandRowHtml(
            $data['id'], //FIXME ids are not unique
            $rate['name'],
            $this->Currency->formatAsDecimal($subtotal, $currencyCode),
            ($shipping) ? $this->Currency->formatAsDecimal($shipping, $currencyCode) : __('FREE'),
            ($taxIncluded ? '<span>(Inc.)</span>&nbsp;' : '') . $this->Currency->formatAsDecimal($taxWithShipping, $currencyCode),
            $this->Currency->formatCurrency($total, $currencyCode),
            $dataVal
        );
    }, array_values($data['shippingRates']));
} else {
    /** @var string[] $htmlRows */
    $htmlRows = [
        $_getBrandRowHtml(
            $data['id'],
            $data['shippingName'],
            $data['totalproductamountdiscount'],
            $data['shippingAmount'],
            $data['taxamtwithShipping'],
            $data['totalwithshippingformat']
        ),
    ];
}

/** @var string $status */
$status = 'success';
/** @var string|int $retailer_id */
$retailer_id = $data['id'];
/** @var array $rawResponse */
$rawResponse = $data;
/** @var string $html */
$html = implode(PHP_EOL, $htmlRows);

echo json_encode(compact('status', 'retailer_id', 'rawResponse', 'html', 'stopLoading'));
