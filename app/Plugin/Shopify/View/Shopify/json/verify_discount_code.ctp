<?php

/**
 * @var AppView|JsonView $this
 * @var array $cart
 * @var array $discountInfo
 * @var array $itemDiscounts
 * @var bool $itemsChanged
 */


$currency = (string)($cart['currency'] ?: $this->Session->read('Shopify.User.User.currency_code'));
$subtotalPrice = (float)$cart['items_subtotal_price'] / 100;
$totalDiscount = (float)array_sum(array_column($itemDiscounts, 'discount_amount'));
$totalPrice = (float)max($subtotalPrice - $totalDiscount, 0);

$response = [
    'success' => true,
    'discount_option' => current($discountInfo['DiscountRule'])['option'],
    'discountTotal' => $this->Currency->formatAsDecimal($totalDiscount, $currency),
    'html' => [
        'order_summary_items' => ($itemsChanged) ? implode('', array_map(
            fn(array $item) => $this->element('Shopify.checkout_page/order_summary_line_item', [
                'value' => $item,
                'currency' => $currency,
            ]),
            (array)$cart['items']
        )) : null,
        'discount_label' => __('Discount') . '&nbsp;' . $discountInfo['code'],
        'items_subtotal_price' => $this->Currency->formatCurrency($subtotalPrice, $currency),
        'total_discount' => ('-&nbsp;' . $this->Currency->formatCurrency($totalDiscount, $currency)),
        'total_price' => $this->Currency->formatCurrency($totalPrice, $currency),
    ],
];
$this->set($response);
$this->set('_serialize', array_keys($response));

echo $this->render();
