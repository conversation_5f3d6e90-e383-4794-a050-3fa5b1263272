/*
 * jQuery extension for handling Stripe PaymentRequest in ShipEarly Ecommerce Plugins.
 * Requires: jquery, jquery.shipearly.ecommerce, https://js.stripe.com/v3/, https://pay.google.com/gp/p/js/pay.js.
 */
(function($) {
	$.fn.extend({
		mountStripePaymentRequestButton: function(options) {
			var defaultOptions = {
				publishable_key: '',
				stripe_api_version: undefined,
				country: 'US',
				currency: 'usd',
				line_items: {},
				state_id_url: '',
				address_validation_url: '',
				shipping_options_url: '',
				stripe_payment_url: '',
				clone_platform_payment_method_url: '',
				place_order_url: '',
				cancel_url: '',
				enable_google_pay: true,
				enable_apple_pay: true,
				label_subtotal: 'Subtotal',
				label_discount: 'Discount',
				label_shipping: 'Shipping',
				label_tax: 'Tax',
				label_total: 'Total'
			};
			options = $.extend({}, defaultOptions, options);

			return this.first().each(function() {
				// Eg. $('#payment-request-button')
				var container = this;

				// Persist order state if the user closes and reopens the dialog
				var savedPaymentItems = {
					line_items: options.line_items,
					discount: { amount: 0, label: options.label_discount, pending: true },
					shipping: { amount: 0, label: options.label_shipping, pending: true },
					tax: { amount: 0, label: options.label_tax, pending: true },
					toPaymentRequestUpdate: function() {
						var subtotal = {
							amount: this.line_items.reduce(function (sum, item) {
								return sum + item.amount;
							}, 0),
							label: options.label_subtotal,
							pending: false
						};
						var total = {
							amount: (subtotal.amount + this.discount.amount + this.shipping.amount + this.tax.amount),
							label: options.label_total,
							pending: (subtotal.pending || this.discount.pending || this.shipping.pending || this.tax.pending)
						};

						var displayItems = this.line_items.concat(subtotal);
						if (this.discount.amount) {
							displayItems = displayItems.concat(this.discount);
						}
						if (this.shipping.amount) {
							displayItems = displayItems.concat(this.shipping);
						}
						if (this.tax.amount) {
							displayItems = displayItems.concat(this.tax);
						}

						return {
							displayItems: displayItems,
							total: total
						}
					}
				};

				var stripe = Stripe(options.publishable_key, {
					apiVersion: options.stripe_api_version
				});

				var paymentRequest = stripe.paymentRequest({
					country: options.country,
					currency: options.currency,
					...(savedPaymentItems.toPaymentRequestUpdate()),
					requestPayerEmail: true,
					requestShipping: true,
					// Apple Pay billing address requires requesting name and phone
					requestPayerName: true,
					requestPayerPhone: true
				});

				var prButton = stripe.elements().create('paymentRequestButton', { paymentRequest: paymentRequest });

				// Check the availability of the Payment Request API before mounting.
				paymentRequest.canMakePayment().then(function(result) {
					if (!result) {
						container.style.display = 'none';
						return;
					}

					if ((options.enable_apple_pay && result.applePay) 
					|| (options.enable_google_pay && result.googlePay)) {
						prButton.mount(container);
						return;
					}

					if(options.enable_google_pay){
						// Mount a fake Google Pay button instead of Stripe's generic payment request button
						var hasGooglePaymentsClient = (!!window.google && !!window.google.payments && !!window.google.payments.api && !!window.google.payments.api.PaymentsClient);
						if (!hasGooglePaymentsClient) {
							console.warn('google.payments.api.PaymentsClient is missing');
							prButton.mount(container);
							return;
						}

						var paymentsClient = new window.google.payments.api.PaymentsClient({
							environment: 'TEST'
						});
						paymentsClient.isReadyToPay({ allowedPaymentMethods: ['CARD', 'TOKENIZED_CARD'] }).then(function(response) {
							if (response.result) {
								var button = paymentsClient.createButton({
									buttonType: 'plain',
									buttonSizeMode: 'fill',
									onClick: function(e) {
										e.preventDefault();
										handleClick();
										paymentRequest.show();
									}
								});
								$(container).html(button);
							} else {
								console.warn('google.payments.api.PaymentsClient is not available');
								prButton.mount(container);
							}
						}).catch(function(err) {
							console.warn(err);
							prButton.mount(container);
						});
					}
				});

				prButton.on('click', handleClick);

				function handleClick() {
					updateWithInitialDiscount();
				}

				function updateWithInitialDiscount() {
					if (!savedPaymentItems.discount) {
						savedPaymentItems.discount = {
							amount: -parseInt($('#discountLabelValue').text().replace(/\D/g, '')) || 0,
							label: $('#discountLabel').text() || options.label_discount,
							pending: true
						};

						paymentRequest.update(savedPaymentItems.toPaymentRequestUpdate());
					}
				}

				paymentRequest.on('shippingaddresschange', function(ev) {
					onShippingAddressChange(ev).catch(function() {
						console.error(arguments);
						ev.updateWith({ status: 'invalid_shipping_address' });
					});
				});

				async function onShippingAddressChange(ev) {
					await setInitialShippingAddress(ev.shippingAddress);

					var shippingOptions = await $.postPromise(options.shipping_options_url, $('#billingValidation').serialize(), 'json');

					updateWithShippingMethod(ev, shippingOptions[0], { shippingOptions: shippingOptions });
				}

				paymentRequest.on('shippingoptionchange', function(ev) {
					updateWithShippingMethod(ev, ev.shippingOption);
				});

				function updateWithShippingMethod(ev, shippingOption, updateDetails) {
					updateDetails = updateDetails || {};

					var shipping_data = JSON.parse(shippingOption.id);
					var payment_items = shipping_data.payment_items;

					payment_items.discount.label = payment_items.discount.label || $('#discountLabel').text() || options.label_discount;
					payment_items.shipping = {
						amount: shippingOption.amount,
						label: options.label_shipping + ' - ' + shippingOption.label,
						pending: false
					}
					payment_items.tax.label = payment_items.tax.label || options.label_tax;

					savedPaymentItems.line_items = payment_items.line_items;
					savedPaymentItems.discount = payment_items.discount;
					savedPaymentItems.shipping = payment_items.shipping;
					savedPaymentItems.tax = payment_items.tax;

					ev.updateWith($.extend({}, updateDetails, {
						status: 'success',
						...(savedPaymentItems.toPaymentRequestUpdate())
					}));
				}

				paymentRequest.on('paymentmethod', function(ev) {
					onPaymentMethod(ev).catch(function() {
						console.error(arguments);
						ev.complete('fail');
					});
				});

				async function onPaymentMethod(ev) {
					var payerEmail = ev.payerEmail || $('#checkout_email').val();
					var payerPhone = ev.payerPhone;
					var payerName = ev.payerName;

					var shippingAddress = ev.shippingAddress;

					shippingAddress.recipient = shippingAddress.recipient || payerName;
					shippingAddress.phone = shippingAddress.phone || payerPhone;

					// Missing shippingAddress fields become available now
					var shipping = await setFinalShippingAddress(payerEmail, shippingAddress, handleInvalidAddress);
					if (!shipping) {
						return;
					}

					var billing_details = ev.paymentMethod.billing_details;

					billing_details.email = billing_details.email || payerEmail;
					billing_details.name = billing_details.name || payerName || shippingAddress.recipient;
					billing_details.phone = billing_details.phone || payerPhone || shippingAddress.phone;

					var billing = await setBillingAddress(billing_details, shippingAddress.organization, handleInvalidAddress);
					if (!billing) {
						return;
					}

					var shipping_data = JSON.parse(ev.shippingOption.id);
					var payment_data = shipping_data.payment_data;

					var stripeResult = await $.postPromise(options.stripe_payment_url, {
						...payment_data,
						// Flag to always create platform intents because the payment method always belongs to the platform
						payment_request: true,
						shipping: $('[name="checkemailv[shipping_address][email]"], [name^="check[shipping_address]"]').serializeArray()
							.reduce((map, { name, value }) => ({ ...map, [ name.match(/\[([^\]]*)\]$/)[1] ]: value }), {}),
					}, 'json');
					if (!stripeResult.success) {
						throw ('Failed to create Stripe payment');
					}

					var setupResult = await stripe.confirmCardSetup(stripeResult.stripe_card_secret, {
						payment_method: ev.paymentMethod.id
					});
					if (!setupResult.setupIntent) {
						throw new Error(setupResult.error);
					}

					var { payment_client_secret } = await $.postPromise(
						options.clone_platform_payment_method_url,
						{ setup_intent_id: setupResult.setupIntent.id, setup_account: stripeResult.stripe_card_account },
						'json'
					);

					var paymentResult = await Stripe(
						stripeResult.stripe_platform_key,
						{
							apiVersion: stripeResult.stripe_api_version,
							stripeAccount: stripeResult.stripe_payment_account
						}
					).retrievePaymentIntent(payment_client_secret);
					if (!paymentResult.paymentIntent) {
						throw new Error(paymentResult.error);
					}

					var selectedType = payment_data.type;

					var typeToShippingMethod = {
						instore: 'shipearly_shipearly',
						local_install: 'shipearly_shipearly',
						local_delivery: 'shipearly_shipearly',
						shipFromStore: 'shipfromstore_shipfromstore',
						sellDirect: 'selldirect_selldirect',
					};
					var shipping_method = (payment_data.velofix) ? 'selldirect_selldirect' : typeToShippingMethod[selectedType];
					var ship_subtype = (selectedType === 'local_delivery') ? 'localdelivery' : '';

					var typeToRetailerInputName = {
						instore: 'retailer_id',
						local_install: 'local_install_id',
						local_delivery: 'local_retailer_id',
						shipFromStore: 'store_id',
						sellDirect: 'sell_direct_brand_id',
					};
					var retailerInputName = typeToRetailerInputName[selectedType];

					var $form = $('#billingValidation').attr('action', options.place_order_url);

					$form.append($('<input />', {
						type: 'hidden',
						name: retailerInputName,
						value: payment_data.retailer_id,
					}));
					$form.append($('<input type="hidden" name="Billing[inlineRadioOptions1]" value="newBilling" />'));
					$form.append($('<input type="hidden" name="data[Customer][accepts_marketing]" value="0" />'));
					$.each(billing, function(name, value) {
						$form.append($('<input />', {
							type: 'hidden',
							name: 'check[billing_address][' + name + ']',
							value: value,
						}));
					});
					$form.append($('<input type="hidden" name="data[Order][payment_method_subtype]" value="card" />'));

					$form.append($('<input type="hidden" name="shipping_method" />').val(shipping_method));
					$form.append($('<input type="hidden" name="shipping_method_ship_subtype" />').val(ship_subtype));

					$form.append($('<input type="hidden" name="payment_request" />').val(1));

					ev.complete('success');
					setTimeout(function() {
						$('.payment-loader-wrapper').show();
						$('body').css('overflow', 'hidden');
					}, 1000);

					$form.get(0).submit();

					function handleInvalidAddress(validation) {
						if (validation.fieldErrors) {
							if (validation.fieldErrors.email_address) {
								return ev.complete('invalid_payer_email');
							} else if (validation.fieldErrors.phone) {
								return ev.complete('invalid_payer_phone');
							} else if (validation.fieldErrors.fullname) {
								return ev.complete('invalid_payer_name');
							}
						}

						return ev.complete('invalid_shipping_address');
					}
				}

				async function setInitialShippingAddress(shippingAddress) {
					var stateResult = await $.getPromise(options.state_id_url, trimFields({
						country_code: shippingAddress.country,
						state_code: shippingAddress.region,
					}), 'json');

					var shippingNames = splitFullname(shippingAddress.recipient);

					// Skip validation because most fields are likely to be empty
					return $.setAddress(trimFields({
						firstname: shippingNames[0],
						lastname: shippingNames[1],
						companyname: shippingAddress.organization,
						address: shippingAddress.addressLine[0],
						apt: shippingAddress.addressLine[1],
						city: shippingAddress.city,
						checkout_shipping_address_country: stateResult.country_id,
						checkout_shipping_address_province: stateResult.state_id,
						Postalcode: shippingAddress.postalCode,
						phone: shippingAddress.phone
					}));
				}

				async function setFinalShippingAddress(payerEmail, shippingAddress, onInvalidAddress) {
					var shippingNames = splitFullname(shippingAddress.recipient);

					var shipping = await $.setAddress(trimFields({
						checkout_email: payerEmail,
						firstname: shippingNames[0],
						lastname: shippingNames[1],
						companyname: shippingAddress.organization,
						address: shippingAddress.addressLine[0],
						apt: shippingAddress.addressLine[1],
						city: shippingAddress.city,
						Postalcode: shippingAddress.postalCode,
						phone: shippingAddress.phone,
					}));

					var shippingValidation = await new Promise(function(resolve, reject) {
						$.validateAddress({
							validation_url: options.address_validation_url,
							onValid: resolve,
							onInvalid: resolve,
						});
					});
					if (shippingValidation.status === 'INVALID' || shippingValidation.status === 'ERROR') {
						if (typeof onInvalidAddress === 'function') {
							onInvalidAddress(shippingValidation);
						}

						return null;
					}

					return shipping;
				}

				async function setBillingAddress(billing_details, company_name, onInvalidAddress) {
					var stateResult = await $.getPromise(options.state_id_url, trimFields({
						country_code: billing_details.address.country,
						state_code: billing_details.address.state,
					}), 'json');

					var billingNames = splitFullname(billing_details.name);
					var billing = trimFields({
						First_name: billingNames[0],
						Last_name: billingNames[1],
						company: company_name,
						address: billing_details.address.line1,
						address2: billing_details.address.line2,
						city: billing_details.address.city,
						country: stateResult.country_id,
						province: stateResult.state_id,
						PostalCode: billing_details.address.postal_code,
						phone: billing_details.phone,
					});

					var billingValidation = await $.postPromise(options.address_validation_url, trimFields({
						email_address: billing_details.email,
						fullname: billing_details.name,
						company: billing.company,
						address: billing.address,
						city: billing.city,
						country_code: billing.country,
						state_code: billing.province,
						postal_code: billing.PostalCode,
						phone: billing.phone,
					}), 'json');
					if (billingValidation.status === 'INVALID' || billingValidation.status === 'ERROR') {
						if (typeof onInvalidAddress === 'function') {
							onInvalidAddress(billingValidation);
						}

						return null;
					}

					return billing;
				}

				function splitFullname(fullname) {
					fullname = fullname.trim();
					var index = fullname.lastIndexOf(' ');
					return [fullname.substring(0, index), fullname.substring(index + 1)];
				}

				function trimFields(object) {
					Object.keys(object).forEach(function(key) {
						if (typeof object[key] === 'string') {
							object[key] = object[key].trim();
						}
					});

					return object;
				}

				paymentRequest.on('cancel', function() {
					$.post(options.cancel_url);
				});
			});
		}
	});
})(jQuery);
