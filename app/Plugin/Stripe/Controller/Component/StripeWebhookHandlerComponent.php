<?php
App::uses('AppComponent', 'Controller/Component');
App::uses('OrderPayout', 'Model');

/**
 * StripeWebhookHandler Component.
 *
 * @property StripeComponent $Stripe
 *
 * @property Order $Order
 * @property OrderPayout $OrderPayout
 */
class StripeWebhookHandlerComponent extends AppComponent
{
    /**
     * @var array
     */
    public $components = [
        'Stripe.Stripe',
    ];
    /**
     * @var array
     */
    public $uses = [
        'Order',
        'OrderPayout',
    ];

    /**
     * Handle stripe webhook events.
     * @param mixed $webhook
     * @return void
     */
    public function handle($webhook)
    {
        // We only care about events for connected accounts.
        if(empty($webhook['account'])){
            CakeLog::notice("Webhook for '{$webhook['type']}' is only supported for connected accounts");

            return;
        }

        switch ($webhook['type']) {
            case 'transfer.reversed':
            case 'transfer.paid':
            case 'transfer.failed':
                // Events for transfer.* used to represent payouts but now represent transfers between Stripe accounts
                // https://stripe.com/docs/transfer-payout-split
                if (version_compare($webhook['api_version'], '2017-04-06', '>=')) {
                    CakeLog::notice("Webhook for '{$webhook['type']}' not supported as a payout webhook since 2017-04-06");
                    break;
                }
            case 'payout.canceled':
            case 'payout.paid':
            case 'payout.failed':
                $this->updatePayoutStatus($webhook);
                break;
            case 'payout.reconciliation_completed':
                $this->createPayout($webhook);
                break;
            default:
                CakeLog::warning("Webhook for '{$webhook['type']}' not supported");
        }
    }

    /**
     * Create OrderPayout records from a webhook payload.
     *
     * @param array $webhook
     * @return void
     * @throws \Stripe\Exception\ApiErrorException
     */
    protected function createPayout(array $webhook)
    {
        $connectedAccountId = $webhook['account'];
        $payoutId = $webhook['data']['object']['id'];

        $payout = $this->Stripe->retrievePayout($connectedAccountId, $payoutId, ['expand' => ['destination']]);
        $transactions = $this->Stripe->getBalanceTransactionsForPayout($connectedAccountId, $payoutId);

        $validTransactions = array_filter($transactions, function($transaction) {
            return in_array($transaction->type, ['charge', 'payment'], true);
        });
        /** @var \Stripe\BalanceTransaction[] $validTransactions */
        $validTransactions = array_combine(array_column($validTransactions, 'id'), $validTransactions);

        $orderIdByTransaction = $this->Order->getOrdersIdForPayout(array_keys($validTransactions));
        if (!$orderIdByTransaction) {
            CakeLog::notice(json_encode([
                'message' => 'Payout does not belong to any orders',
                'payout' => [
                    'id' => $payout->id,
                    'balance_transactions' => array_map(function($transaction) {
                        return ['id' => $transaction->id, 'type' => $transaction->type];
                    }, $transactions),
                    'stripe_account' => $webhook['account'],
                ],
            ]));

            return;
        }

        $orderPayoutIds = $this->OrderPayout->listIdsForPayout($payoutId, array_keys($orderIdByTransaction), array_values($orderIdByTransaction));

        $saveMany = [];
        foreach ($orderIdByTransaction as $transactionId => $orderId) {
            $transaction = $validTransactions[$transactionId];
            $saveMany[] = [
                'id' => $orderPayoutIds[$payoutId][$transactionId][$orderId] ?? null,
                'stripe_payout_id' => $payoutId,
                'stripe_balance_transaction_id' => $transactionId,
                'order_id' => $orderId,
                'amount' => $transaction->net / 100,
                'currency' => strtoupper($transaction->currency),
                'payout_total_amount' => $payout->amount / 100,
                'payout_currency' => strtoupper($payout->currency),
                'date' => date(DATE_FORMAT_SQL, $payout->arrival_date),
                'status' => $payout->status,
                'destination_type' => $payout->type,
                'institution' => $payout->destination->bank_name ?? $payout->destination->brand,
                'last_four_digits' => $payout->destination->last4,
            ];
        }

        if (!$this->OrderPayout->saveMany($saveMany)) {
            throw new InternalErrorException(json_encode(['errors' => $this->OrderPayout->validationErrors, 'data' => $this->OrderPayout->data]));
        }
    }

    /**
     * Update OrderPayout records with status from webhook payload.
     *
     * @param array $webhook
     * @return void
     */
    protected function updatePayoutStatus(array $webhook)
    {
        $payout = \Stripe\Payout::constructFrom($webhook['data']['object']);

        $this->OrderPayout->updateStatus($payout->id, $payout->status);
    }
}
