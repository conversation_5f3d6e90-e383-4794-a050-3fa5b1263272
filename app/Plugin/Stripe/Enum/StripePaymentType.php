<?php

App::uses('StripeCapability', 'Stripe.Enum');
App::uses('User', 'Model');

class StripePaymentType
{
    const CARD = 'card';
    const KLARNA = 'klarna';
    const AFFIRM = 'affirm';

    /**
     * @return string[]
     */
    public static function getAllOptions(): array
    {
        // Sorted based on presentation order in eCom plugins
        return [
            static::CARD => __('Card'),
            static::KLARNA => 'Klarna',
            static::AFFIRM => 'Affirm',
        ];
    }

    /**
     * @param string[] $stripeUserCapabilities
     * @return string[]
     */
    public static function getUserHasCapabilityByPaymentType(array $stripeUserCapabilities): array
    {
        return array_map(function($capability) use ($stripeUserCapabilities) {
            return in_array($capability, $stripeUserCapabilities, true);
        }, self::getCapabilityByPaymentType());
    }

    /**
     * @param string[] $exclude
     * @return string[]
     */
    public static function getCapabilityByPaymentType(array $exclude = []): array
    {
        $capabilityByPaymentType = [
            static::CARD => StripeCapability::CARD_PAYMENTS,
            static::KLARNA => StripeCapability::KLARNA_PAYMENTS,
            static::AFFIRM => StripeCapability::AFFIRM_PAYMENTS,
        ];

        return array_diff_key($capabilityByPaymentType, array_flip($exclude));
    }

    /**
     * @param string[] $stripeUserCapabilities
     * @param bool $isCardOnly
     * @param string[] $exclude
     * @return string[]
     */
    public static function getPaymentMethods(array $stripeUserCapabilities, bool $isCardOnly = false, array $exclude = []): array
    {
        $paymentMethodTypes = array_keys(
            array_intersect(static::getCapabilityByPaymentType($exclude), $stripeUserCapabilities)
        );

        if ($isCardOnly) {
            $paymentMethodTypes = array_values(array_intersect($paymentMethodTypes, [static::CARD]));
        }

        return $paymentMethodTypes;
    }
}
