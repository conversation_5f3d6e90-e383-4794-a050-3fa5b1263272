<?php
App::uses('Router', 'Routing');

$controllerPaths = [
    'cart_widgets' => 'carts',
    'catalogue_widgets' => 'catalogue',
    'checkout_widgets' => 'checkout',
    'locator_widgets' => 'locators',
];
foreach ($controllerPaths as $controller => $path) {
    if ($controller === 'cart_widgets') {
        foreach (Router::resourceMap() as $params) {
            if ($params['action'] === 'index') {
                $params['action'] = 'view';
            } elseif ($params['action'] === 'add') {
                $params['action'] = 'edit';
            }
            Router::connect(
                "/widgets/{$path}" . ($params['id'] ? '/:uuid' : ''),
                ['plugin' => 'widgets', 'controller' => $controller, 'action' => $params['action'], '[method]' => $params['method']],
                ['uuid' => Router::UUID, 'pass' => ['uuid']]
            );
        }
        unset($params);
    }

    Router::connect("/widgets/{$path}", ['plugin' => 'widgets', 'controller' => $controller, 'action' => 'index']);
    Router::connect("/widgets/{$path}/:id", ['plugin' => 'widgets', 'controller' => $controller, 'action' => 'view'], ['id' => Router::ID, 'pass' => ['id']]);
    Router::connect("/widgets/{$path}/:uuid", ['plugin' => 'widgets', 'controller' => $controller, 'action' => 'view'], ['uuid' => Router::UUID, 'pass' => ['uuid']]);
    Router::connect("/widgets/{$path}/:action/:id", ['plugin' => 'widgets', 'controller' => $controller], ['id' => Router::ID, 'pass' => ['id']]);
    Router::connect("/widgets/{$path}/:action/:uuid", ['plugin' => 'widgets', 'controller' => $controller], ['uuid' => Router::UUID, 'pass' => ['uuid']]);
    Router::connect("/widgets/{$path}/:action/*", ['plugin' => 'widgets', 'controller' => $controller]);
}
unset($controllerPaths, $controller, $path);
