<?php

App::uses('WidgetsAppController', 'Widgets.Controller');
App::uses('Configure', 'Core');
App::uses('Hash', 'Utility');

/**
 * Cart Widgets Controller.
 *
 * @property CurrencyComponent $Currency
 * @property SecurityComponent $Security
 * @property TranslateComponent $Translate
 *
 * @property Cart $Cart
 * @property Product $Product
 * @property User $User
 */
class CartWidgetsController extends WidgetsAppController
{
    public $components = [
        'Currency',
    ];

    public $uses = [
        'Cart',
        'Product',
        'User',
    ];

    public function beforeFilter()
    {
        parent::beforeFilter();
        $this->Security->validatePost = false;
    }

    public function view(?string $uuid = null)
    {
        $brandId = (int)$this->Auth->user('User.id');

        $cart = ($uuid) ? $this->Cart->getForWidgetsView($brandId, $uuid) : [];

        $this->response->header('X-Cart-Token', $cart['Cart']['uuid'] ?? '');

        $response = [
            'cart' => $cart,
        ];
        $this->set($response);
        $this->set('_serialize', array_keys($response));

        return $this->render('view', 'ajax');
    }

    public function edit(?string $uuid = null)
    {
        $this->request->allowMethod('post', 'put');

        $brandId = (int)$this->Auth->user('User.id');

        $existingCart = ($uuid) ? $this->Cart->getForWidgetsEdit($brandId, $uuid) : [];
        $existingItemByProductId = Hash::combine($existingCart['CartItem'] ?? [], '{n}.product_id', '{n}');

        $cart = [
            'id' => (int)($existingCart['Cart']['id'] ?? 0) ?: null,
            'user_id' => $brandId,
            'client_id' => (string)$this->Auth->user('client_id'),
        ];
        $cart += array_merge(
            [
                'currency_code' => (string)($existingCart['Cart']['currency_code'] ?? $this->Auth->user('User.currency_code')),
                'preferred_language' => $this->_preferredLanguage(),
            ],
            array_diff_key($this->request->data, array_flip(['items', 'add_items']))
        );

        if (array_key_exists('items', $this->request->data)) {
            $items = $this->_processItems($brandId, (array)$this->request->data('items'));
        } else {
            $add_items = $this->_processItems($brandId, (array)$this->request->data('add_items'));
            $items = array_map(fn(array $item): array => array_merge($item, [
                'quantity' => (int)$item['quantity'] + (int)($existingItemByProductId[$item['product_id']]['quantity'] ?? 0),
            ]), $add_items);

            // Ignore items not affected by the add_items request when deciding which items to remove
            $existingItemByProductId = array_intersect_key($existingItemByProductId, array_column($add_items, 'product_id', 'product_id'));
        }
        $items = array_map(
            fn(array $item): array => ['id' => $existingItemByProductId[$item['product_id']]['id'] ?? null] + $item,
            array_values(array_filter($items, fn(array $item): bool => (int)$item['quantity'] > 0))
        );
        $removedItems = array_values(array_diff_key($existingItemByProductId, array_column($items, 'product_id', 'product_id')));

        $uuid = $this->Cart->saveFromWidgetsEdit($cart, $items, $removedItems);
        $success = (bool)$uuid;

        if ($success) {
            $this->response->header('X-Cart-Token', $uuid);
        }

        $response = [
            'success' => $success,
            'cart' => $success ? $this->Cart->getForWidgetsView($brandId, $uuid) : $this->Cart->data,
            'errors' => $this->Cart->validationErrors,
        ];
        $this->set($response);
        $this->set('_serialize', array_keys($response));

        return null;
    }

    public function delete(string $uuid)
    {
        $this->request->allowMethod('post', 'delete');

        $brandId = (int)$this->Auth->user('User.id');

        $cart = $this->Cart->getByUuid($uuid, [
            'conditions' => ['Cart.user_id' => $brandId],
            'fields' => ['id'],
        ]);
        $success = $this->Cart->delete($cart['Cart']['id']);

        $response = [
            'success' => $success,
        ];
        $this->set($response);
        $this->set('_serialize', array_keys($response));

        return null;
    }

    private function _processItems(int $brandId, array $items): array
    {
        $baseConditions = $this->Product->getConditionsForActiveProducts([
            "{$this->Product->alias}.user_id" => $brandId,
        ]);

        return array_map(function(array $item) use ($baseConditions): array {
            $conditions = $this->_extractVariantIdCondition($item);
            if (!$conditions) {
                throw new BadRequestException(json_encode(['message' => 'Missing product argument', 'args' => $item]));
            }

            $productId = (int)$this->Product->fieldByConditions('id', $baseConditions + $conditions);
            if (!$productId) {
                throw new BadRequestException(json_encode(['message' => 'Product not found with condition', 'conditions' => $conditions]));
            }

            return ['product_id' => $productId, 'quantity' => (int)($item['quantity'] ?? 1)] + $item;
        }, $items);
    }

    private function _extractVariantIdCondition(array $item): array
    {
        $prioritizedConditionKeyByProductKey = [
            'variant_shipearly_id' => "{$this->Product->alias}.id",
            'product_shipearly_id' => "{$this->Product->alias}.product_title_id",
            'variant_id' => "{$this->Product->alias}.productID",
            'product_id' => "{$this->Product->alias}.source_product_id",
            'sku' => "{$this->Product->alias}.product_sku",
            'upc' => "{$this->Product->alias}.product_upc",
        ];
        $productKey = array_key_first(array_intersect_key($prioritizedConditionKeyByProductKey, $item));
        if (!$productKey) {
            return [];
        }

        return [$prioritizedConditionKeyByProductKey[$productKey] => $item[$productKey]];
    }

    private function _preferredLanguage(): string
    {
        return $this->Translate->getHtmlLang((string)Configure::read('Config.language'));
    }
}
