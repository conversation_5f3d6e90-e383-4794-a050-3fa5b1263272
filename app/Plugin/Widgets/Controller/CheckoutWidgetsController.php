<?php

use ShipEarlyApp\Lib\Geolocation\Geolocation;
use ShipEarlyApp\Lib\Stripe\StripePaymentMethodDetails;
use ShipEarlyApp\Lib\Utility\UserSiteType;
use ShipEarlyApp\Plugin\Widgets\Enum\WidgetsDeliveryMethod;

App::uses('WidgetsAppController', 'Widgets.Controller');
App::uses('CheckoutWidgetsException', 'Widgets.Error');
App::uses('ErrorHandler', 'Error');
App::uses('OrderAddress', 'Model');
App::uses('OrderPaymentMethod', 'Utility');
App::uses('OrderType', 'Utility');
App::uses('PreorderType', 'Utility');
App::uses('ProductSellDirect', 'Utility');
App::uses('ShippingMethod', 'Utility');
App::uses('StripePaymentType', 'Stripe.Enum');

/**
 * Checkout Widgets Controller.
 *
 * @property AddressValidatorComponent $AddressValidator
 * @property AvataxComponent $Avatax
 * @property CurrencyComponent $Currency
 * @property NotificationLogicComponent $NotificationLogic
 * @property OrderLogicComponent $OrderLogic
 * @property OrderPlacerComponent $OrderPlacer
 * @property ShippingCalculatorComponent $ShippingCalculator
 * @property SecurityComponent $Security
 * @property StripeComponent $Stripe
 * @property CheckProductComponent $CheckProduct
 *
 * @property Country $Country
 * @property Customer $Customer
 * @property ManufacturerRetailer $ManufacturerRetailer
 * @property Order $Order
 * @property OrderProduct $OrderProduct
 * @property Page $Page
 * @property Preorder $Preorder
 * @property Product $Product
 * @property ProductStateFee $ProductStateFee
 * @property State $State
 * @property StripeUser $StripeUser
 * @property StripeUserCapability $StripeUserCapability
 * @property User $User
 * @property UserSetting $UserSetting
 * @property Warehouse $Warehouse
 * @property WarehouseProductReservation $WarehouseProductReservation
 */
class CheckoutWidgetsController extends WidgetsAppController
{
    const CHECKOUT_ACTIONS = [
        'delivery_methods',
        'payment_details',
        'preorders',
        'order_confirmation',
    ];

    const AJAX_ACTIONS = [
        'get_states',
        'validate_email',
        'validate_address',
        'clone_platform_payment_method',
        'preorders',
    ];

    public $components = [
        'AddressValidator',
        'Avatax',
        'Currency',
        'NotificationLogic',
        'OrderLogic',
        'OrderPlacer',
        'ShippingCalculator',
        'Stripe.Stripe',
        'CheckProduct',
    ];

    public $uses = [
        'Country',
        'Customer',
        'ManufacturerRetailer',
        'Order',
        'OrderProduct',
        'Page',
        'Preorder',
        'Product',
        'ProductStateFee',
        'State',
        'StripeUser',
        'StripeUserCapability',
        'User',
        'UserSetting',
        'Warehouse',
        'WarehouseProductReservation',
    ];

    public $layout = 'checkout_widgets';

    /**
     * @var callable|null
     */
    protected $_previousExceptionHandler = null;

    public function implementedEvents()
    {
        return array_merge(parent::implementedEvents(), [
            'Controller.startup' => '_common',
        ]);
    }

    public function beforeFilter()
    {
        parent::beforeFilter();
        $this->_configureExceptionHandler();
        $this->_configureSecurityUnlockedFields();
    }

    protected function _configureExceptionHandler(): void
    {
        $exception = (array)Configure::read('Exception');

        $exception = array_merge($exception, [
            'handler' => [$this, '_handleException'],
            'skipLog' => array_merge((array)($exception['skipLog'] ?? []), [
                'CheckoutWidgetsException',
            ]),
        ]);

        Configure::write('Exception', $exception);
        $this->_previousExceptionHandler = set_exception_handler($exception['handler']);
    }

    protected function _configureSecurityUnlockedFields(): void
    {
        $action = $this->request->param('action');

        if ($action === 'delivery_methods') {
            $this->Security->unlockedFields = array_merge($this->Security->unlockedFields, [
                'ShippingAddress.first_name',
                'ShippingAddress.last_name',
                'ShippingAddress.company_name',
                'ShippingAddress.address1',
                'ShippingAddress.address2',
                'ShippingAddress.city',
                'ShippingAddress.country_id',
                'ShippingAddress.state_id',
                'ShippingAddress.zipcode',
                'ShippingAddress.telephone',
            ]);
        } elseif ($action === 'payment_details') {
            if ($this->request->data('ShippingAddress')) {
                $this->Security->unlockedFields = array_merge($this->Security->unlockedFields, [
                    'BillingAddress.first_name',
                    'BillingAddress.last_name',
                    'BillingAddress.company_name',
                    'BillingAddress.address1',
                    'BillingAddress.address2',
                    'BillingAddress.city',
                    'BillingAddress.country_id',
                    'BillingAddress.state_id',
                    'BillingAddress.zipcode',
                    'BillingAddress.telephone',
                ]);
            }
        }

        if ($this->request->is('ajax')) {
            $this->Security->unlockedActions = static::AJAX_ACTIONS;
        }
    }

    public function _handleException(Throwable $e): void
    {
        if ($e instanceof Exception && !($e instanceof CheckoutWidgetsException)) {
            CakeLog::error($e);
            $e = CheckoutWidgetsException::fromException($e, $this);
        }

        if (is_callable($this->_previousExceptionHandler)) {
            call_user_func($this->_previousExceptionHandler, $e);
        } else {
            ErrorHandler::handleException($e);
        }
    }

    public function _common()
    {
        $checkoutSteps = array_flip(static::CHECKOUT_ACTIONS);
        $action = $this->request->param('action');
        if (!isset($checkoutSteps[$action]) || !$this->request->data) {
            return;
        }

        $brandId = $this->Auth->user('User.id');
        $brand = $this->User->findById($brandId, [
            'User.id',
            'User.uuid',
            'User.email_address',
            'User.company_name',
            'User.site_type',
            'User.shop_url',
            'User.shop_home_url',
            'User.currency_code',
        ], null, -1);
        $this->set(compact('brand'));

        try {
            $location = Geolocation::fromArray((array)$this->request->data['geolocation']);
        } catch (InvalidArgumentException $e) {
            throw new BadRequestException($e->getMessage());
        }
        $this->set(compact('location'));

        $currencyCode = isset($location->country_code)
            ? $this->Currency->getCurrencyForCountry($location->country_code)
            : $brand['User']['currency_code'];
        $this->set(compact('currencyCode'));

        $items = is_array($this->request->data('items')) ? (array)$this->request->data['items'] : [];

        $quantityByProductId = array_column($items, 'quantity', 'product_id');
        $products = $this->Product->findAllForCheckoutWidget($brandId, $currencyCode, $quantityByProductId);
        if (!$products) {
            throw new NotFoundException('Product not found where id=' . json_encode(array_keys($quantityByProductId)));
        }
        $this->set(compact('products'));

        $retailerId = (int)$this->request->data['retailer_id'];
        $retailer = $this->_getRetailer($retailerId, $items, $location);
        if (!$retailer) {
            throw new NotFoundException('Retailer not found where id=' . json_encode($retailerId));
        }
        $this->set(compact('retailer'));

        $stripePaymentMethods = $this->StripeUser->getWidgetRetailerPaymentMethods($brandId, $retailerId);
        $this->set(compact('stripePaymentMethods'));

        if ($checkoutSteps[$action] > $checkoutSteps['delivery_methods']) {
            $customerEmail = $this->request->data('Order.customerEmail');
            if ($customerEmail) {
                $emailValidationResponse = $this->AddressValidator->validate_email($customerEmail);
                if (in_array($emailValidationResponse['status'], ['INVALID', 'ERROR'], true)) {
                    throw new BadRequestException($emailValidationResponse['message']);
                }
            }

            $shippingAddress = $this->request->data('ShippingAddress');
            $requestLocation = $location;
            if ($shippingAddress) {
                $shippingAddress = $this->_processAddressFormFields($shippingAddress);
                $this->request->data['ShippingAddress'] = $shippingAddress;

                $shippingValidationResponse = $this->_validateAddress($shippingAddress);
                if (in_array($shippingValidationResponse['status'], ['INVALID', 'ERROR'], true)) {
                    throw new BadRequestException($shippingValidationResponse['message']);
                }

                try {
                    $location = OrderAddress::toGeolocation($shippingAddress);
                } catch (InvalidArgumentException $e) {
                    throw new BadRequestException($e->getMessage());
                }
                $this->set(compact('location'));
            }

            $method = $this->request->data('method');
            $deliveryMethod = $this->_getDeliveryMethods($brand, $currencyCode, $retailerId, [$method], $location, $products)[$method] ?? [];
            if (!$deliveryMethod) {
                throw new BadRequestException('Invalid delivery method ' . json_encode($method));
            }
            $this->set(compact('deliveryMethod'));

            if ($location != $requestLocation) {
                $retailer = $this->_getRetailer($retailerId, $items, $location, $deliveryMethod['method']);
                if (!$retailer || !in_array($deliveryMethod['method'], $retailer['methods'], true)) {
                    $this->set('name', __('%s is Not Available', $deliveryMethod['label']));
                    $this->set('message', __('The selected delivery method is not available for the provided shipping address.'));

                    //TODO handle with a view presenting alternative retailers
                    throw new CheckoutWidgetsException($this->viewVars, 400);
                }

                $this->set(compact('retailer'));
            }
        }

        if ($checkoutSteps[$action] > $checkoutSteps['payment_details']) {
            $billingAddress = $this->request->data('BillingAddress');
            if ($billingAddress) {
                $billingAddress = $this->_processAddressFormFields($billingAddress);
                $this->request->data['BillingAddress'] = $billingAddress;

                $billingValidationResponse = $this->_validateAddress($billingAddress);
                if (in_array($billingValidationResponse['status'], ['INVALID', 'ERROR'], true)) {
                    throw new BadRequestException($billingValidationResponse['message']);
                }
            }
        }

        $brandIsInventoryProvider = ($retailerId == $brandId || !$retailer['stock'] || $this->ManufacturerRetailer->isCommissionRetailer($brandId, $retailerId));
        $oversoldInventoryByProductID = $this->Product->listOversoldInventoryByProductID($brandId, array_column($products, 'quantity', 'productID'));
        if ($brandIsInventoryProvider && $oversoldInventoryByProductID) {
            $this->set('name', __('Out of Stock'));
            $this->set('message', __('Sorry, some items in your cart are no longer available. Please update your cart to complete your purchase.'));

            //TODO handle with a view like 'Shopify./Elements/ErrorDetails/out_of_stock_error'
            throw new CheckoutWidgetsException($this->viewVars, 400);
        }
    }

    public function delivery_methods()
    {
        $brand = $this->viewVars['brand'];
        $products = $this->viewVars['products'];
        /** @var Geolocation $location */
        $location = $this->viewVars['location'];
        $currencyCode = $this->viewVars['currencyCode'];
        $retailer = $this->viewVars['retailer'];

        $brandId = (int)$brand['User']['id'];

        $deliveryMethods = $this->_getDeliveryMethods($brand, $currencyCode, $retailer['id'], $retailer['methods'], $location, $products);

        $addressForm = $this->_findAddressFormVars($brandId, $currencyCode, $location, $this->request->data('ShippingAddress'));

        $this->request->data['ShippingAddress'] = $this->request->data['ShippingAddress'] ?? $addressForm['ShippingAddress'];
        $countryId = (int)$this->request->data['ShippingAddress']['country_id'];

        $countries = $addressForm['shippingCountries'];
        $states = $this->State->findAllShippingStatesById($brandId, $countryId);

        $this->set('marketingLabel', $this->User->findEcommerceCustomContent($brand['User']['id'], $brand['User']['email_address'])['customer_accepts_marketing_label']);
        $this->set(compact('deliveryMethods', 'countries', 'states'));
    }

    public function payment_details()
    {
        $brand = $this->viewVars['brand'];
        $products = $this->viewVars['products'];
        /** @var Geolocation $location */
        $location = $this->viewVars['location'];
        $currencyCode = $this->viewVars['currencyCode'];
        $retailer = $this->viewVars['retailer'];
        $deliveryMethod = $this->viewVars['deliveryMethod'];

        $brandId = (int)$brand['User']['id'];

        $customerEmail = $this->request->data['Order']['customerEmail'];

        $shippingAddress = $this->request->data('ShippingAddress');
        $addressForm = $this->_findAddressFormVars($brandId, $currencyCode, $location, $shippingAddress);

        $this->request->data['BillingAddress'] = $this->request->data['BillingAddress'] ?? $addressForm['BillingAddress'];
        $countryId = (int)$this->request->data['BillingAddress']['country_id'];

        $countries = $addressForm['billingCountries'];
        $states = $this->State->findAllBillingStatesById($countryId);

        $brandContent = $this->User->findEcommerceCustomContent($brandId, $brand['User']['email_address']);

        $this->set(compact('countries', 'states', 'brandContent'));
        $this->set($this->_stripeKeys(
            $this->request->data('paymentIntentId'),
            $this->request->data('setupIntentId'),
            $brand,
            $currencyCode,
            $retailer,
            $deliveryMethod,
            $customerEmail,
            $shippingAddress
        ));
    }

    public function order_confirmation($preorderId = null)
    {
        if ($preorderId) {
            $preorderConditions = [
                "{$this->Preorder->alias}.id" => $preorderId,
                "{$this->Preorder->alias}.user_id" => $this->Auth->user('User.id'),
                "{$this->Preorder->alias}.type" => PreorderType::WIDGETS,
            ];
            $this->request->data = $this->Preorder->getLogData($preorderConditions);
            if (!$this->request->data) {
                throw new NotFoundException('Preorder.log data not found where ' . json_encode($preorderConditions));
            }

            $this->_common();
        } else {
            $this->request->allowMethod('post');

            $commonViewVarsBackup = $this->viewVars;

            // Direct call is cheaper than requestAction given that request data is the same
            $preorderResponse = $this->preorders();
            // Embedding preorders as the AJAX response to this endpoint allows for
            // SecurityComponent validation when called from payment_details
            if ($this->request->is('ajax')) {
                return $preorderResponse;
            }

            $preorderId = $this->viewVars['id'];

            $this->viewVars = $commonViewVarsBackup;
            unset($preorderResponse, $commonViewVarsBackup);
        }

        $brand = $this->viewVars['brand'];
        $products = $this->viewVars['products'];
        $retailer = $this->viewVars['retailer'];
        $deliveryMethod = $this->viewVars['deliveryMethod'];

        $brandId = (int)$brand['User']['id'];

        $customerEmail = $this->request->data['Order']['customerEmail'];
        $paymentMethodSubtype = $this->request->data['Order']['payment_method_subtype'];
        $acceptsMarketing = $this->request->data['Customer']['accepts_marketing'];

        list($shippingAddress, $billingAddress) = $this->_resolveOrderAddresses(
            $this->request->data('ShippingAddress'),
            $this->request->data('BillingAddress'),
            $retailer,
            ($this->request->data['Billing']['inlineRadioOptions1'] === 'newBilling')
        );

        $stripePaymentId = $this->request->data['paymentIntentId'];

        $orderId = $this->Order->field('id', [
            'Order.user_id' => $brandId,
            'Order.payment_method' => OrderPaymentMethod::STRIPE,
            'Order.transactionID' => $stripePaymentId,
        ]) ?: null;
        $isNewOrder = (!$orderId);

        if ($isNewOrder) {
            try {
                $stripe_account = (string)$this->request->data['stripeAccount'];
                $payment = $this->Stripe->confirmPaymentIfNotConfirmed($stripe_account, $stripePaymentId);

                try {
                    $stripeRetailerId = (int)($this->request->data['stripeRetailerId'] ?? $brandId);
                    $shipping = $this->_legacyAddressFormat($shippingAddress, $customerEmail);
                    $billing = $this->_legacyAddressFormat($billingAddress, $customerEmail);

                    $this->Stripe->updatePaymentCustomerWithBilling($payment, $stripeRetailerId, $stripe_account, $billing, $shipping);
                } catch (Exception $e) {
                    CakeLog::warning($e);
                }

                $orderId = $this->_createOrder(
                    $preorderId,
                    $brandId,
                    $products,
                    $retailer,
                    $deliveryMethod,
                    $customerEmail,
                    $shippingAddress,
                    $billingAddress,
                    $acceptsMarketing,
                    $paymentMethodSubtype,
                    $this->Stripe->getPaymentMethodDetails($payment, $stripe_account)
                );
                if (!$orderId) {
                    throw new InternalErrorException(json_encode(['errors' => $this->Order->validationErrors, 'data' => $this->Order->data]));
                }
            } catch (Exception $e) {
                if ($e instanceof \Stripe\Exception\ApiErrorException) {
                    CakeLog::error('[' . get_class($e) . '] ' . json_encode($e->getJsonBody()) . PHP_EOL . 'Stack Trace:' . PHP_EOL . $e->getTraceAsString());
                    $this->Preorder->markPaymentError($preorderId);
                } else {
                    CakeLog::error($e);
                    $this->Preorder->markOrderError($preorderId);
                }

                $brandMessages = $this->Page->getOrderResultPages($brand['User']['email_address']);

                $name = __('Payment Error');
                $message = $brandMessages['payment_error'];
                if ($e instanceof \Stripe\Exception\CardException) {
                    $message = $brandMessages[$e->getError()->code] ?? $e->getMessage();
                    if ($e->getError()->decline_code === \Stripe\Charge::DECLINED_FRAUDULENT) {
                        $message = $brandMessages['brandfraudmessage'];
                    }
                }

                throw CheckoutWidgetsException::fromException($e, $this, $message, $name);
            }
        } else {
            CakeLog::info('Loading existing order: ' . json_encode($orderId));
        }

        $this->set('pointOfNoReturn', true);

        if ($isNewOrder) {
            $method = (string)$this->request->data['method'];
            $logs = (array)$this->request->data['log'];

            $this->_afterCreateOrder($orderId, $logs, $method, $customerEmail, $shippingAddress, $billingAddress);
        }

        $this->set($this->_getSuccessViewVars($orderId));
    }

    public function get_states()
    {
        $brandId = (int)$this->Auth->user('User.id');
        $countryId = (int)$this->request->data('country_id');

        $states = ($this->request->data('is_shipping'))
            ? $this->State->findAllShippingStatesById($brandId, $countryId)
            : $this->State->findAllBillingStatesById($countryId);

        $response = [
            'states' => array_values($states),
        ];
        $this->set($response);
        $this->set('_serialize', array_keys($response));
        // Reuse Shopify view unless serializing
        $this->view = 'Shopify./Shopify/get_states';
    }

    public function validate_email()
    {
        $this->request->allowMethod('post');

        $response = $this->AddressValidator->validate_email($this->request->data['email_address']);

        $this->set($response);
        $this->set('_serialize', array_keys($response));
    }

    public function validate_address()
    {
        $this->request->allowMethod('post');

        $response = $this->AddressValidator->validate_address($this->request->data);

        $this->set($response);
        $this->set('_serialize', array_keys($response));
    }

    public function clone_platform_payment_method()
    {
        $this->request->allowMethod('post');

        $setupId = $this->request->data('setup_intent_id');

        /** @var null|\Stripe\PaymentIntent $sellExclusivePayment */
        list($payment, $sellExclusivePayment) = $this->Stripe->cloneCardSetupToPayments($setupId, null);

        $response = [
            'payment_client_secret' => $payment->client_secret,
            'payment_method_id' => $payment->payment_method,
        ];
        $this->set($response);
        $this->set('_serialize', array_keys($response));

        return null;
    }

    public function preorders()
    {
        $this->request->allowMethod('post');

        $brand = $this->viewVars['brand'];
        $products = $this->viewVars['products'];
        /** @var Geolocation $location */
        $location = $this->viewVars['location'];
        $currencyCode = $this->viewVars['currencyCode'];
        $retailer = $this->viewVars['retailer'];
        $deliveryMethod = $this->viewVars['deliveryMethod'];

        $brandId = $brand['User']['id'];
        $retailerId = $retailer['id'];
        $method = $deliveryMethod['method'];
        $orderSummary = $deliveryMethod['order_summary'];

        $retailerInfo = $this->_checkProductByMethod([$method], $brand, $currencyCode, $retailerId, $location, $products)[$method];

        //TODO Consider saving these to our order records. Currently only used for creating Shopify API orders.
        if ($brand['User']['site_type'] === UserSiteType::SHOPIFY) {
            $this->request->data['shippingName'] = $retailerInfo['shippingName'] ?? null;
            $this->request->data['shippingBoxWeight'] = $retailerInfo['shippingBoxWeight'] ?? null;
        }
        unset($retailerInfo['shippingName'], $retailerInfo['shippingBoxWeight']);

        $this->request->data['log'] = $retailerInfo;

        $preorderId = $this->Preorder->createForEcommerce(
            PreorderType::WIDGETS,
            $brandId,
            $retailerId,
            null,
            (string)$this->request->data('paymentIntentId'),
            $currencyCode,
            ($orderSummary['total']['amount'] + $orderSummary['discount']['amount']),
            $orderSummary['discount']['amount'],
            array_column($products, 'id'),
            $this->request->data
        )['id'];
        if (!$preorderId) {
            throw new BadRequestException(json_encode(['errors' => $this->Preorder->validationErrors, 'data' => $this->Preorder->data]));
        }

        $response = [
            'id' => $preorderId,
            'url' => Router::url(['action' => 'order_confirmation', 'id' => $preorderId, '?' => ['client_id' => $this->Auth->user('client_id')]], true),
        ];
        CakeLog::debug(json_encode(compact('response')));

        $this->set($response);
        $this->set('_serialize', array_keys($response));

        return null;
    }

    private function _getRetailer(int $retailerId, array $items, Geolocation $location, ?string $deliveryOption = null): array
    {
        $response = json_decode(
            $this->requestAction(['plugin' => 'widgets', 'controller' => 'locator_widgets', 'action' => 'get_retailers', 'ext' => 'json'], [
                'data' => [
                    'geolocation' => $location->toArray(),
                    'items' => $items,
                    'requestedRetailerId' => $retailerId,
                    'requestedDeliveryOption' => $deliveryOption,
                ],
                'autoRender' => true,
            ]),
            true
        );

        $response['retailers'] = Hash::combine($response['retailers'], '{n}.id', '{n}');

        return $response['retailers'][$retailerId] ?? [];
    }

    private function _getDeliveryMethods(array $brand, string $currencyCode, int $retailerId, array $methods, Geolocation $location, array $products): array
    {
        $retailerInfoByMethod = $this->_checkProductByMethod($methods, $brand, $currencyCode, $retailerId, $location, $products);
        $brandContent = $this->User->findEcommerceCustomContent($brand['User']['id'], $brand['User']['email_address']);
        $products = Hash::combine($products, '{n}.id', '{n}');

        return array_map(function($method) use ($retailerInfoByMethod, $currencyCode, $products, $brandContent) {
            $retailerInfo = $retailerInfoByMethod[$method];

            $stock = ($retailerInfo['type'] !== 'nonstock');

            $deliveryMethod = [
                'method' => $method,
                'label' => $brandContent['ship_to_door_title'],
                'message' => $brandContent['ship_to_door_message'],
                'order_summary' => [
                    'currency' => $currencyCode,
                    'items' => array_map(function($item) use ($products, $currencyCode) {
                        $id = $item['id'];

                        $product = $products[$id] ?? [];
                        if (!$product) {
                            // Ancillary Fee lookup
                            $product = $this->Product->findById($id, ['Product.id', 'Product.product_image', 'Product.product_name', 'Product.variant_options'], null, -1)['Product'];
                        }

                        return [
                            'id' => $id,
                            'product_id' => $id,
                            'quantity' => $item['qty'],
                            'product_image' => $product['product_image'],
                            'product_name' => $product['product_name'],
                            'variant_options' => $product['variant_options'],
                            'unit_price' => $item['unformatedunitprice'],
                            'total_price' => $item['unformatedprice'],
                            'total_discount' => $this->Currency->formatAsDecimal($item['unformatedprice'] - $item['unformatedpricediscount'], $currencyCode),
                            'total_tax' => $item['taxdiscount'],
                            'amount' => $item['unformatedprice'],
                        ];
                    }, Hash::combine($retailerInfo['product'], '{n}.id', '{n}')),
                    'discount' => [
                        'label' => __('Discount'),
                        'amount' => $this->Currency->formatAsDecimal(-$retailerInfo['discountAmount'], $currencyCode),
                    ],
                    'subtotal' => [
                        'label' => __('Subtotal'),
                        'amount' => $retailerInfo['totalproductamountdiscount'] ?? $retailerInfo['totalproductamount'],
                    ],
                    'shipping' => [
                        'label' => __('Shipping'),
                        'amount' => $retailerInfo['shippingAmount'],
                    ],
                    'tax' => [
                        'label' => __('Tax') . ($retailerInfo['tax_included'] ? ' (Inc.)' : ''),
                        'is_inclusive' => $retailerInfo['tax_included'],
                        'amount' => $retailerInfo['taxamtdiscount'] ?? $retailerInfo['taxamt'],
                    ],
                    'total' => [
                        'label' => __('Total'),
                        'amount' => (
                            $retailerInfo['totalwithshippingdiscount'] ?? $retailerInfo['totalwithshipping'] ??
                            $retailerInfo['totalamountdiscount'] ?? $retailerInfo['totalamount']
                        ),
                    ],
                ],
            ];

            if ($method === WidgetsDeliveryMethod::IN_STORE) {
                $deliveryMethod = Hash::merge($deliveryMethod, [
                    'label' => $brandContent['instore_pickup_title'],
                    'message' => ($stock) ? $brandContent['instore_pickup_message'] : $brandContent['ship_to_store_message'],
                ]);
            } elseif ($method === WidgetsDeliveryMethod::LOCAL_INSTALL) {
                $deliveryMethod = Hash::merge($deliveryMethod, [
                    'label' => $brandContent['local_install_title'],
                    'message' => ($stock) ? $brandContent['local_install_in_stock_message'] : $brandContent['local_install_no_stock_message'],
                    'order_summary' => [
                        'shipping' => [
                            'label' => __('Installation'),
                            'amount' => $retailerInfo['shippingAmount_localinstall'],
                        ],
                        'tax' => [
                            'amount' => $retailerInfo['taxamtdiscount_localinstall'] ?? $retailerInfo['taxamt_localinstall'],
                        ],
                        'total' => [
                            'amount' => $retailerInfo['totalamountdiscount_localinstall'] ?? $retailerInfo['totalamount_localinstall'],
                        ],
                    ],
                ]);
            } elseif ($method === WidgetsDeliveryMethod::LOCAL_DELIVERY) {
                $deliveryMethod = Hash::merge($deliveryMethod, [
                    'label' => $brandContent['local_delivery_title'],
                    'message' => ($stock) ? $brandContent['local_delivery_in_stock_message'] : $brandContent['local_delivery_no_stock_message'],
                    'order_summary' => [
                        'shipping' => [
                            'label' => $brandContent['local_delivery_shipping_title'],
                            'amount' => $retailerInfo['shippingAmount_localdelivery'],
                        ],
                        'tax' => [
                            'amount' => $retailerInfo['taxamtdiscount_localdelivery'] ?? $retailerInfo['taxamt_localdelivery'],
                        ],
                        'total' => [
                            'amount' => $retailerInfo['totalamountdiscount_localdelivery'] ?? $retailerInfo['totalamount_localdelivery'],
                        ],
                    ],
                ]);
            } elseif ($method === WidgetsDeliveryMethod::SHIP_FROM_STORE) {
                $deliveryMethod = Hash::merge($deliveryMethod, [
                    'label' => $brandContent['ship_from_store_title'],
                    'message' => '',
                ]);
            }

            $deliveryMethod['order_summary']['items'] = array_map(function($item) use ($currencyCode) {
                return $item + [
                    'amount_format' => $this->Currency->formatCurrency($item['amount'], $currencyCode, true, true),
                ];
            }, $deliveryMethod['order_summary']['items']);
            foreach (['discount', 'subtotal', 'shipping', 'tax', 'total'] as $lineField) {
                $deliveryMethod['order_summary'][$lineField] += [
                    'amount_format' => $this->Currency->formatCurrency($deliveryMethod['order_summary'][$lineField]['amount'], $currencyCode, ($lineField !== 'total'), true),
                ];
            }

            return $deliveryMethod;
        }, array_combine($methods, $methods));
    }

    private function _checkProductByMethod(array $methods, array $brand, string $currencyCode, int $retailerId, Geolocation $location, array $products)
    {
        $brandId = $brand['User']['id'];
        $token = $brand['User']['uuid'];
        $items = $this->Product->findAllAsWsWithTokenCartItems($brandId, $currencyCode, array_column($products, 'quantity', 'id'));

        //TODO support discounts
        $discount = [];
        $itemDiscounts = [];

        $shippingRates = $this->_calculateShippingRates($brandId, $retailerId, $currencyCode, $products, $location, $discount);
        $shippingAmount = (float)($shippingRates[0]['amount'] ?? 0.00);
        $shippingDiscount = (float)($shippingRates[0]['discount'] ?? 0.00);
        // Currently only used for creating Shopify API orders.
        $extraFields = [
            'shippingName' => $shippingRates[0]['name'] ?? null,
            'shippingBoxWeight' => $shippingRates[0]['box_weight'] ?? null,
        ];

        return array_map(function($method) use ($token, $currencyCode, $retailerId, $location, $items, $itemDiscounts, $shippingAmount, $shippingDiscount, $extraFields) {
            return array_merge(
                $this->CheckProduct->checkProduct($token, $currencyCode, $retailerId, $method, $location, $items, $itemDiscounts, $shippingAmount, $shippingDiscount),
                $extraFields
            );
        }, array_combine($methods, $methods));
    }

    private function _calculateShippingRates(int $brandId, int $retailerId, string $currencyCode, array $items, Geolocation $location, array $discount): array
    {
        $state = $this->State->findByCountryCodeAndStateCode((string)$location->country_code, (string)$location->state_code, ['id', 'country_id']);
        $shippingAddress = [
            'email' => null,
            'First_name' => null,
            'Last_name' => null,
            'address' => $location->address1,
            'city' => $location->city,
            'province' => $state['State']['id'],
            'country' => $state['State']['country_id'],
            'PostalCode' => $location->zipcode,
            'phone' => null,
            'regionCode' => $location->state_code,
            'regionName' => $location->state_name,
            'countryCode' => $location->country_code,
            'countryName' => $location->country_name,
        ];

        $cartItemByVariantId = array_map(
            function($item) {
                return [
                    'product_id' => $item['productID'],
                    'qty' => $item['quantity'],
                    'price' => $item['product_price'],
                    'compare_at_price' => $item['compare_at_price'],
                ];
            },
            Hash::combine($items, '{n}.productID', '{n}')
        );
        $products = $this->Product->findAllForEcommerceShippingRates($brandId, $cartItemByVariantId);

        $shippingUserId = $this->ManufacturerRetailer->usesRetailerShippingRates($brandId, $retailerId)
            ? (int)$this->User->getMainRetailerId($retailerId)
            : $brandId;

        return $this->ShippingCalculator->getRetailerShippingRates($shippingUserId, $currencyCode, $shippingAddress, $products, $discount);
    }

    private function _findAddressFormVars($brandId, $currencyCode, Geolocation $location, ?array $ShippingAddress): array
    {
        if (!$ShippingAddress) {
            $locationState = $this->State->findByCountryCodeAndStateCode((string)$location->country_code, (string)$location->state_code, ['id', 'country_id'])['State'];
            $ShippingAddress = [
                'address1' => $location->address1,
                'address2' => $location->address2,
                'city' => $location->city,
                'country_id' => $locationState['country_id'],
                'state_id' => $locationState['id'],
                'zipcode' => $location->zipcode,
            ];
        }

        $billingCountries = $this->Country->findAllBillingCountriesById();
        $shippingCountries = $this->Country->findAllShippingCountriesById((int)$brandId);
        if (!isset($shippingCountries[$ShippingAddress['country_id']])) {
            $ShippingAddress['country_id'] = $this->Country->getDefaultSelectedCountryId((string)$currencyCode, $shippingCountries);
        }

        $BillingAddress = array_intersect_key($ShippingAddress, array_flip([
            'city',
            'country_id',
            'state_id',
        ]));

        return compact('ShippingAddress', 'BillingAddress', 'billingCountries', 'shippingCountries');
    }

    private function _processAddressFormFields(array $address): array
    {
        $address = array_map('trim', $address);
        $address = $this->_fillMissingAddressStateFields($address);
        $address = $this->_fillMissingAddressGeopointFields($address);

        return $address;
    }

    private function _fillMissingAddressStateFields(array $address): array
    {
        $fieldNames = ['country_code', 'country_name', 'state_code', 'state_name'];
        $fieldNames = array_combine($fieldNames, $fieldNames);

        $missing = array_diff_key($fieldNames, array_filter($address));
        if (!$missing) {
            return $address;
        }

        $record = $this->State->findWithCountryName($address['state_id'])['State'];
        if ($record['country_id'] != $address['country_id']) {
            $record['country_code'] = null;
            $record['country_name'] = null;
        }

        return array_merge($address, array_intersect_key($record, $fieldNames));
    }

    private function _fillMissingAddressGeopointFields(array $address): array
    {
        $fieldNames = ['latitude', 'longitude'];
        $fieldNames = array_combine($fieldNames, $fieldNames);

        $missing = array_diff_key($fieldNames, array_filter($address));
        if (!$missing) {
            return $address;
        }

        $geopoints = findGeocode($address['address1'], $address['city'], $address['zipcode'], $address['state_name'], $address['country_name']);

        return array_merge($address, [
            // Set as numeric strings for type matching against associated form inputs
            'latitude' => (string)$geopoints['lat'],
            'longitude' => (string)$geopoints['lng'],
        ]);
    }

    private function _validateAddress(array $address): array
    {
        return $this->AddressValidator->validate_address([
            'fullname' => trim($address['first_name'] . ' ' . $address['last_name']),
            'company' => $address['company_name'],
            'address' => $address['address1'],
            'address2' => $address['address2'],
            'city' => $address['city'],
            'country_code' => $address['country_code'],
            'state_code' => $address['state_code'],
            'postal_code' => $address['zipcode'],
            'phone' => $address['telephone'],
        ]);
    }

    private function _stripeKeys(
        ?string $paymentIntentId,
        ?string $setupIntentId,
        array $brand,
        string $currencyCode,
        array $retailer,
        array $deliveryMethod,
        string $customerEmail,
        ?array $shippingAddress
    ): array
    {
        $brandId = (int)$brand['User']['id'];
        $retailerId = (int)$retailer['id'];

        $stripeShipping = $shippingAddress ?: $this->_retailerAddress($retailer);

        $stripeRetailer = $this->StripeUser->getEcommerceStripePayee($brandId, $retailerId, (int)$stripeShipping['country_id'], (int)$stripeShipping['state_id']);
        if (empty($stripeRetailer['StripeUser']['stripe_user_id'])) {
            throw new InternalErrorException(sprintf('Stripe account not found where user_id=%s and retailer_id=%s', $brandId, $retailerId));
        }
        $stripe_account = (string)$stripeRetailer['StripeUser']['stripe_user_id'];
        $stripeRetailerId = (int)$stripeRetailer['StripeUser']['user_id'];
        $isCommissionRetailer = (bool)$stripeRetailer['ManufacturerRetailer']['is_commission_tier'];

        $isPlatformCustomer = ($stripeRetailer['User']['user_type'] === User::TYPE_RETAILER);
        if (!$isPlatformCustomer) {
            $cus_owner_user_id = $stripeRetailerId;
            $cus_owner_stripe_account = $stripe_account;
        } else {
            $cus_owner_user_id = null;
            $cus_owner_stripe_account = null;
        }

        $stripe_cus_id = $this->Stripe->createPaymentCustomerIfNotExists(
            $customerEmail,
            $cus_owner_user_id,
            $cus_owner_stripe_account,
            $this->_legacyAddressFormat($stripeShipping, $customerEmail)
        );

        $amount = $deliveryMethod['order_summary']['total']['amount'];

        $isSellDirect = $deliveryMethod['method'] === WidgetsDeliveryMethod::SELL_DIRECT;

        $application_fee = $amount;
        if ($isSellDirect || $isCommissionRetailer || $retailer['stock']) {
            $revenueModels = $this->User->findById($brandId, [
                'User.revenue_model',
                'User.retailer_default_amount',
                'User.retailer_revenue_maximum',
                'User.brand_revenue_model',
                'User.brand_direct_default_amount',
                'User.brand_revenue_maximum',
            ], null, -1)['User'];

            $application_fee = ($isSellDirect)
                ? $this->OrderLogic->CalculateFees($amount, 'brand', $revenueModels['brand_revenue_model'], $revenueModels['brand_direct_default_amount'], $revenueModels['brand_revenue_maximum'])
                : $this->OrderLogic->CalculateFees($amount, 'retailer', $revenueModels['revenue_model'], $revenueModels['retailer_default_amount'], $revenueModels['retailer_revenue_maximum']);
        }

        $stripeUserCapabilities = $this->StripeUserCapability->getUserCapabilities($stripeRetailer['StripeUser']['id']);
        $paymentMethodTypes = StripePaymentType::getPaymentMethods($stripeUserCapabilities);

        $paymentParams = [
            'description' => 'Charge for Your Order',
            'metadata' => [],
            'payment_method_types' => $paymentMethodTypes,
            'application_fee_amount' => (int)round($application_fee * 100),
            'capture_method' => 'manual',
        ];
        if ($shippingAddress) {
            $paymentParams['shipping'] = [
                'address' => [
                    'line1' => $shippingAddress['address1'],
                    'line2' => $shippingAddress['address2'] ?: null,
                    'city' => $shippingAddress['city'],
                    'state' => $shippingAddress['state_name'],
                    'country' => $shippingAddress['country_code'],
                    'postal_code' => $shippingAddress['zipcode'],
                ],
                'name' => trim($shippingAddress['first_name'] . ' ' . $shippingAddress['last_name']),
                'phone' => $shippingAddress['telephone'],
            ];
        }
        if (!$isPlatformCustomer) {
            $paymentParams['customer'] = $stripe_cus_id;
        }
        $brand_stripe_account = $this->StripeUser->getAccountId($brandId);
        if ($stripe_account !== $brand_stripe_account) {
            $brandDescriptor = $this->Stripe->getStatementDescriptor($brand_stripe_account, $brand['User']['company_name']);
            if ($brandDescriptor) {
                // 'card' payments use 'statement_descriptor_suffix' instead of 'statement_descriptor'
                $paymentParams = array_merge($paymentParams, [
                    'statement_descriptor' => $brandDescriptor,
                    'statement_descriptor_suffix' => $brandDescriptor,
                ]);
            }
        }

        $paymentIntent = $this->Stripe->initEcommercePaymentIntent($paymentIntentId, $amount, $currencyCode, $paymentParams, compact('stripe_account'));

        $setupIntent = ($isPlatformCustomer)
            ? $this->Stripe->saveCardSetupIntent($setupIntentId, null, $stripe_cus_id, $paymentIntent->id, $stripe_account)
            : null;

        return [
            'stripePlatformKey' => STRIPE_PUBLISHABLE_KEY,
            'stripeRetailerId' => $stripeRetailerId,
            'stripeAccount' => $stripe_account,
            'paymentIntent' => $paymentIntent,
            'setupIntent' => $setupIntent,
            'payeeName' => ($isCommissionRetailer) ? $brand['User']['company_name'] : $retailer['company_name'],
        ];
    }

    private function _resolveOrderAddresses(?array $shippingAddress, ?array $billingAddress, array $retailer, bool $newBilling): array
    {
        if (!$shippingAddress) {
            $shippingAddress = array_merge($this->_retailerAddress($retailer), [
                'first_name' => $billingAddress['first_name'],
                'last_name' => $billingAddress['last_name'],
            ]);
        } else {
            $billingAddress = $newBilling ? $billingAddress : $shippingAddress;
        }

        return [$shippingAddress, $billingAddress];
    }

    private function _retailerAddress(array $retailer): array
    {
        return array_merge(
            [
                'first_name' => '',
                'last_name' => $retailer['company_name'],
            ],
            array_intersect_key($retailer, array_flip([
                'company_name',
                'address1',
                'address2',
                'city',
                'country_id',
                'state_id',
                'zipcode',
                'telephone',
                'state_name',
                'state_code',
                'country_code',
                'country_name',
                'latitude',
                'longitude',
            ]))
        );
    }

    public function _addCustomerByEmail(int $brandId, string $customerEmail, array $shippingAddress, bool $acceptsMarketing): ?int
    {
        $shipping = $this->_legacyAddressFormat($shippingAddress, $customerEmail);

        $cusId = $this->Customer->addEcommerceCustomerByEmail($brandId, $shipping);
        if ($cusId) {
            $preferredLanguage = $this->Translate->getHtmlLang((string)Configure::read('Config.language'));
            $this->Customer->save([
                'id' => $cusId,
                'accepts_marketing' => $acceptsMarketing,
                'preferred_language' => $preferredLanguage,
            ]);
        }

        return $cusId;
    }

    private function _legacyAddressFormat(array $address, string $customerEmail): array
    {
        return [
            'email' => $customerEmail,
            'First_name' => $address['first_name'],
            'Last_name' => $address['last_name'],
            'company' => $address['company_name'],
            'address' => $address['address1'],
            'address2' => $address['address2'],
            'city' => $address['city'],
            'country' => $address['country_id'],
            'province' => $address['state_id'],
            'PostalCode' => $address['zipcode'],
            'phone' => $address['telephone'],
            'regionName' => $address['state_name'],
            'regionCode' => $address['state_code'],
            'countryName' => $address['country_name'],
            'countryCode' => $address['country_code'],
        ];
    }

    private function _createOrder(
        ?int $preorderId,
        int $brandId,
        array $products,
        array $retailer,
        array $deliveryMethod,
        string $customerEmail,
        array $shippingAddress,
        array $billingAddress,
        bool $acceptsMarketing,
        string $paymentMethodSubtype,
        StripePaymentMethodDetails $paymentDetails
    ): ?int
    {
        $paymentMethod = OrderPaymentMethod::STRIPE;
        $transactionID = $paymentDetails->transactionID;
        $stripe_account = (string)$paymentDetails->stripe_account;
        if ($this->Order->exists(['Order.payment_method' => $paymentMethod, 'Order.transactionID' => $transactionID])) {
            throw new BadRequestException('An order already exists for transactionID: ' . json_encode($transactionID));
        }

        $customerId = $this->_addCustomerByEmail($brandId, $customerEmail, $shippingAddress, $acceptsMarketing);

        $user = $this->User->findById($brandId, [
            'User.id',
            'User.ship_from_store_double_ship',
            'User.sell_direct_authorize',
            'User.store_associate_default_amount',
            'User.brand_revenue_model',
            'User.brand_direct_default_amount',
            'User.brand_revenue_maximum',
            'User.revenue_model',
            'User.retailer_default_amount',
            'User.retailer_revenue_maximum',
        ], null, -1)['User'];

        $userId = $user['id'];
        $productIds = array_column($products, 'id');
        $retailerId = $retailer['id'];
        $method = $deliveryMethod['method'];
        $orderSummary = $deliveryMethod['order_summary'];
        $currencyCode = $orderSummary['currency'];
        $discountCode = '';

        $cardType = $paymentDetails->card_type;
        $lastFourDigit = $paymentDetails->last_four_digit;
        $fraudCheckAddress = $paymentDetails->fraud_check_address;
        $fraudCheckPostalCode = $paymentDetails->fraud_check_postal_code;
        $fraudCheckCvc = $paymentDetails->fraud_check_cvc;

        $methodToOrderType = [
            WidgetsDeliveryMethod::IN_STORE => OrderType::IN_STORE_PICKUP,
            WidgetsDeliveryMethod::LOCAL_INSTALL => OrderType::IN_STORE_PICKUP,
            WidgetsDeliveryMethod::LOCAL_DELIVERY => OrderType::LOCAL_DELIVERY,
            WidgetsDeliveryMethod::SHIP_FROM_STORE => OrderType::SHIP_FROM_STORE,
            WidgetsDeliveryMethod::SELL_DIRECT => OrderType::SELL_DIRECT,
        ];
        $orderType = $methodToOrderType[$method];
        $orderSubType = $retailer['stock'] ? OrderType::SUB_TYPE_STOCK : OrderType::SUB_TYPE_NONSTOCK;
        $orderStatus = OrderStatus::getOrderStatusObject((bool)$user['ship_from_store_double_ship'])->getNextOrderStatus($orderType, $orderSubType);
        $paymentStatus = OrderPaymentStatus::AUTHORIZED;

        $storeAssociateId = null;
        $connection = $this->ManufacturerRetailer->getStoreConnection($userId, $retailerId, ['is_commission_tier', 'pricingtierid']);
        $pricingTierId = (int)($connection['pricingtierid'] ?? 0) ?: null;
        $isCommissionRetailer = (bool)($connection['is_commission_tier'] ?? false);
        $salesRepFields = $this->ManufacturerRetailer->findOrderSalesRepFields($userId, $retailerId);

        if ($isCommissionRetailer) {
            $orderStatus = OrderStatus::PENDING;
            $paymentStatus = ($user['sell_direct_authorize']) ? OrderPaymentStatus::AUTHORIZED : OrderPaymentStatus::PAID;
        }

        $secretcode = (in_array($orderType, [OrderType::IN_STORE_PICKUP, OrderType::LOCAL_DELIVERY], true)) ? $this->OrderLogic->generateCode() : '';

        $totalPrice = ($orderSummary['total']['amount'] + $orderSummary['discount']['amount']);

        $shipearlyFees = 0;
        if (!$storeAssociateId) {
            $shipearlyFees = ($method === WidgetsDeliveryMethod::SELL_DIRECT)
                ? $this->OrderLogic->CalculateFees($orderSummary['total']['amount'], 'brand', $user['brand_revenue_model'], $user['brand_retailer_default_amount'], $user['brand_retailer_revenue_maximum'])
                : $this->OrderLogic->CalculateFees($orderSummary['total']['amount'], 'retailer', $user['revenue_model'], $user['retailer_default_amount'], $user['retailer_revenue_maximum']);
        }

        $currencyConversion = 1;
        $retailerCurrencyCode = $this->User->field('currency_code', ['id' => $retailerId]);
        if ($currencyCode !== $retailerCurrencyCode) {
            //TODO
            //$currencyConversion = $this->currencyConversion(1, $currencyCode, $retailerCurrencyCode);
        }

        $productCommissions = Hash::combine(
            $this->Product->find('all', [
                'recursive' => -1,
                'conditions' => ['id' => $productIds],
                'fields' => ['id', 'sales_commission'],
            ]),
            '{n}.Product.id',
            '{n}.Product'
        );

        /** @var ProductTier $ProductTier */
        $ProductTier = ClassRegistry::init('ProductTier');

        $productTierCommission = $ProductTier->find('all', [
            'conditions' => [
                'ProductTier.pricingtierid' => $pricingTierId,
                'ProductTier.product_id' => $productIds,
            ],
            'fields' => ['id', 'product_id',  'commission'],
        ]);
        $productTierCommission = Hash::combine($productTierCommission, '{n}.ProductTier.product_id',  '{n}' );

        $preferredWarehouses = $this->Warehouse->listPreferredWarehouseIdsByProductId($userId, $retailerId, array_map(
            function($product) {
                return [
                    'product_id' => $product['id'],
                    'quantity' => $product['quantity'],
                ];
            },
            $products
        ));

        $orderProducts = array_map(function($item) use ($storeAssociateId, $isCommissionRetailer, $productCommissions, $productTierCommission, $preferredWarehouses, $currencyConversion) {
            $productId = $item['product_id'];
            $commission = $productTierCommission[$productId]['ProductTier']['commission'] ?? 0.00;

            return [
                'product_id' => $productId,
                'warehouse_id' => $preferredWarehouses[$productId] ?? null,
                'quantity' => $item['quantity'],
                'total_tax' => $item['total_tax'],
                'total_price' => $item['total_price'],
                'total_discount' => $item['total_discount'],
                'totalPriceConversion' => ($currencyConversion * $item['total_price']),
                'total_sales_commission' => ($storeAssociateId)
                    ? ($productCommissions[$productId]['sales_commission'] * $item['quantity'])
                    : 0,
                'total_retailer_commission' => ($isCommissionRetailer)
                    ? ($commission * $item['quantity'])
                    : 0,
            ];
        }, array_values($orderSummary['items']));

        $orderinfo = [
            'user_id' => $userId,
            'retailer_id' => $retailerId,
            'store_associate_id' => $storeAssociateId,
            'sales_rep_id' => $salesRepFields['Order']['sales_rep_id'],
            'distributor_id' => $salesRepFields['Order']['distributor_id'],
            'customerID' => $customerId,
            'customerEmail' => $customerEmail,
            'preOrderId' => $preorderId,
            'order_type' => $orderType,
            'subType' => $orderSubType,
            'is_install' => ($method === WidgetsDeliveryMethod::LOCAL_INSTALL),
            'secretcode' => $secretcode,
            'is_commission_retailer' => $isCommissionRetailer,
            'has_distributor' => $salesRepFields['Order']['has_distributor'],
            'order_status' => $orderStatus,
            'payment_status' => $paymentStatus,
            'payment_method' => $paymentMethod,
            'payment_method_subtype' => $paymentMethodSubtype,
            'total_price' => $totalPrice,
            'total_discount' => $orderSummary['discount']['amount'],
            'totalPriceConversion' => ($currencyConversion * $totalPrice),
            'total_sales_commission' => array_sum(array_column($orderProducts, 'total_sales_commission')),
            'shipearly_commission_fee' => ($storeAssociateId) ? $user['User']['store_associate_default_amount'] : 0,
            'total_retailer_commission' => array_sum(array_column($orderProducts, 'total_retailer_commission')),
            'currency_code' => $currencyCode,
            'total_tax' => $orderSummary['tax']['amount'],
            'tax_included' => $orderSummary['tax']['is_inclusive'],
            'shipping_amount' => $orderSummary['shipping']['amount'],
            'transactionID' => $transactionID,
            'stripe_account' => $stripe_account,
            'discount_code' => $discountCode,
            'billing_firstname' => $shippingAddress['first_name'],
            'billing_lastname' => $shippingAddress['last_name'],
            'shipping_company_name' => $shippingAddress['company_name'],
            'shipping_address1' => $shippingAddress['address1'],
            'shipping_address2' => $shippingAddress['address2'],
            'shipping_city' => $shippingAddress['city'],
            'shipping_state' => $shippingAddress['state_name'],
            'shipping_country' => $shippingAddress['country_name'],
            'shipping_zipcode' => $shippingAddress['zipcode'],
            'latitude' => $shippingAddress['latitude'],
            'longitude' => $shippingAddress['longitude'],
            'shipping_telephone' => $shippingAddress['telephone'],
            'shipping_statecode' => $shippingAddress['state_id'],
            'shipping_countrycode' => $shippingAddress['country_id'],
            'total_qty_ordered' => array_sum(array_column($orderProducts, 'quantity')),
            'shipearlyFees' => $this->Currency->formatAsDecimal($shipearlyFees, $currencyCode),
            'retailerAmount' => $this->Currency->formatAsDecimal($totalPrice - $shipearlyFees, $currencyCode),
            'card_type' => $cardType,
            'last_four_digit' => $lastFourDigit,
            'fraud_check_address' => $fraudCheckAddress,
            'fraud_check_postal_code' => $fraudCheckPostalCode,
            'fraud_check_cvc' => $fraudCheckCvc,
        ];

        $saveAssociated = [
            'Order' => $orderinfo,
            'OrderProduct' => $orderProducts,
            'OrderSalesRep' => $salesRepFields['OrderSalesRep'],
        ];

        $this->Order->bindModel([
            'hasMany' => ['OrderProduct', 'OrderSalesRep'],
        ], false);

        try {
            $this->Order->create();
            $success = (bool)$this->Order->saveAssociated($saveAssociated);
            $orderId = (int)$this->Order->id;

            if ($success) {
                /** @var OrderAddress $BillingAddress */
                $BillingAddress = ClassRegistry::init(['class' => 'OrderAddress', 'alias' => 'BillingAddress']);
                if (!$BillingAddress->saveBillingFromCheckoutWidget($orderId, $billingAddress)) {
                    triggerWarning(json_encode(['message' => 'Failed to save billing address', 'errors' => $BillingAddress->validationErrors, 'data' => $BillingAddress->data]));
                }

                $this->Preorder->markPaymentSuccess($preorderId, [
                    'paykey' => $transactionID,
                    'card_type' => $cardType,
                    'last_four_digit' => $lastFourDigit,
                    'fraud_check_address' => $fraudCheckAddress,
                    'fraud_check_postal_code' => $fraudCheckPostalCode,
                    'fraud_check_cvc' => $fraudCheckCvc,
                ]);
            }

            return $success ? $orderId : null;
        } finally {
            $this->Order->unbindModel([
                'hasMany' => ['OrderProduct', 'OrderSalesRep'],
            ], false);
        }
    }

    private function _afterCreateOrder(int $orderId, array $logs, string $method, string $customerEmail, array $shippingAddress, array $billingAddress)
    {
        $methodToShippingMethod = [
            WidgetsDeliveryMethod::IN_STORE => ShippingMethod::SHIP_EARLY,
            WidgetsDeliveryMethod::LOCAL_INSTALL => ShippingMethod::SHIP_EARLY,
            WidgetsDeliveryMethod::LOCAL_DELIVERY => ShippingMethod::SHIP_EARLY,
            WidgetsDeliveryMethod::SHIP_FROM_STORE => ShippingMethod::SHIP_FROM_STORE,
            WidgetsDeliveryMethod::SELL_DIRECT => ShippingMethod::SELL_DIRECT,
        ];
        $shippingMethod = $methodToShippingMethod[$method];

        $methodToShippingMethodSubtype = [
            WidgetsDeliveryMethod::LOCAL_INSTALL => ShippingMethod::SUB_TYPE_LOCAL_INSTALL,
            WidgetsDeliveryMethod::LOCAL_DELIVERY => ShippingMethod::SUB_TYPE_LOCAL_DELIVERY,
        ];
        $shipping_method_subtype = $methodToShippingMethodSubtype[$method] ?? '';

        $shipping = $this->_legacyAddressFormat($shippingAddress, $customerEmail);
        $billing = $this->_legacyAddressFormat($billingAddress, $customerEmail);

        $newOrder = $this->Order->get($orderId);

        $preorderId = $newOrder['Order']['preOrderId'];
        $brandId = $newOrder['Order']['user_id'];
        $retailerId = $newOrder['Order']['retailer_id'];
        $currencyCode = $newOrder['Order']['currency_code'];

        $productIds = array_column($logs['product'], 'id');

        $isCommissionRetailer = $newOrder['Order']['is_commission_retailer'];
        $isShipToStoreOnly = (
            !$isCommissionRetailer && (
                $this->ManufacturerRetailer->isShipToStoreOnly($brandId, $retailerId) ||
                !$this->Product->exists([
                    'Product.id' => $productIds,
                    'Product.sell_direct !=' => ProductSellDirect::EXCLUSIVELY,
                    'Product.is_fee_product' => false,
                    'Product.non_stocking' => false,
                ])
            )
        );
        $is_nonstock = ($newOrder['Order']['subType'] === OrderType::SUB_TYPE_NONSTOCK);

        $notificationOrder = array_merge($newOrder['Order'], [
            'customer_email' => $shipping['email'],
            'customer_firstname' => $shipping['First_name'],
            'customer_lastname' => $shipping['Last_name'],
            'shipping_telephone' => $shipping['phone'],
            'shipping_address' => [
                'street1' => $shipping['address'],
                'street2' => $shipping['address2'],
                'city' => $shipping['city'],
                'state' => $shipping['regionName'],
                'country' => $shipping['countryName'],
                'zipcode' => $shipping['PostalCode'],
            ],
        ]);
        $notificationRetailer = $this->User->findForEcommerceNotifications($retailerId);
        $notificationBrand = $this->User->findById($brandId, ['id', 'email_address', 'company_name', 'avatar'], null, -1)['User'];

        if ($isCommissionRetailer) {
            try {
                $this->OrderPlacer->createEcommerceConsumerOrder($orderId);
            } catch (Exception $e) {
                CakeLog::critical($e);
            }
        } elseif ($isShipToStoreOnly) {
            $this->OrderLogic->dealerOrderEvent($orderId);
            $this->NotificationLogic->handleShipToStoreOnlyNotification($orderId, $notificationOrder, $shippingMethod, $shipping_method_subtype);
        } elseif ($is_nonstock) {
            $this->WarehouseProductReservation->reserveLineItemSet($orderId, $this->OrderProduct->findAllForInventoryReservation($orderId));
        }

        if (!$isShipToStoreOnly) {
            $this->NotificationLogic->handleNotification($shippingMethod, $orderId, $notificationRetailer, $notificationBrand, $notificationOrder, $shipping_method_subtype);
        }

        $this->OrderLogic->updateChargeForNewOrder($orderId);

        if ($method === WidgetsDeliveryMethod::SHIP_FROM_STORE) {
            $this->Avatax->setTotalTaxFromEcommerce($currencyCode, $retailerId, $shipping, $newOrder['Order']['orderID'], $logs, $logs['product']);
        }

        $posShipping = $this->OrderPlacer->formatAddress($shipping, $customerEmail);
        $posBilling = $this->OrderPlacer->formatAddress($billing, $customerEmail);
        $this->OrderPlacer->createPosOrder($retailerId, $newOrder['Order'], $logs, $posShipping, $posBilling, $shippingMethod);

        $this->Preorder->markSuccess($preorderId);

        $this->Order->trackFbpixelPurchaseEvent($orderId);
    }

    private function _getSuccessViewVars(int $orderId): array
    {
        $this->Order->addAssociations([
            'belongsTo' => ['User'],
            'hasOne' => ['BillingAddress' => ['className' => 'OrderAddress', 'conditions' => ['BillingAddress.type' => OrderAddress::TYPE_BILLING]]],
            'hasMany' => ['OrderProduct'],
        ]);
        $newOrder = $this->Order->get($orderId, [
            'contain' => [
                'User' => ['fields' => ['id', 'email_address']],
                'BillingAddress' => ['fields' => ['id', 'first_name']],
                'OrderProduct' => ['fields' => ['id', 'product_id', 'quantity']],
            ],
            'fields' => [
                'id',
                'orderID',
                'order_type',
                'subType',
                'is_install',
                'secretcode',
                'payment_method',
                'payment_method_subtype',
                'card_type',
                'last_four_digit',
            ],
        ]);
        $this->Order->unbindModel([
            'belongsTo' => ['User'],
            'hasOne' => ['BillingAddress'],
            'hasMany' => ['OrderProduct'],
        ], false);

        $orderType = $newOrder['Order']['order_type'];
        $brandId = $newOrder['User']['id'];
        $brandEmail = $newOrder['User']['email_address'];

        $brandContent = $this->User->findEcommerceCustomContent($brandId, $brandEmail);
        $orderTypeToMessageKey = [
            OrderType::IN_STORE_PICKUP => 'instore_pickup_success_message',
            OrderType::LOCAL_DELIVERY => 'local_delivery_success_message',
            OrderType::SHIP_FROM_STORE => 'ship_from_store_success_message',
            OrderType::SELL_DIRECT => 'ship_to_door_success_message',
        ];
        $brandMessage = $brandContent[$orderTypeToMessageKey[$orderType]];

        $deliveryTime = '';
        if ($orderType === OrderType::IN_STORE_PICKUP) {
            $deliveryTime = __('Available Now');
            if ($newOrder['Order']['subType'] === OrderType::SUB_TYPE_NONSTOCK) {
                $backOrderStatus = $this->Product->exists($this->Product->getConditionsForActiveProducts([
                    'Product.id' => array_column($newOrder['OrderProduct'], 'product_id'),
                    'Product.user_id' => $brandId,
                    'Product.brand_inventory <=' => 0,
                ]));
                if ($backOrderStatus) {
                    $deliveryTime = ($newOrder['Order']['is_install'])
                        ? $brandContent['local_install_backorder_message']
                        : $brandContent['ship_to_store_backorder_message'];
                } else {
                    $deliveryTime = ($newOrder['Order']['is_install'])
                        ? $brandContent['local_install_no_stock_message']
                        : $brandContent['ship_to_store_message'];
                }
            }
        }

        return compact('newOrder', 'brandMessage', 'deliveryTime');
    }
}
