<?php

App::uses('BasicAuthenticate', 'Controller/Component/Auth');

/**
 * Custom Widgets Authentication.
 *
 * Provides client side authentication with a passed in client_id in addition
 * to Basic HTTP authentication support for AuthComponent.
 */
class WidgetsAuthenticate extends BasicAuthenticate
{
    public function getUser(CakeRequest $request)
    {
        list($username, $password) = $this->credentialsFromBasic($request);
        if (!$username) {
            list($username, $password) = $this->credentialsFromForm($request);
        }
        if (!$username) {
            list($username, $password) = $this->credentialsFromQuery($request);
        }
        if (!$username) {
            return false;
        }

        if (!$password) {
            $password = null;

            //TODO validate referrer domain against a whitelist
        }

        return $this->_findUser($username, $password);
    }

    /**
     * @param CakeRequest $request
     * @return string[] [username, password]
     */
    protected function credentialsFromBasic(CakeRequest $request): array
    {
        $username = env('PHP_AUTH_USER');
        $pass = env('PHP_AUTH_PW');
        if (!is_string($username) || $username === '') {
            $httpAuthorization = $request->header('Authorization');
            if (strlen($httpAuthorization) > 0 && strpos($httpAuthorization, 'Basic') !== false) {
                list($username, $pass) = explode(':', base64_decode(substr($httpAuthorization, 6)));
            }
        }

        if (!is_string($username)) {
            $username = '';
        }
        if (!is_string($pass)) {
            $pass = '';
        }

        return [$username, $pass];
    }

    /**
     * @param CakeRequest $request
     * @return string[] [username, password]
     */
    protected function credentialsFromForm(CakeRequest $request): array
    {
        list(, $model) = pluginSplit($this->settings['userModel']);
        $fields = (array)$this->settings['fields'];

        $username = $request->data($model . '.' . $fields['username']);
        $pass = $request->data($model . '.' . $fields['password']);
        if (!is_string($username) || $username === '') {
            $username = $request->data($fields['username']);
            $pass = $request->data($fields['password']);
        }

        if (!is_string($username)) {
            $username = '';
        }
        if (!is_string($pass)) {
            $pass = '';
        }

        return [$username, $pass];
    }

    /**
     * @param CakeRequest $request
     * @return string[] [username, password]
     */
    protected function credentialsFromQuery(CakeRequest $request): array
    {
        $field = (string)$this->settings['fields']['username'];

        $username = $request->query($field);
        if (!is_string($username)) {
            $username = '';
        }

        return [$username, ''];
    }
}
