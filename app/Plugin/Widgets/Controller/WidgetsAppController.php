<?php

App::uses('<PERSON><PERSON><PERSON>ontroller', 'Shim.Controller');
App::uses('AuthComponent', 'Controller/Component');
App::uses('User', 'Model');
App::uses('AppControllerLogResourceUsageTrait', 'Utility');

/**
 * Widgets AppController.
 *
 * @property AuthComponent $Auth
 * @property RequestHandlerComponent $RequestHandler
 * @property SecurityComponent $Security
 * @property TranslateComponent $Translate
 *
 * @property ApiClient $ApiClient
 * @property Configuration $Configuration
 * @property UserSubdomain $UserSubdomain
 */
// Skipping AppController in the class hierarchy because it does too much in its callbacks.
// Instead, copy any desired functionality to this class or one of its subclasses.
abstract class WidgetsAppController extends ShimController
{
    use AppControllerLogResourceUsageTrait;

    protected $_mergeParent = 'WidgetsAppController';

    public $helpers = [
        'Html' => ['className' => 'AppHtml', 'configFile' => 'html5_tags.php'],
        'Form' => ['className' => 'AppForm'],
        'Url' => ['className' => 'Shim.UrlShim'],
    ];

    public $components = [
        'Shim.Shim',
        'RequestHandler' => [
            'className' => 'Shim.RequestHandlerShim',
            'viewClassMap' => [
                'json' => 'Shim.JsonShim',
                'xml' => 'Xml',
            ],
        ],
        'Auth' => [
            'authenticate' => [
                'Widgets.Widgets' => [
                    'userModel' => 'ApiClient',
                    'fields' => ['username' => 'client_id', 'password' => 'client_secret'],
                    'scope' => ['User.user_type' => User::TYPE_MANUFACTURER, 'User.status' => 'Active', 'User.setup_status' => true],
                    'passwordHasher' => 'Api.PlainText',
                    'contain' => [
                        'User' => ['fields' => ['id', 'uuid', 'currency_code', 'brand_accent_color', 'checkout_font_family']],
                    ],
                    'userFields' => ['user_id', 'client_id'],
                ],
            ],
            'unauthorizedRedirect' => false,
        ],
        'Security' => [
            'csrfCheck' => false, // Requires 3rd party session cookies which cannot be guaranteed
        ],
        'Translate',
    ];

    public $uses = [
        'ApiClient',
        'Configuration',
        'UserSubdomain',
    ];

    public $viewClass = 'App';

    public function beforeFilter()
    {
        parent::beforeFilter();

        AuthComponent::$sessionKey = false;
        Configure::write('Exception.renderer', 'Widgets.WidgetsExceptionRenderer');
        // Set 'Config.language' before any Model TranslateBehavior callbacks
        Configure::write('Config.language', $this->Translate->getBrowserLanguage());
        $this->Configuration->bootstrap();
    }

    public function beforeRender()
    {
        parent::beforeRender();

        $this->set('lang', $this->Translate->getHtmlLang((string)Configure::read('Config.language')));
        $this->set('clientId', $this->Auth->user('client_id'));
        $this->set('accent_color', (
            $this->Auth->user('User.brand_accent_color') ?:
            ($this->UserSubdomain->findWhitelabelSettings((int)$this->Auth->user('User.id'))['accent_color'] ?? '') ?:
            null
        ));
        $this->set('font_family', $this->Auth->user('User.checkout_font_family'));
    }

    public function beforeRedirect($url, $status = null, $exit = true)
    {
        parent::beforeRedirect($url, $status, $exit);
        $this->logResourceUsage('timing');
    }

    public function afterFilter()
    {
        parent::afterFilter();
        $this->logResourceUsage('timing');
    }
}
