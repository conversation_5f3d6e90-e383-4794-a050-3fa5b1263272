<?php

App::uses('WidgetsAppController', 'Widgets.Controller');

/**
 * Widgets Error Handling Controller.
 *
 * A copy of CakeErrorController that inherits from WidgetsAppController instead of AppController.
 *
 * @see CakeErrorController
 */
class WidgetsErrorController extends WidgetsAppController
{
    public $uses = [];

    public function __construct($request = null, $response = null)
    {
        parent::__construct($request, $response);
        $this->constructClasses();
        if (
            count(Router::extensions()) &&
            !$this->Components->loaded('RequestHandler')
        ) {
            $this->RequestHandler = $this->Components->load('RequestHandler');
        }
        if ($this->Components->enabled('Auth')) {
            $this->Components->disable('Auth');
        }
        if ($this->Components->enabled('Security')) {
            $this->Components->disable('Security');
        }
        $this->cacheAction = false;
        $this->viewPath = 'Errors';
    }
}
