<?php

namespace ShipEarlyApp\Plugin\Widgets\Enum;

/**
 * Widgets Delivery Method Enum.
 *
 * Under the hood these are the retailer keys returned by /ws/getRetailerIds.
 *
 * @see WsController::getRetailerIds
 */
class WidgetsDeliveryMethod
{
    const IN_STORE = 'instore';
    const LOCAL_INSTALL = 'local_install';
    const LOCAL_DELIVERY = 'local_delivery';
    const SHIP_FROM_STORE = 'shipfromstore';
    const SELL_DIRECT = 'selldirect';
}
