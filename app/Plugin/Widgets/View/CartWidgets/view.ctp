<?php
/**
 * @var AppView $this
 * @var array $cart
 * @var string|null $accent_color
 */

$this->Html->script('CustomElements/shipearly-buy-button.js', [
    'id' => 'shipearly-buy-button-js',
    'fullBase' => true,
    'plugin' => false,
    'inline' => false,
    'block' => 'head_script',
]);
?>
<shipearly-buy-button
    cart-items="<?= /** @var AppView $this */ h(json_encode(array_values(array_map(
        fn(array $item): array => [
            'id' =>  $item['Product']['id'],
            'product_title' => $item['Product']['product_name'],
            'variant_title' => $item['Product']['variant_options'],
            'href' => 'javascript:void(0);',
            'price' => $this->Currency->formatAsDecimal($item['Product']['product_price'], $item['Product']['currency']),
            'quantity' => $item['quantity'],
            'max_quantity' => $item['Product']['max_quantity'],
            'image_src' => $item['Product']['product_image'] ?: (BASE_PATH . 'images/no_img.gif'),
            'image_alt' => __('Thumbnail'),
        ],
        (array)($cart['CartItem'] ?? [])
    )))) ?>"
    currency="<?= h($cart['Cart']['currency_code'] ?? '') ?>"
    labels="<?= h(json_encode((object)[
        'Open cart' => __('Open cart'),
        'Shopping cart' => __('Shopping cart'),
        'Close panel' => __('Close panel'),
        'Reduce item quantity by one' => __('Reduce item quantity by one'),
        'Increase item quantity by one' => __('Increase item quantity by one'),
        'Remove' => __('Remove'),
        'No items in cart' => __('No items in cart'),
        'Subtotal' => __('Subtotal'),
        'Shipping and taxes calculated at checkout.' => __('Shipping and taxes calculated at checkout.'),
        'Checkout' => __('Checkout'),
        'Cart count' => __('Cart count'),
        'Quantity' => __('Quantity'),
    ])) ?>"
    style="--accent-color: <?= h($accent_color ?? '#009fff') ?>;"
></shipearly-buy-button>
