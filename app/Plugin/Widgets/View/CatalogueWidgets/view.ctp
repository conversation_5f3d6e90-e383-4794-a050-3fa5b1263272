<?php

use ShipEarlyApp\Lib\Geolocation\Geolocation;

/**
 * @var AppView $this
 * @var string $clientId
 * @var array $brand
 * @var Geolocation $location
 * @var string $currencyCode
 * @var array $retailer
 * @var string[] $hiddenInputs
 * @var array $product
 */

$this->assign('title', trim($retailer['company_name'] . ' ' . __('Catalogue')));

$this->Html->css([
    'Widgets.CatalogueWidgets/view.css',
], ['inline' => false]);

$this->Html->script([
    'Widgets.CatalogueWidgets/view.js',
], ['inline' => false]);

$isSoldOut = ($product['Product']['max_quantity'] ?? 1) < 1;
$isInWarehouse = ($product['Product']['dealer_inventory'] ?? 1) < 1;
$isInStock = ($product['Product']['dealer_inventory'] ?? 1) > 0;

$statusLabel = '';
if ($isSoldOut) {
    $statusLabel = __('Sold Out');
} elseif ($isInWarehouse) {
    $statusLabel = __('In Warehouse');
} elseif ($isInStock) {
    $statusLabel = __('In Stock');
}
$submitLabel = (!$isSoldOut)
    ? __('Add to Cart')
    : __('Sold Out');

$variantMap = [];
foreach ($product['Variant'] as $variant) {
    $key = $variant['variant_options'];
    $variantMap[$key] = $variant['id'];
}

$variantOptionsDefaultValueByName = [];
foreach ($product['Variant'] as $variant) {
    if ($variant['id'] === $product['Product']['id']) {
        $variantOptionsDefaultValueByName = Hash::combine($variant['VariantOption'], '{n}.name', '{n}.ProductVariantOption.value');
        break;
    }
}
?>
<?= $this->Form->postButton(
    __('Back'),
    ['plugin' => 'widgets', 'controller' => 'catalogue_widgets', 'action' => 'index'],
    [
        'class' => 'catalogue-view__back-link btn btn-primary rounded-full! px-2 py-1',
        'data' => $hiddenInputs,
    ],
) ?>
<div id="CatalogueView">
    <div class="row flex-column flex-md-row mb-3">
        <div class="col order-1  mb-4 mb-md-0">
            <div class="catalogue-view-image-wrapper">
                <div class="catalogue-view-image notranslate text-center">
                    <img
                        src="<?= $product['Product']['product_image'] ?: (BASE_PATH . 'images/no_img.gif') ?>"
                        alt="<?= $product['Product']['product_name'] ?> image"
                        class="catalogue-view-image__img img-fluid"
                    >
                </div>
                <?php if (($product['Product']['retailDisplay'] ?? 'no') === 'yes'): ?>
                    <span class="badge-on-display-overlay" style="bottom: 3px;">
                        <i class="fa-solid fa-circle-check"></i> <?= __('On Display') ?>
                    </span>
                <?php elseif (($product['Product']['similarRetailDisplay'] ?? 'no') === 'yes'): ?>
                    <span class="badge-on-display-overlay" style="bottom: 3px;">
                        <i class="fa-solid fa-circle-check"></i> <?= __('Similar Model on Display') ?>
                    </span>
                <?php endif; ?>
            </div>
        </div>
        <div class="col order-2 order-md-1">
            <h3><?= $product['Product']['product_name'] ?></h3>
            <div class="row mb-2">
                <div class="col-6">
                    <?php
                    $productPrice = $product['Product']['product_price'];
                    $compareAtPrice = $product['Product']['compare_at_price'];
                    $currency = $product['Product']['currency'];
                    if ($compareAtPrice && floatval($compareAtPrice) > floatval($productPrice)) {
                        echo '<div class="text-muted text-decoration-line-through" style="display: block;">' .
                            $this->Currency->formatCurrency($compareAtPrice, $currency) .
                            '</div>';
                        echo '<div style="color: red; display: block;">' .
                            $this->Currency->formatCurrency($productPrice, $currency) .
                            '</div>';
                    } else {
                        echo $this->Currency->formatCurrency($productPrice, $currency);
                    }
                    ?>
                </div>
                <div class="col-6 text-end">
                    <?= $product['Product']['product_sku'] ?>
                </div>
            </div>
            <?= $this->Form->create(false, ['id' => 'CatalogueViewForm']) ?>
            <?= $this->Form->inputs(array_map(
                function($value) {
                    return ['type' => 'hidden', 'value' => $value, 'id' => false];
                },
                $hiddenInputs
            ), null, ['fieldset' => false]) ?>
            <div class="row">
                <div class="col<?= count($product['Variant']) > 1 ? '' : ' d-none' ?>">
                    <?= $this->Form->input('product_id', [
                        'id' => 'product_id',
                        'type' => 'select',
                        'options' => array_map(fn(array $variant): array => [
                            'value' => $variant['id'],
                            'name' => $variant['variant_options'] ?: __('Default'),
                            'data-url' => Router::url(['plugin' => 'widgets', 'controller' => 'catalogue_widgets', 'action' => 'view', 'id' => $variant['id']]),
                        ], $product['Variant']),
                        'value' => $product['Product']['id'],
                        'class' => 'form-select mb-4',
                        'label' => ['text' => __('Model'), 'class' => 'form-label'],
                        'div' => 'hidden',
                    ]) ?>
                    <?php foreach ($product['Product']['variantOptions'] as $option): ?>
                        <div class="col mt-2">
                            <?= $this->Form->input("variant_options.{$option['name']}", [
                                'type' => 'select',
                                'label' => $option['name'],
                                'options' => array_combine($option['values'], $option['values']),
                                'value' => $variantOptionsDefaultValueByName[$option['name']] ?? null,
                                'class' => 'form-select',
                                'div' => false,
                                'name' => "data[variant_options][{$option['name']}]"
                            ]) ?>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
            <div class="row mt-1 mb-4">
                <div class="col"><strong><?= $statusLabel ?></strong></div>
            </div>
            <div class="row align-items-end">
                <div class="col">
                    <?= $this->Form->input('quantity', [
                        'type' => 'number',
                        'min' => '0',
                        'max' => $product['Product']['max_quantity'],
                        'step' => '1',
                        'value' => '1',
                        'class' => 'form-control mb-4',
                        'label' => ['text' => __('Quantity'), 'class' => 'form-label'],
                        'div' => false,
                    ]) ?>
                </div>
                <div class="col">
                    <?= $this->Form->submit($submitLabel, [
                        'disabled' => $isSoldOut,
                        'name' => 'add_to_cart',
                        'class' => 'catalogue-view-submit' . (!$isSoldOut ? ' btn btn-primary' : ' btn btn-secondary') . ' mb-4',
                        'div' => false,
                    ]) ?>
                </div>
            </div>
            <?= $this->Form->end() ?>
        </div>
    </div>
    <div class="catalogue-view-description rte"><?= $product['Product']['product_description'] ?></div>
</div>
<?php $this->start('script'); ?>
<script>
    (new CatalogueWidgets.View({
        clientId: <?= json_encode($clientId) ?>,
        variantMap: <?= json_encode($variantMap) ?>
    })).bindEvents();
</script>
<?php $this->end(); ?>
