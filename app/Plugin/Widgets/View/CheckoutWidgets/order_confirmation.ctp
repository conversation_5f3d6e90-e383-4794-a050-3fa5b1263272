<?php

use Ship<PERSON>arlyApp\Lib\Geolocation\Geolocation;

/**
 * @var AppView $this
 * @var string $clientId
 * @var array $brand
 * @var array $products
 * @var Geolocation $location
 * @var array $retailer
 * @var array $deliveryMethod
 * @var array $newOrder
 * @var string $brandMessage
 * @var string $deliveryTime
 */

$this->assign('title', __('Order Confirmation'));

$this->Html->css([
    'Widgets.Elements/CheckoutWidgets/address_inputs.css',
    'Widgets.Elements/CheckoutWidgets/order_summary.css',
    'Widgets.CheckoutWidgets/order_confirmation.css',
], ['inline' => false]);

$this->Html->script([
    'Widgets.CheckoutWidgets/order_confirmation.js',
], ['inline' => false]);

$customerEmail = $this->request->data('Order.customerEmail');
$acceptsMarketing = $this->request->data('Customer.accepts_marketing');
$shippingAddress = $this->request->data('ShippingAddress');
$billingAddress = $this->request->data('BillingAddress');
$paymentMethod = $this->Orders->getPaymentMethod($newOrder);
$orderSummary = $deliveryMethod['order_summary'];
$hiddenInputs = null;

$weekdayToday = date('N');
$todayTiming = $retailer['store_timing'][$weekdayToday];
$hours = (!$todayTiming['Closed'])
    ? preg_replace('/ (AM|PM)/', '&nbsp;$1', $todayTiming['start'] . ' - ' . $todayTiming['end'])
    : 'Closed';
$timinglist = $retailer['html']['timinglist'];
?>
<?php $this->start('summary'); ?>
<?= $this->element('Widgets.CheckoutWidgets/retailer_summary', compact('retailer', 'hiddenInputs')) ?>
<?= $this->element('Widgets.CheckoutWidgets/delivery_method_summary', compact('deliveryMethod', 'customerEmail', 'acceptsMarketing', 'shippingAddress', 'hiddenInputs')) ?>
<?= $this->element('Widgets.CheckoutWidgets/billing_summary', compact('paymentMethod', 'billingAddress', 'hiddenInputs')) ?>
<?= $this->element('Widgets.CheckoutWidgets/order_summary', compact('orderSummary')) ?>
<?php $this->end(); ?>
<div class="success-content">
    <h3 class="content-section-title"><?= __('Order %s', $this->Html->tag('span', $newOrder['Order']['orderID'], ['id' => 'orderNo', 'class' => 'notranslate'])) ?></h3>
    <div class="text-center">
        <i class="far fa-check-circle text-success" style="font-size: 5rem;"></i>
        <h3 class="content-section-title">
            <?= __('Thank you %s', $newOrder['BillingAddress']['first_name']) ?>
        </h3>
    </div>
    <div id="brandsuccessmessage"><?= $brandMessage ?></div>
<?php if ($newOrder['Order']['secretcode']) { ?>
    <div class="text-center">
        <h3 class="content-section-title"><span><?= __('Verification Code') ?></span>:</h3>
        <div id="code" class="notranslate"><?= $newOrder['Order']['secretcode'] ?></div>
    </div>
<?php } ?>
    <h3 class="content-section-title"><span><?= __('Your Order is Being Fulfilled by') ?></span>:</h3>
    <div id="storeaddress" class="row">
        <div class="col">
            <address class="notranslate"><?= $this->FormatAddress->forEcommerceRetailerAddress($retailer) ?></address>
        </div>
        <div class="col">
        <?php if ($deliveryTime) { ?>
            <strong><span><?= __('Your estimated delivery date is') ?></span>:</strong>
            <div id="deliverytime"><?= $deliveryTime ?></div>
        <?php } ?>
            <div><span><?= __('Store Hours') ?>:</span> <a href="javascript:void(0);" id="storehours_timing"><?= $hours ?></a></div>
        </div>
    </div>
    <?= $this->Html->link(__('Back'), $brand['User']['shop_home_url'] ?: ('https://' . $brand['User']['shop_url']), [
        'target' => '_parent',
        'class' => 'btn btn-primary',
        'escapeTitle' => false,
    ]) ?>
</div>
<?php $this->start('script'); ?>
<script>
(function() {
    (new CheckoutWidgets.OrderConfirmation(<?= json_encode(compact('clientId', 'timinglist'), JSON_PRETTY_PRINT) ?>)).bindEvents();
})();
</script>
<?php $this->end(); ?>
