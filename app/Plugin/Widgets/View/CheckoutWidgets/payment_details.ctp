<?php

use ShipEarlyApp\Lib\Geolocation\Geolocation;

App::uses('OrderPaymentMethodSubtype', 'Utility');

/**
 * @var AppView $this
 * @var string $clientId
 * @var array $products
 * @var Geolocation $location
 * @var array $retailer
 * @var array $deliveryMethod
 * @var array $countries
 * @var array $states
 * @var array $brandContent
 * @var string $stripePlatformKey
 * @var int $stripeRetailerId
 * @var string $stripeAccount
 * @var \Stripe\PaymentIntent $paymentIntent
 * @var null|\Stripe\SetupIntent $setupIntent
 * @var string $payeeName
 */

$this->assign('title', __('Payment Details'));

$this->Html->css([
    'Widgets.Elements/CheckoutWidgets/address_inputs.css',
    'Widgets.Elements/CheckoutWidgets/order_summary.css',
    'Widgets.Elements/CheckoutWidgets/payment_methods.css',
], ['inline' => false]);

$this->Html->script([
    'Shopify.jquery.shipearly.ecommerce.js',
    'Widgets.Elements/CheckoutWidgets/address_inputs.js',
    'Widgets.Elements/CheckoutWidgets/payment_methods.js',
    'Widgets.CheckoutWidgets/payment_details.js',
], ['inline' => false]);

$getStatesUrl = Router::url(['plugin' => 'widgets', 'controller' => 'checkout_widgets', 'action' => 'get_states', '?' => ['client_id' => $clientId]]);
$validationUrl = Router::url(['plugin' => 'widgets', 'controller' => 'checkout_widgets', 'action' => 'validate_address', '?' => ['client_id' => $clientId]]);
$clonePlatformPaymentMethodUrl = Router::url(['plugin' => 'widgets', 'controller' => 'checkout_widgets', 'action' => 'clone_platform_payment_method']);
$postalCodeLabelByCountry = [
    'US' => __('Zip') . ' (12345)',
    'CA' => __('Postal Code') . ' (A1A 1A1)',
    'GB' => __('Postal Code'),
];

$customerEmail = $this->request->data('Order.customerEmail');
$acceptsMarketing = $this->request->data('Customer.accepts_marketing');
$shippingAddress = $this->request->data('ShippingAddress');

$hiddenInputs = [
    'client_id' => $clientId,
    'locator' => $this->request->data('locator'),
    'items' => $this->request->data('items'),
    'retailer_id' => $retailer['id'],
    'geolocation' => $location->toArray(),
    'method' => $deliveryMethod['method'],
    'Order.customerEmail' => $customerEmail,
    'Customer.accepts_marketing' => $acceptsMarketing,
    'ShippingAddress' => $shippingAddress ?: null,
    'stripeRetailerId' => $stripeRetailerId,
    'stripeAccount' => $stripeAccount,
    'paymentIntentId' => $paymentIntent->id,
    'setupIntentId' => $setupIntent->id ?? null,
    'ecommerceViewId' => $this->request->data('ecommerceViewId'),
];
$hiddenInputs = array_map('strval', array_filter(Hash::flatten($hiddenInputs), function($value) {
    return $value !== null;
}));

$orderSummary = $deliveryMethod['order_summary'];

$showBilling = (!$shippingAddress);

$stripePaymentSecret = $paymentIntent->client_secret;

$stripeCardAccount = $stripeAccount;
$stripeCardSecret = $stripePaymentSecret;
if ($setupIntent) {
    $stripeCardAccount = null;
    $stripeCardSecret = $setupIntent->client_secret;
}
?>
<?php $this->start('summary'); ?>
<?= $this->element('Widgets.CheckoutWidgets/retailer_summary', compact('retailer', 'hiddenInputs')) ?>
<?= $this->element('Widgets.CheckoutWidgets/delivery_method_summary', compact('deliveryMethod', 'customerEmail', 'acceptsMarketing', 'shippingAddress', 'hiddenInputs')) ?>
<?= $this->element('Widgets.CheckoutWidgets/order_summary', compact('orderSummary')) ?>
<?php $this->end(); ?>
<div class="payment-details-content">
    <div class="mb-3">
        <?= $this->Form->postLink(
            '<<< ' . __('Back'),
            ['plugin' => 'widgets', 'controller' => 'checkout_widgets', 'action' => 'delivery_methods'],
            ['data' => $hiddenInputs, 'class' => 'payment-details-back-link']
        ) ?>
    </div>
    <?= $this->Form->create(false, ['id' => 'PaymentDetailsForm', 'url' => ['plugin' => 'widgets', 'controller' => 'checkout_widgets', 'action' => 'order_confirmation']]) ?>
    <?= $this->Form->inputs(array_map(
        function($value) {
            return ['type' => 'hidden', 'value' => $value, 'id' => false];
        },
        $hiddenInputs
    ), null, ['fieldset' => false]) ?>
    <h3 class="content-section-title"><?= __('Billing Address') ?></h3>
    <?= $this->Form->input('Billing.inlineRadioOptions1', [
        'type' => 'radio',
        'options' => [
            'shipping' => __('Same as shipping address'),
            'newBilling' => __('Use a different billing address'),
        ],
        'default' => ($showBilling) ? 'newBilling' : 'shipping',
        'class' => 'form-check-input',
        'label' => ['class' => 'form-check-label'],
        'div' => 'payment-details-show-billing' . ($shippingAddress ? '' : ' d-none'),
        'before' => '<div class="form-check">',
        'separator' => '</div><div class="form-check">',
        'after' => '</div>',
        'legend' => false,
    ]) ?>
    <div class="payment-details-billing-address<?= ($showBilling) ? '' : ' d-none' ?>">
        <?= $this->element('Widgets.CheckoutWidgets/address_inputs', ['type' => 'billing', 'countries' => $countries, 'states' => $states]) ?>
    </div>
    <?= $this->element('Widgets.CheckoutWidgets/payment_methods', [
        'payeeName' => $payeeName,
        'initPaymentAmount' => $orderSummary['total']['amount'],
        'currency' => $orderSummary['currency'],
        'enableKlarna' => in_array('klarna', $paymentIntent->payment_method_types, true),
        'enableAffirm' => in_array('affirm', $paymentIntent->payment_method_types, true),
        'enableAffirmFinancing' => $brandContent['enable_affirm_financing'],
        'affirmFinancingAppPromoContent' => $brandContent['affirm_financing_app_promo'],
    ]) ?>
    <?= $this->Form->end() ?>
</div>
<?php $this->start('script'); ?>
<script>
(function() {
    window.PaymentMethodSubtypes = {
        CARD: <?= json_encode(OrderPaymentMethodSubtype::CARD) ?>,
        CARD_AFFIRM: <?= json_encode(OrderPaymentMethodSubtype::CARD_AFFIRM) ?>,
        AFFIRM: <?= json_encode(OrderPaymentMethodSubtype::AFFIRM) ?>,
        KLARNA: <?= json_encode(OrderPaymentMethodSubtype::KLARNA) ?>,
    };

    (new CheckoutWidgets.AddressInputs(<?= json_encode(compact('clientId', 'getStatesUrl', 'postalCodeLabelByCountry'), JSON_PRETTY_PRINT) ?>)).bindEvents();
    const paymentMethods = (new CheckoutWidgets.PaymentMethods(<?= json_encode(compact('clientId'), JSON_PRETTY_PRINT) ?>)).bindEvents();

    paymentMethods.showLoader(true);
    const stripeCardForm = new StripeCardForm(<?= json_encode($stripePlatformKey) ?>, <?= json_encode(STRIPE_API_VERSION) ?>, <?= json_encode($stripeCardAccount) ?>, <?= json_encode($stripeCardSecret) ?>, {
        cardNumber: { placeholder: <?= json_encode(__('Card number')) ?> },
        cardCvc: { placeholder: <?= json_encode($this->request->is('mobile') ? __('CVV') : __('Security code')) ?> }
    }, () => paymentMethods.showLoader(false));

    const stripePaymentClient = Stripe(<?= json_encode($stripePlatformKey) ?>, <?= json_encode(['apiVersion' => STRIPE_API_VERSION, 'stripeAccount' => $stripeAccount]) ?>);
    window?.AffirmFinancingAppPromo?.applyValues(<?= json_encode($payeeName) ?>, <?= json_encode($orderSummary['total']['amount_format']) ?>);

    (new CheckoutWidgets.PaymentDetails(<?= json_encode(compact(
        'clientId',
        'validationUrl',
        'clonePlatformPaymentMethodUrl',
        'customerEmail',
        'shippingAddress',
        'stripePaymentSecret'
    ), JSON_PRETTY_PRINT) ?>, stripeCardForm, stripePaymentClient)).bindEvents();
})();
</script>
<?php $this->end(); ?>
