<?php

use ShipEarlyApp\Plugin\Widgets\Enum\WidgetsLocator;

/**
 * @var AppView $this
 * @var array $retailer
 * @var bool|null $showFullAddress
 * @var array|null $hiddenInputs
 * @var array $stripePaymentMethods
 * @var string $productCountsByRetailerId
 */

$showFullAddress = !empty($showFullAddress);

$retailerEditLink = $this->Form->postButton(
    '<i class="fa-solid fa-chevron-left"></i> ' . __('Change Store'),
    ['plugin' => 'widgets', 'controller' => 'locator_widgets', 'action' => ($hiddenInputs['locator'] ?? WidgetsLocator::DEALERS)],
    [
        'data' => $hiddenInputs,
        'style' => 'color: white !important; text-decoration: none !important; cursor: pointer; font-weight: 800 !important; margin-left:4px;',
        'class' => 'edit-link btn-link' . (!$hiddenInputs ? ' invisible' : '')
    ]
);
$weekdayToday = date('N');
$timezone = ($retailer['timezone']) ? new DateTimeZone($retailer['timezone']) : null;
$currentTime = new DateTime('now', $timezone);
$storeTimings = $retailer['store_timing'];
$todayTiming = $storeTimings[$weekdayToday];
$isTodayClosed = ($todayTiming['Closed'] == '1');

$startTime = DateTime::createFromFormat('g:i A', $todayTiming['start'], $timezone);
$endTime = DateTime::createFromFormat('g:i A', $todayTiming['end'], $timezone);

$isOpenNow = false;
$willOpenLaterToday = false;
if (!$isTodayClosed && $startTime && $endTime) {
    if ($currentTime >= $startTime && $currentTime <= $endTime) {
        $isOpenNow = true;
    } elseif ($currentTime < $startTime) {
        $willOpenLaterToday = true;
    }
}
$daysOfWeek = [1 => __x('Days of week', 'Mon'), 2 => __x('Days of week', 'Tue'), 3 => __x('Days of week', 'Wed'), 4 => __x('Days of week', 'Thu'), 5 => __x('Days of week', 'Fri'), 6 => __x('Days of week', 'Sat'), 7 => __x('Days of week', 'Sun')];
$closedMessage = __('Closed');
if (!$isOpenNow) {
    if ($willOpenLaterToday) {
        $openTime = $todayTiming['start'];
        $openTimeFormatted = strtolower(date('g:ia', strtotime($openTime)));
        $closedMessage = sprintf('%s %s %s', __('Opens'), $openTimeFormatted, __('today'));
    } else {
        $nextOpenDay = null;
        for ($i = 1; $i <= 7; $i++) {
            $checkDay = ($weekdayToday + $i - 1) % 7 + 1;
            $timing = $storeTimings[$checkDay];
            if ($timing['Closed'] == '0') {
                $nextOpenDay = $checkDay;
                break;
            }
        }
        if ($nextOpenDay !== null) {
            $openTime = $storeTimings[$nextOpenDay]['start'];
            $openTimeFormatted = strtolower(date('g:ia', strtotime($openTime)));
            $dayLabel = ($nextOpenDay == $weekdayToday) ? __('today') : $daysOfWeek[$nextOpenDay];
            $closedMessage = sprintf('%s %s %s', __('Opens'), $openTimeFormatted, $dayLabel);
        }
    }
}
$hours = ($isOpenNow)
    ? strtolower($todayTiming['start'] . ' - ' . $todayTiming['end'])
    : $closedMessage;
?>
<style>
.retailer-item-body {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}
.retailer-item-hours {
    font-size: 0.8em;
    font-weight: 700;
    line-height: 1.5;
    text-align: center;
    text-decoration: none;
    color: #000 !important;
    font-family: "Georgia", "Times New Roman", serif;
}
.payment-type-icons-container {
    font-size: 12px;
    border-top: 1px solid #ccc;
    cursor: pointer;
}
.retailer-item-open {
    padding: 0.25rem 2.5rem;
    border: none;
    background-color: transparent;
    font-weight: bold;
    text-transform: uppercase;
}
.retailer-item-open.retailer-item-open--yes {
    color: #198754;
}
.retailer-item-open.retailer-item-open--no {
    color: #fd7e14;
}
.retailer-item-availability {
    border-left: 1px solid rgba(0,0,0,.125);
    padding-left: 0.5rem;
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
}
.retailer-item-description {
    flex: 2;
}
.retailer-name {
    margin-left: 10px;
    color: white;
}
.retailer-name-divider {
    color:white;
    margin-left:10px;
}
@media only screen and (min-width: 575px) {
    .retailer-name,
    .retailer-name-divider {
        display: none;
    }
}
@media only screen and (max-width: 575px) {
    .retailer-summary {
        display: none;
    }
}
.retailer-summary-address{
    font-size: .875em;
}
.edit-link:hover {
    font-size: 17px;
}
.badge-inventory-count {
    background-color: var(--accent-color);
    color: white;
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    font-size: 0.7rem;
    font-weight: 500;
    border-radius: 999px;
    line-height: 1;
}
.badge-inventory-count .count-circle {
    background-color: white;
    color: var(--accent-color);
    width: 14px;
    height: 14px;
    font-size: 10px;
    font-weight: 600;
    border-radius: 50%;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    margin-right: 5px;
}
</style>
<div class="bg-primary" style="padding:2px; display: flex; align-items: center;">
    <?= $retailerEditLink ?>
    <span class="retailer-name-divider"><?= '|' ?></span> <span class="retailer-name"><?=$retailer['company_name'] ?></span>
</div>
<div class="retailer-summary summary-section">
    <div class="retailer-item-body">
        <div class="retailer-item-description">
            <div class="retailer-summary-company font-extrabold"><?= $retailer['company_name'] ?></div>
            <div class="retailer-summary-address"><?= ($showFullAddress)
                ? $this->FormatAddress->forEcommerceRetailerAddress($retailer, false)
                : $retailer['city'] . ', ' . $retailer['state_name']
            ?></div>
        </div>
        <div class="retailer-item-availability">
            <div class="retailer-item-open <?= $isOpenNow ? 'retailer-item-open--yes' : 'retailer-item-open--no' ?>">
                <?= (isset($todayTiming['ByAppointment']) && $todayTiming['ByAppointment'] == '1') ? __('By Appointment') : ($isOpenNow ? __('Open') : __('Closed')) ?>
            </div>
            <div class="retailer-item-hours cursor-pointer"
                 tabindex="0"
                 data-tooltipster="<?= h(json_encode(['contentAsHTML' => true, 'position' => 'right'])) ?>"
                 title="<?= h($retailer['html']['timinglist']) ?>"
            ><?= $hours ?></div>
            <?php
            $retailerId = $retailer['id'];
            $productCount = $productCountsByRetailerId[$retailerId] ?? 0;
            ?>
            <?php if ($productCount > 0): ?>
                <div class="retailer-item-inventory-count">
                    <span class="badge badge-inventory-count rounded-pill mb-1">
                        <span class="count-circle"><?= h($productCount) ?></span>
                        <?= __n('Model on display', 'Models on display', $productCount) ?>
                    </span>
                </div>
            <?php endif; ?>
            <?php if ($stripePaymentMethods) { ?>
                <div class="payment-type-icons-container">
                    <?= $this->Ecommerce->stripePaymentTypeIconsDiv($stripePaymentMethods) ?>
                </div>
            <?php } ?>
        </div>
    </div>
</div>
