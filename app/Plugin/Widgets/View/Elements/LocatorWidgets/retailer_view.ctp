<?php

use Ship<PERSON>arlyApp\Lib\Geolocation\Geolocation;
use ShipEarlyApp\Plugin\Widgets\Enum\WidgetsLocator;

/**
 * @var AppView $this
 * @var string $clientId
 * @var string $locator
 * @var array $items
 * @var Geolocation $location
 * @var array $retailer
 * @var int|null $ecommerceViewId
 * @var array $brandContent
 * @var array $stripePaymentMethodsByRetailerId
 */

$buyNowUrl = ($locator === WidgetsLocator::PRODUCTS)
    ? ['plugin' => 'widgets', 'controller' => 'checkout_widgets', 'action' => 'delivery_methods']
    : ['plugin' => 'widgets', 'controller' => 'catalogue_widgets', 'action' => 'index'];

$disableBuyNow = (!$retailer['methods']);
if ($disableBuyNow) {
    $buyNowUrl = 'javascript:void(0);';
}

$hideBuyNow = $disableBuyNow;
$hideCallNow = !$hideBuyNow;

$hiddenInputs = [
    'client_id' => $clientId,
    'locator' => $locator,
    'items' => $items ?: null,
    'retailer_id' => $retailer['id'],
    'geolocation' => $location->toArray(),
    'ecommerceViewId' => $ecommerceViewId,
];
$hiddenInputs = array_map('strval', array_filter(Hash::flatten($hiddenInputs), fn($value): bool => $value !== null));
?>
<div class="retailer-view">
    <div class="retailer-view-item">
        <button class="retailer-view-item__back-link btn-link"><<< <?= __('Back') ?></button>
        <div><?= $this->element('Widgets.LocatorWidgets/retailer_item', [
            'locator' => $locator,
            'retailer' => $retailer,
            'brandContent' => $brandContent,
            'stripePaymentMethodsByRetailerId' => $stripePaymentMethodsByRetailerId,
        ]) ?></div>
    </div>
    <?= $this->Html->link(
        sprintf('<i class="fas fa-phone"></i> %s <span class="sr-only" data-mask-tel="true">%s</span>', __('Call Now'), $retailer['telephone']),
        "tel:{$retailer['telephone']}",
        [
            'class' => 'btn btn-outline-primary w-100 mb-3' . (!$hideCallNow ? '' : ' d-none'),
            'escapeTitle' => false,
        ]
    ) ?>
    <?= $this->Form->postButton(__('Buy Now'), $buyNowUrl, [
        'class' => 'btn btn-primary w-100' . (!$hideBuyNow ? '' : ' d-none'),
        'disabled' => $disableBuyNow,
        'data' => $hiddenInputs,
    ]) ?>
</div>
