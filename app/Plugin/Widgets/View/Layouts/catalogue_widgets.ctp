<?php

use Ship<PERSON>arlyApp\Lib\Geolocation\Geolocation;

/**
 * @var AppView $this
 * @var string $clientId
 * @var array $brand
 * @var array $brandContent
 * @var Geolocation $location
 * @var string $currencyCode
 * @var array $retailer
 * @var array $cartItems
 * @var string[] $hiddenInputs
 */

$this->extend('checkout_widgets');

// Prepend extended view blocks to workaround extended views being fetched after concrete views
$this->prepend('css', implode('', [
    $this->Html->css([
    ], ['plugin' => false]),
    $this->Html->css([
        'Widgets.Layouts/catalogue_widgets.css',
    ])
]));
$this->prepend('script', implode('', [
    $this->Html->script([
        'CustomElements/shipearly-buy-button.js',
    ], ['plugin' => false]),
    $this->Html->script([
        'Widgets.Layouts/catalogue_widgets.js',
    ])
]));

$showFullAddress = true;

// http_build_query() cannot match Google's Maps Static API spec
// https://developers.google.com/maps/documentation/maps-static
$staticMapQuery = implode('&', [
    //'center=' . "{$location->latitude},{$location->longitude}",
    'zoom=13',
    'size=600x400',
    'markers=' . implode(urlencode('|'), [
        'icon:' . $this->MapMarkers->getUri($brandContent['map_markers'], MapMarkersHelper::IN_STOCK_SELECTED),
        'size:30,32',
        'anchor:0,32',
        "{$retailer['latitude']},{$retailer['longitude']}",
    ]),
    'style=' . implode(urlencode('|'), [
        'feature:poi',
        'visibility:off',
    ]),
    'style=' . implode(urlencode('|'), [
        'feature:transit',
        'visibility:off',
    ]),
    'key=' . GOOGLE_GEOCODING_API_KEY,
]);
?>
<?php $this->start('summary'); ?>
<?= $this->element('Widgets.CheckoutWidgets/retailer_summary', compact('retailer', 'showFullAddress', 'hiddenInputs')) ?>
<div class="map-summary summary-section">
    <div class="text-center">
        <img alt="retailer location" src="<?= 'https://maps.googleapis.com/maps/api/staticmap?' . $staticMapQuery ?>" class="img-fluid mx-auto">
    </div>
</div>
<?php $this->end(); ?>
<?= $this->fetch('content') ?>
<shipearly-buy-button
    cart-items="<?= /** @var AppView $this */ h(json_encode(array_values(array_map(
        fn(array $item): array => [
            'id' =>  $item['id'],
            'product_title' => $item['product_name'],
            'variant_title' => $item['variant_options'],
            'href' => 'javascript:void(0);',
            'price' => $this->Currency->formatAsDecimal($item['product_price'], $item['currency']),
            'quantity' => $item['quantity'],
            'max_quantity' => $item['max_quantity'],
            'image_src' => $item['product_image'] ?: (BASE_PATH . 'images/no_img.gif'),
            'image_alt' => __('Thumbnail'),
        ],
        $cartItems
    )))) ?>"
    currency="<?= h($currencyCode) ?>"
    labels="<?= h(json_encode((object)[
        'Open cart' => __('Open cart'),
        'Shopping cart' => __('Shopping cart'),
        'Close panel' => __('Close panel'),
        'Reduce item quantity by one' => __('Reduce item quantity by one'),
        'Increase item quantity by one' => __('Increase item quantity by one'),
        'Remove' => __('Remove'),
        'No items in cart' => __('No items in cart'),
        'Subtotal' => __('Subtotal'),
        'Shipping and taxes calculated at checkout.' => __('Shipping and taxes calculated at checkout.'),
        'Checkout' => __('Checkout'),
        'Cart count' => __('Cart count'),
        'Quantity' => __('Quantity'),
    ])) ?>"
></shipearly-buy-button>
<div class="hidden" aria-hidden="true">
    <?= $this->Form->postButton(
        __('Checkout'),
        ['plugin' => 'widgets', 'controller' => 'checkout_widgets', 'action' => 'delivery_methods'],
        ['data' => $hiddenInputs, 'name' => 'checkout'],
    ) ?>
</div>
