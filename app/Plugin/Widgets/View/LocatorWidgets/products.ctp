<?php

use Ship<PERSON>arlyApp\Lib\Geolocation\Geolocation;

/**
 * @var AppView $this
 * @var string $clientId
 * @var array $brand
 * @var array $brandContent
 * @var Geolocation $defaultLocation
 * @var string $currencyCode
 * @var array $defaultPlace
 * @var int|null $ecommerceViewId
 * @var array $product
 */

$this->assign('title', trim($product['Product']['product_name'] . ' ' . __('Locations')));

$this->Html->css([
    'Widgets.LocatorWidgets/products.css',
    'Widgets.Elements/LocatorWidgets/retailer_item.css',
    'Widgets.Elements/LocatorWidgets/retailers_list.css',
], ['inline' => false]);

$this->Html->script([
    'Widgets.LocatorWidgets/products.js',
    'Widgets.Elements/LocatorWidgets/retailer_item.js',
], ['inline' => false]);

$getRetailersUrl = Router::url(['plugin' => 'widgets', 'controller' => 'locator_widgets', 'action' => 'get_retailers']);
$getPricingUrl = Router::url(['plugin' => 'widgets', 'controller' => 'locator_widgets', 'action' => 'products']);
$iconUrls = [
    'stock' => $this->MapMarkers->getUri($brandContent['map_markers'], MapMarkersHelper::IN_STOCK),
    'stock_selected' => $this->MapMarkers->getUri($brandContent['map_markers'], MapMarkersHelper::IN_STOCK_SELECTED),
    'nonstock' => $this->MapMarkers->getUri($brandContent['map_markers'], MapMarkersHelper::NOT_IN_STOCK),
    'nonstock_selected' => $this->MapMarkers->getUri($brandContent['map_markers'], MapMarkersHelper::NOT_IN_STOCK_SELECTED),
    'EndCustomer' => $this->Html->assetUrl('images/icons/EndCustomer.png', ['plugin' => false]),
];

$defaultProductDetails = $product['Product'];
?>
<div class="variant-container" data-id="">
    <div class="variant-container__label">
        <div class="position-relative">
            <div class="variant-thumbnail">
                <img alt="<?= __('Thumbnail') ?>" src="">
            </div>
            <div class="variant-quantity badge rounded-pill" title="<?= __('Quantity') ?>"></div>
        </div>
        <div class="variant-title">
            <label for="variant-selection" class="variant-title__product"></label>
            <select id="variant-selection" class="variant-title__variant-select form-select"></select>
        </div>
    </div>
    <div class="variant-container__amount"></div>
</div>
<div class="retailers-container">
    <div class="retailers-list-container">
        <div class="retailers-list--message retailers-list d-none">
            <span class="retailers-list-message__content"></span>
        </div>
        <div class="retailers-list--stock retailers-list d-none">
            <div class="retailers-list-items list-group"></div>
        </div>
        <div class="retailers-list--nonstock retailers-list d-none">
            <div class="retailers-list-items list-group"></div>
        </div>
    </div>
    <div class="retailer-view-container d-none">
    </div>
</div>
<?php $this->start('script'); ?>
<script>
window.initMap = (new LocatorWidgets.Products(<?= json_encode(compact(
    'clientId',
    'getPricingUrl',
    'getRetailersUrl',
    'iconUrls',
    'defaultPlace',
    'ecommerceViewId',
    'defaultProductDetails'
), JSON_PRETTY_PRINT) ?>))
    .bindEvents()
    .getInitMap();
</script>
<?php $this->end(); ?>
