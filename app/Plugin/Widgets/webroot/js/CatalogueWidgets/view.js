window.CatalogueWidgets = (function(CatalogueWidgets, $) {
    class View {
        settings = {
            clientId: (window.xprops) ? window.xprops.client_id : null,
            variantMap: {},
            baseUrl: `/widgets/catalogue/`
        };
        selectors = {
            ajaxContent: '#CatalogueView',
            checkoutForm: 'form#CatalogueViewForm',
            variantSelect: '#product_id',
            variantOptionsSelects: '#CatalogueViewForm [name^="data[variant_options]"]',
            addToCart: '#CatalogueViewForm [name="add_to_cart"]',
        }

        constructor(settings, selectors) {
            Object.assign(this.settings, settings);
            Object.assign(this.selectors, selectors);
        }

        bindEvents() {
            const self = this;

            $(document).on('change', self.selectors.variantSelect, function(e) {
                e.preventDefault();
                const $select = $(this);
                const url = $select.find('option:selected').data('url');
                self.loadAjaxContent(url);
            });

            $(document).on('change', self.selectors.variantOptionsSelects, function() {
                const variantOptions = {}
                $(self.selectors.variantOptionsSelects).each(function(i, option) {
                    const match = option.name.match(/^data\[variant_options\]\[(.+?)\]$/);
                    if (match) {
                        const optionName = match[1];
                        variantOptions[optionName] = option.value;
                    }
                });
                self.updateProductId(variantOptions);
            });
            return self;
        }

        updateProductId(variantOptions) {
            const $productIdInput = $(this.selectors.variantSelect);
            const productId = this.getProductIdFromVariants(variantOptions);
            $productIdInput.val(productId);

            const productIdIsMissingOrDisabled = !$productIdInput.val();
            if (productIdIsMissingOrDisabled) {
                this.showVariantError('This combination of options is not available.');
                $(this.selectors.addToCart).prop('disabled', true);
                return;
            }

            // Reload the view with the selected variant; also removes any displayed errors
            $productIdInput.trigger('change');
        }

        showVariantError(message) {
            const errorHtml = `<div class="alert alert-warning variant-error mt-2">${message}</div>`;
            if (!$('.variant-error').length) {
                $(this.selectors.variantSelect).closest('.col').append(errorHtml);
            }
        }

        clearVariantError() {
            $('.variant-error').remove();
        }

        getProductIdFromVariants(variantOptions) {
            const keys = Object.values(variantOptions);
            const key = keys.join(' / ');
            return this.settings.variantMap[key] || null;
        }

        loadAjaxContent(url) {
            const $form = $(this.selectors.checkoutForm);
            const data = $form.serialize();

            return this.#postPromise(url, data).then((response) => {
                const $newContent = $('<div></div>').html(response).find(this.selectors.ajaxContent);
                $(this.selectors.ajaxContent).replaceWith($newContent);
            });
        }

        #postPromise(url, data, dataType) {
            return new Promise((resolve, reject) => $.post(url, data, resolve, dataType).fail(reject));
        }
    }

    return Object.assign(CatalogueWidgets, { View });
})(window.CatalogueWidgets || {}, jQuery);
