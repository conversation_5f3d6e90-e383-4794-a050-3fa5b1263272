window.CheckoutWidgets = (function(CheckoutWidgets, $) {
    class AddressInputs {
        settings = {
            clientId: (window.xprops) ? window.xprops.client_id : null,
            getStatesUrl: '/widgets/checkout/get_states',
            postalCodeLabelByCountry: {
                US: 'Zip (12345)',
                CA: 'Postal Code (A1A 1A1)',
                GB: 'Postal Code',
            }
        };

        constructor(settings) {
            Object.assign(this.settings, settings);

            if (!this.settings.clientId) {
                throw 'Missing required setting clientId.';
            }
        }

        bindEvents() {
            const settings = this.settings;

            $('.address-inputs select[name$="[country_id]"]').on('change', function() {
                const countryCode = $('option:selected', this).data('code');
                const $parent = $(this).closest('.address-inputs');
                $parent.find('input[name$="[telephone]"]').maskPhone(countryCode);
                $parent.find('input[name$="[zipcode]"]').maskPostalCode(countryCode, settings.postalCodeLabelByCountry);
            }).bindGetStatesOnChange({
                url: settings.getStatesUrl
            }).trigger('change');

            $('#address').on('focus', $.initAutocomplete);
            return this;
        }
    }

    return Object.assign(CheckoutWidgets, { AddressInputs: AddressInputs });
})(window.CheckoutWidgets || {}, jQuery);
