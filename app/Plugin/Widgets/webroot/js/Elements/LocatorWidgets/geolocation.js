window.LocatorWidgets = (function (LocatorWidgets, $) {
    class Geolocation {
        location = null;
        addressComponents = null;

        constructor(place) {
            this.location = (typeof place.geometry.location.toJSON === 'function')
                ? place.geometry.location.toJSON()
                : place.geometry.location;
            this.addressComponents = (place.address_components || []).reduce((map, component) => {
                component.types.forEach(function(type) {
                    map[type] = {
                        long_name: component.long_name,
                        short_name: component.short_name,
                        type: type,
                    };
                });
                return map;
            }, {});

            this.addressComponents.get = (type, namePreference) => this.addressComponents[type]?.[namePreference || 'long_name'] || '';
        }

        toRequest() {
            const location = this.location;
            const addressComponents = this.addressComponents;
            return {
                latitude: location.lat,
                longitude: location.lng,
                address1: (addressComponents.get('street_number') + ' ' + addressComponents.get('route')).trim(),
                city: addressComponents.get('locality') || addressComponents.get('sublocality_level_1') || addressComponents.get('postal_town'),
                state_code: addressComponents.get('administrative_area_level_1', 'short_name'),
                state_name: addressComponents.get('administrative_area_level_1', 'long_name'),
                country_code: addressComponents.get('country', 'short_name'),
                country_name: addressComponents.get('country', 'long_name'),
                zipcode: addressComponents.get('postal_code') || addressComponents.get('postal_code_prefix'),
            };
        }
    }

    return Object.assign(LocatorWidgets, { Geolocation: Geolocation });
})(window.LocatorWidgets || {}, jQuery);
