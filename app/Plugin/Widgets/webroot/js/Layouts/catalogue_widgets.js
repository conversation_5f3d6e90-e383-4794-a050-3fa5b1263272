$(function() {
    $('shipearly-buy-button').on('change', function() {
        const newItemInputs = JSON.parse($(this).attr('cart-items') || '[]')
            .map((item, index) => [
                `<input type="hidden" name="data[items][${index}][product_id]" value="${item.id}">`,
                `<input type="hidden" name="data[items][${index}][quantity]" value="${item.quantity}">`,
            ].join(''))
            .join('')
        ;
        $('form[method="post"]')
            // Assert that the form contains something from `$hiddenInputs` that is not empty.
            .filter((i, form) => $('input[type="hidden"][name="data[locator]"]', form).length > 0)
            .each(function(i, form) {
                const $form = $(form);
                $form.find('input[type="hidden"][name^="data[items]"]').remove();
                $form.append(newItemInputs);
            })
        ;
    }).on('submit', function() {
        $('[name="checkout"][type="submit"]').click();
    });
});
