window.LocatorWidgets = (function(LocatorWidgets, $) {
    class Dealers {
        settings = {
        };
        Map;
        RetailerItem;

        constructor(settings) {
            Object.assign(this.settings, settings);
            this.Map = new LocatorWidgets.Map(this.settings);
            this.RetailerItem = new LocatorWidgets.RetailerItem(this.settings, this.Map);
        }

        bindEvents() {
            const self = this;

            $(document).on('click', '.retailer-item[data-id]', function(e) {
                const retailerId = $(this).data('id');

                if (!self.RetailerItem.isRetailerViewLoaded(retailerId)) {
                    self.RetailerItem.loadRetailerView(retailerId);
                }
                if (!self.RetailerItem.isRetailerViewLoaded(retailerId)) {
                    throw new Error(`Failed to load view for .retailer-item[data-id="${retailerId}"]`);
                }

                const $submit = $('.retailer-view-container form button[type="submit"]:enabled');
                if ($submit.length) {
                    // Bypass map focus
                    e.stopImmediatePropagation();
                    $submit.trigger('click');
                }
            });

            self.RetailerItem.bindEvents();

            return self;
        }

        getInitMap() {
            return this.Map.getInitMap();
        }
    }

    return Object.assign(LocatorWidgets, { Dealers: Dealers });
})(window.LocatorWidgets || {}, jQuery);
