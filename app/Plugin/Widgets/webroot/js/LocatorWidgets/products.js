// Originally written in vanilla JS because jQuery wasn't included until it was required for Tooltipster
// and the Ajax call to get_retailers noted further below
// TODO Consider rewriting to use more jQuery
window.LocatorWidgets = (function(LocatorWidgets, $) {
    class Products {
        settings = {
            clientId: (window.xprops) ? window.xprops.client_id : null,
            getPricingUrl: null,
            defaultProductDetails: {
                id: '',
                product_image: '',
                quantity: '',
                product_name: '',
                amount_format: '',
                variants: {},
            },
        };
        Map;
        RetailerItem;

        constructor(settings) {
            settings = Object.assign(this.settings, settings);
            this.Map = new LocatorWidgets.Map(settings);
            this.RetailerItem = new LocatorWidgets.RetailerItem(settings, this.Map);

            if (!this.settings.clientId) {
                throw 'Missing required setting clientId.';
            }
            if (!this.settings.getPricingUrl) {
                throw 'Missing required setting getPricingUrl.';
            }
        }

        bindEvents() {
            const self = this;

            setTimeout(function() {
                self.#updateElements(self.settings.defaultProductDetails);
            }, 0);

            self.Map.onPlaceChanged(function(place) {
                const productId = $('#variant-selection').val();
                const quantity = $('.variant-quantity').text();
                return self.#getPricing(productId, quantity, new LocatorWidgets.Geolocation(place))
                    .then((response) => {
                        self.#updateElements(response.product.Product);
                    })
                    .catch((error) => console.error(error));
            });
            $('#variant-selection').on('change', function() {
                self.Map.triggerPlaceChanged();
            });

            self.RetailerItem.bindEvents();

            return self;
        }

        getInitMap() {
            return this.Map.getInitMap();
        }

        #updateElements(productDetails) {
            const $container = $('.variant-container');
            $container.attr('data-id', productDetails.id);
            $container.find('.variant-thumbnail > img').attr('src', productDetails.product_image);
            $container.find('.variant-quantity').text(productDetails.quantity);
            $container.find('.variant-title__product').text(productDetails.product_name);

            const $variantSelect = $container.find('#variant-selection');
            const variantIds = Object.keys(productDetails.variants);
            const disableVariantSelect = (variantIds.length <= 1);
            $variantSelect.empty();
            variantIds.map(function(id) {
                $('<option></option>')
                    .attr('value', id)
                    .text(productDetails.variants[id])
                    .attr('selected', id === productDetails.id)
                    .appendTo($variantSelect);
            });
            $variantSelect.prop('disabled', disableVariantSelect).toggle(!disableVariantSelect);

            const $amountContainer = $container.find('.variant-container__amount');
            if (productDetails.compare_at_price && parseFloat(productDetails.compare_at_price) > parseFloat(productDetails.product_price)) {
                $amountContainer.html(
                    `<span class="text-muted text-decoration-line-through" style="display: block;">${productDetails.compare_at_price} ${productDetails.currency}</span>
                    <span style="color: red; display: block;">${productDetails.amount_format}</span>`
                );
            } else {
                $amountContainer.text(productDetails.amount_format);
            }

            return this;
        }

        #getPricing(productId, quantity, geolocation) {
            const settings = this.settings;

            return new Promise((resolve, reject) => {
                $.post(settings.getPricingUrl, {
                    client_id: settings.clientId,
                    items: [{
                        product_id: productId,
                        quantity: quantity,
                    }],
                    geolocation: geolocation.toRequest(),
                }, function(response) {
                    resolve(response);
                }, 'JSON').fail(function(jqXHR, status, error) {
                    reject(error);
                });
            });
        }
    }

    return Object.assign(LocatorWidgets, { Products: Products });
})(window.LocatorWidgets || {}, jQuery);
