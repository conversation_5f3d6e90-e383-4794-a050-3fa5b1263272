<?php
App::uses('AppController', 'Controller');
App::uses('CakeRequest', 'Network');
App::uses('CakeResponse', 'Network');
App::uses('AppModelValidationsTrait', 'Model/Behavior/Trait');
App::uses('OrderPaymentMethod', 'Utility');
App::uses('OrderPaymentMethodSubtype', 'Utility');
App::uses('OrderPaymentStatus', 'Utility');
App::uses('OrderType', 'Utility');
App::uses('PreorderType', 'Utility');
App::uses('StripePaymentType', 'Stripe.Enum');

/**
 * Class WoocommerceController.
 *
 * @property AddressValidatorComponent $AddressValidator
 * @property CurrencyComponent $Currency
 * @property OrderPlacerComponent $OrderPlacer
 * @property WoocommerceComponent $Woocommerce
 * @property ShopifyAPIComponent $ShopifyAPI
 * @property ShopifyComponent $Shopify
 * @property OrderLogicComponent $OrderLogic
 * @property NotificationLogicComponent $NotificationLogic
 * @property LightspeedComponent $Lightspeed
 * @property SquarePosComponent $SquarePos
 * @property AvataxComponent $Avatax
 * @property ShopifyPOSComponent $ShopifyPOS
 * @property StripeComponent $Stripe
 * @property QuickbookComponent $Quickbook
 * @property VendPOSComponent $VendPOS
 *
 * @property User $User
 * @property Preorder $Preorder
 * @property Customer $Customer
 * @property CustomerAddress $CustomerAddress
 * @property Order $Order
 * @property OrderAddress $OrderAddress
 * @property OrderProduct $OrderProduct
 * @property EmailTemplate $EmailTemplate
 * @property Store $Store
 * @property ProductRetailer $ProductRetailer
 * @property AppModel $Notifycustomer
 * @property Page $Page
 * @property Btask $Btask
 * @property ShippingZone $ShippingZone
 * @property StripeUser $StripeUser
 * @property StripeUserCapability $StripeUserCapability
 * @property QuickbookProduct $QuickbookProduct
 * @property UserSetting $UserSetting
 * @property ManufacturerRetailer $ManufacturerRetailer
 * @property Discount $Discount
 * @property DiscountUsage $DiscountUsage
 * @property WarehouseProductReservation $WarehouseProductReservation
 */
class WoocommerceController extends AppController
{
    /**
     * @var string
     */
    public $name = 'Woocommerce';

    /**
     * @var array
     */
    public $components = [
        'AddressValidator',
        'Currency',
        'OrderPlacer',
        'Woocommerce.Woocommerce',
        'Shopify.ShopifyAPI',
        'Shopify.Shopify',
        'OrderLogic',
        'NotificationLogic',
        'Lightspeed',
        'SquarePos',
        'Avatax',
        'Shopifypos.ShopifyPOS',
        'Stripe.Stripe',
        'Quickbook.Quickbook',
        'Vendpos.VendPOS',
    ];

    /**
     * @var array
     */
    public $uses = [
        'User',
        'Preorder',
        'Customer',
        'CustomerAddress',
        'Order',
        'OrderAddress',
        'OrderProduct',
        'EmailTemplate',
        'Store',
        'ProductRetailer',
        'Notifycustomer',
        'Page',
        'Btask',
        'ShippingZone',
        'StripeUser',
        'StripeUserCapability',
        'Quickbook.QuickbookProduct',
        'UserSetting',
        'ManufacturerRetailer',
        'Discount',
        'DiscountUsage',
        'WarehouseProductReservation',
    ];

    /**
     * @var string
     * @see Controller::$layout
     */
    public $layout = '';

    /**
     * @var bool
     * @see Controller::$layout
     */
    public $autoRender = false;

    /**
     *
     */
    public function beforeFilter()
    {
        parent::beforeFilter();
        $this->Auth->allow();
    }

    private function _getPluginVersion(): string
    {
        $versionHeader = ((string)CakeRequest::header('X-ShipearlyWooCommerce-Version')) ?: null;
        $versionBeforeHeader = '2.13.0';

        return $versionHeader ?? $versionBeforeHeader;
    }

    /**
     * @param int|null|string $selectedShippingCountryId
     * @return CakeResponse
     * @deprecated Since 2.19. Use get_billing_countries, get_shipping_countries and get_states instead.
     * @see WoocommerceController::get_billing_countries
     * @see WoocommerceController::get_shipping_countries
     * @see WoocommerceController::get_states
     */
    public function getDropDownLists($selectedShippingCountryId = null)
    {
        $billingUrl = Router::url(['plugin' => 'woocommerce', 'controller' => 'woocommerce', 'action' => 'get_billing_countries']);
        $billingResponse = json_decode($this->requestAction($billingUrl, ['data' => $this->request->data, 'ext' => 'json']), true);

        $countryUrl = Router::url(['plugin' => 'woocommerce', 'controller' => 'woocommerce', 'action' => 'get_shipping_countries', 'id' => $selectedShippingCountryId]);
        $countryResponse = json_decode($this->requestAction($countryUrl, ['data' => $this->request->data, 'ext' => 'json']), true);

        $selectedShippingCountryId = ((int)$countryResponse['shippingCountryId']) ?: null;

        $stateUrl = Router::url(['plugin' => 'woocommerce', 'controller' => 'woocommerce', 'action' => 'get_states', 'id' => $selectedShippingCountryId]);
        $stateResponse = json_decode($this->requestAction($stateUrl, ['data' => $this->request->data, 'ext' => 'json']), true);

        $user = $this->User->authenticateFromEcommerce($this->request, ['id', 'company_name', 'shop_url']);
        deprecationWarning("2.19 {$this->request->here()} is deprecated. Use {$billingUrl}, {$countryUrl} and {$stateUrl} instead. " . json_encode(['plugin_version' => $this->_getPluginVersion()] + $user), 0);

        $response = [
            'country' => array_column($countryResponse['shippingCountries'], 'country_name', 'id'),
            'states' => array_column($stateResponse['states'], 'state_name', 'id'),
            'billingCountry' => array_column($billingResponse['billingCountries'], 'country_name', 'id'),
        ];
        $this->response->body(json_encode($response));

        return $this->response;
    }

    /**
     * @param int|null $countryId
     * @return CakeResponse
     * @deprecated Since 2.19. Use the country codes provided by get_billing_countries instead.
     * @see WoocommerceController::get_billing_countries
     */
    public function getUniversalCountryCode($countryId = null)
    {
        $countryUrl = Router::url(['plugin' => 'woocommerce', 'controller' => 'woocommerce', 'action' => 'get_billing_countries']);
        deprecationWarning("2.19 {$this->request->here()} is deprecated. Use the country codes provided by {$countryUrl} instead. " . json_encode(['plugin_version' => $this->_getPluginVersion()]), 0);

        $this->response->body($this->Country->getCountryCode($countryId));

        return $this->response;
    }

    public function get_billing_countries(): ?CakeResponse
    {
        $user = $this->User->authenticateFromEcommerce($this->request, ['id']);

        $billingCountries = $this->Country->findAllBillingCountriesById();

        $response = [
            'billingCountries' => array_values($billingCountries),
        ];
        $this->response->body(json_encode($response));

        return $this->response;
    }

    public function get_shipping_countries(?int $shippingCountryId = null): ?CakeResponse
    {
        $user = $this->User->authenticateFromEcommerce($this->request, ['id', 'currency_code']);

        $userId = (int)$user['User']['id'];
        $currencyCode = (string)($this->request->data('currency_code') ?: $user['User']['currency_code']);

        $shippingCountries = $this->Country->findAllShippingCountriesById($userId);
        if (!isset($shippingCountries[$shippingCountryId])) {
            $shippingCountryId = $this->Country->getDefaultSelectedCountryId($currencyCode, $shippingCountries);
        }

        $response = [
            'shippingCountries' => array_values($shippingCountries),
            'shippingCountryId' => $shippingCountryId,
        ];
        $this->response->body(json_encode($response));

        return $this->response;
    }

    public function get_states(?int $countryId = null): ?CakeResponse
    {
        $user = $this->User->authenticateFromEcommerce($this->request, ['id', 'company_name', 'shop_url']);

        $userId = (int)$user['User']['id'];
        $countryId = (int)$countryId;

        if (!isset($this->request->params['is_shipping'])) {
            $billingUrl = Router::url(['plugin' => 'woocommerce', 'controller' => 'woocommerce', 'action' => 'get_states', 'is_shipping' => false, 'id' => $countryId]);
            $shippingUrl = Router::url(['plugin' => 'woocommerce', 'controller' => 'woocommerce', 'action' => 'get_states', 'is_shipping' => true, 'id' => $countryId]);
            deprecationWarning("2.26 {$this->request->here()} is deprecated. Use {$billingUrl} or {$shippingUrl} instead. " . json_encode(['plugin_version' => $this->_getPluginVersion()] + $user), 0);
        }

        $states = $this->request->param('is_shipping')
            ? $this->State->findAllShippingStatesById($userId, $countryId)
            : $this->State->findAllBillingStatesById($countryId);

        $response = [
            'states' => array_values($states),
        ];
        $this->response->body(json_encode($response));

        return $this->response;
    }

    public function validate_items(): ?CakeResponse
    {
        $user = $this->User->authenticateFromEcommerce($this->request, ['id', 'currency_code']);

        $brandId = (int)$user['User']['id'];
        $brandCurrency = (string)$user['User']['currency_code'];

        $items = (array)$this->request->data['items'];
        $currency = (string)$this->request->data('currency');

        $products = $this->Product->findAllWithRegionalPricing($brandId, $currency, [
            'conditions' => $this->Product->getConditionsForActiveProducts([
                'Product.productID' => array_column($items, 'product_id'),
            ]),
            'fields' => [
                'Product.id',
                'Product.productID',
                'Product.product_price',
                'Product.compare_at_price',
                'Product.currency',
                'Product.product_title',
                'Product.product_name',
            ],
        ], $brandCurrency);

        $regionalCurrency = current(Hash::extract($products, '{n}.RegionalPrice.currency_code')) ?: $brandCurrency;
        $productMap = Hash::combine($products, '{n}.Product.productID', '{n}');

        // Filter out invalid products
        $correctedItems = array_filter($items, fn(array $item): bool => array_key_exists($item['product_id'], $productMap));

        $quantityByProductID = (array)array_reduce($correctedItems, function($map, $item) {
            return $map + [$item['product_id'] => $item['qty']];
        }, []);
        $oversoldInventoryByProductID = $this->Product->listOversoldInventoryByProductID($brandId, $quantityByProductID);

        $correctedItems = array_map(fn(array $item): array => array_merge($item, [
            'qty' => (int)($oversoldInventoryByProductID[$item['product_id']] ?? $item['qty']),
            'price' => $productMap[$item['product_id']]['RegionalPrice']['price'],
        ]), $correctedItems);
        $correctedItems = array_filter($correctedItems, fn(array $item): bool => $item['qty'] > 0);

        $isValid = ($regionalCurrency === $currency && $correctedItems === $items);

        $correctedItems = array_map(fn(array $item): array => array_merge($item, [
            // Note that WC_Product 'name' is the full title with variant
            'name' => $productMap[$item['product_id']]['Product']['product_title'],
            'title' => $productMap[$item['product_id']]['Product']['product_name'],
        ]), $correctedItems);

        $response = [
            'is_valid' => $isValid,
            'items' => $correctedItems,
            'currency' => $regionalCurrency,
        ];
        $this->response->body(json_encode($response));

        return $this->response;
    }

    public function stripePayment(): ?CakeResponse
    {
        $user = $this->User->authenticateFromEcommerce($this->request, ['id', 'company_name']);
        $version = $this->_getPluginVersion();

        $missingFieldNames = $this->_getMissingArrayKeys($this->request->data, [
            'retailerId',
            'type',
        ]);
        $missingFieldNamesForPaymentIntents = $this->_getMissingArrayKeys($this->request->data, [
            'shipping.email',
            'shipping.province',
            'retailerInfo',
        ]);
        if ($missingFieldNames) {
            throw new BadRequestException('Missing required fields: ' . implode(', ', $missingFieldNames));
        }

        $brandId = (int)$user['User']['id'];
        $retailerId = (int)$this->request->data('retailerId');
        $type = (string)$this->request->data('type');
        $shipping = (array)$this->request->data('shipping');
        $retailerInfo = (array)$this->request->data('retailerInfo');
        $sellExclusiveResult = (array)$this->request->data('sellExclusiveResult');
        if ($type === 'sellDirect') {
            // Courier shipping calc would go here if implemented in plugin
            $sellExclusiveResult = [];
        }

        $stripePaymentIdsByAccount = (array)$this->request->data('stripePaymentIdsByAccount');
        $stripeSellExclusivePaymentIdsByAccount = (array)$this->request->data('stripeSellExclusivePaymentIdsByAccount');
        $stripeSetupIdsByAccount = (array)$this->request->data('stripeSetupIdsByAccount');

        // Be careful that legacy requests may not provide 'shipping'
        $shippingAddress = ($shipping) ? $this->_processAddressFormFields($shipping) : [];
        $shippingCountryId = (int)($shippingAddress['country'] ?? 0);
        $shippingStateId = (int)($shippingAddress['province'] ?? 0);

        $stripeRetailer = $this->StripeUser->getEcommerceStripePayee($brandId, $retailerId, $shippingCountryId, $shippingStateId);
        if (empty($stripeRetailer['StripeUser']['stripe_user_id'])) {
            throw new NotFoundException(sprintf('Stripe account not found where user_id=%s and retailer_id=%s', $brandId, $retailerId));
        }
        $stripe_account = (string)$stripeRetailer['StripeUser']['stripe_user_id'];
        $stripeRetailerId = (int)$stripeRetailer['StripeUser']['user_id'];
        $isCommissionRetailer = (bool)$stripeRetailer['ManufacturerRetailer']['is_commission_tier'];

        $sellExclusiveId = !empty($sellExclusiveResult['id']) ? (int)$sellExclusiveResult['id'] : null;
        $sell_exclusive_stripe_account = $this->StripeUser->getSellExclusiveAccountId($sellExclusiveId, $type, $shippingCountryId, $shippingStateId);

        $isPlatformCustomer = (
            $stripeRetailer['User']['user_type'] === User::TYPE_RETAILER
            || ($sell_exclusive_stripe_account && $sell_exclusive_stripe_account !== $stripe_account)
        );
        if (!$isPlatformCustomer) {
            $cus_owner_user_id = $stripeRetailerId;
            $cus_owner_stripe_account = $stripe_account;
        } else {
            $cus_owner_user_id = null;
            $cus_owner_stripe_account = null;
        }

        if ($missingFieldNamesForPaymentIntents) {
            deprecationWarning($version . ' legacy request ' . json_encode($this->request->data), 0);

            $this->response->body(json_encode([
                'success' => true,
                'stripe_platform_key' => STRIPE_PUBLISHABLE_KEY,
                'stripe_account' => $cus_owner_stripe_account,
                'key' => (!$isPlatformCustomer) ? $stripeRetailer['StripeUser']['stripe_publishable_key'] : STRIPE_PUBLISHABLE_KEY,
                'rname' => ($isCommissionRetailer) ? $user['User']['company_name'] : $this->request->data['companyName'],
                'amount' => round($this->request->data['amount'] * 100),
                'currency' => $this->request->data['currency'],
            ]));

            return $this->response;
        }

        $retailerName = ($isCommissionRetailer) ? $user['User']['company_name'] : $retailerInfo['company_name'];
        $currencyCode = (string)$retailerInfo['currency'];
        $amount = $this->_extractRetailerTotalPriceDiscounted($type, $retailerInfo);
        $sellExclusiveAmount = $sellExclusiveResult['totalwithshippingdiscount'] ?? $sellExclusiveResult['totalwithshipping'] ?? '0.00';
        $combinedAmount = $this->Currency->formatAsDecimal($amount + $sellExclusiveAmount, $currencyCode);

        $stripeUserCapabilities = $this->StripeUserCapability->getUserCapabilities($stripeRetailer['StripeUser']['id']);
        $paymentMethodTypes = StripePaymentType::getPaymentMethods($stripeUserCapabilities, (bool)$sellExclusiveResult);

        $paymentMethodTypeFilter = OrderPaymentMethodSubtype::toStripePaymentType((string)$this->request->data('paymentMethodSubtype'));
        if ($paymentMethodTypeFilter) {
            $paymentMethodTypes = array_values(array_intersect($paymentMethodTypes, [$paymentMethodTypeFilter]));
        }

        $cardIsSetupIntent = ($isPlatformCustomer || $sellExclusiveResult);

        if ($this->request->param('filter') === 'payment_methods') {
            $this->response->body(json_encode([
                'success' => true,
                'stripe_platform_key' => STRIPE_PUBLISHABLE_KEY,
                'stripe_api_version' => STRIPE_API_VERSION,
                'stripe_payment_types' => $this->Stripe->_filterPaymentMethodTypes($paymentMethodTypes, (float)$amount, $currencyCode),
                'stripe_payment_account' => $stripe_account,
                'stripe_payment_secret' => null,
                'stripe_payment_id' => null,
                'stripe_sell_exclusive_payment_account' => $sell_exclusive_stripe_account,
                'stripe_sell_exclusive_payment_secret' => null,
                'stripe_sell_exclusive_payment_id' => null,
                'stripe_setup_account' => ($cardIsSetupIntent) ? $cus_owner_stripe_account : null,
                'stripe_setup_secret' => null,
                'stripe_setup_id' => null,
                'stripe_card_account' => ($cardIsSetupIntent) ? $cus_owner_stripe_account : $stripe_account,
                'stripe_card_secret' => null,
                'stripe_card_id' => null,
                'rname' => $retailerName,
                'currency' => $currencyCode,
                'amount' => (int)round($combinedAmount * 100),
                'amount_format' => $this->Currency->formatCurrency($combinedAmount, $currencyCode, false, true),
            ]));

            return $this->response;
        }

        $stripeCustomerId = $this->Stripe->createPaymentCustomerIfNotExists((string)$shippingAddress['email'], $cus_owner_user_id, $cus_owner_stripe_account, $shippingAddress);

        $application_fee = $amount;
        $sell_exclusive_application_fee = null;
        if ($type === 'sellDirect' || $isCommissionRetailer || $retailerInfo['type'] !== 'nonstock' || $sellExclusiveResult) {
            $revenueModels = $this->User->findById($brandId, [
                'User.revenue_model',
                'User.retailer_default_amount',
                'User.retailer_revenue_maximum',
                'User.brand_revenue_model',
                'User.brand_direct_default_amount',
                'User.brand_revenue_maximum',
            ], null, -1)['User'];

            if ($type === 'sellDirect') {
                $application_fee = $this->OrderLogic->CalculateFees($amount, 'brand', $revenueModels['brand_revenue_model'], $revenueModels['brand_direct_default_amount'], $revenueModels['brand_revenue_maximum']);
            } elseif ($isCommissionRetailer || $retailerInfo['type'] !== 'nonstock') {
                $application_fee = $this->OrderLogic->CalculateFees($amount, 'retailer', $revenueModels['revenue_model'], $revenueModels['retailer_default_amount'], $revenueModels['retailer_revenue_maximum']);
            }
            if ($sellExclusiveResult) {
                $sell_exclusive_application_fee = $this->OrderLogic->CalculateFees($sellExclusiveAmount, 'brand', $revenueModels['brand_revenue_model'], $revenueModels['brand_direct_default_amount'], $revenueModels['brand_revenue_maximum']);
            }
        }

        $paymentParams = [
            'description' => 'Charge for Your Order',
            'metadata' => [],
            'payment_method_types' => $paymentMethodTypes,
            'shipping' => [
                'address' => [
                    'line1' => $shippingAddress['address'],
                    'line2' => $shippingAddress['address2'] ?: null,
                    'city' => $shippingAddress['city'],
                    'state' => $shippingAddress['regionName'],
                    'country' => $shippingAddress['countryCode'],
                    'postal_code' => $shippingAddress['PostalCode'],
                ],
                'name' => trim($shippingAddress['First_name'] . ' ' . $shippingAddress['Last_name']),
                'phone' => $shippingAddress['phone'],
            ],
            'application_fee_amount' => (int)round($application_fee * 100),
            'capture_method' => 'manual',
        ];
        if (!$isPlatformCustomer) {
            $paymentParams['customer'] = $stripeCustomerId;
        }
        $brand_stripe_account = $this->StripeUser->getAccountId($brandId);
        if ($stripe_account !== $brand_stripe_account) {
            $brandDescriptor = $this->Stripe->getStatementDescriptor($brand_stripe_account, $user['User']['company_name']);
            if ($brandDescriptor) {
                // 'card' payments use 'statement_descriptor_suffix' instead of 'statement_descriptor'
                $paymentParams = array_merge($paymentParams, [
                    'statement_descriptor' => $brandDescriptor,
                    'statement_descriptor_suffix' => $brandDescriptor,
                ]);
            }
        }

        $payment = $this->Stripe->initEcommercePaymentIntent(
            $stripePaymentIdsByAccount[$stripe_account] ?? null,
            $amount,
            $currencyCode,
            $paymentParams,
            compact('stripe_account')
        );
        $stripePaymentMethodTypes = $payment->payment_method_types;
        $stripePaymentAccount = $stripe_account;
        $stripePaymentSecret = $payment->client_secret;
        $stripePaymentId = $payment->id;

        $sellExclusivePayment = null;
        if ($sellExclusiveResult) {
            $sellExclusivePaymentParams = array_merge($paymentParams, [
                'payment_method_types' => ['card'],
                'application_fee_amount' => (int)round($sell_exclusive_application_fee * 100),
            ]);
            if ($sell_exclusive_stripe_account === $brand_stripe_account) {
                unset(
                    $sellExclusivePaymentParams['statement_descriptor'],
                    $sellExclusivePaymentParams['statement_descriptor_suffix'],
                );
            }
            $sellExclusivePayment = $this->Stripe->initEcommercePaymentIntent(
                $stripeSellExclusivePaymentIdsByAccount[$sell_exclusive_stripe_account] ?? null,
                $sellExclusiveAmount,
                $currencyCode,
                $sellExclusivePaymentParams,
                ['stripe_account' => $sell_exclusive_stripe_account]
            );
        }
        $stripeSellExclusivePaymentAccount = ($sellExclusivePayment) ? $sell_exclusive_stripe_account : null;
        $stripeSellExclusivePaymentSecret = $sellExclusivePayment->client_secret ?? null;
        $stripeSellExclusivePaymentId = $sellExclusivePayment->id ?? null;

        $setup = null;
        if ($cardIsSetupIntent) {
            $setup = $this->Stripe->saveCardSetupIntent(
                $stripeSetupIdsByAccount[$cus_owner_stripe_account] ?? null,
                $cus_owner_stripe_account,
                $stripeCustomerId,
                $stripePaymentId,
                $stripe_account,
                $stripeSellExclusivePaymentId,
                $sell_exclusive_stripe_account
            );
        }
        $stripeSetupAccount = ($setup) ? $cus_owner_stripe_account : null;
        $stripeSetupSecret = $setup->client_secret ?? null;
        $stripeSetupId = $setup->id ?? null;

        $this->response->body(json_encode([
            'success' => true,
            'stripe_platform_key' => STRIPE_PUBLISHABLE_KEY,
            'stripe_api_version' => STRIPE_API_VERSION,
            'stripe_payment_types' => $stripePaymentMethodTypes,
            'stripe_payment_account' => $stripePaymentAccount,
            'stripe_payment_secret' => $stripePaymentSecret,
            'stripe_payment_id' => $stripePaymentId,
            'stripe_sell_exclusive_payment_account' => $stripeSellExclusivePaymentAccount,
            'stripe_sell_exclusive_payment_secret' => $stripeSellExclusivePaymentSecret,
            'stripe_sell_exclusive_payment_id' => $stripeSellExclusivePaymentId,
            'stripe_setup_account' => $stripeSetupAccount,
            'stripe_setup_secret' => $stripeSetupSecret,
            'stripe_setup_id' => $stripeSetupId,
            'stripe_card_account' => ($setup) ? $stripeSetupAccount : $stripePaymentAccount,
            'stripe_card_secret' => ($setup) ? $stripeSetupSecret : $stripePaymentSecret,
            'stripe_card_id' => ($setup) ? $stripeSetupId : $stripePaymentId,
            'rname' => $retailerName,
            'currency' => $currencyCode,
            'amount' => (int)round($combinedAmount * 100),
            'amount_format' => $this->Currency->formatCurrency($combinedAmount, $currencyCode, false, true),
        ]));

        return $this->response;
    }

    public function clone_platform_payment_method(): ?CakeResponse
    {
        $users = $this->User->authenticateFromEcommerce($this->request, ['User.id']);
        $this->request->allowMethod('post');

        $setupId = $this->request->data('setup_intent_id');
        $setupAccount = $this->request->data('setup_account');

        /** @var null|\Stripe\PaymentIntent $sellExclusivePayment */
        list($payment, $sellExclusivePayment) = $this->Stripe->cloneCardSetupToPayments($setupId, $setupAccount);

        $response = [
            'payment_client_secret' => $payment->client_secret,
            'payment_method_id' => $payment->payment_method,
            'sell_exclusive_payment_client_secret' => $sellExclusivePayment->client_secret ?? null,
            'sell_exclusive_payment_method_id' => $sellExclusivePayment->payment_method ?? null,
        ];
        $this->response->body(json_encode($response));

        return $this->response;
    }

    public function preorders(): ?CakeResponse
    {
        $users = $this->User->authenticateFromEcommerce($this->request, ['User.id']);
        $this->request->allowMethod('post');

        CakeLog::info(json_encode(['request' => ['data' => $this->request->data]]), ['woocommerce']);

        $brandId = (int)$users['User']['id'];
        $retailerId = (int)$this->request->data('retailer_id');

        $type = (string)$this->request->data('type');
        $typeToShippingMethod = [
            'instore' => 'shipearly_shipearly',
            'local_install' => 'shipearly_shipearly',
            'local_delivery' => 'shipearly_shipearly',
            'shipFromStore' => 'shipfromstore_shipfromstore',
            'sellDirect' => 'selldirect_selldirect',
        ];
        $shipping_method = $typeToShippingMethod[$type];

        $logdata = (array)json_decode($this->request->data('logdata'), true);

        // Adapt requests from plugins before 2.19 payment intents
        $logdata['Order']['payment_method_subtype'] = $logdata['Order']['payment_method_subtype'] ?? OrderPaymentMethodSubtype::CARD;

        $logdata['shipping'] = $this->_processAddressFormFields($logdata['shipping']);
        $logdata['billing'] = $this->_processAddressFormFields($logdata['billing']);

        $currentStoreAmount = (array)$logdata['log'];
        if (empty($currentStoreAmount)) {
            throw new BadRequestException('Retailer not found where ' . json_encode(compact('type', 'retailerId')));
        }

        $sellExclusiveId = !empty($currentStoreAmount['sellExclusive']['id']) ? (int)$currentStoreAmount['sellExclusive']['id'] : null;
        $shippingCountryId = (int)$logdata['shipping']['country'];
        $shippingStateId = (int)$logdata['shipping']['province'];

        $stripeRetailer = $this->StripeUser->getEcommerceStripePayee($brandId, $retailerId, $shippingCountryId, $shippingStateId);
        if (empty($stripeRetailer['StripeUser']['stripe_user_id'])) {
            throw new NotFoundException(sprintf('Stripe account not found where user_id=%s and retailer_id=%s', $brandId, $retailerId));
        }
        $stripeAccount = (string)$stripeRetailer['StripeUser']['stripe_user_id'];
        $stripeRetailerId = (int)$stripeRetailer['StripeUser']['user_id'];
        $paymentId = (string)$this->request->data("stripePaymentIdsByAccount.{$stripeAccount}") ?: null;

        $sellExclusiveStripePayee = $this->StripeUser->getSellExclusiveStripePayee($sellExclusiveId, $type, $shippingCountryId, $shippingStateId);
        $sellExclusiveStripeAccount = (string)($sellExclusiveStripePayee['StripeUser']['stripe_user_id'] ?? '') ?: null;
        $sellExclusiveStripeRetailerId = (int)($sellExclusiveStripePayee['StripeUser']['user_id'] ?? 0) ?: null;
        $sellExclusivePaymentId = (string)$this->request->data("stripeSellExclusivePaymentIdsByAccount.{$sellExclusiveStripeAccount}") ?: null;

        $preorder = $this->Preorder->createForEcommerce(
            PreorderType::WOOCOMMERCE,
            $brandId,
            $retailerId,
            null,
            (string)$paymentId,
            $this->request->data('currency_code'),
            $this->_extractRetailerTotalPrice($type, $currentStoreAmount),
            $this->_extractRetailerTotalPriceDiscounted($type, $currentStoreAmount),
            array_column($currentStoreAmount['product'], 'id'),
            array_merge($logdata, [
                'type' => $type,
                'shipping_method' => $shipping_method,
                'shippingAmount' => $this->_extractShippingAmount($type, $currentStoreAmount),
                'tax' => $this->_extractTax($type, $currentStoreAmount),
                'stripeToken' => $this->request->data('stripeToken'),
                'stripe_retailer_id' => $stripeRetailerId,
                'stripe_account' => $stripeAccount,
                'payment_intent' => $paymentId,
                'sell_exclusive_stripe_retailer_id' => $sellExclusiveStripeRetailerId,
                'sell_exclusive_stripe_account' => $sellExclusiveStripeAccount,
                'sell_exclusive_payment_intent' => $sellExclusivePaymentId,
            ])
        );
        if (empty($preorder['id'])) {
            throw new BadRequestException(json_encode(['errors' => $this->Preorder->validationErrors, 'data' => $this->Preorder->data]));
        }

        $response = [
            'id' => $preorder['id'],
            'url' => Router::url(['plugin' => 'woocommerce', 'controller' => 'woocommerce', 'action' => 'placeOrder', 'id' => $preorder['id']], true),
        ];
        CakeLog::info(json_encode(compact('response')), ['woocommerce']);

        $this->response->body(json_encode($response));

        return $this->response;
    }

    public function placeOrder($preorderId = null): ?CakeResponse
    {
        if (!$preorderId) {
            $preordersUrl = ['plugin' => 'woocommerce', 'controller' => 'woocommerce', 'action' => 'preorders'];
            $preordersResponse = json_decode($this->requestAction($preordersUrl, ['data' => $this->request->data, 'ext' => 'json']), true);
            $preorderId = $preordersResponse['id'];
        } else {
            CakeLog::info(json_encode(['request' => ['preorder_id' => $preorderId, 'data' => $this->request->data]]), ['woocommerce']);
        }

        $users = $this->User->authenticateFromEcommerce($this->request);

        $brandId = (int)$users['User']['id'];

        $preorder = $this->Preorder->findForPlaceOrder($preorderId, $brandId, PreorderType::WOOCOMMERCE);
        CakeLog::debug(json_encode(compact('preorder')), ['woocommerce']);

        $logs = (array)json_decode($preorder['log'], true);
        if (!$logs) {
            throw new NotFoundException('Preorder.log data not found where id=' . json_encode($preorderId));
        }

        $stripeToken = ((string)$logs['stripeToken'] ?? '') ?: null;
        $response = (array)json_decode($this->_shipearlyPay($stripeToken, $preorder, $users), true);

        if ($response['status'] === 'success') {
            if (!empty($logs['discountInfo']['id'])) {
                $this->DiscountUsage->saveCustomerUsage($brandId, $logs['discountInfo']['id'], $logs['discountInfo']['email']);
            }
        }

        CakeLog::info(json_encode(compact('response')), ['woocommerce']);
        $this->response->body(json_encode($response));

        return $this->response;
    }

    /**
     * @param string|null $stripeToken
     * @param array $preorder
     * @param array $users
     * @return string
     */
    protected function _shipearlyPay(?string $stripeToken, array $preorder, array $users)
    {
        $brandId = (int)$preorder['user_id'];
        $lastid = (int)$preorder['id'];

        $log = (array)json_decode($preorder['log'], true);

        try {
            if (!$stripeToken) {
                $stripe_account = (string)$log['stripe_account'];
                $payment = $this->Stripe->confirmPaymentIfNotConfirmed($stripe_account, (string)$log['payment_intent']);
                $sellExclusivePayment = !empty($log['sell_exclusive_stripe_account'])
                    ? $this->Stripe->confirmPaymentIfNotConfirmed((string)$log['sell_exclusive_stripe_account'], (string)$log['sell_exclusive_payment_intent'])
                    : null;

                try {
                    $stripeRetailerId = (int)($log['stripe_retailer_id'] ?? $brandId);
                    $shipping = (array)$log['shipping'];
                    $billing = (array)$log['billing'];
                    $this->_formatInput($shipping, $billing);

                    $this->Stripe->updatePaymentCustomerWithBilling($payment, $stripeRetailerId, $stripe_account, $billing, $shipping);
                } catch (Exception $e) {
                    CakeLog::warning($e);
                }

                $paymentDetails = $this->Stripe->getPaymentMethodDetails($payment, $stripe_account);
            } else {
                list($payment, $sellExclusivePayment) = $this->_shipearlyLegacyCharges($stripeToken, $preorder, $users);
                $paymentDetails = $this->Stripe->getPaymentMethodDetailsFromCharge($payment);
            }

            $this->Preorder->markPaymentSuccess($lastid, [
                'paykey' => $paymentDetails->transactionID,
                'card_type' => $paymentDetails->card_type,
                'last_four_digit' => $paymentDetails->last_four_digit,
                'fraud_check_address' => $paymentDetails->fraud_check_address,
                'fraud_check_postal_code' => $paymentDetails->fraud_check_postal_code,
                'fraud_check_cvc' => $paymentDetails->fraud_check_cvc,
            ]);
        } catch (Exception $e) {
            if ($e instanceof \Stripe\Exception\ApiErrorException) {
                CakeLog::error('[' . get_class($e) . '] ' . json_encode($e->getJsonBody()), ['woocommerce', 'stripe']);
            }
            CakeLog::error($e, ['woocommerce', 'stripe']);

            $errorMessage = $e->getMessage();
            if ($e instanceof \Stripe\Exception\CardException) {
                $errorMessage = $e->getError()->code;
                if ($e->getError()->decline_code === \Stripe\Charge::DECLINED_FRAUDULENT) {
                    $errorMessage = 'Fraud User';
                }
            }

            $this->Preorder->markPaymentError($lastid);

            return json_encode(['status' => 'error', 'data' => ['message' => $errorMessage]]);
        }

        try {
            $Preorder = $this->Preorder->findForPaymentSuccess($lastid);
            $sellExclusiveTransactionId = isset($sellExclusivePayment->id) ? $sellExclusivePayment->id : '';
            return $this->_paymentSuccess($users, $Preorder, $payment->id, $sellExclusiveTransactionId);
        } catch (Exception $e) {
            CakeLog::error($e, ['woocommerce']);
            $this->Preorder->markOrderError($lastid);
            return json_encode(array('status' => 'error', 'data' => array('message' => $e->getMessage())));
        }
    }

    /**
     * @param string $stripeToken
     * @param array $preorder
     * @param array $users
     * @return \Stripe\Charge[] [charge, sell_exclusive_charge]
     * @throws \Stripe\Exception\ApiErrorException
     * @throws \Stripe\Exception\CardException
     */
    private function _shipearlyLegacyCharges(string $stripeToken, array $preorder, array $users): array
    {
        $lastid = (int)$preorder['id'];
        $brandId = (int)$preorder['user_id'];
        $retailerId = (int)$preorder['retailer_id'];
        $log = (array)json_decode($preorder['log'], true);

        deprecationWarning($this->_getPluginVersion() . ' legacy request ' . json_encode(['stripeToken' => $stripeToken, 'preorder' => array_merge($preorder, ['log' => $log])]), 0);

        $shippingCountryId = (int)$log['shipping']['country'];
        $shippingStateId = (int)$log['shipping']['province'];

        $stripeRetailer = $this->StripeUser->getEcommerceStripePayee($brandId, $retailerId, $shippingCountryId, $shippingStateId);
        if (empty($stripeRetailer['StripeUser']['stripe_user_id'])) {
            throw new InternalErrorException(sprintf('Stripe account not found where user_id=%s and retailer_id=%s', $brandId, $retailerId));
        }

        $sellExclusiveId = !empty($log['log']['sellExclusive']['id']) ? (int)$log['log']['sellExclusive']['id'] : null;
        $sellExclusiveStripePayee = $this->StripeUser->getSellExclusiveStripePayee($sellExclusiveId, (string)$log['type'], $shippingCountryId, $shippingStateId);
        if (!empty($sellExclusiveStripePayee['StripeUser']['stripe_user_id'])) {
            $stripeRetailer['sellExclusive'] = $sellExclusiveStripePayee['StripeUser'];
        }

        $customer = $this->_createLegacyStripeCustomer($stripeToken, $stripeRetailer, (array)$log['billing'], (array)$log['shipping']);
        $amount = $this->_buildChargeConsumerCustomerParams($preorder, $users);

        return $this->Stripe->chargeConsumerCustomer($brandId, $stripeRetailer, $customer, $amount, $lastid);
    }

    /**
     * @param string $stripeToken
     * @param array $stripeRetailer
     * @param array $billing
     * @param array $shipping
     * @return \Stripe\Customer
     * @throws \Stripe\Exception\ApiErrorException
     */
    private function _createLegacyStripeCustomer(string $stripeToken, array $stripeRetailer, array $billing, array $shipping): \Stripe\Customer
    {
        $this->_formatInput($shipping, $billing);

        $isPlatformCustomer = ($stripeRetailer['User']['user_type'] === User::TYPE_RETAILER);
        if (!$isPlatformCustomer) {
            $cus_owner_user_id = (int)$stripeRetailer['StripeUser']['user_id'];
            $cus_owner_stripe_account = (string)$stripeRetailer['StripeUser']['stripe_user_id'];
        } else {
            $cus_owner_user_id = null;
            $cus_owner_stripe_account = null;
        }

        $stripe_cus_id = $this->StripeUser->getCustomerId($billing['email'], $cus_owner_user_id);
        $customer = $this->Stripe->createPlatformCustomer($stripeToken, $billing, $shipping, $stripe_cus_id, $cus_owner_stripe_account);
        $this->StripeUser->saveCustomer($customer, $cus_owner_user_id);

        return $customer;
    }

    private function _buildChargeConsumerCustomerParams(array $preorder, array $users): array
    {
        $log = json_decode($preorder['log'], true);

        $currencyCode = $preorder['currency_code'];
        $shippingAmount = $log['shippingAmount'];
        $taxamt = $log['tax'];
        $totalprice = $preorder['totalpricediscount'] ?? $preorder['totalprice'];

        $amount = [
            'orderNonstock' => $log['log']['type'] ?? 'stock',
            'amt' => $totalprice,
            'currencycode' => $currencyCode,
            'shippingamt' => $shippingAmount,
            'taxamt' => round($taxamt, 2),
            'itemamt' => round($totalprice - ($shippingAmount + $taxamt), 2),
            'revenue_model' => $users['User']['revenue_model'],
            'retailer_default_amount' => $users['User']['retailer_default_amount'],
            'retailer_revenue_maximum' => $users['User']['retailer_revenue_maximum'],
            'sellExclusiveAmt' => Hash::get($log['log'], 'sellExclusive.sellExclusiveTotalwithshipping'),
            'brand_currency_code' => Hash::get($log['log'], 'sellExclusive.currency_code'),
            'sellExclusive_brand_revenue_model' => Hash::get($log['log'], 'sellExclusive.brand_revenue_model'),
            'sellExclusive_brand_direct_default_amount' => Hash::get($log['log'], 'sellExclusive.brand_direct_default_amount'),
            'sellExclusive_brand_revenue_maximum' => Hash::get($log['log'], 'sellExclusive.brand_revenue_maximum'),
        ];
        // A condition in StripeComponent depend on isset($payment['brand_revenue_model'])
        if ($log['shipping_method'] == 'selldirect_selldirect') {
            $amount['brand_revenue_model'] = $users['User']['brand_revenue_model'];
            $amount['brand_direct_default_amount'] = $users['User']['brand_direct_default_amount'];
            $amount['brand_revenue_maximum'] = $users['User']['brand_revenue_maximum'];
        }
        if ($preorder['is_commission_retailer']) {
            // Charge the brand using the retailer revenue model without split payment fees
            $amount['orderNonstock'] = 'stock';
        }

        return $amount;
    }

    public function automatic_discount()
    {
        $user = $this->User->authenticateFromEcommerce($this->request, ['id']);

        $userId = (int)$user['User']['id'];
        $items = (array)json_decode($this->request->data['items'], true);

        $discount_info = $this->Discount->findForEcommerceByAuto($userId, $items);
        $itemDiscounts = $this->Discount->calculateEcommerceItemDiscounts($userId, $discount_info, $items);

        $this->response->body(json_encode(['success' => true, 'data' => $discount_info, 'itemDiscounts' => $itemDiscounts]));
        return $this->response;
    }

    public function verifyDiscountCode()
    {
        $user = $this->User->authenticateFromEcommerce($this->request, array('id'));
        if (empty($user['User']['id'])) {
            $this->response->body(json_encode(['error' => true]));
            return $this->response;
        }
        $userId = (int)$user['User']['id'];

        $code = trim($this->request->data['code']);
        $items = (array)json_decode($this->request->data['items'], true);

        if (
            !$this->request->data('email') ||
            !AppModelValidationsTrait::email($this->request->data['email'], true)
        ) {
            $this->response->body(json_encode(['error' => true, 'message' => __('Please provide a valid email before applying code')]));
            return $this->response;
        }

        $discount_info = $this->Discount->findForEcommerceByCode($userId, $code, $items);
        if (empty($discount_info['id'])) {
            $this->response->body(json_encode(['error' => true, 'message' => __('Invalid discount code')]));
            return $this->response;
        }

        // Save to session in case shipping address is empty on reload
        $discount_info['email'] = $this->request->data['email'];

        if ($discount_info['limit_per_customer'] == 1 && $this->DiscountUsage->hasCustomerUsage($userId, $discount_info['id'], $discount_info['email'])) {
            $this->response->body(json_encode(['error' => true, 'message' => __('Coupon code was used on a previous order')]));
            return $this->response;
        }

        $itemDiscounts = $this->Discount->calculateEcommerceItemDiscounts($userId, $discount_info, $items);

        $this->response->body(json_encode(['success' => true, 'data' => $discount_info, 'itemDiscounts' => $itemDiscounts]));
        return $this->response;
    }

    /**
     * @param $shipping
     * @param $billing
     */
    public function _formatInput(&$shipping, &$billing)
    {
        $email = $shipping['email'];

        $shipping['regionName'] = $this->State->getStateName($shipping['province'], $shipping['country']);
        $shipping['countryName'] = $this->Country->getCountryCode($shipping['country']);
        $nshipping = $shipping;
        $shipping = array_merge($nshipping, $this->OrderPlacer->formatAddress($shipping, $email));

        $billing['regionName'] = $this->State->getStateName($billing['province'], $billing['country']);
        $billing['countryName'] = $this->Country->getCountryCode($billing['country']);
        $nbilling = $billing;
        $billing = array_merge($nbilling, $this->OrderPlacer->formatAddress($billing, $email));
    }

    /**
     * @param array $user
     * @return array
     */
    protected function _formatUserAddress($user)
    {
        $this->formatAddress($user['User']);
        return array(
            'email' => $user['User']['email_address'],
            'First_name' => $user['Contactperson']['firstname'],
            'Last_name' => $user['Contactperson']['lastname'],
            'company' => $user['User']['company_name'],
            'address' => $user['User']['address1'],
            'address2' => !empty($user['User']['address2']) ? $user['User']['address2'] : '',
            'city' => $user['User']['city'],
            'stateCode' => strtoupper($user['State']['state_code']),
            'PostalCode' => $user['User']['zipcode'],
            'countryCode' => strtoupper($user['Country']['country_code']),
            'phone' => $user['Contact']['value'],
        );
    }

    /**
     * @param $user
     * @param $Preorder
     * @param $TransactionID
     * @return string
     */
    public function _paymentSuccess($user, $Preorder, $TransactionID, $sellExclusiveTransactionId = '')
    {
        $sellDirectPreOrderLogs = json_decode($Preorder['Preorder']['log'], true);
        $shippingMethod = (string)$sellDirectPreOrderLogs['shipping_method'];

        if (!empty($sellDirectPreOrderLogs['log']['sellExclusive'])) {
            $this->_buyDirectOrder($Preorder['Preorder'], $user, $sellExclusiveTransactionId);
        }
        if ($shippingMethod === 'selldirect_selldirect') {
            $response = $this->_buyDirectOrder($Preorder['Preorder'], $user, $TransactionID);
        } else {
            $response = $this->_directorder($Preorder['Preorder'], $user['User'], $TransactionID);
        }
        $this->Preorder->markSuccess($Preorder['Preorder']['id']);

        $successResponse = json_decode($response, true);
        $newOrderId = (int)$successResponse['data']['id'];
        // Reproduce the order number used in the ShipearlyWooCommerce success page analytics scripts.
        $orderNo = (string)$successResponse['data']['orderId'];
        if ($shippingMethod !== 'selldirect_selldirect') {
            $orderNo = "#SE{$orderNo}";
        }
        $orderNo = str_replace('#', '', $orderNo);

        $this->Order->trackFbpixelPurchaseEvent($newOrderId, $orderNo);

        return $response;
    }

    /**
     * @param $preorder
     * @param $user
     * @param $transactionID
     * @return string
     */
    function _buyDirectOrder($preorder, $user, $transactionID)
    {
        $logs = json_decode($preorder['log'], true);

        if (!empty($logs['log']['sellExclusive'])) {
            $logs['log'] = $logs['log']['sellExclusive'];
            $logs['log']['totalwithshipping'] = $logs['log']['sellExclusiveTotalwithshipping'];
            $logs['log']['shippingAmount'] = $logs['log']['sellExclusiveShippingAmount'];
            $logs['shippingAmount'] = $logs['log']['shippingAmount'];
            $logs['stripe_retailer_id'] = $logs['sell_exclusive_stripe_retailer_id'];
            $logs['stripe_account'] = $logs['sell_exclusive_stripe_account'];
        }
        $brand_id = $logs['log']['id'];

        $distributorId = null;
        $stripeRetailerId = (int)($logs['stripe_retailer_id'] ?? 0);
        if ($this->User->isUserType($stripeRetailerId, User::TYPE_SALES_REP)) {
            $distributorId = $stripeRetailerId;
        }

        $response = $this->Woocommerce->createOrderFromPreorderLogs($user, $logs);
        if ($response) {
            $sourceId = $response->id;
            $orderNumber = $response->number;

            $customerShipping = $logs['shipping'];
            $customerBilling = $logs['billing'];
            $this->_formatInput($customerShipping, $customerBilling);

            $this->Preorder->markSuccess($preorder['id'], ['order_id' => $orderNumber]);
            $totalPrice =  $preorder['totalprice'];
            $shipearlyFees = $this->OrderLogic->CalculateFees($preorder['totalpricediscount'], 'brand', $user['User']['brand_revenue_model'], $user['User']['brand_direct_default_amount'], $user['User']['brand_revenue_maximum']);
            $orderinfo = [
                'user_id' => $brand_id,
                'distributor_id' => $distributorId,
                'customerID' => $this->_addCustomerById($logs, $user['User'], $customerShipping),
                'source_id' => $sourceId,
                'orderNO' => $orderNumber,
                'preOrderId' => $preorder['id'],
                'payment_method' => OrderPaymentMethod::STRIPE,
                'payment_method_subtype' => $logs['Order']['payment_method_subtype'],
                'payment_status' => ($user['User']['sell_direct_authorize']) ? OrderPaymentStatus::AUTHORIZED : OrderPaymentStatus::PAID,
                'transactionID' => $transactionID,
                'stripe_account' => $logs['stripe_account'],
                'total_price' => $totalPrice,
                'shipearlyFees' => $this->Currency->formatAsDecimal($shipearlyFees, $preorder['currency_code']),
                'retailerAmount' => $this->Currency->formatAsDecimal($totalPrice - $shipearlyFees, $preorder['currency_code']),
            ];
            $orderinfo = array_merge($orderinfo, $this->_extractOrderDiscountFields((array)($logs['discountInfo'] ?? []), $logs['log'], $preorder['totalpricediscount']));

            $this->Order->create();
            if (!$this->Order->save($orderinfo)) {
                throw new InternalErrorException(json_encode(['errors' => $this->Order->validationErrors, 'data' => $this->Order->data]));
            }
            $newOrderId = (int)$this->Order->getLastInsertID();
            if (!$this->OrderAddress->saveBillingFromEcommercePreorder($newOrderId, $customerBilling)) {
                triggerWarning(json_encode(['message' => 'Failed to save billing address', 'errors' => $this->OrderAddress->validationErrors, 'data' => $this->OrderAddress->data]));
            }
            $this->OrderLogic->updateChargeForNewOrder($newOrderId);
            try {
                $this->NotificationLogic->handleSellDirectNotifications($newOrderId);
            } catch (Exception $e) {
                CakeLog::error($e);
            }

            $this->Session->write('Woocommerce.order.orderID', $orderNumber);
            $this->Session->write('Woocommerce.shippingMethod', $logs['shipping_method']);
            $this->Session->write('Woocommerce.log', $logs['log']);

            return json_encode(['status' => 'success', 'data' => ['id' => $sourceId, 'orderId' => '#' . $orderNumber, 'code' => '']]);
        }
    }

    /**
     * @param $preorder
     * @param $user
     * @param $transactionID
     * @return string
     */
    function _directorder($preorder, $user, $transactionID)
    {
        $logs = json_decode($preorder['log'], true);

        $shipping = $logs['shipping'];
        $billing = $logs['billing'];
        $this->_formatInput($shipping, $billing);

        $products = $logs['log'];
        $shippingMethod = $logs['shipping_method'];

        $typeToShippingMethodSubtype = [
            'instore' => '',
            'local_install' => 'localinstall',
            'local_delivery' => 'localdelivery',
            'shipFromStore' => '',
            'sellDirect' => '',
        ];
        $shipping_method_subtype = $typeToShippingMethodSubtype[$logs['type']];

        $retailer_id = $products['retailer_id'];

        $cusId = $this->_addCustomerById($logs, $user, (array) $shipping);

        $c_retailer = $this->User->findForEcommerceNotifications($retailer_id);

        $currencyConversion = 1;
        if ($preorder['currency_code'] != $c_retailer['User']['currency_code']) {
            $currencyConversion = $this->currencyConversion(1, $preorder['currency_code'], $c_retailer['User']['currency_code']);
        }

        $type = (string)Hash::get(array(
            'shipearly_shipearly' => Order::TYPE_IN_STORE_PICKUP,
            'shipfromstore_shipfromstore' => Order::TYPE_SHIP_FROM_STORE,
        ), $shippingMethod);

        $code = ($shippingMethod === 'shipearly_shipearly') ? $this->OrderLogic->generateCode() : '';

        $subType = $products['type'];

        $is_nonstock = ($subType === OrderType::SUB_TYPE_NONSTOCK);

        if ($shippingMethod === 'shipearly_shipearly') {
            $orderStatus = ($is_nonstock)
                ? Order::STATUS_NEED_TO_CONFIRM
                : Order::STATUS_NOT_PICKED_UP;
        } elseif ($shippingMethod === 'shipfromstore_shipfromstore') {
            $orderStatus = ($is_nonstock)
                ? Order::STATUS_NEED_TO_CONFIRM
                : Order::STATUS_OPEN;
        } else {
            CakeLog::error('Unhandled retailer shipping method ' . json_encode($shippingMethod), ['woocommerce']);
            $orderStatus = null;
        }

        $totalPrice = $preorder['totalprice'];

        $orderinfo = array(
            'user_id' => $user['id'],
            'order_type' => $type,
            'subType' => $subType,
            'code' => $code,
            'secretcode' => $code,
            'customerID' => $cusId,
            'preOrderId' => $preorder['id'],
            'retailer_id' => $retailer_id,
            'store_associate_id' => $preorder['store_associate_id'],
            'is_commission_retailer' => $preorder['is_commission_retailer'],
            'order_status' => $orderStatus,
            'payment_method' => OrderPaymentMethod::STRIPE,
            'payment_method_subtype' => $logs['Order']['payment_method_subtype'],
            'payment_status' => OrderPaymentStatus::AUTHORIZED,
            'tax_included' => $products['tax_included'],
            'shipping_amount' => ($shippingMethod === 'shipearly_shipearly' && !$is_nonstock) ? 0 : $logs['shippingAmount'],
            'created_at' => $preorder['created'],
            'total_price' => $totalPrice,
            'totalPriceConversion' => $currencyConversion * $totalPrice,
            'currency_code' => $preorder['currency_code'],
            'total_tax' => isset($products['taxamtdiscount']) ? $products['taxamtdiscount'] : $products['taxamt'],
            'transactionID' => $transactionID,
            'stripe_account' => $logs['stripe_account'],
            'shipearlyFees' => $this->Currency->formatAsDecimal(
                $this->OrderLogic->CalculateFees($preorder['totalpricediscount'], 'retailer', $user['revenue_model'], $user['retailer_default_amount'], $user['retailer_revenue_maximum']),
                $preorder['currency_code']
            ),
            'card_type' => $preorder['card_type'],
            'last_four_digit' => $preorder['last_four_digit'],
            'fraud_check_cvc' => $preorder['fraud_check_cvc'],
            'fraud_check_address' => $preorder['fraud_check_address'],
            'fraud_check_postal_code' => $preorder['fraud_check_postal_code'],
        );

        if ($shipping_method_subtype === 'localinstall') {
            $orderinfo['is_install'] = true;
            $orderinfo['shipping_amount'] = $products['shippingAmount_localinstall'];
            $orderinfo['total_tax'] = isset($products['taxamtdiscount_localinstall'])
                ? $products['taxamtdiscount_localinstall']
                : $products['taxamt_localinstall'];
        } elseif ($shipping_method_subtype === 'localdelivery') {
            $orderinfo['order_type'] = Order::TYPE_LOCAL_DELIVERY;
            if (!$is_nonstock) {
                $orderinfo['order_status'] = Order::STATUS_READY_FOR_DELIVERY;
            }
            $orderinfo['shipping_amount'] = $products['shippingAmount_localdelivery'];
            $orderinfo['total_tax'] = isset($products['taxamtdiscount_localdelivery'])
                ? $products['taxamtdiscount_localdelivery']
                : $products['taxamt_localdelivery'];
        }

        $orderinfo['retailerAmount'] = $this->Currency->formatAsDecimal($orderinfo['total_price'] - $orderinfo['shipearlyFees'], $preorder['currency_code']);

        if ($orderinfo['is_commission_retailer']) {
            $orderinfo['order_status'] = Order::STATUS_PENDING;
            $orderinfo['payment_status'] = ($user['sell_direct_authorize']) ? OrderPaymentStatus::AUTHORIZED : OrderPaymentStatus::PAID;

            $stripeRetailerId = (int)($logs['stripe_retailer_id'] ?? 0);
            if ($this->User->isUserType($stripeRetailerId, User::TYPE_SALES_REP)) {
                // Note that `$orderinfo += $salesRepFields['Order'];` occurs later if this is not set now
                $orderinfo['distributor_id'] = $stripeRetailerId;
            }
        }

        $orderinfo = array_merge($orderinfo, array(
            'customerEmail' => $shipping['email'],
            'billing_firstname' => $shipping['First_name'],
            'billing_lastname' => $shipping['Last_name'],
            'shipping_company_name' => $shipping['company'],
            'shipping_address1' => $shipping['address'],
            'shipping_address2' => $shipping['address2'],
            'shipping_city' => $shipping['city'],
            'shipping_state' => $shipping['regionName'],
            'shipping_country' => $shipping['countryName'],
            'shipping_zipcode' => $shipping['PostalCode'],
            'shipping_telephone' => $shipping['phone'],
            'shipping_statecode' => $shipping['province'],
            'shipping_countrycode' => $shipping['country'],
        ));
        if (!empty($orderinfo['shipping_zipcode'])) {
            /** update latitude & longitude **/
            $geopoints = $this->_getLnt(
                $orderinfo['shipping_address1'],
                $orderinfo['shipping_city'],
                $orderinfo['shipping_zipcode'],
                $orderinfo['shipping_state'],
                $orderinfo['shipping_country']
            );
            $orderinfo['latitude'] = $geopoints['lat'];
            $orderinfo['longitude'] = $geopoints['lng'];
        }

        $orderinfo = array_merge($orderinfo, $this->_extractOrderDiscountFields((array)($logs['discountInfo'] ?? []), $products, $preorder['totalpricediscount']));

        if (!$this->Order->createFromEcommercePreorderWithRetailer($orderinfo, $products['product'], $billing, $currencyConversion)) {
            throw new InternalErrorException(json_encode(['errors' => $this->Order->validationErrors, 'data' => $this->Order->data]));
        }

        $inc_oid = $this->Order->getLastInsertID();
        $newOrder = $this->Order->get($inc_oid);

        $order = array_merge($newOrder['Order'], array(
            'customer_email' => $shipping['email'],
            'customer_firstname' => $shipping['First_name'],
            'customer_lastname' => $shipping['Last_name'],
            'shipping_telephone' => $shipping['phone'],
            'order_type' => $type,
            'shipping_address' => array(
                'street1' => $shipping['address'],
                'street2' => $shipping['address2'],
                'city' => $shipping['city'],
                'state' => $shipping['regionName'],
                'country' => $shipping['countryName'],
                'zipcode' => $shipping['PostalCode'],
            ),
        ));

        if ($orderinfo['is_commission_retailer']) {
            $response = $this->_commissionRetailerOrder($user, $logs, $c_retailer);
            if ($response) {
                if (!$this->Order->save(['id' => $inc_oid, 'source_id' => $response->id, 'orderNO' => $response->number])) {
                    CakeLog::warning(new InternalErrorException(json_encode(['errors' => $this->Order->validationErrors, 'data' => $this->Order->data])));
                }
            }
        } elseif ($preorder['is_ship_to_store_only']) {
            $this->OrderLogic->dealerOrderEvent($inc_oid);
            $this->NotificationLogic->handleShipToStoreOnlyNotification($inc_oid, $order, $shippingMethod, $shipping_method_subtype);
        } elseif ($is_nonstock) {
            $this->WarehouseProductReservation->reserveLineItemSet($inc_oid, $this->OrderProduct->findAllForInventoryReservation($inc_oid));
        }

        if (!$preorder['is_ship_to_store_only']) {
            $this->NotificationLogic->handleNotification($shippingMethod, $inc_oid, $c_retailer, $user, $order, $shipping_method_subtype);
        }

        $this->OrderLogic->updateChargeForNewOrder((int)$inc_oid);

        if ($shippingMethod == 'shipfromstore_shipfromstore') {
            $this->Avatax->setTotalTaxFromEcommerce(
                $preorder['currency_code'], $retailer_id, $shipping, $newOrder['Order']['orderID'], $logs, $products['product']
            );
        }

        $this->OrderPlacer->createPosOrder($retailer_id, $newOrder['Order'], $products, $shipping, $billing, $shippingMethod);

        return json_encode(array('status' => 'success', 'data' => array('id' => $inc_oid, 'orderId' => $this->formatOrderId($inc_oid, false), 'code' => $code)));
    }

    /**
     * @param array $logs
     * @param array $user
     * @param array $shipping
     * @return int|null
     */
    public function _addCustomerById($logs, $user, $shipping): ?int
    {
        $userId = (int)$user['id'];
        $shipping = (array)$shipping;

        $cusId = null;
        if (!empty($logs['customerID'])) {
            try {
                $result = $this->Woocommerce->getCustomer($user, (int)$logs['customerID']);
                if (!empty($result->customer->id)) {
                    $cusId = $this->Customer->syncWoocommerceCustomer($userId, (array)$result->customer);
                }
            } catch (Exception $e) {
                CakeLog::warning($e);
            }
        }
        if (empty($cusId)) {
            $cusId = $this->Customer->addEcommerceCustomerByEmail($userId, $shipping);
        }

        if (!empty($cusId)) {
            $extraData = [
                'id' => $cusId,
                'accepts_marketing' => (bool)$logs['Customer']['accepts_marketing'],
            ];
            // WC plugin is not yet trusted to always send this value
            if (isset($logs['preferred_language'])) {
                $extraData['preferred_language'] = (string)$logs['preferred_language'];
            }
            $this->Customer->save($extraData);
        }

        return $cusId;
    }

    /**
     * @param array $user
     * @param array $logs
     * @param array $retailer
     * @return mixed orderNo
     */
    protected function _commissionRetailerOrder($user, $logs, $retailer)
    {
        $logs['alt_shipping'] = $this->_formatUserAddress($retailer);
        return $this->Woocommerce->createOrderFromPreorderLogs($user, $logs);
    }

    /**
     * @since 2.18.0
     * @deprecated Since 2.22. Sift Science is no longer supported.
     * @see WsController::getSiftKey
     */
    public function getSiftKey()
    {
        deprecationWarning(sprintf(
            '2.22 %s is deprecated. Sift Science is no longer supported. %s',
            $this->request->here(),
            json_encode(['plugin_version' => $this->_getPluginVersion(), 'app_username' => $this->request->data('app_username')])
        ), 0);

        return $this->response;
    }

    /**
     * @since 2.18.0
     * @see WsController::getGoogleApiKey
     */
    public function getGoogleApiKey()
    {
        try {
            $this->User->authenticateFromEcommerce($this->request, ['User.id']);
            $this->response->body(GOOGLE_GEOCODING_API_KEY);
        } catch (HttpException $e) {
            CakeLog::error($e);
            $this->response->statusCode($e->getCode());
        }

        return $this->response;
    }

    /**
     * @since 2.18.0
     * @see WsController::brandPolicies
     */
    public function brandPolicies()
    {
        $this->response->body($this->requestAction('/ws/brandPolicies', ['data' => $this->request->data]));

        return $this->response;
    }

    /**
     * @since 2.18.0
     * @see WsController::paymentPageCustomContent
     */
    public function paymentPageCustomContent()
    {
        $this->response->body($this->requestAction('/ws/paymentPageCustomContent', ['data' => $this->request->data]));

        return $this->response;
    }

    /**
     * @since 2.18.0
     * @see WsController::getRetailerIds
     */
    public function getRetailerIds()
    {
        $this->response->body($this->requestAction('/ws/getRetailerIds', ['data' => $this->request->data]));

        return $this->response;
    }

    /**
     * @since 2.18.0
     * @see WsController::checkProduct
     */
    public function checkProduct()
    {
        $this->response->body($this->requestAction('/ws/checkProduct', ['data' => $this->request->data]));

        return $this->response;
    }

    /**
     * @since 2.18.0
     * @see WsController::checkSellExclusiveProduct
     */
    public function checkSellExclusiveProduct()
    {
        $this->response->body($this->requestAction('/ws/checkSellExclusiveProduct', ['data' => $this->request->data]));

        return $this->response;
    }

    /**
     * @since 2.18.0
     * @see WsController::update
     */
    public function update()
    {
        $this->response->body($this->requestAction('/ws/update', ['data' => $this->request->data]));

        return $this->response;
    }

    /**
     * @return CakeResponse|null
     * @since Version1.32
     */
    public function validate_address()
    {
        $this->response->body(json_encode($this->AddressValidator->validate_address($this->request->data)));
        return $this->response;
    }

    private function _processAddressFormFields(array $addressForm): array
    {
        $addressForm = array_map('trim', $addressForm);

        $addressForm['PostalCode'] = preg_replace('/^([ABCEGHJKLMNPRSTVXY][0-9][ABCEGHJKLMNPRSTVWXYZ])([0-9][ABCEGHJKLMNPRSTVWXYZ][0-9])$/', '$1 $2', strtoupper($addressForm['PostalCode']));

        return $this->_fillMissingAddressStateFields($addressForm);
    }

    private function _fillMissingAddressStateFields(array $addressForm): array
    {
        $fieldNames = ['regionName', 'regionCode', 'countryName', 'countryCode'];
        $fieldNames = array_combine($fieldNames, $fieldNames);

        $missing = array_diff_key($fieldNames, array_filter($addressForm));
        if (!$missing) {
            return $addressForm;
        }

        $record = $this->State->findWithCountryName($addressForm['province'])['State'];
        if ($record['country_id'] != $addressForm['country']) {
            $record['country_code'] = null;
            $record['country_name'] = null;
        }

        return array_merge($addressForm, [
            'regionName' => $record['state_name'],
            'regionCode' => $record['state_code'],
            'countryName' => $record['country_name'],
            'countryCode' => $record['country_code'],
        ]);
    }

    /**
     * @param string $type
     * @param array $retailerInfo
     * @return float|string
     */
    private function _extractShippingAmount(string $type, array $retailerInfo)
    {
        $shippingAmount = $retailerInfo['shippingAmount'];
        if ($type === 'local_install') {
            $shippingAmount = $retailerInfo['shippingAmount_localinstall'];
        } elseif ($type === 'local_delivery') {
            $shippingAmount = $retailerInfo['shippingAmount_localdelivery'];
        }

        return $shippingAmount;
    }

    /**
     * @param string $type
     * @param array $retailerInfo
     * @return float|string
     */
    private function _extractTax(string $type, array $retailerInfo)
    {
        $tax = $retailerInfo['taxamt'];
        if ($type === 'local_install') {
            $tax = $retailerInfo['taxamt_localinstall'];
        } elseif ($type === 'local_delivery') {
            $tax = $retailerInfo['taxamt_localdelivery'];
        }

        return $tax;
    }

    /**
     * @param string $type
     * @param array $retailerInfo
     * @return float|string
     */
    private function _extractRetailerTotalPrice(string $type, array $retailerInfo)
    {
        if ($type === 'instore') {
            $totalPrice = $retailerInfo['totalamount'];
        } elseif ($type === 'local_install') {
            $totalPrice = $retailerInfo['totalamount_localinstall'];
        } elseif ($type === 'local_delivery') {
            $totalPrice = $retailerInfo['totalamount_localdelivery'];
        } else {
            $totalPrice = $retailerInfo['totalwithshipping'];
        }

        return $totalPrice;
    }

    /**
     * @param string $type
     * @param array $retailerInfo
     * @return float|string
     */
    private function _extractRetailerTotalPriceDiscounted(string $type, array $retailerInfo)
    {
        if ($type === 'instore') {
            $totalPriceDiscounted = $retailerInfo['totalamountdiscount'] ?? $retailerInfo['totalamount'];
        } elseif ($type === 'local_install') {
            $totalPriceDiscounted = $retailerInfo['totalamountdiscount_localinstall'] ?? $retailerInfo['totalamount_localinstall'];
        } elseif ($type === 'local_delivery') {
            $totalPriceDiscounted = $retailerInfo['totalamountdiscount_localdelivery'] ?? $retailerInfo['totalamount_localdelivery'];
        } else {
            $totalPriceDiscounted = $retailerInfo['totalwithshippingdiscount'] ?? $retailerInfo['totalwithshipping'];
        }

        return $totalPriceDiscounted;
    }

    private function _extractOrderDiscountFields(array $discountInfo, array $products, float $totalPriceDiscounted): array
    {
        if (empty($discountInfo['code'])) {
            return [];
        }

        // Trigger Notice if preorder is missing expected fields
        $productDiscount = (float)($products['totalproductamount'] - $products['totalproductamountdiscount']);
        //TODO SHIP-2666 WooCommerce Free Shipping Discounts
        $shippingDiscount = 0.00;

        $totalDiscount = $productDiscount + $shippingDiscount;
        $totalPrice = $totalPriceDiscounted + $totalDiscount;

        return [
            'total_price' => $totalPrice,
            'total_discount' => $totalDiscount,
            'shipping_discount' => $shippingDiscount,
            'discount_code' => $discountInfo['code'],
        ];
    }

    private function _getMissingArrayKeys(array $array, array $requiredKeys): array
    {
        return array_filter($requiredKeys, function($key) use ($array) {
            return !Hash::get($array, $key);
        });
    }
}
