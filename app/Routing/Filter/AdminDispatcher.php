<?php
App::uses('DispatcherFilter', 'Routing');

/**
 * Class AdminDispatcher.
 */
class AdminDispatcher extends DispatcherFilter
{
    /**
     * Default priority for all methods in this filter.
     *
     * This filter should run after the request gets parsed by Router.
     *
     * @var int
     */
    public $priority = 11;

    public function beforeDispatch(CakeEvent $event)
    {
        parent::beforeDispatch($event);
        /** @var CakeRequest $request */
        $request = $event->data['request'];
        /** @var CakeRequest $request */
        $response = $event->data['response'];

        if ($request->param('prefix') === 'admin') {
            if (DEFAULT_ENGINE === 'Memcached') {
                $sessionConfig = Configure::read('Session');

                $sessionConfig['cookie'] = APP_NAME . '_admin_session';
                $sessionConfig['ini']['session.cookie_path'] = '/admin/';

                Configure::write('Session', $sessionConfig);
            }
            $response->disableCache();
        }
    }
}
