<?php
App::uses('Router', 'Routing');
App::uses('CakeRoute', 'Routing/Route');

/**
 * Subdomain Route.
 *
 * Route class that supports a 'subdomain' url parameter.
 *
 * @package app.Routing.Route
 */
class SubdomainRoute extends CakeRoute
{
    public function parse($url)
    {
        $params = parent::parse($url);
        if (!$params) {
            return false;
        }

        $host = static::getHost();
        $subdomain = static::parseSubdomain($host);

        $params['subdomain'] = $subdomain;

        return $params;
    }

    public function match($url)
    {
        $subdomain = $url['subdomain'] ?? null;
        unset($url['subdomain']);

        $path = parent::match($url);

        if ($path && $subdomain) {
            $scheme = static::getScheme();
            $host = static::getHost();
            $domain = static::parseDomain($host);

            $path = "{$scheme}://{$subdomain}.{$domain}" . $path;
        }

        return $path;
    }

    public static function getScheme(): string
    {
        $scheme = '';
        if (isset($_SERVER['HTTPS']) || env('SCRIPT_URI')) {
            $scheme = env('HTTPS') ? 'https' : 'http';
        }

        // Fallback for `ControllerTestCase::_testAction()` with subdomain.
        if (!$scheme && strpos(($_SERVER['REQUEST_URI'] ?? ''), '://') !== false) {
            $scheme = (string)parse_url($_SERVER['REQUEST_URI'], PHP_URL_SCHEME);
        }
        // Fallback for other CLI requests and tests.
        if (!$scheme) {
            $scheme = (string)parse_url(Router::fullBaseUrl(), PHP_URL_SCHEME);
        }

        return $scheme;
    }

    /**
     * Get the host that the request was handled on.
     *
     * @return string
     * @see CakeRequest::host()
     */
    public static function getHost(): string
    {
        $host = (string)env('HTTP_HOST');

        // Fallback for `ControllerTestCase::_testAction()` with subdomain.
        if (!$host && strpos(($_SERVER['REQUEST_URI'] ?? ''), '://') !== false) {
            $host = (string)parse_url($_SERVER['REQUEST_URI'], PHP_URL_HOST);
        }
        // Fallback for other CLI requests and tests.
        if (!$host) {
            $host = (string)parse_url(Router::fullBaseUrl(), PHP_URL_HOST);
        }

        return $host;
    }

    /**
     * Get the domain name and include $tldLength segments of the tld.
     *
     * @param string $host
     * @param int $tldLength Number of segments your tld contains.
     *   For example: `example.com` contains 1 tld. While `example.co.uk` contains 2.
     * @return string Domain name without subdomains.
     * @see CakeRequest::domain()
     */
    public static function parseDomain(string $host, int $tldLength = 1): string
    {
        $segments = explode('.', $host);
        $domainParts = array_slice($segments, -1 * ($tldLength + 1));
        return implode('.', $domainParts);
    }

    /**
     * Get the subdomain for a host.
     *
     * @param string $host
     * @param int $tldLength Number of segments your tld contains.
     *   For example: `example.com` contains 1 tld. While `example.co.uk` contains 2.
     * @return string Combined subdomains without domain name.
     * @see CakeRequest::subdomains()
     */
    public static function parseSubdomain(string $host, int $tldLength = 1): string
    {
        $segments = explode('.', $host);
        $subdomains = array_slice($segments, 0, -1 * ($tldLength + 1));
        return implode('.', $subdomains);
    }
}
