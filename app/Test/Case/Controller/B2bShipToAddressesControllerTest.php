<?php
App::uses('IntegrationTestCase', 'TestSuite');
App::uses('B2bShipToAddressesController', 'Controller');

/**
 * B2bShipToAddressesController Test Case.
 *
 * Run with the following console command:
 *
 * ./app/Console/cake test app Controller/B2bShipToAddressesController --stderr
 *
 * @property B2bShipToAddressesController|PHPUnit_Framework_MockObject_MockObject $controller
 */
class B2bShipToAddressesControllerTest extends IntegrationTestCase
{
    const USER_ID_BRAND = '8';
    const USER_ID_RETAILER = '7';

    const COUNTRY_ID_CA = '39';
    const STATE_ID_ON_CA = '611';

    public $fixtures = [
        'app.b2b_ship_to_address',
        'app.brand_staff',
        'app.brand_staff_permission',
        'app.category',
        'app.configuration',
        'app.contact',
        'app.contactpersons',
        'app.country',
        'app.manufacturer_retailer',
        'app.manufacturer_sales_rep',
        'app.staff_permission',
        'app.state',
        'app.user',
        'app.user_category',
        'app.user_subdomain',
    ];

    public function setUp()
    {
        parent::setUp();
        $this->generate('B2bShipToAddresses', [
            'methods' => [
                '_headerNotifications',
                '_checkCron',
            ],
        ]);
        $this->controller->Auth->unauthorizedRedirect = false;
        $this->controller->syncUserSession(static::USER_ID_BRAND);
    }

    public function testEditForbidden()
    {
        $this->expectException(ForbiddenException::class, null);

        $this->controller->syncUserSession(static::USER_ID_RETAILER);

        $this->get(['controller' => 'b2b_ship_to_addresses', 'action' => 'edit', static::USER_ID_RETAILER]);
    }

    public function testEditNotFound()
    {
        $this->expectException(NotFoundException::class, null);

        $this->get(['controller' => 'b2b_ship_to_addresses', 'action' => 'edit']);
    }

    public function testEditGetAccountAddress()
    {
        $manufacturerRetailerId = $this->controller->ManufacturerRetailer->fieldByConditions('id', [
            'ManufacturerRetailer.user_id' => static::USER_ID_BRAND,
            'ManufacturerRetailer.retailer_id' => static::USER_ID_RETAILER,
        ]);
        $this->controller->B2bShipToAddress->deleteAllJoinless(['B2bShipToAddress.manufacturer_retailer_id' => $manufacturerRetailerId], false);

        $this->get(['controller' => 'b2b_ship_to_addresses', 'action' => 'edit', static::USER_ID_RETAILER]);
        $this->assertResponseOk();

        $expected = [
            'B2bShipToAddress' => [
                'id' => null,
                'city' => 'Thunder Bay',
                'country_id' => static::COUNTRY_ID_CA,
                'state_id' => static::STATE_ID_ON_CA,
                'zipcode' => 'P7B 5E1',
                'address1' => '955 Oliver Rd',
                'address2' => '',
                'telephone' => '************',
            ],
        ];
        $this->assertEquals($expected, $this->controller->request->data);

        $expected = [
            'countryList' => $this->controller->Country->getCountryList(),
            'stateList' => $this->controller->State->getStateList(static::COUNTRY_ID_CA),
        ];
        $this->assertArraySubset($expected, $this->controller->viewVars);
    }

    public function testEditGetShipToAddress()
    {
        $manufacturerRetailerId = $this->controller->ManufacturerRetailer->fieldByConditions('id', [
            'ManufacturerRetailer.user_id' => static::USER_ID_BRAND,
            'ManufacturerRetailer.retailer_id' => static::USER_ID_RETAILER,
        ]);
        $this->seedB2bShipToAddress($manufacturerRetailerId);
        $id = $this->controller->B2bShipToAddress->id;

        $this->get(['controller' => 'b2b_ship_to_addresses', 'action' => 'edit', static::USER_ID_RETAILER]);
        $this->assertResponseOk();

        $expected = [
            'B2bShipToAddress' => [
                'id' => $id,
                'address1' => '2400 Nipigon Road',
                'address2' => 'Box 398',
                'city' => 'Thunder Bay',
                'country_id' => static::COUNTRY_ID_CA,
                'state_id' => static::STATE_ID_ON_CA,
                'zipcode' => 'P7C 4W1',
                'telephone' => '(*************',
            ],
        ];
        $this->assertEquals($expected, $this->controller->request->data);

        $expected = [
            'countryList' => $this->controller->Country->getCountryList(),
            'stateList' => $this->controller->State->getStateList('39'),
        ];
        $this->assertArraySubset($expected, $this->controller->viewVars);
    }

    public function testEditPost()
    {
        $manufacturerRetailerId = $this->controller->ManufacturerRetailer->fieldByConditions('id', [
            'ManufacturerRetailer.user_id' => static::USER_ID_BRAND,
            'ManufacturerRetailer.retailer_id' => static::USER_ID_RETAILER,
        ]);
        $this->controller->B2bShipToAddress->deleteAllJoinless(['B2bShipToAddress.manufacturer_retailer_id' => $manufacturerRetailerId], false);

        $data = [
            'B2bShipToAddress' => [
                'address1' => '2400 Nipigon Road',
                'address2' => 'Box 398',
                'city' => 'Thunder Bay',
                'country_id' => static::COUNTRY_ID_CA,
                'state_id' => static::STATE_ID_ON_CA,
                'zipcode' => 'P7C 4W1',
                'telephone' => '(*************',
            ],
        ];
        $this->post(['controller' => 'b2b_ship_to_addresses', 'action' => 'edit', static::USER_ID_RETAILER], $data);
        $this->assertRedirect();
        $id = $this->controller->B2bShipToAddress->id;

        $expected = [
            'B2bShipToAddress' => [
                'id' => $id,
                'manufacturer_retailer_id' => $manufacturerRetailerId,
            ] + $data['B2bShipToAddress'],
        ];
        $actual = $this->controller->B2bShipToAddress->findByManufacturerRetailerId($manufacturerRetailerId, array_keys($expected['B2bShipToAddress']), null, -1);
        $this->assertEquals($expected, $actual);
    }

    public function testEditPut()
    {
        $manufacturerRetailerId = $this->controller->ManufacturerRetailer->fieldByConditions('id', [
            'ManufacturerRetailer.user_id' => static::USER_ID_BRAND,
            'ManufacturerRetailer.retailer_id' => static::USER_ID_RETAILER,
        ]);
        $this->seedB2bShipToAddress($manufacturerRetailerId);
        $id = $this->controller->B2bShipToAddress->id;
        $this->controller->B2bShipToAddress->clear();

        $data = [
            'B2bShipToAddress' => [
                'address1' => '955 Oliver Rd',
                'address2' => '',
                'city' => 'Thunder Bay',
                'country_id' => static::COUNTRY_ID_CA,
                'state_id' => static::STATE_ID_ON_CA,
                'zipcode' => 'P7B 5E1',
                'telephone' => '************',
            ],
        ];
        $this->put(['controller' => 'b2b_ship_to_addresses', 'action' => 'edit', static::USER_ID_RETAILER], $data);
        $this->assertRedirect();

        $this->assertEquals($id, $this->controller->B2bShipToAddress->id);
        $expected = [
            'B2bShipToAddress' => [
                'id' => $id,
                'manufacturer_retailer_id' => $manufacturerRetailerId,
            ] + $data['B2bShipToAddress'],
        ];
        $actual = $this->controller->B2bShipToAddress->findByManufacturerRetailerId($manufacturerRetailerId, array_keys($expected['B2bShipToAddress']), null, -1);
        $this->assertEquals($expected, $actual);
    }

    private function seedB2bShipToAddress(int $manufacturerRetailerId): array
    {
        $data = [
            'B2bShipToAddress' => [
                'id' => $this->controller->B2bShipToAddress->fieldByConditions('id', ['B2bShipToAddress.manufacturer_retailer_id' => $manufacturerRetailerId]),
                'manufacturer_retailer_id' => $manufacturerRetailerId,
                'address1' => '2400 Nipigon Road',
                'address2' => 'Box 398',
                'city' => 'Thunder Bay',
                'country_id' => static::COUNTRY_ID_CA,
                'state_id' => static::STATE_ID_ON_CA,
                'zipcode' => 'P7C 4W1',
                'telephone' => '(*************',
            ],
        ];

        $success = $this->controller->B2bShipToAddress->save($data);
        $this->assertTrue((bool)$success, json_encode(['errors' => $this->controller->B2bShipToAddress->validationErrors]));

        $data['B2bShipToAddress']['id'] = $this->controller->B2bShipToAddress->id;

        return $data;
    }
}
