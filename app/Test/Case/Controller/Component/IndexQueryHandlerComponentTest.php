<?php
App::uses('AppTestCase', 'TestSuite');
App::uses('ComponentCollection', 'Controller');
App::uses('IndexQueryHandlerComponent', 'Controller/Component');

/**
 * IndexQueryHandlerComponent Test Case.
 *
 * Run with the following console command:
 *
 * ./app/Console/cake test app Controller/Component/IndexQueryHandlerComponent
 *
 * @property IndexQueryHandlerComponent $IndexQueryHandler
 */
class IndexQueryHandlerComponentTest extends AppTestCase
{
    const DEFAULT_QUERY = [
        'sort' => 'id',
        'direction' => 'DESC',
        'limit' => 50,
        'status' => '',
    ];

    public function setUp()
    {
        parent::setUp();
        $this->IndexQueryHandler = new IndexQueryHandlerComponent(new ComponentCollection());
        $this->IndexQueryHandler->defaultQuery = static::DEFAULT_QUERY;
    }

    public function tearDown()
    {
        unset($this->IndexQueryHandler);
        parent::tearDown();
    }

    /**
     * @dataProvider providerGetDefaultQuery
     * @param array $defaultQuery
     * @param array $expectedDiff
     */
    public function testGetDefaultQuery(array $defaultQuery, array $expectedDiff)
    {
        $this->IndexQueryHandler->defaultQuery = $defaultQuery;
        $actual = $this->IndexQueryHandler->getDefaultQuery();

        $expected = array_merge(IndexQueryHandlerComponent::DEFAULT_QUERY, $expectedDiff);
        $this->assertEquals($expected, $actual);
    }

    public function providerGetDefaultQuery(): array
    {
        return $this->providerExtractAllParams();
    }

    /**
     * @dataProvider providerExtractAllParams
     * @param array $query
     * @param array $expectedDiff
     */
    public function testExtractAllParams(array $query, array $expectedDiff)
    {
        $actual = $this->IndexQueryHandler->extractAllParams($query);

        $expected = array_merge(IndexQueryHandlerComponent::DEFAULT_QUERY, static::DEFAULT_QUERY, $expectedDiff);
        $this->assertEquals($expected, $actual);
    }

    public function providerExtractAllParams(): array
    {
        return [
            'returns_default_for_empty_query' => [
                'query' => [],
                'expectedDiff' => [],
            ],
            'merges_override' => [
                'query' => [
                    'sort' => 'created_at',
                    'direction' => 'DESC',
                    'limit' => '100',
                    'status' => 'Activated',
                ],
                'expectedDiff' => [
                    'sort' => 'created_at',
                    'direction' => 'DESC',
                    'limit' => 100,
                    'status' => 'Activated',
                ],
            ],
            'processes_override' => [
                'query' => [
                    'sort' => 'created_at',
                    'limit' => -1,
                    'page' => 2.5,
                    'status' => ' ',
                ],
                'expectedDiff' => [
                    'sort' => 'created_at',
                    'direction' => 'ASC',
                    'limit' => 1,
                    'page' => 2,
                ],
            ],
        ];
    }

    /**
     * @dataProvider providerExtractModifiedParams
     * @param array $query
     * @param array $expected
     */
    public function testExtractModifiedParams(array $query, array $expected)
    {
        $actual = $this->IndexQueryHandler->extractModifiedParams($query);

        $this->assertEquals($expected, $actual);
    }

    public function providerExtractModifiedParams(): array
    {
        return array_merge($this->providerExtractAllParams(), [
            'excludes_default_values' => [
                'query' => array_merge(IndexQueryHandlerComponent::DEFAULT_QUERY, static::DEFAULT_QUERY),
                'expected' => [],
            ],
            'keep_sort_and_direction_if_sort_is_modified' => [
                'query' => [
                    'sort' => 'created_at',
                    'direction' => static::DEFAULT_QUERY['direction'],
                ],
                'expected' => [
                    'sort' => 'created_at',
                    'direction' => static::DEFAULT_QUERY['direction'],
                ],
            ],
            'keep_sort_and_direction_if_direction_is_modified' => [
                'query' => [
                    'sort' => static::DEFAULT_QUERY['sort'],
                    'direction' => 'bogus',
                ],
                'expected' => [
                    'sort' => static::DEFAULT_QUERY['sort'],
                    'direction' => 'ASC',
                ],
            ],
        ]);
    }

    /**
     * @dataProvider providerProcessQuery
     * @param array $query
     * @param array $expected
     * @throws ReflectionException
     */
    public function testProcessQuery(array $query, array $expected)
    {
        $actual = $this->callPrivateMethod([$this->IndexQueryHandler, '_processQuery'], $query);

        $this->assertEquals($expected, $actual);
    }

    public function providerProcessQuery(): array
    {
        return [
            'filter_removes_blank_value' => [
                'query' => [
                    'status' => ' ',
                ],
                'expected' => [
                ],
            ],
            'filter_keeps_empty_value' => [
                'query' => [
                    'status' => '',
                ],
                'expected' => [
                    'status' => '',
                ],
            ],
            'sort_order_empty_removes_both_fields' => [
                'query' => [
                    'sort' => '',
                    'direction' => 'DESC',
                ],
                'expected' => [
                ],
            ],
            'sort_order_uppercases_direction' => [
                'query' => [
                    'sort' => 'id',
                    'direction' => 'desc',
                ],
                'expected' => [
                    'sort' => 'id',
                    'direction' => 'DESC',
                ],
            ],
            'sort_order_fills_missing_direction' => [
                'query' => [
                    'sort' => 'id',
                ],
                'expected' => [
                    'sort' => 'id',
                    'direction' => 'ASC',
                ],
            ],
            'sort_order_fills_empty_direction' => [
                'query' => [
                    'sort' => 'id',
                    'direction' => '',
                ],
                'expected' => [
                    'sort' => 'id',
                    'direction' => 'ASC',
                ],
            ],
            'pagination_casts_to_int' => [
                'query' => [
                    'limit' => '10.9',
                    'page' => '',
                ],
                'expected' => [
                    'limit' => 10,
                    'page' => 1,
                ],
            ],
            'pagination_minimum_of_1' => [
                'query' => [
                    'limit' => -1,
                    'page' => 0,
                ],
                'expected' => [
                    'limit' => 1,
                    'page' => 1,
                ],
            ],
            'pagination_keeps_null' => [
                'query' => [
                    'limit' => null,
                    'page' => null,
                ],
                'expected' => [
                    'limit' => null,
                    'page' => null,
                ],
            ],
        ];
    }
}
