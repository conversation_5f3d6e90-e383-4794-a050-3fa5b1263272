<?php

use ShipEarlyApp\Test\Support\FakeAppController;

App::uses('IntegrationTestCase', 'TestSuite');
App::uses('OrderLogicComponent', 'Controller/Component');
App::uses('OrderStatus', 'Utility');
App::uses('OrderType', 'Utility');

/**
 * OrderLogicComponent::dealerOrderEvent Test Case.
 *
 * Run with the following console command:
 *
 * ./app/Console/cake test app Controller/Component/OrderLogic/OrderLogicDealerOrderEvent
 *
 * @property OrderLogicComponent|PHPUnit_Framework_MockObject_MockObject $OrderLogic
 * @property Order|PHPUnit_Framework_MockObject_MockObject $Order
 */
class OrderLogicDealerOrderEventTest extends IntegrationTestCase
{
    const ORDER_ID = '2';

    const DEFAULT_ORDER = [
        'Order' => [
            'id' => self::ORDER_ID,
            'user_id' => '8',
            'retailer_id' => '7',
            'branch_id' => null,
            'b2b_ship_to_user_id' => null,
            'order_type' => OrderType::IN_STORE_PICKUP,
            'order_status' => OrderStatus::NEED_TO_CONFIRM,
            'shipping_statecode' => '611',
            'shipping_countrycode' => '39',
        ],
    ];

    const DEFAULT_ORDER_PRODUCTS = [
        [
            'OrderProduct' => [
                'id' => '1755',
                'order_id' => self::ORDER_ID,
                'product_id' => '21',
                'warehouse_id' => '2',
                'quantity' => '2',
                'refunded_quantity' => '1',
                'remaining_quantity' => '1',
            ],
        ],
        [
            'OrderProduct' => [
                'id' => '1756',
                'order_id' => self::ORDER_ID,
                'product_id' => '23',
                'warehouse_id' => '1',
                'quantity' => '2',
                'refunded_quantity' => '0',
                'remaining_quantity' => '2',
            ],
        ],
    ];

    public $fixtures = [
        'app.b2b_ship_to_address',
        'app.brand_staff_permission',
        'app.configuration',
        'app.fulfillment_product',
        'app.manufacturer_retailer',
        'app.order',
        'app.order_refund_product',
        'app.order_comment',
        'app.order_customer_message',
        'app.pricing_tier',
        'app.pricing_tiers_hidden_warehouse',
        'app.product',
        'app.product_state_fee',
        'app.product_tier',
        'app.staff_permission',
        'app.user',
        'app.warehouse',
        'app.warehouse_product',
    ];

    public function setUp()
    {
        parent::setUp();

        /** @var OrderLogicDealerOrderEventTestController $controller */
        $controller = $this->generate('OrderLogicDealerOrderEventTest', [
            'components' => ['OrderLogic' => ['reserveNewDealerOrderInventory']],
        ]);
        $controller->startupProcess();
        $this->OrderLogic = $controller->OrderLogic;

        $this->Order = $this->getMockForModel('Order', ['find', 'save']);
        $this->OrderLogic->Order = $this->Order;

        // Called inside ProductTier::calcNewDealerOrderPricing
        /** @var OrderProduct|PHPUnit_Framework_MockObject_MockObject $OrderProduct */
        $OrderProduct = $this->getMockForModel('OrderProduct', ['find']);
        $OrderProduct
            ->expects($this->any())
            ->method('find')
            ->will($this->returnValue(static::DEFAULT_ORDER_PRODUCTS));
    }

    public function tearDown()
    {
        unset($this->OrderLogic);
        parent::tearDown();
    }

    public function testDealerOrderFromShipToStoreHasProvidedItems()
    {
        $orderId = static::ORDER_ID;
        $request_dealer_qty_ordered = [
            'products' => [
                21 => ['quantity' => '1'],
            ],
        ];
        $expected_dealer_qty_ordered = [
            'products' => [
                21 => [
                    'quantity' => 2,
                    'dealer_price' => '499.99',
                    'warehouse_id' => '2',
                ],
            ],
            'currencytype' => 'CAD',
            'b2b_tax' => '5.5000',
        ];

        $this->mockOrderFind(OrderType::IN_STORE_PICKUP, OrderStatus::NEED_TO_CONFIRM);
        $this->mockOrderSaveWithAssertion([
            'id' => $orderId,
            'order_status' => OrderStatus::DEALER_ORDER,
            'dealer_qty_ordered' => json_encode($expected_dealer_qty_ordered),
        ]);
        $this->OrderLogic->expects($this->once())->method('reserveNewDealerOrderInventory')->with($orderId);

        $this->assertTrue($this->OrderLogic->dealerOrderEvent($orderId, $request_dealer_qty_ordered));
    }

    public function testDealerOrderWithWholesaleTopUpHasDistinctSet()
    {
        $orderId = static::ORDER_ID;
        $request_dealer_qty_ordered = [
            'products' => [
                34 => ['quantity' => '1'],
            ],
        ];
        $expected_dealer_qty_ordered = [
            'products' => [
                34 => [
                    'quantity' => 1,
                    'dealer_price' => '375.00',
                    'warehouse_id' => '2',
                ],
                68 => [
                    'quantity' => 1,
                    'dealer_price' => '3.00',
                    'tax_rate' => '0.055000',
                    'warehouse_id' => null,
                ],
            ],
            'currencytype' => 'CAD',
            'b2b_tax' => '5.5000',
        ];

        $this->mockOrderFind(OrderType::IN_STORE_PICKUP, OrderStatus::NEED_TO_CONFIRM);
        $this->mockOrderSaveWithAssertion([
            'id' => $orderId,
            'order_status' => OrderStatus::DEALER_ORDER,
            'dealer_qty_ordered' => json_encode($expected_dealer_qty_ordered),
        ]);
        $this->OrderLogic->expects($this->once())->method('reserveNewDealerOrderInventory')->with($orderId);

        $this->assertTrue($this->OrderLogic->dealerOrderEvent($orderId, $request_dealer_qty_ordered));
    }

    public function testDealerOrderFromShipToStoreCronHasAllItems()
    {
        $orderId = static::ORDER_ID;
        $expected_dealer_qty_ordered = [
            'products' => [
                21 => [
                    'quantity' => 2,
                    'dealer_price' => '499.99',
                    'warehouse_id' => '2',
                ],
                23 => [
                    'quantity' => 2,
                    'dealer_price' => '499.00',
                    'warehouse_id' => '1',
                ],
            ],
            'currencytype' => 'CAD',
            'b2b_tax' => '5.5000',
        ];

        $this->mockOrderFind(OrderType::IN_STORE_PICKUP, OrderStatus::NEED_TO_CONFIRM);
        $this->mockOrderSaveWithAssertion([
            'id' => $orderId,
            'order_status' => OrderStatus::DEALER_ORDER,
            'dealer_qty_ordered' => json_encode($expected_dealer_qty_ordered),
        ]);
        $this->OrderLogic->expects($this->once())->method('reserveNewDealerOrderInventory')->with($orderId);

        $this->assertTrue($this->OrderLogic->dealerOrderEvent($orderId));
    }

    public function testDealerOrderFromShipFromStoreHasAllItems()
    {
        $orderId = static::ORDER_ID;
        $request_dealer_qty_ordered = [
            'products' => [
                21 => ['quantity' => '1'],
            ],
        ];
        $expected_dealer_qty_ordered = [
            'products' => [
                21 => [
                    'quantity' => 2,
                    'dealer_price' => '499.99',
                    'warehouse_id' => '2',
                ],
                23 => [
                    'quantity' => 2,
                    'dealer_price' => '499.00',
                    'warehouse_id' => '1',
                ],
            ],
            'currencytype' => 'CAD',
            'b2b_tax' => '5.5000',
        ];

        $this->mockOrderFind(OrderType::SHIP_FROM_STORE, OrderStatus::NEED_TO_CONFIRM);
        $this->mockOrderSaveWithAssertion([
            'id' => $orderId,
            'order_status' => OrderStatus::DEALER_ORDER,
            'dealer_qty_ordered' => json_encode($expected_dealer_qty_ordered),
        ]);
        $this->OrderLogic->expects($this->once())->method('reserveNewDealerOrderInventory')->with($orderId);

        $this->assertTrue($this->OrderLogic->dealerOrderEvent($orderId, $request_dealer_qty_ordered));
    }

    public function testDealerOrderFromInvalidStatusFails()
    {
        $orderId = static::ORDER_ID;
        $request_dealer_qty_ordered = [
            'products' => [
                21 => ['quantity' => '1'],
            ],
        ];

        $this->mockOrderFind(OrderType::IN_STORE_PICKUP, OrderStatus::DEALER_ORDER);
        $this->Order->expects($this->never())->method('save');
        $this->OrderLogic->expects($this->never())->method('reserveNewDealerOrderInventory');

        $this->assertFalse($this->OrderLogic->dealerOrderEvent($orderId, $request_dealer_qty_ordered));
    }

    private function mockOrderFind(string $orderType, string $orderStatus)
    {
        $order = static::DEFAULT_ORDER;
        $order['Order']['order_type'] = $orderType;
        $order['Order']['order_status'] = $orderStatus;

        $this->Order
            ->expects($this->once())
            ->method('find')
            ->willReturn($order);
    }

    private function mockOrderSaveWithAssertion(array $expectedData)
    {
        $this->Order
            ->expects($this->once())
            ->method('save')
            ->with($expectedData)
            ->willReturn(true);
    }
}

/**
 * OrderLogicDealerOrderEvent Test Controller.
 *
 * @property OrderLogicComponent $OrderLogic
 */
class OrderLogicDealerOrderEventTestController extends FakeAppController
{
    public $components = ['OrderLogic'];
}
