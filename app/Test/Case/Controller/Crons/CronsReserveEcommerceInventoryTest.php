<?php
App::uses('IntegrationTestCase', 'TestSuite');
App::uses('<PERSON><PERSON><PERSON><PERSON>roller', 'Controller');

/**
 * CronsController::_reserveEcommerceInventory Test Case.
 *
 * Run with the following console command:
 *
 * ./app/Console/cake test app Controller/Crons/CronsReserveEcommerceInventory
 *
 * @property CronsReserveEcommerceInventoryTestController|PHPUnit_Framework_MockObject_MockObject $controller
 */
class CronsReserveEcommerceInventoryTest extends IntegrationTestCase
{
    const WAREHOUSE_PRODUCT_ID = '1';
    const PRODUCT_ID = '21';
    const BRAND_ID = '8';
    const USER_LOG = ['id' => self::BRAND_ID, 'email_address' => '<EMAIL>', 'site_type' => 'Shopify', 'shop_url' => 'aron-shipearly-2.myshopify.com', 'api_key' => '*****', 'secret_key' => '*****'];

    const SHOPIFY_MOCK_METHODS = ['listMissingAccessScopes', 'setInventoryLevel'];

    const SHOPIFY_INVENTORY_ITEM_ID = '7412906375';
    const SHOPIFY_LOCATION_ID = '36179574835';
    const RESERVED_QUANTITY = '3';

    public $fixtures = [
        'app.brand_staff_permission',
        'app.product',
        'app.product_state_fee',
        'app.staff_permission',
        'app.user',
        'app.warehouse',
        'app.warehouse_product',
    ];

    public function setUp()
    {
        parent::setUp();
        $this->generate('CronsReserveEcommerceInventoryTest', [
            'components' => ['Shopify.Shopify' => static::SHOPIFY_MOCK_METHODS],
        ]);

        /** @var ShopifyComponent|PHPUnit_Framework_MockObject_MockObject $Shopify */
        $Shopify = $this->controller->Shopify;
        $Shopify->expects($this->never())->method('listMissingAccessScopes');
        $Shopify->expects($this->never())->method('setInventoryLevel');

        $this->controller->Warehouse->WarehouseProduct->updateAllJoinless(
            ['WarehouseProduct.reserved_quantity' => 0],
            ['WarehouseProduct.product_id' => static::PRODUCT_ID]
        );
    }

    public function testReserveEcommerceInventory()
    {
        $this->controller->Warehouse->WarehouseProduct->updateAllJoinless(
            ['WarehouseProduct.reserved_quantity' => static::RESERVED_QUANTITY],
            ['WarehouseProduct.id' => static::WAREHOUSE_PRODUCT_ID]
        );

        $Shopify = $this->resetShopifyMock();
        $Shopify->expects($this->once())->method('listMissingAccessScopes')->willReturn([]);
        $Shopify->expects($this->once())->method('setInventoryLevel')->with(
            'e8d27b84d0d3e8549cd2faa84309c75d',
            '723213fc099cd21a806692052d3c8949',
            'aron-shipearly-2.myshopify.com',
            static::SHOPIFY_INVENTORY_ITEM_ID,
            static::SHOPIFY_LOCATION_ID,
            -static::RESERVED_QUANTITY
        )->willReturn([
            'inventory_item_id' => (int)static::SHOPIFY_INVENTORY_ITEM_ID,
            'location_id' => (int)static::SHOPIFY_LOCATION_ID,
            'available' => -static::RESERVED_QUANTITY,
            'updated_at' => static::date(DATE_W3C),
            'admin_graphql_api_id' => sprintf('gid://shopify/InventoryLevel/%s?inventory_item_id=%s', static::SHOPIFY_LOCATION_ID, static::SHOPIFY_INVENTORY_ITEM_ID),
        ]);

        $this->assertTrue($this->controller->_reserveEcommerceInventory(static::WAREHOUSE_PRODUCT_ID), 'Success');
    }

    public function testReserveTotalInventoryOfProductWithMultipleWarehouses()
    {
        $otherId = $this->controller->Warehouse->WarehouseProduct->fieldByConditions('id', [
            'product_id' => static::PRODUCT_ID,
            'id !=' => static::WAREHOUSE_PRODUCT_ID,
        ]);

        $this->controller->Warehouse->WarehouseProduct->updateAllJoinless(
            ['WarehouseProduct.reserved_quantity' => (static::RESERVED_QUANTITY - 1)],
            ['WarehouseProduct.id' => static::WAREHOUSE_PRODUCT_ID]
        );
        $this->controller->Warehouse->WarehouseProduct->updateAllJoinless(
            ['WarehouseProduct.reserved_quantity' => 1],
            ['WarehouseProduct.id' => $otherId]
        );

        $Shopify = $this->resetShopifyMock();
        $Shopify->expects($this->once())->method('listMissingAccessScopes')->willReturn([]);
        $Shopify->expects($this->once())->method('setInventoryLevel')->with(
            'e8d27b84d0d3e8549cd2faa84309c75d',
            '723213fc099cd21a806692052d3c8949',
            'aron-shipearly-2.myshopify.com',
            static::SHOPIFY_INVENTORY_ITEM_ID,
            static::SHOPIFY_LOCATION_ID,
            -static::RESERVED_QUANTITY
        )->willReturn([
            'inventory_item_id' => (int)static::SHOPIFY_INVENTORY_ITEM_ID,
            'location_id' => (int)static::SHOPIFY_LOCATION_ID,
            'available' => -static::RESERVED_QUANTITY,
            'updated_at' => static::date(DATE_W3C),
            'admin_graphql_api_id' => sprintf('gid://shopify/InventoryLevel/%s?inventory_item_id=%s', static::SHOPIFY_LOCATION_ID, static::SHOPIFY_INVENTORY_ITEM_ID),
        ]);

        $this->assertTrue($this->controller->_reserveEcommerceInventory(static::WAREHOUSE_PRODUCT_ID), 'Success');
    }

    public function testApiFailure()
    {
        $Shopify = $this->resetShopifyMock();
        $Shopify->expects($this->once())->method('listMissingAccessScopes')->willReturn([]);
        //TODO ShopifyAPIComponent should throw an exception instead of just logging one
        $Shopify->expects($this->once())->method('setInventoryLevel')->willReturn(false);

        $this->assertFalse($this->controller->_reserveEcommerceInventory(static::WAREHOUSE_PRODUCT_ID), 'Success');
    }

    public function testRecordNotFound()
    {
        $this->controller->Warehouse->WarehouseProduct->delete(static::WAREHOUSE_PRODUCT_ID);

        $this->setExpectedException(NotFoundException::class, json_encode(['message' => 'Not Found', 'warehouse_product_id' => static::WAREHOUSE_PRODUCT_ID]));

        $this->controller->_reserveEcommerceInventory(static::WAREHOUSE_PRODUCT_ID);
    }

    public function testReserveLocationNotFound()
    {
        $this->controller->Warehouse->deleteAllJoinless(['Warehouse.user_id' => static::BRAND_ID, 'Warehouse.name' => Warehouse::NAME_RESERVED], false);

        $this->assertFalse($this->controller->_reserveEcommerceInventory(static::WAREHOUSE_PRODUCT_ID), 'Success');
    }

    public function testReserveLocationNotConnectedToProductWillCreateConnection()
    {
        $this->controller->Warehouse->WarehouseProduct->updateAllJoinless(
            ['WarehouseProduct.reserved_quantity' => static::RESERVED_QUANTITY],
            ['WarehouseProduct.id' => static::WAREHOUSE_PRODUCT_ID]
        );
        $this->_deleteWarehouseProductsByAssocConditions([
            'Product.inventory_item_id' => static::SHOPIFY_INVENTORY_ITEM_ID,
            'Warehouse.name' => Warehouse::NAME_RESERVED,
        ]);

        // It has been verified that this Shopify API call will create a new
        // InventoryLevel if one does not exist so assert that the call is made
        $Shopify = $this->resetShopifyMock();
        $Shopify->expects($this->once())->method('listMissingAccessScopes')->willReturn([]);
        $Shopify->expects($this->once())->method('setInventoryLevel')->with(
            'e8d27b84d0d3e8549cd2faa84309c75d',
            '723213fc099cd21a806692052d3c8949',
            'aron-shipearly-2.myshopify.com',
            static::SHOPIFY_INVENTORY_ITEM_ID,
            static::SHOPIFY_LOCATION_ID,
            -static::RESERVED_QUANTITY
        )->willReturn([
            'inventory_item_id' => (int)static::SHOPIFY_INVENTORY_ITEM_ID,
            'location_id' => (int)static::SHOPIFY_LOCATION_ID,
            'available' => -static::RESERVED_QUANTITY,
            'updated_at' => static::date(DATE_W3C),
            'admin_graphql_api_id' => sprintf('gid://shopify/InventoryLevel/%s?inventory_item_id=%s', static::SHOPIFY_LOCATION_ID, static::SHOPIFY_INVENTORY_ITEM_ID),
        ]);

        $this->assertTrue($this->controller->_reserveEcommerceInventory(static::WAREHOUSE_PRODUCT_ID), 'Success');
    }

    public function testSiteTypeNotSupported()
    {
        $this->controller->User->save(['id' => static::BRAND_ID, 'site_type' => 'Woocommerce']);

        $this->setExpectedException(ForbiddenException::class, json_encode(['message' => 'User site_type not supported', 'User' => array_merge(static::USER_LOG, ['site_type' => 'Woocommerce'])]));

        $this->controller->_reserveEcommerceInventory(static::WAREHOUSE_PRODUCT_ID);
    }

    public function testMissingApiPermissions()
    {
        $Shopify = $this->resetShopifyMock();
        $Shopify->expects($this->once())->method('listMissingAccessScopes')->willReturn(['write_inventory']);
        $Shopify->expects($this->never())->method('setInventoryLevel');

        $this->setExpectedException(ForbiddenException::class, json_encode(['message' => 'Required Shopify API permissions are not enabled for this app', 'permissions' => ['write_inventory'], 'User' => static::USER_LOG]));

        $this->controller->_reserveEcommerceInventory(static::WAREHOUSE_PRODUCT_ID);
    }

    private function _deleteWarehouseProductsByAssocConditions(array $conditions): bool
    {
        $warehouseProductIdsToDelete = $this->controller->Warehouse->WarehouseProduct->find('list', [
            'contain' => ['Product', 'Warehouse'],
            'conditions' => $conditions,
            'fields' => ['id', 'id'],
        ]);

        return $this->controller->Warehouse->WarehouseProduct->deleteAllJoinless(['WarehouseProduct.id' => $warehouseProductIdsToDelete], false);
    }

    private function resetShopifyMock(array $methods = self::SHOPIFY_MOCK_METHODS)
    {
        /** @var ShopifyComponent|PHPUnit_Framework_MockObject_MockObject $Shopify */
        $Shopify = $this->getMock(ShopifyComponent::class, $methods, [$this->controller->Components]);

        $this->controller->Components->set('Shopify', $Shopify);
        $this->controller->Components->enable('Shopify');
        $this->controller->Components->init($this->controller);

        return $Shopify;
    }
}

class CronsReserveEcommerceInventoryTestController extends CronsController
{
    public function _reserveEcommerceInventory($warehouseProductId): bool
    {
        return parent::_reserveEcommerceInventory($warehouseProductId);
    }
}
