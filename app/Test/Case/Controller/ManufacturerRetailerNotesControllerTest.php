<?php
App::uses('IntegrationTestCase', 'TestSuite');
App::uses('ManufacturerRetailerNotesController', 'Controller');

/**
 * ManufacturerRetailerNotesController Test Case.
 *
 * @property ManufacturerRetailerNotesController|PHPUnit_Framework_MockObject_MockObject $controller
 */
class ManufacturerRetailerNotesControllerTest extends IntegrationTestCase
{
    public $fixtures = [
        'app.manufacturer_retailer_note',
        'app.user',
        'app.manufacturer_retailer',
        'app.b2b_cart',
        'app.discount',
        'app.credit_term',
        'app.order',
        'app.legacy_retailer_credit',
        'app.retailer_credit_term',
        'app.discount_credit_term',
        'app.b2b_ship_to_address',
        'app.country',
        'app.country_tax',
        'app.state_tax',
        'app.state',
        'app.user_setting',
        'app.b2b_cart_product',
        'app.product',
        'app.warehouse',
        'app.warehouse_product',
        'app.warehouse_product_reservation',
        'app.inventory_transfer',
        'app.inventory_transfer_product',
        'app.inventory_transfer_product_reservation',
        'app.btask',
        'app.configuration',
        'app.cron',
        'app.notification',
        'app.user_categories',
        'app.user_subdomain',
        'app.watch_product',
    ];

    public function setUp()
    {
        parent::setUp();
        $this->generate('ManufacturerRetailerNotes', [
            // Methods called in AppController::beforeFilter that add unnecessary dependencies
            'methods' => ['_headerNotifications', '_checkCron'],
        ]);

        $this->controller->Auth->unauthorizedRedirect = false;

        // Preferred way to set an initial auth user
        //$this->controller->Auth->login(['id' => static::USER_ID_BRAND]);
    }
}
