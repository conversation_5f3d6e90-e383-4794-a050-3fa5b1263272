<?php
App::uses('IntegrationTestCase', 'TestSuite');
App::uses('RetailersController', 'Controller');
App::uses('User', 'Model');
App::uses('OrderType', 'Utility');

/**
 * OrdersController::dealerorder_totals Test Case.
 *
 * Run with the following console command:
 *
 * ./app/Console/cake test app Controller/Orders/OrdersDealerorderTotals --stderr
 *
 * @property OrdersController|PHPUnit_Framework_MockObject_MockObject $controller
 *
 * @see OrdersController::dealerorder_totals()
 */
class OrdersDealerorderTotalsTest extends IntegrationTestCase
{
    const USER_ID_BRAND = '8';

    /**
     * @var string
     * @see OrderSeeder::CASE_IDS
     */
    const ORDER_ID_PURCHASE_ORDER = '3';

    // Request data sent when first loading this order
    const DEFAULT_REQUEST_DATA = [
        'dealerTotalInvoiceAmt' => '1821.99',
        'Order' => [
            'id' => self::ORDER_ID_PURCHASE_ORDER,
            'order_type' => 'wholesale',
            'order_status' => 'Purchase Order',
            'is_dealerorder' => '1',
            'b2b_tax_rate' => '5.5',
            'b2b_shipping_tax_option' => '1',
            'currency_code' => 'USD',
            'requested_b2b_ship_date' => 'May 15, 2020',
            'external_invoice_id' => '',
            'restock_date' => [
                1 => [21 => '', 23 => '2021-09-30'],
                2 => [21 => ''],
            ],
            'dealer_quantity' => [
                1 => [21 => '1', 23 => '2'],
                2 => [21 => '2'],
            ],
            'extra_quantity' => [
                1 => [21 => '1', 23 => '2'],
                2 => [21 => '2'],
            ],
            'dealer_price' => [
                1 => [21 => '499.00', 23 => '100.00'],
                2 => [21 => '499.00'],
            ],
            'product_total' => '1697.00',
            'total_tax_amount' => '94.99',
            'dealer_total_amount' => '1821.99',
            'place_ecommerce_order' => '1',
            'Discount' => [
                'enable_free_freight' => false,
            ],
        ],
        'DealerOrder' => [
            'dealerDiscount' => '0.00',
            'calculatedDiscount' => '0.00',
            'total_discount' => '0.00',
            'b2b_shipping_rate_id' => '2',
            'shipping_name' => '',
        ],
    ];

    public $fixtures = [
        'app.b2b_shipping_rate',
        'app.b2b_shipping_rate_category',
        'app.b2b_shipping_rate_title',
        'app.b2b_shipping_zone_tier',
        'app.brand_staff',
        'app.brand_staff_permission',
        'app.btask',
        'app.category',
        'app.collection',
        'app.collections_product',
        'app.contactpersons',
        'app.configuration',
        'app.credit_term',
        'app.courier',
        'app.dealer_order',
        'app.discount',
        'app.discount_credit_term',
        'app.fulfillment',
        'app.fulfillment_product',
        'app.i18n',
        'app.inventory_transfer',
        'app.inventory_transfer_product',
        'app.inventory_transfer_product_reservation',
        'app.manufacturer_retailer',
        'app.manufacturer_sales_rep',
        'app.order',
        'app.order_address',
        'app.order_comment',
        'app.order_customer_message',
        'app.order_payout',
        'app.order_product',
        'app.order_refund',
        'app.order_refund_product',
        'app.order_sales_rep',
        'app.pricing_tiers_hidden_warehouse',
        'app.product',
        'app.product_non_applicable_order_type',
        'app.product_state_fee',
        'app.product_tag',
        'app.product_title',
        'app.product_variant_option',
        'app.retailer_credit_term',
        'app.staff',
        'app.staff_permission',
        'app.state',
        'app.tag',
        'app.user',
        'app.user_category',
        'app.user_domain',
        'app.user_setting',
        'app.user_subdomain',
        'app.variant_option',
        'app.warehouse',
        'app.warehouse_product',
        'app.warehouse_product_reservation',
    ];

    public function setUp()
    {
        parent::setUp();
        $this->generate('Orders', [
            // Methods called in AppController::beforeFilter that add unnecessary dependencies
            'methods' => ['_headerNotifications', '_checkCron'],
        ]);

        $this->controller->Auth->unauthorizedRedirect = false;

        // Preferred way to set an initial auth user
        $this->controller->Auth->login(['id' => static::USER_ID_BRAND]);

        /** @var ShippingCalculatorComponent|PHPUnit_Framework_MockObject_MockObject $ShippingCalculator */
        $ShippingCalculator = $this->getMockForComponent('ShippingCalculator', ['calculateShippingFromDealerProducts'], $this->controller->Components);
        $ShippingCalculator->expects($this->any())->method('calculateShippingFromDealerProducts')->willReturn(30.00);
        $this->controller->ShippingCalculator = $ShippingCalculator;
    }

    public function testDealerorderTotalsUpdatesFromExtraQuantity()
    {
        $orderId = static::ORDER_ID_PURCHASE_ORDER;
        $requestData = static::DEFAULT_REQUEST_DATA;
        $requestData['Order'] = array_merge($requestData['Order'], [
            'dealer_quantity' => [
                1 => [21 => '1', 23 => '0'],
                2 => [21 => '2'],
            ],
            'extra_quantity' => [
                1 => [21 => '2', 23 => '0'],
                2 => [21 => '3'],
            ],
        ]);

        $this->post(['controller' => 'orders', 'action' => 'dealerorder_totals', $orderId], $requestData);
        $this->assertResponseOk();

        $actual = $this->_findPurchaseOrderUpdateFields($orderId);
        $expected = [
            'Order' => [
                'id' => $orderId,
                'dealer_qty_ordered' => '{"products":{"21":{"quantity":5,"dealer_price":"499.00"},"23":{"quantity":0,"dealer_price":"100.00"}},"currencytype":"USD","b2b_tax":"5.5000"}',
                'total_price' => '2663.88',
                'total_discount' => '0.00',
                'totalPriceConversion' => '0.00',
                'total_tax' => '138.88',
                'shipping_amount' => '30.00',
                'total_qty_ordered' => '5',
                'retailerAmount' => '2663.88',
            ],
            'OrderProduct' => [
                0 => [
                    'order_id' => $orderId,
                    'product_id' => '21',
                    'warehouse_id' => '1',
                    'quantity' => '2',
                    'total_tax' => '54.89',
                    'total_price' => '998.00',
                    'total_discount' => '0.00',
                    'totalPriceConversion' => '0.00',
                ],
                1 => [
                    'order_id' => $orderId,
                    'product_id' => '23',
                    'warehouse_id' => '1',
                    'quantity' => '0',
                    'total_tax' => '0.00',
                    'total_price' => '0.00',
                    'total_discount' => '0.00',
                    'totalPriceConversion' => '0.00',
                ],
                2 => [
                    'order_id' => $orderId,
                    'product_id' => '21',
                    'warehouse_id' => '2',
                    'quantity' => '3',
                    'total_tax' => '82.34',
                    'total_price' => '1497.00',
                    'total_discount' => '0.00',
                    'totalPriceConversion' => '0.00',
                ],
            ],
        ];
        $this->assertEquals($expected, $actual);
    }

    /**
     * @dataProvider providerDealerorderTotalsReservesWarehouseInventory
     */
    public function testDealerorderTotalsReservesWarehouseInventory(string $subType)
    {
        $orderId = static::ORDER_ID_PURCHASE_ORDER;
        $requestData = static::DEFAULT_REQUEST_DATA;
        $requestData['Order'] = array_merge($requestData['Order'], [
            'dealer_quantity' => [
                1 => [21 => '1', 23 => '0'],
                2 => [21 => '2'],
            ],
            'extra_quantity' => [
                1 => [21 => '2', 23 => '0'],
                2 => [21 => '3'],
            ],
        ]);

        if (!$this->controller->Order->updateAllJoinless(['Order.subType' => $this->controller->Order->value($subType)], ['Order.id' => $orderId])) {
            throw new RuntimeException('Failed to seed order subType');
        }

        $this->post(['controller' => 'orders', 'action' => 'dealerorder_totals', $orderId], $requestData);
        $this->assertResponseOk();

        $actual = $this->controller->WarehouseProductReservation->find('list', [
            'conditions' => ['WarehouseProductReservation.order_id' => $orderId],
            'fields' => ['warehouse_product_id', 'quantity'],
            'order' => ['WarehouseProductReservation.warehouse_product_id' => 'ASC'],
        ]);
        $expected = ($subType !== OrderType::SUB_TYPE_B2B_BOOKING)
            ? [1 => '3', 7 => '2']
            : [];
        $this->assertEquals($expected, $actual);
    }

    public function providerDealerorderTotalsReservesWarehouseInventory(): array
    {
        return [
            'regular' => ['subType' => OrderType::SUB_TYPE_B2B_REGULAR],
            'booking' => ['subType' => OrderType::SUB_TYPE_B2B_BOOKING],
        ];
    }

    public function testDealerorderTotalsPersistanceIsIdempotent()
    {
        $orderId = static::ORDER_ID_PURCHASE_ORDER;
        $requestData = static::DEFAULT_REQUEST_DATA;

        $before = $this->_findPurchaseOrderUpdateFields($orderId);
        $before['OrderProduct'] = array_map(function($item) {
            //FIXME change varchar DB columns to decimal so we can guarantee number format
            foreach (['total_tax', 'total_price', 'total_discount', 'totalPriceConversion'] as $priceField) {
                $item[$priceField] = format_number($item[$priceField]);
            }

            return $item;
        }, $before['OrderProduct']);

        $this->post(['controller' => 'orders', 'action' => 'dealerorder_totals', $orderId], $requestData);
        $this->assertResponseOk();

        $after = $this->_findPurchaseOrderUpdateFields($orderId);
        $this->assertEquals($before, $after);
    }

    private function _findPurchaseOrderUpdateFields(int $orderId): array
    {
        $this->controller->Order->bindModel(['hasMany' => ['OrderProduct']], false);
        $result = (array)$this->controller->Order->get($orderId, [
            'contain' => [
                'OrderProduct' => [
                    'fields' => ['order_id', 'product_id', 'warehouse_id', 'quantity', 'total_tax', 'total_price', 'total_discount', 'totalPriceConversion'],
                    'order' => ['OrderProduct.warehouse_id' => 'ASC', 'OrderProduct.product_id' => 'ASC'],
                ],
            ],
            'fields' => ['id', 'dealer_qty_ordered', 'total_price', 'total_discount', 'totalPriceConversion', 'total_tax', 'shipping_amount', 'total_qty_ordered', 'retailerAmount'],
        ]);
        $this->controller->Order->unbindModel(['hasMany' => ['OrderProduct']], false);

        return $result;
    }
}
