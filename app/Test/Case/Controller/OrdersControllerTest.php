<?php
App::uses('IntegrationTestCase', 'TestSuite');
App::uses('OrdersController', 'Controller');
App::uses('FulfillmentStatus', 'Utility');
App::uses('OrderPaymentMethod', 'Utility');
App::uses('OrderPaymentStatus', 'Utility');
App::uses('OrderStatus', 'Utility');
App::uses('OrderType', 'Utility');

/**
 * OrdersController Test Case
 *
 * Run with the following console command:
 *
 * ./app/Console/cake test app Controller/OrdersController --stderr
 *
 * @property OrdersController|PHPUnit_Framework_MockObject_MockObject $controller
 */
class OrdersControllerTest extends IntegrationTestCase
{
    const USER_ID_BRAND = '8';
    const USER_ID_OTHER_BRAND = '12';
    const USER_UUID_BRAND = '5666ffac-5964-4f7a-9048-1cc291c2de43';

    const USER_ID_RETAILER = '7';

    public $fixtures = [
        'app.brand_staff',
        'app.brand_staff_permission',
        'app.category',
        'app.configuration',
        'app.contact',
        'app.contactpersons',
        'app.country',
        'app.customer',
        'app.dealer_order',
        'app.email_template',
        'app.i18n',
        'app.inventory_transfer_product_reservation',
        'app.mail_queue',
        'app.manufacturer_retailer',
        'app.manufacturer_sales_rep',
        'app.order',
        'app.order_product',
        'app.order_sales_rep',
        'app.product',
        'app.product_non_applicable_order_type',
        'app.product_state_fee',
        'app.product_title',
        'app.product_variant_option',
        'app.staff',
        'app.staff_permission',
        'app.state',
        'app.user',
        'app.user_category',
        'app.warehouse_product_reservation',
    ];

    public function setUp()
    {
        parent::setUp();

        /**
         * @see AppController::_headerNotifications()
         * @see AppController::_checkCron()
         * @see AppController::beforeRender()
         * @see Order::initializeWithAssociatedModels()
         */
        $mocksForUnnecessaryDependencies = [
            'methods' => ['_headerNotifications', '_checkCron', 'beforeRender'],
            'models' => [
                'Order' => ['initializeWithAssociatedModels'],
            ],
        ];
        $this->generate('Orders', Hash::merge($mocksForUnnecessaryDependencies, [
            'models' => [
                'Order' => ['findForPopup'],
            ],
            'components' => [
                'OrderLogic' => ['captureStripeCharge'],
                'Stripe.Stripe' => true,
                'Upload' => true,
            ],
        ]));

        $this->controller->Auth->unauthorizedRedirect = false;
        $this->controller->Auth->login(['id' => static::USER_ID_BRAND]);
    }

    /**
     * @dataProvider providerMarkDelivered
     * @see OrdersController::mark_delivered
     */
    public function testMarkDelivered($captured, $expected)
    {
        $id = $expected['id'];
        if (!$captured) {
            $this->seedUncapturedOrderForMarkDelivered($id);
        }
        $this->mockCaptureStripeChargeForMarkDelivered();

        $this->put(['controller' => 'orders', 'action' => 'mark_delivered', $id]);

        $this->assertEquals($expected, $this->findOrderMarkedAsDeliveredAssertion($id));
        $this->assertRedirect();
        $this->assertEmpty($this->controller->getFlash('error'), 'Error flash message: ' . $this->controller->getFlash('error'));
        $this->assertNotEmpty($this->controller->getFlash('success'), 'Success flash message');
    }

    public function providerMarkDelivered(): array
    {
        $cases = array_filter($this->providerPutVerificationCode(), function($case) {
            return !$case['has_verification_image'];
        });

        return Hash::remove($cases, '{s}.has_verification_image');
    }

    /**
     * @dataProvider providerPutVerificationCode
     * @see OrdersController::verification_code
     */
    public function testPutVerificationCode($hasVerificationImage, $captured, $expected)
    {
        $id = $expected['id'];
        $verificationImageFile = [
            'error' => ($hasVerificationImage) ? UPLOAD_ERR_OK : UPLOAD_ERR_NO_FILE,
            // Don't care about the rest of the file argument
        ];
        if (!$captured) {
            $this->seedUncapturedOrderForMarkDelivered($id);
        }

        // Not strictly necessary but saves on fixture dependencies
        $this->mockOrderFindForPopupForVerificationCode($id, $captured);

        $this->mockUploadVerificationCodeImage($id, $verificationImageFile, 'https://example.com/verification.jpeg');
        $this->mockCaptureStripeChargeForMarkDelivered();

        $this->put(['controller' => 'orders', 'action' => 'verification_code', $id], [
            'Order' => [
                'instore_code' => 'UW5NFZ',
                'verification_image_file' => $verificationImageFile,
            ],
        ]);

        $this->assertEquals($expected, $this->findOrderMarkedAsDeliveredAssertion($id));
        $this->assertRedirect();
        $this->assertEmpty($this->controller->getFlash('error'), 'Error flash message: ' . $this->controller->getFlash('error'));
        $this->assertNotEmpty($this->controller->getFlash('success'), 'Success flash message');
    }

    public function providerPutVerificationCode(): array
    {
        $defaultExpected = [
            'id' => '1',
            'order_status' => OrderStatus::DELIVERED,
            'fulfillment_status' => FulfillmentStatus::FULFILLED,
            'deliveryDate' => static::date(),
            'verification_image_url' => null,
            'payment_status' => OrderPaymentStatus::PAID,
            'payment_captured_at' => '2020-02-29 23:59:59',
            'balance_transaction_id' => 'txn_1GO6u8LBFYPhk3KpE1ZwLew3',
            'stripe_fees' => '79.17',
        ];

        return [
            'default' => [
                'has_verification_image' => false,
                'captured' => true,
                'expected' => array_merge($defaultExpected, [
                ]),
            ],
            'capture' => [
                'has_verification_image' => false,
                'captured' => false,
                'expected' => array_merge($defaultExpected, [
                    'payment_captured_at' => static::date(),
                    'balance_transaction_id' => 'txn_fake',
                    'stripe_fees' => '80.00',
                ]),
            ],
            'upload_image' => [
                'has_verification_image' => true,
                'captured' => true,
                'expected' => array_merge($defaultExpected, [
                    'verification_image_url' => 'https://example.com/verification.jpeg',
                ]),
            ],
        ];
    }

    public function testCancelStripeOrder()
    {
        /**
         * purchase_order_on_file
         *
         * @see OrderSeeder::CASE_IDS
         */
        $id = '3';
        if (!$this->controller->Order->save([
            'id' => $id,
            'payment_method' => OrderPaymentMethod::STRIPE,
            'transactionID' => 'pi_fake',
            'stripe_account' => 'acct_fake_brand',
        ])) {
            throw new RuntimeException('Failed to seed order');
        }
        /** @var StripeComponent|PHPUnit_Framework_MockObject_MockObject $Stripe */
        $Stripe = $this->controller->Stripe;
        $Stripe->expects($this->once())->method('cancelUnconfirmedPayment')
            ->with('acct_fake_brand', 'pi_fake', [])
            ->willReturn(\Stripe\PaymentIntent::constructFrom([
                'id' => 'pi_fake',
                'cancelled_at' => static::time(),
                'cancellation_reason' => null,
                'status' => 'canceled',
            ]));

        $this->post(['controller' => 'orders', 'action' => 'cancel', $id]);

        $this->assertFalse($this->controller->Order->existsById($id), 'Order should have been deleted');
        $actualEmailsSent = $this->controller->MailQueue->find('all', [
            'conditions' => [
                'MailQueue.status' => MailQueue::STATUS_OPEN,
            ],
            'fields' => ['fromemail', 'to', 'cc', 'bcc', 'logo', 'subject', 'content', 'attachments', 'generateUrl'],
        ]);
        $expectedEmailsSent = [
            [
                'MailQueue' => [
                    'fromemail' => 'My Local Brand<<EMAIL>>',
                    'to' => json_encode(['<EMAIL>']),
                    'cc' => json_encode([]),
                    'bcc' => json_encode([]),
                    'logo' => 'shopify0.png',
                    'subject' => 'My Local Brand has cancelled purchase order #SE0012504',
                    // Assume content is correct if the rest of the record is correct
                    'content' => $actualEmailsSent[0]['MailQueue']['content'],
                    'attachments' => json_encode([]),
                    'generateUrl' => '',
                ],
            ],
        ];
        $this->assertEquals($expectedEmailsSent, $actualEmailsSent, 'Emails sent');

        $this->assertRedirect();
        $this->assertEquals('Order "#SE0012504" has been cancelled', $this->controller->getFlash('success'), 'Success flash message');
        $this->assertEquals('', $this->controller->getFlash('error'), 'Error flash message');
    }

    public function testCancelInvalidOrder()
    {
        /**
         * api_wholesale
         *
         * @see OrderSeeder::CASE_IDS
         */
        $id = '7';
        $this->controller->Auth->login(['id' => static::USER_ID_OTHER_BRAND]);
        /** @var StripeComponent|PHPUnit_Framework_MockObject_MockObject $Stripe */
        $Stripe = $this->controller->Stripe;
        $Stripe->expects($this->never())->method('cancelUnconfirmedPayment');

        $this->post(['controller' => 'orders', 'action' => 'cancel', $id]);

        $this->assertTrue($this->controller->Order->existsById($id), 'Order should not have been deleted');
        $actualEmailsSent = $this->controller->MailQueue->find('all', [
            'conditions' => [
                'MailQueue.status' => MailQueue::STATUS_OPEN,
            ],
            'fields' => ['fromemail', 'to', 'cc', 'bcc', 'logo', 'subject', 'content', 'attachments', 'generateUrl'],
        ]);
        $expectedEmailsSent = [
        ];
        $this->assertEquals($expectedEmailsSent, $actualEmailsSent, 'Emails sent');

        $this->assertRedirect();
        $this->assertEquals('', $this->controller->getFlash('success'), 'Success flash message');
        $this->assertEquals('Unable to cancel order "#SE0012508"', $this->controller->getFlash('error'), 'Error flash message');
    }

    protected function seedUncapturedOrderForMarkDelivered($id): void
    {
        $this->controller->Order->save([
            'id' => $id,
            'payment_status' => OrderPaymentStatus::AUTHORIZED,
            'payment_captured_at' => null,
            'balance_transaction_id' => '',
        ]);

        $this->assertEmpty($this->controller->Order->validationErrors, json_encode(['seedPaymentStatus_validationErrors' => $this->controller->Order->validationErrors]));

        $this->controller->Order->clear();
    }

    protected function mockOrderFindForPopupForVerificationCode($id, $captured): PHPUnit_Framework_MockObject_MockObject
    {
        /** @var Order|PHPUnit_Framework_MockObject_MockObject $mock */
        $mock = $this->controller->Order;

        $mock->expects($this->any())->method('findForPopup')
            ->with($id)
            ->willReturn([
                'Order' => [
                    'id' => $id,
                    'user_id' => static::USER_ID_BRAND,
                    'retailer_id' => static::USER_ID_RETAILER,
                    'orderID' => '#SE0012502',
                    'order_type' => OrderType::IN_STORE_PICKUP,
                    'is_commission_retailer' => false,
                    'verification_image_url' => null,
                    'payment_status' => ($captured) ? OrderPaymentStatus::PAID : OrderPaymentStatus::AUTHORIZED,
                    'payment_method' => OrderPaymentMethod::STRIPE,
                    'total_price' => '3519.51',
                    'total_discount' => '800.00',
                    'transactionID' => 'ch_1GO6lpLBFYPhk3KpV3E0f65w',
                    'stripe_account' => 'acct_17FVVxLBFYPhk3Kp',
                    'shipearlyFees' => '145.93',
                ],
            ]);

        return $mock;
    }

    protected function mockUploadVerificationCodeImage($id, $verificationImageFile, $expectedUrl): PHPUnit_Framework_MockObject_MockObject
    {
        /** @var UploadComponent|PHPUnit_Framework_MockObject_MockObject $mock */
        $mock = $this->controller->Upload;

        $mock->expects($this->any())->method('replaceFileInUserHash')
            ->with(
                null,
                $verificationImageFile,
                static::USER_UUID_BRAND,
                'verification',
                'order_' . $id,
                null,
                ['jpg', 'jpeg', 'gif', 'png']
            )
            ->willReturn($expectedUrl);

        return $mock;
    }

    protected function mockCaptureStripeChargeForMarkDelivered(): PHPUnit_Framework_MockObject_MockObject
    {
        /** @var OrderLogicComponent|PHPUnit_Framework_MockObject_MockObject $mock */
        $mock = $this->controller->OrderLogic;

        $mock->expects($this->any())->method('captureStripeCharge')
            ->willReturnCallback(function($orderId, $orderFields) {
                $this->assertArraySubset([
                    'id' => $orderId,
                    'payment_status' => OrderPaymentStatus::AUTHORIZED,
                    'payment_method' => 'stripe',
                    'transactionID' => 'ch_1GO6lpLBFYPhk3KpV3E0f65w',
                ], $orderFields);

                return (bool)$this->controller->Order->save([
                    'id' => $orderId,
                    'payment_status' => OrderPaymentStatus::PAID,
                    'payment_captured_at' => static::date(),
                    'balance_transaction_id' => 'txn_fake',
                    'stripe_fees' => '80.00',
                ]);
            });

        return $mock;
    }

    protected function findOrderMarkedAsDeliveredAssertion($id): array
    {
        return $this->controller->Order->findById($id, [
            'id',
            'order_status',
            'fulfillment_status',
            'deliveryDate',
            'verification_image_url',
            'payment_status',
            'payment_captured_at',
            'balance_transaction_id',
            'stripe_fees',
        ], null, -1)['Order'];
    }
}
