<?php
App::uses('IntegrationTestCase', 'TestSuite');
App::uses('ProductsC<PERSON>roller', 'Controller');
App::uses('DiscountProvider', 'Test/Provider');

/**
 * Products::index_retailer Test Case.
 *
 * Run with the following console command:
 *
 * ./app/Console/cake test app Controller/Products/ProductsIndexRetailer --stderr
 *
 * @property ProductsController|PHPUnit_Framework_MockObject_MockObject $controller
 */
class ProductsIndexRetailerTest extends IntegrationTestCase
{
    const USER_ID_BRAND = 8;
    const USER_ID_RETAILER = 7;
    const USER_ID_BRANCH = 17;

    public $fixtures = [
        'app.b2b_cart',
        'app.b2b_cart_product',
        'app.b2b_ship_to_address',
        'app.brand_staff',
        'app.brand_staff_permission',
        'app.category',
        'app.collection',
        'app.collections_product',
        'app.configuration',
        'app.contactpersons',
        'app.discount',
        'app.discount_rule',
        'app.fulfillment_product',
        'app.i18n',
        'app.inventory_transfer',
        'app.inventory_transfer_product',
        'app.manufacturer_retailer',
        'app.manufacturer_sales_rep',
        'app.order',
        'app.order_product',
        'app.order_refund_product',
        'app.pricing_tier',
        'app.pricing_tiers_collection',
        'app.pricing_tiers_hidden_warehouse',
        'app.product',
        'app.product_category',
        'app.product_non_applicable_order_type',
        'app.product_price',
        'app.product_state_fee',
        'app.product_tag',
        'app.product_tier',
        'app.product_title',
        'app.product_variant_option',
        'app.retailer_credit',
        'app.retailer_credit_item',
        'app.retailer_credit_payment',
        'app.staff',
        'app.staff_permission',
        'app.state',
        'app.storefront',
        'app.tag',
        'app.user',
        'app.user_category',
        'app.user_domain',
        'app.user_setting',
        'app.user_subdomain',
        'app.variant_option',
        'app.variant_sort_order',
        'app.warehouse',
        'app.warehouse_product',
        'app.watch_product',
    ];

    public function setUp()
    {
        parent::setUp();
        $this->generate('Products', [
            // Methods called in AppController::beforeFilter that add unnecessary dependencies
            'methods' => ['_headerNotifications', '_checkCron'],
        ]);

        $this->controller->Auth->unauthorizedRedirect = false;
    }

    /**
     * @dataProvider providerIndexRetailer
     * @see ProductsController::index_retailer
     */
    public function testIndexRetailer(array $query, array $expected)
    {
        $userId = static::USER_ID_RETAILER;
        $this->controller->Auth->login(['id' => $userId]);
        $this->_mockHeaderNotifications();

        $error_reporting = error_reporting();
        error_reporting($error_reporting & ~E_USER_DEPRECATED);
        $this->get(['controller' => 'products', 'action' => 'index', '?' => $query]);
        error_reporting($error_reporting);

        $this->assertRedirect($expected);
    }

    public function providerIndexRetailer(): array
    {
        return [
            'popup_regular_new' => [
                'query' => ['user_id' => '8', 'branch_id' => '17', 'orderType' => 'regular'],
                'expected' => [
                    'controller' => 'products',
                    'action' => 'index_retailer',
                    'retailer_id' => '17',
                    '?' => ['user_id' => '8', 'orderType' => 'regular'],
                ],
            ],
            'popup_booking_new' => [
                'query' => ['user_id' => '8', 'branch_id' => '17', 'discountId' => '2', 'orderType' => 'booking'],
                'expected' => [
                    'controller' => 'products',
                    'action' => 'index_retailer',
                    'retailer_id' => '17',
                    '?' => ['user_id' => '8', 'discountId' => '2', 'orderType' => 'booking'],
                ],
            ],
            'popup_regular_cart' => [
                'query' => ['user_id' => '8', 'branch_id' => '7', 'cartId' => '1'],
                'expected' => [
                    'controller' => 'products',
                    'action' => 'index_retailer',
                    'retailer_id' => '7',
                    '?' => ['user_id' => '8', 'cartId' => '1'],
                ],
            ],
            'popup_booking_cart' => [
                'query' => ['user_id' => '8', 'branch_id' => '7', 'cartId' => '3'],
                'expected' => [
                    'controller' => 'products',
                    'action' => 'index_retailer',
                    'retailer_id' => '7',
                    '?' => ['user_id' => '8', 'cartId' => '3'],
                ],
            ],
        ];
    }

    /**
     * @dataProvider providerB2bCatalogue
     * @see ProductsController::index_retailer
     */
    public function testB2bCatalogue($userId, $retailerId, array $query, array $expected)
    {
        $this->controller->Auth->login(['id' => $userId]);
        $this->_mockHeaderNotifications();

        $this->get(['controller' => 'products', 'action' => 'index_retailer', 'retailer_id' => $retailerId, '?' => $query]);

        $actual = [
            'query' => $this->controller->request->query,
            'viewVars' => array_diff_key($this->controller->viewVars, array_flip([
                'shipearly_user',
                'subdomain_settings',
                'lang',
                'shipearly_logo_url',
                'b2b_cart_count',
            ])),
        ];
        $this->assertEquals($expected, $actual);
        $this->assertResponseOk();
    }

    public function providerB2bCatalogue(): array
    {
        return [
            'retailer_popup_regular_new' => [
                'user_id' => static::USER_ID_RETAILER,
                'retailer_id' => '17',
                'query' => ['user_id' => '8', 'orderType' => 'regular'],
                'expected' => [
                    'query' => [
                        'user_id' => '8',
                        'orderType' => 'regular',
                        'display' => 'grid',
                        'sort' => 'Product.sort_order',
                        'order' => 'ASC',
                        'page' => 1,
                        'show' => 12,
                        'warehouse_id' => 2,
                    ],
                    'viewVars' => [
                        'all_products' => [
                            0 => [
                                'Product' => [
                                    'id' => '21',
                                    'uuid' => '56e33952-2348-4c1e-9a34-109b91c2de43',
                                    'user_id' => '8',
                                    'source_product_id' => '5572298951',
                                    'productID' => '17532934023',
                                    'product_sku' => 'BICYCLE-1',
                                    'product_title' => 'Super Bike - Red',
                                    'product_upc' => '9090909090909',
                                    'product_image' => 'https://cdn.shopify.com/s/files/1/1205/5522/products/RedBike.jpg?v=1513192497',
                                    'product_price' => '1200.00',
                                    'b2b_min_order_quantity' => null,
                                    'b2b_max_order_quantity' => null,
                                    'product_name' => 'Super Bike',
                                    'brand_inventory' => '483',
                                    'dealer_price' => '499.99',
                                    'can_add_to_cart' => '1',
                                    'count_instock' => '3',
                                    'count_total' => '3',
                                    'lowest_price' => '375.00',
                                    'lowest_price_currency' => 'CAD',
                                    'lowest_retail_price' => '1349.9900',
                                    'lowest_b2b_min_order_quantity' => '0',
                                    'highest_b2b_max_order_quantity' => '0',
                                    'lowest_price_discount' => [],
                                    'lowest_retail_price_currency' => 'CAD',
                                    'highest_price' => '499.99',
                                    'highest_retail_price' => '1349.9900',
                                    'highest_price_discount' => [],
                                    'lowest_margin' => '-24.75',
                                    'highest_margin' => '62.96',
                                ],
                                'User' => [
                                    'id' => '8',
                                    'company_name' => 'My Local Brand',
                                    'currency_code' => 'USD',
                                    'enable_b2b_cart' => true,
                                ],
                                'ManufacturerRetailer' => [
                                    'id' => '18',
                                    'warehouse_id' => null,
                                ],
                                'PricingTier' => [
                                    'currencytype' => 'CAD',
                                ],
                                'Collection' => [
                                    0 => ['id' => '1', 'title' => 'Home page'],
                                ],
                                'Tag' => [
                                    0 => ['name' => 'Bike'],
                                ],
                            ],
                        ],
                        'paging' => [
                            'offset' => 0,
                            'rowsPerPage' => 12,
                            'pageNum' => 1,
                            'last' => 1.0,
                            'nav' => [0 => ['pageNum' => 1]],
                        ],
                        'count_products' => 1,
                        'selectedWarehouseId' => '2',
                        'warehouseOptions' => [
                            2 => 'East Coast',
                        ],
                        'stockOptions' => [
                            0 => 'All Products',
                            1 => 'In Stock Only',
                        ],
                        'variantOptionsByName' => [
                            'color' => [
                                'name' => 'Color',
                                'values' => [
                                    'blue' => 'Blue',
                                    'red' => 'Red',
                                    'yellow' => 'Yellow',
                                ],
                            ],
                        ],
                        'productTypeOptions' => [
                            'Bikes' => 'Bikes',
                        ],
                        'collectionOptions' => [
                            1 => 'Home page',
                        ],
                        'collectionImages' => [
                            [
                                'Collection' => [
                                    'id' => '1',
                                    'title' => 'Home page',
                                    'image_url' => null,
                                    'locale' => 'eng',
                                ],
                            ],
                        ],
                        'selectedCollectionTitle' => '',
                        'selectedCollectionId' => null,
                        'orderType' => 'regular',
                        'discountId' => null,
                        'cartId' => null,
                        'existingCarts' => [
                            0 => [
                                'B2bCart' => [
                                    'id' => '4',
                                    'order_type' => 'regular',
                                    'retailer_id' => '7',
                                ],
                                'Discount' => [
                                    'id' => null,
                                    'code' => null,
                                    'description' => null,
                                    'is_b2b_discount' => null,
                                    'b2b_hide_ineligible_products' => null,
                                    'retailer_option' => null,
                                    'retailer_values' => null,
                                    'enable_free_freight' => null,
                                ],
                                'B2bCartProduct' => [
                                    0 => ['id' => '7', 'b2b_cart_id' => '4', 'quantity' => '2'],
                                    1 => ['id' => '8', 'b2b_cart_id' => '4', 'quantity' => '2'],
                                ],
                            ],
                        ],
                        'selectedBrandId' => '8',
                        'noRecords' => 12,
                        'title_for_layout' => 'Products',
                        'has_b2b_cart_permission' => true,
                        'isConsumerView' => false,
                        'isRetailer' => true,
                        'locations' => [
                            7 => 'Local Shop',
                            17 => 'Local Shop Branch'
                        ],
                        'discountTypes' => [
                            [
                                'Discount' => [
                                    'id' => '2',
                                    'code' => 'B2BFORDAYS',
                                    'description' => '25.00% off variant (BICYCLE-1)',
                                    'b2b_discount_type' => 'booking',
                                    'retailer_option' => 'tiers',
                                    'retailer_values' => '1',
                                ],
                            ],
                        ],
                        'storefront' => [],
                        'visibleProductUuids' => [
                            '56e33952-f918-4ecd-b6b5-109b91c2de43' => '56e33952-f918-4ecd-b6b5-109b91c2de43',
                            '56e33952-2348-4c1e-9a34-109b91c2de43' => '56e33952-2348-4c1e-9a34-109b91c2de43',
                            '571f796b-2410-4ce7-8e86-0fd791c2de43' => '571f796b-2410-4ce7-8e86-0fd791c2de43',
                            '574c39c4-39dc-4853-a6ea-0ab891c2de43' => '574c39c4-39dc-4853-a6ea-0ab891c2de43',
                        ],
                        'shippingData' => [
                            'unitThreshold' => 0,
                            'priceThreshold' => 0.0,
                            'unitBasedRateOption' => '',
                            'priceBasedRateOption' => '',
                            'shippingFilters' => [],
                            'freeShippingMessages' => [
                                'initial' => '',
                                'progress' => '',
                                'success' => 'Congratulations! You’ve Earned Free Shipping'
                            ],
                        ],
                    ],
                ],
            ],
            'retailer_popup_booking_new' => [
                'user_id' => static::USER_ID_RETAILER,
                'retailer_id' => '17',
                'query' => ['user_id' => '8', 'discountId' => '2', 'orderType' => 'booking'],
                'expected' => [
                    'query' => [
                        'user_id' => '8',
                        'discountId' => '2',
                        'orderType' => 'booking',
                        'display' => 'grid',
                        'sort' => 'Product.sort_order',
                        'order' => 'ASC',
                        'page' => 1,
                        'show' => 12,
                        'warehouse_id' => 2,
                    ],
                    'viewVars' => [
                        'all_products' => [
                            0 => [
                                'Product' => [
                                    'id' => '21',
                                    'uuid' => '56e33952-2348-4c1e-9a34-109b91c2de43',
                                    'user_id' => '8',
                                    'source_product_id' => '5572298951',
                                    'productID' => '17532934023',
                                    'product_sku' => 'BICYCLE-1',
                                    'product_title' => 'Super Bike - Red',
                                    'product_upc' => '9090909090909',
                                    'product_image' => 'https://cdn.shopify.com/s/files/1/1205/5522/products/RedBike.jpg?v=1513192497',
                                    'product_price' => '1200.00',
                                    'b2b_min_order_quantity' => null,
                                    'b2b_max_order_quantity' => null,
                                    'product_name' => 'Super Bike',
                                    'brand_inventory' => '483',
                                    'dealer_price' => '499.99',
                                    'can_add_to_cart' => '1',
                                    'count_instock' => '3',
                                    'count_total' => '3',
                                    'lowest_price' => '499.99',
                                    'lowest_price_currency' => 'CAD',
                                    'lowest_retail_price' => '1349.9900',
                                    'lowest_b2b_min_order_quantity' => '0',
                                    'highest_b2b_max_order_quantity' => '0',
                                    'lowest_price_discount' => [
                                        'id' => '21',
                                        'discount_amount' => '125.00',
                                        'discount_quantity' => 1,
                                        'retailers' => [
                                            0 => '7',
                                            1 => '17',
                                        ],
                                        'is_product_discount' => true,
                                    ],
                                    'lowest_retail_price_currency' => 'CAD',
                                    'highest_price' => '499.00',
                                    'highest_retail_price' => '1349.9900',
                                    'highest_price_discount' => [],
                                    'lowest_margin' => '-24.75',
                                    'highest_margin' => '68.75',
                                ],
                                'User' => [
                                    'id' => '8',
                                    'company_name' => 'My Local Brand',
                                    'currency_code' => 'USD',
                                    'enable_b2b_cart' => true,
                                ],
                                'ManufacturerRetailer' => [
                                    'id' => '18',
                                    'warehouse_id' => null,
                                ],
                                'PricingTier' => [
                                    'currencytype' => 'CAD',
                                ],
                                'Collection' => [
                                    0 => ['id' => '1', 'title' => 'Home page'],
                                ],
                                'Tag' => [
                                    0 => ['name' => 'Bike'],
                                ],
                            ],
                        ],
                        'paging' => [
                            'offset' => 0,
                            'rowsPerPage' => 12,
                            'pageNum' => 1,
                            'last' => 1.0,
                            'nav' => [0 => ['pageNum' => 1]],
                        ],
                        'count_products' => 1,
                        'selectedWarehouseId' => '2',
                        'warehouseOptions' => [
                            2 => 'East Coast',
                        ],
                        'stockOptions' => [
                            0 => 'All Products',
                            1 => 'In Stock Only',
                        ],
                        'variantOptionsByName' => [
                            'color' => [
                                'name' => 'Color',
                                'values' => [
                                    'blue' => 'Blue',
                                    'red' => 'Red',
                                    'yellow' => 'Yellow',
                                ],
                            ],
                            'material' => [
                                'name' => 'Material',
                                'values' => [
                                    'gold' => 'Gold',
                                ],
                            ],
                        ],
                        'productTypeOptions' => [
                            'Accessories' => 'Accessories',
                            'Bikes' => 'Bikes',
                        ],
                        'collectionOptions' => [
                            1 => 'Home page',
                        ],
                        'collectionImages' => [
                            [
                                'Collection' => [
                                    'id' => '1',
                                    'title' => 'Home page',
                                    'image_url' => null,
                                    'locale' => 'eng',
                                ],
                            ],
                        ],
                        'selectedCollectionTitle' => '',
                        'selectedCollectionId' => null,
                        'orderType' => 'booking',
                        'discountId' => '2',
                        'cartId' => null,
                        'existingCarts' => [
                            0 => [
                                'B2bCart' => [
                                    'id' => '4',
                                    'order_type' => 'regular',
                                    'retailer_id' => '7',
                                ],
                                'Discount' => [
                                    'id' => null,
                                    'code' => null,
                                    'description' => null,
                                    'is_b2b_discount' => null,
                                    'b2b_hide_ineligible_products' => null,
                                    'retailer_option' => null,
                                    'retailer_values' => null,
                                    'enable_free_freight' => null,
                                ],
                                'B2bCartProduct' => [
                                    0 => [
                                        'id' => '7',
                                        'b2b_cart_id' => '4',
                                        'quantity' => '2',
                                    ],
                                    1 => [
                                        'id' => '8',
                                        'b2b_cart_id' => '4',
                                        'quantity' => '2',
                                    ],
                                ],
                            ],
                        ],
                        'selectedBrandId' => '8',
                        'noRecords' => 12,
                        'title_for_layout' => 'Products',
                        'has_b2b_cart_permission' => true,
                        'isConsumerView' => false,
                        'isRetailer' => true,
                        'locations' => [
                            7 => 'Local Shop',
                            17 => 'Local Shop Branch'
                        ],
                        'discountTypes' => [
                            [
                                'Discount' => [
                                    'id' => '2',
                                    'code' => 'B2BFORDAYS',
                                    'description' => '25.00% off variant (BICYCLE-1)',
                                    'b2b_discount_type' => 'booking',
                                    'retailer_option' => 'tiers',
                                    'retailer_values' => '1',
                                ],
                            ],
                        ],
                        'storefront' => [],
                        'visibleProductUuids' => [
                            '56e33952-f918-4ecd-b6b5-109b91c2de43' => '56e33952-f918-4ecd-b6b5-109b91c2de43',
                            '56e33952-2348-4c1e-9a34-109b91c2de43' => '56e33952-2348-4c1e-9a34-109b91c2de43',
                            '571f796b-2410-4ce7-8e86-0fd791c2de43' => '571f796b-2410-4ce7-8e86-0fd791c2de43',
                            '574c39c4-39dc-4853-a6ea-0ab891c2de43' => '574c39c4-39dc-4853-a6ea-0ab891c2de43',
                        ],
                        'shippingData' => [
                            'unitThreshold' => 0,
                            'priceThreshold' => 0.0,
                            'unitBasedRateOption' => '',
                            'priceBasedRateOption' => '',
                            'shippingFilters' => [],
                            'freeShippingMessages' => [
                                'initial' => '',
                                'progress' => '',
                                'success' => 'Congratulations! You’ve Earned Free Shipping'
                            ],
                        ],
                    ],
                ],
            ],
            'retailer_popup_regular_cart' => [
                'user_id' => static::USER_ID_RETAILER,
                'retailer_id' => '7',
                'query' => ['user_id' => '8', 'cartId' => '1'],
                'expected' => [
                    'query' => [
                        'user_id' => '8',
                        'cartId' => '1',
                        'display' => 'grid',
                        'sort' => 'Product.sort_order',
                        'order' => 'ASC',
                        'page' => 1,
                        'show' => 12,
                        'warehouse_id' => 2,
                    ],
                    'viewVars' => [
                        'all_products' => [
                            0 => [
                                'Product' => [
                                    'id' => '21',
                                    'uuid' => '56e33952-2348-4c1e-9a34-109b91c2de43',
                                    'user_id' => '8',
                                    'source_product_id' => '5572298951',
                                    'productID' => '17532934023',
                                    'product_sku' => 'BICYCLE-1',
                                    'product_title' => 'Super Bike - Red',
                                    'product_upc' => '9090909090909',
                                    'product_image' => 'https://cdn.shopify.com/s/files/1/1205/5522/products/RedBike.jpg?v=1513192497',
                                    'product_price' => '1200.00',
                                    'b2b_min_order_quantity' => null,
                                    'b2b_max_order_quantity' => null,
                                    'product_name' => 'Super Bike',
                                    'brand_inventory' => '483',
                                    'dealer_price' => '499.99',
                                    'can_add_to_cart' => '1',
                                    'count_instock' => '3',
                                    'count_total' => '3',
                                    'lowest_price' => '375.00',
                                    'lowest_price_currency' => 'CAD',
                                    'lowest_retail_price' => '1349.9900',
                                    'lowest_b2b_min_order_quantity' => '0',
                                    'highest_b2b_max_order_quantity' => '0',
                                    'lowest_price_discount' => [],
                                    'lowest_retail_price_currency' => 'CAD',
                                    'highest_price' => '499.99',
                                    'highest_retail_price' => '1349.9900',
                                    'highest_price_discount' => [],
                                    'lowest_margin' => '-24.75',
                                    'highest_margin' => '62.96',
                                ],
                                'User' => [
                                    'id' => '8',
                                    'company_name' => 'My Local Brand',
                                    'currency_code' => 'USD',
                                    'enable_b2b_cart' => true,
                                ],
                                'ManufacturerRetailer' => [
                                    'id' => '3',
                                    'warehouse_id' => null,
                                ],
                                'PricingTier' => [
                                    'currencytype' => 'CAD',
                                ],
                                'Collection' => [
                                    0 => ['id' => '1', 'title' => 'Home page'],
                                ],
                                'Tag' => [
                                    0 => ['name' => 'Bike'],
                                ],
                            ],
                        ],
                        'paging' => [
                            'offset' => 0,
                            'rowsPerPage' => 12,
                            'pageNum' => 1,
                            'last' => 1.0,
                            'nav' => [0 => ['pageNum' => 1]],
                        ],
                        'count_products' => 1,
                        'selectedWarehouseId' => '2',
                        'warehouseOptions' => [
                            2 => 'East Coast',
                        ],
                        'stockOptions' => [
                            0 => 'All Products',
                            1 => 'In Stock Only',
                        ],
                        'variantOptionsByName' => [
                            'color' => [
                                'name' => 'Color',
                                'values' => [
                                    'blue' => 'Blue',
                                    'red' => 'Red',
                                    'yellow' => 'Yellow',
                                ],
                            ],
                        ],
                        'productTypeOptions' => [
                            'Bikes' => 'Bikes',
                        ],
                        'collectionOptions' => [
                            1 => 'Home page',
                        ],
                        'collectionImages' => [
                            [
                                'Collection' => [
                                    'id' => '1',
                                    'title' => 'Home page',
                                    'image_url' => null,
                                    'locale' => 'eng',
                                ],
                            ],
                        ],
                        'selectedCollectionTitle' => '',
                        'selectedCollectionId' => null,
                        'orderType' => 'regular',
                        'discountId' => null,
                        'cartId' => '1',
                        'existingCarts' => [
                            0 => [
                                'B2bCart' => [
                                    'id' => '3',
                                    'order_type' => 'booking',
                                    'retailer_id' => '7',
                                ],
                                'Discount' => DiscountProvider::defaultB2bCatalogueDiscountInfo(),
                                'B2bCartProduct' => [
                                    0 => [
                                        'id' => '5',
                                        'b2b_cart_id' => '3',
                                        'quantity' => '2',
                                    ],
                                    1 => [
                                        'id' => '6',
                                        'b2b_cart_id' => '3',
                                        'quantity' => '2',
                                    ],
                                ],
                            ],
                            1 => [
                                'B2bCart' => [
                                    'id' => '1',
                                    'order_type' => 'regular',
                                    'retailer_id' => '7',
                                ],
                                'Discount' => [
                                    'id' => null,
                                    'code' => null,
                                    'description' => null,
                                    'is_b2b_discount' => null,
                                    'b2b_hide_ineligible_products' => null,
                                    'retailer_option' => null,
                                    'retailer_values' => null,
                                    'enable_free_freight' => null,
                                ],
                                'B2bCartProduct' => [
                                    0 => [
                                        'id' => '1',
                                        'b2b_cart_id' => '1',
                                        'quantity' => '2',
                                    ],
                                    1 => [
                                        'id' => '2',
                                        'b2b_cart_id' => '1',
                                        'quantity' => '2',
                                    ],
                                ],
                            ],
                        ],
                        'selectedBrandId' => '8',
                        'noRecords' => 12,
                        'title_for_layout' => 'Products',
                        'has_b2b_cart_permission' => true,
                        'isConsumerView' => false,
                        'isRetailer' => true,
                        'locations' => [
                            7 => 'Local Shop',
                            17 => 'Local Shop Branch'
                        ],
                        'discountTypes' => [
                            [
                                'Discount' => [
                                    'id' => '2',
                                    'code' => 'B2BFORDAYS',
                                    'description' => '25.00% off variant (BICYCLE-1)',
                                    'b2b_discount_type' => 'booking',
                                    'retailer_option' => 'tiers',
                                    'retailer_values' => '1',
                                ],
                            ],
                        ],
                        'storefront' => [],
                        'visibleProductUuids' => [
                            '56e33952-f918-4ecd-b6b5-109b91c2de43' => '56e33952-f918-4ecd-b6b5-109b91c2de43',
                            '56e33952-2348-4c1e-9a34-109b91c2de43' => '56e33952-2348-4c1e-9a34-109b91c2de43',
                            '571f796b-2410-4ce7-8e86-0fd791c2de43' => '571f796b-2410-4ce7-8e86-0fd791c2de43',
                            '574c39c4-39dc-4853-a6ea-0ab891c2de43' => '574c39c4-39dc-4853-a6ea-0ab891c2de43',
                        ],
                        'shippingData' => [
                            'unitThreshold' => 0,
                            'priceThreshold' => 0.0,
                            'unitBasedRateOption' => '',
                            'priceBasedRateOption' => '',
                            'shippingFilters' => [],
                            'freeShippingMessages' => [
                                'initial' => '',
                                'progress' => '',
                                'success' => 'Congratulations! You’ve Earned Free Shipping'
                            ],
                        ],
                    ],
                ],
            ],
            'retailer_popup_booking_cart' => [
                'user_id' => static::USER_ID_RETAILER,
                'retailer_id' => '7',
                'query' => ['user_id' => '8', 'cartId' => '3'],
                'expected' => [
                    'query' => [
                        'user_id' => '8',
                        'cartId' => '3',
                        'display' => 'grid',
                        'sort' => 'Product.sort_order',
                        'order' => 'ASC',
                        'page' => 1,
                        'show' => 12,
                        'warehouse_id' => 2,
                    ],
                    'viewVars' => [
                        'all_products' => [
                            0 => [
                                'Product' => [
                                    'id' => '21',
                                    'uuid' => '56e33952-2348-4c1e-9a34-109b91c2de43',
                                    'user_id' => '8',
                                    'source_product_id' => '5572298951',
                                    'productID' => '17532934023',
                                    'product_sku' => 'BICYCLE-1',
                                    'product_title' => 'Super Bike - Red',
                                    'product_upc' => '9090909090909',
                                    'product_image' => 'https://cdn.shopify.com/s/files/1/1205/5522/products/RedBike.jpg?v=1513192497',
                                    'product_price' => '1200.00',
                                    'b2b_min_order_quantity' => null,
                                    'b2b_max_order_quantity' => null,
                                    'product_name' => 'Super Bike',
                                    'brand_inventory' => '483',
                                    'dealer_price' => '499.99',
                                    'can_add_to_cart' => '1',
                                    'count_instock' => '3',
                                    'count_total' => '3',
                                    'lowest_price' => '499.99',
                                    'lowest_price_currency' => 'CAD',
                                    'lowest_retail_price' => '1349.9900',
                                    'lowest_b2b_min_order_quantity' => '0',
                                    'highest_b2b_max_order_quantity' => '0',
                                    'lowest_price_discount' => [
                                        'id' => '21',
                                        'discount_amount' => '125.00',
                                        'discount_quantity' => 1,
                                        'retailers' => [
                                            0 => '7',
                                            1 => '17',
                                        ],
                                        'is_product_discount' => true,
                                    ],
                                    'lowest_retail_price_currency' => 'CAD',
                                    'highest_price' => '499.00',
                                    'highest_retail_price' => '1349.9900',
                                    'highest_price_discount' => [],
                                    'lowest_margin' => '-24.75',
                                    'highest_margin' => '68.75',
                                ],
                                'User' => [
                                    'id' => '8',
                                    'company_name' => 'My Local Brand',
                                    'currency_code' => 'USD',
                                    'enable_b2b_cart' => true,
                                ],
                                'ManufacturerRetailer' => [
                                    'id' => '3',
                                    'warehouse_id' => null,
                                ],
                                'PricingTier' => [
                                    'currencytype' => 'CAD',
                                ],
                                'Collection' => [
                                    0 => ['id' => '1', 'title' => 'Home page'],
                                ],
                                'Tag' => [
                                    0 => ['name' => 'Bike'],
                                ],
                            ],
                        ],
                        'paging' => [
                            'offset' => 0,
                            'rowsPerPage' => 12,
                            'pageNum' => 1,
                            'last' => 1.0,
                            'nav' => [0 => ['pageNum' => 1]],
                        ],
                        'count_products' => 1,
                        'selectedWarehouseId' => '2',
                        'warehouseOptions' => [
                            2 => 'East Coast',
                        ],
                        'stockOptions' => [
                            0 => 'All Products',
                            1 => 'In Stock Only',
                        ],
                        'variantOptionsByName' => [
                            'color' => [
                                'name' => 'Color',
                                'values' => [
                                    'blue' => 'Blue',
                                    'red' => 'Red',
                                    'yellow' => 'Yellow',
                                ],
                            ],
                            'material' => [
                                'name' => 'Material',
                                'values' => [
                                    'gold' => 'Gold',
                                ],
                            ],
                        ],
                        'productTypeOptions' => [
                            'Accessories' => 'Accessories',
                            'Bikes' => 'Bikes',
                        ],
                        'collectionOptions' => [
                            1 => 'Home page',
                        ],
                        'collectionImages' => [
                            [
                                'Collection' => [
                                    'id' => '1',
                                    'title' => 'Home page',
                                    'image_url' => null,
                                    'locale' => 'eng',
                                ],
                            ],
                        ],
                        'selectedCollectionTitle' => '',
                        'selectedCollectionId' => null,
                        'orderType' => 'booking',
                        'discountId' => '2',
                        'cartId' => '3',
                        'existingCarts' => [
                            0 => [
                                'B2bCart' => [
                                    'id' => '3',
                                    'order_type' => 'booking',
                                    'retailer_id' => '7',
                                ],
                                'Discount' => DiscountProvider::defaultB2bCatalogueDiscountInfo(),
                                'B2bCartProduct' => [
                                    0 => [
                                        'id' => '5',
                                        'b2b_cart_id' => '3',
                                        'quantity' => '2',
                                    ],
                                    1 => [
                                        'id' => '6',
                                        'b2b_cart_id' => '3',
                                        'quantity' => '2',
                                    ],
                                ],
                            ],
                            1 => [
                                'B2bCart' => [
                                    'id' => '1',
                                    'order_type' => 'regular',
                                    'retailer_id' => '7',
                                ],
                                'Discount' => [
                                    'id' => null,
                                    'code' => null,
                                    'description' => null,
                                    'is_b2b_discount' => null,
                                    'b2b_hide_ineligible_products' => null,
                                    'retailer_option' => null,
                                    'retailer_values' => null,
                                    'enable_free_freight' => null,
                                ],
                                'B2bCartProduct' => [
                                    0 => [
                                        'id' => '1',
                                        'b2b_cart_id' => '1',
                                        'quantity' => '2',
                                    ],
                                    1 => [
                                        'id' => '2',
                                        'b2b_cart_id' => '1',
                                        'quantity' => '2',
                                    ],
                                ],
                            ],
                        ],
                        'selectedBrandId' => '8',
                        'noRecords' => 12,
                        'title_for_layout' => 'Products',
                        'has_b2b_cart_permission' => true,
                        'isConsumerView' => false,
                        'isRetailer' => true,
                        'locations' => [
                            7 => 'Local Shop',
                            17 => 'Local Shop Branch'
                        ],
                        'discountTypes' => [
                            [
                                'Discount' => [
                                    'id' => '2',
                                    'code' => 'B2BFORDAYS',
                                    'description' => '25.00% off variant (BICYCLE-1)',
                                    'b2b_discount_type' => 'booking',
                                    'retailer_option' => 'tiers',
                                    'retailer_values' => '1',
                                ],
                            ],
                        ],
                        'storefront' => [],
                        'visibleProductUuids' => [
                            '56e33952-f918-4ecd-b6b5-109b91c2de43' => '56e33952-f918-4ecd-b6b5-109b91c2de43',
                            '56e33952-2348-4c1e-9a34-109b91c2de43' => '56e33952-2348-4c1e-9a34-109b91c2de43',
                            '571f796b-2410-4ce7-8e86-0fd791c2de43' => '571f796b-2410-4ce7-8e86-0fd791c2de43',
                            '574c39c4-39dc-4853-a6ea-0ab891c2de43' => '574c39c4-39dc-4853-a6ea-0ab891c2de43',
                        ],
                        'shippingData' => [
                            'unitThreshold' => 0,
                            'priceThreshold' => 0.0,
                            'unitBasedRateOption' => '',
                            'priceBasedRateOption' => '',
                            'shippingFilters' => [],
                            'freeShippingMessages' => [
                                'initial' => '',
                                'progress' => '',
                                'success' => 'Congratulations! You’ve Earned Free Shipping'
                            ],
                        ],
                    ],
                ],
            ],
            'brand_popup_regular_new' => [
                'user_id' => static::USER_ID_BRAND,
                'retailer_id' => '17',
                'query' => ['orderType' => 'regular'],
                'expected' => [
                    'query' => [
                        'orderType' => 'regular',
                        'display' => 'grid',
                        'sort' => 'Product.sort_order',
                        'order' => 'ASC',
                        'page' => 1,
                        'show' => 12,
                        'warehouse_id' => '2',
                    ],
                    'viewVars' => [
                        'all_products' => [
                            0 => [
                                'Product' => [
                                    'id' => '21',
                                    'uuid' => '56e33952-2348-4c1e-9a34-109b91c2de43',
                                    'user_id' => '8',
                                    'source_product_id' => '5572298951',
                                    'productID' => '17532934023',
                                    'product_sku' => 'BICYCLE-1',
                                    'product_title' => 'Super Bike - Red',
                                    'product_upc' => '9090909090909',
                                    'product_image' => 'https://cdn.shopify.com/s/files/1/1205/5522/products/RedBike.jpg?v=1513192497',
                                    'product_price' => '1200.00',
                                    'b2b_min_order_quantity' => null,
                                    'b2b_max_order_quantity' => null,
                                    'product_name' => 'Super Bike',
                                    'brand_inventory' => '483',
                                    'dealer_price' => '499.99',
                                    'can_add_to_cart' => '1',
                                    'count_instock' => '3',
                                    'count_total' => '3',
                                    'lowest_price' => '375.00',
                                    'lowest_price_currency' => 'CAD',
                                    'lowest_retail_price' => '1349.9900',
                                    'lowest_b2b_min_order_quantity' => '0',
                                    'highest_b2b_max_order_quantity' => '0',
                                    'lowest_price_discount' => [],
                                    'lowest_retail_price_currency' => 'CAD',
                                    'highest_price' => '499.99',
                                    'highest_retail_price' => '1349.9900',
                                    'highest_price_discount' => [],
                                    'lowest_margin' => '-24.75',
                                    'highest_margin' => '62.96',
                                ],
                                'User' => [
                                    'id' => '8',
                                    'company_name' => 'My Local Brand',
                                    'currency_code' => 'USD',
                                    'enable_b2b_cart' => true,
                                ],
                                'ManufacturerRetailer' => [
                                    'id' => '18',
                                    'warehouse_id' => null,
                                ],
                                'PricingTier' => [
                                    'currencytype' => 'CAD',
                                ],
                                'Collection' => [
                                    0 => ['id' => '1', 'title' => 'Home page'],
                                ],
                                'Tag' => [
                                    0 => ['name' => 'Bike'],
                                ],
                            ],
                        ],
                        'paging' => [
                            'offset' => 0,
                            'rowsPerPage' => 12,
                            'pageNum' => 1,
                            'last' => 1.0,
                            'nav' => [0 => ['pageNum' => 1]],
                        ],
                        'count_products' => 1,
                        'selectedWarehouseId' => '2',
                        'warehouseOptions' => [
                            2 => 'East Coast',
                        ],
                        'stockOptions' => [
                            0 => 'All Products',
                            1 => 'In Stock Only',
                        ],
                        'variantOptionsByName' => [
                            'color' => [
                                'name' => 'Color',
                                'values' => [
                                    'blue' => 'Blue',
                                    'red' => 'Red',
                                    'yellow' => 'Yellow',
                                ],
                            ],
                        ],
                        'productTypeOptions' => [
                            'Bikes' => 'Bikes',
                        ],
                        'collectionOptions' => [
                            1 => 'Home page',
                        ],
                        'collectionImages' => [
                            [
                                'Collection' => [
                                    'id' => '1',
                                    'title' => 'Home page',
                                    'image_url' => null,
                                    'locale' => 'eng',
                                ],
                            ],
                        ],
                        'selectedCollectionTitle' => '',
                        'selectedCollectionId' => null,
                        'orderType' => 'regular',
                        'discountId' => null,
                        'cartId' => null,
                        'existingCarts' => [
                            0 => [
                                'B2bCart' => [
                                    'id' => '4',
                                    'order_type' => 'regular',
                                    'retailer_id' => '7',
                                ],
                                'Discount' => [
                                    'id' => null,
                                    'code' => null,
                                    'description' => null,
                                    'is_b2b_discount' => null,
                                    'b2b_hide_ineligible_products' => null,
                                    'retailer_option' => null,
                                    'retailer_values' => null,
                                    'enable_free_freight' => null,
                                ],
                                'B2bCartProduct' => [
                                    0 => [
                                        'id' => '7',
                                        'b2b_cart_id' => '4',
                                        'quantity' => '2',
                                    ],
                                    1 => [
                                        'id' => '8',
                                        'b2b_cart_id' => '4',
                                        'quantity' => '2',
                                    ],
                                ],
                            ],
                        ],
                        'selectedBrandId' => 8,
                        'noRecords' => 12,
                        'title_for_layout' => 'Products',
                        'has_b2b_cart_permission' => true,
                        'isConsumerView' => false,
                        'isRetailer' => false,
                        'locations' => [
                            7 => 'Local Shop',
                            17 => 'Local Shop Branch'
                        ],
                        'discountTypes' => [
                            [
                                'Discount' => [
                                    'id' => '2',
                                    'code' => 'B2BFORDAYS',
                                    'description' => '25.00% off variant (BICYCLE-1)',
                                    'b2b_discount_type' => 'booking',
                                    'retailer_option' => 'tiers',
                                    'retailer_values' => '1',
                                ],
                            ],
                        ],
                        'storefront' => [],
                        'visibleProductUuids' => [
                            '56e33952-f918-4ecd-b6b5-109b91c2de43' => '56e33952-f918-4ecd-b6b5-109b91c2de43',
                            '56e33952-2348-4c1e-9a34-109b91c2de43' => '56e33952-2348-4c1e-9a34-109b91c2de43',
                            '571f796b-2410-4ce7-8e86-0fd791c2de43' => '571f796b-2410-4ce7-8e86-0fd791c2de43',
                            '574c39c4-39dc-4853-a6ea-0ab891c2de43' => '574c39c4-39dc-4853-a6ea-0ab891c2de43',
                        ],
                        'shippingData' => [
                            'unitThreshold' => 0,
                            'priceThreshold' => 0.0,
                            'unitBasedRateOption' => '',
                            'priceBasedRateOption' => '',
                            'shippingFilters' => [],
                            'freeShippingMessages' => [
                                'initial' => '',
                                'progress' => '',
                                'success' => 'Congratulations! You’ve Earned Free Shipping'
                            ],
                        ],
                    ],
                ],
            ],
            'brand_popup_booking_new' => [
                'user_id' => static::USER_ID_BRAND,
                'retailer_id' => '17',
                'query' => ['discountId' => '2', 'orderType' => 'booking'],
                'expected' => [
                    'query' => [
                        'discountId' => '2',
                        'orderType' => 'booking',
                        'display' => 'grid',
                        'sort' => 'Product.sort_order',
                        'order' => 'ASC',
                        'page' => 1,
                        'show' => 12,
                        'warehouse_id' => '2',
                    ],
                    'viewVars' => [
                        'all_products' => [
                            0 => [
                                'Product' => [
                                    'id' => '21',
                                    'uuid' => '56e33952-2348-4c1e-9a34-109b91c2de43',
                                    'user_id' => '8',
                                    'source_product_id' => '5572298951',
                                    'productID' => '17532934023',
                                    'product_sku' => 'BICYCLE-1',
                                    'product_title' => 'Super Bike - Red',
                                    'product_upc' => '9090909090909',
                                    'product_image' => 'https://cdn.shopify.com/s/files/1/1205/5522/products/RedBike.jpg?v=1513192497',
                                    'product_price' => '1200.00',
                                    'b2b_min_order_quantity' => null,
                                    'b2b_max_order_quantity' => null,
                                    'product_name' => 'Super Bike',
                                    'brand_inventory' => '483',
                                    'dealer_price' => '499.99',
                                    'can_add_to_cart' => '1',
                                    'count_instock' => '3',
                                    'count_total' => '3',
                                    'lowest_price' => '499.99',
                                    'lowest_price_currency' => 'CAD',
                                    'lowest_retail_price' => '1349.9900',
                                    'lowest_b2b_min_order_quantity' => '0',
                                    'highest_b2b_max_order_quantity' => '0',
                                    'lowest_price_discount' => [
                                        'id' => '21',
                                        'discount_amount' => '125.00',
                                        'discount_quantity' => 1,
                                        'retailers' => [
                                            0 => '7',
                                            1 => '17',
                                        ],
                                        'is_product_discount' => true,
                                    ],
                                    'lowest_retail_price_currency' => 'CAD',
                                    'highest_price' => '499.00',
                                    'highest_retail_price' => '1349.9900',
                                    'highest_price_discount' => [],
                                    'lowest_margin' => '-24.75',
                                    'highest_margin' => '68.75',
                                ],
                                'User' => [
                                    'id' => '8',
                                    'company_name' => 'My Local Brand',
                                    'currency_code' => 'USD',
                                    'enable_b2b_cart' => true,
                                ],
                                'ManufacturerRetailer' => [
                                    'id' => '18',
                                    'warehouse_id' => null,
                                ],
                                'PricingTier' => [
                                    'currencytype' => 'CAD',
                                ],
                                'Collection' => [
                                    0 => ['id' => '1', 'title' => 'Home page'],
                                ],
                                'Tag' => [
                                    0 => ['name' => 'Bike'],
                                ],
                            ],
                        ],
                        'paging' => [
                            'offset' => 0,
                            'rowsPerPage' => 12,
                            'pageNum' => 1,
                            'last' => 1.0,
                            'nav' => [0 => ['pageNum' => 1]],
                        ],
                        'count_products' => 1,
                        'selectedWarehouseId' => '2',
                        'warehouseOptions' => [
                            2 => 'East Coast',
                        ],
                        'stockOptions' => [
                            0 => 'All Products',
                            1 => 'In Stock Only',
                        ],
                        'variantOptionsByName' => [
                            'color' => [
                                'name' => 'Color',
                                'values' => [
                                    'blue' => 'Blue',
                                    'red' => 'Red',
                                    'yellow' => 'Yellow',
                                ],
                            ],
                            'material' => [
                                'name' => 'Material',
                                'values' => [
                                    'gold' => 'Gold',
                                ],
                            ],
                        ],
                        'productTypeOptions' => [
                            'Accessories' => 'Accessories',
                            'Bikes' => 'Bikes',
                        ],
                        'collectionOptions' => [
                            1 => 'Home page',
                        ],
                        'collectionImages' => [
                            [
                                'Collection' => [
                                    'id' => '1',
                                    'title' => 'Home page',
                                    'image_url' => null,
                                    'locale' => 'eng',
                                ],
                            ],
                        ],
                        'selectedCollectionTitle' => '',
                        'selectedCollectionId' => null,
                        'orderType' => 'booking',
                        'discountId' => '2',
                        'cartId' => null,
                        'existingCarts' => [
                            0 => [
                                'B2bCart' => [
                                    'id' => '4',
                                    'order_type' => 'regular',
                                    'retailer_id' => '7',
                                ],
                                'Discount' => [
                                    'id' => null,
                                    'code' => null,
                                    'description' => null,
                                    'is_b2b_discount' => null,
                                    'b2b_hide_ineligible_products' => null,
                                    'retailer_option' => null,
                                    'retailer_values' => null,
                                    'enable_free_freight' => null,
                                ],
                                'B2bCartProduct' => [
                                    0 => [
                                        'id' => '7',
                                        'b2b_cart_id' => '4',
                                        'quantity' => '2',
                                    ],
                                    1 => [
                                        'id' => '8',
                                        'b2b_cart_id' => '4',
                                        'quantity' => '2',
                                    ],
                                ],
                            ],
                        ],
                        'selectedBrandId' => 8,
                        'noRecords' => 12,
                        'title_for_layout' => 'Products',
                        'has_b2b_cart_permission' => true,
                        'isConsumerView' => false,
                        'isRetailer' => false,
                        'locations' => [
                            7 => 'Local Shop',
                            17 => 'Local Shop Branch'
                        ],
                        'discountTypes' => [
                            [
                                'Discount' => [
                                    'id' => '2',
                                    'code' => 'B2BFORDAYS',
                                    'description' => '25.00% off variant (BICYCLE-1)',
                                    'b2b_discount_type' => 'booking',
                                    'retailer_option' => 'tiers',
                                    'retailer_values' => '1',
                                ],
                            ],
                        ],
                        'storefront' => [],
                        'visibleProductUuids' => [
                            '56e33952-f918-4ecd-b6b5-109b91c2de43' => '56e33952-f918-4ecd-b6b5-109b91c2de43',
                            '56e33952-2348-4c1e-9a34-109b91c2de43' => '56e33952-2348-4c1e-9a34-109b91c2de43',
                            '571f796b-2410-4ce7-8e86-0fd791c2de43' => '571f796b-2410-4ce7-8e86-0fd791c2de43',
                            '574c39c4-39dc-4853-a6ea-0ab891c2de43' => '574c39c4-39dc-4853-a6ea-0ab891c2de43',
                        ],
                        'shippingData' => [
                            'unitThreshold' => 0,
                            'priceThreshold' => 0.0,
                            'unitBasedRateOption' => '',
                            'priceBasedRateOption' => '',
                            'shippingFilters' => [],
                            'freeShippingMessages' => [
                                'initial' => '',
                                'progress' => '',
                                'success' => 'Congratulations! You’ve Earned Free Shipping'
                            ],
                        ],
                    ],
                ],
            ],
            'brand_popup_regular_cart' => [
                'user_id' => static::USER_ID_BRAND,
                'retailer_id' => '7',
                'query' => ['cartId' => '1'],
                'expected' => [
                    'query' => [
                        'cartId' => '1',
                        'display' => 'grid',
                        'sort' => 'Product.sort_order',
                        'order' => 'ASC',
                        'page' => 1,
                        'show' => 12,
                        'user_id' => 8,
                        'warehouse_id' => '2',
                    ],
                    'viewVars' => [
                        'all_products' => [
                            0 => [
                                'Product' => [
                                    'id' => '21',
                                    'uuid' => '56e33952-2348-4c1e-9a34-109b91c2de43',
                                    'user_id' => '8',
                                    'source_product_id' => '5572298951',
                                    'productID' => '17532934023',
                                    'product_sku' => 'BICYCLE-1',
                                    'product_title' => 'Super Bike - Red',
                                    'product_upc' => '9090909090909',
                                    'product_image' => 'https://cdn.shopify.com/s/files/1/1205/5522/products/RedBike.jpg?v=1513192497',
                                    'product_price' => '1200.00',
                                    'b2b_min_order_quantity' => null,
                                    'b2b_max_order_quantity' => null,
                                    'product_name' => 'Super Bike',
                                    'brand_inventory' => '483',
                                    'dealer_price' => '499.99',
                                    'can_add_to_cart' => '1',
                                    'count_instock' => '3',
                                    'count_total' => '3',
                                    'lowest_price' => '375.00',
                                    'lowest_price_currency' => 'CAD',
                                    'lowest_retail_price' => '1349.9900',
                                    'lowest_b2b_min_order_quantity' => '0',
                                    'highest_b2b_max_order_quantity' => '0',
                                    'lowest_price_discount' => [],
                                    'lowest_retail_price_currency' => 'CAD',
                                    'highest_price' => '499.99',
                                    'highest_retail_price' => '1349.9900',
                                    'highest_price_discount' => [],
                                    'lowest_margin' => '-24.75',
                                    'highest_margin' => '62.96',
                                ],
                                'User' => [
                                    'id' => '8',
                                    'company_name' => 'My Local Brand',
                                    'currency_code' => 'USD',
                                    'enable_b2b_cart' => true,
                                ],
                                'ManufacturerRetailer' => [
                                    'id' => '3',
                                    'warehouse_id' => null,
                                ],
                                'PricingTier' => [
                                    'currencytype' => 'CAD',
                                ],
                                'Collection' => [
                                    0 => ['id' => '1', 'title' => 'Home page'],
                                ],
                                'Tag' => [
                                    0 => ['name' => 'Bike'],
                                ],
                            ],
                        ],
                        'paging' => [
                            'offset' => 0,
                            'rowsPerPage' => 12,
                            'pageNum' => 1,
                            'last' => 1.0,
                            'nav' => [0 => ['pageNum' => 1]],
                        ],
                        'count_products' => 1,
                        'selectedWarehouseId' => '2',
                        'warehouseOptions' => [
                            2 => 'East Coast',
                        ],
                        'stockOptions' => [
                            0 => 'All Products',
                            1 => 'In Stock Only',
                        ],
                        'variantOptionsByName' => [
                            'color' => [
                                'name' => 'Color',
                                'values' => [
                                    'blue' => 'Blue',
                                    'red' => 'Red',
                                    'yellow' => 'Yellow',
                                ],
                            ],
                        ],
                        'productTypeOptions' => [
                            'Bikes' => 'Bikes',
                        ],
                        'collectionOptions' => [
                            1 => 'Home page',
                        ],
                        'collectionImages' => [
                            [
                                'Collection' => [
                                    'id' => '1',
                                    'title' => 'Home page',
                                    'image_url' => null,
                                    'locale' => 'eng',
                                ],
                            ],
                        ],
                        'selectedCollectionTitle' => '',
                        'selectedCollectionId' => null,
                        'orderType' => 'regular',
                        'discountId' => null,
                        'cartId' => '1',
                        'existingCarts' => [
                            0 => [
                                'B2bCart' => [
                                    'id' => '3',
                                    'order_type' => 'booking',
                                    'retailer_id' => '7',
                                ],
                                'Discount' => DiscountProvider::defaultB2bCatalogueDiscountInfo(),
                                'B2bCartProduct' => [
                                    0 => [
                                        'id' => '5',
                                        'b2b_cart_id' => '3',
                                        'quantity' => '2',
                                    ],
                                    1 => [
                                        'id' => '6',
                                        'b2b_cart_id' => '3',
                                        'quantity' => '2',
                                    ],
                                ],
                            ],
                            1 => [
                                'B2bCart' => [
                                    'id' => '1',
                                    'order_type' => 'regular',
                                    'retailer_id' => '7',
                                ],
                                'Discount' => [
                                    'id' => null,
                                    'code' => null,
                                    'description' => null,
                                    'is_b2b_discount' => null,
                                    'b2b_hide_ineligible_products' => null,
                                    'retailer_option' => null,
                                    'retailer_values' => null,
                                    'enable_free_freight' => null,
                                ],
                                'B2bCartProduct' => [
                                    0 => [
                                        'id' => '1',
                                        'b2b_cart_id' => '1',
                                        'quantity' => '2',
                                    ],
                                    1 => [
                                        'id' => '2',
                                        'b2b_cart_id' => '1',
                                        'quantity' => '2',
                                    ],
                                ],
                            ],
                        ],
                        'selectedBrandId' => 8,
                        'noRecords' => 12,
                        'title_for_layout' => 'Products',
                        'has_b2b_cart_permission' => true,
                        'isConsumerView' => false,
                        'isRetailer' => false,
                        'locations' => [
                            7 => 'Local Shop',
                            17 => 'Local Shop Branch'
                        ],
                        'discountTypes' => [
                            [
                                'Discount' => [
                                    'id' => '2',
                                    'code' => 'B2BFORDAYS',
                                    'description' => '25.00% off variant (BICYCLE-1)',
                                    'b2b_discount_type' => 'booking',
                                    'retailer_option' => 'tiers',
                                    'retailer_values' => '1',
                                ],
                            ],
                        ],
                        'storefront' => [],
                        'visibleProductUuids' => [
                            '56e33952-f918-4ecd-b6b5-109b91c2de43' => '56e33952-f918-4ecd-b6b5-109b91c2de43',
                            '56e33952-2348-4c1e-9a34-109b91c2de43' => '56e33952-2348-4c1e-9a34-109b91c2de43',
                            '571f796b-2410-4ce7-8e86-0fd791c2de43' => '571f796b-2410-4ce7-8e86-0fd791c2de43',
                            '574c39c4-39dc-4853-a6ea-0ab891c2de43' => '574c39c4-39dc-4853-a6ea-0ab891c2de43',
                        ],
                        'shippingData' => [
                            'unitThreshold' => 0,
                            'priceThreshold' => 0.0,
                            'unitBasedRateOption' => '',
                            'priceBasedRateOption' => '',
                            'shippingFilters' => [],
                            'freeShippingMessages' => [
                                'initial' => '',
                                'progress' => '',
                                'success' => 'Congratulations! You’ve Earned Free Shipping'
                            ],
                        ],
                    ],
                ],
            ],
            'brand_popup_booking_cart' => [
                'user_id' => static::USER_ID_BRAND,
                'retailer_id' => '7',
                'query' => ['cartId' => '3'],
                'expected' => [
                    'query' => [
                        'cartId' => '3',
                        'display' => 'grid',
                        'sort' => 'Product.sort_order',
                        'order' => 'ASC',
                        'page' => 1,
                        'show' => 12,
                        'user_id' => 8,
                        'warehouse_id' => '2',
                    ],
                    'viewVars' => [
                        'all_products' => [
                            0 => [
                                'Product' => [
                                    'id' => '21',
                                    'uuid' => '56e33952-2348-4c1e-9a34-109b91c2de43',
                                    'user_id' => '8',
                                    'source_product_id' => '5572298951',
                                    'productID' => '17532934023',
                                    'product_sku' => 'BICYCLE-1',
                                    'product_title' => 'Super Bike - Red',
                                    'product_upc' => '9090909090909',
                                    'product_image' => 'https://cdn.shopify.com/s/files/1/1205/5522/products/RedBike.jpg?v=1513192497',
                                    'product_price' => '1200.00',
                                    'b2b_min_order_quantity' => null,
                                    'b2b_max_order_quantity' => null,
                                    'product_name' => 'Super Bike',
                                    'brand_inventory' => '483',
                                    'dealer_price' => '499.99',
                                    'can_add_to_cart' => '1',
                                    'count_instock' => '3',
                                    'count_total' => '3',
                                    'lowest_price' => '499.99',
                                    'lowest_price_currency' => 'CAD',
                                    'lowest_retail_price' => '1349.9900',
                                    'lowest_b2b_min_order_quantity' => '0',
                                    'highest_b2b_max_order_quantity' => '0',
                                    'lowest_price_discount' => [
                                        'id' => '21',
                                        'discount_amount' => '125.00',
                                        'discount_quantity' => 1,
                                        'retailers' => [
                                            0 => '7',
                                            1 => '17',
                                        ],
                                        'is_product_discount' => true,
                                    ],
                                    'lowest_retail_price_currency' => 'CAD',
                                    'highest_price' => '499.00',
                                    'highest_retail_price' => '1349.9900',
                                    'highest_price_discount' => [],
                                    'lowest_margin' => '-24.75',
                                    'highest_margin' => '68.75',
                                ],
                                'User' => [
                                    'id' => '8',
                                    'company_name' => 'My Local Brand',
                                    'currency_code' => 'USD',
                                    'enable_b2b_cart' => true,
                                ],
                                'ManufacturerRetailer' => [
                                    'id' => '3',
                                    'warehouse_id' => null,
                                ],
                                'PricingTier' => [
                                    'currencytype' => 'CAD',
                                ],
                                'Collection' => [
                                    0 => ['id' => '1', 'title' => 'Home page'],
                                ],
                                'Tag' => [
                                    0 => ['name' => 'Bike'],
                                ],
                            ],
                        ],
                        'paging' => [
                            'offset' => 0,
                            'rowsPerPage' => 12,
                            'pageNum' => 1,
                            'last' => 1.0,
                            'nav' => [0 => ['pageNum' => 1]],
                        ],
                        'count_products' => 1,
                        'selectedWarehouseId' => '2',
                        'warehouseOptions' => [
                            2 => 'East Coast',
                        ],
                        'stockOptions' => [
                            0 => 'All Products',
                            1 => 'In Stock Only',
                        ],
                        'variantOptionsByName' => [
                            'color' => [
                                'name' => 'Color',
                                'values' => [
                                    'blue' => 'Blue',
                                    'red' => 'Red',
                                    'yellow' => 'Yellow',
                                ],
                            ],
                            'material' => [
                                'name' => 'Material',
                                'values' => [
                                    'gold' => 'Gold',
                                ],
                            ],
                        ],
                        'productTypeOptions' => [
                            'Accessories' => 'Accessories',
                            'Bikes' => 'Bikes',
                        ],
                        'collectionOptions' => [
                            1 => 'Home page',
                        ],
                        'collectionImages' => [
                            [
                                'Collection' => [
                                    'id' => '1',
                                    'title' => 'Home page',
                                    'image_url' => null,
                                    'locale' => 'eng',
                                ],
                            ],
                        ],
                        'selectedCollectionTitle' => '',
                        'selectedCollectionId' => null,
                        'orderType' => 'booking',
                        'discountId' => '2',
                        'cartId' => '3',
                        'existingCarts' => [
                            0 => [
                                'B2bCart' => [
                                    'id' => '3',
                                    'order_type' => 'booking',
                                    'retailer_id' => '7',
                                ],
                                'Discount' => DiscountProvider::defaultB2bCatalogueDiscountInfo(),
                                'B2bCartProduct' => [
                                    0 => [
                                        'id' => '5',
                                        'b2b_cart_id' => '3',
                                        'quantity' => '2',
                                    ],
                                    1 => [
                                        'id' => '6',
                                        'b2b_cart_id' => '3',
                                        'quantity' => '2',
                                    ],
                                ],
                            ],
                            1 => [
                                'B2bCart' => [
                                    'id' => '1',
                                    'order_type' => 'regular',
                                    'retailer_id' => '7',
                                ],
                                'Discount' => [
                                    'id' => null,
                                    'code' => null,
                                    'description' => null,
                                    'is_b2b_discount' => null,
                                    'b2b_hide_ineligible_products' => null,
                                    'retailer_option' => null,
                                    'retailer_values' => null,
                                    'enable_free_freight' => null,
                                ],
                                'B2bCartProduct' => [
                                    0 => [
                                        'id' => '1',
                                        'b2b_cart_id' => '1',
                                        'quantity' => '2',
                                    ],
                                    1 => [
                                        'id' => '2',
                                        'b2b_cart_id' => '1',
                                        'quantity' => '2',
                                    ],
                                ],
                            ],
                        ],
                        'selectedBrandId' => 8,
                        'noRecords' => 12,
                        'title_for_layout' => 'Products',
                        'has_b2b_cart_permission' => true,
                        'isConsumerView' => false,
                        'isRetailer' => false,
                        'locations' => [
                            7 => 'Local Shop',
                            17 => 'Local Shop Branch'
                        ],
                        'discountTypes' => [
                            [
                                'Discount' => [
                                    'id' => '2',
                                    'code' => 'B2BFORDAYS',
                                    'description' => '25.00% off variant (BICYCLE-1)',
                                    'b2b_discount_type' => 'booking',
                                    'retailer_option' => 'tiers',
                                    'retailer_values' => '1',
                                ],
                            ],
                        ],
                        'storefront' => [],
                        'visibleProductUuids' => [
                            '56e33952-f918-4ecd-b6b5-109b91c2de43' => '56e33952-f918-4ecd-b6b5-109b91c2de43',
                            '56e33952-2348-4c1e-9a34-109b91c2de43' => '56e33952-2348-4c1e-9a34-109b91c2de43',
                            '571f796b-2410-4ce7-8e86-0fd791c2de43' => '571f796b-2410-4ce7-8e86-0fd791c2de43',
                            '574c39c4-39dc-4853-a6ea-0ab891c2de43' => '574c39c4-39dc-4853-a6ea-0ab891c2de43',
                        ],
                        'shippingData' => [
                            'unitThreshold' => 0,
                            'priceThreshold' => 0.0,
                            'unitBasedRateOption' => '',
                            'priceBasedRateOption' => '',
                            'shippingFilters' => [],
                            'freeShippingMessages' => [
                                'initial' => '',
                                'progress' => '',
                                'success' => 'Congratulations! You’ve Earned Free Shipping'
                            ],
                        ],
                    ],
                ],
            ],
            'consumer_view' => [
                'user_id' => static::USER_ID_BRAND,
                'retailer_id' => '7',
                'query' => ['isConsumerView' => 1],
                'expected' => [
                    'query' => [
                        'display' => 'grid',
                        'sort' => 'Product.sort_order',
                        'order' => 'ASC',
                        'page' => 1,
                        'show' => 12,
                        'warehouse_id' => '2',
                        'isConsumerView' => '1',
                    ],
                    'viewVars' => [
                        'all_products' => [
                            0 => [
                                'Product' => [
                                    'id' => '21',
                                    'uuid' => '56e33952-2348-4c1e-9a34-109b91c2de43',
                                    'user_id' => '8',
                                    'source_product_id' => '5572298951',
                                    'productID' => '17532934023',
                                    'product_sku' => 'BICYCLE-1',
                                    'product_title' => 'Super Bike - Red',
                                    'product_upc' => '9090909090909',
                                    'product_image' => 'https://cdn.shopify.com/s/files/1/1205/5522/products/RedBike.jpg?v=1513192497',
                                    'product_price' => '1200.00',
                                    'b2b_min_order_quantity' => null,
                                    'b2b_max_order_quantity' => null,
                                    'product_name' => 'Super Bike',
                                    'brand_inventory' => '483',
                                    'dealer_price' => '499.99',
                                    'can_add_to_cart' => '1',
                                    'count_instock' => '3',
                                    'count_total' => '3',
                                    'lowest_price' => '375.00',
                                    'lowest_price_currency' => 'CAD',
                                    'lowest_retail_price' => '1349.9900',
                                    'lowest_b2b_min_order_quantity' => '0',
                                    'highest_b2b_max_order_quantity' => '0',
                                    'lowest_price_discount' => [],
                                    'lowest_retail_price_currency' => 'CAD',
                                    'highest_price' => '499.99',
                                    'highest_retail_price' => '1349.9900',
                                    'highest_price_discount' => [],
                                    'lowest_margin' => '-24.75',
                                    'highest_margin' => '62.96',
                                ],
                                'User' => [
                                    'id' => '8',
                                    'company_name' => 'My Local Brand',
                                    'currency_code' => 'USD',
                                    'enable_b2b_cart' => true,
                                ],
                                'ManufacturerRetailer' => [
                                    'id' => '3',
                                    'warehouse_id' => null,
                                ],
                                'PricingTier' => [
                                    'currencytype' => 'CAD',
                                ],
                                'Collection' => [
                                    0 => ['id' => '1', 'title' => 'Home page'],
                                ],
                                'Tag' => [
                                    0 => ['name' => 'Bike'],
                                ],
                            ],
                        ],
                        'paging' => [
                            'offset' => 0,
                            'rowsPerPage' => 12,
                            'pageNum' => 1,
                            'last' => 1.0,
                            'nav' => [0 => ['pageNum' => 1]],
                        ],
                        'count_products' => 1,
                        'selectedWarehouseId' => '2',
                        'warehouseOptions' => [
                            2 => 'East Coast',
                        ],
                        'stockOptions' => [
                            0 => 'All Products',
                            1 => 'In Stock Only',
                        ],
                        'variantOptionsByName' => [
                            'color' => [
                                'name' => 'Color',
                                'values' => [
                                    'blue' => 'Blue',
                                    'red' => 'Red',
                                    'yellow' => 'Yellow',
                                ],
                            ],
                            'material' => [
                                'name' => 'Material',
                                'values' => [
                                    'gold' => 'Gold',
                                ],
                            ],
                        ],
                        'productTypeOptions' => [
                            'Accessories' => 'Accessories',
                            'Bikes' => 'Bikes',
                        ],
                        'collectionOptions' => [
                            1 => 'Home page',
                        ],
                        'collectionImages' => [
                            [
                                'Collection' => [
                                    'id' => '1',
                                    'title' => 'Home page',
                                    'image_url' => null,
                                    'locale' => 'eng',
                                ],
                            ],
                        ],
                        'selectedCollectionTitle' => '',
                        'selectedCollectionId' => null,
                        'orderType' => null,
                        'discountId' => null,
                        'cartId' => null,
                        'existingCarts' => [
                            0 => [
                                'B2bCart' => [
                                    'id' => '3',
                                    'order_type' => 'booking',
                                    'retailer_id' => '7',
                                ],
                                'Discount' => DiscountProvider::defaultB2bCatalogueDiscountInfo(),
                                'B2bCartProduct' => [
                                    0 => [
                                        'id' => '5',
                                        'b2b_cart_id' => '3',
                                        'quantity' => '2',
                                    ],
                                    1 => [
                                        'id' => '6',
                                        'b2b_cart_id' => '3',
                                        'quantity' => '2',
                                    ],
                                ],
                            ],
                            1 => [
                                'B2bCart' => [
                                    'id' => '1',
                                    'order_type' => 'regular',
                                    'retailer_id' => '7',
                                ],
                                'Discount' => [
                                    'id' => null,
                                    'code' => null,
                                    'description' => null,
                                    'is_b2b_discount' => null,
                                    'b2b_hide_ineligible_products' => null,
                                    'retailer_option' => null,
                                    'retailer_values' => null,
                                    'enable_free_freight' => null,
                                ],
                                'B2bCartProduct' => [
                                    0 => [
                                        'id' => '1',
                                        'b2b_cart_id' => '1',
                                        'quantity' => '2',
                                    ],
                                    1 => [
                                        'id' => '2',
                                        'b2b_cart_id' => '1',
                                        'quantity' => '2',
                                    ],
                                ],
                            ],
                        ],
                        'selectedBrandId' => 8,
                        'noRecords' => 12,
                        'title_for_layout' => 'Products',
                        'has_b2b_cart_permission' => true,
                        'isConsumerView' => false,
                        'isRetailer' => false,
                        'locations' => [
                            7 => 'Local Shop',
                            17 => 'Local Shop Branch'
                        ],
                        'discountTypes' => [
                            [
                                'Discount' => [
                                    'id' => '2',
                                    'code' => 'B2BFORDAYS',
                                    'description' => '25.00% off variant (BICYCLE-1)',
                                    'b2b_discount_type' => 'booking',
                                    'retailer_option' => 'tiers',
                                    'retailer_values' => '1',
                                ],
                            ],
                        ],
                        'storefront' => [],
                        'visibleProductUuids' => [
                            '56e33952-f918-4ecd-b6b5-109b91c2de43' => '56e33952-f918-4ecd-b6b5-109b91c2de43',
                            '56e33952-2348-4c1e-9a34-109b91c2de43' => '56e33952-2348-4c1e-9a34-109b91c2de43',
                            '571f796b-2410-4ce7-8e86-0fd791c2de43' => '571f796b-2410-4ce7-8e86-0fd791c2de43',
                            '574c39c4-39dc-4853-a6ea-0ab891c2de43' => '574c39c4-39dc-4853-a6ea-0ab891c2de43',
                        ],
                        'shippingData' => [
                            'unitThreshold' => 0,
                            'priceThreshold' => 0.0,
                            'unitBasedRateOption' => '',
                            'priceBasedRateOption' => '',
                            'shippingFilters' => [],
                            'freeShippingMessages' => [
                                'initial' => '',
                                'progress' => '',
                                'success' => 'Congratulations! You’ve Earned Free Shipping'
                            ],
                        ],
                    ],
                ],
            ],
        ];
    }

    /**
     * @dataProvider providerB2bEligibleForDiscountCatalogue
     */
    public function testB2bEligibleForDiscountCatalogue(array $discountSeed, array $expected)
    {
        $userId = '12';
        $retailerId = '15';
        $discountId = '3';
        $discountRuleId = '3';
        $query = ['discountId' => $discountId, 'orderType' => 'booking', 'show' => 5];

        $this->controller->Auth->login(['id' => $userId]);
        $this->_mockHeaderNotifications();

        $this->_seedB2bEligibleForDiscountCatalogue();
        if (!$this->controller->Discount->saveAssociated([
            'Discount' => ['id' => $discountId, 'b2b_hide_ineligible_products' => true],
            'DiscountRule' => [
                0 => (['id' => $discountRuleId] + $discountSeed),
                1 => [
                    'id' => null,
                    'option' => 'percent_off',
                    'option_amount' => '25.00',
                    'prerequisite_subtotal' => '0.00',
                    'prerequisite_quantity' => '0',
                    'prerequisite_option' => null,
                    'prerequisite_values' => null,
                    'order_quantity' => '0',
                    'order_option' => 'product_variant',
                    'order_values' => json_encode(['SELL-DIRECT-UNLESS-BUNDLED']),
                    'auto_add_sku_quantities' => null,
                ],
            ],
        ])) {
            throw new RuntimeException('Failed to seed discount applies to ' . json_encode(['errors' => $this->controller->Discount->validationErrors, 'data' => $this->controller->Discount->data]));
        }
        $this->controller->Discount->clear();

        $this->get(['controller' => 'products', 'action' => 'index_retailer', 'retailer_id' => $retailerId, '?' => $query]);

        $actual = array_intersect_key($this->controller->viewVars, array_flip([
            'all_products',
            'count_products',
            'variantOptionsByName',
            'productTypeOptions',
            'collectionOptions',
        ]));
        $actual['all_products'] = array_map(function($product) {
            return array_intersect_key($product['Product'], array_flip(['id', 'product_sku', 'count_total']));
        }, $actual['all_products']);

        $expected = array_merge($expected, [
            'variantOptionsByName' => [
                'color' => [
                    'name' => 'Color',
                    'values' => [
                        'blue' => 'Blue',
                        'red' => 'Red',
                    ],
                ],
            ],
            'productTypeOptions' => [
                'Cycling' => 'Cycling',
                'Flowers' => 'Flowers',
            ],
            'collectionOptions' => [
                2 => 'Home page',
                3 => 'Test Manual Collection',
            ],
        ]);

        $this->assertEquals($expected, $actual);
        $this->assertResponseOk();
    }

    public function providerB2bEligibleForDiscountCatalogue(): array
    {
        return [
            'eligible_for_all' => [
                'discountSeed' => [
                    'order_option' => 'all',
                    'order_values' => '',
                ],
                'expected' => [
                    'all_products' => [
                        0 => [
                            'id' => '41',
                            'product_sku' => 'VARIANT-1',
                            'count_total' => '2',
                        ],
                        1 => [
                            'id' => '29',
                            'product_sku' => 'NO-STOCK',
                            'count_total' => '1',
                        ],
                        2 => [
                            'id' => '25',
                            'product_sku' => 'BICYCLE',
                            'count_total' => '1',
                        ],
                    ],
                    'count_products' => 3,
                ],
            ],
            'eligible_for_category' => [
                'discountSeed' => [
                    'order_option' => 'category',
                    'order_values' => json_encode(['Cycling']),
                ],
                'expected' => [
                    'all_products' => [
                        0 => [
                            'id' => '42',
                            'product_sku' => 'VARIANT-2',
                            'count_total' => '1',
                        ],
                        1 => [
                            'id' => '29',
                            'product_sku' => 'NO-STOCK',
                            'count_total' => '1',
                        ],
                        2 => [
                            'id' => '25',
                            'product_sku' => 'BICYCLE',
                            'count_total' => '1',
                        ],
                    ],
                    'count_products' => 3,
                ],
            ],
            'eligible_for_categories' => [
                'discountSeed' => [
                    'order_option' => 'category',
                    'order_values' => json_encode(['Cycling', 'Flowers']),
                ],
                'expected' => [
                    'all_products' => [
                        0 => [
                            'id' => '41',
                            'product_sku' => 'VARIANT-1',
                            'count_total' => '2',
                        ],
                        1 => [
                            'id' => '29',
                            'product_sku' => 'NO-STOCK',
                            'count_total' => '1',
                        ],
                        2 => [
                            'id' => '25',
                            'product_sku' => 'BICYCLE',
                            'count_total' => '1',
                        ],
                    ],
                    'count_products' => 3,
                ],
            ],
            'eligible_for_product_title' => [
                'discountSeed' => [
                    'order_option' => 'product_title',
                    'order_values' => json_encode(['Variant']),
                ],
                'expected' => [
                    'all_products' => [
                        0 => [
                            'id' => '41',
                            'product_sku' => 'VARIANT-1',
                            'count_total' => '2',
                        ],
                    ],
                    'count_products' => 1,
                ],
            ],
            'eligible_for_product_titles' => [
                'discountSeed' => [
                    'order_option' => 'product_title',
                    'order_values' => json_encode(['Variant', 'Bicycle']),
                ],
                'expected' => [
                    'all_products' => [
                        0 => [
                            'id' => '41',
                            'product_sku' => 'VARIANT-1',
                            'count_total' => '2',
                        ],
                        1 => [
                            'id' => '25',
                            'product_sku' => 'BICYCLE',
                            'count_total' => '1',
                        ],
                    ],
                    'count_products' => 2,
                ],
            ],
            'eligible_for_product_variant' => [
                'discountSeed' => [
                    'order_option' => 'product_variant',
                    'order_values' => json_encode(['VARIANT-1']),
                ],
                'expected' => [
                    'all_products' => [
                        0 => [
                            'id' => '41',
                            'product_sku' => 'VARIANT-1',
                            'count_total' => '1',
                        ],
                    ],
                    'count_products' => 1,
                ],
            ],
            'eligible_for_product_variants' => [
                'discountSeed' => [
                    'order_option' => 'product_variant',
                    'order_values' => json_encode(['VARIANT-1', 'BICYCLE']),
                ],
                'expected' => [
                    'all_products' => [
                        0 => [
                            'id' => '41',
                            'product_sku' => 'VARIANT-1',
                            'count_total' => '1',
                        ],
                        1 => [
                            'id' => '25',
                            'product_sku' => 'BICYCLE',
                            'count_total' => '1',
                        ],
                    ],
                    'count_products' => 2,
                ],
            ],
            'eligible_for_product_collection' => [
                'discountSeed' => [
                    'order_option' => 'product_collection',
                    'order_values' => json_encode(['Home page']),
                ],
                'expected' => [
                    'all_products' => [
                        0 => [
                            'id' => '42',
                            'product_sku' => 'VARIANT-2',
                            'count_total' => '1',
                        ],
                        1 => [
                            'id' => '29',
                            'product_sku' => 'NO-STOCK',
                            'count_total' => '1',
                        ],
                        2 => [
                            'id' => '25',
                            'product_sku' => 'BICYCLE',
                            'count_total' => '1',
                        ],
                    ],
                    'count_products' => 3,
                ],
            ],
            'eligible_for_product_collections' => [
                'discountSeed' => [
                    'order_option' => 'product_collection',
                    'order_values' => json_encode(['Home page', 'Test Manual Collection']),
                ],
                'expected' => [
                    'all_products' => [
                        0 => [
                            'id' => '41',
                            'product_sku' => 'VARIANT-1',
                            'count_total' => '2',
                        ],
                        1 => [
                            'id' => '29',
                            'product_sku' => 'NO-STOCK',
                            'count_total' => '1',
                        ],
                        2 => [
                            'id' => '25',
                            'product_sku' => 'BICYCLE',
                            'count_total' => '1',
                        ],
                    ],
                    'count_products' => 3,
                ],
            ],
            'eligible_for_product_tag' => [
                'discountSeed' => [
                    'order_option' => 'product_tag',
                    'order_values' => json_encode(['Bike']),
                ],
                'expected' => [
                    'all_products' => [
                        0 => [
                            'id' => '42',
                            'product_sku' => 'VARIANT-2',
                            'count_total' => '1',
                        ],
                        1 => [
                            'id' => '29',
                            'product_sku' => 'NO-STOCK',
                            'count_total' => '1',
                        ],
                        2 => [
                            'id' => '25',
                            'product_sku' => 'BICYCLE',
                            'count_total' => '1',
                        ],
                    ],
                    'count_products' => 3,
                ],
            ],
            'eligible_for_product_tags' => [
                'discountSeed' => [
                    'order_option' => 'product_tag',
                    'order_values' => json_encode(['Bike', 'Test']),
                ],
                'expected' => [
                    'all_products' => [
                        0 => [
                            'id' => '41',
                            'product_sku' => 'VARIANT-1',
                            'count_total' => '2',
                        ],
                        1 => [
                            'id' => '29',
                            'product_sku' => 'NO-STOCK',
                            'count_total' => '1',
                        ],
                        2 => [
                            'id' => '25',
                            'product_sku' => 'BICYCLE',
                            'count_total' => '1',
                        ],
                    ],
                    'count_products' => 3,
                ],
            ],
        ];
    }

    private function _seedB2bEligibleForDiscountCatalogue()
    {
        /** @var CollectionsProduct $CollectionsProduct */
        $CollectionsProduct = ClassRegistry::init('CollectionsProduct');
        /** @var ProductTag $ProductTag */
        $ProductTag = ClassRegistry::init('ProductTag');

        $this->controller->Product->updateAllJoinless(['Product.product_type' => '""'], ['Product.user_id' => '12']);
        $this->controller->Product->saveMany([
            ['id' => '25', 'product_type' => 'Cycling'],
            ['id' => '26', 'product_type' => 'Flowers'],
            // Simulate WC product_type
            ['id' => '29', 'product_type' => json_encode(['Cycling', 'Flowers'])],
            ['id' => '41', 'product_type' => 'Flowers'],
            ['id' => '42', 'product_type' => 'Cycling'],
        ]);

        $CollectionsProduct->truncate();
        $this->assertTrue(
            $CollectionsProduct->saveMany([
                ['product_id' => '25', 'collection_id' => '2'],
                ['product_id' => '26', 'collection_id' => '3'],
                ['product_id' => '29', 'collection_id' => '2'],
                ['product_id' => '29', 'collection_id' => '3'],
                ['product_id' => '41', 'collection_id' => '3'],
                ['product_id' => '42', 'collection_id' => '2'],
            ]),
            'Failed to seed product tag ' . json_encode(['errors' => $CollectionsProduct->validationErrors, 'data' => $CollectionsProduct->data])
        );
        $CollectionsProduct->clear();

        $ProductTag->truncate();
        $this->assertTrue(
            (
                $ProductTag->saveTagSet('25', ['Bike'])
                && $ProductTag->saveTagSet('26', ['Test'])
                && $ProductTag->saveTagSet('29', ['Bike', 'Test'])
                && $ProductTag->saveTagSet('41', ['Test'])
                && $ProductTag->saveTagSet('42', ['Bike'])
            ),
            'Failed to seed product tag ' . json_encode(['errors' => $ProductTag->validationErrors, 'data' => $ProductTag->data])
        );
        $ProductTag->clear();
    }

    private function _mockHeaderNotifications(): void
    {
        $this->controller->expects($this->any())->method('_headerNotifications')->willReturnCallback(function(): void {
            $this->controller->set('has_b2b_cart_permission', $this->controller->Permissions->userHasB2bCartPermission($this->controller->Auth->user()));
            $this->controller->set('b2b_cart_count', 0);
        });
    }
}
