<?php

use ShipEarlyApp\Test\Support\FakePhpExcelComponent;

App::uses('IntegrationTestCase', 'TestSuite');
App::uses('RetailersController', 'Controller');
App::uses('ManufacturerRetailer', 'Model');

/**
 * RetailersController::export and import Test Case.
 *
 * Run with the following console command:
 *
 * ./app/Console/cake test app Controller/Retailers/RetailersExportImport --stderr
 *
 * @property RetailersController|PHPUnit_Framework_MockObject_MockObject $controller
 * @property FakePhpExcelComponent $PhpExcel
 */
class RetailersExportImportTest extends IntegrationTestCase
{
    const BRAND_ID = '8';
    const EXPORT_URL = ['controller' => 'retailers', 'action' => 'export'];
    const EXPORT_FILENAME = 'My Local Brand Retailer Export %s.xlsx';

    const IMPORT_URL = ['controller' => 'retailers', 'action' => 'import'];
    const IMPORT_REQUEST = ['ManufacturerRetailer' => ['upload' => [
        'name' => self::EXPORT_FILENAME,
        'type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'tmp_name' => TMP . 'phprys9Gj',
        'error' => 0,
        'size' => 8825,
    ]]];

    const SAMPLE_EXPORT_ROW = [
        'Retailer Name' => 'LightSpeed',
        'Pricing Tier' => 'Tier 1',
        'Dealer Type' => 'Standard',
        'Commission Uses Dealer Tax Rate' => 'No',
        'Distributor' => '',
        'Sales Rep 1' => '',
        'Sales Rep 2' => '',
        'Delivery' => '0',
        'Ship from Store Distance' => '0',
        'Dealer Protect' => '0',
        'Account ID' => '',
        'B2B Tax' => '0.0000',
        'VAT ID #' => '',
        'Min B2B Value' => '0.00',
        'Enable Split Payment' => 'Yes',
        'Default Warehouse' => '',
        'Territory' => '',
        'Enable Consumer Orders' => 'Yes', // 'Yes', 'No'
        'Non-Stocking' => 'No', // 'Yes', 'No'
        'Inventory Connected' => 'No', // 'Yes', 'No'
        'Stripe Status' => 'Activated', // 'Activated', 'Deferred', or NULL
        'Klarna Payments Status' => 'No', // 'Yes', 'No'
        'Affirm Payments Status' => 'No', // 'Yes', 'No'
        'Payments' => 'Enabled', //'Enabled', 'Disabled'
        'Outstanding Balance' => '0.00',
        'Test credit term 3' => 'No',
        'Net 15' => 'No',
        'Net 30' => 'No',
        'Test credit term 1' => 'No',
        'Net 45' => 'No',
        'Test credit term 2' => 'No',
        'Net 60' => 'No',
        'Net 90' => 'No',
        'Net 120' => 'No',
        'Net 180' => 'No',
        'Credit Limit' => '10000.00',
        'Credit Card' => 'Yes',
        'File Payment' => 'No',
        'Status' => 'Not Connected', // ManufacturerRetailer::STATUS_NAMES
        'Billing Email' => '',
        'Billing Company' => '',
        'Address' => '2400 Nipigon Road',
        'City' => 'Thunder Bay',
        'State/Province' => 'Ontario',
        'ZIP' => 'P7C 4W1',
        'Country' => 'Canada',
        'Telephone' => '************',
        'Primary eMail' => '<EMAIL>',
        'Contact Person' => 'Tony Pulis',
        'Support eMail' => '<EMAIL>',
        'Support Telephone' => '***********',
        'Ship to Address 1' => '',
        'Ship to Address 2' => '',
        'Ship to City' => '',
        'Ship to State/Province' => '',
        'Ship to ZIP/Postal Code' => '',
        'Ship to Country' => '',
        'Ship to Telephone' => '',
    ];

    public $fixtures = [
        'app.b2b_ship_to_address',
        'app.brand_staff',
        'app.brand_staff_permission',
        'app.category',
        'app.configuration',
        'app.contact',
        'app.contactpersons',
        'app.country',
        'app.credit_term',
        'app.manufacturer_retailer',
        'app.manufacturer_retailer_sales_rep',
        'app.manufacturer_sales_rep',
        'app.order',
        'app.order_comment',
        'app.order_customer_message',
        'app.order_product',
        'app.pricing_tier',
        'app.pricing_tiers_hidden_warehouse',
        'app.retailer_credit',
        'app.retailer_credit_item',
        'app.retailer_credit_payment',
        'app.retailer_credit_term',
        'app.staff',
        'app.staff_permission',
        'app.state',
        'app.store',
        'app.stripe_user',
        'app.stripe_user_capability',
        'app.territory',
        'app.user',
        'app.user_category',
        'app.warehouse',
    ];

    public function setUp()
    {
        parent::setUp();
        $this->_buildMockController();

        // Use <EMAIL> for commission retailer cases
        $this->controller->ManufacturerRetailer->updateAllJoinless(
            ['pricingtierid' => null, 'is_commission_tier' => true],
            ['id' => '4']
        );
    }

    private function _buildMockController()
    {
        unset($this->controller);

        $mocksForUnnecessaryDependencies = [
            'methods' => ['_headerNotifications', '_checkCron'],
        ];
        $this->generate('Retailers', Hash::merge($mocksForUnnecessaryDependencies, [
            'components' => [
                'Upload' => ['getTempFile'],
                'UserLogic' => ['productAssociation'],
            ],
        ]));

        $this->controller->Auth->unauthorizedRedirect = false;

        $this->PhpExcel = new FakePhpExcelComponent($this->controller->Components);
        $this->controller->Components->set('PhpExcel', $this->PhpExcel);
        $this->controller->Components->enable('PhpExcel');
        $this->controller->Components->init($this->controller);

        // Bare minimum import file for tests where data doesn't matter
        $this->PhpExcel->importTableMap = [
            ['Primary eMail' => '<EMAIL>'],
        ];

        /** @var UploadComponent|PHPUnit_Framework_MockObject_MockObject $Upload */
        $Upload = $this->controller->Upload;
        $Upload->expects($this->any())->method('getTempFile')->willReturnCallback(function($file) {
            if (!empty($file['tmp_name'])) {
                return $file['tmp_name'];
            } else {
                throw new Exception($this->callPrivateMethod([$this->controller->Upload, 'upload_error'], UPLOAD_ERR_NO_FILE));
            }
        });

        $this->controller->syncUserSession(static::BRAND_ID);
    }

    public function testExportFileAttributes()
    {
        $this->get(static::EXPORT_URL);
        $this->assertResponseOk();

        $expected = [
            'isWorksheetCreated' => true,
            'defaultFont' => ['name' => 'Calibri', 'size' => 12],
            'tableHeaderParams' => [
                ['name' => 'Cambria', 'bold' => true],
            ],
            'isTableFooterSet' => true,
            'renderedFile' => ['filename' => sprintf(static::EXPORT_FILENAME, static::date('Y-m-d')), 'writer' => 'Xlsx'],
        ];
        $actual = [
            'isWorksheetCreated' => $this->PhpExcel->isWorksheetCreated,
            'defaultFont' => $this->PhpExcel->defaultFont,
            'tableHeaderParams' => array_column($this->PhpExcel->tableHeaders, 'params'),
            'isTableFooterSet' => $this->PhpExcel->isTableFooterSet,
            'renderedFile' => $this->PhpExcel->renderedFile,
        ];
        $this->assertEquals($expected, $actual);
    }

    public function testExportFileColumns()
    {
        $this->get(static::EXPORT_URL);
        $this->assertResponseOk();

        $expectedSampleRowData = static::SAMPLE_EXPORT_ROW;

        $actualHeaders = array_column($this->PhpExcel->tableHeaders[0]['data'], 'label');
        $this->assertEquals(array_keys($expectedSampleRowData), array_values($actualHeaders), 'Correct order of headers');

        $actualSampleRow = $this->PhpExcel->tableRows[0];

        $this->assertEquals($expectedSampleRowData, array_combine($actualHeaders, $actualSampleRow['data']), 'Sample row mapped data');
        $this->assertEquals([], $actualSampleRow['data_types'], 'Sample row data_types');
    }

    /**
     * @dataProvider providerExportQueries
     * @param array $query
     * @param string[] $expectedEmails
     */
    public function testExportQueryFiltersRetailers(array $query, array $expectedEmails)
    {
        $this->get(array_merge(static::EXPORT_URL, ['?' => $query]));
        $this->assertResponseOk();
        $actualEmails = array_column($this->getPhpExcelTableMap(), 'Primary eMail');

        $this->assertEquals($expectedEmails, $actualEmails);
    }

    public function providerExportQueries(): array
    {
        return [
            'search' => [
                'query' => ['search' => 'Local Shop'],
                'expectedEmails' => ['<EMAIL>', '<EMAIL>'],
            ],
            'status' => [
                // Good value to test because it is falsey
                'query' => ['status' => ManufacturerRetailer::STATUS_DISCONNECTED],
                'expectedEmails' => ['<EMAIL>'],
            ],
            'tier' => [
                'query' => ['tier' => '1'],
                'expectedEmails' => ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
            ],
            'sales_rep' => [
                'query' => ['sales_rep' => '21'],
                'expectedEmails' => ['<EMAIL>', '<EMAIL>'],
            ],
        ];
    }

    /**
     * @dataProvider providerExportStripeStatusValues
     */
    public function testExportStripeStatusValues(array $updateStripeUser, array $expected)
    {
        if ($updateStripeUser) {
            /** @var StripeUser $StripeUser */
            $StripeUser = ClassRegistry::init('StripeUser');
            if (!$StripeUser->updateAllJoinless($updateStripeUser, ['StripeUser.user_id' => '7'])) {
                throw new RuntimeException('Failed to seed StripeUser');
            }
        }

        $this->get(array_merge(static::EXPORT_URL, ['?' => ['sales_rep' => '21']]));
        $this->assertResponseOk();
        $actual = array_map(function($rowMap) {
            return array_intersect_key($rowMap, array_flip([
                'Stripe Status',
                'Payments',
                'Primary eMail',
            ]));
        }, $this->getPhpExcelTableMap());

        $this->assertEquals($expected, $actual);
    }

    public function providerExportStripeStatusValues(): array
    {
        return [
            'default' => [
                'updateStripeUser' => [
                ],
                'expected' => [
                    [
                        'Stripe Status' => 'Activated',
                        'Payments' => 'Enabled',
                        'Primary eMail' => '<EMAIL>',
                    ],
                    [
                        'Stripe Status' => 'Activated',
                        'Payments' => 'Enabled',
                        'Primary eMail' => '<EMAIL>',
                    ],
                ],
            ],
            'payments_disabled' => [
                'updateStripeUser' => [
                    'StripeUser.charges_enabled' => false,
                ],
                'expected' => [
                    [
                        'Stripe Status' => 'Activated',
                        'Payments' => 'Disabled',
                        'Primary eMail' => '<EMAIL>',
                    ],
                    [
                        'Stripe Status' => 'Activated',
                        'Payments' => 'Disabled',
                        'Primary eMail' => '<EMAIL>',
                    ],
                ],
            ],
            'status_deferred' => [
                'updateStripeUser' => [
                    'StripeUser.is_activated' => false,
                ],
                'expected' => [
                    [
                        'Stripe Status' => 'Deferred',
                        'Payments' => 'Enabled',
                        'Primary eMail' => '<EMAIL>',
                    ],
                    [
                        'Stripe Status' => 'Deferred',
                        'Payments' => 'Enabled',
                        'Primary eMail' => '<EMAIL>',
                    ],
                ],
            ],
            'no_account' => [
                'updateStripeUser' => [
                    'StripeUser.stripe_user_id' => null,
                ],
                'expected' => [
                    [
                        'Stripe Status' => null,
                        'Payments' => 'Disabled',
                        'Primary eMail' => '<EMAIL>',
                    ],
                    [
                        'Stripe Status' => null,
                        'Payments' => 'Disabled',
                        'Primary eMail' => '<EMAIL>',
                    ],
                ],
            ],
        ];
    }

    public function testImportFileUpdatesExportData()
    {
        /** @var UserLogicComponent|PHPUnit_Framework_MockObject_MockObject $UserLogic */
        $UserLogic = $this->controller->UserLogic;
        $UserLogic->expects($this->once())->method('productAssociation')->with(static::BRAND_ID, '2');

        // Modify all export file fields
        $this->PhpExcel->importTableMap = [
            [
                'Retailer Name' => 'Modified LightSpeed',
                'Pricing Tier' => 'Tier 2',
                'Dealer Type' => 'Commission',
                'Commission Uses Dealer Tax Rate' => 'Yes',
                'Distributor' => 'Distributor',
                'Sales Rep 1' => 'Sales Rep',
                'Sales Rep 2' => 'Sales Rep 2',
                'Sales Rep 3' => '',
                'Delivery' => '100',
                'Ship from Store Distance' => '50',
                'Dealer Protect' => '50',
                'Account ID' => '#1001',
                'B2B Tax' => '13.0000',
                'VAT ID #' => 'IE1234567T',
                'Min B2B Value' => '100.00',
                'Enable Split Payment' => 'No',
                'Default Warehouse' => 'West Coast',
                'Territory' => 'Local Dealers',
                'Enable Consumer Orders' => 'No',
                'Non-Stocking' => 'Yes',
                'Inventory Connected' => 'Yes',
                'Stripe Status' => 'Deferred',
                'Klarna Payments Status' => 'Yes',
                'Affirm Payments Status' => 'Yes',
                'Payments' => 'Disabled',
                'Outstanding Balance' => '100.00',
                'Test credit term 3' => 'Yes',
                'Net 15' => 'Yes',
                'Net 30' => 'Yes',
                'Test credit term 1' => 'Yes',
                'Net 45' => 'Yes',
                'Test credit term 2' => 'Yes',
                'Net 60' => 'Yes',
                'Net 90' => 'Yes',
                'Net 120' => 'Yes',
                'Net 180' => 'Yes',
                'Credit Limit' => '1000.00',
                'Credit Card' => 'No',
                'File Payment' => 'Yes',
                'Status' => 'Connected',
                'Billing Email' => '<EMAIL>',
                'Billing Company' => 'Billing Company',
                'Address' => '3935 Walnut Street',
                'City' => 'Philadelphia',
                'State/Province' => 'Pennsylvania',
                'ZIP' => '19104',
                'Country' => 'United States',
                'Telephone' => '************',
                'Primary eMail' => '<EMAIL>', // Do not change
                'Contact Person' => 'Donald Trump',
                'Support eMail' => '<EMAIL>',
                'Support Telephone' => '************',
                'Ship to Address 1' => '3935 Walnut Street',
                'Ship to Address 2' => 'Apt 1',
                'Ship to City' => 'Philadelphia',
                'Ship to State/Province' => 'Pennsylvania',
                'Ship to ZIP/Postal Code' => '19104',
                'Ship to Country' => 'United States',
                'Ship to Telephone' => '************',
            ],
        ];
        $rowDataToUpdate = array_diff_key(static::SAMPLE_EXPORT_ROW, array_flip(['Primary eMail']));
        $rowDataMissingOrNotUpdated = array_intersect_assoc($rowDataToUpdate, array_merge($rowDataToUpdate, $this->PhpExcel->importTableMap[0]));
        if ($rowDataMissingOrNotUpdated) {
            $this->fail('Input row values missing or not updated from original row values' . PHP_EOL . Debugger::exportVar($rowDataMissingOrNotUpdated));
        }

        $expectedModifiedRowData = array_merge(static::SAMPLE_EXPORT_ROW, [
            'Pricing Tier' => 'Tier 2',
            'Dealer Type' => 'Commission',
            'Commission Uses Dealer Tax Rate' => 'Yes',
            'Distributor' => 'Distributor',
            'Sales Rep 1' => 'Sales Rep',
            'Sales Rep 2' => 'Sales Rep 2',
            'Sales Rep 3' => null,
            'Delivery' => '100',
            'Ship from Store Distance' => '50',
            'Dealer Protect' => '50',
            'Account ID' => '#1001',
            'B2B Tax' => '13.0000',
            'VAT ID #' => 'IE1234567T',
            'Min B2B Value' => '100.00',
            'Enable Split Payment' => 'No',
            'Default Warehouse' => 'West Coast',
            'Territory' => 'Local Dealers',
            'Enable Consumer Orders' => 'No',
            'Non-Stocking' => 'Yes',
            'Test credit term 3' => 'Yes',
            'Net 15' => 'Yes',
            'Net 30' => 'Yes',
            'Test credit term 1' => 'Yes',
            'Net 45' => 'Yes',
            'Test credit term 2' => 'Yes',
            'Net 60' => 'Yes',
            'Net 90' => 'Yes',
            'Net 120' => 'Yes',
            'Net 180' => 'Yes',
            'Credit Limit' => '1000.00',
            'Credit Card' => 'No',
            'File Payment' => 'Yes',
            'Status' => 'Connected',
            'Billing Email' => '<EMAIL>',
            'Billing Company' => 'Billing Company',
            'Ship to Address 1' => '3935 Walnut Street',
            'Ship to Address 2' => 'Apt 1',
            'Ship to City' => 'Philadelphia',
            'Ship to State/Province' => 'Pennsylvania',
            'Ship to ZIP/Postal Code' => '19104',
            'Ship to Country' => 'United States',
            'Ship to Telephone' => '************',
        ]);

        $this->post(static::IMPORT_URL, static::IMPORT_REQUEST);
        $this->assertRedirect();
        $this->assertNotEmpty($this->controller->getFlash('success'), 'Success flash message');

        // Rebuild controller to clean properties for next request
        $this->_buildMockController();

        $this->get(static::EXPORT_URL);

        $actualHeaders = array_column($this->PhpExcel->tableHeaders[0]['data'], 'label');
        $actualSampleRow = $this->PhpExcel->tableRows[0]['data'];

        $this->assertEquals($expectedModifiedRowData, array_combine($actualHeaders, $actualSampleRow));
    }

    public function testImportFileCreatesNewTerritory()
    {
        // Modify all export file fields
        $this->PhpExcel->importTableMap = [
            [
                'Territory' => 'New Territory',
                'Primary eMail' => '<EMAIL>', // Do not change
            ],
        ];

        $expectedModifiedRowData = array_merge(static::SAMPLE_EXPORT_ROW, [
            'Territory' => 'New Territory',
        ]);

        $this->post(static::IMPORT_URL, static::IMPORT_REQUEST);
        $this->assertRedirect();
        $this->assertNotEmpty($this->controller->getFlash('success'), 'Success flash message');

        // Rebuild controller to clean properties for next request
        $this->_buildMockController();

        $this->get(static::EXPORT_URL);

        $actualHeaders = array_column($this->PhpExcel->tableHeaders[0]['data'], 'label');
        $actualSampleRow = $this->PhpExcel->tableRows[0]['data'];

        $this->assertEquals($expectedModifiedRowData, array_combine($actualHeaders, $actualSampleRow));
    }

    /**
     * @dataProvider providerInvalidUserType
     * @param int $userId
     */
    public function testExportingAsInvalidUserTypeIsForbidden($userId)
    {
        $this->setExpectedException(ForbiddenException::class);

        $this->controller->syncUserSession($userId);

        $this->get(static::EXPORT_URL);
    }

    /**
     * @dataProvider providerInvalidUserType
     * @param int $userId
     */
    public function testImportingAsInvalidUserTypeIsForbidden($userId)
    {
        $this->setExpectedException(ForbiddenException::class);

        $this->controller->syncUserSession($userId);

        $this->post(static::IMPORT_URL, static::IMPORT_REQUEST);
    }

    public function providerInvalidUserType()
    {
        return [
            'Retailer' => ['15'],
            'StoreAssociate' => ['20'],
            'SalesRep' => ['21'],
        ];
    }

    public function testExportingWithNoRetailersDisplaysErrorMessage()
    {
        $this->controller->ManufacturerRetailer->deleteAllJoinless(['user_id' => static::BRAND_ID], false);

        $this->get(static::EXPORT_URL);

        $this->assertRedirect();
        $this->assertNotEmpty($this->controller->getFlash('error'), 'Error flash message');
    }

    public function testImportingWithNoFileDisplaysErrorMessage()
    {
        $this->post(static::IMPORT_URL);

        $this->assertRedirect();
        $this->assertNotEmpty($this->controller->getFlash('error'), 'Error flash message');
    }

    public function testGetImportIsNotAllowed()
    {
        $this->setExpectedException(MethodNotAllowedException::class);

        $this->get(static::IMPORT_URL);
    }

    private function getPhpExcelTableMap(): array
    {
        $actualHeaders = array_column($this->PhpExcel->tableHeaders[0]['data'], 'label');

        return array_map(function($row) use ($actualHeaders) {
            return array_combine($actualHeaders, $row['data']);
        }, $this->PhpExcel->tableRows);
    }
}
