<?php
App::uses('IntegrationTestCase', 'TestSuite');
App::uses('UsersController', 'Controller');
App::uses('StripeCapability', 'Stripe.Enum');

/**
 * UsersController Test Case.
 *
 * Run with the following console command:
 *
 * ./app/Console/cake test app Controller/Users/<USER>
 *
 * @property UsersController|PHPUnit_Framework_MockObject_MockObject $controller
 */
class UsersPayoutsTest extends IntegrationTestCase
{
    const URL = ['controller' => 'users', 'action' => 'payouts'];

    const USER_ID_BRAND = '8';
    const USER_ID_RETAILER = '7';

    public $fixtures = [
        'app.brand_staff',
        'app.brand_staff_permission',
        'app.category',
        'app.contactpersons',
        'app.manufacturer_retailer',
        'app.manufacturer_sales_rep',
        'app.staff_permission',
        'app.stripe_user',
        'app.stripe_user_capability',
        'app.user',
        'app.user_category',
    ];

    public function setUp()
    {
        parent::setUp();

        $mocksForUnnecessaryDependencies = [
            'methods' => ['_headerNotifications', '_checkCron'],
        ];
        $this->generate('Users', Hash::merge($mocksForUnnecessaryDependencies, [
            'models' => [
                'StripeUserCapability' => ['saveActiveCapabilityNames'],
                'User' => ['save'],
            ],
        ]));

        $this->controller->Auth->unauthorizedRedirect = false;
        $this->controller->Auth->login(['id' => static::USER_ID_BRAND]);
    }

    /**
     * @dataProvider providerPostPayouts
     */
    public function testPostPayouts($authId, $request, $expected)
    {
        $this->controller->Auth->login(['id' => $authId]);
        $stripeUserId = $this->controller->StripeUser->getUserField($authId, 'id') ?: null;

        /** @var StripeUserCapability|PHPUnit_Framework_MockObject_MockObject $mock */
        $mock = $this->controller->StripeUserCapability;
        if (isset($expected['StripeUser'])) {
            $mock->expects($this->once())->method('saveActiveCapabilityNames')
                ->with($stripeUserId, $expected['StripeUser']['Capabilities'])
                ->willReturn(true);
        } else {
            $mock->expects($this->never())->method('saveActiveCapabilityNames');
        }
        /** @var User|PHPUnit_Framework_MockObject_MockObject $mock */
        $mock = $this->controller->User;
        if (isset($expected['User'])) {
            $mock->expects($this->once())->method('save')
                ->with($expected['User'], ['validate' => true, 'fieldList' => ['enable_affirm_financing']])
                ->willReturn(true);
        } else {
            $mock->expects($this->never())->method('save');
        }

        $this->configRequest(['headers' => [
            'Accept' => 'application/json',
            'X-Requested-With' => 'XMLHttpRequest',
        ]]);
        $this->post(static::URL, $request);

        $this->assertResponseEquals(json_encode(['success' => 'Update successful']));
        $this->assertResponseOk();
    }

    public function providerPostPayouts(): array
    {
        return [
            'brand' => [
                'authId' => static::USER_ID_BRAND,
                'request' => [
                    'User' => [
                        'enable_affirm_financing' => '1',
                    ],
                    'StripeUser' => [
                        'Capabilities' => [
                            StripeCapability::KLARNA_PAYMENTS,
                            StripeCapability::AFFIRM_PAYMENTS,
                        ],
                    ],
                ],
                'expected' => [
                    'User' => [
                        'enable_affirm_financing' => '1',
                        'id' => static::USER_ID_BRAND,
                    ],
                    'StripeUser' => [
                        'Capabilities' => [
                            StripeCapability::KLARNA_PAYMENTS,
                            StripeCapability::AFFIRM_PAYMENTS,
                            StripeCapability::CARD_PAYMENTS,
                        ],
                    ],
                ],
            ],
            'retailer' => [
                'authId' => static::USER_ID_RETAILER,
                'request' => [
                    'StripeUser' => [
                        'Capabilities' => [
                            StripeCapability::KLARNA_PAYMENTS,
                            StripeCapability::AFFIRM_PAYMENTS,
                        ],
                    ],
                ],
                'expected' => [
                    'StripeUser' => [
                        'Capabilities' => [
                            StripeCapability::KLARNA_PAYMENTS,
                            StripeCapability::AFFIRM_PAYMENTS,
                            StripeCapability::CARD_PAYMENTS,
                        ],
                    ],
                ],
            ],
            'single_capability' => [
                'authId' => static::USER_ID_RETAILER,
                'request' => [
                    'StripeUser' => [
                        'Capabilities' => StripeCapability::KLARNA_PAYMENTS,
                    ],
                ],
                'expected' => [
                    'StripeUser' => [
                        'Capabilities' => [
                            StripeCapability::KLARNA_PAYMENTS,
                            StripeCapability::CARD_PAYMENTS,
                        ],
                    ],
                ],
            ],
            'card_only' => [
                'authId' => static::USER_ID_RETAILER,
                'request' => [
                    'StripeUser' => [
                        'Capabilities' => '',
                    ],
                ],
                'expected' => [
                    'StripeUser' => [
                        'Capabilities' => [
                            StripeCapability::CARD_PAYMENTS,
                        ],
                    ],
                ],
            ],
            'legacy_brand_form' => [
                'authId' => static::USER_ID_BRAND,
                'request' => [
                    'User' => [
                        'enable_affirm_financing' => '1',
                    ],
                ],
                'expected' => [
                    'User' => [
                        'enable_affirm_financing' => '1',
                        'id' => static::USER_ID_BRAND,
                    ],
                ],
            ],
        ];
    }

    /**
     * @dataProvider providerPostPayoutsFailure
     */
    public function testPostPayoutsFailure($mockSaveErrors)
    {
        /** @var StripeUserCapability|PHPUnit_Framework_MockObject_MockObject $mock */
        $mock = $this->controller->StripeUserCapability;
        $mock->expects($this->atMost(1))->method('saveActiveCapabilityNames')
            ->willReturn(!in_array('StripeUserCapability', $mockSaveErrors, true));
        /** @var User|PHPUnit_Framework_MockObject_MockObject $mock */
        $mock = $this->controller->User;
        $mock->expects($this->atMost(1))->method('save')
            ->willReturn(!in_array('User', $mockSaveErrors, true));

        $this->configRequest(['headers' => [
            'Accept' => 'application/json',
            'X-Requested-With' => 'XMLHttpRequest',
        ]]);
        $this->post(static::URL, [
            'User' => [
                'enable_affirm_financing' => '1',
            ],
            'StripeUser' => [
                'Capabilities' => '',
            ],
        ]);

        $this->assertResponseEquals(json_encode([
            'error' => 'An error occurred. Please, try again.',
            'name' => 'Internal Server Error',
            'message' => 'An error occurred. Please, try again.',
            'url' => $this->controller->request->here(),
        ]));
        $this->assertResponseFailure();
    }

    public function providerPostPayoutsFailure(): array
    {
        return [
            'saving_user' => [
                'mockSaveErrors' => ['User'],
            ],
            'saving_capabilities' => [
                'mockSaveErrors' => ['StripeUserCapability'],
            ],
            'saving_both' => [
                'mockSaveErrors' => ['User', 'StripeUserCapability'],
            ],
        ];
    }
}
