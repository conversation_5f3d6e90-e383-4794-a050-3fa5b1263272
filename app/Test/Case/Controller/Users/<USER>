<?php
App::uses('IntegrationTestCase', 'TestSuite');
App::uses('UsersController', 'Controller');

/**
 * UsersController::squareConnect() Test Case.
 *
 * Run with the following console command:
 *
 * ./app/Console/cake test app Controller/Users/<USER>
 *
 * @property UsersController|PHPUnit_Framework_MockObject_MockObject $controller
 * @property SquarePosComponent|PHPUnit_Framework_MockObject_MockObject $SquarePos
 *
 * @see UsersController::squareConnect()
 */
class UsersSquareConnectTest extends IntegrationTestCase
{
    const USER_ID_RETAILER = '7';
    const USER_ID_BRANCH = '17';
    const USER_ID_BRAND = '8';

    const URL = ['controller' => 'users', 'action' => 'squareConnect'];

    const CODE = 'sq0cgp-7FhzrEfuyKTWV7hOxypbmQ';
    const STATE_TOKEN = '4f2b2c830213a8e1791d65f795a4a06b7f80a565bed49cc323596743fe7a5105';

    public $fixtures = [
        'app.brand_staff',
        'app.brand_staff_permission',
        'app.category',
        'app.configuration',
        'app.contactpersons',
        'app.manufacturer_retailer',
        'app.staff',
        'app.staff_permission',
        'app.user',
        'app.user_category',
    ];

    public function setUp()
    {
        parent::setUp();

        $mocksForUnnecessaryDependencies = [
            'methods' => ['_headerNotifications', '_checkCron'],
        ];
        $this->generate('Users', Hash::merge($mocksForUnnecessaryDependencies, [
            'components' => [
                /**
                 * @see SquarePosComponent::generateOAuthStateToken()
                 * @see SquarePosComponent::getAccessToken()
                 */
                'SquarePos' => ['generateOAuthStateToken', 'getAccessToken'],
            ],
        ]));

        $this->controller->Auth->unauthorizedRedirect = false;
        $this->controller->Auth->login(['id' => static::USER_ID_RETAILER]);

        $this->SquarePos = $this->controller->SquarePos;

        $this->SquarePos->expects($this->any())->method('generateOAuthStateToken')->willReturn(static::STATE_TOKEN);
        $this->clearOauthState();

        Cache::delete('list_configurations', $this->controller->Configuration->cacheConfig);
        $this->controller->Configuration->bootstrap();
    }

    /**
     * @dataProvider providerSquareLogin
     */
    public function testSquareLogin(array $urlOptions, array $expectedState)
    {
        $this->get(array_merge(static::URL, $urlOptions));

        $this->assertResponseSuccess();

        $redirect = $this->controller->response->location();
        $this->assertStringStartsWith('https://connect.squareup.com/oauth2/authorize?', $redirect, 'Redirect URL');

        parse_str(parse_url($redirect, PHP_URL_QUERY), $queryArgs);
        $expectedQueryArgs = [
            'client_id' => SQUARE_APPLICATION_ID,
            'scope' => implode(' ', [
                'MERCHANT_PROFILE_READ',
                'CUSTOMERS_READ',
                'CUSTOMERS_WRITE',
                'ITEMS_READ',
                'ORDERS_READ',
                'INVENTORY_READ',
            ]),
            'session' => 'false',
            'state' => static::STATE_TOKEN,
        ];
        $this->assertEquals($expectedQueryArgs, $queryArgs, 'Query args');

        $state = $this->getOauthState();
        $this->assertEquals($expectedState, $state, 'OAuth state');
    }

    public function providerSquareLogin(): array
    {
        $retailerId = static::USER_ID_RETAILER;
        $branchId = static::USER_ID_BRANCH;
        $expectedExpiryTime = static::time() + (60 * 60);
        $returnUrl = $this->buildReturnUrl(['controller' => 'users', 'action' => 'inventory_settings']);

        return [
            'default' => [
                'urlOptions' => [],
                'expectedState' => [
                    'expiry_time' => $expectedExpiryTime,
                    'user_id' => (int)$retailerId,
                    'return_url' => $returnUrl,
                ],
            ],
            'with retailer_id' => [
                'urlOptions' => ['retailer_id' => $branchId],
                'expectedState' => [
                    'expiry_time' => $expectedExpiryTime,
                    'user_id' => (int)$branchId,
                    'return_url' => array_merge($returnUrl, ['id' => $branchId]),
                ],
            ],
            'with subdomain' => [
                'urlOptions' => ['subdomain' => $this->otherSubdomain()],
                'expectedState' => [
                    'expiry_time' => $expectedExpiryTime,
                    'user_id' => (int)$retailerId,
                    'return_url' => array_merge($returnUrl, ['subdomain' => $this->otherSubdomain()]),
                ],
            ],
        ];
    }

    /**
     * @dataProvider providerInvalidSquareLogin
     */
    public function testInvalidSquareLogin(array $urlOptions, string $expectedErrorFlash)
    {
        $this->get(array_merge(static::URL, $urlOptions));

        $this->assertResponseSuccess();

        $state = $this->getOauthState();
        $this->assertEquals(false, $state, 'OAuth state');

        $this->assertRedirect('/', 'Redirect URL');
        $this->assertEmpty($this->controller->getFlash('success'), 'Success flash message: ' . $this->controller->getFlash('success'));
        $this->assertEquals($expectedErrorFlash, $this->controller->getFlash('error'), 'Error flash message');
    }

    public function providerInvalidSquareLogin(): array
    {
        return [
            'invalid retailer_id' => [
                'urlOptions' => ['retailer_id' => static::USER_ID_BRAND],
                'expectedErrorFlash' => 'Location not found.',
            ],
        ];
    }

    /**
     * @dataProvider providerSquareConnect
     */
    public function testSquareConnect(array $urlOptions)
    {
        $now = static::time();
        $branchId = static::USER_ID_BRANCH;
        $returnUrl = $this->buildReturnUrl(array_merge(['controller' => 'users', 'action' => 'inventory_settings'], $urlOptions));
        $accessTokenResponse = [
            'access_token' => 'EAAAlmugrvut83Svb2g83rtekhZB-3vRjFghVSZzecWINR6ntk7mhQJVTSUqvy-x',
            'token_type' => 'bearer',
            'expires_at' => '2024-05-12T22:55:45Z',
            'merchant_id' => '5V7J744B8WSD3',
        ];
        $this->SquarePos->expects($this->once())->method('getAccessToken')->with(static::CODE)->willReturn($accessTokenResponse);

        $this->setOauthState([
            'expiry_time' => $now,
            'user_id' => $branchId,
            'return_url' => $returnUrl,
        ]);
        $this->get(array_merge(static::URL, ['subdomain' => ($urlOptions['subdomain'] ?? null), '?' => ['code' => static::CODE, 'state' => static::STATE_TOKEN]]));

        $this->assertResponseSuccess();
        $actualRecord = $this->findUserPosFields($branchId);
        $expectedRecord = [
            'User' => [
                'id' => $branchId,
                'inventory_apiuser' => $accessTokenResponse['merchant_id'],
                'inventory_password' => $accessTokenResponse['access_token'],
                'inventory_type' => 'square',
                'vend_access_token_expires' => $accessTokenResponse['expires_at'],
                'vend_refresh_access_token' => null,
            ],
        ];
        $this->assertEquals($expectedRecord, $actualRecord, 'Retailer record');
        $this->assertRedirect($returnUrl, 'Redirect URL');
        $this->assertEmpty($this->controller->getFlash('error'), 'Error flash message: ' . $this->controller->getFlash('error'));
        $this->assertEquals('Your Square account has been connected successfully.', $this->controller->getFlash('success'), 'Success flash message');
    }

    public function providerSquareConnect(): array
    {
        return [
            'default' => [
                'urlOptions' => [],
            ],
            'with retailer_id' => [
                'urlOptions' => ['id' => static::USER_ID_BRANCH],
            ],
            'with subdomain' => [
                'urlOptions' => ['subdomain' => $this->otherSubdomain()],
            ],
        ];
    }

    /**
     * @dataProvider providerInvalidOauthStateSquareConnect
     */
    public function testInvalidOauthStateSquareConnect(array $oauthState, array $accessTokenResponse, array $expectedReturnUrl, string $expectedErrorFlash)
    {
        $branchId = static::USER_ID_BRANCH;
        $this->SquarePos->expects($this->any())->method('getAccessToken')->with(static::CODE)->willReturn($accessTokenResponse);

        $this->setOauthState($oauthState);
        $this->get(array_merge(static::URL, ['?' => ['code' => static::CODE, 'state' => static::STATE_TOKEN]]));

        $this->assertResponseSuccess();
        $actualRecord = $this->findUserPosFields($branchId);
        $expectedRecord = [
            'User' => [
                'id' => $branchId,
                'inventory_apiuser' => '',
                'inventory_password' => '',
                'inventory_type' => 'other',
                'vend_access_token_expires' => null,
                'vend_refresh_access_token' => null,
            ],
        ];
        $this->assertEquals($expectedRecord, $actualRecord, 'Retailer record');
        $this->assertRedirect($expectedReturnUrl, 'Redirect URL');
        $this->assertEmpty($this->controller->getFlash('success'), 'Success flash message: ' . $this->controller->getFlash('success'));
        $this->assertEquals($expectedErrorFlash, $this->controller->getFlash('error'), 'Error flash message');
    }

    public function providerInvalidOauthStateSquareConnect(): array
    {
        $now = static::time();
        $branchId = static::USER_ID_BRANCH;
        $returnUrl = $this->buildReturnUrl(['controller' => 'users', 'action' => 'inventory_settings', 'id' => $branchId]);
        $oauthState = [
            'expiry_time' => $now,
            'user_id' => $branchId,
            'return_url' => $returnUrl,
        ];
        $accessTokenResponse = [
            'access_token' => '5ffc858bdebadd82dff1c2f02767dad76361e333',
            'expires_in' => 1800,
            'token_type' => 'bearer',
            'scope' => 'employee:all',
            'refresh_token' => 'f72a0c3f20db20ee506eb96ad9c454ba2a0e54ab',
        ];

        return [
            'expired' => [
                'oauthState' => array_merge($oauthState, ['expiry_time' => $now - 1]),
                'accessTokenResponse' => $accessTokenResponse,
                'expectedReturnUrl' => ['controller' => 'users', 'action' => 'inventory_settings'],
                'expectedErrorFlash' => 'Square authorization expired, please try again.',
            ],
            'invalid user_id' => [
                'oauthState' => array_merge($oauthState, ['user_id' => static::USER_ID_BRAND]),
                'accessTokenResponse' => $accessTokenResponse,
                'expectedReturnUrl' => $returnUrl,
                'expectedErrorFlash' => 'Location not found.',
            ],
            'invalid access_token' => [
                'oauthState' => $oauthState,
                'accessTokenResponse' => [
                    'error' => 'invalid_grant',
                    'error_description' => 'Authorization code doesn\'t exist or is invalid for the client',
                ],
                'expectedReturnUrl' => $returnUrl,
                'expectedErrorFlash' => 'Invalid Square account information.',
            ],
            'mismatched subdomain' => [
                'oauthState' => array_merge($oauthState, [
                    'return_url' => array_merge($returnUrl, ['subdomain' => $this->otherSubdomain()]),
                ]),
                'accessTokenResponse' => $accessTokenResponse,
                'expectedReturnUrl' => array_merge(static::URL, ['subdomain' => $this->otherSubdomain(), '?' => ['code' => static::CODE, 'state' => static::STATE_TOKEN]]),
                'expectedErrorFlash' => '',
            ],
        ];
    }

    private function findUserPosFields(int $userId): array
    {
        return (array)$this->controller->User->get($userId, [
            'fields' => [
                'id',
                'inventory_apiuser',
                'inventory_password',
                'inventory_type',
                'vend_access_token_expires',
                'vend_refresh_access_token',
            ],
        ]);
    }

    private function buildReturnUrl(array $url): array
    {
        return $url + array_filter(['subdomain' => $this->defaultSubdomain()]);
    }

    private function getOauthState()
    {
        return Cache::read('squarepos_oauth_' . static::STATE_TOKEN, 'short');
    }

    private function setOauthState($state): void
    {
        Cache::write('squarepos_oauth_' . static::STATE_TOKEN, $state, 'short');
    }

    private function clearOauthState(): void
    {
        Cache::delete('squarepos_oauth_' . static::STATE_TOKEN, 'short');
    }
}
