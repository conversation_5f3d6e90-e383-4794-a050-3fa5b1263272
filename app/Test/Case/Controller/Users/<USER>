<?php
App::uses('IntegrationTestCase', 'TestSuite');
App::uses('StripeComponentProvider', 'Test/Provider');
App::uses('UsersController', 'Controller');

/**
 * UsersController::stripeconnect Test Case
 *
 * Run with the following console command:
 *
 * ./app/Console/cake test app Controller/Users/<USER>
 *
 * @property UsersController $controller
 * @property StripeComponent|PHPUnit_Framework_MockObject_MockObject $Stripe
 * @property StripeUser|PHPUnit_Framework_MockObject_MockObject $StripeUser
 *
 * @see UsersController::stripeconnect()
 */
class UsersStripeConnectTest extends IntegrationTestCase
{
    const USER_ID = '8';
    const STRIPE_USER_ID = '4';
    const STRIPE_ACCOUNT = 'acct_194DCmJKdns8gzMQ';

    const URL = ['controller' => 'users', 'action' => 'stripeconnect'];

    const CODE = 'ac_IkOCkSX6PZz6dprdvnCr8Lnr0vsYOT15';
    const STATE_TOKEN = '095bb051adcd796a489dd4e1ba132380702908670474501169b3821deacee77e';
    const ACCESS_TOKEN_RESPONSE = [
        'access_token' => 'sk_test_517FVVxLBFYPhk3KpGBYykzV55mQC8IyRMeYU8IuN8fQi0M8Dwbcx7q52NeI7db5Wsp5KTOKCPy7DihPW6sIE7JTx00GZZU8c7T',
        'livemode' => false,
        'refresh_token' => 'rt_IkODAbE3PW4YqwjNEnEhY7mtr3WoIPoSaeMhs0I0r2MD57JY',
        'token_type' => 'bearer',
        'stripe_publishable_key' => 'pk_test_517FVVxLBFYPhk3KpoWAUKfYtzbzBs99JAXZ9tf6EKdP3ERGcYS8WTcg5PYfgqglW6CFJxiwy9Zwby8w12hv9FMJ000S4XgECMk',
        'stripe_user_id' => self::STRIPE_ACCOUNT,
        'scope' => 'read_write',
    ];

    public $fixtures = [
        'app.brand_staff',
        'app.brand_staff_permission',
        'app.category',
        'app.configuration',
        'app.contactpersons',
        'app.contact',
        'app.country',
        'app.manufacturer_retailer',
        'app.manufacturer_sales_rep',
        'app.order',
        'app.order_comment',
        'app.order_customer_message',
        'app.staff',
        'app.staff_permission',
        'app.state',
        'app.stripe_user',
        'app.user',
        'app.user_category',
        'app.user_subdomain',
    ];

    public function setUp()
    {
        parent::setUp();

        $mocksForUnnecessaryDependencies = [
            'methods' => ['_headerNotifications', '_checkCron'],
        ];
        $this->generate('Users', Hash::merge($mocksForUnnecessaryDependencies, [
            'models' => [
                /** @see StripeUser::connectAccount() */
                'StripeUser' => ['connectAccount'],
            ],
            'components' => [
                /**
                 * @see StripeComponent::generateOAuthStateToken()
                 * @see StripeComponent::getAccessToken()
                 * @see StripeComponent::getAccount()
                 */
                'Stripe.Stripe' => ['generateOAuthStateToken', 'getAccessToken', 'getAccount'],
            ],
        ]));

        $this->controller->Auth->unauthorizedRedirect = false;
        $this->controller->Auth->login(['id' => static::USER_ID]);

        $this->Stripe = $this->controller->Stripe;
        $this->StripeUser = $this->controller->StripeUser;

        $this->Stripe->expects($this->any())->method('generateOAuthStateToken')->willReturn(static::STATE_TOKEN);
        $this->clearOauthState();

        Cache::delete('list_configurations', $this->controller->Configuration->cacheConfig);
        $this->controller->Configuration->bootstrap();
    }

    public function tearDown()
    {
        unset(
            $this->StripeUser,
            $this->Stripe
        );

        parent::tearDown();
    }

    /**
     * @dataProvider providerGetView
     */
    public function testGetView(string $setupFunctionName, bool $expectedStripeIsActivated)
    {
        call_user_func([$this, $setupFunctionName]);

        $this->get(static::URL);

        $this->assertResponseOk();
        $this->assertEquals($expectedStripeIsActivated, $this->controller->viewVars['stripe_is_activated']);
    }

    public function providerGetView(): array
    {
        return [
            'with active stripe account' => [
                /** @see UsersStripeConnectTest::setupActiveStripeAccount() */
                'setupFunctionName' => 'setupActiveStripeAccount',
                'expectedStripeIsActivated' => true,
            ],
            'with inactive stripe account' => [
                /** @see UsersStripeConnectTest::setupInactiveStripeAccount() */
                'setupFunctionName' => 'setupInactiveStripeAccount',
                'expectedStripeIsActivated' => false,
            ],
            'without stripe account' => [
                /** @see UsersStripeConnectTest::setupMissingStripeAccount() */
                'setupFunctionName' => 'setupMissingStripeAccount',
                'expectedStripeIsActivated' => false,
            ],
            'without stripe record' => [
                /** @see UsersStripeConnectTest::setupMissingStripeRecord() */
                'setupFunctionName' => 'setupMissingStripeRecord',
                'expectedStripeIsActivated' => false,
            ],
        ];
    }

    /**
     * @dataProvider providerConnectToBank
     */
    public function testConnectToBank(string $setupFunctionName)
    {
        call_user_func([$this, $setupFunctionName]);

        // Prove that OAuth state return_url ignores external referer
        $this->configRequest(['headers' => [
            'Referer' => 'https://example.com',
        ]]);
        $this->post(static::URL);

        $redirect = $this->controller->response->location();
        $this->assertStringStartsWith('https://connect.stripe.com/oauth/authorize?', $redirect, 'Redirect URL');

        parse_str(parse_url($redirect, PHP_URL_QUERY), $queryArgs);
        $expectedQueryArgs = [
            'scope' => 'read_write',
            'state' => static::STATE_TOKEN,
            'stripe_user' => [
                'email' => '<EMAIL>',
                'country' => 'CA',
                'phone_number' => '************',
                'business_name' => 'My Local Brand',
                'street_address' => '123 Localhost St.',
                'city' => 'Thunder Bay',
                'state' => 'Ontario',
                'zip' => 'P7C 4W1',
            ],
            'client_id' => STRIPE_CLIENT_ID,
            'response_type' => 'code',
        ];
        $this->assertEquals($expectedQueryArgs, $queryArgs, 'Query args');

        $state = $this->getOauthState();
        $expectedState = [
            'expiry_time' => static::time() + (60 * 60),
            'user_id' => (int)static::USER_ID,
            'return_url' => $this->buildReturnUrl(['controller' => 'users', 'action' => 'stripeconnect']),
        ];
        $this->assertEquals($expectedState, $state, 'OAuth state');
    }

    public function providerConnectToBank(): array
    {
        return [
            'with active stripe account' => [
                /** @see UsersStripeConnectTest::setupActiveStripeAccount() */
                'setupFunctionName' => 'setupActiveStripeAccount',
            ],
            'without stripe account' => [
                /** @see UsersStripeConnectTest::setupMissingStripeAccount() */
                'setupFunctionName' => 'setupMissingStripeAccount',
            ],
            'without stripe record' => [
                /** @see UsersStripeConnectTest::setupMissingStripeRecord() */
                'setupFunctionName' => 'setupMissingStripeRecord',
            ],
        ];
    }

    public function testConnectToBankFromPayouts()
    {
        $this->setupMissingStripeRecord();

        $this->configRequest(['headers' => [
            'Referer' => Router::url(['controller' => 'users', 'action' => 'payouts'], true),
        ]]);
        $this->post(static::URL);

        $state = $this->getOauthState();
        $expectedState = [
            'expiry_time' => static::time() + (60 * 60),
            'user_id' => (int)static::USER_ID,
            'return_url' => $this->buildReturnUrl(['controller' => 'users', 'action' => 'payouts']),
        ];
        $this->assertEquals($expectedState, $state, 'OAuth state');
    }

    public function testActivateStripeAccount()
    {
        $this->setupInactiveStripeAccount();

        $this->post(static::URL);

        $redirect = $this->controller->response->location();
        $this->assertStringStartsWith('https://dashboard.stripe.com/account/activate?', $redirect, 'Redirect URL');

        parse_str(parse_url($redirect, PHP_URL_QUERY), $queryArgs);
        $expectedQueryArgs = [
            'client_id' => STRIPE_CLIENT_ID,
            'user_id' => static::STRIPE_ACCOUNT,
        ];
        $this->assertEquals($expectedQueryArgs, $queryArgs, 'Query args');

        $state = $this->getOauthState();
        $this->assertEquals(false, $state, 'OAuth state');
    }

    /**
     * @dataProvider providerOAuthRedirectConnectsStripeUser
     */
    public function testOAuthRedirectConnectsStripeUser(array $urlOptions)
    {
        $now = static::time();
        $userId = static::USER_ID;
        $returnUrl = $this->buildReturnUrl(array_merge(['controller' => 'users', 'action' => 'stripeconnect'], $urlOptions));
        $accessTokenResponse = static::ACCESS_TOKEN_RESPONSE;
        $stripeAccount = \Stripe\Account::constructFrom([
            'id' => static::STRIPE_ACCOUNT,
            'charges_enabled' => true,
            'payouts_enabled' => true,
            'details_submitted' => true,
            'email' => '<EMAIL>',
        ]);
        $this->Stripe->expects($this->once())->method('getAccessToken')->with(static::CODE)->willReturn($accessTokenResponse);
        $this->Stripe->expects($this->once())->method('getAccount')->with(static::STRIPE_ACCOUNT)->willReturn($stripeAccount);
        $this->StripeUser->expects($this->once())->method('connectAccount')->with($userId, $accessTokenResponse, $stripeAccount)->willReturn(true);

        $this->setupMissingStripeRecord();
        $this->setOauthState([
            'expiry_time' => $now,
            'user_id' => $userId,
            'return_url' => $returnUrl,
        ]);
        $this->get(array_merge(static::URL, [
            'subdomain' => ($urlOptions['subdomain'] ?? null),
            '?' => [
                'scope' => 'read_write',
                'code' => static::CODE,
                'state' => static::STATE_TOKEN,
            ],
        ]));

        $this->assertResponseSuccess();
        $this->assertRedirect($returnUrl);
        $this->assertEmpty($this->controller->getFlash('error'), 'Error flash message: ' . $this->controller->getFlash('error'));
        $this->assertEquals('Your Stripe account has been connected successfully.', $this->controller->getFlash('success'), 'Success flash message');
    }

    public function providerOAuthRedirectConnectsStripeUser(): array
    {
        return [
            'default' => [
                'urlOptions' => [],
            ],
            'with subdomain' => [
                'urlOptions' => ['subdomain' => $this->otherSubdomain()],
            ],
            'with payouts destination' => [
                'urlOptions' => ['controller' => 'users', 'action' => 'payouts'],
            ],
        ];
    }

    /**
     * @dataProvider providerInvalidOAuthRedirect
     */
    public function testInvalidOAuthRedirect(array $oauthState, callable $accessTokenResponse, array $expectedReturnUrl, string $expectedErrorFlash)
    {
        $this->Stripe->expects($this->any())->method('getAccessToken')->with(static::CODE)->willReturnCallback($accessTokenResponse);
        $this->Stripe->expects($this->never())->method('getAccount');
        $this->StripeUser->expects($this->never())->method('connectAccount');

        $this->setOauthState($oauthState);
        $this->get(array_merge(static::URL, [
            '?' => [
                'scope' => 'read_write',
                'code' => static::CODE,
                'state' => static::STATE_TOKEN,
            ],
        ]));

        $this->assertResponseSuccess();
        $this->assertRedirect($expectedReturnUrl, 'Redirect URL');
        $this->assertEmpty($this->controller->getFlash('success'), 'Success flash message: ' . $this->controller->getFlash('success'));
        $this->assertEquals($expectedErrorFlash, $this->controller->getFlash('error'), 'Error flash message');
    }

    public function providerInvalidOAuthRedirect(): array
    {
        $now = static::time();
        $userId = static::USER_ID;
        $returnUrl = $this->buildReturnUrl(['controller' => 'users', 'action' => 'payouts']);
        $oauthState = [
            'expiry_time' => $now,
            'user_id' => $userId,
            'return_url' => $returnUrl,
        ];
        $accessTokenResponse = static::ACCESS_TOKEN_RESPONSE;

        return [
            'expired' => [
                'oauthState' => array_merge($oauthState, ['expiry_time' => $now - 1]),
                'accessTokenResponse' => fn() => $accessTokenResponse,
                'expectedReturnUrl' => ['controller' => 'users', 'action' => 'stripeconnect'],
                'expectedErrorFlash' => 'Stripe authorization expired, please try again.',
            ],
            'invalid user_id' => [
                'oauthState' => array_merge($oauthState, ['user_id' => 0]),
                'accessTokenResponse' => fn() => $accessTokenResponse,
                'expectedReturnUrl' => $returnUrl,
                'expectedErrorFlash' => 'An error occurred. Please, try again.',
            ],
            'invalid code' => [
                'oauthState' => $oauthState,
                'accessTokenResponse' => function() {
                    throw StripeComponentProvider::newOAuthInvalidGrantException(static::CODE);
                },
                'expectedReturnUrl' => $returnUrl,
                'expectedErrorFlash' => 'Authorization code does not exist: ' . static::CODE,
            ],
            'mismatched subdomain' => [
                'oauthState' => array_merge($oauthState, [
                    'return_url' => array_merge($returnUrl, ['subdomain' => $this->otherSubdomain()]),
                ]),
                'accessTokenResponse' => fn() => $accessTokenResponse,
                'expectedReturnUrl' => array_merge(static::URL, [
                    'subdomain' => $this->otherSubdomain(),
                    '?' => [
                        'scope' => 'read_write',
                        'code' => static::CODE,
                        'state' => static::STATE_TOKEN,
                    ],
                ]),
                'expectedErrorFlash' => '',
            ],
        ];
    }

    public function testOAuthRedirectWithInvalidAccessToken()
    {
        $now = static::time();
        $userId = static::USER_ID;
        $returnUrl = $this->buildReturnUrl(['controller' => 'users', 'action' => 'payouts']);
        $accessTokenResponse = static::ACCESS_TOKEN_RESPONSE;
        $this->Stripe->expects($this->once())->method('getAccessToken')->with(static::CODE)->willReturn($accessTokenResponse);
        $this->Stripe->expects($this->once())->method('getAccount')->with(static::STRIPE_ACCOUNT)->willReturnCallback(function($accountId) {
            throw new \Stripe\Exception\UnknownApiErrorException("The provided key 'sk_test_JW******************V0LT' does not have access to account '{$accountId}' (or that account does not exist). Application access may have been revoked.");
        });
        $this->StripeUser->expects($this->never())->method('connectAccount');

        $this->setupMissingStripeRecord();
        $this->setOauthState([
            'expiry_time' => $now,
            'user_id' => $userId,
            'return_url' => $returnUrl,
        ]);
        $this->get(array_merge(static::URL, [
            '?' => [
                'scope' => 'read_write',
                'code' => static::CODE,
                'state' => static::STATE_TOKEN,
            ],
        ]));

        $this->assertResponseSuccess();
        $this->assertRedirect($returnUrl);
        $this->assertEmpty($this->controller->getFlash('success'), 'Success flash message: ' . $this->controller->getFlash('success'));
        $this->assertEquals('An error occurred. Please, try again.', $this->controller->getFlash('error'), 'Error flash message');
    }

    private function setupActiveStripeAccount()
    {
        return true; // No setup required
    }

    private function setupInactiveStripeAccount()
    {
        return $this->StripeUser->updateAllJoinless(['StripeUser.is_activated' => false], ['StripeUser.id' => static::STRIPE_USER_ID]);
    }

    private function setupMissingStripeAccount()
    {
        return $this->StripeUser->updateAllJoinless(['StripeUser.stripe_user_id' => null], ['StripeUser.id' => static::STRIPE_USER_ID]);
    }

    private function setupMissingStripeRecord()
    {
        return $this->StripeUser->delete(static::STRIPE_USER_ID);
    }

    private function buildReturnUrl(array $url): array
    {
        return $url + array_filter(['subdomain' => $this->defaultSubdomain()]);
    }

    private function getOauthState()
    {
        return Cache::read('stripe_oauth_' . static::STATE_TOKEN, 'short');
    }

    private function setOauthState($state): void
    {
        Cache::write('stripe_oauth_' . static::STATE_TOKEN, $state, 'short');
    }

    private function clearOauthState(): void
    {
        Cache::delete('stripe_oauth_' . static::STATE_TOKEN, 'short');
    }
}
