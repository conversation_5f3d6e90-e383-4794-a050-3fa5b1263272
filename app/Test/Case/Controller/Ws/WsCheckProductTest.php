<?php
App::uses('IntegrationTestCase', 'TestSuite');
App::uses('<PERSON>s<PERSON><PERSON><PERSON><PERSON>', 'Controller');
App::uses('WsCheckProductProvider', 'Test/Provider');
App::uses('RateOption', 'Utility');

/**
 * WsController::checkProduct Test Case
 *
 * Run with the following console command:
 *
 * ./app/Console/cake test app Controller/Ws/WsCheckProduct --stderr
 *
 * @property WsController|PHPUnit_Framework_MockObject_MockObject $controller
 * @property WsCheckProductProvider $CheckProduct
 */
class WsCheckProductTest extends IntegrationTestCase
{
    public $fixtures = [
        'app.brand_staff_permission',
        'app.collections_product',
        'app.configuration',
        'app.contact',
        'app.country',
        'app.country_tax',
        'app.i18n',
        'app.manufacturer_retailer',
        'app.order',
        'app.order_comment',
        'app.order_customer_message',
        'app.order_product',
        'app.product',
        'app.product_non_applicable_order_type',
        'app.product_state_fee',
        'app.product_title',
        'app.product_variant_option',
        'app.shipping_zone',
        'app.staff_permission',
        'app.state',
        'app.state_tax',
        'app.store',
        'app.user',
        'app.user_country_tax',
        'app.user_setting',
        'app.user_tax',
        'app.variant_option',
        'app.warehouse',
        'app.warehouse_product',
        'app.zone_tax_override',
        'app.zone_tax_override_collection',
        'app.zone_tax_override_product',
    ];

    public function __construct($name = NULL, array $data = array(), $dataName = '')
    {
        parent::__construct($name, $data, $dataName);
        $this->CheckProduct = new WsCheckProductProvider();
    }

    public function setUp()
    {
        parent::setUp();
        $this->generate('Ws', [
            'methods' => ['_extractTaxIncluded'],
            'components' => [
                'Lightspeed' => ['getAllProductInventoriesByUpc', 'getShop', 'getTaxCategory'],
                'Velofix' => ['getCoveringFranchise'],
            ],
        ]);
        /** @see WsController::_extractTaxIncluded() */
        $this->controller
            ->expects($this->any())
            ->method('_extractTaxIncluded')
            ->willReturn(false);

        $this->mockLightspeedResponses();

        /**
         * @var VelofixComponent|PHPUnit_Framework_MockObject_MockObject $Velofix
         * @see VelofixComponent::getCoveringFranchise()
         */
        $Velofix = $this->controller->Velofix;
        $Velofix
            ->expects($this->any())
            ->method('getCoveringFranchise')
            ->willReturnCallback(function(array $address): array {
                return (json_encode($address) === $this->CheckProduct->velofixRequest()['address'])
                    ? $this->CheckProduct->velofixFranchise()
                    : [];
            });
    }

    /**
     * @dataProvider requestTypesProvider
     */
    public function testShopifyRequest(callable $requestCallback, callable $responseCallback)
    {
        $request = $requestCallback();
        $expectedResponse = $responseCallback();

        $this->post('/ws/checkProduct', $request);
        $actualResponse = $this->_response->body();
        //debug(json_encode(json_decode($actualResponse), JSON_PRETTY_PRINT));

        $this->assertVelofixSelldirectResult($expectedResponse, $actualResponse);
        $this->assertResult($expectedResponse, $actualResponse);
    }

    /**
     * @dataProvider requestTypesProvider
     */
    public function testShopifySplitCartRequestIgnoresExclusiveItem(callable $requestCallback, callable $responseCallback)
    {
        $request = $requestCallback();
        $expectedResponse = $responseCallback();

        $request['items'] = $this->addSplitCartItem($request['items']);
        $request['buyDirectOnly'] = 0;

        $this->post('/ws/checkProduct', $request);
        $actualResponse = $this->_response->body();
        //debug(json_encode(json_decode($actualResponse), JSON_PRETTY_PRINT));

        $this->assertVelofixSelldirectResult($expectedResponse, $actualResponse);
        $this->assertResult($expectedResponse, $actualResponse);
    }

    /**
     * @dataProvider requestTypesProvider
     */
    public function testShopifyMsrpOffRequest(callable $requestCallback, callable $responseCallback)
    {
        $request = $requestCallback();
        $expectedResponse = $responseCallback();

        $brandId = $this->controller->User->fieldByConditions('id', ['User.uuid' => $request['token']]);
        $this->controller->UserSetting->updateAllJoinless(['UserSetting.msrp' => 0], ['UserSetting.user_id' => $brandId]);
        $expectedResponse = $this->CheckProduct->applyNonMsrpPriceToResponse($expectedResponse);

        $this->post('/ws/checkProduct', $request);
        $actualResponse = $this->_response->body();
        //debug(json_encode(json_decode($actualResponse), JSON_PRETTY_PRINT));

        $this->assertVelofixSelldirectResult($expectedResponse, $actualResponse);
        $this->assertResult($expectedResponse, $actualResponse);
    }

    public function requestTypesProvider()
    {
        return array(
            'instore stock lightspeed' => array([$this->CheckProduct, 'instorePickupRequest'], [$this->CheckProduct, 'instorePickupResponse']),
            'instore nonstock' => array([$this->CheckProduct, 'shipToStoreRequest'], [$this->CheckProduct, 'shipToStoreResponse']),
            'local_delivery stock lightspeed' => array([$this->CheckProduct, 'localDeliveryLightspeedRequest'], [$this->CheckProduct, 'localDeliveryInStockResponse']),
            'local_delivery nonstock' => array([$this->CheckProduct, 'localDeliveryNoStockRequest'], [$this->CheckProduct, 'localDeliveryNoStockResponse']),
            'velofix' => array([$this->CheckProduct, 'velofixRequest'], [$this->CheckProduct, 'velofixResponse']),
            'legacy velofix' => array([$this->CheckProduct, 'legacyVelofixRequest'], [$this->CheckProduct, 'velofixResponse']),
            'shipFromStore' => array([$this->CheckProduct, 'shipFromStoreRequest'], [$this->CheckProduct, 'shipFromStoreResponse']),
            'sellDirect' => array([$this->CheckProduct, 'shipToDoorRequest'], [$this->CheckProduct, 'shipToDoorResponse']),
            'instore stock nontaxable ancillary fee' => array([$this->CheckProduct, 'instorePickupNontaxableFeeRequest'], [$this->CheckProduct, 'instorePickupNontaxableFeeResponse']),
        );
    }

    public function testShopifyVelofixNotFound()
    {
        $request = $this->CheckProduct->shipToStoreRequest();
        $request['retailers'] = '8';

        $this->post('/ws/checkProduct', $request);
        $actualResponse = $this->_response->body();

        $this->assertNull($actualResponse);
    }

    /**
     * @dataProvider providerShippingTaxOverrideRequests
     */
    public function testShippingTaxOverrideSelectsHigherRate(callable $requestCallback)
    {
        $request = $requestCallback();

        /** @var PHPUnit_Framework_MockObject_MockObject $mock */
        $mock = $this->getMockForComponent('TaxCalculator', ['findRatesForLocation'], $this->controller->Components);
        /** @see TaxCalculatorComponent::findRatesForLocation() */
        $mock->method('findRatesForLocation')->willReturn([
            'tax_name' => 'PST',
            'tax_rate' => '0.08000',
            'shipping_tax_name' => 'HST',
            'shipping_tax_rate' => '0.00000',
            'tax_included' => false,
        ]);
        $this->controller->TaxCalculator = $mock;

        /** @var PHPUnit_Framework_MockObject_MockObject $mock */
        $mock = $this->getMockForModel('ZoneTaxOverride', ['findMaxTaxOverridesByProductId']);
        /** @see ZoneTaxOverride::findMaxTaxOverridesByProductId() */
        $mock->method('findMaxTaxOverridesByProductId')->willReturnCallback(function($zoneId, $productIds, $is_shipping_tax) {
            $overrides = [];
            if ($is_shipping_tax) {
                $overrides = [
                    $productIds[0] => ['id' => 1, 'name' => 'E-Bike Rate', 'tax_rate' => '0.0500'],
                    $productIds[1] => ['id' => 2, 'name' => 'BBQ Rate', 'tax_rate' => '0.0501'],
                ];
            }

            return $overrides;
        });
        $this->controller->ZoneTaxOverride = $mock;

        $this->post('/ws/checkProduct', $request);
        $response = json_decode($this->_response->body(), true);

        $expected = [
            'shipping_tax_name' => 'BBQ Rate',
            'shipping_tax_rate' => '0.0501',
        ];
        $actual = array_intersect_key($response, $expected);
        $this->assertEquals($expected, $actual);
    }

    /**
     * @dataProvider providerShippingTaxOverrideRequests
     */
    public function testShippingTaxOverrideFallsBackToDefaultRate(callable $requestCallback)
    {
        $request = $requestCallback();

        /** @var PHPUnit_Framework_MockObject_MockObject $mock */
        $mock = $this->getMockForComponent('TaxCalculator', ['findRatesForLocation'], $this->controller->Components);
        /** @see TaxCalculatorComponent::findRatesForLocation() */
        $mock->method('findRatesForLocation')->willReturn([
            'tax_name' => 'PST',
            'tax_rate' => '0.08000',
            'shipping_tax_name' => 'HST',
            'shipping_tax_rate' => '0.13000',
            'tax_included' => false,
        ]);
        $this->controller->TaxCalculator = $mock;

        /** @var PHPUnit_Framework_MockObject_MockObject $mock */
        $mock = $this->getMockForModel('ZoneTaxOverride', ['findMaxTaxOverridesByProductId']);
        /** @see ZoneTaxOverride::findMaxTaxOverridesByProductId() */
        $mock->method('findMaxTaxOverridesByProductId')->willReturn([]);
        $this->controller->ZoneTaxOverride = $mock;

        $this->post('/ws/checkProduct', $request);
        $response = json_decode($this->_response->body(), true);

        $expected = [
            'shipping_tax_name' => 'HST',
            'shipping_tax_rate' => '0.13000',
        ];
        if ($request['type'] === 'instore') {
            $expected = [
                'shipping_tax_name' => 'Shipping Tax',
                'shipping_tax_rate' => '0.10000000',
            ];
        }
        $actual = array_intersect_key($response, $expected);
        $this->assertEquals($expected, $actual);
    }

    /**
     * @dataProvider providerShippingTaxOverrideRequests
     */
    public function testShippingTaxOverridesIsPrioritizedOverStateTax(callable $requestCallback)
    {
        $request = $requestCallback();

        /** @var PHPUnit_Framework_MockObject_MockObject $mock */
        $mock = $this->getMockForComponent('TaxCalculator', ['findRatesForLocation'], $this->controller->Components);
        /** @see TaxCalculatorComponent::findRatesForLocation() */
        $mock->method('findRatesForLocation')->willReturn([
            'tax_name' => 'PST',
            'tax_rate' => '0.08000',
            'shipping_tax_name' => 'HST',
            'shipping_tax_rate' => '0.13000',
            'tax_included' => false,
        ]);
        $this->controller->TaxCalculator = $mock;

        /** @var PHPUnit_Framework_MockObject_MockObject $mock */
        $mock = $this->getMockForModel('ZoneTaxOverride', ['findMaxTaxOverridesByProductId']);
        /** @see ZoneTaxOverride::findMaxTaxOverridesByProductId() */
        $mock->method('findMaxTaxOverridesByProductId')->willReturnCallback(function($zoneId, $productIds, $is_shipping_tax) {
            $overrides = [];
            if ($is_shipping_tax) {
                $overrides = [
                    $productIds[0] => ['id' => 1, 'name' => 'E-Bike Rate', 'tax_rate' => '0.0500'],
                    //'HST' of '0.13000' associated with $productIds[1] should not be the selected shipping tax
                ];
            }

            return $overrides;
        });
        $this->controller->ZoneTaxOverride = $mock;

        $this->post('/ws/checkProduct', $request);
        $response = json_decode($this->_response->body(), true);

        $expected = [
            'shipping_tax_name' => 'E-Bike Rate',
            'shipping_tax_rate' => '0.0500',
        ];
        $actual = array_intersect_key($response, $expected);
        $this->assertEquals($expected, $actual);
    }

    public function providerShippingTaxOverrideRequests(): array
    {
        return [
            'instore nonstock' => [[$this->CheckProduct, 'shipToStoreRequest']],
            'local_delivery stock lightspeed' => [[$this->CheckProduct, 'localDeliveryLightspeedRequest']],
            'local_delivery nonstock' => [[$this->CheckProduct, 'localDeliveryNoStockRequest']],
            'shipFromStore' => [[$this->CheckProduct, 'shipFromStoreRequest']],
            'sellDirect' => [[$this->CheckProduct, 'shipToDoorRequest']],
        ];
    }

    /**
     * @dataProvider providerWooCommerceRequestTypes
     * @covers WsController::checkProduct
     * @covers WsController::_woocommerceList
     */
    public function testWooCommerceRequest(array $request, array $expected)
    {
        $expectedResponse = json_encode($expected);

        $this->post('/ws/checkProduct', $request);
        $actualResponse = $this->_response->body();

        $this->assertVelofixSelldirectResult($expectedResponse, $actualResponse);
        $this->assertResult($expectedResponse, $actualResponse);
    }

    public function providerWooCommerceRequestTypes(): array
    {
        return [
            'sellDirect' => [
                'request' => [
                    'app_username' => 'public_a77f1682b70522ac9e0f81d9ba2f95f3',
                    'app_password' => 'secret_45983d067c61390e1b30206fa0fd2568',
                    'code' => '{"street":", ","city":"Thunder Bay","postcode":"P7C 4W1","region":"ON","country_id":"CA"}',
                    'ids' => '[34,40]',
                    'Qty' => json_encode([34 => 2, 40 => 2]),
                    'customer' => 'c99fa20aaf6f49ddb5c2d73b251c5252',
                    'shipping' => '{"street":", ","city":"Thunder Bay","postcode":"P7C 4W1","region":"ON","country_id":"CA"}',
                    'shippingAmount' => '15.00',
                    'items' => '{"e369853df766fa44e1ed0ff613f563bd":{"product_id":34,"qty":2},"538696cb24d1fd50691fffbb6fb29454":{"product_id":40,"qty":2}}',
                    'address' => '{"First_name":"Aron","Last_name":"Schmidt","company":"ShipEarly","address":"2400 Nipigon Rd.","address2":"","city":"Thunder Bay","country":"39","province":"611","PostalCode":"P7C 4W1","phone":"8075551234","email":"<EMAIL>"}',
                    'geopoints' => '{"lat":"48.407966","lng":"-89.256571"}',
                    'type' => 'sellDirect',
                    'buyDirectOnly' => 1,
                    'shopTaxOption' => false,
                    'shippingTotalTax' => 1.5,
                    'totalTax' => 10.498000000000001108446667785756289958953857421875,
                    'discounts' => '{"34":{"id":34,"discount_amount":"20.00","discount_quantity":2,"retailers":[7,17]},"40":{"id":40,"discount_amount":"10.00","discount_quantity":2,"retailers":[7,17]}}',
                    'brand' => '19',
                    'productTaxes' => 8.998000000000001108446667785756289958953857421875,
                ],
                'expected' => [
                    'password' => '*****',
                    'id' => '19',
                    'formatAddress' => '<b>WooCommerce Brand</b><br/>2400 Nipigon Rd.<br/>Thunder Bay, Ontario<br/>P7C 4W1<br/>Ph: ************',
                    'state' => 'Ontario',
                    'product' => [
                        34 => [
                            'id' => '48',
                            'inventoryId' => '34',
                            'inventory' => 1980,
                            'weight' => 0.0,
                            'currency' => 'CAD',
                            'isTax' => true,
                            'tax_included' => false,
                            'taxcode' => '',
                            'qty' => 2,
                            'unformatedunitprice' => '40.00',
                            'unitprice' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 40.00</span>',
                            'unformatedprice' => '80.00',
                            'price' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 80.00</span>',
                            'unformatedunitpricediscount' => '30.00',
                            'unitpricediscount' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 30.00</span>',
                            'unformatedpricediscount' => '60.00',
                            'pricediscount' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 60.00</span>',
                        ],
                        40 => [
                            'id' => '53',
                            'inventoryId' => '40',
                            'inventory' => 199,
                            'weight' => 0.0,
                            'currency' => 'CAD',
                            'isTax' => true,
                            'tax_included' => false,
                            'taxcode' => '',
                            'qty' => 2,
                            'unformatedunitprice' => '19.99',
                            'unitprice' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 19.99</span>',
                            'unformatedprice' => '39.98',
                            'price' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 39.98</span>',
                            'unformatedunitpricediscount' => '14.99',
                            'unitpricediscount' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 14.99</span>',
                            'unformatedpricediscount' => '29.98',
                            'pricediscount' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 29.98</span>',
                        ],
                    ],
                    'currency' => 'CAD',
                    'price' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 119.98</span>',
                    'priceformat' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 119.98</span>',
                    'pricediscount' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 89.98</span>',
                    'priceformatdiscount' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 89.98</span>',
                    'shippingAmount' => '15.00',
                    'shippingAmountFormat' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 15.00</span>',
                    'shippingDiscount' => '0.00',
                    'shippingDiscountFormat' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 0.00</span>',
                    'shippingAmountDiscounted' => '15.00',
                    'shippingAmountDiscountedFormat' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 15.00</span>',
                    'shipping_tax_option' => false,
                    'shipping_tax_name' => 'Shipping Tax',
                    'shipping_tax_rate' => '0.00000',
                    'taxamt' => '10.50',
                    'taxamtdiscount' => '10.50',
                    'shippingTax' => '0.00',
                    'taxamtwithShipping' => '10.50',
                    'totalproductamount' => '119.98',
                    'totalamount' => '130.48',
                    'taxamtwithShippingDiscount' => '10.50',
                    'totalproductamountdiscount' => '89.98',
                    'totalamountdiscount' => '100.48',
                    'discountAmount' => '30.00',
                    'discountAmountFormat' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 30.00</span>',
                    'tax_included' => false,
                    'taxformatwithShipping' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 10.50</span>',
                    'totalwithshipping' => '145.48',
                    'taxformatwithShippingDiscount' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 10.50</span>',
                    'totalwithshippingdiscount' => '115.48',
                    'totalformat' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 130.48</span>',
                    'totalwithshippingformat' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 145.48</span>',
                    'totalformatdiscount' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 100.48</span>',
                    'totalwithshippingformatdiscount' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>CAD 115.48</span>',
                ],
            ],
        ];
    }

    public function testLocalDeliveryPercentageWithPartialPosResponse()
    {
        /** @var LightspeedComponent|PHPUnit_Framework_MockObject_MockObject $Lightspeed */
        $Lightspeed = $this->getMock(LightspeedComponent::class, ['getAllProductInventoriesByUpc', 'getShop', 'getTaxCategory'], [$this->controller->Components]);
        $this->controller->Components->set('Lightspeed', $Lightspeed);
        $this->controller->Components->enable('Lightspeed');
        $this->controller->Components->init($this->controller);

        // Mock Lightspeed having only 1 of 2 items available
        $this->mockLightspeedResponses([
            'getAllProductInventoriesByUpc' => new SimpleXMLElement(<<<'XML'
<?xml version="1.0"?>
<Items count="1" offset="0" limit="100">
    <Item>
        <itemID>76</itemID>
        <tax>true</tax>
        <description>Bicycle</description>
        <upc>9090909090909</upc>
        <taxClassID>11</taxClassID>
        <ItemShops>
            <ItemShop>
                <itemShopID>125</itemShopID>
                <qoh>700</qoh>
                <itemID>76</itemID>
                <shopID>1</shopID>
            </ItemShop>
        </ItemShops>
        <Prices>
            <ItemPrice>
                <amount currency="CAD">999</amount>
                <useTypeID>1</useTypeID>
                <useType readonly="true">Default</useType>
            </ItemPrice>
        </Prices>
    </Item>
</Items>
XML
            ),
        ]);

        // Include both products in the local delivery subtotal
        $this->controller->Product->save([
            'id' => '23',
            'assembly_option' => true,
        ]);
        // POS price of 999.00 for the available item
        // Brand MSRP of 399.99 for the missing item
        $this->controller->UserSetting->save([
            'id' => '3',
            'msrp' => false,
        ]);

        $this->controller->User->save([
            'id' => '8',
            'local_delivery_shipping' => '0.10',
            'local_delivery_shipping_option' => RateOption::PERCENT,
        ]);
        $this->post('/ws/checkProduct', $this->CheckProduct->localDeliveryLightspeedRequest());
        $actual = $this->_response->body();

        $expected = format_number(( (999.00 * 2) + (399.99 * 2) ) * 0.10);
        $this->assertAttributeEquals($expected, 'shippingAmount_localdelivery', json_decode($actual));
    }

    private function mockLightspeedResponses(array $methodResponses = [])
    {
        /** @var LightspeedComponent|PHPUnit_Framework_MockObject_MockObject $Lightspeed */
        $Lightspeed = $this->controller->Lightspeed;
        $Lightspeed
            ->expects($this->any())
            ->method('getAllProductInventoriesByUpc')
            ->willReturn($methodResponses['getAllProductInventoriesByUpc'] ?? new SimpleXMLElement(<<<'XML'
<?xml version="1.0"?>
<Items count="3" offset="0" limit="100"><Item><itemID>87</itemID><systemSku readonly="true">210000000091</systemSku><defaultCost currency="CAD">0</defaultCost><avgCost currency="CAD">0</avgCost><discountable>false</discountable><tax>true</tax><archived>false</archived><itemType>default</itemType><serialized>false</serialized><description>Retail Exclusive Product</description><modelYear>0</modelYear><upc>0707070707070</upc><ean /><customSku /><manufacturerSku>Retail Exclusive</manufacturerSku><createTime>2016-01-07T17:07:28+00:00</createTime><timeStamp>2016-06-22T16:47:12+00:00</timeStamp><categoryID>0</categoryID><taxClassID>4</taxClassID><departmentID>0</departmentID><itemMatrixID>0</itemMatrixID><manufacturerID>10</manufacturerID><seasonID>0</seasonID><defaultVendorID>0</defaultVendorID><ItemShops><ItemShop><itemShopID>180</itemShopID><qoh>897</qoh><sellable readonly="true">897</sellable><backorder>0</backorder><componentQoh>0</componentQoh><componentBackorder>0</componentBackorder><reorderPoint>0</reorderPoint><reorderLevel>0</reorderLevel><timeStamp>2017-09-01T16:17:54+00:00</timeStamp><itemID>87</itemID><shopID>1</shopID></ItemShop><ItemShop><itemShopID>181</itemShopID><qoh>993</qoh><sellable readonly="true">993</sellable><backorder>0</backorder><componentQoh>0</componentQoh><componentBackorder>0</componentBackorder><reorderPoint>0</reorderPoint><reorderLevel>0</reorderLevel><timeStamp>2016-06-22T16:47:12+00:00</timeStamp><itemID>87</itemID><shopID>2</shopID></ItemShop><ItemShop><itemShopID>179</itemShopID><qoh>1890</qoh><sellable readonly="true">1890</sellable><backorder>0</backorder><componentQoh>0</componentQoh><componentBackorder>0</componentBackorder><reorderPoint>0</reorderPoint><reorderLevel>0</reorderLevel><timeStamp>2017-09-01T16:17:54+00:00</timeStamp><itemID>87</itemID><shopID>0</shopID></ItemShop></ItemShops><Prices><ItemPrice><amount currency="CAD">399.99</amount><useTypeID>1</useTypeID><useType readonly="true">Default</useType></ItemPrice><ItemPrice><amount currency="CAD">459.99</amount><useTypeID>2</useTypeID><useType readonly="true">MSRP</useType></ItemPrice><ItemPrice><amount currency="CAD">459.99</amount><useTypeID>3</useTypeID><useType readonly="true">Custom MSRP</useType></ItemPrice></Prices></Item><Item><itemID>76</itemID><systemSku readonly="true">210000000077</systemSku><defaultCost currency="CAD">0</defaultCost><avgCost currency="CAD">0</avgCost><discountable>true</discountable><tax>true</tax><archived>false</archived><itemType>default</itemType><serialized>false</serialized><description>Bicycle</description><modelYear>0</modelYear><upc>9090909090909</upc><ean /><customSku /><manufacturerSku /><createTime>2015-12-09T17:39:19+00:00</createTime><timeStamp>2020-07-23T18:21:07+00:00</timeStamp><categoryID>0</categoryID><taxClassID>11</taxClassID><departmentID>0</departmentID><itemMatrixID>0</itemMatrixID><manufacturerID>0</manufacturerID><seasonID>0</seasonID><defaultVendorID>0</defaultVendorID><ItemShops><ItemShop><itemShopID>125</itemShopID><qoh>700</qoh><sellable readonly="true">700</sellable><backorder>0</backorder><componentQoh>0</componentQoh><componentBackorder>0</componentBackorder><reorderPoint>0</reorderPoint><reorderLevel>0</reorderLevel><timeStamp>2019-07-10T20:04:04+00:00</timeStamp><itemID>76</itemID><shopID>1</shopID></ItemShop><ItemShop><itemShopID>126</itemShopID><qoh>477</qoh><sellable readonly="true">477</sellable><backorder>0</backorder><componentQoh>0</componentQoh><componentBackorder>0</componentBackorder><reorderPoint>0</reorderPoint><reorderLevel>0</reorderLevel><timeStamp>2016-08-04T19:46:04+00:00</timeStamp><itemID>76</itemID><shopID>2</shopID></ItemShop><ItemShop><itemShopID>124</itemShopID><qoh>1177</qoh><sellable readonly="true">1177</sellable><backorder>0</backorder><componentQoh>0</componentQoh><componentBackorder>0</componentBackorder><reorderPoint>0</reorderPoint><reorderLevel>0</reorderLevel><timeStamp>2019-07-10T20:04:04+00:00</timeStamp><itemID>76</itemID><shopID>0</shopID></ItemShop></ItemShops><Prices><ItemPrice><amount currency="CAD">999</amount><useTypeID>1</useTypeID><useType readonly="true">Default</useType></ItemPrice><ItemPrice><amount currency="CAD">499.99</amount><useTypeID>2</useTypeID><useType readonly="true">MSRP</useType></ItemPrice><ItemPrice><amount currency="CAD">499.99</amount><useTypeID>3</useTypeID><useType readonly="true">Custom MSRP</useType></ItemPrice></Prices></Item><Item><itemID>138</itemID><systemSku readonly="true">210000000142</systemSku><defaultCost currency="CAD">0</defaultCost><avgCost currency="CAD">0</avgCost><discountable>true</discountable><tax>true</tax><archived>false</archived><itemType>default</itemType><serialized>false</serialized><description>Bell Bronze Medium</description><modelYear>0</modelYear><upc>112345123455</upc><ean /><customSku /><manufacturerSku /><createTime>2023-04-18T17:39:19+00:00</createTime><timeStamp>2023-04-18T17:39:19+00:00</timeStamp><categoryID>0</categoryID><taxClassID>11</taxClassID><departmentID>0</departmentID><itemMatrixID>0</itemMatrixID><manufacturerID>0</manufacturerID><seasonID>0</seasonID><defaultVendorID>0</defaultVendorID><ItemShops><ItemShop><itemShopID>182</itemShopID><qoh>293</qoh><sellable readonly="true">293</sellable><backorder>0</backorder><componentQoh>0</componentQoh><componentBackorder>0</componentBackorder><reorderPoint>0</reorderPoint><reorderLevel>0</reorderLevel><timeStamp>2023-04-18T17:39:19+00:00</timeStamp><itemID>138</itemID><shopID>1</shopID></ItemShop><ItemShop><itemShopID>183</itemShopID><qoh>204</qoh><sellable readonly="true">204</sellable><backorder>0</backorder><componentQoh>0</componentQoh><componentBackorder>0</componentBackorder><reorderPoint>0</reorderPoint><reorderLevel>0</reorderLevel><timeStamp>2023-04-18T17:39:19+00:00</timeStamp><itemID>138</itemID><shopID>2</shopID></ItemShop><ItemShop><itemShopID>184</itemShopID><qoh>10</qoh><sellable readonly="true">10</sellable><backorder>0</backorder><componentQoh>0</componentQoh><componentBackorder>0</componentBackorder><reorderPoint>0</reorderPoint><reorderLevel>0</reorderLevel><timeStamp>2023-04-18T17:39:19+00:00</timeStamp><itemID>138</itemID><shopID>0</shopID></ItemShop></ItemShops><Prices><ItemPrice><amount currency="CAD">9.99</amount><useTypeID>1</useTypeID><useType readonly="true">Default</useType></ItemPrice><ItemPrice><amount currency="CAD">9.99</amount><useTypeID>2</useTypeID><useType readonly="true">MSRP</useType></ItemPrice><ItemPrice><amount currency="CAD">9.99</amount><useTypeID>3</useTypeID><useType readonly="true">Custom MSRP</useType></ItemPrice></Prices></Item></Items>
XML
            ));
        $Lightspeed
            ->expects($this->any())
            ->method('getShop')
            ->willReturn($methodResponses['getShop'] ?? new SimpleXMLElement(<<<'XML'
<?xml version="1.0"?>
<Shop><shopID>1</shopID><name>Dax Shop</name><serviceRate currency="CAD">0</serviceRate><timeZone>US/Eastern</timeZone><taxLabor>false</taxLabor><labelTitle>Shop Name</labelTitle><labelMsrp>false</labelMsrp><archived>false</archived><timeStamp>2019-08-13T14:31:03+00:00</timeStamp><companyRegistrationNumber/><vatNumber/><zebraBrowserPrint>false</zebraBrowserPrint><contactID>3</contactID><taxCategoryID>7</taxCategoryID><receiptSetupID>1</receiptSetupID><ccGatewayID>4</ccGatewayID><gatewayConfigID/><priceLevelID>1</priceLevelID><PriceLevel><priceLevelID>1</priceLevelID><name>Default</name><archived>false</archived><system>true</system></PriceLevel></Shop>
XML
            ));
        $Lightspeed
            ->expects($this->any())
            ->method('getTaxCategory')
            ->willReturn($methodResponses['getTaxCategory'] ?? new SimpleXMLElement(<<<'XML'
<?xml version="1.0"?>
<TaxCategory><taxCategoryID>7</taxCategoryID><isTaxInclusive>false</isTaxInclusive><tax1Name>Ontario HST</tax1Name><tax2Name/><tax1Rate>0.13</tax1Rate><tax2Rate>0</tax2Rate><timeStamp>2016-08-14T04:14:04+00:00</timeStamp><TaxCategoryClasses><TaxCategoryClass><taxCategoryClassID>11</taxCategoryClassID><tax1Rate>0.5</tax1Rate><tax2Rate>0</tax2Rate><timeStamp>2019-08-06T19:50:46+00:00</timeStamp><taxCategoryID>7</taxCategoryID><taxClassID>6</taxClassID><TaxClass><taxClassID>6</taxClassID><name>Super High</name><timeStamp>2014-08-20T03:25:54+00:00</timeStamp></TaxClass></TaxCategoryClass></TaxCategoryClasses></TaxCategory>
XML
            ));
        return $Lightspeed;
    }

    /**
     * @param string $itemsJson
     * @return string $itemsJson with the split cart item added
     */
    protected function addSplitCartItem($itemsJson)
    {
        $itemsJson = json_decode($itemsJson, true);
        $itemsJson[] = json_decode($this->CheckProduct->splitCartItem(), true);
        return json_encode($itemsJson);
    }

    protected function assertVelofixSelldirectResult($expectedJson, $actualJson)
    {
        list($expectedResult, $actualResult) = array_map(function($json) { return json_decode($json, true); }, array($expectedJson, $actualJson));
        if (array_key_exists('velofix_selldirect_result', $expectedResult)) {
            $expected = json_decode($expectedResult['velofix_selldirect_result'], true);
            if (array_key_exists('id', $expected)) {
                $keep = array_intersect_key($expected, array_flip(['password', 'company_name', 'address', 'address1', 'address2', 'city', 'zipcode', 'state_id', 'country_id']));
                $brand = array_merge($this->controller->User->get($expected['id'])['User'], $keep);
                $expected = $brand + $expected;
            }
            $expectedVelofixJson = json_encode($expected);

            $actualVelofixJson = $actualResult['velofix_selldirect_result'];

            //debug(json_encode(json_decode($actualVelofixJson), JSON_PRETTY_PRINT));
            $this->assertJsonStringEqualsJsonString($expectedVelofixJson, $actualVelofixJson, "Failed asserting that 'velofix_selldirect_result' is correct.");
        }
    }

    protected function assertResult($expectedJson, $actualJson)
    {
        $expected = json_decode($expectedJson, true);
        if (array_key_exists('id', $expected)) {
            $keep = array_intersect_key($expected, array_flip(['password']));
            $brand = array_merge($this->controller->User->get($expected['id'])['User'], $keep);
            $expected = $brand + $expected;
        }
        $expectedJson = json_encode($expected);

        list($expectedJson, $actualJson) = array_map(function($resultJson) {
            $result = json_decode($resultJson, true);
            if (!empty($result['velofix_selldirect_result'])) {
                $result['velofix_selldirect_result'] = '[ignoring content]';
            }
            return json_encode($result);
        }, [$expectedJson, $actualJson]);

        //debug(json_encode(json_decode($actualJson), JSON_PRETTY_PRINT));
        $this->assertJsonStringEqualsJsonString($expectedJson, $actualJson);
    }
}
