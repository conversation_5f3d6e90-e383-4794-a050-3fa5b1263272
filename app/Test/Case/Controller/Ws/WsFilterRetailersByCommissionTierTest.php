<?php
App::uses('IntegrationTestCase', 'TestSuite');
App::uses('Ws<PERSON><PERSON>roller', 'Controller');

/**
 * WsController::filterRetailersByCommissionTier Test Case
 *
 * Run with the following console command:
 *
 * ./app/Console/cake test app Controller/Ws/WsFilterRetailersByCommissionTier
 *
 * @property WsController|PHPUnit_Framework_MockObject_MockObject $controller
 *
 * @see WsController::filterRetailersByCommissionTier()
 */
class WsFilterRetailersByCommissionTierTest extends IntegrationTestCase
{
    public $fixtures = [
        'app.product_tier'
    ];

    public function setUp()
    {
        parent::setUp();
        $this->generate('Ws');
    }

    /**
     * @dataProvider providerFilterRetailersByCommissionTier
     */
    public function testFilterRetailersByCommissionTier(array $storeConnections, array $productIds, array $expected)
    {
        $actual = $this->callPrivateMethod([$this->controller, 'filterRetailersByCommissionTier'], $storeConnections, $productIds);

        $this->assertEquals($expected, $actual);
    }

    public function providerFilterRetailersByCommissionTier(): array
    {
        $storeConnections = [
            7 => [
                'retailer_id' => '7',
                'pricingtierid' => '1',
                'is_commission_tier' => false,
            ],
            10 => [
                'retailer_id' => '10',
                'pricingtierid' => '1',
                'is_commission_tier' => true,
            ],
            17 => [
                'retailer_id' => '17',
                'pricingtierid' => '2',
                'is_commission_tier' => true,
            ],
        ];
        $productIds = ['20', '21', '23'];

        return [
            'valid' => [
                'storeConnections' => $storeConnections,
                'productIds' => $productIds,
                'expected' => [10 => '10', 17 => '17'],
            ],
            'no_valid_retailers' => [
                'storeConnections' => [
                    7 => [
                        'retailer_id' => '7',
                        'pricingtierid' => null,
                        'is_commission_tier' => false,
                    ],
                    10 => [
                        'retailer_id' => '10',
                        'pricingtierid' => null,
                        'is_commission_tier' => true,
                    ],
                    17 => [
                        'retailer_id' => '17',
                        'pricingtierid' => '1',
                        'is_commission_tier' => false,
                    ],
                ],
                'productIds' => $productIds,
                'expected' => [],
            ],
            'no_valid_pricing_tiers' => [
                'storeConnections' => [
                    7 => [
                        'retailer_id' => '7',
                        'pricingtierid' => null,
                        'is_commission_tier' => true,
                    ],
                    10 => [
                        'retailer_id' => '10',
                        'pricingtierid' => '-1',
                        'is_commission_tier' => true,
                    ],
                ],
                'productIds' => $productIds,
                'expected' => [],
            ],
            'no_commission_values' => [
                'storeConnections' => $storeConnections,
                'productIds' => ['20'],
                'expected' => [],
            ],
        ];
    }
}
