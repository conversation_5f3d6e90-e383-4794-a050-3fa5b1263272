<?php
App::uses('IntegrationTestCase', 'TestSuite');
App::uses('Ws<PERSON><PERSON>roller', 'Controller');

/**
 * WsController::_formatGetRetailerInputs Test Case
 *
 * Run with the following console command:
 *
 * ./app/Console/cake test app Controller/Ws/WsFormatGetRetailerInputs
 *
 * @property WsController|PHPUnit_Framework_MockObject_MockObject $controller
 */
class WsFormatGetRetailerInputsTest extends IntegrationTestCase
{
    public function setUp()
    {
        parent::setUp();
        $this->generate('Ws');
    }

    public function testFormatGetRetailerInputs()
    {
        $data = [
            'items' => json_encode([
                [
                    'id' => (int) 20016274759,
                    'properties' => null,
                    'quantity' => (int) 2,
                    'variant_id' => (int) 20016274759,
                    'key' => '20016274759:45a63f853daad116d59153d210f4fbb2',
                    'title' => 'Super Bike - Blue',
                    'price' => (int) 39999,
                    'original_price' => (int) 39999,
                    'discounted_price' => (int) 39999,
                    'line_price' => (int) 79998,
                    'original_line_price' => (int) 79998,
                    'total_discount' => (int) 0,
                    'discounts' => [],
                    'sku' => 'BICYCLE-2',
                    'grams' => (int) 4536,
                    'vendor' => 'aron-shipearly',
                    'product_id' => (int) 5572298951,
                    'gift_card' => false,
                    'url' => '/products/super-bike?variant=20016274759',
                    'image' => null,
                    'handle' => 'super-bike',
                    'requires_shipping' => true,
                    'product_type' => 'Bikes',
                    'product_title' => 'Super Bike',
                    'product_description' => '',
                    'variant_title' => 'Blue',
                    'variant_options' => ['Blue'],
                    'compare_at_price' => 120000,
                ], [
                    'id' => (int) 17532934023,
                    'properties' => null,
                    'quantity' => (int) 2,
                    'variant_id' => (int) 17532934023,
                    'key' => '17532934023:4e1ff7977e78345c262af31534df7b36',
                    'title' => 'Super Bike - Red',
                    'price' => (int) 120000,
                    'original_price' => (int) 120000,
                    'discounted_price' => (int) 120000,
                    'line_price' => (int) 240000,
                    'original_line_price' => (int) 240000,
                    'total_discount' => (int) 0,
                    'discounts' => [],
                    'sku' => 'BICYCLE-1',
                    'grams' => (int) 4536,
                    'vendor' => 'aron-shipearly',
                    'product_id' => (int) 5572298951,
                    'gift_card' => false,
                    'url' => '/products/super-bike?variant=17532934023',
                    'image' => null,
                    'handle' => 'super-bike',
                    'requires_shipping' => true,
                    'product_type' => 'Bikes',
                    'product_title' => 'Super Bike',
                    'product_description' => '',
                    'variant_title' => 'Red',
                    'variant_options' => ['Red'],
                    'compare_at_price' => null,
                ],
            ]),
            'address' => json_encode([
                'First_name' => 'Aron',
                'Last_name' => 'Schmidt',
                'company' => 'ShipEarly',
                'address' => '2400 Nipigon Rd.',
                'address2' => '',
                'city' => 'Thunder Bay',
                'country' => '39',
                'province' => '611',
                'PostalCode' => 'P7C 4W1',
                'phone' => '1231231234',
                'email' => '<EMAIL>',
                'regionName' => 'Ontario',
                'regionCode' => 'on',
                'countryName' => 'ca'
            ]),
            'customer' => '',
        ];

        $actual = $this->controller->_formatGetRetailerInputs($data);

        $shipping = json_encode([
            'street' => '2400 Nipigon Rd.',
            'city' => 'Thunder Bay',
            'postcode' => 'P7C 4W1',
            'region' => 'Ontario',
            'country_id' => 'ca',
        ]);
        $expected = array_merge($data, [
            'ids' => json_encode([
                0 => 20016274759,
                1 => 17532934023,
            ]),
            'Qty' => json_encode([
                '20016274759' => 2,
                '17532934023' => 2,
            ]),
            'items' => json_encode([
                '20016274759:45a63f853daad116d59153d210f4fbb2' => [
                    'product_id' => 20016274759,
                    'qty' => 2,
                    'price' => '399.99',
                    'compare_at_price' => '1200.00',
                ],
                '17532934023:4e1ff7977e78345c262af31534df7b36' => [
                    'product_id' => 17532934023,
                    'qty' => 2,
                    'price' => '1200.00',
                    'compare_at_price' => null,
                ],
            ]),
            'shipping' => $shipping,
            'code' => $shipping,
            'customerID' => $data['customer'],
        ]);

        $this->assertEquals($expected, $actual);
    }
}
