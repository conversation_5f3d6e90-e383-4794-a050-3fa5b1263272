<?php
App::uses('IntegrationTestCase', 'TestSuite');
App::uses('Ws<PERSON><PERSON>roll<PERSON>', 'Controller');
App::uses('ShopifyComponentProvider', 'Test/Provider');
App::uses('ShopifyApiException', 'Shopify.Error');

/**
 * WsController Test Case.
 *
 * Run with the following console command:
 *
 * ./app/Console/cake test app Controller/Ws/WsSyncShopifyCollections
 *
 * @property WsController|PHPUnit_Framework_MockObject_MockObject $controller
 *
 * @see WsController::_syncShopifyCollections()
 */
class WsSyncShopifyCollectionsTest extends IntegrationTestCase
{
    const USER_ID_BRAND = '12';

    public $fixtures = [
    ];

    public function setUp()
    {
        parent::setUp();

        $mocksForUnnecessaryDependencies = [
            'methods' => ['_headerNotifications', '_checkCron'],
            'models' => ['Collection' => ['initialize']],
        ];
        $this->generate('Ws', Hash::merge($mocksForUnnecessaryDependencies, [
            'models' => [
                'Collection' => ['syncAllShopifyCollections'],
            ],
            'components' => [
                'Shopify.Shopify' => [],
            ],
        ]));

        $this->controller->Auth->unauthorizedRedirect = false;
    }

    public function testSyncShopifyCollectionsSuccess()
    {
        $userId = static::USER_ID_BRAND;
        $apiKey = ShopifyComponentProvider::API_KEY;
        $secretKey = ShopifyComponentProvider::SECRET_KEY;
        $shopUrl = ShopifyComponentProvider::SHOP_DOMAIN;
        $collections = ShopifyComponentProvider::getCustomCollectionApiResponse($shopUrl);
        $collects = ShopifyComponentProvider::getCollectsApiResponse($shopUrl);

        /** @var PHPUnit_Framework_MockObject_MockObject $mock */
        $mock = $this->controller->Shopify;
        $mock->expects($this->once())->method('getAllPaginatedCustomCollections')
            ->with($secretKey, $shopUrl, $this->anything())
            ->willReturn($collections);
        $mock->expects($this->once())->method('getAllPaginatedCollects')
            ->with($secretKey, $shopUrl, $this->anything())
            ->willReturn($collects);

        /** @var PHPUnit_Framework_MockObject_MockObject $mock */
        $mock = $this->controller->Collection;
        $mock->expects($this->once())->method('syncAllShopifyCollections')
            ->with((int)$userId, $collections, $collects)
            ->willReturn(true);

        $this->callPrivateMethod([$this->controller, '_syncShopifyCollections'], $userId, $apiKey, $secretKey, $shopUrl);
    }

    public function testSyncShopifyCollectionsException()
    {
        $userId = static::USER_ID_BRAND;
        $apiKey = ShopifyComponentProvider::API_KEY;
        $secretKey = ShopifyComponentProvider::SECRET_KEY;
        $shopUrl = ShopifyComponentProvider::SHOP_DOMAIN;

        /** @var PHPUnit_Framework_MockObject_MockObject $mock */
        $mock = $this->controller->Shopify;
        $mock->expects($this->once())->method('getAllPaginatedCustomCollections')
            ->with($secretKey, $shopUrl, $this->anything())
            ->willReturnCallback(function($secretKey, $shopUrl, $query) {
                throw ShopifyApiException::fromCurlException(
                    new CurlException('Connection timed out after 30001 milliseconds', 28),
                    'GET',
                    "https://{$shopUrl}/admin/api/{$this->controller->Shopify->apiVersion}/custom_collections.json",
                    $query + ['limit' => 250],
                    ['Content-Type: application/json; charset=utf-8', 'X-Shopify-Access-Token: ' . ('*****' . substr($secretKey, -4))]
                );
            });
        $mock->expects($this->never())->method('getAllPaginatedCollects');

        /** @var PHPUnit_Framework_MockObject_MockObject $mock */
        $mock = $this->controller->Collection;
        $mock->expects($this->never())->method('syncAllShopifyCollections');

        $this->callPrivateMethod([$this->controller, '_syncShopifyCollections'], $userId, $apiKey, $secretKey, $shopUrl);
    }

    public function testSyncShopifyCollectsException()
    {
        $userId = static::USER_ID_BRAND;
        $apiKey = ShopifyComponentProvider::API_KEY;
        $secretKey = ShopifyComponentProvider::SECRET_KEY;
        $shopUrl = ShopifyComponentProvider::SHOP_DOMAIN;
        $collections = ShopifyComponentProvider::getCustomCollectionApiResponse($shopUrl);

        /** @var PHPUnit_Framework_MockObject_MockObject $mock */
        $mock = $this->controller->Shopify;
        $mock->expects($this->once())->method('getAllPaginatedCustomCollections')
            ->with($secretKey, $shopUrl, $this->anything())
            ->willReturn($collections);
        $mock->expects($this->once())->method('getAllPaginatedCollects')
            ->with($secretKey, $shopUrl, $this->anything())
            ->willReturnCallback(function($secretKey, $shopUrl, $query) {
                throw ShopifyApiException::fromCurlException(
                    new CurlException('Connection timed out after 30001 milliseconds', 28),
                    'GET',
                    "https://{$shopUrl}/admin/api/{$this->controller->Shopify->apiVersion}/collects.json",
                    $query + ['limit' => 250],
                    ['Content-Type: application/json; charset=utf-8', 'X-Shopify-Access-Token: ' . ('*****' . substr($secretKey, -4))]
                );
            });

        /** @var PHPUnit_Framework_MockObject_MockObject $mock */
        $mock = $this->controller->Collection;
        $mock->expects($this->never())->method('syncAllShopifyCollections');

        $this->callPrivateMethod([$this->controller, '_syncShopifyCollections'], $userId, $apiKey, $secretKey, $shopUrl);
    }
}
