<?php

use ShipEarlyApp\Test\Support\FakeAppController;

App::uses('IntegrationTestCase', 'TestSuite');
App::uses('AfterShipComponent', 'Controller/Component');

/**
 * AfterShipComponent Test Case.
 *
 * ! This hits the real API with real values.
 *
 * Run with the following console command:
 *
 * ./app/Console/cake test app Integration/AfterShipComponent
 *
 * @property AfterShipTestController $controller
 * @property AfterShipComponent $AfterShip
 */
class AfterShipComponentTest extends IntegrationTestCase
{
    // These will need to change if the number expires.
    const NEW_TRACKING = array(
        'slug' => 'fedex',
        'tracking_number' => '484016015727',
    );

    const OLD_TRACKING = array(
        'slug' => 'fedex',
        'tracking_number' => '************',
    );

    const COURIER = array(
        'slug' => 'fedex',
        'name' => 'FedEx®',
        'phone' => '03456 070809',
        'other_name' => 'Federal Express',
        'web_url' => 'https://www.fedex.com/',
        'required_fields' => array(),
        'optional_fields' => array('tracking_ship_date'),
        'default_language' => 'en',
        'support_languages' => array('en'),
        'service_from_country_iso3' => array('USA', 'SWE'),
    );

    const TRACKING = array(
        'id' => '5cdaf9016f406d6912aaa511',
        'created_at' => '2019-05-14T17:21:05+00:00',
        'updated_at' => '2019-05-14T17:32:02+00:00',
        'last_updated_at' => '2019-05-14T17:32:02+00:00',
        'tracking_number' => '************',
        'slug' => 'fedex',
        'active' => false,
        'android' => array(),
        'custom_fields' => null,
        'customer_name' => 'Sirisha Test Retailer',
        'delivery_time' => 0,
        'destination_country_iso3' => null,
        'courier_destination_country_iso3' => null,
        'emails' => array(0 => '<EMAIL>',),
        'expected_delivery' => null,
        'ios' => array(),
        'note' => null,
        'order_id' => null,
        'order_id_path' => null,
        'order_date' => null,
        'origin_country_iso3' => null,
        'shipment_package_count' => 0,
        'shipment_pickup_date' => null,
        'shipment_delivery_date' => null,
        'shipment_type' => null,
        'shipment_weight' => null,
        'shipment_weight_unit' => null,
        'signed_by' => null,
        'smses' => array(),
        'source' => 'api',
        'tag' => 'Delivered',
        'subtag' => 'Delivered_001',
        'subtag_message' => 'Delivered',
        'title' => '************',
        'tracked_count' => 0,
        'last_mile_tracking_supported' => null,
        'language' => null,
        'unique_token' => 'deprecated',
        'checkpoints' => array(),
        'subscribed_smses' => array(),
        'subscribed_emails' => array(),
        'return_to_sender' => false,
        'order_promised_delivery_date' => null,
        'delivery_type' => null,
        'pickup_location' => null,
        'pickup_note' => null,
        'courier_tracking_link' => 'https://www.fedex.com/apps/fedextrack/?tracknumbers=************&cntry_code=US',
        'first_attempted_at' => null,
        'courier_redirect_link' => 'https://www.fedex.com/apps/fedextrack/?action=track&tracknumbers=************&cntry_code=US',
        'tracking_account_number' => null,
        'tracking_origin_country' => null,
        'tracking_destination_country' => null,
        'tracking_key' => null,
        'tracking_postal_code' => null,
        'tracking_ship_date' => null,
        'tracking_state' => null,
        'LastCheckPoint' => null,
    );

    const LAST_CHECKPOINT = array(
        'id' => '5cdaf9016f406d6912aaa511',
        'tracking_number' => '************',
        'slug' => 'fedex',
        'tag' => 'Delivered',
        'subtag' => 'Delivered_001',
        'subtag_message' => 'Delivered',
        'checkpoint' => array(
            'slug' => null,
            'created_at' => null,
            'checkpoint_time' => null,
            'city' => null,
            'coordinates' => array(),
            'country_iso3' => null,
            'country_name' => null,
            'message' => null,
            'state' => null,
            'tag' => null,
            'subtag' => null,
            'subtag_message' => null,
            'zip' => null,
        ),
    );

    public $fixtures = [
        'app.configuration',
        'app.order',
        'app.order_comment',
        'app.order_customer_message',
    ];

    public function setUp()
    {
        parent::setUp();
        $this->controller = $this->generate('AfterShipTest', array(
            'models' => array(
                'Order' => ['findById'],
            ),
        ));
        $this->controller->startupProcess();
        $this->AfterShip = $this->controller->AfterShip;
    }

    public function tearDown()
    {
        try {
            $this->AfterShip->deleteTracking(self::NEW_TRACKING['slug'], self::NEW_TRACKING['tracking_number']);
        } catch (AfterShip\AfterShipException $e) {
            if ($e->getCode() !== 4004) {
                throw $e;
            }
        }
        unset($this->AfterShip);
        unset($this->controller);
        parent::tearDown();
    }

    public function testGetAllCouriers()
    {
        $actual = $this->AfterShip->getAllCouriers();
        $this->assertCourierList($actual);
        $this->assertCourier($actual);
    }

    public function testDetectCouriers()
    {
        $actual = $this->AfterShip->detectCouriers(self::OLD_TRACKING['tracking_number'], ['slug' => self::OLD_TRACKING['slug']]);
        $this->assertCourierList($actual);
        $this->assertCourier($actual);
    }

    public function testDetectInvalidCouriers()
    {
        $actual = $this->AfterShip->detectCouriers('bogus', ['slug' => self::OLD_TRACKING['slug']]);
        $this->assertEquals(['total' => 0, 'couriers' => []], $actual);
    }

    public function testCreateTracking()
    {
        $tracking_info = array(
            'slug' => self::NEW_TRACKING['slug'],
//            'title' => self::NEW_TRACKING['tracking_number'],
            'customer_name' => 'Donald Trump',
            'order_id' => '#SE0012500',
//            'order_id_path' => '',

            // Optional notification fields
//            'android' => [],
//            'ios' => [],
            'emails' => ['<EMAIL>', '<EMAIL>'],
            'smses' => ['+***********', '+***********'],

            // Optional fields required by some couriers
//            'tracking_postal_code' => '',
//            'tracking_ship_date' => '',
//            'tracking_account_number' => '',
//            'tracking_key' => '',
//            'tracking_origin_country' => '',
//            'tracking_destination_country' => '',
//            'tracking_state' => '',
        );
        $createResponse = $this->AfterShip->createTracking(self::NEW_TRACKING['tracking_number'], $tracking_info);

        $expected = $tracking_info + self::NEW_TRACKING + ['title' => self::NEW_TRACKING['tracking_number']];
        $actual = array_intersect_key($createResponse, $expected);
        rsort($actual['emails']);
        sort($actual['smses']);

        $this->assertTrackingAttributes($createResponse);
        $this->assertEquals($expected, $actual);
    }

    /**
     * @expectedException AfterShip\AfterShipException
     * @expectedExceptionCode 4003
     * @expectedExceptionMessage BadRequest: 4003 - Tracking already exists.
     */
    public function testCreateExistingTracking()
    {
        $this->AfterShip->createTracking(self::OLD_TRACKING['tracking_number'], ['slug' => self::OLD_TRACKING['slug']]);
    }

    public function testDeleteTracking()
    {
        $this->AfterShip->createTracking(self::NEW_TRACKING['tracking_number'], ['slug' => self::NEW_TRACKING['slug']]);

        $deleteResponse = $this->AfterShip->deleteTracking(self::NEW_TRACKING['slug'], self::NEW_TRACKING['tracking_number']);

        $expected = self::NEW_TRACKING;
        $actual = array_intersect_key($deleteResponse['tracking'], $expected);

        $this->assertEquals($expected, $actual);
    }

    public function testGetTracking()
    {
        $this->markTestSkipped('Order has expired');

        $actual = $this->AfterShip->getTracking(self::OLD_TRACKING['slug'], self::OLD_TRACKING['tracking_number']);
        $this->assertTracking($actual);
    }

    /**
     * @expectedException AfterShip\AfterShipException
     * @expectedExceptionCode 4004
     * @expectedExceptionMessage NotFound: 4004 - Tracking does not exist.
     */
    public function testGetInvalidTracking()
    {
        $this->AfterShip->getTracking(self::OLD_TRACKING['slug'], 'bogus');
    }

    public function testGetLastCheckpoint()
    {
        $this->markTestSkipped('Order has expired');

        $actual = $this->AfterShip->getLastCheckpoint(self::OLD_TRACKING['slug'], self::OLD_TRACKING['tracking_number']);
        $this->assertLastCheckpoint($actual);
    }

    /**
     * @expectedException AfterShip\AfterShipException
     * @expectedExceptionCode 4004
     * @expectedExceptionMessage NotFound: 4004 - Tracking does not exist.
     */
    public function testGetLastInvalidCheckpoint()
    {
        $this->AfterShip->getLastCheckpoint(self::OLD_TRACKING['slug'], 'bogus');
    }

    public function testHasCouriers()
    {
        $actual = $this->AfterShip->hasCouriers(self::OLD_TRACKING['tracking_number'], self::OLD_TRACKING['slug']);
        $this->assertTrue($actual);
    }

    public function testHasNoCouriers()
    {
        $actual = $this->AfterShip->hasCouriers('bogus', self::OLD_TRACKING['slug']);
        $this->assertFalse($actual);
    }

    public function testTrackOrder()
    {
        $fake = $this->mockOrder(Order::TYPE_WHOLESALE);

        $actual = $this->AfterShip->trackOrder('bogus', self::NEW_TRACKING['tracking_number'], self::NEW_TRACKING['slug']);

        $expected = self::NEW_TRACKING + array(
            'title' => self::NEW_TRACKING['tracking_number'],
            'order_id' => $fake['Order']['orderID'],
            'customer_name' => $fake['Retailer']['company_name'],
            'emails' => array($fake['Retailer']['email_address']),
            //FIXME The smses field has likely never worked in production
            //'smses' => array($fake['Contact']['value']),
        );
        $actual = array_intersect_key($actual, $expected);

        $this->assertEquals($expected, $actual);
    }

    public function testTrackExistingOrder()
    {
        $this->markTestSkipped('Order has expired');

        $this->mockOrder(Order::TYPE_WHOLESALE);

        $actual = $this->AfterShip->trackOrder('bogus', self::OLD_TRACKING['tracking_number'], self::OLD_TRACKING['slug']);

        $this->assertEquals(self::TRACKING, $actual);
    }

    public function testTrackShipFromStoreOrder()
    {
        $fake = $this->mockOrder(Order::TYPE_SHIP_FROM_STORE);

        $actual = $this->AfterShip->trackOrder('bogus', self::NEW_TRACKING['tracking_number'], self::NEW_TRACKING['slug']);

        $expected = self::NEW_TRACKING + array(
            'title' => self::NEW_TRACKING['tracking_number'],
            'order_id' => $fake['Order']['orderID'],
            'customer_name' => $fake['Order']['customer_name'],
            'emails' => array($fake['Order']['customerEmail']),
            //FIXME The smses field has likely never worked in production
            //'smses' => array($fake['Order']['shipping_telephone']),
        );
        $actual = array_intersect_key($actual, $expected);

        $this->assertEquals($expected, $actual);
    }

    private function assertCourierList($actual)
    {
        $this->assertArrayHasKey('total', $actual);
        $this->assertArrayHasKey('couriers', $actual);
    }

    private function assertCourier($actual)
    {
        $courier = null;
        foreach ($actual['couriers'] as $_courier) {
            if ($_courier['slug'] === 'fedex') {
                $courier = $_courier;
                break;
            }
        }

        /** @see Courier::syncAllAfterShipCouriers */
        $fieldsReadByOurApp = [
            'slug',
            'name',
            'phone',
            'other_name',
            'web_url',
        ];
        $expected = array_intersect_key(static::COURIER, array_flip($fieldsReadByOurApp));
        $actual = array_intersect_key($courier, array_flip($fieldsReadByOurApp));

        $this->assertEquals($expected, $actual);
    }

    private function assertLastCheckpoint($actual)
    {
        $this->assertEquals(self::LAST_CHECKPOINT, $actual);
    }

    private function assertTracking($actual)
    {
        $this->assertEquals(self::TRACKING, $actual);
    }

    private function assertTrackingAttributes($actual)
    {
        foreach (array_keys(self::TRACKING) as $attribute) {
            $this->assertArrayHasKey($attribute, $actual);
        }
    }

    private function mockOrder($orderType)
    {
        $fake = array(
            'Order' => array(
                'id' => 'bogus',
                'orderID' => '#SE0012500',
                'order_type' => $orderType,
                'shipping_telephone' => '***********',
                'customerEmail' => '<EMAIL>',
                'customer_name' => 'Donald Trump',
            ),
            'Retailer' => array(
                'id' => 'bogus',
                'email_address' => '<EMAIL>',
                'company_name' => 'Local Shop',
            ),
            'Contact' => array(
                'id' => 'bogus',
                'value' => '***********',
            ),
        );

        /** @var PHPUnit_Framework_MockObject_MockObject $mocker */
        $mocker = $this->getMockForModel('Order', ['record']);
        $mocker->expects($this->once())
            ->method('record')
            ->with($fake['Order']['id'], [
                'contain' => [
                    'Retailer' => ['fields' => array_keys($fake['Retailer'])],
                    'Contact' => ['fields' => array_keys($fake['Contact'])],
                ],
                'fields' => array_keys($fake['Order']),
            ])
            ->willReturn($fake);

        return $fake;
    }

}

/**
 * Class AfterShipTestController.
 *
 * @property AfterShipComponent $AfterShip
 */
class AfterShipTestController extends FakeAppController
{
    public $components = array('AfterShip');
}
