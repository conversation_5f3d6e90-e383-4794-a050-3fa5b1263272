<?php

use ShipEarlyApp\Test\Support\FakeAppController;

App::uses('IntegrationTestCase', 'TestSuite');
App::uses('LightspeedComponent', 'Controller/Component');

/**
 * LightspeedComponent Test Case.
 *
 * This hits the test account of a real API.
 *
 * Run with the following console command:
 *
 * ./app/Console/cake test app Integration/LightspeedComponent
 *
 * @property LightspeedTestController $controller
 * @property LightspeedComponent|PHPUnit_Framework_MockObject_MockObject $Lightspeed
 */
class LightspeedComponentTest extends IntegrationTestCase
{
    const REFRESH_TOKEN = '2dac41f8b74c464c27755d384e46f69b4fd3cd0b';
    const EXPIRED_ACCESS_TOKEN = 'b95d74c49a67f1e645f06e3546c70d7c4de9223c';

    const ACCOUNT_ID = '87633';
    const EMPLOYEE_ID = '1';
    const REGISTER_ID = '1';
    const SHOP_ID = '1';

    const EXAMPLE_URL = 'https://api.merchantos.com/API/Account/' . self::ACCOUNT_ID . '/Item';
    const EXAMPLE_UPC = '*************';
    const NO_STOCK_UPC = '*************';
    const INVALID_UPC = '************* / 9-D-3';

    const EXAMPLE_TAX_CLASS_ID = '6';

    public $fixtures = [
        'app.btask',
        'app.customer',
        'app.customer_address',
    ];

    public function setUp()
    {
        parent::setUp();
        $this->controller = $this->generate('LightspeedTest');
        $this->controller->startupProcess();
        $this->Lightspeed = $this->controller->Lightspeed;

        $this->Lightspeed->clearLastResponseHeaders();
        Cache::clear(false, 'lightspeed');
    }

    public function tearDown()
    {
        unset($this->Lightspeed);
        unset($this->controller);
        parent::tearDown();
    }

    /**
     * @expectedException LightspeedApiException
     * @expectedExceptionCode 401
     * @expectedExceptionMessage Invalid access token.
     */
    public function testExpiredAccessToken()
    {
        try {
            $this->Lightspeed->lightResponse(self::EXPIRED_ACCESS_TOKEN, self::ACCOUNT_ID, self::EXAMPLE_URL);
        } catch (LightspeedApiException $e) {
            // Assertions not handled by annotations
            $this->assertEquals(401, $e->getHttpCode());
            $this->assertEquals('Unauthorized', $e->getHttpMessage());
            $this->assertEquals('BadAuthenticationRequestException', $e->getErrorClass());
            throw $e;
        }
    }

    /**
     * @dataProvider providerRefreshTokenExpiry
     */
    public function testProvidedRefreshToken($expires_at)
    {
        $this->Lightspeed->lightResponse(self::EXPIRED_ACCESS_TOKEN, self::ACCOUNT_ID, self::EXAMPLE_URL, '', self::REFRESH_TOKEN, $expires_at);

        $response = $this->Lightspeed->getNewAccessTokenResponse(self::ACCOUNT_ID);

        $this->assertNewAccessTokenResponse($response);
    }

    public function providerRefreshTokenExpiry()
    {
        return array(
            'with_expires_at' => [format_datetime('1 SECOND AGO')],
            'dummy_expires_at' => ['0000-00-00 00:00:00'],
            'empty_expires_at' => [''],
            'null_expires_at' => [null],
        );
    }

    public function testGetTaxCategory()
    {
        $taxCategoryID = '1';

        $actual = $this->Lightspeed->getTaxCategory(self::EXPIRED_ACCESS_TOKEN, self::ACCOUNT_ID, $taxCategoryID, self::REFRESH_TOKEN, '');

        $this->assertAttributeEquals($taxCategoryID, 'taxCategoryID', $actual, $actual->asXML());
        $this->assertObjectHasAttribute('isTaxInclusive', $actual, $actual->asXML());
        $this->assertObjectHasAttribute('tax1Name', $actual, $actual->asXML());
        $this->assertObjectHasAttribute('tax2Name', $actual, $actual->asXML());
        $this->assertObjectHasAttribute('tax1Rate', $actual, $actual->asXML());
        $this->assertObjectHasAttribute('tax2Rate', $actual, $actual->asXML());
        foreach ($actual->TaxCategoryClasses->TaxCategoryClass as $taxCategoryClass) {
            $this->assertAttributeGreaterThan('0', 'taxCategoryClassID', $taxCategoryClass, $taxCategoryClass->asXML());
            $this->assertObjectHasAttribute('tax1Rate', $taxCategoryClass, $taxCategoryClass->asXML());
            $this->assertObjectHasAttribute('tax2Rate', $taxCategoryClass, $taxCategoryClass->asXML());
            $this->assertAttributeEquals($taxCategoryID, 'taxCategoryID', $taxCategoryClass, $taxCategoryClass->asXML());
        }
        $this->assertNewAccessTokenResponse($this->Lightspeed->getNewAccessTokenResponse(self::ACCOUNT_ID));
    }

    public function testCreateSale()
    {

        extract($this->createSaleRequest());

        $response = $this->Lightspeed->createSale(self::EXPIRED_ACCESS_TOKEN, self::ACCOUNT_ID, self::REGISTER_ID, self::EMPLOYEE_ID, self::SHOP_ID, $productDetails, $address, $retailerId, $currencyCode, $totalPrice, $shippingAmount, $shipto, $address, $shippingTax, $taxAmt, $orderNumber, $refreshToken, $tokenExpiresAt, false);

        $this->assertTrue($response);
        $this->assertNewAccessTokenResponse($this->Lightspeed->getNewAccessTokenResponse(self::ACCOUNT_ID));
    }

    public function testCreateSaleWithFee()
    {

        extract($this->createSaleRequest());

        $productDetails += [
            '9227372' => [
                'id' => '68',
                'inventoryId' => 9227372,
                'inventory' => 0,
                'taxcode' => '',
                'currency' => 'USD',
                'qty' => 2,
                'unformatedunitprice' => 50,
                'unformatedprice' => 100,
                'unitprice' => "<span class='Currency' style='white-space: nowrap;'>USD 50.00<\/span>",
                'price' => "<span class='Currency' style='white-space: nowrap;'>USD 100.00<\/span>",
                'unformatedunitpricediscount' => 50,
                'unformatedpricediscount' => 100,
                'unitpricediscount' => "<span class='Currency' style='white-space: nowrap;'>USD 50.00<\/span>",
                'pricediscount' => "<span class='Currency' style='white-space: nowrap;'>USD 100.00<\/span>",
                'isTax' => 'true',
                'tax' => 13,
                'taxdiscount' => 13,
                'brand_id' => '8',
                'isFeeProduct' => true,
                'title' => 'Fee 50'
            ],
        ];

        $taxAmt += 13;
        $totalPrice += 113;

        $response = $this->Lightspeed->createSale(self::EXPIRED_ACCESS_TOKEN, self::ACCOUNT_ID, self::REGISTER_ID, self::EMPLOYEE_ID, self::SHOP_ID, $productDetails, $address, $retailerId, $currencyCode, $totalPrice, $shippingAmount, $shipto, $address, $shippingTax, $taxAmt, $orderNumber, $refreshToken, $tokenExpiresAt, false);

        $this->assertTrue($response);
        $this->assertNewAccessTokenResponse($this->Lightspeed->getNewAccessTokenResponse(self::ACCOUNT_ID));
    }

    public function testCreateShipToStoreSale()
    {

        // This test simulates an order that has a product that does not exist in the POS yet.
        // if the product is not created before the order is created, LightspeedComponent::createSale() will throw an error

        extract($this->createSaleRequest());

        $productUpc = '*************';

        // archive all instances of $productUpc to avoid leftovers from contaminating test setup
        $this->archiveUpcs([$productUpc]);

        $productDetails += [
            '*********' => [
                'id' => '68',
                'inventoryId' => *********,
                'inventory' => 0,
                'taxcode' => '',
                'currency' => 'USD',
                'qty' => 2,
                'unformatedunitprice' => 75,
                'unformatedprice' => 150,
                'unitprice' => "<span class='Currency' style='white-space: nowrap;'>USD 75.00<\/span>",
                'price' => "<span class='Currency' style='white-space: nowrap;'>USD 150.00<\/span>",
                'unformatedunitpricediscount' => 75,
                'unformatedpricediscount' => 150,
                'unitpricediscount' => "<span class='Currency' style='white-space: nowrap;'>USD 75.00<\/span>",
                'pricediscount' => "<span class='Currency' style='white-space: nowrap;'>USD 150.00<\/span>",
                'isTax' => 'true',
                'tax' => 19.5,
                'taxdiscount' => 19.5,
                'brand_id' => '8',
                'title' => 'Test Missing Product',
                'upc' => $productUpc,
            ],
        ];

        $taxAmt += 19.5;
        $totalPrice += 169.5;

        $response = $this->Lightspeed->createSale(self::EXPIRED_ACCESS_TOKEN, self::ACCOUNT_ID, self::REGISTER_ID, self::EMPLOYEE_ID, self::SHOP_ID, $productDetails, $address, $retailerId, $currencyCode, $totalPrice, $shippingAmount, $shipto, $address, $shippingTax, $taxAmt, $orderNumber, $refreshToken, $tokenExpiresAt, false);

        $this->assertTrue($response);
        $this->assertNewAccessTokenResponse($this->Lightspeed->getNewAccessTokenResponse(self::ACCOUNT_ID));

        // Cleanup products created during test.
        $this->archiveUpcs([$productUpc]);
    }

    protected function archiveUpcs(array $productUpcs){
        $productsToArchive = $this->Lightspeed->getAllProductInventoriesByUpc(self::EXPIRED_ACCESS_TOKEN, self::ACCOUNT_ID, $productUpcs, self::REFRESH_TOKEN);
        $itemIds = [];
        foreach ($productsToArchive as $product) {
            $itemIds[] = (int)$product->itemID;
        }

        $this->archiveManyItemIds($itemIds);
    }

    protected function archiveManyItemIds(array $itemIds){

        foreach ($itemIds as $itemId) {
            $this->Lightspeed->archiveProduct(self::EXPIRED_ACCESS_TOKEN, self::ACCOUNT_ID, $itemId, self::REFRESH_TOKEN);
        }
    }

    public function testOutOfBalanceOrder()
    {
        extract($this->createSaleRequest());

        $totalPrice = 200.00;

        $this->expectExceptionMessage("Cannot complete an out of balance sale. Payments are insufficient.");

        $this->Lightspeed->createSale(self::EXPIRED_ACCESS_TOKEN, self::ACCOUNT_ID, self::REGISTER_ID, self::EMPLOYEE_ID, self::SHOP_ID, $productDetails, $address, $retailerId, $currencyCode, $totalPrice, $shippingAmount, $shipto, $address, $shippingTax, $taxAmt, $orderNumber, $refreshToken, $tokenExpiresAt, false);

        $this->assertNewAccessTokenResponse($this->Lightspeed->getNewAccessTokenResponse(self::ACCOUNT_ID));
    }

    protected function createSaleRequest()
    {
        $productDetails = [
            '***********' => [
                'id' => '21',
                'inventoryId' => 76,
                'inventory' => 701,
                'taxcode' => '',
                'currency' => 'USD',
                'qty' => 2,
                'unformatedunitprice' => 1200,
                'unformatedprice' => 2400,
                'unitprice' => "<span class='Currency' style='white-space: nowrap;'>USD 1,200.00<\/span>",
                'price' => "<span class='Currency' style='white-space: nowrap;'>USD 2,400.00<\/span>",
                'unformatedunitpricediscount' => 900,
                'unformatedpricediscount' => 1800,
                'unitpricediscount' => "<span class='Currency' style='white-space: nowrap;'>USD 900.00<\/span>",
                'pricediscount' => "<span class='Currency' style='white-space: nowrap;'>USD 1,800.00<\/span>",
                'isTax' => 'true',
                'TaxCategoryID' => 0,
                'taxClassID' => 11,
                'tax' => 312,
                'taxdiscount' => 234,
                'assembly_option' => '1',
                'brand_id' => '8',
                'local_delivery_shipping' => 3.33,
                'local_delivery_radius' => '1000',
                'upc' => '*************',
            ],
        ];
        $address = [
            'email' => '<EMAIL>',
            'firstname' => 'Aron',
            'lastname' => 'Schmidt',
            'company' => 'ShipEarly',
            'street' => '2400 Nipigon Rd.',
            'city' => 'Thunder Bay',
            'region' => 'Ontario',
            'postcode' => 'P7C 4W1',
            'country_id' => 'CA',
            'telephone' => '8071231234',
        ];
        $retailerId = '7';
        $currencyCode = 'USD';
        $totalPrice = '2034.00';
        $shippingAmount = '0.00';
        $shipto = false;
        $shippingTax = 0;
        $taxAmt = 312;
        $orderNumber = '#SE0012345';
        $refreshToken = self::REFRESH_TOKEN;
        $tokenExpiresAt = strtotime('1 second ago');

        return compact('productDetails', 'address', 'retailerId', 'currencyCode', 'totalPrice', 'shippingAmount', 'shipto', 'shippingTax', 'taxAmt', 'orderNumber', 'refreshToken', 'tokenExpiresAt');
    }

    /**
     * @dataProvider providerGetAllList
     */
    public function testGetAllList(?int $shopId, array $expectedSubset)
    {
        $actual = $this->Lightspeed->getAllList(self::EXPIRED_ACCESS_TOKEN, self::ACCOUNT_ID, $shopId, self::REFRESH_TOKEN, '');

        // Extract a subset of the response to assert against because the actual response contains many more items.
        $assertionSubset = [
            'Employee' => [
                1 => 'Nick Kolobutin',
                3 => 'Test Account',
            ],
            'Register' => [
                1 => 'Test Register',
                2 => 'Test 2 Register',
            ],
            'Shop' => [
                1 => 'Dax Shop',
                2 => 'Dev Environment',
            ],
        ];
        $actualSubset = [];
        foreach ($assertionSubset as $type => $list) {
            $actualSubset[$type] = array_intersect_key($actual[$type], $list);
        }

        $this->assertEquals($expectedSubset, $actualSubset);
        $this->assertNewAccessTokenResponse($this->Lightspeed->getNewAccessTokenResponse(self::ACCOUNT_ID));
    }

    public function providerGetAllList(): array
    {
        return [
            'ALL' => [
                'shopId' => null,
                'expectedSubset' => [
                    'Employee' => [
                        1 => 'Nick Kolobutin',
                        3 => 'Test Account',
                    ],
                    'Register' => [
                        1 => 'Test Register',
                        2 => 'Test 2 Register',
                    ],
                    'Shop' => [
                        1 => 'Dax Shop',
                        2 => 'Dev Environment',
                    ],
                ],
            ],
            'Dax Shop' => [
                'shopId' => 1,
                'expectedSubset' => [
                    'Employee' => [
                        1 => 'Nick Kolobutin',
                    ],
                    'Register' => [
                        1 => 'Test Register',
                    ],
                    'Shop' => [
                        1 => 'Dax Shop',
                        2 => 'Dev Environment',
                    ],
                ],
            ],
            'Dev Environment' => [
                'shopId' => 2,
                'expectedSubset' => [
                    'Employee' => [
                        3 => 'Test Account',
                    ],
                    'Register' => [
                        2 => 'Test 2 Register',
                    ],
                    'Shop' => [
                        1 => 'Dax Shop',
                        2 => 'Dev Environment',
                    ],
                ],
            ],
        ];
    }

    /**
     * @dataProvider providerShopIds
     * @param $shopID
     */
    public function testGetShop($shopID)
    {
        $actual = $this->Lightspeed->getShop(self::EXPIRED_ACCESS_TOKEN, self::ACCOUNT_ID, $shopID, self::REFRESH_TOKEN);

        $this->assertAttributeEquals($shopID, 'shopID', $actual, $actual->asXML());
        $this->assertObjectHasAttribute('archived', $actual, $actual->asXML());
        // Details
        $this->assertObjectHasAttribute('name', $actual, $actual->asXML());
        $this->assertObjectHasAttribute('timeZone', $actual, $actual->asXML());
        // Pricing Level
        $this->assertObjectHasAttribute('priceLevelID', $actual, $actual->asXML());
        // Tax Setup
        // taxCategoryID=0 indicates that the shop never charges tax regardless of item tax classes
        $this->assertAttributeGreaterThanOrEqual('0', 'taxCategoryID', $actual, $actual->asXML());
        $this->assertObjectHasAttribute('taxLabor', $actual, $actual->asXML());
        // Service / Labor
        $this->assertObjectHasAttribute('serviceRate', $actual, $actual->asXML());
    }

    public function providerShopIds()
    {
        return [
            ['shopID' => '1'],
            ['shopID' => '2'],
        ];
    }

    public function testGetInventoryCount()
    {
        $inventory = $this->Lightspeed->getInventoryCount(self::EXPIRED_ACCESS_TOKEN, self::ACCOUNT_ID, self::EXAMPLE_UPC, self::REFRESH_TOKEN);
        $this->assertGreaterThan(0, $inventory);
        $this->assertNewAccessTokenResponse($this->Lightspeed->getNewAccessTokenResponse(self::ACCOUNT_ID));
    }

    public function testGetAllProductInventoriesByUpc()
    {
        $upcs = [static::EXAMPLE_UPC, static::NO_STOCK_UPC, static::INVALID_UPC];

        $response = $this->Lightspeed->getAllProductInventoriesByUpc(self::EXPIRED_ACCESS_TOKEN, self::ACCOUNT_ID, $upcs, self::REFRESH_TOKEN);

        $this->assertEquals(2, $response->count(), $response->asXML());

        foreach ($response->Item as $item) {
            $this->assertAttributeGreaterThan('0', 'itemID', $item, $item->asXML());
            $this->assertTrue(in_array((string)$item->upc, $upcs, true), sprintf('%s not in [%s]', $item->upc, implode(', ', $upcs)));
            $this->assertObjectHasAttribute('tax', $item, $item->asXML());
            $this->assertObjectHasAttribute('archived', $item, $item->asXML());
            $this->assertAttributeGreaterThan('0', 'taxClassID', $item, $item->asXML());
            foreach ($item->ItemShops->ItemShop as $itemShop) {
                $this->assertAttributeGreaterThan('0', 'itemShopID', $itemShop, $itemShop->asXML());
                $this->assertAttributeGreaterThanOrEqual('0', 'qoh', $itemShop, $itemShop->asXML());
                $this->assertAttributeEquals($item->itemID, 'itemID', $itemShop, $itemShop->asXML());
                // shopID=0 indicates the total inventory of all shops
                $this->assertAttributeGreaterThanOrEqual('0', 'shopID', $itemShop, $itemShop->asXML());
            }
            foreach ($item->Prices->ItemPrice as $itemPrice) {
                $this->assertAttributeGreaterThanOrEqual('0', 'amount', $itemPrice, $itemPrice->asXML());
                $this->assertAttributeGreaterThan('0', 'useTypeID', $itemPrice, $itemPrice->asXML());
                $this->assertObjectHasAttribute('useType', $itemPrice, $itemPrice->asXML());
            }
        }

        $this->assertNewAccessTokenResponse($this->Lightspeed->getNewAccessTokenResponse(self::ACCOUNT_ID));
    }

    public function testGetAllProductInventories()
    {
        $response = $this->Lightspeed->getAllProductInventories(self::EXPIRED_ACCESS_TOKEN, self::ACCOUNT_ID, self::REFRESH_TOKEN);

        $this->assertGreaterThan(0, $response->count(), $response->asXML());
        $this->assertTrue(isset($response->Item->itemID));

        $this->assertNewAccessTokenResponse($this->Lightspeed->getNewAccessTokenResponse(self::ACCOUNT_ID));
    }

    public function testGetAllProductInventoriesPaginationWithPageLimit()
    {
        /** @var LightspeedComponent|PHPUnit_Framework_MockObject_MockObject $Lightspeed */
        $this->Lightspeed = $this->getMockForComponent('Lightspeed', ['_sendApiRequest'], $this->controller->Components);

        $this->Lightspeed->expects($this->exactly(10))->method('_sendApiRequest')->willReturn(new SimpleXMLElement('<Items count="0" next="NextPageURL"/>'));

        $response = $this->Lightspeed->getAllProductInventories(self::EXPIRED_ACCESS_TOKEN, self::ACCOUNT_ID, self::REFRESH_TOKEN);
        $requestCount = 1;

        while (!empty($response['next']) && $requestCount < 10) {
            $response = $this->Lightspeed->nextPage($response, self::EXPIRED_ACCESS_TOKEN, self::ACCOUNT_ID, self::REFRESH_TOKEN);
            $requestCount++;
        }
    }

    /**
     * @dataProvider providerRateLimit
     * @param array $rateHeaders
     * @param int $now
     * @param float $expectedSleepTime
     */
    public function testRateLimit(array $rateHeaders, int $now, float $expectedSleepTime)
    {
        /** @var LightspeedComponent|PHPUnit_Framework_MockObject_MockObject $Lightspeed */
        $Lightspeed = $this->getMockForComponent('Lightspeed', ['_sendApiRequest', 'time', 'sleep'], $this->controller->Components);
        $this->Lightspeed = $Lightspeed;

        // Bypass real api calls
        $tokenExpiresAt = format_datetime('+1 MINUTE');
        $Lightspeed->expects($this->exactly(1))->method('_sendApiRequest')->willReturn(new SimpleXMLElement('<xml></xml>'));
        // Make time constant
        $Lightspeed->expects($this->any())->method('time')->willReturn($now);

        // Assert calculated sleep time match
        $Lightspeed->expects($this->exactly($expectedSleepTime > 0 ? 1 : 0))->method('sleep')->with($expectedSleepTime);

        $this->callPrivateMethod([$this->Lightspeed, 'setLastResponseHeaders'], ['x-ls-acct-id' => static::ACCOUNT_ID] + $rateHeaders);
        $this->Lightspeed->lightResponse(static::EXPIRED_ACCESS_TOKEN, static::ACCOUNT_ID, static::EXAMPLE_URL, '', static::REFRESH_TOKEN, $tokenExpiresAt);
    }

    public function providerRateLimit(): array
    {
        $now = static::time();
        $pastDateHeader = date(DATE_RFC7231, $now - 1);
        $presentDateHeader = date(DATE_RFC7231, $now);
        $futureDateHeader = date(DATE_RFC7231, $now + 1);

        return [
            'no_headers' => [
                'rateHeaders' => [
                ],
                'now' => $now,
                'expectedSleepTime' => 0,
            ],
            'within_limit' => [
                'rateHeaders' => [
                    'date' => $presentDateHeader,
                    'x-ls-api-bucket-level' => '79/90',
                    'x-ls-api-drip-rate' => '0.1',
                ],
                'now' => $now,
                'expectedSleepTime' => 0,
            ],
            'within_limit_time_adjusted' => [
                'rateHeaders' => [
                    'date' => $pastDateHeader,
                    'x-ls-api-bucket-level' => '79.1/90',
                    'x-ls-api-drip-rate' => '0.1',
                ],
                'now' => $now,
                'expectedSleepTime' => 0,
            ],
            'rate_limit' => [
                'rateHeaders' => [
                    'date' => $presentDateHeader,
                    'x-ls-api-bucket-level' => '79.1/90',
                    'x-ls-api-drip-rate' => '0.1',
                ],
                'now' => $now,
                'expectedSleepTime' => 1,
            ],
            'rate_limit_time_adjusted' => [
                'rateHeaders' => [
                    'date' => $pastDateHeader,
                    'x-ls-api-bucket-level' => '79.2/90',
                    'x-ls-api-drip-rate' => '0.1',
                ],
                'now' => $now,
                'expectedSleepTime' => 1,
            ],
            'retry_after' => [
                'rateHeaders' => [
                    'date' => $presentDateHeader,
                    'retry-after' => '60.0',
                    'x-ls-api-bucket-level' => '89/90',
                ],
                'now' => $now,
                'expectedSleepTime' => 60,
            ],
            'retry_after_time_adjusted' => [
                'rateHeaders' => [
                    'date' => $pastDateHeader,
                    'retry-after' => '60.0',
                    'x-ls-api-bucket-level' => '89/90',
                ],
                'now' => $now,
                'expectedSleepTime' => 59,
            ],
            'no_date_same_as_present' => [
                'rateHeaders' => [
                    'retry-after' => '60.0',
                    'x-ls-api-bucket-level' => '89/90',
                ],
                'now' => $now,
                'expectedSleepTime' => 60,
            ],
            'future_date_same_as_present' => [
                'rateHeaders' => [
                    'date' => $futureDateHeader,
                    'retry-after' => '60.0',
                    'x-ls-api-bucket-level' => '89/90',
                ],
                'now' => $now,
                'expectedSleepTime' => 60,
            ],
        ];
    }

    public function testRetryAfter429Error()
    {
        $now = static::time();

        /** @var LightspeedComponent|PHPUnit_Framework_MockObject_MockObject $Lightspeed */
        $Lightspeed = $this->getMockForComponent('Lightspeed', ['_sendApiRequest', 'time', 'sleep'], $this->controller->Components);
        $this->Lightspeed = $Lightspeed;

        $exception = new LightspeedApiException(LightspeedApiException::createXmlElement(
            429,
            'Too Many Requests',
            'Rate Limit Exceeded: Please decrease your request volume. To eliminate this message, keep your request rate below 1 requests per second. Clients that continue to receive this response may have their traffic throttled.',
            'RateLimitedException'
        ));

        // Bypass real api calls
        $tokenExpiresAt = format_datetime('+1 MINUTE');
        $Lightspeed->expects($this->exactly(3))->method('_sendApiRequest')->willReturnCallback(function() use ($now, $exception) {
            $this->callPrivateMethod([$this->Lightspeed, 'setLastResponseHeaders'], [
                'date' => date(DATE_RFC7231, $now),
                'retry-after' => '60.0',
                'x-ls-acct-id' => static::ACCOUNT_ID,
                'x-ls-api-bucket-level' => '89/90',
            ]);

            throw $exception;
        });
        // Make time constant
        $Lightspeed->expects($this->any())->method('time')->willReturn($now);

        // Assert calculated sleep time match
        $Lightspeed->expects($this->exactly(4))->method('sleep')->with(60);

        $this->setExpectedException(get_class($exception), $exception->getMessage(), $exception->getCode());
        $this->Lightspeed->lightResponse(static::EXPIRED_ACCESS_TOKEN, static::ACCOUNT_ID, static::EXAMPLE_URL, '', static::REFRESH_TOKEN, $tokenExpiresAt);
    }

    public function testRetryAfterCurlTimeoutError()
    {
        $this->setupCurlTimeoutTest('_sendApiRequest');
        $tokenExpiresAt = format_datetime('+1 MINUTE');
        $this->Lightspeed->lightResponse(static::EXPIRED_ACCESS_TOKEN, static::ACCOUNT_ID, static::EXAMPLE_URL, '', static::REFRESH_TOKEN, $tokenExpiresAt);
    }

    public function testRetryAfterCurlTimeoutErrorTokenRefresh()
    {
        $this->setupCurlTimeoutTest('refreshAccessToken');
        $tokenExpiresAt = format_datetime('-1 MINUTE');
        $this->Lightspeed->lightResponse(static::EXPIRED_ACCESS_TOKEN, static::ACCOUNT_ID, static::EXAMPLE_URL, '', static::REFRESH_TOKEN, $tokenExpiresAt);
    }

    /**
     * @param string $timeoutFunction Function that will be mocked to throw CurlException
     */
    protected function setupCurlTimeoutTest($timeoutFunction){

        $exception = new LightspeedApiException('cUrl timeout', 28);

        /** @var LightspeedComponent|PHPUnit_Framework_MockObject_MockObject $Lightspeed */
        $Lightspeed = $this->getMockForComponent('Lightspeed', [$timeoutFunction, 'time', 'sleep'], $this->controller->Components);
        $Lightspeed->expects($this->exactly(3))->method($timeoutFunction)->willThrowException($exception);
        $this->Lightspeed = $Lightspeed;
        // Assert calculated sleep time match
        $Lightspeed->expects($this->exactly(2))->method('sleep')->with(5);

        $this->setExpectedException(get_class($exception), $exception->getMessage(), $exception->getCode());
    }

    public function testGetAccessToken()
    {
        $this->markTestSkipped('Requires a valid temporary Oauth token.');
    }

    public function testRefreshToken()
    {
        $response = $this->Lightspeed->refreshAccessToken(self::REFRESH_TOKEN);

        $this->assertNewAccessTokenResponse($response);
    }

    public function testFreshTokenDoesNotRefresh()
    {
        $response = $this->Lightspeed->refreshAccessToken(self::REFRESH_TOKEN, self::ACCOUNT_ID);

        $this->Lightspeed->lightResponse($response['access_token'], self::ACCOUNT_ID, self::EXAMPLE_URL, '', self::REFRESH_TOKEN, $response['expires_at']);

        $this->assertEquals(array(), $this->Lightspeed->getNewAccessTokenResponse(self::ACCOUNT_ID), 'Failed asserting that a fresh token was not refreshed.');
    }

    /**
     * @dataProvider providerInvalidRefreshTokens
     */
    public function testInvalidRefreshToken($refreshToken, $expectedErrorClass)
    {
        try {
            $this->Lightspeed->refreshAccessToken($refreshToken, self::ACCOUNT_ID);
            $this->fail('Failed asserting that exception of type "LightspeedOauthException" is thrown.');
        } catch (LightspeedOauthException $e) {
            $this->assertEquals($expectedErrorClass, $e->getErrorClass());
        }
    }

    public function providerInvalidRefreshTokens()
    {
        return array(
            'invalid_request' => ['', 'invalid_request'],
            'invalid_grant' => ['bogus', 'invalid_grant'],
        );
    }

    public function testGetAccessTokenSession()
    {
        $refreshed = $this->Lightspeed->refreshAccessToken(self::REFRESH_TOKEN, null);

        $response = $this->Lightspeed->getAccessTokenSession($refreshed['access_token']);

        $this->assertAttributeEquals(self::ACCOUNT_ID, 'systemCustomerID', (object)$response);
    }

    private function assertNewAccessTokenResponse($response)
    {
        $response = (object)$response;
        $this->assertAttributeNotEquals(self::EXPIRED_ACCESS_TOKEN, 'access_token', $response, '$response[\'access_token\']');
        $this->assertAttributeGreaterThan('0', 'expires_in', $response, '$response[\'expires_in\']');
        $this->assertAttributeEquals('bearer', 'token_type', $response, '$response[\'token_type\']');
        $this->assertAttributeContains('employee:all', 'scope', $response, '$response[\'scope\']');
        $this->assertStringMatchesFormat('%d-%d-%d %d:%d:%d', $response->expires_at, '$response[\'expires_at\']');
        $this->assertGreaterThan(static::time(), strtotime($response->expires_at), '$response[\'expires_at\'] > NOW()');
    }

    public function testAddNewProduct()
    {
        $productDescription = 'testAddNewProduct Product';

        $products = $this->Lightspeed->fetchItemByDescription($productDescription,self::EXPIRED_ACCESS_TOKEN, self::ACCOUNT_ID, self::REFRESH_TOKEN, strtotime('1 second ago'));

        $productIds = [];
        foreach ($products as $product) {
            $productIds[] = (int)$product->itemID;
        }
        $this->archiveManyItemIds($productIds);
        $newProductId = $this->callPrivateMethod([$this->Lightspeed, '_addNewProduct'], self::EXPIRED_ACCESS_TOKEN, self::ACCOUNT_ID, $productDescription);

        $this->assertInternalType('int', $newProductId);

        $this->archiveManyItemIds([$newProductId]);
    }

}

/**
 * Class LightspeedTestController.
 *
 * @property LightspeedComponent $Lightspeed
 * @property Configuration $Configuration
 * @property Customer $Customer
 */
class LightspeedTestController extends FakeAppController
{
    public $components = array('Lightspeed');
    public $uses = array('Configuration', 'Customer');
}
