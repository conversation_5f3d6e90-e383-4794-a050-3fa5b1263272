<?php
App::uses('AppTestCase', 'TestSuite');
App::uses('A<PERSON><PERSON><PERSON><PERSON><PERSON>', 'Controller');
App::uses('ComponentCollection', 'Controller');
App::uses('CakeEventManager', 'Event');
App::uses('CakeEvent', 'Event');

/**
 * ShippoComponent Test Case.
 *
 * This hits the test account of a real API.
 *
 * Run with the following console command:
 *
 * ./app/Console/cake test app Integration/ShippoComponent
 *
 * @property ShippoComponent $Shippo
 */
class ShippoComponentTest extends AppTestCase
{
    /** @see https://docs.goshippo.com/shippoapi/public-api/#tag/Carrier-Accounts */
    const FEDEX_ACCOUNT_ARRAY = [
        'carrier' => 'fedex',
        'object_id' => '0b45342c9add4ff4a99d8ce0be093600',
        'object_owner' => '<EMAIL>',
        'account_id' => 'HRevan',
        'parameters' => [
            'meter' => '******', // Assigned 'R55E47'
            'smartpost_id' => 'Racks12812'
        ],
        'test' => false,
        'active' => true,
        'is_shippo_account' => false,
        'metadata' => '',
        'carrier_name' => 'FedEx',

        // Found in API docs but not in response
        //'service_levels' => [
        //    [
        //        'name' => 'Priority Mail Express',
        //        'token' => 'usps_priority_express',
        //        'supports_return_labels' => true,
        //    ],
        //],

        // Found in response but not in API docs; should not be asserted or depended on
        //'carrier_images' => [
        //    'https://shippo-static-v2.s3.amazonaws.com/providers/75/FedEx.png',
        //    'https://shippo-static-v2.s3.amazonaws.com/providers/200/FedEx.png',
        //],
        //'object_info' => [
        //    'authentication' => ['type' => 'default'],
        //],
    ];

    public $fixtures = [
    ];

    public function setUp()
    {
        parent::setUp();
        $Collection = new ComponentCollection();
        $Collection->setController(new AppController());

        // Don't really want to mock anything yet
        $methods = ['_stop'];
        $this->Shippo = $this->getMockForComponent('Shippo', $methods, $Collection);

        if (!defined('SHIPPO_PRIVATE_AUTH_TOKEN')) {
            // FIXME I think this token belongs to hlawal who originally developed this integration.
            //  Replace this key (and the matching Sql/shipearlydev-data.sql record) with a test token we control.
            define('SHIPPO_PRIVATE_AUTH_TOKEN', 'eb7f0a752275334bfe0b04f1e30693f62d54de0a');
        }
        $eventManager = new CakeEventManager();
        $eventManager->attach($Collection);
        $eventManager->dispatch(new CakeEvent('Controller.initialize', $Collection->getController()));
        $eventManager->dispatch(new CakeEvent('Controller.startup', $Collection->getController()));
    }

    public function tearDown()
    {
        unset($this->Shippo);
        parent::tearDown();
    }

    public function testListCarrierAccounts()
    {
        $fedexAccountArray = static::FEDEX_ACCOUNT_ARRAY;
        $account_id = $fedexAccountArray['account_id'];
        $params = ['carrier' => $fedexAccountArray['carrier']];

        $all = $this->Shippo->listCarrierAccounts($params);
        $actual = $all[array_search($account_id, array_column($all, 'account_id'), true)];

        $this->assertInstanceOf('Shippo_Object', $actual);
        $actualAccountArray = array_intersect_key($actual->__toArray(true), $fedexAccountArray);
        $this->assertEquals($fedexAccountArray, $actualAccountArray);
    }

    public function testFindCarrierAccountByAccountId()
    {
        $fedexAccountArray = static::FEDEX_ACCOUNT_ARRAY;
        $account_id = $fedexAccountArray['account_id'];
        $params = ['carrier' => $fedexAccountArray['carrier']];

        $actual = $this->Shippo->findCarrierAccountByAccountId($account_id, $params);

        $this->assertInstanceOf('Shippo_Object', $actual);
        $actualAccountArray = array_intersect_key($actual->__toArray(true), $fedexAccountArray);
        $this->assertEquals($fedexAccountArray, $actualAccountArray);
    }

    public function testCreateCarrierAccount()
    {
        $this->markTestSkipped('Creates a record that cannot be deleted');

        $fedexAccountArray = static::FEDEX_ACCOUNT_ARRAY;
        $carrier_id = $fedexAccountArray['carrier'];
        $carrier_parameters = [
            'parameter1' => $fedexAccountArray['account_id'],
            'parameter2' => 'R55E47',
            'parameter3' => 'Racks12812',
            'parameter4' => '',
        ];

        $actual = $this->Shippo->createCarrierAccount($carrier_id, $carrier_parameters);

        $this->assertInstanceOf('Shippo_Object', $actual);
        $actualAccountArray = array_intersect_key($actual->__toArray(true), $fedexAccountArray);
        $this->assertEquals($fedexAccountArray, $actualAccountArray);
    }

    public function testUpdateCarrierAccount()
    {
        $fedexAccountArray = static::FEDEX_ACCOUNT_ARRAY;
        $object_id = $fedexAccountArray['object_id'];
        $carrier_id = $fedexAccountArray['carrier'];
        $carrier_parameters = [
            'parameter1' => $fedexAccountArray['account_id'],
            'parameter2' => 'R55E47',
            'parameter3' => 'Racks12812',
            'parameter4' => '',
        ];

        $actual = $this->Shippo->updateCarrierAccount($object_id, $carrier_id, $carrier_parameters);

        $this->assertInstanceOf('Shippo_Object', $actual);
        $actualAccountArray = array_intersect_key($actual->__toArray(true), $fedexAccountArray);
        $this->assertEquals($fedexAccountArray, $actualAccountArray);
    }

    public function testGetCarrierRates()
    {
        $address_from = [
            'name' => 'My Local Brand',
            'street1' => '417 Montgomery St',
            'city' => 'San Francisco',
            'state' => 'CA',
            'country' => 'US',
            'zip' => '94104',
            'phone' => '************',
            'email' => '<EMAIL>'
        ];
        $address_to = [
            'name' => 'Aron Schmidt',
            'company' => 'Home',
            'street1' => '2400 Nipigon Rd., Apt 2',
            'city' => 'Thunder Bay',
            'state' => 'ON',
            'country' => 'CA',
            'zip' => 'P7C 4W1',
            'phone' => '+****************',
            'email' => '<EMAIL>'
        ];
        $parcel = [
            'length' => '30',
            'width' => '20',
            'height' => '15',
            'distance_unit' => 'in',
            'weight' => 1.0,
            'box_weight' => '1',
            'mass_unit' => 'kg'
        ];
        $carrier_accounts = [
            0 => 'a5292b4f563a49c79a0499adb3d022d9',
            1 => static::FEDEX_ACCOUNT_ARRAY['object_id'],
        ];

        $actual = $this->Shippo->getCarrierRates($address_from, $address_to, $parcel, $carrier_accounts);

        $expected = [
            [
                'amount' => '47.39',
                'amount_local' => '64.35',
                'arrives_by' => null,
                'attributes' => [
                    0 => 'CHEAPEST',
                ],
                'carrier_account' => 'a5292b4f563a49c79a0499adb3d022d9',
                'currency' => 'USD',
                'currency_local' => 'CAD',
                'duration_terms' => 'Delivery in 6 to 10 business days.',
                'estimated_days' => 8,
                'included_insurance_price' => null,
                'messages' => [],
                'object_created' => '2024-03-25T18:02:19.104Z',
                'object_id' => '7fb885ab691f4b50bc87873bd41760c6',
                'object_owner' => '<EMAIL>',
                'provider' => 'USPS',
                'provider_image_200' => 'https://shippo-static-v2.s3.amazonaws.com/providers/200/USPS.png',
                'provider_image_75' => 'https://shippo-static-v2.s3.amazonaws.com/providers/75/USPS.png',
                'servicelevel' => [
                    'display_name' => null,
                    'extended_token' => 'usps_priority_mail_international',
                    'name' => 'Priority Mail International',
                    'parent_servicelevel' => null,
                    'terms' => '',
                    'token' => 'usps_priority_mail_international',
                ],
                'shipment' => 'b2d04ce94959425aa56773bdbdec2d04',
                'test' => false,
                'zone' => '1.1',
            ],
            [
                'amount' => '71.29',
                'amount_local' => '96.81',
                'arrives_by' => null,
                'attributes' => [
                    0 => 'BESTVALUE',
                    1 => 'FASTEST',
                ],
                'carrier_account' => 'a5292b4f563a49c79a0499adb3d022d9',
                'currency' => 'USD',
                'currency_local' => 'CAD',
                'duration_terms' => 'Delivery in 3 to 5 business days.',
                'estimated_days' => 4,
                'included_insurance_price' => null,
                'messages' => [],
                'object_created' => '2024-03-25T18:02:19.104Z',
                'object_id' => '4aca280633994f48abd94cda777cd682',
                'object_owner' => '<EMAIL>',
                'provider' => 'USPS',
                'provider_image_200' => 'https://shippo-static-v2.s3.amazonaws.com/providers/200/USPS.png',
                'provider_image_75' => 'https://shippo-static-v2.s3.amazonaws.com/providers/75/USPS.png',
                'servicelevel' => [
                    'display_name' => null,
                    'extended_token' => 'usps_priority_mail_express_international',
                    'name' => 'Priority Mail Express International',
                    'parent_servicelevel' => null,
                    'terms' => '',
                    'token' => 'usps_priority_mail_express_international',
                ],
                'shipment' => 'b2d04ce94959425aa56773bdbdec2d04',
                'test' => false,
                'zone' => '1',
            ],
        ];
        $volatileFields = [
            'object_created',
            'object_id',
            'shipment',
            'amount',
            'amount_local',
        ];
        foreach (array_keys($expected) as $index) {
            foreach ($volatileFields as $volatileField) {
                $expected[$index][$volatileField] = $actual[$index][$volatileField];
            }
        }
        $this->assertEquals($expected, $actual);
    }

    public function testGetCarrierRatesFedexError()
    {
        $address_from = [
            'name' => 'My Local Brand',
            'street1' => '417 Montgomery St',
            'city' => 'San Francisco',
            'state' => 'CA',
            'country' => 'US',
            'zip' => '94104',
            'phone' => '************',
            'email' => '<EMAIL>'
        ];
        $address_to = [
            'name' => 'Aron Schmidt',
            'company' => 'Home',
            'street1' => '2400 Nipigon Rd., Apt 2',
            'city' => 'Thunder Bay',
            'state' => 'ON',
            'country' => 'CA',
            'zip' => 'P7C 4W1',
            'phone' => '+****************',
            'email' => '<EMAIL>'
        ];
        $parcel = [
            'length' => '30',
            'width' => '20',
            'height' => '15',
            'distance_unit' => 'in',
            'weight' => 1.0,
            'box_weight' => '1',
            'mass_unit' => 'kg'
        ];
        $carrier_accounts = [
            0 => '0b45342c9add4ff4a99d8ce0be093600',
        ];

        $this->setExpectedException(Shippo_Error::class, 'FedEx ERROR: Invalid Shipper Account Nbr.', 0);

        try {
            $this->Shippo->getCarrierRates($address_from, $address_to, $parcel, $carrier_accounts);
        } catch (Shippo_Error $e) {
            $expectedJsonBody = [
                ['code' => '2', 'source' => 'FedEx', 'text' => 'ERROR: Invalid Shipper Account Nbr.'],
            ];
            $this->assertEquals([
                'httpStatus' => 400,
                'httpBody' => json_encode($expectedJsonBody),
                'jsonBody' => $expectedJsonBody,
            ], get_object_vars($e));

            throw $e;
        }
    }
}
