<?php
App::uses('AppTestCase', 'TestSuite');
App::uses('SquarePosComponent', 'Controller/Component');
App::uses('ComponentCollection', 'Controller');

/**
 * SquarePosComponent Test Case.
 *
 * This hits the test account of a real API.
 *
 * Run with the following console command:
 *
 * ./app/Console/cake test app Integration/SquarePosComponent
 *
 * @property SquarePosComponent|PHPUnit_Framework_MockObject_MockObject $SquarePos
 */
class SquarePosComponentTest extends AppTestCase
{
    // Production access token <NAME_EMAIL>
    // from https://developer.squareup.com/apps/*****************************/settings.
    // Production token is necessary for deprecated V1 endpoints.
    const ACCESS_TOKEN = 'EAAAELLgYqez_sryrADKL3pJFANRkUegLY366mDq4yqXpQjIxGObBeJOEqje8H6e';

    const MERCHANT_ID = '5V7J744B8WSD3';
    const LOCATION_ID = 'FMBE6WFDRJRNJ';

    public function setUp()
    {
        parent::setUp();
        if (!defined('SQUARE_APPLICATION_ID')) {
            define('SQUARE_APPLICATION_ID', '*****************************');
        }
        if (!defined('SQUARE_APPLICATION_SECRET')) {
            define('SQUARE_APPLICATION_SECRET', 'sq0csp-v-8B1ave66zoLrZafqIJ9t4mf5HZEZTBZfmdAl0hEyg');
        }
        $this->SquarePos = $this->mockSquarePos();
    }

    public function tearDown()
    {
        unset($this->SquarePos);
        parent::tearDown();
    }

    public function testOAuthApiCalls()
    {

        // A real temporary oauth code can be retrieved by signing in at
        // https://connect.squareup.com/oauth2/authorize?client_id=*****************************&scope=MERCHANT_PROFILE_READ%20CUSTOMERS_READ%20CUSTOMERS_WRITE%20ITEMS_READ%20ORDERS_READ%20INVENTORY_READ&session=false
        $oauthCode = '';

        if (!$oauthCode) {
            $this->markTestSkipped('Requires a temp oauth code from https://connect.squareup.com/oauth2/authorize');
        }

        $expected = [
            'access_token' => '{new_access_token}',
            'token_type' => 'bearer',
            'expires_at' => '{new_expires_at}',
            'merchant_id' => static::MERCHANT_ID,
        ];

        $newToken = $this->SquarePos->getAccessToken($oauthCode);

        $actual = $newToken;
        if (!empty($actual['access_token'])) {
            $actual['access_token'] = '{new_access_token}';
        }
        if (!empty($actual['expires_at'])) {
            $actual['expires_at'] = '{new_expires_at}';
        }
        $this->assertEquals($expected, $actual);

        $newToken = $this->SquarePos->renewAccessToken($newToken['access_token']);

        $actual = $newToken;
        if (!empty($actual['access_token'])) {
            $actual['access_token'] = '{new_access_token}';
        }
        if (!empty($actual['expires_at'])) {
            $actual['expires_at'] = '{new_expires_at}';
        }
        $this->assertEquals($expected, $actual);

        $actual = $this->SquarePos->revokeAccessToken($newToken['access_token']);
        $this->assertEquals(['success' => true], $actual);
    }

    public function testListLocations()
    {
        $expected = [
            [
                'id' => static::LOCATION_ID,
                'name' => 'Square ShipEarly',
                'timezone' => 'America/Thunder_Bay',
                'capabilities' => [
                    0 => 'AUTOMATIC_TRANSFERS'
                ],
                'status' => 'ACTIVE',
                'created_at' => '2016-07-15T13:32:12Z',
                'merchant_id' => '5V7J744B8WSD3',
                'country' => 'US',
                'language_code' => 'en-US',
                'currency' => 'USD',
                'business_name' => 'Square ShipEarly',
                'type' => 'PHYSICAL',
                'business_hours' => [],
                'business_email' => '<EMAIL>',
                'mcc' => '7299',
            ],
        ];

        $actual = $this->SquarePos->listLocations(static::ACCESS_TOKEN);

        $this->assertEquals($expected, $actual);
    }

    public function testListItems()
    {
        $items = $this->SquarePos->listItems(static::ACCESS_TOKEN);
        // Pick a sample item
        $actual = $items[array_search('Bicycle', Hash::extract($items, '{n}.item_data.name'), true)];

        $expected = [
            'id' => 'OZNFOQB7KIPKU46Z4VCVW2VY',
            'type' => 'ITEM',
            'updated_at' => $actual['updated_at'],
            'version' => $actual['version'],
            'is_deleted' => false,
            'catalog_v1_ids' => [
                0 => [
                    'catalog_v1_id' => '48c93eed-d595-46f4-95ed-00860f203bba',
                    'location_id' => 'FMBE6WFDRJRNJ',
                ],
            ],
            'present_at_all_locations' => false,
            'present_at_location_ids' => [0 => 'FMBE6WFDRJRNJ'],
            'item_data' => [
                'variations' => [
                    0 => [
                        'id' => '7KRHDH7A7T7N2MVJO5THWWTJ',
                        'type' => 'ITEM_VARIATION',
                        'updated_at' => $actual['item_data']['variations'][0]['updated_at'],
                        'version' => $actual['item_data']['variations'][0]['version'],
                        'is_deleted' => false,
                        'catalog_v1_ids' => [
                            0 => [
                                'catalog_v1_id' => 'ffb23f24-617d-440c-8253-fc0df84bb794',
                                'location_id' => 'FMBE6WFDRJRNJ',
                            ],
                        ],
                        'present_at_all_locations' => false,
                        'present_at_location_ids' => [0 => 'FMBE6WFDRJRNJ'],
                        'item_variation_data' => [
                            'item_id' => 'OZNFOQB7KIPKU46Z4VCVW2VY',
                            'name' => 'Regular',
                            'sku' => '9090909090909',
                            'ordinal' => 1,
                            'pricing_type' => 'FIXED_PRICING',
                            'price_money' => [
                                'amount' => 99900,
                                'currency' => 'USD',
                            ],
                            'location_overrides' => [
                                0 => [
                                    'location_id' => 'FMBE6WFDRJRNJ',
                                    'track_inventory' => true,
                                ],
                            ],
                            'stockable' => true,
                        ],
                    ],
                ],
                'name' => 'Bicycle',
                'product_type' => 'REGULAR',
                'skip_modifier_screen' => false,
                'tax_ids' => [
                    0 => 'N7BMTRIGP4MZYEWOPOSFVUCV',
                    1 => 'YOI33TO5GHGBMJZ3YQK7VUKX',
                ],
            ],
        ];
        $this->assertEquals($expected, $actual);
    }

    public function testFindItem()
    {
        $expected = [
            'images' => [],
            'name' => 'Bicycle',
            'product_type' => 'REGULAR',
            'skip_modifier_screen' => false,
            'variations' => [
                '7KRHDH7A7T7N2MVJO5THWWTJ' => [
                    'item_id' => 'OZNFOQB7KIPKU46Z4VCVW2VY',
                    'name' => 'Regular',
                    'sku' => '9090909090909',
                    'ordinal' => 1,
                    'pricing_type' => 'FIXED_PRICING',
                    'stockable' => true,
                    'price_money' => [
                        'amount' => 166.50,
                        'currency' => 'USD',
                        'total_tax' => '24.98',
                    ],
                    'location_overrides' => [
                        0 => [
                            'location_id' => 'FMBE6WFDRJRNJ',
                            'track_inventory' => true,
                        ],
                    ],
                ],
            ],
            'total_tax_rate' => '0.15',
            'tax_ids' => [
                0 => 'N7BMTRIGP4MZYEWOPOSFVUCV',
                1 => 'YOI33TO5GHGBMJZ3YQK7VUKX',
            ],
        ];

        $actual = $this->SquarePos->findItem(static::ACCESS_TOKEN, 'OZNFOQB7KIPKU46Z4VCVW2VY');

        $this->assertEquals($expected, $actual);
    }

    public function testListInventoriesByVariantId()
    {
        $expected = [
            'YRL64MLCITCKEJH4HQ32LKSE' => '970',  //'Sell Direct Unless Bundled'
            'K5HVL3VHV6R3X2Y7HGGDNP22' => '900',  //'Sell Direct Exclusively'
            'R6T4TILEYBOD4ELXMX6MCMAB' => '899',  //'Retail Exclusive Product'
            'MXKR4BBSU2VA5BAZ5NUOAMFN' => '100',  //'No UPC 2'
            'WRC53LSP3QWFHRNK2IOZMAHF' => '101',  //'No UPC'
            'DFCYLBGVZECPDDPLH5UBME2D' => '0',    //'No Stock'
            'TMF6CWSYBTJJJSDXCUES5SNB' => '899',  //'In-Stock Only'
            '37CCULRCQYYLAAFKZL45CUPN' => '1981', //'In-Stock Dealer Protect'
            'TY7MYVO3PAJAUIACB5E72ELW' => '1966', //'Always Sell Direct'
            '7KRHDH7A7T7N2MVJO5THWWTJ' => '803',  //'Bicycle'
            'YXAAKTLCJH4YNN56Y5IXPJQN' => '199',  //'In-Stock Dealer Protect 2'
        ];

        $actual = $this->SquarePos->listInventoriesByVariantId(static::ACCESS_TOKEN, static::LOCATION_ID);

        $this->assertEquals($expected, $actual);
    }

    public function testCreateCustomerFromEcommerce()
    {
        $address = new \Square\Models\Address();
        $address->setAddressLine1('2400 Nipigon Rd.');
        $address->setAddressLine2(null);
        $address->setLocality('Thunder Bay');
        $address->setAdministrativeDistrictLevel1('Ontario');
        $address->setPostalCode('P7C 4W1');
        $address->setCountry('CA');

        $request = new \Square\Models\CreateCustomerRequest();
        $request->setGivenName('Aron');
        $request->setFamilyName('Schmidt');
        $request->setCompanyName(null);
        $request->setEmailAddress('<EMAIL>');
        $request->setAddress($address);
        $request->setPhoneNumber('8071231234');
        $request->setReferenceId(BASE_PATH . 'customer/1001/orders');
        $request->setNote('ShipEarly order #SE0012501');

        $expected = new \Square\Models\Customer();
        $expected->setId('DRY6QXBCC4YPZ9FDBDXR7HJ7X4');
        $expected->setCreatedAt('2020-08-28T02:09:44.752Z');
        $expected->setUpdatedAt('2020-08-28T02:09:44Z');
        $expected->setGivenName('Aron');
        $expected->setFamilyName('Schmidt');
        $expected->setCompanyName(null);
        $expected->setEmailAddress('<EMAIL>');
        $expected->setAddress($address);
        $expected->setPhoneNumber('8071231234');
        $expected->setReferenceId(BASE_PATH . 'customer/1001/orders');
        $expected->setNote('ShipEarly order #SE0012501');
        $expected->setPreferences(new \Square\Models\CustomerPreferences());
        $expected->setCreationSource('THIRD_PARTY');

        $SquarePos = $this->mockSquarePos(['createCustomer']);
        $SquarePos->expects($this->once())->method('createCustomer')->with(static::ACCESS_TOKEN, $request)->willReturn($expected);

        // Comment this to use the real API
        $this->SquarePos = $SquarePos;

        $shipping = [
            'email' => '<EMAIL>',
            'firstname' => 'Aron',
            'lastname' => 'Schmidt',
            'company' => '',
            'street' => '2400 Nipigon Rd.',
            'city' => 'Thunder Bay',
            'region' => 'Ontario',
            'postcode' => 'P7C 4W1',
            'country_id' => 'ca',
            'telephone' => '8071231234',
        ];
        $orderInfo = [
            'shipping_address1' => '2400 Nipigon Rd.',
            'shipping_address2' => '',
            'customerID' => '1001',
            'orderID' => '#SE0012501',
        ];
        $actual = $this->SquarePos->createCustomerFromEcommerce(static::ACCESS_TOKEN, (object)$shipping, $orderInfo);

        $this->assertEquals($expected, $actual);
    }

    private function mockSquarePos(array $methods = [])
    {
        $methods[] = 'getEnvironment';
        /** @var SquarePosComponent|PHPUnit_Framework_MockObject_MockObject $SquarePos */
        $SquarePos = $this->getMock(SquarePosComponent::class, $methods, [new ComponentCollection()]);

        //FIXME switch to sandbox env after obtaining sandbox 'SQUARE_APPLICATION_ID'
        $SquarePos->expects($this->any())->method('getEnvironment')->willReturn(\Square\Environment::PRODUCTION);

        return $SquarePos;
    }
}
