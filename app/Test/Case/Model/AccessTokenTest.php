<?php
App::uses('AppTestCase', 'TestSuite');
App::uses('AccessToken', 'Model');

/**
 * AccessToken Test Case.
 *
 * Run with the following console command:
 *
 * ./app/Console/cake test app Model/AccessToken
 *
 * @property AccessToken|PHPUnit_Framework_MockObject_MockObject $AccessToken
 */
class AccessTokenTest extends AppTestCase
{
    public $fixtures = [
        'app.access_token',
    ];

    /**
     * Reference to the static Unix timestamp kept by CakeTest.
     *
     * @var int
     * @see CakeTestCase::date
     */
    private $time;

    public function setUp()
    {
        parent::setUp();

        $this->time = (int)static::date('U');

        $this->AccessToken = $this->getMockForModel('AccessToken', ['date_create_now']);
        $this->AccessToken->expects($this->any())->method('date_create_now')->willReturnCallback(function() {
            return new DateTime('@' . $this->time);
        });

        $this->AccessToken->truncate();
    }

    public function tearDown()
    {
        unset($this->AccessToken);
        parent::tearDown();
    }

    public function testRequestTokenAndRefresh()
    {
        // Test create new token
        $initToken = $this->AccessToken->requestToken(null, 10);
        $this->assertEmpty($this->AccessToken->validationErrors, Debugger::exportVar(['initToken_validationErrors' => $this->AccessToken->validationErrors]));
        $this->assertRegExp('/' . Router::UUID . '/', (string)$initToken, 'initToken Validation::uuid');
        $expected = [
            'id' => '1',
            'token' => $initToken,
            'access_granted' => false,
            'expires_at' => format_datetime('@' . ($this->time + 10)),
        ];
        $this->assertEquals($expected, $this->findRecord($initToken), 'newRecord === expected');

        // Test refresh existing token
        $refreshedToken = $this->AccessToken->requestToken($initToken, '1 minute');
        $this->assertEmpty($this->AccessToken->validationErrors, Debugger::exportVar(['refreshedToken_validationErrors' => $this->AccessToken->validationErrors]));
        $this->assertEquals($initToken, $refreshedToken, 'refreshedToken === initToken');
        $expected = array_merge($expected, [
            'expires_at' => format_datetime('@' . ($this->time + 60)),
        ]);
        $this->assertEquals($expected, $this->findRecord($refreshedToken), 'refreshedRecord === newRecord');
    }

    public function testRevokeToken()
    {
        $token = $this->saveRecord(['expires_at' => format_datetime('@' . ($this->time + 10))]);

        $success = $this->AccessToken->revokeToken($token);

        $this->assertFalse($this->AccessToken->fieldByConditions('token', ['token' => $token]), 'Revoked token must not exist');
        $this->assertTrue($success, 'Revoke success response');
    }

    public function testRevokeNullToken()
    {
        $expiredCookieToken = null;
        $this->assertTrue($this->AccessToken->revokeToken($expiredCookieToken));
    }

    public function testRequestAccessAndRefresh()
    {
        $this->AccessToken->accessLimit = 2;

        // Expired tokens must not block access
        $expiredWaitingToken = $this->saveRecord(['access_granted' => false, 'expires_at' => format_datetime('@' . ($this->time + 0))]);
        $expiredAccessingToken = $this->saveRecord(['access_granted' => true, 'expires_at' => format_datetime('@' . ($this->time + 0))]);
        // Accessing tokens must not block access while under accessLimit
        $accessingToken = $this->saveRecord(['access_granted' => true, 'expires_at' => format_datetime('@' . ($this->time + (30 * 60)))]);

        $initToken = $this->saveRecord(['access_granted' => false, 'expires_at' => format_datetime('@' . ($this->time + 10))]);
        $this->assertRegExp('/' . Router::UUID . '/', (string)$initToken, 'initToken Validation::uuid');

        // Tokens waiting after target must not block access
        $this->saveRecord(['access_granted' => false, 'expires_at' => format_datetime('@' . ($this->time + 10))]);

        // Test grant access
        $initToken = $this->AccessToken->requestAccess($initToken, '10 minutes');
        $this->assertEmpty($this->AccessToken->validationErrors, Debugger::exportVar(['refreshedToken_validationErrors' => $this->AccessToken->validationErrors]));
        $this->assertRegExp('/' . Router::UUID . '/', (string)$initToken, 'initToken Validation::uuid');
        $expected = [
            'id' => '4',
            'token' => $initToken,
            'access_granted' => true,
            'expires_at' => format_datetime('@' . ($this->time + (10 * 60))),
        ];
        $this->assertEquals($expected, $this->findRecord($initToken));

        // Test refresh access
        $refreshedToken = $this->AccessToken->requestAccess($initToken, '30 minutes');
        $this->assertEmpty($this->AccessToken->validationErrors, Debugger::exportVar(['refreshedToken_validationErrors' => $this->AccessToken->validationErrors]));
        $this->assertEquals($initToken, $refreshedToken, 'refreshedToken === initToken');
        $expected = array_merge($expected, [
            'expires_at' => format_datetime('@' . ($this->time + (30 * 60))),
        ]);
        $this->assertEquals($expected, $this->findRecord($initToken));
    }

    public function testRequestAccessRejectedWhileWaiting()
    {
        $this->AccessToken->accessLimit = 150;
        $this->saveRecord(['access_granted' => false, 'expires_at' => format_datetime('@' . ($this->time + 10))]);

        $token = $this->AccessToken->requestAccess(null, '30 minutes');

        $this->assertEmpty($this->AccessToken->validationErrors, Debugger::exportVar(['refreshedToken_validationErrors' => $this->AccessToken->validationErrors]));
        $this->assertEmpty($token, 'Token must not exist');
    }

    public function testRequestAccessRejectedOverLimit()
    {
        $this->AccessToken->accessLimit = 2;
        $this->saveRecord(['access_granted' => true, 'expires_at' => format_datetime('@' . ($this->time + (30 * 60)))]);
        $this->saveRecord(['access_granted' => true, 'expires_at' => format_datetime('@' . ($this->time + (30 * 60)))]);

        $token = $this->AccessToken->requestAccess(null, '30 minutes');

        $this->assertEmpty($this->AccessToken->validationErrors, Debugger::exportVar(['refreshedToken_validationErrors' => $this->AccessToken->validationErrors]));
        $this->assertEmpty($token, 'Token must not exist');
    }

    public function testDeleteAllExpired()
    {
        $activeToken = $this->saveRecord(['expires_at' => format_datetime('@' . ($this->time + 1))]);
        $expiredToken = $this->saveRecord(['expires_at' => format_datetime('@' . ($this->time + 0))]);

        $this->AccessToken->deleteAllExpired();

        $this->assertEquals([$activeToken], array_keys($this->AccessToken->find('list')));
    }

    /**
     * @dataProvider providerCountWaiting
     * @throws Exception
     */
    public function testCountWaiting(?string $tokenName, int $expectedCount)
    {
        $tokensByName = $this->createTokenCaseMap();
        $tokenArg = $tokensByName[$tokenName] ?? null;

        $actualCount = $this->AccessToken->countWaiting($tokenArg);

        $this->assertEquals($expectedCount, $actualCount);
    }

    public function providerCountWaiting()
    {
        return [
            [null, 2],
            ['waiting_behind', 2],
            ['waiting_ahead', 1],
            ['waiting_expired', 0],
            ['accessing_behind', 0],
            ['accessing_ahead', 0],
            ['accessing_expired', 0],
        ];
    }

    /**
     * @dataProvider providerCountAccessing
     * @throws Exception
     */
    public function testCountAccessing(?string $tokenName, int $expectedCount)
    {
        $tokensByName = $this->createTokenCaseMap();
        $tokenArg = $tokensByName[$tokenName] ?? null;

        $actualCount = $this->AccessToken->countAccessing($tokenArg);

        $this->assertEquals($expectedCount, $actualCount);
    }

    public function providerCountAccessing()
    {
        return [
            [null, 2],
            ['waiting_behind', 2],
            ['waiting_ahead', 2],
            ['waiting_expired', 2],
            ['accessing_behind', 1],
            ['accessing_ahead', 1],
            ['accessing_expired', 2],
        ];
    }

    /**
     * @dataProvider providerGetNextInWaiting
     * @throws Exception
     */
    public function testGetNextInWaiting(?string $tokenName, ?string $expectedName)
    {
        $tokensByName = $this->createTokenCaseMap();
        $tokenArg = $tokensByName[$tokenName] ?? null;

        $actualToken = $this->AccessToken->getNextInWaiting($tokenArg);

        $actualName = array_search($actualToken, $tokensByName, true);
        $this->assertEquals($expectedName, $actualName);
    }

    public function providerGetNextInWaiting()
    {
        return [
            [null, 'waiting_ahead'],
            ['waiting_behind', 'waiting_ahead'],
            ['waiting_ahead', 'waiting_ahead'],
            ['waiting_expired', 'waiting_expired'],
            ['accessing_behind', 'accessing_behind'],
            ['accessing_ahead', 'accessing_ahead'],
            ['accessing_expired', 'accessing_expired'],
        ];
    }

    public function testGetNextInWaitingWithEmptyLineIsNull()
    {
        $this->assertNull($this->AccessToken->getNextInWaiting());
    }

    /**
     * Create token cases with names for easier to read assertions.
     *
     * @return string[]
     * @throws Exception
     */
    private function createTokenCaseMap(): array
    {
        return [
            'accessing_expired' => $this->saveRecord(['access_granted' => true, 'expires_at' => format_datetime('@' . ($this->time + 0))]),
            'accessing_ahead' => $this->saveRecord(['access_granted' => true, 'expires_at' => format_datetime('@' . ($this->time + 10))]),
            'accessing_behind' => $this->saveRecord(['access_granted' => true, 'expires_at' => format_datetime('@' . ($this->time + 10))]),
            'waiting_expired' => $this->saveRecord(['access_granted' => false, 'expires_at' => format_datetime('@' . ($this->time + 0))]),
            'waiting_ahead' => $this->saveRecord(['access_granted' => false, 'expires_at' => format_datetime('@' . ($this->time + 10))]),
            'waiting_behind' => $this->saveRecord(['access_granted' => false, 'expires_at' => format_datetime('@' . ($this->time + 10))]),
        ];
    }

    /**
     * Retrieve a record directly.
     *
     * @param string|null $token
     * @return array
     */
    private function findRecord(?string $token): array
    {
        return ($this->AccessToken->findByToken($token, ['id', 'token', 'access_granted', 'expires_at'])['AccessToken'] ?? []);
    }

    /**
     * Create a record directly.
     *
     * @param array $data
     * @return array|bool|mixed|null
     * @throws Exception
     */
    private function saveRecord(array $data): ?string
    {
        $this->AccessToken->clear();
        $this->AccessToken->save($data);
        $this->assertEmpty($this->AccessToken->validationErrors, Debugger::exportVar(['validationErrors' => $this->AccessToken->validationErrors]));

        return ($this->AccessToken->id ?: null);
    }
}
