<?php
App::uses('AppTestCase', 'TestSuite');
App::uses('B2bCartProduct', 'Model');

/**
 * B2bCartProduct Test Case.
 *
 * Run with the following console command:
 *
 * ./app/Console/cake test app Model/B2bCartProduct
 *
 * @property B2bCartProduct $B2bCartProduct
 */
class B2bCartProductTest extends AppTestCase
{
    const B2B_CART_ID = '1';

    public $fixtures = [
        'app.b2b_cart',
        'app.b2b_cart_product',
        'app.brand_staff_permission',
        'app.fulfillment_product',
        'app.i18n',
        'app.inventory_transfer',
        'app.inventory_transfer_product',
        'app.manufacturer_retailer',
        'app.manufacturer_retailer_sales_rep',
        'app.manufacturer_sales_rep',
        'app.order_product',
        'app.order_refund_product',
        'app.product',
        'app.product_state_fee',
        'app.staff_permission',
        'app.user',
        'app.warehouse',
        'app.warehouse_product',
    ];

    public function setUp()
    {
        parent::setUp();
        $this->B2bCartProduct = ClassRegistry::init('B2bCartProduct');
    }

    public function tearDown()
    {
        unset($this->B2bCartProduct);
        parent::tearDown();
    }

    public function testAfterSaveUpdatesCart()
    {
        $b2bCartId = '1';

        $this->B2bCartProduct->save([
            'b2b_cart_id' => $b2bCartId,
            'product_id' => '21',
            'warehouse_id' => '1',
            'inventory_transfer_id' => '',
            'quantity' => '1',
        ]);
        $actual = $this->B2bCartProduct->B2bCart->fieldByConditions('updated_at', ['id' => $b2bCartId]);

        $this->assertNotEquals('2020-09-24 00:00:00', $actual);
    }

    public function testAfterDeleteRemovesCart()
    {
        $b2bCartId = '1';

        $this->B2bCartProduct->deleteAllJoinless(['b2b_cart_id' => $b2bCartId], false, true);

        $this->assertFalse($this->B2bCartProduct->B2bCart->exists(['B2bCart.id' => $b2bCartId]), 'B2bCart::exists');
    }

    /**
     * @dataProvider providerValidations
     * @param array $record
     * @param array $expected
     * @see B2bCartProduct::productBelongsToCartUser
     * @see B2bCartProduct::warehouseBelongsToCartUser
     * @see B2bCartProduct::hasInventoryForQuantity
     */
    public function testValidations(array $record, array $expected)
    {
        $this->B2bCartProduct->clear();
        $this->B2bCartProduct->set($record);
        $this->B2bCartProduct->validates();

        $this->assertEquals($expected, $this->B2bCartProduct->validationErrors);
    }

    public function providerValidations()
    {
        $validRecord = [
            'id' => null,
            'b2b_cart_id' => '1',
            'product_id' => '21',
            'warehouse_id' => '1',
            'inventory_transfer_id' => '',
            'quantity' => '1',
        ];

        $cartValidations = [
            'accept_valid_record' => [
                'record' => $validRecord,
                'expected' => [
                ],
            ],
            'accept_if_cart_is_not_provided' => [
                // Allow scenarios where the cart is created in the same transaction
                'record' => array_diff_key($validRecord, array_flip(['b2b_cart_id'])),
                'expected' => [
                ],
            ],
            'reject_if_assocs_do_not_belong_to_cart' => [
                'record' => array_merge($validRecord, [
                    'b2b_cart_id' => '1',
                    'product_id' => '53',
                    'warehouse_id' => '9',
                ]),
                'expected' => [
                    'product_id' => ['Product does not belong to cart owner'],
                    'warehouse_id' => ['Warehouse does not belong to cart owner'],
                ],
            ],
            'reject_if_assocs_do_not_belong_to_existing_item_cart' => [
                'record' => array_merge(array_diff_key($validRecord, array_flip(['b2b_cart_id'])), [
                    'id' => '1',
                    'product_id' => '53',
                    'warehouse_id' => '9',
                ]),
                'expected' => [
                    'product_id' => ['Product does not belong to cart owner'],
                    'warehouse_id' => ['Warehouse does not belong to cart owner'],
                ],
            ],
            'reject_if_cart_is_not_valid' => [
                'record' => array_merge($validRecord, [
                    'b2b_cart_id' => '',
                ]),
                'expected' => [
                    'b2b_cart_id' => ['Invalid b2b cart id'],
                ],
            ],
            'reject_if_cart_does_not_exist' => [
                'record' => array_merge($validRecord, [
                    'b2b_cart_id' => PHP_INT_MAX,
                ]),
                'expected' => [
                    'product_id' => ['Product does not belong to cart owner'],
                    'warehouse_id' => ['Warehouse does not belong to cart owner'],
                ],
            ],
        ];
        $productValidations = [
            'reject_if_product_does_not_belong' => [
                'record' => array_merge($validRecord, [
                    'b2b_cart_id' => '1',
                    'product_id' => '29',
                ]),
                'expected' => [
                    'product_id' => ['Product does not belong to cart owner'],
                    'quantity' => ['The order quantity exceeds the amount of inventory available'],
                ],
            ],
            'reject_if_invalid_product' => [
                'record' => array_merge($validRecord, [
                    'product_id' => '',
                ]),
                'expected' => [
                    'product_id' => ['Invalid product id'],
                    'quantity' => ['The order quantity exceeds the amount of inventory available'],
                ],
            ],
            'reject_if_product_does_not_exist' => [
                'record' => array_merge($validRecord, [
                    'product_id' => PHP_INT_MAX,
                ]),
                'expected' => [
                    'product_id' => ['Product does not belong to cart owner'],
                    'quantity' => ['The order quantity exceeds the amount of inventory available'],
                ],
            ],
        ];
        $warehouseValidations = [
            'accept_if_empty_warehouse' => [
                'record' => array_merge($validRecord, [
                    'warehouse_id' => '',
                ]),
                'expected' => [
                ],
            ],
            'reject_if_warehouse_does_not_belong' => [
                'record' => array_merge($validRecord, [
                    'b2b_cart_id' => '1',
                    'warehouse_id' => '3',
                ]),
                'expected' => [
                    'warehouse_id' => ['Warehouse does not belong to cart owner'],
                    'quantity' => ['The order quantity exceeds the amount of inventory available'],
                ],
            ],
            'reject_if_invalid_warehouse' => [
                'record' => array_merge($validRecord, [
                    'warehouse_id' => '0',
                ]),
                'expected' => [
                    'warehouse_id' => ['Invalid warehouse id'],
                ],
            ],
            'reject_if_warehouse_does_not_exist' => [
                'record' => array_merge($validRecord, [
                    'warehouse_id' => PHP_INT_MAX,
                ]),
                'expected' => [
                    'warehouse_id' => ['Warehouse does not belong to cart owner'],
                    'quantity' => ['The order quantity exceeds the amount of inventory available'],
                ],
            ],
        ];
        $quantityValidations = [
            'accept_if_enough_warehouse_inventory' => [
                'record' => array_merge($validRecord, [
                    'product_id' => '21',
                    'warehouse_id' => '1',
                    'quantity' => '5',
                ]),
                'expected' => [
                ],
            ],
            'reject_if_not_enough_warehouse_inventory' => [
                'record' => array_merge($validRecord, [
                    'product_id' => '21',
                    'warehouse_id' => '1',
                    'quantity' => '6',
                ]),
                'expected' => [
                    'quantity' => ['The order quantity exceeds the amount of inventory available'],
                ],
            ],
            'accept_if_enough_product_level_inventory' => [
                'record' => array_merge($validRecord, [
                    'product_id' => '21',
                    'warehouse_id' => null,
                    'quantity' => '174',
                ]),
                'expected' => [
                ],
            ],
            'reject_if_not_enough_product_level_inventory' => [
                'record' => array_merge($validRecord, [
                    'product_id' => '21',
                    'warehouse_id' => null,
                    'quantity' => '175',
                ]),
                'expected' => [
                    'quantity' => ['The order quantity exceeds the amount of inventory available'],
                ],
            ],
            'reject_if_product_not_in_warehouse' => [
                'record' => array_merge($validRecord, [
                    'product_id' => '20',
                    'warehouse_id' => '1',
                ]),
                'expected' => [
                    'quantity' => ['The order quantity exceeds the amount of inventory available'],
                ],
            ],
            'reject_if_invalid_quantity' => [
                'record' => array_merge($validRecord, [
                    'quantity' => '0',
                ]),
                'expected' => [
                    'quantity' => ['The order quantity must be a number greater than zero'],
                ],
            ],
        ];

        return (
            $cartValidations +
            $productValidations +
            $warehouseValidations +
            $quantityValidations
        );
    }

    public function testCountAllItems()
    {
        $expected = [
            'brand' => 4,
            'retailer' => 12,
            'sales_rep' => 3,
        ];
        $actual = [
            'brand' => $this->B2bCartProduct->countAllItems('8'),
            'retailer' => $this->B2bCartProduct->countAllItems('7'),
            'sales_rep' => $this->B2bCartProduct->countAllItems('21'),
        ];
        $this->assertEquals($expected, $actual);
    }

    public function testSaveManyToCart()
    {
        $b2bCartId = static::B2B_CART_ID;

        $b2bCartProducts = [
            0 => [
                'product_id' => '21',
                'warehouse_id' => '1',
                'inventory_transfer_id' => '',
                'quantity' => '1',
            ],
            1 => [
                'product_id' => '23',
                'warehouse_id' => '2',
                'inventory_transfer_id' => '',
                'quantity' => '1',
            ],
        ];
        $success = $this->B2bCartProduct->saveManyToCart($b2bCartId, $b2bCartProducts);

        $actual = $this->B2bCartProduct->find('all', [
            'conditions' => ['b2b_cart_id' => $b2bCartId],
            'fields' => ['product_id', 'warehouse_id', 'quantity'],
            'order' => ['warehouse_id', 'product_id'],
        ]);
        $expected = [
            0 => [
                'B2bCartProduct' => [
                    'product_id' => '21',
                    'warehouse_id' => '1',
                    'quantity' => '1',
                ],
            ],
            1 => [
                'B2bCartProduct' => [
                    'product_id' => '21',
                    'warehouse_id' => '2',
                    'quantity' => '2',
                ],
            ],
            2 => [
                'B2bCartProduct' => [
                    'product_id' => '23',
                    'warehouse_id' => '2',
                    'quantity' => '1',
                ],
            ],
        ];
        $this->assertEquals($expected, $actual);

        $this->assertTrue($success, json_encode(['errors' => $this->B2bCartProduct->validationErrors]));
    }

    public function testSaveManyToWrongCart()
    {
        $b2bCartId = static::B2B_CART_ID;

        $b2bCartProducts = [
            [
                // product belongs to user_id = 12
                'product_id' => '29',
                'warehouse_id' => '1',
                'inventory_transfer_id' => '',
                'quantity' => '1',
            ],
            [
                // warehouse belongs to user_id = 19
                'product_id' => '23',
                'warehouse_id' => '9',
                'inventory_transfer_id' => '',
                'quantity' => '1',
            ],
            [
                // product and warehouse belong to user_id = 19
                'product_id' => '53',
                'warehouse_id' => '9',
                'inventory_transfer_id' => '',
                'quantity' => '1',
            ],
        ];
        $success = $this->B2bCartProduct->saveManyToCart($b2bCartId, $b2bCartProducts);

        $actual = $this->B2bCartProduct->find('all', [
            'conditions' => ['b2b_cart_id' => $b2bCartId],
            'fields' => ['product_id', 'warehouse_id', 'quantity'],
            'order' => ['warehouse_id', 'product_id'],
        ]);
        $expected = [
            0 => [
                'B2bCartProduct' => [
                    'product_id' => '21',
                    'warehouse_id' => '2',
                    'quantity' => '2',
                ],
            ],
            1 => [
                'B2bCartProduct' => [
                    'product_id' => '23',
                    'warehouse_id' => '2',
                    'quantity' => '2',
                ],
            ],
        ];
        $this->assertEquals($expected, $actual, 'Records modified');

        $expected = [
            0 => [
                'product_id' => [
                    0 => 'Product does not belong to cart owner',
                ],
                'quantity' => [
                    0 => 'The order quantity exceeds the amount of inventory available',
                ],
            ],
            1 => [
                'warehouse_id' => [
                    0 => 'Warehouse does not belong to cart owner',
                ],
                'quantity' => [
                    0 => 'The order quantity exceeds the amount of inventory available',
                ],
            ],
            2 => [
                'product_id' => [
                    0 => 'Product does not belong to cart owner',
                ],
                'warehouse_id' => [
                    0 => 'Warehouse does not belong to cart owner',
                ],
            ],
        ];
        $this->assertEquals($expected, $this->B2bCartProduct->validationErrors, 'validationErrors');

        $this->assertFalse($success, 'Success response');
    }
}
