<?php
App::uses('AppTestCase', 'TestSuite');
App::uses('B2bShippingRate', 'Model');

/**
 * B2bShippingRate Test Case.
 *
 * Run with the following console command:
 *
 * ./app/Console/cake test app Model/B2bShippingRate
 *
 * @property B2bShippingRate $B2bShippingRate
 */
class B2bShippingRateTest extends AppTestCase
{
    const B2B_SHIPPING_RATE_ID = '3';
    const B2B_SHIPPING_ZONE_ID = '1';
    const USER_ID_BRAND = '12';
    const USER_ID_RETAOER = '15';

    public $fixtures = [
        'app.b2b_shipping_rate',
        'app.b2b_shipping_rate_category',
        'app.b2b_shipping_rate_title',
        'app.b2b_shipping_zone',
        'app.b2b_shipping_zone_tier',
        'app.pricing_tier',
        'app.user',
    ];

    public function setUp()
    {
        parent::setUp();
        $this->B2bShippingRate = ClassRegistry::init('B2bShippingRate');
    }

    public function tearDown()
    {
        unset($this->B2bShippingRate);
        parent::tearDown();
    }

    public function testFindForIndex()
    {
        $zoneId = static::B2B_SHIPPING_ZONE_ID;

        $actual = $this->B2bShippingRate->findForIndex($zoneId);

        $expected = [
            0 => [
                'B2bShippingRate' => [
                    'id' => '1',
                    'b2b_shipping_zone_id' => '1',
                    'type' => 'price',
                    'name' => 'Domestic Price Rate',
                    'price' => '15.00',
                    'min_range' => '0.00',
                    'max_range' => null,
                    'product_categories' => [],
                    'product_titles' => [],
                    'key' => 'price_domestic-price-rate',
                ],
            ],
            1 => [
                'B2bShippingRate' => [
                    'id' => '2',
                    'b2b_shipping_zone_id' => '1',
                    'type' => 'unit',
                    'name' => 'Bulk Bikes',
                    'price' => '1.00',
                    'min_range' => '5.00',
                    'max_range' => null,
                    'product_categories' => ['Cycling'],
                    'product_titles' => [],
                    'key' => 'unit_bulk-bikes',
                ],
            ],
            2 => [
                'B2bShippingRate' => [
                    'id' => '3',
                    'b2b_shipping_zone_id' => '1',
                    'type' => 'weight_lb',
                    'name' => 'Light Bikes',
                    'price' => '10.00',
                    'min_range' => '0.00',
                    'max_range' => '25.00',
                    'product_categories' => ['Cycling'],
                    'product_titles' => [],
                    'key' => 'weight-lb_light-bikes',
                ],
            ],
        ];
        $this->assertEquals($expected, $actual);
    }

    public function testFindForEdit()
    {
        $id = static::B2B_SHIPPING_RATE_ID;

        $actual = $this->B2bShippingRate->findForEdit($id);

        $expected = [
            'B2bShippingRate' => [
                'id' => $id,
                'b2b_shipping_zone_id' => '1',
                'type' => 'weight_lb',
                'name' => 'Light Bikes',
                'price' => '10.00',
                'min_range' => '0.00',
                'max_range' => '25.00',
                'product_categories' => ['Cycling'],
                'product_titles' => [],
                'key' => 'weight-lb_light-bikes',
            ],
        ];
        $this->assertEquals($expected, $actual);
    }

    public function testFindAllForItems()
    {
        $brandId = static::USER_ID_BRAND;
        $retailerId = static::USER_ID_RETAOER;

        $actual = $this->B2bShippingRate->findAllForItems($brandId, $retailerId);
        $actual = array_map(function($record) {
            $record['B2bShippingRateCategory'] = Hash::remove($record['B2bShippingRateCategory'], '{n}.id');

            return $record;
        }, $actual);

        $expected = [
            0 => [
                'B2bShippingRate' => [
                    'id' => '1',
                    'type' => 'price',
                    'name' => 'Domestic Price Rate',
                    'price' => '15.00',
                    'min_range' => '0.00',
                    'max_range' => null,
                ],
                'B2bShippingRateCategory' => [
                ],
                'B2bShippingRateTitle' => [
                ],
            ],
            1 => [
                'B2bShippingRate' => [
                    'id' => '2',
                    'type' => 'unit',
                    'name' => 'Bulk Bikes',
                    'price' => '1.00',
                    'min_range' => '5.00',
                    'max_range' => null,
                ],
                'B2bShippingRateCategory' => [
                    0 => [
                        'b2b_shipping_rate_id' => '2',
                        'product_category' => 'Cycling',
                    ],
                ],
                'B2bShippingRateTitle' => [
                ],
            ],
            2 => [
                'B2bShippingRate' => [
                    'id' => '3',
                    'type' => 'weight_lb',
                    'name' => 'Light Bikes',
                    'price' => '10.00',
                    'min_range' => '0.00',
                    'max_range' => '25.00',
                ],
                'B2bShippingRateCategory' => [
                    0 => [
                        'b2b_shipping_rate_id' => '3',
                        'product_category' => 'Cycling',
                    ],
                ],
                'B2bShippingRateTitle' => [
                ],
            ],
        ];
        $this->assertEquals($expected, $actual);
    }
}
