<?php
App::uses('AppTestCase', 'TestSuite');
App::uses('BrandStaffRole', 'Model');

/**
 * BrandStaffRole Test Case.
 *
 * Run with the following console command:
 *
 * ./app/Console/cake test app Model/BrandStaffRole
 *
 * @property BrandStaffRole $BrandStaffRole
 */
class BrandStaffRoleTest extends AppTestCase
{
    const USER_ID_BRAND = '12';

    public $fixtures = [
        'app.brand_staff_role',
        'app.brand_staff',
        'app.brand_staff_permission',
        'app.staff_permission',
        'app.user',
    ];

    public function setUp()
    {
        parent::setUp();
        $this->BrandStaffRole = ClassRegistry::init('BrandStaffRole');
    }

    public function tearDown()
    {
        unset($this->BrandStaffRole);
        parent::tearDown();
    }

    public function testListOptions()
    {
        $actual = $this->BrandStaffRole->listOptions(static::USER_ID_BRAND);
        $expected = [
            '3' => 'Admin',
            '4' => 'Support',
        ];
        $this->assertEquals($expected, $actual);
    }

    /**
     * @dataProvider providerSaveByName
     * @param string $name
     * @param array $expected
     */
    public function testSaveByName(string $name, array $expected)
    {
        $roleId = $this->BrandStaffRole->saveByName(static::USER_ID_BRAND, $name);
        $this->assertNotEmpty($roleId, json_encode(['errors' => $this->BrandStaffRole->validationErrors]));

        $actual = $this->listBrandStaffRoleNames(static::USER_ID_BRAND);
        $this->assertEquals($expected, $actual);
    }

    public function providerSaveByName()
    {
        return [
            'create' => [
                'name' => 'Finance',
                'expected' => [
                    'Admin',
                    'Finance',
                    'Support',
                ],
            ],
            'update' => [
                'name' => 'SUPPORT',
                'expected' => [
                    'Admin',
                    'SUPPORT',
                ],
            ],
        ];
    }

    public function testDeleteAllOrphansWithBrandId()
    {
        $success = $this->BrandStaffRole->deleteAllOrphansWithBrandId(static::USER_ID_BRAND);
        $this->assertTrue($success, json_encode(compact('success')));

        $actual = $this->listBrandStaffRoleNames(static::USER_ID_BRAND);
        $expected = [
            'Admin',
        ];
        $this->assertEquals($expected, $actual);
    }

    private function listBrandStaffRoleNames($brandId)
    {
        return array_values(
            $this->BrandStaffRole->find('list', [
                'conditions' => ['BrandStaffRole.brand_id' => $brandId],
                'fields' => ['BrandStaffRole.id', 'BrandStaffRole.name'],
                'order' => ['BrandStaffRole.name' => 'ASC'],
            ])
        );
    }
}
