<?php
App::uses('AppTestCase', 'TestSuite');
App::uses('BrandStaff', 'Model');
App::uses('Permissions', 'Utility');

/**
 * BrandStaff Test Case.
 *
 * Run with the following console command:
 *
 * ./app/Console/cake test app Model/BrandStaff
 *
 * @property BrandStaff $BrandStaff
 * @property BrandStaffPermission $BrandStaffPermission
 */
class BrandStaffTest extends AppTestCase
{
    const USER_ID_BRAND = '12';
    const USER_ID_BRAND_STAFF = '25';
    const BRAND_STAFF_ID_NEW = '4';
    const ROLE_ID_NEW = '7';

    public $fixtures = [
        'app.brand_staff',
        'app.brand_staff_permission',
        'app.brand_staff_role',
        'app.contact',
        'app.contactpersons',
        'app.i18n',
        'app.staff_permission',
        'app.user',
        'app.user_setting',
    ];

    public function setUp()
    {
        parent::setUp();
        $this->BrandStaff = ClassRegistry::init('BrandStaff');
        $this->BrandStaffPermission = ClassRegistry::init('BrandStaffPermission');
    }

    public function tearDown()
    {
        unset($this->BrandStaffPermission);
        unset($this->BrandStaff);
        parent::tearDown();
    }

    public function testCountForIndex()
    {
        $actual = $this->BrandStaff->countForIndex(static::USER_ID_BRAND);
        $this->assertSame(1, $actual);
    }

    public function testFindAllForIndex()
    {
        $actual = $this->BrandStaff->findAllForIndex(static::USER_ID_BRAND);
        $expected = [
            [
                'BrandStaff' => [
                    'id' => '2',
                    'brand_id' => '12',
                    'staff_id' => '25',
                    'role_id' => '3',
                    'has_full_permissions' => false,
                ],
                'StaffUser' => [
                    'id' => '25',
                    'email_address' => '<EMAIL>',
                    'company_name' => 'Test BrandStaff',
                    'last_login' => null,
                    'language_code' => null,
                ],
                'Role' => [
                    'id' => '3',
                    'name' => 'Admin',
                ],
            ],
        ];
        $this->assertEquals($expected, $actual);
    }

    public function testFindForView()
    {
        $existingPermissions = [
            'orders' => [
                'id' => '5',
                'brand_staff_id' => '2',
                'name' => 'orders',
                'level' => '2',
            ],
            'products' => [
                'id' => '4',
                'brand_staff_id' => '2',
                'name' => 'products',
                'level' => '1',
            ],
            'retailers' => [
                'id' => '6',
                'brand_staff_id' => '2',
                'name' => 'retailers',
                'level' => '0',
            ],
        ];

        $actual = $this->BrandStaff->findForView(static::USER_ID_BRAND_STAFF);
        $expected = [
            'BrandStaff' => [
                'id' => '2',
                'brand_id' => '12',
                'staff_id' => '25',
                'role_id' => '3',
                'has_full_permissions' => false,
                'Permission' => $existingPermissions + Permissions::buildFieldsForAllPermissionsByName(Permissions::BRAND_STAFF_PERMISSIONS, Permissions::LEVEL_NONE),
            ],
            'StaffUser' => [
                'id' => '25',
                'email_address' => '<EMAIL>',
                'company_name' => 'Test BrandStaff',
                'language_code' => null,
            ],
            'Contactpersons' => [
                'id' => '21',
                'firstname' => 'Test',
                'lastname' => 'BrandStaff',
            ],
            'CompanyTelephone' => [
                'id' => '66',
                'value' => '**********',
            ],
        ];
        $this->assertEquals($expected, $actual);
    }

    /**
     * @dataProvider providerFindHasFullPermissions
     * @param string $permissionLevel
     * @param string $expected
     */
    public function testFindHasFullPermissionsForIndex(string $permissionLevel, string $expected)
    {
        $brandId = static::USER_ID_BRAND;

        $this->_updateAllPermissionLevels($permissionLevel, ['BrandStaff.brand_id' => $brandId]);

        $brandStaffSample = current($this->BrandStaff->findAllForIndex($brandId));
        $actual = $brandStaffSample['BrandStaff']['has_full_permissions'];

        $this->assertEquals($expected, $actual);
    }

    /**
     * @dataProvider providerFindHasFullPermissions
     * @param string $permissionLevel
     * @param string $expected
     */
    public function testFindHasFullPermissionsForView(string $permissionLevel, string $expected)
    {
        $staffId = static::USER_ID_BRAND_STAFF;

        $this->_updateAllPermissionLevels($permissionLevel, ['BrandStaff.staff_id' => $staffId]);

        $brandStaffSample = $this->BrandStaff->findForView($staffId);
        $actual = $brandStaffSample['BrandStaff']['has_full_permissions'];

        $this->assertEquals($expected, $actual);
    }

    public function providerFindHasFullPermissions()
    {
        return [
            'partial_permissions' => [
                'permissionLevel' => Permissions::LEVEL_VIEW,
                'expected' => false,
            ],
            'full_permissions' => [
                'permissionLevel' => Permissions::LEVEL_EDIT,
                'expected' => false,
            ],
        ];
    }

    public function testCreateFromAddForm()
    {
        $data = [
            'Contactpersons' => [
                'firstname' => 'Finance',
                'lastname' => 'BrandStaff',
            ],
            'CompanyTelephone' => [
                'value' => '**********',
            ],
            'StaffUser' => [
                'email_address' => '<EMAIL>',
                'language_code' => null,
            ],
            'BrandStaff' => [
                'role_id' => '',
                'role_name' => 'Finance',
                'has_full_permissions' => false,
                'Permission' => [
                    'products' => [
                        'name' => 'products',
                        'level' => '1',
                    ],
                    'orders' => [
                        'name' => 'orders',
                        'level' => '2',
                    ],
                    'retailers' => [
                        'name' => 'retailers',
                        'level' => '0',
                    ],
                ],
            ],
        ];
        $staffId = $this->BrandStaff->createFromAddForm(static::USER_ID_BRAND, $data);
        $this->assertNotEmpty($staffId, json_encode(['errors' => $this->BrandStaff->validationErrors]));

        $actual = $this->_findStaffForAssert($staffId);
        $expected = [
            'StaffUser' => [
                'id' => $staffId,
                'user_type' => User::TYPE_BRAND_STAFF,
                'email_address' => '<EMAIL>',
                'company_name' => 'Finance BrandStaff',
                'status' => 'Approve',
                'language_code' => null,
            ],
            'Contactpersons' => [
                'user_id' => $staffId,
                'firstname' => 'Finance',
                'lastname' => 'BrandStaff',
                'email' => '<EMAIL>',
            ],
            'UserSetting' => [
                'user_id' => $staffId,
            ],
            'BrandStaff' => [
                'id' => self::BRAND_STAFF_ID_NEW,
                'brand_id' => static::USER_ID_BRAND,
                'staff_id' => $staffId,
                'role_id' => static::ROLE_ID_NEW,
                'Permission' => [
                    [
                        'brand_staff_id' => self::BRAND_STAFF_ID_NEW,
                        'name' => 'orders',
                        'level' => '2',
                    ],
                    [
                        'brand_staff_id' => self::BRAND_STAFF_ID_NEW,
                        'name' => 'products',
                        'level' => '1',
                    ],
                    [
                        'brand_staff_id' => self::BRAND_STAFF_ID_NEW,
                        'name' => 'retailers',
                        'level' => '0',
                    ],
                ],
                'Role' => [
                    'id' => static::ROLE_ID_NEW,
                    'brand_id' => static::USER_ID_BRAND,
                    'name' => 'Finance',
                ],
            ],
            'Contact' => [
                [
                    'user_id' => $staffId,
                    'type' => 'company',
                    'contact_medium' => 'telephone',
                    'value' => '**********',
                ],
                [
                    'user_id' => $staffId,
                    'type' => 'person',
                    'contact_medium' => 'telephone',
                    'value' => '**********',
                ],
            ],
        ];
        $this->assertEquals($expected, $actual);
    }

    public function testSaveFromEditForm()
    {
        $brandStaffId = '2';

        // Assert that status does not change
        $this->BrandStaff->StaffUser->save(['id' => static::USER_ID_BRAND_STAFF, 'status' => 'Reject']);

        $data = [
            'Contactpersons' => [
                'firstname' => 'Finance',
                'lastname' => 'BrandStaff',
            ],
            'CompanyTelephone' => [
                'value' => '**********',
            ],
            'StaffUser' => [
                'email_address' => '<EMAIL>',
                'language_code' => null,
            ],
            'BrandStaff' => [
                'role_id' => '',
                'role_name' => 'Finance',
                'has_full_permissions' => true,
                // Simulate 'Permission' inputs being disabled on front-end
                //'Permission' => [],
            ],
        ];
        $staffId = $this->BrandStaff->saveFromEditForm(static::USER_ID_BRAND, static::USER_ID_BRAND_STAFF, $data);
        $this->assertEquals(static::USER_ID_BRAND_STAFF, $staffId, json_encode(['errors' => $this->BrandStaff->validationErrors]));

        $actual = $this->_findStaffForAssert($staffId);
        $expected = [
            'StaffUser' => [
                'id' => $staffId,
                'user_type' => User::TYPE_BRAND_STAFF,
                'email_address' => '<EMAIL>',
                'company_name' => 'Finance BrandStaff',
                'status' => 'Reject',
                'language_code' => null,
            ],
            'Contactpersons' => [
                'user_id' => $staffId,
                'firstname' => 'Finance',
                'lastname' => 'BrandStaff',
                'email' => '<EMAIL>',
            ],
            'UserSetting' => [
                'user_id' => $staffId,
            ],
            'BrandStaff' => [
                'id' => $brandStaffId,
                'brand_id' => static::USER_ID_BRAND,
                'staff_id' => $staffId,
                'role_id' => static::ROLE_ID_NEW,
                'Permission' => array_values(array_map(
                    function($permission) use ($brandStaffId) {
                        return ['brand_staff_id' => $brandStaffId] + $permission;
                    },
                    Permissions::buildFieldsForAllPermissionsByName()
                )),
                'Role' => [
                    'id' => static::ROLE_ID_NEW,
                    'brand_id' => static::USER_ID_BRAND,
                    'name' => 'Finance',
                ],
            ],
            'Contact' => [
                [
                    'user_id' => $staffId,
                    'type' => 'company',
                    'contact_medium' => 'telephone',
                    'value' => '**********',
                ],
                [
                    'user_id' => $staffId,
                    'type' => 'person',
                    'contact_medium' => 'telephone',
                    'value' => '**********',
                ],
            ],
        ];
        $this->assertEquals($expected, $actual);
    }

    private function _findStaffForAssert($staffId): array
    {
        $this->BrandStaff->StaffUser->hasMany('Contact');
        $record = (array)$this->BrandStaff->StaffUser->find('first', [
            'contain' => [
                'BrandStaff' => [
                    'Permission' => [
                        'fields' => ['brand_staff_id', 'name', 'level'],
                        'order' => ['Permission.name' => 'ASC']
                    ],
                    'Role' => [
                        'fields' => ['id', 'brand_id', 'name'],
                        'order' => ['Role.name' => 'ASC'],
                    ],
                ],
                'Contactpersons' => ['fields' => ['user_id', 'firstname', 'lastname', 'email']],
                'UserSetting' => ['fields' => ['user_id']],
                'Contact' => [
                    'fields' => ['type', 'contact_medium', 'value'],
                    'order' => ['Contact.type' => 'DESC'],
                ],
            ],
            'conditions' => ['StaffUser.id' => $staffId],
            'fields' => [
                'StaffUser.id',
                'StaffUser.user_type',
                'StaffUser.email_address',
                'StaffUser.company_name',
                'StaffUser.status',
                'StaffUser.language_code',
                // Listing BrandStaff fields here instead of contain avoids fetching BrandStaff.Permission.id
                'BrandStaff.id',
                'BrandStaff.staff_id',
                'BrandStaff.brand_id',
                'BrandStaff.role_id',
            ],
        ]);
        $this->BrandStaff->StaffUser->unbindModel(['hasMany' => ['Contact']], false);

        return $record;
    }

    private function _updateAllPermissionLevels(int $permissionLevel, array $conditions): bool
    {
        $ids = (array)$this->BrandStaff->find('list', [
            'conditions' => $conditions,
            'fields' => ['id', 'id'],
            'group' => ['id'],
        ]);

        return (bool)$this->BrandStaff->Permission->updateAllJoinless(
            ['Permission.level' => $permissionLevel],
            ['Permission.brand_staff_id' => $ids]
        );
    }
}
