<?php
App::uses('AppTestCase', 'TestSuite');
App::uses('Configuration', 'Model');

/**
 * Configuration Test Case.
 *
 * Run with the following console command:
 *
 * ./app/Console/cake test app Model/Configuration
 *
 * @property Configuration|PHPUnit_Framework_MockObject_MockObject $Configuration
 */
class ConfigurationTest extends AppTestCase
{
    // Arbitrary config to test
    const CONFIG_NAME = 'AFTERSHIP_KEY';
    const CONFIG_VALUE = '8d3f0272-96f3-44b6-b961-dedddac53569';

    public $fixtures = [
        'app.configuration',
    ];

    public function setUp()
    {
        parent::setUp();
        $this->Configuration = $this->getMockForModel('Configuration', ['defineConstants']);

        // Work with reduced sets
        $this->Configuration->truncate();
        $this->Configuration->saveMany([
            ['name' => static::CONFIG_NAME, 'value' => static::CONFIG_VALUE],
            ['name' => 'DELETE', 'value' => 'config with no constant'],
        ]);
        // Don't actually set global constants
        $this->Configuration->expects($this->any())->method('defineConstants')->willReturn([
            static::CONFIG_NAME => static::CONFIG_VALUE,
            'ADD' => 'constant with no config',
        ]);

        Cache::clear(false, $this->Configuration->cacheConfig);
    }

    public function tearDown()
    {
        Cache::clear(false, $this->Configuration->cacheConfig);

        unset($this->Configuration);
        parent::tearDown();
    }

    public function testBootstrap()
    {
        $this->Configuration = $this->getMockForModel('Configuration', ['defineConstants']);
        $this->Configuration->expects($this->once())->method('defineConstants')->with(
            $this->Configuration->find('list')
        );

        $this->Configuration->bootstrap();
        // Repeat should be ignored
        $this->Configuration->bootstrap();

        $this->assertTrue($this->Configuration->bootstrapped, 'Configuration::$bootstrapped');
    }

    public function testListConstants()
    {
        $actual = $this->Configuration->listConstants();
        $expected = [
            static::CONFIG_NAME => static::CONFIG_VALUE,
            'ADD' => 'constant with no config',
        ];
        $this->assertEquals($expected, $actual);
    }

    public function testListConfigurations()
    {
        $actual = $this->Configuration->listConfigurations();
        $expected = [
            static::CONFIG_NAME => static::CONFIG_VALUE,
            'DELETE' => 'config with no constant',
        ];
        $this->assertEquals($expected, $actual);
        $this->assertCacheEquals($expected, 'Cache not populated');
    }

    public function testGetAllConstantsAsRecords()
    {
        $actual = $this->Configuration->getAllConstantsAsRecords();
        $expected = [
            0 => [
                'Configuration' => [
                    'id' => '1',
                    'name' => static::CONFIG_NAME,
                    'value' => static::CONFIG_VALUE,
                ],
            ],
        ];
        $this->assertArraySubset($expected, $actual);
    }

    public function testUpdateValue()
    {
        $name = static::CONFIG_NAME;
        $value = 'bogus';
        $this->populateCache();

        $result = $this->Configuration->updateValue($name, $value);
        $this->assertTrue($result, Debugger::exportVar(['errors' => $this->Configuration->validationErrors]));
        $actual = $this->Configuration->fieldByConditions('value', ['name' => $name]);

        $expected = $value;
        $this->assertEquals($expected, $actual);
        $this->assertCacheEquals(false, 'Cache not cleared');
    }

    public function testUpdateConfiguration()
    {
        $name = static::CONFIG_NAME;
        $value = 'bogus';
        $this->populateCache();

        $data = [
            [
                'Configuration' => [
                    'name' => $name,
                    'value' => $value,
                ],
            ],
            [
                'Configuration' => [
                    'name' => 'DELETE',
                    'value' => 'config with no constant',
                ],
            ],
        ];
        $result = $this->Configuration->updateConfiguration($data);
        $this->assertTrue($result, Debugger::exportVar(['errors' => $this->Configuration->validationErrors]));
        $actual = $this->Configuration->find('list');

        $expected = [$name => $value];
        $this->assertEquals($expected, $actual);
        $this->assertCacheEquals(false, 'Cache not cleared');
    }

    public function testDeleteAllRemovedConfigs()
    {
        $this->populateCache();

        $this->Configuration->deleteAllRemovedConfigs();
        $actual = $this->Configuration->find('list');

        $expected = [static::CONFIG_NAME => static::CONFIG_VALUE];
        $this->assertEquals($expected, $actual);
        $this->assertCacheEquals(false, 'Cache not cleared');
    }

    private function populateCache()
    {
        Cache::write('list_configurations', $this->Configuration->find('list'), $this->Configuration->cacheConfig);
    }

    private function assertCacheEquals($expected, $message = '')
    {
        $this->assertEquals($expected, Cache::read('list_configurations', $this->Configuration->cacheConfig), $message);
    }
}
