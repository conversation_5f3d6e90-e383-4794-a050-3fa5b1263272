<?php
App::uses('AppTestCase', 'TestSuite');
App::uses('CountryTax', 'Model');

/**
 * CountryTax Test Case.
 *
 * Run with the following console command:
 *
 * ./app/Console/cake test app Model/CountryTax
 *
 * @property CountryTax $CountryTax
 */
class CountryTaxTest extends AppTestCase
{
    const COUNTRY_ID_CA = '39';
    const COUNTRY_ID_US = '233';

    const STATE_ID_ON_CA = '611';
    const STATE_ID_BC_CA = '606';
    const STATE_ID_MN_CA = '607';
    const STATE_ID_CA_US = '5197';
    const STATE_ID_PA_US = '5231';

    public $fixtures = [
        'app.country_tax',
        'app.country',
        'app.state',
        'app.state_tax'
    ];

    public function setUp()
    {
        parent::setUp();
        $this->CountryTax = ClassRegistry::init('CountryTax');
    }

    public function tearDown()
    {
        unset($this->CountryTax);
        parent::tearDown();
    }

    /**
     * @dataProvider providerFindAdminCountryTaxSettings
     */
    public function testFindAdminCountryTaxSettings(int $countryId, array $expected)
    {
        $this->assertEquals($expected, $this->CountryTax->findAdminCountryTaxSettings($countryId));
    }

    public function providerFindAdminCountryTaxSettings(): array
    {
        return [
            'CA' => [
                'countryId' => static::COUNTRY_ID_CA,
                'expected' => [
                    'CountryTax' => [
                        'country_id' => static::COUNTRY_ID_CA,
                        'name' => 'GST',
                        'includes_shipping' => true,
                        'included_in_prices' => false,
                        'percentage' => '5.000',
                    ],
                    'StateTax' => [
                        static::STATE_ID_BC_CA => [
                            'state_id' => static::STATE_ID_BC_CA,
                            'country_id' => static::COUNTRY_ID_CA,
                            'name' => 'PST/GST',
                            'includes_shipping' => true,
                            'uses_origin_based_rates' => false,
                            'uses_api' => false,
                            'percentage' => '12.000',
                        ],
                        static::STATE_ID_ON_CA => [
                            'state_id' => static::STATE_ID_ON_CA,
                            'country_id' => static::COUNTRY_ID_CA,
                            'name' => 'HST',
                            'includes_shipping' => true,
                            'uses_origin_based_rates' => false,
                            'uses_api' => false,
                            'percentage' => '13.000',
                        ],
                    ],
                ],
            ],
            'US' => [
                'countryId' => static::COUNTRY_ID_US,
                'expected' => [
                    'CountryTax' => [
                        'country_id' => static::COUNTRY_ID_US,
                        'name' => 'State Tax',
                        'includes_shipping' => true,
                        'included_in_prices' => false,
                        'percentage' => '0.000',
                    ],
                    'StateTax' => [
                        static::STATE_ID_CA_US => [
                            'state_id' => static::STATE_ID_CA_US,
                            'country_id' => static::COUNTRY_ID_US,
                            'name' => '',
                            'includes_shipping' => true,
                            'uses_origin_based_rates' => true,
                            'uses_api' => true,
                            'percentage' => '7.250',
                        ],
                        static::STATE_ID_PA_US => [
                            'state_id' => static::STATE_ID_PA_US,
                            'country_id' => static::COUNTRY_ID_US,
                            'name' => '',
                            'includes_shipping' => true,
                            'uses_origin_based_rates' => true,
                            'uses_api' => false,
                            'percentage' => '6.000',
                        ],
                    ],
                ],
            ],
        ];
    }

    /**
     * @dataProvider providerSaveAdminCountryTaxSettings
     * @param int $countryId
     * @param callable $dataCallback
     * @param callable $expectedCallback
     */
    public function testSaveAdminCountryTaxSettings($countryId, callable $dataCallback, callable $expectedCallback)
    {
        $defaultData = [
            'CountryTax' => [
                'name' => 'GST',
                'percentage' => '5.000',
                'includes_shipping' => true,
            ],
            'StateTax' => [
                static::STATE_ID_BC_CA => [
                    'state_id' => static::STATE_ID_BC_CA,
                    'name' => 'PST/GST',
                    'percentage' => '12.000',
                    'includes_shipping' => true,
                    'uses_origin_based_rates' => false,
                    'uses_api' => false,
                ],
                static::STATE_ID_ON_CA => [
                    'state_id' => static::STATE_ID_ON_CA,
                    'name' => 'HST',
                    'percentage' => '13.000',
                    'includes_shipping' => true,
                    'uses_origin_based_rates' => false,
                    'uses_api' => false,
                ],
            ],
        ];
        $data = call_user_func($dataCallback, $defaultData);

        $success = $this->CountryTax->saveAdminCountryTaxSettings($countryId, $data);
        $this->assertTrue($success, json_encode(['errors' => $this->CountryTax->validationErrors]));

        $actual = $this->CountryTax->find('first', [
            'contain' => [
                'StateTax' => [
                    'fields' => [
                        'StateTax.state_id',
                        'StateTax.country_id',
                        'StateTax.name',
                        'StateTax.rate',
                        'StateTax.includes_shipping',
                        'StateTax.uses_origin_based_rates',
                        'StateTax.uses_api',
                    ],
                ],
            ],
            'conditions' => ['CountryTax.country_id' => static::COUNTRY_ID_CA],
            'fields' => [
                'CountryTax.country_id',
                'CountryTax.name',
                'CountryTax.rate',
                'CountryTax.includes_shipping',
            ],
        ]);
        $actual['StateTax'] = Hash::combine($actual['StateTax'], '{n}.state_id', '{n}');

        $defaultExpected = [
            'CountryTax' => [
                'country_id' => static::COUNTRY_ID_CA,
                'name' => 'GST',
                'rate' => '0.05000',
                'includes_shipping' => true,
            ],
            'StateTax' => [
                static::STATE_ID_BC_CA => [
                    'state_id' => static::STATE_ID_BC_CA,
                    'country_id' => static::COUNTRY_ID_CA,
                    'name' => 'PST/GST',
                    'rate' => '0.12000',
                    'includes_shipping' => true,
                    'uses_origin_based_rates' => false,
                    'uses_api' => false,
                ],
                static::STATE_ID_ON_CA => [
                    'state_id' => static::STATE_ID_ON_CA,
                    'country_id' => static::COUNTRY_ID_CA,
                    'name' => 'HST',
                    'rate' => '0.13000',
                    'includes_shipping' => true,
                    'uses_origin_based_rates' => false,
                    'uses_api' => false,
                ],
            ],
        ];
        $expected = call_user_func($expectedCallback, $defaultExpected);
        $this->assertEquals($expected, $actual);
    }

    public function providerSaveAdminCountryTaxSettings()
    {
        return [
            'add' => [
                'countryId' => static::COUNTRY_ID_CA,
                'dataCallback' => function($data) {
                    $data['StateTax'][static::STATE_ID_MN_CA] = [
                        'state_id' => static::STATE_ID_MN_CA,
                        'percentage' => '12.000',
                        'name' => 'RST',
                        'includes_shipping' => true,
                        'uses_origin_based_rates' => false,
                        'uses_api' => false,
                    ];
                    return $data;
                },
                'expectedCallback' => function($expected) {
                    $expected['StateTax'][static::STATE_ID_MN_CA] = [
                        'state_id' => static::STATE_ID_MN_CA,
                        'country_id' => static::COUNTRY_ID_CA,
                        'rate' => '0.12000',
                        'name' => 'RST',
                        'includes_shipping' => true,
                        'uses_origin_based_rates' => false,
                        'uses_api' => false,
                    ];
                    return $expected;
                },
            ],
            'add_uses_api' => [
                'countryId' => static::COUNTRY_ID_CA,
                'dataCallback' => function($data) {
                    $data['StateTax'][static::STATE_ID_MN_CA] = [
                        'state_id' => static::STATE_ID_MN_CA,
                        // Simulate disabled inputs
                        //'percentage' => '0.000',
                        'name' => 'RST',
                        //'includes_shipping' => true,
                        'uses_origin_based_rates' => false,
                        'uses_api' => true,
                    ];
                    return $data;
                },
                'expectedCallback' => function($expected) {
                    $expected['StateTax'][static::STATE_ID_MN_CA] = [
                        'state_id' => static::STATE_ID_MN_CA,
                        'country_id' => static::COUNTRY_ID_CA,
                        'rate' => '0.00000',
                        'name' => 'RST',
                        'includes_shipping' => true,
                        'uses_origin_based_rates' => false,
                        'uses_api' => true,
                    ];
                    return $expected;
                },
            ],
            'update' => [
                'countryId' => static::COUNTRY_ID_CA,
                'dataCallback' => function($data) {
                    $data['CountryTax'] = [
                        'name' => '',
                        'percentage' => '',
                        'includes_shipping' => false,
                    ];
                    $data['StateTax'][static::STATE_ID_BC_CA] = [
                        'state_id' => static::STATE_ID_BC_CA,
                        'percentage' => '7.000',
                        'name' => 'PST',
                        'includes_shipping' => false,
                        'uses_origin_based_rates' => false,
                        'uses_api' => false,
                    ];
                    $data['StateTax'][static::STATE_ID_ON_CA] = [
                        'state_id' => static::STATE_ID_ON_CA,
                        'percentage' => '0.000',
                        'name' => '',
                        'includes_shipping' => false,
                        'uses_origin_based_rates' => false,
                        'uses_api' => false,
                    ];
                    return $data;
                },
                'expectedCallback' => function($expected) {
                    $expected['CountryTax'] = [
                        'country_id' => static::COUNTRY_ID_CA,
                        'name' => '',
                        'rate' => '0.00000',
                        'includes_shipping' => false,
                    ];
                    $expected['StateTax'][static::STATE_ID_BC_CA] = [
                        'state_id' => static::STATE_ID_BC_CA,
                        'country_id' => static::COUNTRY_ID_CA,
                        'name' => 'PST',
                        'rate' => '0.07000',
                        'includes_shipping' => false,
                        'uses_origin_based_rates' => false,
                        'uses_api' => false,
                    ];
                    $expected['StateTax'][static::STATE_ID_ON_CA] = [
                        'state_id' => static::STATE_ID_ON_CA,
                        'country_id' => static::COUNTRY_ID_CA,
                        'name' => '',
                        'rate' => '0.00000',
                        'includes_shipping' => false,
                        'uses_origin_based_rates' => false,
                        'uses_api' => false,
                    ];
                    return $expected;
                },
            ],
            'update_uses_api' => [
                'countryId' => static::COUNTRY_ID_CA,
                'dataCallback' => function($data) {
                    $data['StateTax'][static::STATE_ID_ON_CA] = [
                        'state_id' => static::STATE_ID_ON_CA,
                        // Simulate disabled inputs
                        //'percentage' => '13.000',
                        'name' => 'HST',
                        //'includes_shipping' => true,
                        'uses_origin_based_rates' => false,
                        'uses_api' => true,
                    ];
                    return $data;
                },
                'expectedCallback' => function($expected) {
                    $expected['StateTax'][static::STATE_ID_ON_CA] = [
                        'state_id' => static::STATE_ID_ON_CA,
                        'country_id' => static::COUNTRY_ID_CA,
                        'name' => 'HST',
                        'rate' => '0.13000',
                        'includes_shipping' => true,
                        'uses_origin_based_rates' => false,
                        'uses_api' => true,
                    ];
                    return $expected;
                },
            ],
            'remove_excluded' => [
                'countryId' => static::COUNTRY_ID_CA,
                'dataCallback' => function($data) {
                    unset($data['StateTax'][static::STATE_ID_BC_CA]);
                    return $data;
                },
                'expectedCallback' => function($expected) {
                    unset($expected['StateTax'][static::STATE_ID_BC_CA]);
                    return $expected;
                },
            ],
            'remove_empty' => [
                'countryId' => static::COUNTRY_ID_CA,
                'dataCallback' => function($data) {
                    $data['StateTax'][static::STATE_ID_BC_CA] = [
                        'state_id' => static::STATE_ID_BC_CA,
                        'percentage' => '',
                        // Simulate disabled inputs
                        //'name' => 'PST',
                        //'includes_shipping' => true,
                        'uses_origin_based_rates' => false,
                        'uses_api' => false,
                    ];
                    return $data;
                },
                'expectedCallback' => function($expected) {
                    unset($expected['StateTax'][static::STATE_ID_BC_CA]);
                    return $expected;
                },
            ],
            'ignore_other_country' => [
                'countryId' => static::COUNTRY_ID_US,
                'dataCallback' => function($data) {
                    $data['StateTax'][static::STATE_ID_BC_CA] = [
                        'state_id' => static::STATE_ID_BC_CA,
                        'percentage' => '7.000',
                        'name' => 'PST',
                        'includes_shipping' => false,
                        'uses_origin_based_rates' => false,
                        'uses_api' => false,
                    ];
                    $data['StateTax'][static::STATE_ID_ON_CA] = [
                        'state_id' => static::STATE_ID_ON_CA,
                        'percentage' => '0.000',
                        'name' => '',
                        'includes_shipping' => false,
                        'uses_origin_based_rates' => false,
                        'uses_api' => false,
                    ];
                    return $data;
                },
                'expectedCallback' => function($expected) {
                    return $expected;
                },
            ],
        ];
    }

    public function testSaveNewAdminCountryTaxSettings()
    {
        $this->CountryTax->StateTax->deleteAllJoinless(['StateTax.country_id' => static::COUNTRY_ID_CA], false);
        $this->CountryTax->delete(static::COUNTRY_ID_CA, false);

        $data = [
            'CountryTax' => [
                'name' => 'GST',
                'percentage' => '5.000',
                'includes_shipping' => true,
            ],
            'StateTax' => [
                static::STATE_ID_BC_CA => [
                    'state_id' => static::STATE_ID_BC_CA,
                    'percentage' => '12.000',
                    'name' => 'PST/GST',
                    'includes_shipping' => true,
                    'uses_origin_based_rates' => false,
                    'uses_api' => false,
                ],
                static::STATE_ID_ON_CA => [
                    'state_id' => static::STATE_ID_ON_CA,
                    // Simulate disabled inputs
                    //'percentage' => '13.000',
                    'name' => 'HST',
                    //'includes_shipping' => true,
                    'uses_origin_based_rates' => false,
                    'uses_api' => true,
                ],
            ],
        ];
        $success = $this->CountryTax->saveAdminCountryTaxSettings(static::COUNTRY_ID_CA, $data);
        $this->assertTrue($success, json_encode(['errors' => $this->CountryTax->validationErrors]));

        $actual = $this->CountryTax->find('first', [
            'contain' => [
                'StateTax' => [
                    'fields' => [
                        'StateTax.state_id',
                        'StateTax.country_id',
                        'StateTax.name',
                        'StateTax.rate',
                        'StateTax.includes_shipping',
                        'StateTax.uses_origin_based_rates',
                        'StateTax.uses_api',
                    ],
                ],
            ],
            'conditions' => ['CountryTax.country_id' => static::COUNTRY_ID_CA],
            'fields' => [
                'CountryTax.country_id',
                'CountryTax.name',
                'CountryTax.rate',
                'CountryTax.includes_shipping',
            ],
        ]);
        $actual['StateTax'] = Hash::combine($actual['StateTax'], '{n}.state_id', '{n}');

        $expected = [
            'CountryTax' => [
                'country_id' => static::COUNTRY_ID_CA,
                'name' => 'GST',
                'rate' => '0.05000',
                'includes_shipping' => true,
            ],
            'StateTax' => [
                static::STATE_ID_BC_CA => [
                    'state_id' => static::STATE_ID_BC_CA,
                    'country_id' => static::COUNTRY_ID_CA,
                    'name' => 'PST/GST',
                    'rate' => '0.12000',
                    'includes_shipping' => true,
                    'uses_origin_based_rates' => false,
                    'uses_api' => false,
                ],
                static::STATE_ID_ON_CA => [
                    'state_id' => static::STATE_ID_ON_CA,
                    'country_id' => static::COUNTRY_ID_CA,
                    'name' => 'HST',
                    'rate' => '0.00000',
                    'includes_shipping' => true,
                    'uses_origin_based_rates' => false,
                    'uses_api' => true,
                ],
            ],
        ];
        $this->assertEquals($expected, $actual);
    }

    public function testValidateAdminCountryTaxSettings()
    {
        $data = [
            'CountryTax' => [
                'country_id' => '',
                'name' => ' ',
                'rate' => '',
                'percentage' => '-5.000',
                'includes_shipping' => '',
                'created_at' => '',
                'updated_at' => '',
            ],
            'StateTax' => [
                static::STATE_ID_BC_CA => [
                    'state_id' => static::STATE_ID_BC_CA,
                    'country_id' => '',
                    'name' => '',
                    'rate' => '',
                    'percentage' => 'invalid',
                    'includes_shipping' => '',
                    'uses_origin_based_rates' => '',
                    'uses_api' => 'on',
                    'created_at' => '',
                    'updated_at' => '',
                ],
                static::STATE_ID_ON_CA => [
                    'state_id' => static::STATE_ID_ON_CA,
                    'country_id' => '0',
                    'name' => ' ',
                    'rate' => '-0.13000',
                    'percentage' => '',
                    'includes_shipping' => 'on',
                    'uses_origin_based_rates' => 'on',
                    'uses_api' => '',
                    'created_at' => '0000-00-00 00:00:00',
                    'updated_at' => '0000-00-00 00:00:00',
                ],
            ],
        ];

        $success = $this->CountryTax->saveAdminCountryTaxSettings(static::COUNTRY_ID_CA, $data);
        $this->assertFalse($success, json_encode(compact('success')));

        $expected = [
            'name' => ['This field cannot be left blank'],
            'rate' => ['Invalid tax rate'],
            'percentage' => ['Tax cannot be negative'],
            'includes_shipping' => ['Invalid boolean'],
            'created_at' => ['Invalid datetime'],
            'updated_at' => ['Invalid datetime'],
            'StateTax' => [
                static::STATE_ID_BC_CA => [
                    'country_id' => ['Invalid id'],
                    'rate' => ['Invalid tax rate'],
                    'percentage' => ['Invalid tax percentage'],
                    'includes_shipping' => ['Invalid boolean'],
                    'uses_origin_based_rates' => ['Invalid boolean'],
                    'uses_api' => ['Invalid boolean'],
                    'created_at' => ['Invalid datetime'],
                    'updated_at' => ['Invalid datetime'],
                ],
                static::STATE_ID_ON_CA => [
                    'country_id' => ['Invalid id'],
                    'name' => ['This field cannot be left blank'],
                    'rate' => ['Tax cannot be negative'],
                    'percentage' => ['Tax cannot be negative'],
                    'includes_shipping' => ['Invalid boolean'],
                    'uses_origin_based_rates' => ['Invalid boolean'],
                    'uses_api' => ['Invalid boolean'],
                    'created_at' => ['Invalid datetime'],
                    'updated_at' => ['Invalid datetime'],
                ],
            ],
        ];
        $this->assertEquals($expected, $this->CountryTax->validationErrors);
    }
}
