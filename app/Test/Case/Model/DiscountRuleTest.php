<?php
App::uses('AppTestCase', 'TestSuite');
App::uses('DiscountRule', 'Model');
App::uses('DiscountProvider', 'Test/Provider');

/**
 * DiscountRule Test Case.
 *
 * Run with the following console command:
 *
 * ./app/Console/cake test app Model/DiscountRule
 *
 * @property DiscountRule $DiscountRule
 */
class DiscountRuleTest extends AppTestCase
{
    public $fixtures = [
        'app.collection',
        'app.collections_product',
        'app.discount',
        'app.discount_rule',
        'app.i18n',
        'app.product',
        'app.product_non_applicable_order_type',
        'app.product_state_fee',
        'app.product_title',
        'app.product_variant_option',
        'app.product_tag',
        'app.tag',
    ];

    public function setUp()
    {
        parent::setUp();
        $this->DiscountRule = ClassRegistry::init('DiscountRule');
    }

    public function tearDown()
    {
        unset($this->DiscountRule);
        parent::tearDown();
    }

    /**
     * @dataProvider DiscountProvider::testCalculateLineItemDiscount
     */
    public function testCalculateLineItemDiscount($option, $option_amount, $line_price, $subtotal, $expected)
    {
        $this->assertSame($expected, DiscountRule::calculateItemDiscountAmount($option, $option_amount, $line_price, $subtotal));
    }

    public function testCalculateLineItemDiscountWithInvalidOption()
    {
        set_error_handler(function($code, $message) {
            $this->assertStringStartsWith('Unexpected discount type "not an option"', $message);
        });

        try {
            $this->assertSame(0.00, DiscountRule::calculateItemDiscountAmount('not an option', '50.00', '500.00', '500.00'));
        } finally {
            restore_error_handler();
        }
    }

    /**
     * @dataProvider DiscountProvider::testCalculateShippingDiscount
     */
    public function testCalculateShippingDiscount($option, $optionAmount, $shippingAmount, $expected)
    {
        $this->assertSame($expected, DiscountRule::calculateShippingDiscountAmount($option, $optionAmount, $shippingAmount));
    }

    public function testCalculateShippingDiscountWithInvalidOption()
    {
        set_error_handler(function($code, $message) {
            $this->assertStringStartsWith('Unexpected discount type "not an option"', $message);
        });

        try {
            $this->assertSame(0.00, DiscountRule::calculateShippingDiscountAmount('not an option', '0.00', '25.00'));
        } finally {
            restore_error_handler();
        }
    }

    /**
     * @dataProvider DiscountProvider::testProcessOrderOptionsWithValidValues
     */
    public function testProcessOrderOptionsWithValidValues(array $orderOptions, array $expectedDiscountDiff)
    {
        $discountRule = array_merge($this->getDiscountRuleForProcessOrderItems(), $orderOptions);
        $items = $this->callPrivateMethod([$this->DiscountRule->Discount, '_formatEcommerceItems'], DiscountProvider::USER_ID, DiscountProvider::defaultItems());

        $actual = DiscountRule::processOrderItems($discountRule, $items);

        $expected = array_merge($discountRule, $expectedDiscountDiff);
        $this->assertEquals($expected, $actual);
    }

    /**
     * @dataProvider DiscountProvider::testProcessOrderOptionsWithInvalidSubtotal
     */
    public function testProcessOrderOptionsWithInvalidSubtotal(array $orderOptions)
    {
        $discountRule = array_merge($this->getDiscountRuleForProcessOrderItems(), $orderOptions);
        $items = $this->callPrivateMethod([$this->DiscountRule->Discount, '_formatEcommerceItems'], DiscountProvider::USER_ID, DiscountProvider::defaultItems());

        $prerequisiteSubtotal = DiscountRule::isBuyXGetY($discountRule)
            ? $discountRule['prerequisite_subtotal']
            : array_sum(array_column($items, 'line_price'));
        $discountRule['prerequisite_subtotal'] = format_number($prerequisiteSubtotal + 0.01);

        $actual = DiscountRule::processOrderItems($discountRule, $items);

        $this->assertEquals([], $actual);
    }

    /**
     * @dataProvider DiscountProvider::testProcessOrderOptionsWithInvalidQuantity
     */
    public function testProcessOrderOptionsWithInvalidQuantity(array $orderOptions)
    {
        $discountRule = array_merge($this->getDiscountRuleForProcessOrderItems(), $orderOptions);
        $items = $this->callPrivateMethod([$this->DiscountRule->Discount, '_formatEcommerceItems'], DiscountProvider::USER_ID, DiscountProvider::defaultItems());

        $prerequisiteQuantity = DiscountRule::isBuyXGetY($discountRule)
            ? $discountRule['prerequisite_quantity']
            : array_sum(array_column($items, 'quantity'));
        $discountRule['prerequisite_quantity'] = format_number($prerequisiteQuantity + 1, 0);

        $actual = DiscountRule::processOrderItems($discountRule, $items);

        $this->assertEquals([], $actual);
    }

    /**
     * @dataProvider DiscountProvider::testProcessOrderOptionsWithInvalidValues
     */
    public function testProcessOrderOptionsWithInvalidValues(array $orderOptions)
    {
        $discountRule = array_merge($this->getDiscountRuleForProcessOrderItems(), $orderOptions);
        $items = $this->callPrivateMethod([$this->DiscountRule->Discount, '_formatEcommerceItems'], DiscountProvider::USER_ID, DiscountProvider::defaultItems());

        $actual = DiscountRule::processOrderItems($discountRule, $items);

        $this->assertEquals([], $actual);
    }

    /**
     * @dataProvider DiscountProvider::testProcessOrderOptionsWithDeductedGetYQuantity
     */
    public function testProcessOrderOptionsWithDeductedGetYQuantity(array $orderOptions, array $expectedDiscountDiff)
    {
        $discountRule = array_merge($this->getDiscountRuleForProcessOrderItems(), $orderOptions);
        $items = $this->callPrivateMethod([$this->DiscountRule->Discount, '_formatEcommerceItems'], DiscountProvider::USER_ID, DiscountProvider::defaultItems());

        $actual = DiscountRule::processOrderItems($discountRule, $items);

        $expected = array_merge($discountRule, $expectedDiscountDiff);
        $this->assertEquals($expected, $actual);
    }

    /**
     * @dataProvider DiscountProvider::testProcessOrderOptionsWithDeductedGetYQuantity
     */
    public function testProcessOrderOptionsWithInvalidDeductedGetYQuantity(array $orderOptions)
    {
        $discountRule = array_merge($this->getDiscountRuleForProcessOrderItems(), $orderOptions);
        $items = $this->callPrivateMethod([$this->DiscountRule->Discount, '_formatEcommerceItems'], DiscountProvider::USER_ID, DiscountProvider::defaultItems());

        $discountRule['order_quantity'] = '2';

        $actual = DiscountRule::processOrderItems($discountRule, $items);

        $this->assertEquals([], $actual);
    }

    private function getDiscountRuleForProcessOrderItems(): array
    {
        return array_diff_key(DiscountProvider::defaultDiscountInfo()['DiscountRule'][0], array_flip([
            // Exercise scenarios where the virtual fields are not set
            'is_buy_x_get_y',
            'is_auto_add_y',
        ]));
    }
}
