<?php
App::uses('AppTestCase', 'TestSuite');
App::uses('Discount', 'Model');
App::uses('DiscountProvider', 'Test/Provider');

/**
 * Discount Test Case.
 *
 * Run with the following console command:
 *
 * ./app/Console/cake test app Model/Discount
 *
 * @property Discount $Discount
 */
class DiscountTest extends AppTestCase
{
    public $fixtures = [
        'app.brand_staff_permission',
        'app.credit_term',
        'app.collection',
        'app.collections_product',
        'app.discount',
        'app.discount_credit_term',
        'app.discount_rule',
        'app.discount_usage',
        'app.i18n',
        'app.manufacturer_retailer',
        'app.product',
        'app.product_non_applicable_order_type',
        'app.product_state_fee',
        'app.product_tag',
        'app.product_title',
        'app.product_variant_option',
        'app.user',
        'app.staff_permission',
        'app.tag',
    ];

    public function setUp()
    {
        parent::setUp();
        $this->Discount = ClassRegistry::init('Discount');
    }

    public function tearDown()
    {
        unset($this->Discount);
        parent::tearDown();
    }

    public function testDateFieldComparison()
    {
        $this->markTestIncomplete('testDateFieldComparison not implemented.');
    }

    public function testCheckAutomaticDiscountNoConflict()
    {
        $this->markTestIncomplete('testCheckAutomaticDiscountNoConflict not implemented.');
    }

    public function testNoDateRangeConflict()
    {
        $this->markTestIncomplete('testNoDateRangeConflict not implemented.');
    }

    /**
     * @dataProvider DiscountProvider::testFindForEcommerceByCode
     */
    public function testFindForEcommerceByCode(string $code, array $items, array $expected)
    {
        $userId = DiscountProvider::USER_ID;

        $actual = $this->Discount->findForEcommerceByCode($userId, $code, $items);

        $this->assertEquals($expected, $actual);
    }

    /**
     * @dataProvider DiscountProvider::testFindForEcommerceByAuto
     */
    public function testFindForEcommerceByAuto(array $items, array $expected)
    {
        $userId = DiscountProvider::USER_ID;
        if (!$this->Discount->updateAllJoinless(['Discount.is_automatic' => 'TRUE'], ['Discount.id' => DiscountProvider::eCommerceDiscountInfo()['id']])) {
            throw new RuntimeException('Failed to set automatic discount');
        }

        $actual = $this->Discount->findForEcommerceByAuto($userId, $items);

        $this->assertEquals($expected, $actual);
    }

    public function testFindAutoAddQuantitiesByVariantId()
    {
        $userId = DiscountProvider::USER_ID;
        $discountRules = array_map(function(array $rule): array {
            return array_diff_key(
                array_merge($rule, [
                    'prerequisite_option' => 'category',
                    'prerequisite_subtotal' => '0.00',
                    'prerequisite_quantity' => '2',
                    'prerequisite_values' => '["Accessories"]',
                    'order_quantity' => '6',
                    'order_option' => 'product_variant',
                    'order_values' => '["BICYCLE-1","BICYCLE-2","BICYCLE-3"]',
                    'auto_add_sku_quantities' => '{"BICYCLE-1":2,"BICYCLE-2":3,"BICYCLE-3":3}',
                ]),
                // Exercise scenarios where the virtual fields are not set
                array_flip(['is_buy_x_get_y', 'is_auto_add_y'])
            );
        }, DiscountProvider::eCommerceDiscountInfo()['DiscountRule']);
        $items = DiscountProvider::defaultItems();

        $actual = $this->Discount->findEcommerceAutoAddQuantitiesByVariantId($userId, $discountRules, $items);

        $expected = [
            //17532934023 => 0, // BICYCLE-1 (2 of 2 already in cart)
            20016274759 => 1, // BICYCLE-2 (2 of 3 already in cart)
            21856096903 => 3, // BICYCLE-3
        ];
        $this->assertEquals($expected, $actual);
    }

    /**
     * @dataProvider DiscountProvider::testCalculateEcommerceItemDiscounts
     * @param array $discountInfoArray
     * @param array $expected
     */
    public function testCalculateEcommerceItemDiscounts(array $discountInfoArray, array $expected)
    {
        $items = DiscountProvider::defaultItems();
        $discountInfo = array_merge(DiscountProvider::defaultDiscountInfo(), $discountInfoArray);
        $userId = DiscountProvider::USER_ID;

        $actual = $this->Discount->calculateEcommerceItemDiscounts($userId, $discountInfo, $items);

        $this->assertEquals($expected, $actual);
    }

    /**
     * @dataProvider DiscountProvider::testCalculateOverlappingMultiRuleEcommerceItemDiscounts
     * @covers Discount::_resolveOverlappingItemDiscounts
     */
    public function testCalculateOverlappingMultiRuleEcommerceItemDiscounts(array $discountRules, array $expected)
    {
        $userId = DiscountProvider::USER_ID;
        $discountInfo = DiscountProvider::defaultDiscountInfo();
        $items = DiscountProvider::defaultItems();
        // It is easier to write test cases for quantity 3 instead of 2.
        $items = array_map(fn(array $item): array => array_merge($item, [
            'quantity' => 3,
            'line_price' => 3 * $item['price'],
        ]), $items);

        foreach ($discountRules as $idx => &$discountRule) {
            $discountRule = ['id' => (string)($idx + 1), 'discount_id' => $discountInfo['id']] + $discountRule;
        }
        $discountInfo['DiscountRule'] = $discountRules;

        $actual = $this->Discount->calculateEcommerceItemDiscounts($userId, $discountInfo, $items);

        $this->assertEquals($expected, $actual);
    }

    public function testCalculateEcommerceItemDiscountsWithNoDiscount()
    {
        $items = DiscountProvider::defaultItems();
        $discountInfo = [];
        $userId = DiscountProvider::USER_ID;

        $actual = $this->Discount->calculateEcommerceItemDiscounts($userId, $discountInfo, $items);

        $expected = [];
        $this->assertEquals($expected, $actual);
    }

    /**
     * @dataProvider DiscountProvider::testFindManageDiscountsForm
     * @param array $seedUpdate
     * @param array $expected
     */
    public function testFindManageDiscountsForm(array $seedUpdate, array $expected)
    {
        $token = DiscountProvider::DISCOUNT_UUID;
        if ($seedUpdate) {
            if (
                !$this->Discount->saveAssociated($seedUpdate)
                || $this->Discount->id !== $expected['Discount']['id']
            ) {
                throw new RuntimeException(json_encode(compact('seedUpdate')));
            }
        }

        $actual = $this->Discount->findManageDiscountsForm($token);

        $this->assertEquals($expected, $actual);
    }

    public function testSaveManageDiscountsForm()
    {
        $this->markTestIncomplete('testSaveManageDiscountsForm not implemented.');
    }

    /**
     * @dataProvider DiscountProvider::testReadBuyXGetYForm
     * @param array $data
     * @param array $expected
     * @throws ReflectionException
     */
    public function testReadBuyXGetYForm(array $data, array $expected)
    {
        $actual = $this->callPrivateMethod([$this->Discount, '_read_buy_x_get_y_form'], $data);

        $this->assertEquals($expected, $actual);
    }

    public function testUpdateUsageCount()
    {
        $this->markTestIncomplete('testUpdateUsageCount not implemented.');
    }

    /**
     * @dataProvider DiscountProvider::testCalculateB2bCartItemDiscountsProvider
     */
    public function testCalculateB2bCartItemDiscounts(array $discount, int $brandId, array $items, $expected)
    {
        $actual = $this->Discount->calculateB2bCartItemDiscounts($brandId, $discount, $items);

        $this->assertEquals($expected, $actual);
    }

    /**
     * @dataProvider DiscountProvider::testProductListItemDiscountsProvider
     */
    public function testProductListItemDiscounts(array $discount, int $brandId, array $items, $expected)
    {
        $actual = $this->Discount->calculateProductListItemDiscounts($brandId, $discount, $items);

        $this->assertEquals($expected, $actual);
    }

    /**
     * @dataProvider DiscountProvider::testOrderProductDiscountsProvider
     */
    public function testOrderProductDiscounts(array $discount, int $brandId, array $items, $expected)
    {
        $actual = $this->Discount->calculateOrderProductDiscounts($brandId, $discount, $items);

        $this->assertEquals($expected, $actual);
    }

    /**
     * @dataProvider DiscountProvider::testGetAvailableB2bDiscounts
     */
    public function testGetAvailableB2bDiscounts(int $brandId, int $retailerId, array $expected)
    {
        $this->assertEquals($expected, $this->Discount->getAvailableB2bDiscounts($retailerId, $brandId));
    }
}
