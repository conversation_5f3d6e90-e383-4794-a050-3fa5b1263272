<?php
App::uses('AppTestCase', 'TestSuite');
App::uses('Label', 'Model');

/**
 * Label Test Case.
 *
 * @property Label $Label
 */
class LabelTest extends AppTestCase
{
    public $fixtures = [
        'app.label',
        'app.user',
    ];

    public function setUp()
    {
        parent::setUp();
        $this->Label = ClassRegistry::init('Label');
    }

    public function tearDown()
    {
        unset($this->Label);
        parent::tearDown();
    }
}
