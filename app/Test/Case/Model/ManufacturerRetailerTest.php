<?php
App::uses('AppTestCase', 'TestSuite');
App::uses('ManufacturerRetailer', 'Model');
App::uses('Router', 'Routing');

/**
 * ManufacturerRetailer Test Case.
 *
 * Run with the following console command:
 *
 * ./app/Console/cake test app Model/ManufacturerRetailer
 *
 * @property ManufacturerRetailer $ManufacturerRetailer
 */
class ManufacturerRetailerTest extends AppTestCase
{
    const ID_RETAILER = '3';
    const ID_BRANCH = '18';
    const ID_NEW = '21';

    const USER_ID_BRAND = '8';
    const USER_ID_RETAILER = '7';
    const USER_ID_BRANCH = '17';
    const USER_ID_SALES_REP = '21';
    const USER_ID_SALES_REP_2 = '23';
    const USER_ID_DISTRIBUTOR = '22';
    const USER_ID_CREDIT_TERM = '1001';

    const COUNTRY_ID_CA = '39';
    const STATE_ID_ON_CA = '611';

    public $fixtures = [
        'app.b2b_ship_to_address',
        'app.brand_staff_permission',
        'app.credit_term',
        'app.contactpersons',
        'app.i18n',
        'app.label',
        'app.local_delivery_zip_code',
        'app.manufacturer_retailer',
        'app.manufacturer_retailer_label',
        'app.manufacturer_retailer_sales_rep',
        'app.manufacturer_retailer_tag',
        'app.manufacturer_sales_rep',
        'app.pricing_tier',
        'app.product',
        'app.product_non_applicable_order_type',
        'app.product_state_fee',
        'app.product_title',
        'app.product_variant_option',
        'app.retailer_credit',
        'app.retailer_credit_item',
        'app.retailer_credit_payment',
        'app.retailer_credit_term',
        'app.ship_from_store_zip_code',
        'app.staff_permission',
        'app.store',
        'app.stripe_user',
        'app.stripe_user_capability',
        'app.tag',
        'app.territory',
        'app.user',
        'app.warehouse',
    ];

    public function setUp()
    {
        parent::setUp();
        $this->ManufacturerRetailer = ClassRegistry::init('ManufacturerRetailer');

        /** @var ManufacturerRetailerTag $ManufacturerRetailerTag */
        $ManufacturerRetailerTag = ClassRegistry::init('ManufacturerRetailerTag');
        $ManufacturerRetailerTag->truncate();
    }

    public function tearDown()
    {
        unset($this->ManufacturerRetailer);
        parent::tearDown();
    }

    /**
     * @dataProvider providerBeforeSave
     * @param array $data
     * @param array $expected
     */
    public function testBeforeSave(array $data, array $expected)
    {
        $this->ManufacturerRetailer->data = $data;

        $this->ManufacturerRetailer->beforeSave();
        $actual = $this->ManufacturerRetailer->data;

        $this->assertRegExp('/' . Router::UUID . '/', $actual['ManufacturerRetailer']['uuid']);
        unset($actual['ManufacturerRetailer']['uuid']);
        $this->assertEquals($expected, $actual, json_encode($this->ManufacturerRetailer->data));
    }

    public function providerBeforeSave()
    {
        return [
            'local_delivery_disabled' => [
                [
                    'ManufacturerRetailer' => [
                        'local_delivery_radius' => 0.0,
                    ],
                ],
                [
                    'ManufacturerRetailer' => [
                        'local_delivery_radius' => 0.0,
                        'brand_local_delivery' => false,
                    ],
                ],
            ],
            'local_delivery_enabled' => [
                [
                    'ManufacturerRetailer' => [
                        'local_delivery_radius' => 0.1,
                    ],
                ],
                [
                    'ManufacturerRetailer' => [
                        'local_delivery_radius' => 0.1,
                        'brand_local_delivery' => true,
                    ],
                ],
            ],
            'null_empty_account_fields' => [
                [
                    'ManufacturerRetailer' => [
                        'external_retailer_account' => '',
                        'external_retailer_email' => '',
                        'external_retailer_company' => '',
                    ],
                ],
                [
                    'ManufacturerRetailer' => [
                        'external_retailer_account' => null,
                        'external_retailer_email' => null,
                        'external_retailer_company' => null,
                    ],
                ],
            ],
        ];
    }

    public function testGetStoreConnection()
    {
        $actual = $this->ManufacturerRetailer->getStoreConnection(static::USER_ID_BRAND, static::USER_ID_BRANCH, [
            'retailer_id',
        ]);

        $expected = [
            'retailer_id' => static::USER_ID_BRANCH,
        ];
        $this->assertEquals($expected, $actual);
    }

    public function testGetMasterConnection()
    {
        if (!$this->ManufacturerRetailer->deleteAllJoinless(['ManufacturerRetailer.user_id' => static::USER_ID_BRAND, 'ManufacturerRetailer.retailer_id' => static::USER_ID_BRANCH], false)) {
            throw new RuntimeException('Failed to setup branch deferring to master connection record');
        }

        $deprecationWarning = '';
        set_error_handler(function($code, $message) use (&$deprecationWarning) {
            $deprecationWarning = $message;
        });

        try {
            $actual = $this->ManufacturerRetailer->getStoreConnection(static::USER_ID_BRAND, static::USER_ID_BRANCH, [
                'retailer_id'
            ]);
        } finally {
            restore_error_handler();
        }

        $expected = [
            'retailer_id' => static::USER_ID_RETAILER,
        ];
        $this->assertEquals($expected, $actual);
        $this->assertStringStartsWith('Deferring to master retailer connection where: {"brandId":8,"storeId":17,"masterId":7}', $deprecationWarning);
    }

    public function testFindStoreConnections()
    {
        $actual = $this->ManufacturerRetailer->findStoreConnections(static::USER_ID_BRAND, [static::USER_ID_RETAILER, static::USER_ID_BRANCH], [
            'id',
        ]);
        $expected = [
            static::USER_ID_RETAILER => ['id' => '3', 'retailer_id' => static::USER_ID_RETAILER],
            static::USER_ID_BRANCH => ['id' => '18', 'retailer_id' => static::USER_ID_BRANCH],
        ];
        $this->assertEquals($expected, $actual);
    }

    /**
     * @dataProvider providerGetRetailerCountByManufacturer
     */
    public function testGetRetailerCountByManufacturer($manufacturerId, $expectedCount, $expectedSample)
    {
        $actual = $this->ManufacturerRetailer->getRetailerCountByManufacturer($manufacturerId);
        $this->assertCount($expectedCount, $actual);
        $this->assertEquals($expectedSample, $actual[0]);
    }

    public function providerGetRetailerCountByManufacturer(): array
    {
        $manufacturerId = static::USER_ID_BRAND;

        return [
            'all' => [
                'manufacturerId' => null,
                'expectedCount' => 4,
                'expectedSample' => [
                    'ManufacturerRetailer' => ['user_id' => '1'],
                    0 => ['Retailers' => '2'],
                    'User' => ['id' => '1'],
                ],
            ],
            'filtered' => [
                'manufacturerId' => $manufacturerId,
                'expectedCount' => 1,
                'expectedSample' => [
                    'ManufacturerRetailer' => ['user_id' => $manufacturerId],
                    0 => ['Retailers' => '3'],
                    'User' => ['id' => $manufacturerId],
                ],
            ],
        ];
    }

    public function testCountMasterRetailers()
    {
        $this->markTestIncomplete('testCountMasterRetailers not implemented.');
        $this->ManufacturerRetailer->countMasterRetailers(static::USER_ID_BRAND);
    }

    public function testCountManufacturers()
    {
        $this->markTestIncomplete('testCountManufacturers not implemented.');
        $this->ManufacturerRetailer->countManufacturers(static::USER_ID_RETAILER);
    }

    /**
     * @dataProvider providerFindAllRetailersWithStores
     */
    public function testCountAllRetailersWithStores(callable $setup, array $expected)
    {
        call_user_func($setup, $this);
        $actual = $this->ManufacturerRetailer->countAllRetailersWithStores(static::USER_ID_BRAND, ['ManufacturerRetailer.retailer_id' => [7, 17, 2, 10]]);
        $this->assertSame(count($expected) + 2, $actual);
    }

    /**
     * @dataProvider providerFindAllRetailersWithStores
     */
    public function testFindAllRetailersWithStores(callable $setup, array $expected)
    {
        call_user_func($setup, $this);
        $actual = $this->ManufacturerRetailer->findAllRetailersWithStores(static::USER_ID_BRAND, ['ManufacturerRetailer.retailer_id' => [7, 17]], null, null, null);
        $this->assertEquals($expected, $actual);
    }

    public function providerFindAllRetailersWithStores(): array
    {
        $expected = [
            7 => [
                'ManufacturerRetailer' => [
                    'id' => '3',
                    'user_id' => '8',
                    'retailer_id' => '7',
                    'pricingtierid' => '1',
                    'is_commission_tier' => false,
                    'status' => '1',
                    'dealer_protect_radius' => '0',
                    'enable_install' => false,
                    'local_delivery_radius' => '1000',
                ],
                'User' => [
                    'id' => '7',
                    'Branch' => '0',
                    'company_name' => 'Local Shop',
                    'currency_code' => 'CAD',
                ],
                'Product' => [
                    'count' => 3,
                ],
                'RetailerCredit' => [
                    'total_balance' => null,
                ],
                'SalesRep' => [
                    21 => 'Sales Rep',
                ],
                'stores' => [
                    17 => [
                        'ManufacturerRetailer' => [
                            'id' => '18',
                            'user_id' => '8',
                            'retailer_id' => '17',
                            'pricingtierid' => '1',
                            'is_commission_tier' => false,
                            'status' => '1',
                            'dealer_protect_radius' => '0',
                            'enable_install' => false,
                            'local_delivery_radius' => '1000',
                        ],
                        'User' => [
                            'id' => '17',
                            'Branch' => '7',
                            'company_name' => 'Local Shop Branch',
                            'currency_code' => 'CAD',
                        ],
                        'Product' => [
                            'count' => 0,
                        ],
                        'RetailerCredit' => [
                            'total_balance' => null,
                        ],
                        'SalesRep' => [
                            21 => 'Sales Rep',
                        ],
                    ],
                ],
            ],
        ];

        return [
            'default' => [
                'setup' => function(self $test) {
                },
                'expected' => $expected,
            ],
            'product_count_excludes_negative_inventory' => [
                'setup' => function(self $test) {
                    /** @var Store $Store */
                    $Store = ClassRegistry::init('Store');
                    $success = $Store->updateAllJoinless(['Store.inventoryCount' => '-Store.inventoryCount'], [
                        'Store.storeId' => '7',
                        'Store.productId' => '21',
                    ]);
                    if (!$success) {
                        throw new RuntimeException('Failed to seed negative Store.inventoryCount');
                    }
                },
                'expected' => (function($expected) {
                    $expected[7]['Product']['count'] = 2;

                    return $expected;
                })($expected),
            ],
            'excludes_removed_master_with_stores' => [
                'setup' => function(self $test) {
                    $success = $test->ManufacturerRetailer->updateAllJoinless(['ManufacturerRetailer.removed' => 'TRUE'], [
                        'ManufacturerRetailer.user_id' => '8',
                        'ManufacturerRetailer.retailer_id' => '7',
                    ]);
                    if (!$success) {
                        throw new RuntimeException('Failed to seed removed master retailer');
                    }
                },
                'expected' => (function($expected) {
                    return [];
                })($expected),
            ],
            'excludes_removed_store' => [
                'setup' => function(self $test) {
                    $success = $test->ManufacturerRetailer->updateAllJoinless(['ManufacturerRetailer.removed' => 'TRUE'], [
                        'ManufacturerRetailer.user_id' => '8',
                        'ManufacturerRetailer.retailer_id' => '17',
                    ]);
                    if (!$success) {
                        throw new RuntimeException('Failed to seed removed store');
                    }
                },
                'expected' => (function($expected) {
                    $expected[7]['stores'] = [];

                    return $expected;
                })($expected),
            ],
        ];
    }

    /**
     * Covers an edge case where the inner Branch query made inside the streamPagedQuery() callback broke
     * ContainableBehavior causing the next page to fetch with a higher recursive level leading to poor performance
     * and unused excess data.
     *
     * Discovered by PDOExceptions thrown when Territory associations of the next page attempted to retrieve hasMany
     * ManufacturerRetailer with virtual fields that did not have the required joins.
     */
    public function testStreamExportDataPaginationKeepsBoundAssociations()
    {
        // Exercise maintaining associations when paginating
        $pageSize = 1;
        $seedData = array_map(
            function(int $id): array {
                return [
                    'ManufacturerRetailer' => [
                        'id' => $id,
                        'distributor_id' => static::USER_ID_DISTRIBUTOR,
                        'territory_id' => '1',
                        'warehouse_id' => '1',
                    ],
                    'B2bShipToAddress' => [
                        'id' => null,
                        'manufacturer_retailer_id' => $id,
                        'address1' => '417 Montgomery St',
                        'address2' => 'Floor 5',
                        'city' => 'San Francisco',
                        'country_id' => '233',
                        'state_id' => '5197',
                        'zipcode' => '94104',
                        'telephone' => '4155554567',
                    ],
                    // See: https://book.cakephp.org/2.0/en/models/saving-your-data.html#saving-related-model-data-habtm
                    'SalesRep' => ['SalesRep' => [static::USER_ID_SALES_REP]],
                    'CreditTerm' => ['CreditTerm' => [static::USER_ID_CREDIT_TERM]],
                ];
            },
            array_values((array)$this->ManufacturerRetailer->find('list', [
                'recursive' => -1,
                'conditions' => ['ManufacturerRetailer.user_id' => static::USER_ID_BRAND],
                'fields' => ['id'],
            ]))
        );
        $this->ManufacturerRetailer->bindModel([
            'hasOne' => ['B2bShipToAddress'],
            'hasAndBelongsToMany' => [
                'SalesRep' => ['className' => 'User', 'with' => 'ManufacturerRetailerSalesRep', 'associationForeignKey' => 'sales_rep_id', 'unique' => 'keepExisting'],
                'CreditTerm' => ['with' => 'RetailerCreditTerm', 'unique' => 'keepExisting'],
            ],
        ], false);
        $success = $this->ManufacturerRetailer->saveMany($seedData, ['deep' => true]);
        $this->ManufacturerRetailer->unbindModel([
            'hasOne' => ['B2bShipToAddress'],
            'hasAndBelongsToMany' => ['SalesRep', 'CreditTerm'],
        ], false);
        $this->ManufacturerRetailer->clear();
        if (!$success) {
            throw new RuntimeException(json_encode(['message' => 'Failed to seed B2bShipToAddress', 'errors' => $this->ManufacturerRetailer->validationErrors]));
        }

        $actual = [];
        $this->ManufacturerRetailer->streamExportData(static::USER_ID_BRAND, [], function(array $record) use (&$actual) {
            $actual[] = $record;
        }, $pageSize);

        $expected = [
            0 => [
                'ManufacturerRetailer' => [
                    'id' => '5',
                    'user_id' => '8',
                    'retailer_id' => '2',
                    'external_retailer_account' => null,
                    'external_retailer_email' => null,
                    'external_retailer_company' => null,
                    'pricingtierid' => '1',
                    'is_commission_tier' => false,
                    'commission_uses_retailer_tax_rate' => false,
                    'warehouse_id' => '1',
                    'is_ship_to_store_only' => false,
                    'status' => '0',
                    'dealer_protect_radius' => '0',
                    'b2b_tax' => '0.0000',
                    'vat_number' => null,
                    'b2b_minimum' => '0.00',
                    'enable_consumer_orders' => true,
                    'enable_split_payment' => true,
                    'local_delivery_radius' => '0',
                    'credit_limit' => '10000.00',
                    'enable_b2b_credit_card' => true,
                    'enable_b2b_external_payment' => false,
                    'ship_from_store_distance' => '0',
                    'has_active_integration' => '0',
                ],
                'RetailerCredit' => ['total_balance' => null],
                'StripeUser' => [
                    'id' => '2',
                    'is_activated' => true,
                    'charges_enabled' => true,
                    'stripe_user_id' => 'acct_17FVVxLBFYPhk3Kp',
                ],
                'StripeUserCapabilities' => [
                    'stripe_user_id' => null,
                    'affirm_payments' => null,
                    'klarna_payments' => null,
                ],
                'B2bShipToAddress' => [
                    'id' => '1',
                    'address1' => '417 Montgomery St',
                    'address2' => 'Floor 5',
                    'city' => 'San Francisco',
                    'country_id' => '233',
                    'state_id' => '5197',
                    'zipcode' => '94104',
                    'telephone' => '4155554567',
                    'Country' => ['id' => '233', 'country_name' => 'United States'],
                    'State' => ['id' => '5197', 'state_name' => 'California'],
                ],
                'User' => [
                    'id' => '2',
                    'email_address' => '<EMAIL>',
                    'company_name' => 'LightSpeed',
                    'Branch' => '0',
                    'address' => '2400 Nipigon Road_,_',
                    'city' => 'Thunder Bay',
                    'zipcode' => 'P7C 4W1',
                    'state_id' => '611',
                    'country_id' => '39',
                    'Country' => ['id' => '39', 'country_name' => 'Canada'],
                    'State' => ['id' => '611', 'state_name' => 'Ontario'],
                    'Contactperson' => [
                        'id' => '2',
                        'user_id' => '2',
                        'firstname' => 'Tony',
                        'lastname' => 'Pulis',
                        'email' => '<EMAIL>',
                    ],
                    'Contact' => ['company' => '************', 'person' => '***********'],
                ],
                'Distributor' => [
                    'id' => '22',
                    'ManufacturerSalesRep' => ['id' => '4', 'descriptor' => 'Distributor'],
                ],
                'PricingTier' => ['id' => '1', 'pricingtiername' => 'Tier 1'],
                'Warehouse' => ['id' => '1', 'name' => 'West Coast'],
                'Territory' => ['id' => '1', 'name' => 'Local Dealers'],
                'SalesRep' => [
                    0 => [
                        'id' => '21',
                        'ManufacturerSalesRep' => ['id' => '3', 'descriptor' => 'Sales Rep'],
                    ],
                ],
                'CreditTerm' => [1001 => 'Test credit term 1'],
            ],
            1 => [
                'ManufacturerRetailer' => [
                    'id' => '3',
                    'user_id' => '8',
                    'retailer_id' => '7',
                    'external_retailer_account' => null,
                    'external_retailer_email' => null,
                    'external_retailer_company' => null,
                    'pricingtierid' => '1',
                    'is_commission_tier' => false,
                    'commission_uses_retailer_tax_rate' => false,
                    'warehouse_id' => '1',
                    'is_ship_to_store_only' => false,
                    'status' => '1',
                    'dealer_protect_radius' => '0',
                    'b2b_tax' => '5.5000',
                    'vat_number' => null,
                    'b2b_minimum' => '0.00',
                    'enable_consumer_orders' => true,
                    'enable_split_payment' => true,
                    'local_delivery_radius' => '1000',
                    'credit_limit' => '10000.00',
                    'enable_b2b_credit_card' => true,
                    'enable_b2b_external_payment' => true,
                    'ship_from_store_distance' => '0',
                    'has_active_integration' => '0',
                ],
                'RetailerCredit' => ['total_balance' => null],
                'StripeUser' => [
                    'id' => '3',
                    'is_activated' => true,
                    'charges_enabled' => true,
                    'stripe_user_id' => 'acct_17FVVxLBFYPhk3Kp',
                ],
                'StripeUserCapabilities' => [
                    'stripe_user_id' => '3',
                    'affirm_payments' => '1',
                    'klarna_payments' => '1',
                ],
                'B2bShipToAddress' => [
                    'id' => '2',
                    'address1' => '417 Montgomery St',
                    'address2' => 'Floor 5',
                    'city' => 'San Francisco',
                    'country_id' => '233',
                    'state_id' => '5197',
                    'zipcode' => '94104',
                    'telephone' => '4155554567',
                    'Country' => ['id' => '233', 'country_name' => 'United States'],
                    'State' => ['id' => '5197', 'state_name' => 'California'],
                ],
                'User' => [
                    'id' => '7',
                    'email_address' => '<EMAIL>',
                    'company_name' => 'Local Shop',
                    'Branch' => '0',
                    'address' => '955 Oliver Rd_,_',
                    'city' => 'Thunder Bay',
                    'zipcode' => 'P7B 5E1',
                    'state_id' => '611',
                    'country_id' => '39',
                    'Country' => ['id' => '39', 'country_name' => 'Canada'],
                    'State' => ['id' => '611', 'state_name' => 'Ontario'],
                    'Contactperson' => [
                        'id' => '3',
                        'user_id' => '7',
                        'firstname' => 'Local',
                        'lastname' => 'Host',
                        'email' => '<EMAIL>',
                    ],
                    'Contact' => ['company' => '123-123-1234', 'person' => '123-123-1234'],
                ],
                'Distributor' => [
                    'id' => '22',
                    'ManufacturerSalesRep' => ['id' => '4', 'descriptor' => 'Distributor'],
                ],
                'PricingTier' => ['id' => '1', 'pricingtiername' => 'Tier 1'],
                'Warehouse' => ['id' => '1', 'name' => 'West Coast'],
                'Territory' => ['id' => '1', 'name' => 'Local Dealers'],
                'SalesRep' => [
                    0 => [
                        'id' => '21',
                        'ManufacturerSalesRep' => ['id' => '3', 'descriptor' => 'Sales Rep'],
                    ],
                ],
                'CreditTerm' => [1001 => 'Test credit term 1'],
            ],
            2 => [
                'ManufacturerRetailer' => [
                    'id' => '18',
                    'user_id' => '8',
                    'retailer_id' => '17',
                    'external_retailer_account' => null,
                    'external_retailer_email' => null,
                    'external_retailer_company' => null,
                    'pricingtierid' => '1',
                    'is_commission_tier' => false,
                    'commission_uses_retailer_tax_rate' => false,
                    'warehouse_id' => '1',
                    'is_ship_to_store_only' => false,
                    'status' => '1',
                    'dealer_protect_radius' => '0',
                    'b2b_tax' => '5.5000',
                    'vat_number' => null,
                    'b2b_minimum' => '0.00',
                    'enable_consumer_orders' => true,
                    'enable_split_payment' => true,
                    'local_delivery_radius' => '1000',
                    'credit_limit' => '10000.00',
                    'enable_b2b_credit_card' => true,
                    'enable_b2b_external_payment' => true,
                    'ship_from_store_distance' => '0',
                    'has_active_integration' => '0',
                ],
                'RetailerCredit' => ['total_balance' => null],
                'StripeUser' => [
                    'id' => '3',
                    'is_activated' => true,
                    'charges_enabled' => true,
                    'stripe_user_id' => 'acct_17FVVxLBFYPhk3Kp',
                ],
                'StripeUserCapabilities' => [
                    'stripe_user_id' => '3',
                    'affirm_payments' => '1',
                    'klarna_payments' => '1',
                ],
                'B2bShipToAddress' => [
                    'id' => '4',
                    'address1' => '417 Montgomery St',
                    'address2' => 'Floor 5',
                    'city' => 'San Francisco',
                    'country_id' => '233',
                    'state_id' => '5197',
                    'zipcode' => '94104',
                    'telephone' => '4155554567',
                    'Country' => ['id' => '233', 'country_name' => 'United States'],
                    'State' => ['id' => '5197', 'state_name' => 'California'],
                ],
                'User' => [
                    'id' => '17',
                    'email_address' => '<EMAIL>',
                    'company_name' => 'Local Shop Branch',
                    'Branch' => '7',
                    'address' => '216 Brodie St S_,_',
                    'city' => 'Thunder Bay',
                    'zipcode' => 'P7E 1C2',
                    'state_id' => '611',
                    'country_id' => '39',
                    'Country' => ['id' => '39', 'country_name' => 'Canada'],
                    'State' => ['id' => '611', 'state_name' => 'Ontario'],
                    'Contactperson' => [
                        'id' => '13',
                        'user_id' => '17',
                        'firstname' => 'Aron',
                        'lastname' => 'Schmidt',
                        'email' => '<EMAIL>',
                    ],
                    'Contact' => ['company' => '123-123-1234', 'person' => '123-123-1234'],
                ],
                'Distributor' => [
                    'id' => '22',
                    'ManufacturerSalesRep' => ['id' => '4', 'descriptor' => 'Distributor'],
                ],
                'PricingTier' => ['id' => '1', 'pricingtiername' => 'Tier 1'],
                'Warehouse' => ['id' => '1', 'name' => 'West Coast'],
                'Territory' => ['id' => '1', 'name' => 'Local Dealers'],
                'SalesRep' => [
                    0 => [
                        'id' => '21',
                        'ManufacturerSalesRep' => ['id' => '3', 'descriptor' => 'Sales Rep'],
                    ],
                ],
                'CreditTerm' => [1001 => 'Test credit term 1'],
            ],
            3 => [
                'ManufacturerRetailer' => [
                    'id' => '4',
                    'user_id' => '8',
                    'retailer_id' => '10',
                    'external_retailer_account' => null,
                    'external_retailer_email' => null,
                    'external_retailer_company' => null,
                    'pricingtierid' => '3',
                    'is_commission_tier' => false,
                    'commission_uses_retailer_tax_rate' => false,
                    'warehouse_id' => '1',
                    'is_ship_to_store_only' => false,
                    'status' => '1',
                    'dealer_protect_radius' => '0',
                    'b2b_tax' => '0.0000',
                    'vat_number' => null,
                    'b2b_minimum' => '0.00',
                    'enable_consumer_orders' => true,
                    'enable_split_payment' => true,
                    'local_delivery_radius' => '1000',
                    'credit_limit' => '10000.00',
                    'enable_b2b_credit_card' => true,
                    'enable_b2b_external_payment' => false,
                    'ship_from_store_distance' => '0',
                    'has_active_integration' => '0',
                ],
                'RetailerCredit' => ['total_balance' => null],
                'StripeUser' => [
                    'id' => '5',
                    'is_activated' => true,
                    'charges_enabled' => true,
                    'stripe_user_id' => 'acct_17FVVxLBFYPhk3Kp',
                ],
                'StripeUserCapabilities' => [
                    'stripe_user_id' => null,
                    'affirm_payments' => null,
                    'klarna_payments' => null,
                ],
                'B2bShipToAddress' => [
                    'id' => '3',
                    'address1' => '417 Montgomery St',
                    'address2' => 'Floor 5',
                    'city' => 'San Francisco',
                    'country_id' => '233',
                    'state_id' => '5197',
                    'zipcode' => '94104',
                    'telephone' => '4155554567',
                    'Country' => ['id' => '233', 'country_name' => 'United States'],
                    'State' => ['id' => '5197', 'state_name' => 'California'],
                ],
                'User' => [
                    'id' => '10',
                    'email_address' => '<EMAIL>',
                    'company_name' => 'No POS',
                    'Branch' => '0',
                    'address' => '123 Fake Street_,_',
                    'city' => 'Thunder Bay',
                    'zipcode' => 'P7C 4W1',
                    'state_id' => '611',
                    'country_id' => '39',
                    'Country' => ['id' => '39', 'country_name' => 'Canada'],
                    'State' => ['id' => '611', 'state_name' => 'Ontario'],
                    'Contactperson' => [
                        'id' => '6',
                        'user_id' => '10',
                        'firstname' => 'Activate',
                        'lastname' => 'Me',
                        'email' => '<EMAIL>',
                    ],
                    'Contact' => ['company' => '123-123-1234', 'person' => '123-123-1234'],
                ],
                'Distributor' => [
                    'id' => '22',
                    'ManufacturerSalesRep' => ['id' => '4', 'descriptor' => 'Distributor'],
                ],
                'PricingTier' => ['id' => '3', 'pricingtiername' => 'Tier 3'],
                'Warehouse' => ['id' => '1', 'name' => 'West Coast'],
                'Territory' => ['id' => '1', 'name' => 'Local Dealers'],
                'SalesRep' => [
                    0 => [
                        'id' => '21',
                        'ManufacturerSalesRep' => ['id' => '3', 'descriptor' => 'Sales Rep'],
                    ],
                ],
                'CreditTerm' => [1001 => 'Test credit term 1'],
            ],
        ];
        $this->assertEquals($expected, $actual);
    }

    /**
     * @dataProvider providerFindStoreSettings
     * @param int $retailerId
     * @param array $expected
     */
    public function testFindStoreSettings(int $retailerId, array $expected)
    {
        $id = $expected['ManufacturerRetailer']['id'];
        $this->_seedExampleTags(['ManufacturerRetailer.id' => $id]);
        $this->_seedExampleZipCodeFilters(['id' => $id, 'retailer_id' => '7'], ['P7C 4W1', 'P7B%']);

        $actual = $this->ManufacturerRetailer->findStoreSettings(static::USER_ID_BRAND, $retailerId);

        $this->assertEquals($expected, $actual);
    }

    public function providerFindStoreSettings(): array
    {
        return [
            'master' => [
                'retailerId' => static::USER_ID_RETAILER,
                'expected' => [
                    'ManufacturerRetailer' => [
                        'id' => '3',
                        'user_id' => '8',
                        'retailer_id' => '7',
                        'external_retailer_account' => null,
                        'external_retailer_email' => null,
                        'external_retailer_company' => null,
                        'is_commission_tier' => false,
                        'commission_uses_retailer_tax_rate' => false,
                        'distributor_id' => null,
                        'territory_id' => null,
                        'warehouse_id' => null,
                        'status' => '1',
                        'b2b_tax' => '5.5000',
                        'vat_number' => null,
                        'b2b_minimum' => '0.00',
                        'enable_b2b_credit_card' => true,
                        'enable_b2b_external_payment' => true,
                        'credit_limit' => '10000.00',
                        'is_ship_to_store_only' => false,
                        'ship_from_store_distance' => '0',
                        'ship_from_store_unprotected_zones_only' => false,
                        'pricingtierid' => '1',
                        'dealer_protect_radius' => '0',
                        'local_delivery_radius' => '1000',
                        'enable_consumer_orders' => true,
                        'enable_split_payment' => true,
                        'enable_strict_tag_matching' => false,
                        'use_retailer_shipping_rates' => false,
                        'enable_uploaded_inventory_reset' => true,
                        'credit_term_ids' => [
                            1001 => '1001',
                            1002 => '1002',
                            1003 => '1003'
                        ],
                        'sales_rep_ids' => [
                            0 => '21',
                        ],
                        'tag_ids' => [
                            0 => '1',
                            1 => '2',
                        ],
                        'territory_name' => null,
                        'local_delivery_zip_codes' => 'P7B*, P7C 4W1',
                        'ship_from_store_zip' => 'P7B*, P7C 4W1',
                        'labels' => ['TestLabel1', 'TestLabel2'],
                    ],
                    'Territory' => [
                        'id' => null,
                        'name' => null,
                    ],
                    'User' => [
                        'id' => '7',
                        'Branch' => '0',
                        'company_name' => 'Local Shop',
                        'currency_code' => 'CAD',
                        'install_hourly_rate' => '0.00',
                    ],
                    'RetailerCreditTerm' => [
                        0 => [
                            'id' => '1',
                            'manufacturer_retailer_id' => '3',
                            'credit_term_id' => '1001'
                        ],
                        1 => [
                            'id' => '2',
                            'manufacturer_retailer_id' => '3',
                            'credit_term_id' => '1002'
                        ],
                        2 => [
                            'id' => '3',
                            'manufacturer_retailer_id' => '3',
                            'credit_term_id' => '1003'
                        ],
                    ],
                    'ManufacturerRetailerSalesRep' => [
                        0 => [
                            'id' => '1',
                            'manufacturer_retailer_id' => '3',
                            'sales_rep_id' => '21',
                        ],
                    ],
                    'ManufacturerRetailerTag' => [
                        0 => [
                            'id' => '1',
                            'manufacturer_retailer_id' => '3',
                            'tag_id' => '1',
                        ],
                        1 => [
                            'id' => '2',
                            'manufacturer_retailer_id' => '3',
                            'tag_id' => '2',
                        ],
                    ],
                    'LocalDeliveryZipCode' => [
                        [
                            'id' => '2',
                            'manufacturer_retailer_id' => '3',
                            'zip_code' => 'P7B%',
                        ],
                        [
                            'id' => '1',
                            'manufacturer_retailer_id' => '3',
                            'zip_code' => 'P7C 4W1',
                        ],
                    ],
                    'ShipFromStoreZipCode' => [
                        [
                            'id' => '2',
                            'manufacturer_retailer_id' => '3',
                            'zip_code' => 'P7B%',
                        ],
                        [
                            'id' => '1',
                            'manufacturer_retailer_id' => '3',
                            'zip_code' => 'P7C 4W1',
                        ],
                    ],
                    'Label' => [
                        [
                            'id' => 1,
                            'name' => 'TestLabel1'
                        ],
                        [
                            'id' => 2,
                            'name' => 'TestLabel2'
                        ]
                    ],
                ],
            ],
            'branch' => [
                'retailerId' => static::USER_ID_BRANCH,
                'expected' => [
                    'ManufacturerRetailer' => [
                        'id' => '18',
                        'user_id' => '8',
                        'retailer_id' => '17',
                        'external_retailer_account' => null,
                        'external_retailer_email' => null,
                        'external_retailer_company' => null,
                        'is_commission_tier' => false,
                        'commission_uses_retailer_tax_rate' => false,
                        'distributor_id' => null,
                        'territory_id' => null,
                        'warehouse_id' => null,
                        'status' => '1',
                        'b2b_tax' => '5.5000',
                        'vat_number' => null,
                        'b2b_minimum' => '0.00',
                        'enable_b2b_credit_card' => true,
                        'enable_b2b_external_payment' => true,
                        'credit_limit' => '10000.00',
                        'is_ship_to_store_only' => false,
                        'ship_from_store_distance' => '0',
                        'ship_from_store_unprotected_zones_only' => false,
                        'pricingtierid' => '1',
                        'dealer_protect_radius' => '0',
                        'local_delivery_radius' => '1000',
                        'enable_consumer_orders' => true,
                        'enable_split_payment' => true,
                        'enable_strict_tag_matching' => false,
                        'use_retailer_shipping_rates' => false,
                        'enable_uploaded_inventory_reset' => true,
                        'credit_term_ids' => [],
                        'sales_rep_ids' => [
                            0 => '21',
                        ],
                        'tag_ids' => [
                            0 => '1',
                            1 => '2',
                        ],
                        'territory_name' => null,
                        'local_delivery_zip_codes' => '',
                        'ship_from_store_zip' => '',
                        'labels' => ['TestLabel1', 'TestLabel2'],
                    ],
                    'Territory' => [
                        'id' => null,
                        'name' => null,
                    ],
                    'User' => [
                        'id' => '17',
                        'Branch' => '7',
                        'company_name' => 'Local Shop Branch',
                        'currency_code' => 'CAD',
                        'install_hourly_rate' => '0.00',
                    ],
                    'RetailerCreditTerm' => [],
                    'ManufacturerRetailerSalesRep' => [
                        0 => [
                            'id' => '2',
                            'manufacturer_retailer_id' => '18',
                            'sales_rep_id' => '21',
                        ],
                    ],
                    'ManufacturerRetailerTag' => [
                        0 => [
                            'id' => '1',
                            'manufacturer_retailer_id' => '18',
                            'tag_id' => '1',
                        ],
                        1 => [
                            'id' => '2',
                            'manufacturer_retailer_id' => '18',
                            'tag_id' => '2',
                        ],
                    ],
                    'LocalDeliveryZipCode' => [],
                    'ShipFromStoreZipCode' => [],
                    'Label' => [
                        [
                            'id' => 1,
                            'name' => 'TestLabel1'
                        ],
                        [
                            'id' => 2,
                            'name' => 'TestLabel2'
                        ]
                    ],
                ],
            ],
        ];
    }

    /**
     * @dataProvider providerSaveRetailerConnections
     * @param array $connections
     * @param array $expected
     */
    public function testSaveRetailerConnections(array $connections, array $expected)
    {
        $this->ManufacturerRetailer->saveRetailerConnections(static::USER_ID_BRAND, $connections);

        $retailers = $this->findSavedStoreSettings(static::USER_ID_BRAND, [static::USER_ID_RETAILER, static::USER_ID_BRANCH]);
        $retailers = Hash::combine($retailers, '{n}.ManufacturerRetailer.retailer_id', '{n}');
        $actual = [];
        foreach ($expected as $path => $value) {
            $actual[$path] = Hash::extract($retailers, $path);
        }
        $this->assertEquals($expected, $actual);
    }

    public function providerSaveRetailerConnections()
    {
        $master = static::USER_ID_RETAILER;
        $branch = static::USER_ID_BRANCH;
        $keyFields = [
            $master => ['retailer_id' => $master],
            $branch => ['retailer_id' => $branch],
        ];
        return [
            'master_overrides' => [
                'connections' => [
                    $master => $keyFields[$master] + [
                        'pricingtierid' => '2',
                        'sales_rep_ids' => [static::USER_ID_SALES_REP, static::USER_ID_SALES_REP_2],
                        'local_delivery_radius' => '10',
                        'dealer_protect_radius' => '30',
                    ],
                    $branch => $keyFields[$branch] + [
                        'pricingtierid' => '3',
                        'sales_rep_ids' => '',
                        'local_delivery_radius' => '20',
                        'dealer_protect_radius' => '40',
                    ],
                ],
                'expected' => [
                    "{$master}.ManufacturerRetailer.pricingtierid" => ['2'],
                    "{$branch}.ManufacturerRetailer.pricingtierid" => ['3'],
                    "{$master}.ManufacturerRetailer.is_commission_tier" => [false],
                    "{$branch}.ManufacturerRetailer.is_commission_tier" => [false],
                    "{$master}.ManufacturerRetailerSalesRep.{n}.sales_rep_id" => [static::USER_ID_SALES_REP, static::USER_ID_SALES_REP_2],
                    "{$branch}.ManufacturerRetailerSalesRep.{n}.sales_rep_id" => [],
                    "{$master}.ManufacturerRetailer.local_delivery_radius" => ['10'],
                    "{$branch}.ManufacturerRetailer.local_delivery_radius" => ['20'],
                    "{$master}.ManufacturerRetailer.dealer_protect_radius" => ['30'],
                    "{$branch}.ManufacturerRetailer.dealer_protect_radius" => ['40'],
                ],
            ],
            'branch_before_master' => [
                'connections' => [
                    $branch => $keyFields[$branch] + [
                        'pricingtierid' => '3',
                        'sales_rep_ids' => '',
                        'local_delivery_radius' => '20',
                        'dealer_protect_radius' => '40',
                    ],
                    $master => $keyFields[$master] + [
                        'pricingtierid' => '2',
                        'sales_rep_ids' => [static::USER_ID_SALES_REP, static::USER_ID_SALES_REP_2],
                        'local_delivery_radius' => '10',
                        'dealer_protect_radius' => '30',
                    ],
                ],
                'expected' => [
                    "{$master}.ManufacturerRetailer.pricingtierid" => ['2'],
                    "{$branch}.ManufacturerRetailer.pricingtierid" => ['3'],
                    "{$master}.ManufacturerRetailer.is_commission_tier" => [false],
                    "{$branch}.ManufacturerRetailer.is_commission_tier" => [false],
                    "{$master}.ManufacturerRetailerSalesRep.{n}.sales_rep_id" => [static::USER_ID_SALES_REP, static::USER_ID_SALES_REP_2],
                    "{$branch}.ManufacturerRetailerSalesRep.{n}.sales_rep_id" => [],
                    "{$master}.ManufacturerRetailer.local_delivery_radius" => ['10'],
                    "{$branch}.ManufacturerRetailer.local_delivery_radius" => ['20'],
                    "{$master}.ManufacturerRetailer.dealer_protect_radius" => ['30'],
                    "{$branch}.ManufacturerRetailer.dealer_protect_radius" => ['40'],
                ],
            ],
        ];
    }

    /**
     * @dataProvider providerSaveManyImportedConnections
     * @param array $connections
     * @param array $expected
     */
    public function testSaveManyImportedConnections(array $connections, array $expected)
    {
        $this->ManufacturerRetailer->saveManyImportedConnections(static::USER_ID_BRAND, $connections);

        $retailers = $this->findSavedStoreSettings(static::USER_ID_BRAND, [static::USER_ID_RETAILER, static::USER_ID_BRANCH]);
        $retailers = Hash::combine($retailers, '{n}.ManufacturerRetailer.retailer_id', '{n}');
        $actual = [];
        foreach ($expected as $path => $value) {
            $actual[$path] = Hash::extract($retailers, $path);
        }
        $this->assertEquals($expected, $actual);
    }

    public function providerSaveManyImportedConnections()
    {
        $master = static::USER_ID_RETAILER;
        $branch = static::USER_ID_BRANCH;
        $keyFields = [
            $master => ['id' => '3', 'retailer_id' => $master],
            $branch => ['id' => '18', 'retailer_id' => $branch],
        ];
        return [
            'master_overrides' => [
                'connections' => [
                    $keyFields[$master] + [
                        'pricingtierid' => '2',
                        'sales_rep_ids' => [static::USER_ID_SALES_REP, static::USER_ID_SALES_REP_2],
                        'distributor_id' => static::USER_ID_DISTRIBUTOR,
                        'local_delivery_radius' => '10',
                        'dealer_protect_radius' => '30',
                        'external_retailer_account' => '#1001',
                        'b2b_tax' => '13.0000',
                        'vat_number' => 'IE1234567T',
                        'b2b_minimum' => '100.00',
                        'warehouse_id' => '2',
                        'is_ship_to_store_only' => '1',
                        'credit_limit' => '100.00',
                        'status' => '0',
                        'external_retailer_email' => '<EMAIL>',
                        'external_retailer_company' => 'Master Company',
                    ],
                    $keyFields[$branch] + [
                        'pricingtierid' => '3',
                        'sales_rep_ids' => '',
                        'distributor_id' => '',
                        'local_delivery_radius' => '20',
                        'dealer_protect_radius' => '40',
                        'external_retailer_account' => '#1002',
                        'b2b_tax' => '7.5000',
                        'vat_number' => null,
                        'b2b_minimum' => '50.00',
                        'warehouse_id' => '1',
                        'is_ship_to_store_only' => '0',
                        'credit_limit' => '50.00',
                        'status' => '1',
                        'external_retailer_email' => '<EMAIL>',
                        'external_retailer_company' => 'Branch Company',
                    ],
                ],
                'expected' => [
                    "{$master}.ManufacturerRetailer.pricingtierid" => ['2'],
                    "{$branch}.ManufacturerRetailer.pricingtierid" => ['3'],
                    "{$master}.ManufacturerRetailer.is_commission_tier" => [false],
                    "{$branch}.ManufacturerRetailer.is_commission_tier" => [false],
                    "{$master}.ManufacturerRetailerSalesRep.{n}.sales_rep_id" => [static::USER_ID_SALES_REP, static::USER_ID_SALES_REP_2],
                    "{$branch}.ManufacturerRetailerSalesRep.{n}.sales_rep_id" => [],
                    "{$master}.ManufacturerRetailer.distributor_id" => [static::USER_ID_DISTRIBUTOR],
                    "{$branch}.ManufacturerRetailer.distributor_id" => [],
                    "{$master}.ManufacturerRetailer.local_delivery_radius" => ['10'],
                    "{$branch}.ManufacturerRetailer.local_delivery_radius" => ['20'],
                    "{$master}.ManufacturerRetailer.dealer_protect_radius" => ['30'],
                    "{$branch}.ManufacturerRetailer.dealer_protect_radius" => ['40'],
                    "{$master}.ManufacturerRetailer.external_retailer_account" => ['#1001'],
                    "{$branch}.ManufacturerRetailer.external_retailer_account" => ['#1002'],
                    "{$master}.ManufacturerRetailer.b2b_tax" => ['13.0000'],
                    "{$branch}.ManufacturerRetailer.b2b_tax" => ['13.0000'],
                    "{$master}.ManufacturerRetailer.vat_number" => ['IE1234567T'],
                    "{$branch}.ManufacturerRetailer.vat_number" => ['IE1234567T'],
                    "{$master}.ManufacturerRetailer.b2b_minimum" => ['100.00'],
                    "{$branch}.ManufacturerRetailer.b2b_minimum" => ['100.00'],
                    "{$master}.ManufacturerRetailer.warehouse_id" => ['2'],
                    "{$branch}.ManufacturerRetailer.warehouse_id" => ['2'],
                    "{$master}.ManufacturerRetailer.is_ship_to_store_only" => [true],
                    "{$branch}.ManufacturerRetailer.is_ship_to_store_only" => [false],
                    "{$master}.ManufacturerRetailer.credit_limit" => ['100.00'],
                    "{$branch}.ManufacturerRetailer.credit_limit" => ['100.00'],
                    "{$master}.ManufacturerRetailer.status" => ['0'],
                    "{$branch}.ManufacturerRetailer.status" => ['1'],
                    "{$master}.ManufacturerRetailer.external_retailer_email" => ['<EMAIL>'],
                    "{$branch}.ManufacturerRetailer.external_retailer_email" => ['<EMAIL>'],
                    "{$master}.ManufacturerRetailer.external_retailer_company" => ['Master Company'],
                    "{$branch}.ManufacturerRetailer.external_retailer_company" => ['Master Company'],
                ],
            ],
            'branch_before_master' => [
                'connections' => [
                    $keyFields[$branch] + [
                        'external_retailer_account' => '#1002',
                        'external_retailer_company' => 'Branch Company',
                    ],
                    $keyFields[$master] + [
                        'external_retailer_account' => '#1001',
                        'external_retailer_company' => 'Master Company',
                    ],
                ],
                'expected' => [
                    "{$branch}.ManufacturerRetailer.external_retailer_account" => ['#1002'],
                    "{$master}.ManufacturerRetailer.external_retailer_account" => ['#1001'],
                    "{$branch}.ManufacturerRetailer.external_retailer_company" => ['Master Company'],
                    "{$master}.ManufacturerRetailer.external_retailer_company" => ['Master Company'],
                ],
            ],
        ];
    }

    /**
     * @dataProvider providerSaveManyImportedShipToAddresses
     * @param array $connections
     * @param array $expected
     * @throws Exception
     */
    public function testSaveManyImportedShipToAddresses(array $connections, array $expected)
    {
        /** @var B2bShipToAddress $B2bShipToAddress */
        $B2bShipToAddress = ClassRegistry::init('B2bShipToAddress');

        // Seed default records
        $manufacturer_retailer_id = static::ID_RETAILER;
        $B2bShipToAddress->save([
            'id' => $B2bShipToAddress->fieldByConditions('id', compact('manufacturer_retailer_id')),
            'manufacturer_retailer_id' => $manufacturer_retailer_id,
            'address1' => '2400 Nipigon Road',
            'address2' => 'Box 398',
            'city' => 'Thunder Bay',
            'country_id' => static::COUNTRY_ID_CA,
            'state_id' => static::STATE_ID_ON_CA,
            'zipcode' => 'P7C 4W1',
            'telephone' => '************',
        ]);

        $this->ManufacturerRetailer->saveManyImportedConnections(static::USER_ID_BRAND, $connections);

        $retailers = $this->findSavedStoreSettings(static::USER_ID_BRAND, [static::USER_ID_RETAILER, static::USER_ID_BRANCH]);
        $retailers = Hash::combine($retailers, '{n}.ManufacturerRetailer.retailer_id', '{n}');
        $actual = [];
        foreach ($expected as $path => $value) {
            $actual[$path] = Hash::extract($retailers, $path);
        }
        $this->assertEquals($expected, $actual);
    }

    public function providerSaveManyImportedShipToAddresses()
    {
        $master = static::USER_ID_RETAILER;
        $branch = static::USER_ID_BRANCH;
        $keyFields = [
            $master => ['id' => '3', 'retailer_id' => $master],
            $branch => ['id' => '18', 'retailer_id' => $branch],
        ];

        // Seeded in test
        $defaultAddresses = [
            "{$master}.B2bShipToAddress.0" => [
                'manufacturer_retailer_id' => static::ID_RETAILER,
                'address1' => '2400 Nipigon Road',
                'address2' => 'Box 398',
                'city' => 'Thunder Bay',
                'country_id' => static::COUNTRY_ID_CA,
                'state_id' => static::STATE_ID_ON_CA,
                'zipcode' => 'P7C 4W1',
                'telephone' => '************',
            ],
        ];
        return [
            'saved' => [
                'connections' => [
                    // Update can omit fields
                    $keyFields[$master] + [
                        'B2bShipToAddress' => [
                            'address1' => '955 Oliver Rd',
                            'address2' => '',
                            //'city' => 'Thunder Bay',
                            //'state_id' => static::STATE_ID_ON_CA,
                            'zipcode' => 'P7B 5E1',
                            //'country_id' => static::COUNTRY_ID_CA,
                            'telephone' => '************',
                        ],
                    ],
                    // Create must provide all fields
                    $keyFields[$branch] + [
                        'B2bShipToAddress' => [
                            'address1' => '2400 Nipigon Road',
                            'address2' => 'Box 398',
                            'city' => 'Thunder Bay',
                            'state_id' => static::STATE_ID_ON_CA,
                            'zipcode' => 'P7C 4W1',
                            'country_id' => static::COUNTRY_ID_CA,
                            'telephone' => '************',
                        ],
                    ],
                ],
                'expected' => [
                    "{$master}.B2bShipToAddress.0" => [
                        'manufacturer_retailer_id' => static::ID_RETAILER,
                        'address1' => '955 Oliver Rd',
                        'address2' => '',
                        'city' => 'Thunder Bay',
                        'country_id' => static::COUNTRY_ID_CA,
                        'state_id' => static::STATE_ID_ON_CA,
                        'zipcode' => 'P7B 5E1',
                        'telephone' => '************',
                    ],
                    "{$branch}.B2bShipToAddress.0" => [
                        'manufacturer_retailer_id' => static::ID_BRANCH,
                        'address1' => '2400 Nipigon Road',
                        'address2' => 'Box 398',
                        'city' => 'Thunder Bay',
                        'country_id' => static::COUNTRY_ID_CA,
                        'state_id' => static::STATE_ID_ON_CA,
                        'zipcode' => 'P7C 4W1',
                        'telephone' => '************',
                    ],
                ],
            ],
            'deleted' => [
                'connections' => [
                    $keyFields[$master] + [
                        'B2bShipToAddress' => [
                            'address1' => '',
                            'address2' => '',
                            'city' => '',
                            'state_id' => null,
                            'zipcode' => '',
                            'country_id' => null,
                            'telephone' => '',
                        ],
                    ],
                ],
                'expected' => [
                    "{$master}.B2bShipToAddress.0" => [],
                ],
            ],
            'ignored_if_no_addresses' => [
                'connections' => [
                    $keyFields[$master],
                    $keyFields[$branch],
                ],
                'expected' => $defaultAddresses,
            ],
        ];
    }

    /**
     * @dataProvider providerSaveStoreSettings
     * @param string|int $retailerId
     * @param array $connection
     * @param array $expected
     */
    public function testSaveStoreSettings($retailerId, array $connection, array $expected)
    {
        // Seed zip code filter records to exercise updating/deleting
        $this->_seedExampleZipCodeFilters(['user_id' => static::USER_ID_BRAND, 'retailer_id' => $retailerId], ['p7b%', 'p7c%']);

        $success = $this->ManufacturerRetailer->saveStoreSettings(static::USER_ID_BRAND, $retailerId, $connection);
        $this->assertTrue($success, json_encode(['errors' => $this->ManufacturerRetailer->validationErrors]));

        $retailers = $this->findSavedStoreSettings(static::USER_ID_BRAND, [static::USER_ID_RETAILER, static::USER_ID_BRANCH]);
        $retailers = Hash::combine($retailers, '{n}.ManufacturerRetailer.retailer_id', '{n}');
        $actual = [];
        foreach ($expected as $path => $value) {
            $actual[$path] = Hash::extract($retailers, $path);
        }
        $this->assertEquals($expected, $actual);
    }

    public function providerSaveStoreSettings()
    {
        $master = static::USER_ID_RETAILER;
        $branch = static::USER_ID_BRANCH;
        $connection = [
            'external_retailer_account' => '#1001',
            'external_retailer_email' => '<EMAIL>',
            'external_retailer_company' => 'Company',
            'b2b_tax' => '13.0000',
            'vat_number' => 'IE1234567T',
            'b2b_minimum' => '100.00',
            'credit_limit' => '500.00',
            'credit_term_ids' => ['1001', '1002'],
            'enable_b2b_credit_card' => '0',
            'enable_b2b_external_payment' => '0',
            'enable_split_payment' => '0',
            'sales_rep_ids' => [static::USER_ID_SALES_REP, static::USER_ID_SALES_REP_2],
            'is_commission_tier' => '1',
            'commission_uses_retailer_tax_rate' => '1',
            'distributor_id' => static::USER_ID_DISTRIBUTOR,
            'tag_ids' => ['1', '2'],
            'enable_strict_tag_matching' => '1',
            'warehouse_id' => '1',
            'territory_id' => '',
            'territory_name' => 'NWO Dealers',
            'labels' => ['TestLabel1', 'TestLabel2', 'NewLabel'],
            'enable_consumer_orders' => '0',
            'is_ship_to_store_only' => '1',
            'local_delivery_zip_codes' => 'p7c 4w1, P7B*, P7C 4W1, p7b*',
            'ship_from_store_distance' => '1000',
            'ship_from_store_zip' => 'P7C 4W1, p7b*, p7c 4w1, P7B*',
            'ship_from_store_unprotected_zones_only' => '1',
            'use_retailer_shipping_rates' => '1',
            'enable_uploaded_inventory_reset' => '0',
        ];
        return [
            'master' => [
                'retailer_id' => static::USER_ID_RETAILER,
                'connection' => $connection,
                'expected' => [
                    "{$master}.ManufacturerRetailer.external_retailer_account" => ['#1001'],
                    "{$branch}.ManufacturerRetailer.external_retailer_account" => [],
                    "{$master}.ManufacturerRetailer.external_retailer_email" => ['<EMAIL>'],
                    "{$branch}.ManufacturerRetailer.external_retailer_email" => ['<EMAIL>'],
                    "{$master}.ManufacturerRetailer.external_retailer_company" => ['Company'],
                    "{$branch}.ManufacturerRetailer.external_retailer_company" => ['Company'],
                    "{$master}.ManufacturerRetailer.b2b_tax" => ['13.0000'],
                    "{$branch}.ManufacturerRetailer.b2b_tax" => ['13.0000'],
                    "{$master}.ManufacturerRetailer.vat_number" => ['IE1234567T'],
                    "{$branch}.ManufacturerRetailer.vat_number" => ['IE1234567T'],
                    "{$master}.ManufacturerRetailer.b2b_minimum" => ['100.00'],
                    "{$branch}.ManufacturerRetailer.b2b_minimum" => ['100.00'],
                    "{$master}.ManufacturerRetailer.credit_limit" => ['500.00'],
                    "{$branch}.ManufacturerRetailer.credit_limit" => ['500.00'],
                    "{$master}.RetailerCreditTerm.{n}.credit_term_id" => ['1001', '1002'],
                    "{$branch}.RetailerCreditTerm.{n}.credit_term_id" => ['1001', '1002'],
                    "{$master}.ManufacturerRetailer.enable_b2b_credit_card" => [false],
                    "{$branch}.ManufacturerRetailer.enable_b2b_credit_card" => [false],
                    "{$master}.ManufacturerRetailer.enable_b2b_external_payment" => [false],
                    "{$branch}.ManufacturerRetailer.enable_b2b_external_payment" => [false],
                    "{$master}.ManufacturerRetailer.enable_split_payment" => [false],
                    "{$branch}.ManufacturerRetailer.enable_split_payment" => [false],
                    "{$master}.ManufacturerRetailerSalesRep.{n}.sales_rep_id" => [static::USER_ID_SALES_REP, static::USER_ID_SALES_REP_2],
                    "{$branch}.ManufacturerRetailerSalesRep.{n}.sales_rep_id" => [static::USER_ID_SALES_REP],
                    "{$master}.ManufacturerRetailer.is_commission_tier" => [true],
                    "{$branch}.ManufacturerRetailer.is_commission_tier" => [false],
                    "{$master}.ManufacturerRetailer.commission_uses_retailer_tax_rate" => [true],
                    "{$branch}.ManufacturerRetailer.commission_uses_retailer_tax_rate" => [false],
                    "{$master}.ManufacturerRetailer.distributor_id" => [static::USER_ID_DISTRIBUTOR],
                    "{$branch}.ManufacturerRetailer.distributor_id" => [],
                    "{$master}.ManufacturerRetailerTag.{n}.tag_id" => ['1', '2'],
                    "{$branch}.ManufacturerRetailerTag.{n}.tag_id" => ['1', '2'],
                    "{$master}.ManufacturerRetailer.enable_strict_tag_matching" => [true],
                    "{$branch}.ManufacturerRetailer.enable_strict_tag_matching" => [true],
                    "{$master}.ManufacturerRetailer.warehouse_id" => ['1'],
                    "{$branch}.ManufacturerRetailer.warehouse_id" => ['1'],
                    "{$master}.Territory.name" => ['NWO Dealers'],
                    "{$branch}.Territory.name" => ['NWO Dealers'],
                    "{$master}.Label.{n}.name" => ['NewLabel', 'TestLabel1', 'TestLabel2'],
                    "{$branch}.Label.{n}.name" => ['NewLabel', 'TestLabel1', 'TestLabel2'],
                    "{$master}.ManufacturerRetailer.enable_consumer_orders" => [false],
                    "{$branch}.ManufacturerRetailer.enable_consumer_orders" => [true],
                    "{$master}.ManufacturerRetailer.is_ship_to_store_only" => [true],
                    "{$branch}.ManufacturerRetailer.is_ship_to_store_only" => [false],
                    "{$master}.LocalDeliveryZipCode.{n}.zip_code" => ['P7B%', 'P7C 4W1'],
                    "{$branch}.LocalDeliveryZipCode.{n}.zip_code" => [],
                    "{$master}.ManufacturerRetailer.ship_from_store_distance" => ['1000'],
                    "{$branch}.ManufacturerRetailer.ship_from_store_distance" => ['0'],
                    "{$master}.ShipFromStoreZipCode.{n}.zip_code" => ['P7B%', 'P7C 4W1'],
                    "{$branch}.ShipFromStoreZipCode.{n}.zip_code" => [],
                    "{$master}.ManufacturerRetailer.ship_from_store_unprotected_zones_only" => [true],
                    "{$branch}.ManufacturerRetailer.ship_from_store_unprotected_zones_only" => [false],
                    "{$master}.ManufacturerRetailer.use_retailer_shipping_rates" => [true],
                    "{$branch}.ManufacturerRetailer.use_retailer_shipping_rates" => [true],
                    "{$master}.ManufacturerRetailer.enable_uploaded_inventory_reset" => [false],
                    "{$branch}.ManufacturerRetailer.enable_uploaded_inventory_reset" => [true],
                ],
            ],
            'branch' => [
                'retailer_id' => static::USER_ID_BRANCH,
                'connection' => $connection,
                'expected' => [
                    "{$master}.ManufacturerRetailer.external_retailer_account" => [],
                    "{$branch}.ManufacturerRetailer.external_retailer_account" => ['#1001'],
                    "{$master}.ManufacturerRetailer.external_retailer_email" => [],
                    "{$branch}.ManufacturerRetailer.external_retailer_email" => [],
                    "{$master}.ManufacturerRetailer.external_retailer_company" => [],
                    "{$branch}.ManufacturerRetailer.external_retailer_company" => [],
                    "{$master}.ManufacturerRetailer.b2b_tax" => ['5.5000'],
                    "{$branch}.ManufacturerRetailer.b2b_tax" => ['5.5000'],
                    "{$master}.ManufacturerRetailer.vat_number" => [],
                    "{$branch}.ManufacturerRetailer.vat_number" => [],
                    "{$master}.ManufacturerRetailer.b2b_minimum" => ['0.00'],
                    "{$branch}.ManufacturerRetailer.b2b_minimum" => ['0.00'],
                    "{$master}.ManufacturerRetailer.credit_limit" => ['10000.00'],
                    "{$branch}.ManufacturerRetailer.credit_limit" => ['10000.00'],
                    "{$master}.RetailerCreditTerm.{n}.credit_term_id" => ['1001', '1002', '1003'],
                    "{$branch}.RetailerCreditTerm.{n}.credit_term_id" => ['1001', '1002', '1003'],
                    "{$master}.ManufacturerRetailer.enable_b2b_credit_card" => [true],
                    "{$branch}.ManufacturerRetailer.enable_b2b_credit_card" => [true],
                    "{$master}.ManufacturerRetailer.enable_b2b_external_payment" => [true],
                    "{$branch}.ManufacturerRetailer.enable_b2b_external_payment" => [true],
                    "{$master}.ManufacturerRetailer.enable_split_payment" => [true],
                    "{$branch}.ManufacturerRetailer.enable_split_payment" => [true],
                    "{$master}.ManufacturerRetailerSalesRep.{n}.sales_rep_id" => [static::USER_ID_SALES_REP],
                    "{$branch}.ManufacturerRetailerSalesRep.{n}.sales_rep_id" => [static::USER_ID_SALES_REP, static::USER_ID_SALES_REP_2],
                    "{$master}.ManufacturerRetailer.is_commission_tier" => [false],
                    "{$branch}.ManufacturerRetailer.is_commission_tier" => [true],
                    "{$master}.ManufacturerRetailer.commission_uses_retailer_tax_rate" => [false],
                    "{$branch}.ManufacturerRetailer.commission_uses_retailer_tax_rate" => [true],
                    "{$master}.ManufacturerRetailer.distributor_id" => [],
                    "{$branch}.ManufacturerRetailer.distributor_id" => [static::USER_ID_DISTRIBUTOR],
                    "{$master}.ManufacturerRetailerTag.{n}.tag_id" => [],
                    "{$branch}.ManufacturerRetailerTag.{n}.tag_id" => [],
                    "{$master}.ManufacturerRetailer.enable_strict_tag_matching" => [false],
                    "{$branch}.ManufacturerRetailer.enable_strict_tag_matching" => [false],
                    "{$master}.ManufacturerRetailer.warehouse_id" => [],
                    "{$branch}.ManufacturerRetailer.warehouse_id" => [],
                    "{$master}.Territory.name" => [],
                    "{$branch}.Territory.name" => [],
                    "{$master}.Label.{n}.name" => ['TestLabel1', 'TestLabel2'],
                    "{$branch}.Label.{n}.name" => ['TestLabel1', 'TestLabel2'],
                    "{$master}.ManufacturerRetailer.enable_consumer_orders" => [true],
                    "{$branch}.ManufacturerRetailer.enable_consumer_orders" => [false],
                    "{$master}.ManufacturerRetailer.is_ship_to_store_only" => [false],
                    "{$branch}.ManufacturerRetailer.is_ship_to_store_only" => [true],
                    "{$master}.LocalDeliveryZipCode.{n}.zip_code" => [],
                    "{$branch}.LocalDeliveryZipCode.{n}.zip_code" => ['P7B%', 'P7C 4W1'],
                    "{$master}.ManufacturerRetailer.ship_from_store_distance" => ['0'],
                    "{$branch}.ManufacturerRetailer.ship_from_store_distance" => ['1000'],
                    "{$master}.ShipFromStoreZipCode.{n}.zip_code" => [],
                    "{$branch}.ShipFromStoreZipCode.{n}.zip_code" => ['P7B%', 'P7C 4W1'],
                    "{$master}.ManufacturerRetailer.ship_from_store_unprotected_zones_only" => [false],
                    "{$branch}.ManufacturerRetailer.ship_from_store_unprotected_zones_only" => [true],
                    "{$master}.ManufacturerRetailer.use_retailer_shipping_rates" => [false],
                    "{$branch}.ManufacturerRetailer.use_retailer_shipping_rates" => [false],
                    "{$master}.ManufacturerRetailer.enable_uploaded_inventory_reset" => [true],
                    "{$branch}.ManufacturerRetailer.enable_uploaded_inventory_reset" => [false],
                ],
            ],
        ];
    }

    public function testCreateSettingsForNewStore()
    {
        $newId1 = static::ID_NEW;
        $newId2 = (string)((int)$newId1 + 1);
        $brandIds = [static::USER_ID_BRAND, '19'];
        $retailerId = static::USER_ID_RETAILER;
        $branchId = static::USER_ID_BRANCH;

        $this->_seedExampleTags(['ManufacturerRetailer.retailer_id' => $retailerId]);
        $this->_seedExampleZipCodeFilters(['user_id' => static::USER_ID_BRAND, 'retailer_id' => $retailerId], ['P7C 4W1', 'P7B%']);
        $this->ManufacturerRetailer->deleteAllJoinless(['ManufacturerRetailer.retailer_id' => $branchId], false);

        $success = $this->ManufacturerRetailer->createSettingsForNewStore($branchId, $retailerId);
        $this->assertTrue($success, json_encode(['errors' => $this->ManufacturerRetailer->validationErrors]));

        $actual = $this->findSavedStoreSettings($brandIds, $branchId);
        $expected = [
            [
                'ManufacturerRetailer' => [
                    'id' => $newId1,
                    'user_id' => '8',
                    'retailer_id' => '17',
                    'status' => '1',
                    'pricingtierid' => '1',
                    'local_delivery_radius' => '1000',
                    'dealer_protect_radius' => '0',
                    'external_retailer_account' => null,
                    'external_retailer_email' => null,
                    'external_retailer_company' => null,
                    'b2b_tax' => '5.5000',
                    'vat_number' => null,
                    'b2b_minimum' => '0.00',
                    'credit_limit' => '10000.00',
                    'enable_b2b_credit_card' => true,
                    'enable_b2b_external_payment' => true,
                    'enable_split_payment' => true,
                    'is_commission_tier' => false,
                    'commission_uses_retailer_tax_rate' => false,
                    'distributor_id' => null,
                    'enable_strict_tag_matching' => false,
                    'warehouse_id' => null,
                    'enable_consumer_orders' => true,
                    'is_ship_to_store_only' => false,
                    'ship_from_store_distance' => '0',
                    'ship_from_store_unprotected_zones_only' => false,
                    'use_retailer_shipping_rates' => false,
                    'enable_uploaded_inventory_reset' => true,
                ],
                'B2bShipToAddress' => [],
                'Territory' => [
                    'id' => null,
                    'name' => null,
                ],
                'LocalDeliveryZipCode' => [
                    [
                        'manufacturer_retailer_id' => $newId1,
                        'zip_code' => 'P7B%',
                    ],
                    [
                        'manufacturer_retailer_id' => $newId1,
                        'zip_code' => 'P7C 4W1',
                    ],
                ],
                'ShipFromStoreZipCode' => [
                    [
                        'manufacturer_retailer_id' => $newId1,
                        'zip_code' => 'P7B%',
                    ],
                    [
                        'manufacturer_retailer_id' => $newId1,
                        'zip_code' => 'P7C 4W1',
                    ],
                ],
                'ManufacturerRetailerSalesRep' => [
                    0 => [
                        'manufacturer_retailer_id' => $newId1,
                        'sales_rep_id' => static::USER_ID_SALES_REP,
                    ],
                ],
                'ManufacturerRetailerTag' => [
                    0 => [
                        'manufacturer_retailer_id' => $newId1,
                        'tag_id' => '1',
                    ],
                    1 => [
                        'manufacturer_retailer_id' => $newId1,
                        'tag_id' => '2',
                    ],
                ],
                'RetailerCreditTerm' => [
                    0 => [
                        'manufacturer_retailer_id' => $newId1,
                        'credit_term_id' => '1001'
                    ],
                    1 => [
                        'manufacturer_retailer_id' => $newId1,
                        'credit_term_id' => '1002'
                    ],
                    2 => [
                        'manufacturer_retailer_id' => $newId1,
                        'credit_term_id' => '1003'
                    ],
                ],
                'Label' => [
                    [
                        'id' => '1',
                        'name' => 'TestLabel1'
                    ],
                    [
                        'id' => '2',
                        'name' => 'TestLabel2'
                    ]
                ]
            ],
            [
                'ManufacturerRetailer' => [
                    'id' => $newId2,
                    'user_id' => '19',
                    'retailer_id' => '17',
                    'status' => '1',
                    'pricingtierid' => '4',
                    'local_delivery_radius' => '0',
                    'dealer_protect_radius' => '0',
                    'external_retailer_account' => null,
                    'external_retailer_email' => null,
                    'external_retailer_company' => null,
                    'b2b_tax' => '0.0000',
                    'vat_number' => null,
                    'b2b_minimum' => '0.00',
                    'credit_limit' => '10000.00',
                    'enable_b2b_credit_card' => true,
                    'enable_b2b_external_payment' => false,
                    'enable_split_payment' => true,
                    'is_commission_tier' => false,
                    'commission_uses_retailer_tax_rate' => false,
                    'distributor_id' => null,
                    'enable_strict_tag_matching' => false,
                    'warehouse_id' => null,
                    'enable_consumer_orders' => true,
                    'is_ship_to_store_only' => false,
                    'ship_from_store_distance' => '0',
                    'ship_from_store_unprotected_zones_only' => false,
                    'use_retailer_shipping_rates' => false,
                    'enable_uploaded_inventory_reset' => true,
                ],
                'B2bShipToAddress' => [],
                'Territory' => [
                    'id' => null,
                    'name' => null,
                ],
                'LocalDeliveryZipCode' => [],
                'ShipFromStoreZipCode' => [],
                'ManufacturerRetailerSalesRep' => [],
                'ManufacturerRetailerTag' => [],
                'RetailerCreditTerm' => [],
                'Label' => [],

            ],
        ];

        // Brittle: Adding new records to ManufacturerRetailerSeeder will require a revision to increase ID_NEW
        $this->assertEquals($expected, $actual);
    }

    public function testFindDefaultWarehouseId()
    {
        $this->markTestIncomplete('testFindDefaultWarehouseId not implemented.');
        $this->ManufacturerRetailer->findDefaultWarehouseId(static::USER_ID_BRAND, static::USER_ID_RETAILER);
    }

    public function testFindForBrandLocalDeliveryRadius()
    {
        $actual = $this->ManufacturerRetailer->findForBrandLocalDeliveryRadius(static::USER_ID_BRAND, static::USER_ID_RETAILER);
        $expected = [
            'ManufacturerRetailer' => [
                'id' => '3',
                'local_delivery_radius' => '1000',
            ],
            'User' => [
                'id' => static::USER_ID_BRAND,
                'local_delivery' => '1',
                'local_delivery_shipping' => '3.33',
            ],
        ];
        $this->assertEquals($expected, $actual);
    }

    /**
     * @dataProvider providerFindOrderSalesRepFields
     * @param int|null|string $distributorId
     * @param array $expected
     */
    public function testFindOrderSalesRepFields($distributorId, array $expected)
    {
        // Seed distributor
        $this->ManufacturerRetailer->updateAllJoinless(
            ['ManufacturerRetailer.distributor_id' => $this->ManufacturerRetailer->value($distributorId)],
            ['ManufacturerRetailer.user_id' => static::USER_ID_BRAND, 'ManufacturerRetailer.retailer_id' => static::USER_ID_RETAILER]
        );

        $actual = $this->ManufacturerRetailer->findOrderSalesRepFields(static::USER_ID_BRAND, static::USER_ID_RETAILER);

        $this->assertEquals($expected, $actual);
    }

    public function providerFindOrderSalesRepFields()
    {
        return [
            'no_distributor' => [
                'distributorId' => null,
                'expected' => [
                    'Order' => [
                        'distributor_id' => null,
                        // deprecated fields
                        'sales_rep_id' => static::USER_ID_SALES_REP,
                        'has_distributor' => false,
                    ],
                    'OrderSalesRep' => [
                        ['sales_rep_id' => static::USER_ID_SALES_REP],
                    ],
                ],
            ],
            'has_distributor' => [
                'distributorId' => static::USER_ID_DISTRIBUTOR,
                'expected' => [
                    'Order' => [
                        'distributor_id' => static::USER_ID_DISTRIBUTOR,
                        // deprecated fields
                        'sales_rep_id' => static::USER_ID_DISTRIBUTOR,
                        'has_distributor' => true,
                    ],
                    'OrderSalesRep' => [
                        ['sales_rep_id' => static::USER_ID_SALES_REP],
                    ],
                ],
            ],
        ];
    }

    /**
     * @dataProvider providerFindAllSalesRepUserRetailerNames
     * @param int $salesRepId
     * @param array $order
     * @param array $extraConditions
     * @param array $expected
     */
    public function testCountSalesRepUserRetailerNames($salesRepId, $order, $extraConditions, $expected)
    {
        // Seed distributor case
        $this->ManufacturerRetailer->updateAllJoinless(['ManufacturerRetailer.distributor_id' => static::USER_ID_DISTRIBUTOR], [
            'ManufacturerRetailer.user_id' => '12',
            'ManufacturerRetailer.retailer_id' => ['15', '27'],
        ]);

        $this->assertEquals(count($expected), $this->ManufacturerRetailer->countSalesRepUserRetailerNames($salesRepId, $extraConditions));
    }

    /**
     * @dataProvider providerFindAllSalesRepUserRetailerNames
     * @param int $salesRepId
     * @param array $order
     * @param array $extraConditions
     * @param array $expected
     */
    public function testFindAllSalesRepUserRetailerNames($salesRepId, $order, $extraConditions, $expected)
    {
        // Seed distributor case
        $this->ManufacturerRetailer->updateAllJoinless(['ManufacturerRetailer.distributor_id' => static::USER_ID_DISTRIBUTOR], [
            'ManufacturerRetailer.user_id' => '12',
            'ManufacturerRetailer.retailer_id' => ['15', '27'],
        ]);

        $this->assertEquals($expected, $this->ManufacturerRetailer->findAllSalesRepUserRetailerNames($salesRepId, $order, $extraConditions));
    }

    public function providerFindAllSalesRepUserRetailerNames()
    {
        return [
            'sales_rep' => [
                'salesRepId' => static::USER_ID_SALES_REP,
                'order' => ['Retailer.company_name' => 'DESC'],
                'extraConditions' => [],
                'expected' => [
                    [
                        'User' => ['id' => '12', 'company_name' => 'Sirisha Test Brand'],
                        'Retailer' => ['id' => '15', 'company_name' => 'Sirisha Test Retailer'],
                    ],
                    [
                        'User' => ['id' => '12', 'company_name' => 'Sirisha Test Brand'],
                        'Retailer' => ['id' => '27', 'company_name' => 'Sirisha Test Branch'],
                    ],
                    [
                        'User' => ['id' => '8', 'company_name' => 'My Local Brand'],
                        'Retailer' => ['id' => '17', 'company_name' => 'Local Shop Branch'],
                    ],
                    [
                        'User' => ['id' => '8', 'company_name' => 'My Local Brand'],
                        'Retailer' => ['id' => '7', 'company_name' => 'Local Shop'],
                    ],
                ],
            ],
            'distributor' => [
                'salesRepId' => static::USER_ID_DISTRIBUTOR,
                'order' => ['Retailer.company_name' => 'ASC'],
                'extraConditions' => [],
                'expected' => [
                    [
                        'User' => ['id' => '12', 'company_name' => 'Sirisha Test Brand'],
                        'Retailer' => ['id' => '27', 'company_name' => 'Sirisha Test Branch'],
                    ],
                    [
                        'User' => ['id' => '12', 'company_name' => 'Sirisha Test Brand'],
                        'Retailer' => ['id' => '15', 'company_name' => 'Sirisha Test Retailer'],
                    ],
                ],
            ],
            'filtered' => [
                'salesRepId' => static::USER_ID_SALES_REP,
                'order' => ['Retailer.company_name' => 'ASC'],
                'extraConditions' => [
                    'ManufacturerRetailer.user_id' => '8',
                    'Retailer.id' => ['7', '17'],
                ],
                'expected' => [
                    [
                        'User' => ['id' => '8', 'company_name' => 'My Local Brand'],
                        'Retailer' => ['id' => '7', 'company_name' => 'Local Shop'],
                    ],
                    [
                        'User' => ['id' => '8', 'company_name' => 'My Local Brand'],
                        'Retailer' => ['id' => '17', 'company_name' => 'Local Shop Branch'],
                    ],
                ],
            ],
        ];
    }

    public function testHasInstallEnabled()
    {
        $this->markTestIncomplete('testHasInstallEnabled not implemented.');
        $this->ManufacturerRetailer->hasInstallEnabled(static::USER_ID_BRAND, static::USER_ID_RETAILER);
    }

    public function testHasProtectedRetailer()
    {
        $this->markTestIncomplete('testHasProtectedRetailer not implemented.');
        $this->ManufacturerRetailer->hasProtectedRetailer([], []);
    }

    public function testHasBrandAssociation()
    {
        $this->assertTrue($this->ManufacturerRetailer->hasBrandAssociation(static::USER_ID_RETAILER), 'Active retailer');
        $this->assertFalse($this->ManufacturerRetailer->hasBrandAssociation(4), 'Newly registered retailer');
    }

    public function testConnectToRetailer()
    {
        $this->markTestIncomplete('testConnectToRetailer not implemented.');
        $this->ManufacturerRetailer->connectToRetailer(static::USER_ID_BRAND, static::USER_ID_RETAILER);
    }

    public function testConnectToBrand()
    {
        $this->markTestIncomplete('testConnectToBrand not implemented.');
        $this->ManufacturerRetailer->connectToBrand(static::USER_ID_RETAILER, static::USER_ID_BRAND);
    }

    public function testDisconnectFromRetailer()
    {
        $this->markTestIncomplete('testDisconnectFromRetailer not implemented.');
        $this->ManufacturerRetailer->disconnectFromRetailer(static::USER_ID_BRAND, static::USER_ID_RETAILER);
    }

    public function testDisconnectFromBrand()
    {
        $this->markTestIncomplete('testDisconnectFromBrand not implemented.');
        $this->ManufacturerRetailer->disconnectFromBrand(static::USER_ID_RETAILER, static::USER_ID_BRAND);
    }

    public function testRemove()
    {
        $this->markTestIncomplete('testRemove not implemented.');
        $this->ManufacturerRetailer->remove(static::USER_ID_BRAND, static::USER_ID_RETAILER);
    }

    public function testUndoRemove()
    {
        $this->markTestIncomplete('testUndoRemove not implemented.');
        $this->ManufacturerRetailer->undoRemove(static::USER_ID_BRAND, static::USER_ID_RETAILER);
    }

    public function testUpdateAllStoreConnections()
    {
        $this->markTestIncomplete('testUpdateAllStoreConnections not implemented.');
        $this->ManufacturerRetailer->updateAllStoreConnections(static::USER_ID_BRAND, static::USER_ID_RETAILER, []);
    }

    private function findSavedStoreSettings($brandIds, $retailerIds)
    {
        $this->ManufacturerRetailer->addAssociations([
            'belongsTo' => ['Territory'],
            'hasMany' => [
                'B2bShipToAddress',
                'LocalDeliveryZipCode',
                'ShipFromStoreZipCode',
                'ManufacturerRetailerSalesRep',
                'ManufacturerRetailerTag',
                'RetailerCreditTerm',
            ],
            'belongsToMany' => [
                'Label' => ['with' => 'ManufacturerRetailerLabel', 'unique' => 'keepExisting'],
            ],
        ]);
        $records = $this->ManufacturerRetailer->find('all', [
            'contain' => [
                'Territory' => [
                    'fields' => ['id', 'name'],
                ],
                'B2bShipToAddress' => [
                    'fields' => ['manufacturer_retailer_id', 'address1', 'address2', 'city', 'country_id', 'state_id', 'zipcode', 'telephone'],
                ],
                'LocalDeliveryZipCode' => [
                    'fields' => ['manufacturer_retailer_id', 'zip_code'],
                    'order' => ['LocalDeliveryZipCode.zip_code' => 'ASC'],
                ],
                'ShipFromStoreZipCode' => [
                    'fields' => ['manufacturer_retailer_id', 'zip_code'],
                    'order' => ['ShipFromStoreZipCode.zip_code' => 'ASC'],
                ],
                'ManufacturerRetailerSalesRep' => [
                    'fields' => ['manufacturer_retailer_id', 'sales_rep_id'],
                    'order' => ['ManufacturerRetailerSalesRep.sales_rep_id' => 'ASC'],
                ],
                'ManufacturerRetailerTag' => [
                    'fields' => ['manufacturer_retailer_id', 'tag_id'],
                    'order' => ['ManufacturerRetailerTag.tag_id' => 'ASC'],
                ],
                'RetailerCreditTerm' => [
                    'fields' => ['manufacturer_retailer_id', 'credit_term_id'],
                    'order' => ['RetailerCreditTerm.credit_term_id' => 'ASC'],
                ],
                'Label' => [
                    'with' => ['ManufacturerRetailerLabel' => []],
                    'fields' => ['id', 'name'],
                    'order' => ['Label.name' => 'ASC'],
                ],
            ],
            'conditions' => [
                'ManufacturerRetailer.user_id' => $brandIds,
                'ManufacturerRetailer.retailer_id' => $retailerIds,
            ],
            'fields' => [
                'ManufacturerRetailer.user_id',
                'ManufacturerRetailer.retailer_id',
                // Index fields
                'ManufacturerRetailer.status',
                'ManufacturerRetailer.pricingtierid',
                //'ManufacturerRetailerSalesRep.{n}.sales_rep_id',
                'ManufacturerRetailer.local_delivery_radius',
                'ManufacturerRetailer.dealer_protect_radius',
                // Edit fields
                'ManufacturerRetailer.external_retailer_account',
                'ManufacturerRetailer.external_retailer_email',
                'ManufacturerRetailer.external_retailer_company',
                'ManufacturerRetailer.b2b_tax',
                'ManufacturerRetailer.vat_number',
                'ManufacturerRetailer.b2b_minimum',
                'ManufacturerRetailer.credit_limit',
                //'RetailerCreditTerm.{n}.credit_term_id',
                'ManufacturerRetailer.enable_b2b_credit_card',
                'ManufacturerRetailer.enable_b2b_external_payment',
                'ManufacturerRetailer.enable_split_payment',
                //'ManufacturerRetailerSalesRep.{n}.sales_rep_id',
                'ManufacturerRetailer.is_commission_tier',
                'ManufacturerRetailer.commission_uses_retailer_tax_rate',
                'ManufacturerRetailer.distributor_id',
                //'ManufacturerRetailerTag.{n}.tag_id',
                'ManufacturerRetailer.enable_strict_tag_matching',
                'ManufacturerRetailer.warehouse_id',
                'Territory.name',
                //'Label.{n}.name',
                'ManufacturerRetailer.enable_consumer_orders',
                'ManufacturerRetailer.is_ship_to_store_only',
                //'LocalDeliveryZipCode.{n}.zip_code',
                'ManufacturerRetailer.ship_from_store_distance',
                //'ShipFromStoreZipCode.{n}.zip_code',
                'ManufacturerRetailer.ship_from_store_unprotected_zones_only',
                'ManufacturerRetailer.use_retailer_shipping_rates',
                'ManufacturerRetailer.enable_uploaded_inventory_reset',
                // Import fields
                //'B2bShipToAddress.{n}',
            ],
            'order' => [
                'ManufacturerRetailer.user_id' => 'ASC',
                'ManufacturerRetailer.retailer_id' => 'ASC',
            ],
        ]);
        $this->ManufacturerRetailer->unbindModel([
            'belongsTo' => ['Territory'],
            'hasMany' => [
                'B2bShipToAddress',
                'LocalDeliveryZipCode',
                'ShipFromStoreZipCode',
                'ManufacturerRetailerSalesRep',
                'ManufacturerRetailerTag',
                'RetailerCreditTerm',
            ],
            'hasAndBelongsToMany' => ['Label'],
        ], false);

        return $records;
    }

    private function _seedExampleTags(array $conditions)
    {
        /** @var ManufacturerRetailerTag $ManufacturerRetailerTag */
        $ManufacturerRetailerTag = ClassRegistry::init('ManufacturerRetailerTag');

        $ids = $this->ManufacturerRetailer->find('list', [
            'recursive' => -1,
            'conditions' => $conditions,
            'fields' => ['ManufacturerRetailer.id', 'ManufacturerRetailer.id'],
            'order' => false,
        ]);

        $ManufacturerRetailerTag->saveMany(array_reduce(
            $ids,
            function($records, $id) {
                return array_merge($records, [
                    ['manufacturer_retailer_id' => $id, 'tag_id' => '1'],
                    ['manufacturer_retailer_id' => $id, 'tag_id' => '2'],
                ]);
            },
            []
        ));
    }

    private function _seedExampleZipCodeFilters(array $conditions, array $zipCodeFilters)
    {
        /** @var LocalDeliveryZipCode $LocalDeliveryZipCode */
        $LocalDeliveryZipCode = ClassRegistry::init('LocalDeliveryZipCode');
        /** @var ShipFromStoreZipCode $ShipFromStoreZipCode */
        $ShipFromStoreZipCode = ClassRegistry::init('ShipFromStoreZipCode');

        $ids = $this->ManufacturerRetailer->find('list', [
            'recursive' => -1,
            'conditions' => $conditions,
            'fields' => ['ManufacturerRetailer.id', 'ManufacturerRetailer.id'],
            'order' => false,
        ]);

        $zipCodeRecords = array_reduce(
            $ids,
            function($records, $id) use ($zipCodeFilters) {
                return array_merge($records, array_map(
                    fn($zipCode) => ['manufacturer_retailer_id' => $id, 'zip_code' => $zipCode],
                    $zipCodeFilters
                ));
            },
            []
        );
        $LocalDeliveryZipCode->truncate();
        $LocalDeliveryZipCode->saveMany($zipCodeRecords);
        $ShipFromStoreZipCode->truncate();
        $ShipFromStoreZipCode->saveMany($zipCodeRecords);
    }
}
