<?php
App::uses('AppTestCase', 'TestSuite');
App::uses('Order', 'Model');

/**
 * Order::findAllWithReservations Test Case.
 *
 * Run with the following console command:
 *
 * ./app/Console/cake test app Model/Order/OrderFindAllWithReservations
 *
 * @property Order $Order
 * @property WarehouseProductReservation $WarehouseProductReservation
 */
class OrderFindAllWithReservationsTest extends AppTestCase
{
    public $fixtures = [
        'app.brand_staff_permission',
        'app.order',
        'app.order_comment',
        'app.order_customer_message',
        'app.order_product',
        'app.product',
        'app.product_state_fee',
        'app.staff_permission',
        'app.user',
        'app.warehouse',
        'app.warehouse_product',
        'app.warehouse_product_reservation',
    ];

    public function setUp()
    {
        parent::setUp();
        $this->Order = ClassRegistry::init('Order');
        $this->WarehouseProductReservation = ClassRegistry::init('WarehouseProductReservation');

        /** @var Btask|PHPUnit_Framework_MockObject_MockObject $mock */
        $mock = $this->getMockForModel('Btask', ['queueInventoryReservation']);
        $mock->expects($this->any())->method('queueInventoryReservation')->willReturn(true);
    }

    public function tearDown()
    {
        unset(
            $this->WarehouseProductReservation,
            $this->Order
        );
        parent::tearDown();
    }

    /**
     * @dataProvider providerFindAllWithInventoryReservations
     * @param int $warehouseId
     * @param array $expectedReservations
     */
    public function testFindAllWithInventoryReservations($warehouseId, $expectedReservations)
    {
        $items = [
            [
                'product_id' => '21',
                'warehouse_id' => '1',
                'inventory_transfer_id' => null,
                'quantity' => '2',
            ],
            [
                'product_id' => '21',
                'warehouse_id' => '2',
                'inventory_transfer_id' => null,
                'quantity' => '3',
            ],
        ];
        $this->WarehouseProductReservation->reserveLineItemSet('1', $items);
        $this->WarehouseProductReservation->reserveLineItemSet('2', $items);

        $actual = $this->Order->findAllWithReservations('21', $warehouseId);

        $sample = current($actual);
        $this->assertArrayHasKey('Order', $sample);
        $this->assertArrayHasKey('Retailer', $sample);
        $this->assertArrayHasKey('OrderReservation', $sample);

        $this->assertEquals($expectedReservations, array_column($actual, 'OrderReservation'));
    }

    public function providerFindAllWithInventoryReservations()
    {
        return [
            [
                'warehouseId' => '1',
                'expectedReservations' => [
                    [
                        'order_id' => '1',
                        'quantity' => '5',
                        'reserved_quantity' => '2',
                        'count' => '1',
                    ],
                    [
                        'order_id' => '2',
                        'quantity' => '5',
                        'reserved_quantity' => '2',
                        'count' => '1',
                    ],
                ],
            ],
            [
                'warehouseId' => '2',
                'expectedReservations' => [
                    [
                        'order_id' => '1',
                        'quantity' => '169',
                        'reserved_quantity' => '3',
                        'count' => '1',
                    ],
                    [
                        'order_id' => '2',
                        'quantity' => '169',
                        'reserved_quantity' => '3',
                        'count' => '1',
                    ],
                ],
            ],
            [
                'warehouseId' => null,
                'expectedReservations' => [
                    [
                        'order_id' => '1',
                        'quantity' => '174',
                        'reserved_quantity' => '5',
                        'count' => '2',
                    ],
                    [
                        'order_id' => '2',
                        'quantity' => '174',
                        'reserved_quantity' => '5',
                        'count' => '2',
                    ],
                ],
            ],
        ];
    }
}
