<?php
App::uses('AppTestCase', 'TestSuite');
App::uses('Order', 'Model');

/**
 * Order::findForPopup Test Case.
 *
 * Run with the following console command:
 *
 * ./app/Console/cake test app Model/Order/OrderFindForPopup
 *
 * @property Order $Order
 *
 * @property string $dateToday
 * @property string $dateTomorrow
 */
class OrderFindForPopupTest extends AppTestCase
{
    public $fixtures = [
        'app.brand_staff_permission',
        'app.collection',
        'app.collections_product',
        'app.country',
        'app.courier',
        'app.credit_term',
        'app.dealer_order',
        'app.dealer_order_product',
        'app.dealer_order_refund',
        'app.dealer_order_refund_product',
        'app.discount',
        'app.discount_rule',
        'app.fulfillment',
        'app.fulfillment_product',
        'app.i18n',
        'app.inventory_transfer',
        'app.inventory_transfer_product',
        'app.inventory_transfer_product_reservation',
        'app.manufacturer_retailer',
        'app.order',
        'app.order_address',
        'app.order_comment',
        'app.order_customer_message',
        'app.order_payout',
        'app.order_product',
        'app.order_refund',
        'app.order_refund_product',
        'app.order_sales_rep',
        'app.order_warranty_image',
        'app.product',
        'app.product_non_applicable_order_type',
        'app.product_state_fee',
        'app.product_tag',
        'app.product_title',
        'app.product_variant_option',
        'app.staff_permission',
        'app.state',
        'app.tag',
        'app.user',
        'app.variant_option',
        'app.warehouse',
        'app.warehouse_product',
        'app.warehouse_product_reservation',
    ];

    public function __construct($name = null, array $data = [], $dataName = '')
    {
        parent::__construct($name, $data, $dataName);

        $this->dateToday = static::format_datetime('today', 'Y-m-d');
        $this->dateTomorrow = static::format_datetime('tomorrow', 'Y-m-d');
    }

    public function setUp()
    {
        parent::setUp();

        $this->Order = ClassRegistry::init('Order');

        $this->seedPartialOrderFulfillment('4');
        $this->seedRelativeInventoryTransferDates();
    }

    public function tearDown()
    {
        unset($this->Order);
        parent::tearDown();
    }

    /**
     * @dataProvider providerFindForPopup
     * @param int $orderId
     * @param array $expected
     */
    public function testFindForPopup(int $orderId, array $expected)
    {
        $actual = $this->Order->findForPopup($orderId);
        //fwrite(STDERR, print_r(json_encode($actual), TRUE));


        $this->assertEquals($expected, $actual);
    }

    public function providerFindForPopup()
    {
        $User = [
            'id' => '8',
            'uuid' => '5666ffac-5964-4f7a-9048-1cc291c2de43',
            'email_address' => '<EMAIL>',
            'password' => '3be70f15222270d5c843692c1247b57b',
            'company_name' => 'My Local Brand',
            'company_description' => '',
            'company_code' => '',
            'status' => 'Active',
            'Branch' => '0',
            'user_type' => 'Manufacturer',
            'staff_role' => null,
            'minOrderAmount' => '0',
            'avatar' => 'shopify0.png',
            'shipping_infographic' => 'https://shipearlyapp.localhost/files/users/Linus_ShipEarly_ShippingOptionsPopUp650x500-**********.jpg',
            'flatshipping' => '0',
            'site_type' => 'Shopify',
            'shop_url' => 'aron-shipearly-2.myshopify.com',
            'shop_home_url' => '',
            'shop_cart_url' => '',
            'shop_app_id' => null,
            'gtm_container_id' => '',
            'enable_ga_tracking' => true,
            'google_conversion_label' => '',
            'fbpixelId' => '904599178183609',
            'currency_code' => 'USD',
            'api_key' => 'e8d27b84d0d3e8549cd2faa84309c75d',
            'secret_key' => '723213fc099cd21a806692052d3c8949',
            'webhook_shared_secret' => '',
            'inventory_apiuser' => '',
            'inventory_password' => '',
            'Inventory_Emp_ID' => null,
            'Inventory_Reg_ID' => null,
            'Inventory_Store_ID' => null,
            'defaultTax' => null,
            'enable_affirm_financing' => false,
            'tax_id_number' => null,
            'enable_install' => false,
            'install_rate_option' => '0',
            'install_flat_rate' => '0.00',
            'install_hourly_rate' => '0.00',
            'otherInventory' => null,
            'last_login' => null,
            'created' => '2015-12-08 17:05:00',
            'modified' => '2017-08-14 20:09:33',
            'timezone' => null,
            'address' => '123 Localhost St._,_',
            'store_timing' => '',
            'country_id' => '39',
            'state_id' => '611',
            'city' => 'Thunder Bay',
            'zipcode' => 'P7C 4W1',
            'latitude' => '48.407966',
            'longitude' => '-89.2565708',
            'inventory_type' => null,
            'permission' => '',
            'send_store_emails_to_master_only' => false,
            'do_not_list' => false,
            'instore' => true,
            'shipment' => true,
            'shipment_type' => '',
            'shipment_option' => '',
            'free_shipping' => null,
            'shipfromstore_instock_only' => false,
            'ship_from_store_double_ship' => false,
            'shiptostore_free_shipping' => '1000000000',
            'shiptostore_tax' => '0',
            'local_delivery' => '1',
            'local_delivery_shipping' => '3.33',
            'local_delivery_shipping_title' => '',
            'local_delivery_shipping_option' => '0',
            'local_delivery_percent_source' => 'product_price',
            'local_delivery_no_retailer_listing' => false,
            'enable_wholesale_topup' => false,
            'wholesale_topup_infographic' => null,
            'subscription_plan' => 'core',
            'coupon_code' => null,
            'store_associate_pin' => null,
            'setup_status' => true,
            'revenue_model' => null,
            'retailer_default_amount' => '0.99',
            'retailer_revenue_maximum' => null,
            'store_associate_default_amount' => '0.00',
            'in-store_radius' => '50',
            'ship_from_store_radius' => '250',
            'admin_sell_direct' => '1',
            'sell_direct_percentage' => null,
            'sell_direct' => '1',
            'sell_direct_authorize' => false,
            'enable_b2b_cart' => true,
            'b2b_hero_image' => 'https://shipearlyapp.localhost/files/users/main-banner0.jpg',
            'b2b_product_display_list' => false,
            'enable_online_to_offline' => true,
            'enable_velofix' => true,
            'brand_revenue_model' => '2',
            'brand_direct_default_amount' => '0.99',
            'brand_revenue_maximum' => null,
            'splitpayment_percentage' => '2.00',
            'enable_stripe_payment_request' => false,
            'require_terms_of_service' => false,
            'enable_product_discovery' => false,
            'brand_abandon_cart' => true,
            'brand_abandon_cart_message' => 'Abandoned cart message.',
            'brand_abandon_cart_subject' => 'Abandoned cart subject',
            'brand_accent_color' => null,
            'vend_access_token_expires' => null,
            'vend_refresh_access_token' => null,
            'ship_to_door_title' => '',
            'ship_to_door_message' => '',
            'ship_to_door_backorder_message' => '',
            'ship_from_store_title' => '',
            'instore_pickup_title' => '',
            'instore_pickup_message' => '',
            'ship_to_store_message' => '',
            'ship_to_store_backorder_message' => '',
            'ship_to_store_noretailer_message' => '',
            'local_install_title' => null,
            'local_install_in_stock_message' => null,
            'local_install_no_stock_message' => null,
            'local_delivery_title' => '',
            'local_delivery_in_stock_message' => '',
            'local_delivery_no_stock_message' => '',
            'local_delivery_backorder_message' => '',
            'local_delivery_noretailer_message' => '',
            'address1' => '123 Localhost St.',
            'address2' => '',
        ];

        $ProductById = [
            21 => [
                'id' => '21',
                'uuid' => '56e33952-2348-4c1e-9a34-109b91c2de43',
                'user_id' => '8',
                'source_product_id' => '5572298951',
                'productID' => '17532934023',
                'inventory_item_id' => '7412906375',
                'product_type' => 'Bikes',
                'product_title' => 'Super Bike - Red',
                'product_sku' => 'BICYCLE-1',
                'product_upc' => '9090909090909',
                'taxcode' => '',
                'product_price' => '1200.00',
                'compare_at_price' => null,
                'sales_commission' => '0.00',
                'currency' => '',
                'brand_inventory' => '174',
                'brand_restock_date' => null,
                'non_stocking' => false,
                'enable_oversell' => false,
                'enable_b2b_oversell' => false,
                'weight' => '1334.98',
                'weight_unit' => 'g',
                'installation_hours' => '0',
                'product_description' => '<h1>Elegant, Feminine, &amp; Independent</h1>
<p><img src="//cdn.shopify.com/s/files/1/1205/5522/files/flower1_1024x1024_bfe4b5b7-b11b-4c81-bddc-c5b0e91962aa_grande.jpg?v=1513193143" alt=""></p>
<p>This is the classic steel road bike you\'ve searched for but struggled to find, until now. The upright riding position makes it very comfortable for everyday use, and the low step-through frame allows for easy on and off. If there ever was a frame designed for trips to the flower market or picking up fresh baked bread, this is it.</p>
<h4>AVAILABLE IN A SELECTION OF COLORS:</h4>
<ul>
<li>Red</li>
<li>Blue</li>
<li>Yellow</li>
<li>Green</li>
<li>Black</li>
</ul>
<p><iframe width="560" height="315" src="https://www.youtube.com/embed/K9XCKP9KN7A" frameborder="0" gesture="media" allow="encrypted-media" allowfullscreen=""></iframe></p>',
                'product_image' => 'https://cdn.shopify.com/s/files/1/1205/5522/products/RedBike.jpg?v=1513192497',
                'product_status' => 'Enabled',
                'created_at' => '2016-03-11 16:29:52',
                'updated_at' => '2019-02-19 16:49:40',
                'published_at' => '2016-03-11 16:27:00',
                'product_handle' => 'super-bike',
                'category_id' => null,
                'deleted' => false,
                'vendor' => null,
                'invoice_amount' => null,
                'min_order_qty' => null,
                'min_shipping' => null,
                'shipearly_description' => null,
                'no_of_sold' => '0',
                'no_of_retailers' => '7',
                'no_of_inquiries' => '0',
                'no_of_orders' => '0',
                'no_of_views' => '3',
                'sell_direct' => '1',
                'assembly_option' => '1',
                'ship_from_store' => false,
                'product_name' => 'Super Bike',
                'variant_options' => 'Red',
                'sell_direct_method' => 'Only in Unprotected Territories',
                'is_fee_product' => '0',
                'Collection' => [['title' => 'Home page']],
                'Tag' => [['name' => 'Bike']],
                'InventoryTransfer' => [],
            ],
            23 => [
                'id' => '23',
                'uuid' => '571f796b-2410-4ce7-8e86-0fd791c2de43',
                'user_id' => '8',
                'source_product_id' => '5572298951',
                'productID' => '20016274759',
                'inventory_item_id' => '7412906503',
                'product_type' => 'Bikes',
                'product_title' => 'Super Bike - Blue',
                'product_sku' => 'BICYCLE-2',
                'product_upc' => '0707070707070',
                'taxcode' => '',
                'product_price' => '399.99',
                'compare_at_price' => null,
                'sales_commission' => '0.00',
                'currency' => '',
                'brand_inventory' => '314',
                'brand_restock_date' => null,
                'non_stocking' => false,
                'enable_oversell' => false,
                'enable_b2b_oversell' => false,
                'weight' => '10',
                'weight_unit' => 'lb',
                'installation_hours' => '0',
                'product_description' => '<h1>Elegant, Feminine, &amp; Independent</h1>
<p><img src="//cdn.shopify.com/s/files/1/1205/5522/files/flower1_1024x1024_bfe4b5b7-b11b-4c81-bddc-c5b0e91962aa_grande.jpg?v=1513193143" alt=""></p>
<p>This is the classic steel road bike you\'ve searched for but struggled to find, until now. The upright riding position makes it very comfortable for everyday use, and the low step-through frame allows for easy on and off. If there ever was a frame designed for trips to the flower market or picking up fresh baked bread, this is it.</p>
<h4>AVAILABLE IN A SELECTION OF COLORS:</h4>
<ul>
<li>Red</li>
<li>Blue</li>
<li>Yellow</li>
<li>Green</li>
<li>Black</li>
</ul>
<p><iframe width="560" height="315" src="https://www.youtube.com/embed/K9XCKP9KN7A" frameborder="0" gesture="media" allow="encrypted-media" allowfullscreen=""></iframe></p>',
                'product_image' => 'https://cdn.shopify.com/s/files/1/1205/5522/products/bicycle.jpg?v=1513192459',
                'product_status' => 'Enabled',
                'created_at' => '2016-04-26 10:20:47',
                'updated_at' => '2019-02-11 11:49:49',
                'published_at' => '2016-03-11 16:27:00',
                'product_handle' => 'super-bike',
                'category_id' => null,
                'deleted' => false,
                'vendor' => null,
                'invoice_amount' => null,
                'min_order_qty' => null,
                'min_shipping' => null,
                'shipearly_description' => null,
                'no_of_sold' => '0',
                'no_of_retailers' => '7',
                'no_of_inquiries' => '0',
                'no_of_orders' => '0',
                'no_of_views' => '0',
                'sell_direct' => '1',
                'assembly_option' => '0',
                'ship_from_store' => false,
                'product_name' => 'Super Bike',
                'variant_options' => 'Blue',
                'sell_direct_method' => 'Only in Unprotected Territories',
                'is_fee_product' => '0',
                'Collection' => [['title' => 'Home page']],
                'Tag' => [['name' => 'Bike']],
                'InventoryTransfer' => [],
            ],
        ];

        $dealerOrderProductById = $ProductById;

        return [
            // Cases from OrderSeeder
            'split_payment_fulfilled' => [
                'orderId' => '1',
                'expected' => [
                    'Order' => [
                        'id' => '1',
                        'user_id' => '8',
                        'retailer_id' => '7',
                        'branch_id' => null,
                        'store_associate_id' => null,
                        'sales_rep_id' => '21',
                        'distributor_id' => null,
                        'created_by_user_id' => null,
                        'b2b_ship_to_user_id' => null,
                        'customerID' => '4037',
                        'customerEmail' => '<EMAIL>',
                        'preOrderId' => null,
                        'orderID' => '#SE0012502',
                        'parentOrderId' => null,
                        'source_id' => null,
                        'orderNO' => null,
                        'source_order_name' => null,
                        'purchase_order_number' => null,
                        'serial_number' => null,
                        'external_invoice_id' => null,
                        'order_type' => 'In_store',
                        'subType' => 'nonstock',
                        'is_install' => false,
                        'secretcode' => 'UW5NFZ',
                        'is_commission_retailer' => false,
                        'has_distributor' => false,
                        'requested_b2b_ship_date' => null,
                        'shipped_date' => '2020-03-01 00:00:00',
                        'dealer_qty_ordered' => [
                            'products' => [
                                21 => ['quantity' => 2, 'dealer_price' => '499.00'],
                                23 => ['quantity' => 2, 'dealer_price' => '100.00'],
                            ],
                            'currencytype' => 'CAD',
                            'b2b_tax' => '5.5000',
                            'shipping_amount' => '15.00',
                            'b2b_shipping_tax_option' => 1,
                            'splitpayment_percentage' => '2.00',
                            'splitpayment_amount' => '25.59',
                            'shipment_date' => '2020-03-01 00:00:00',
                        ],
                        'deliveryDate' => null,
                        'verification_image_url' => null,
                        'order_status' => 'Not picked up',
                        'payment_status' => '2',
                        'payment_method' => 'stripe',
                        'payment_method_subtype' => 'card',
                        'payment_captured_at' => '2020-02-29 23:59:59',
                        'total_price' => '3519.51',
                        'total_discount' => '800.00',
                        'shipping_discount' => '0.00',
                        'totalPriceConversion' => '0',
                        'total_sales_commission' => '0.00',
                        'shipearly_commission_fee' => '0.00',
                        'sales_commission_payment_method' => '',
                        'sales_commission_payment_id' => '',
                        'total_retailer_commission' => '0.00',
                        'retailer_commission_payment_method' => '',
                        'retailer_commission_payment_id' => '',
                        'total_labor_compensation' => '0.00',
                        'labor_compensation_payment_method' => '',
                        'currency_code' => 'USD',
                        'total_tax' => '312.87',
                        'tax_included' => false,
                        'shipping_amount' => '6.66',
                        'transactionID' => 'ch_1GO6lpLBFYPhk3KpV3E0f65w',
                        'stripe_account' => 'acct_17FVVxLBFYPhk3Kp',
                        'balance_transaction_id' => 'txn_1GO6u8LBFYPhk3KpE1ZwLew3',
                        'stripe_fees' => '79.17',
                        'risk_level' => 'not_assessed',
                        'discount_code' => 'BIKESFORDAYS',
                        'shipping_address1' => '2400 Nipigon Rd.',
                        'shipping_address2' => '',
                        'shipping_city' => 'Thunder Bay',
                        'shipping_state' => 'Ontario',
                        'shipping_country' => 'ca',
                        'shipping_zipcode' => 'P7C 4W1',
                        'latitude' => '48.400059',
                        'longitude' => '-89.269111',
                        'shipping_telephone' => '(*************',
                        'shipping_statecode' => '611',
                        'shipping_countrycode' => '39',
                        'total_qty_ordered' => '4',
                        'courier' => null,
                        'trackingno' => '',
                        'last_check_point' => '',
                        'unique_token' => null,
                        'masspay' => null,
                        'shipearlyFees' => '145.93',
                        'retailerAmount' => '3477.58',
                        'card_type' => 'Visa',
                        'last_four_digit' => '4242',
                        'fraud_check_cvc' => 'pass',
                        'fraud_check_address' => 'pass',
                        'fraud_check_postal_code' => 'pass',
                        'notes' => null,
                        'created_at' => '2020-02-29 23:59:58',
                        'updated_at' => '2020-03-01 00:00:00',
                        'is_dealerorder' => '0',
                        'payment_status_name' => 'Paid',
                        'customer_name' => 'Aron Schmidt',
                    ],
                    'User' => $User,
                    'Retailer' => [
                        'id' => '7',
                        'Branch' => '0',
                        'company_name' => 'Local Shop',
                    ],
                    'CreatedByUser' => [
                        'id' => null,
                        'company_name' => null,
                    ],
                    'Courier' => [
                        'id' => null,
                        'name' => null,
                    ],
                    'DealerOrder' => [
                        'id' => '10',
                        'user_id' => '8',
                        'order_id' => '1',
                        'source_id' => null,
                        'orderNO' => null,
                        'source_order_name' => null,
                        'total_price' => '1279.72',
                        'product_total_price' => '1198.00',
                        'total_discount' => '0.00',
                        'tax' => '5.50000',
                        'total_tax' => '66.72',
                        'shipping_name' => 'Domestic Price Rate',
                        'shipping_amount' => '15.00',
                        'shipping_tax_amount' => '0.83',
                        'splitpayment_percentage' => '2.00',
                        'splitpayment_amount' => '25.59',
                        'is_split_payment' => true,
                        'wholesale_charge_id' => '',
                        'wholesale_charge_amount' => '0.00',
                        'product_details' => '{"products":{"21":{"quantity":2,"dealer_price":"499.00"},"23":{"quantity":2,"dealer_price":"100.00"}},"currencytype":"CAD","b2b_tax":"5.5000","shipping_amount":"15.00","b2b_shipping_tax_option":1,"splitpayment_percentage":"2.00","splitpayment_amount":"25.59","shipment_date":"2020-03-01 00:00:00"}',
                        'payment_status' => '0',
                        'payment_method' => '',
                        'payment_reference_id' => '',
                        'shipment_date' => '2020-03-01 00:00:00',
                        'created_at' => '2020-02-29 23:59:59',
                        'updated_at' => '2020-03-01 00:00:00',
                        'DealerOrderProduct' => [
                            [
                                'id' => '12',
                                'user_id' => '8',
                                'order_id' => '1',
                                'dealer_order_id' => '10',
                                'product_id' => '21',
                                'warehouse_id' => null,
                                'inventory_transfer_id' => null,
                                'product_price' => '499.00',
                                'product_quantity' => '2',
                                'total_discount' => '0.00',
                                'tax' => '5.50000',
                                'restock_date' => null,
                                'created_at' => '2020-02-29 23:59:59',
                                'updated_at' => '2020-02-29 23:59:59',
                                'Product' => $dealerOrderProductById[21],
                                'Warehouse' => [],
                                'InventoryTransfer' => [],
                                'DealerOrderRefundProduct' => [],
                                'FulfillmentProduct' => [
                                    [
                                        'id' => '12',
                                        'fulfillment_id' => '10',
                                        'dealer_order_product_id' => '12',
                                        'quantity' => '2',
                                    ],
                                ],
                                'quantity' => 2,
                                'remaining_quantity' => 0,
                                'refunded_quantity' => 0,
                                'is_ordered' => false,
                                'is_refunded' => false,
                                'unit_price' => '499.00',
                                'total_price' => '998.00',
                                'remaining_price' => '0.00',
                                'refunded_price' => '0.00',
                                'discount' => [],
                                'WarehouseProduct' => [
                                    'id' => null,
                                    'warehouse_id' => null,
                                    'product_id' => null,
                                    'quantity' => null,
                                    'reserved_quantity' => null,
                                    'restock_date' => null,
                                    'available_quantity' => null,
                                ],
                                'InventoryTransferProduct' => [
                                    'id' => null,
                                    'inventory_transfer_id' => null,
                                    'product_id' => null,
                                    'quantity' => null,
                                    'accepted_quantity' => null,
                                    'remaining_quantity' => null,
                                    'reserved_quantity' => null,
                                    'available_quantity' => null,
                                ],
                            ],
                            [
                                'id' => '13',
                                'user_id' => '8',
                                'order_id' => '1',
                                'dealer_order_id' => '10',
                                'product_id' => '23',
                                'warehouse_id' => null,
                                'inventory_transfer_id' => null,
                                'product_price' => '100.00',
                                'product_quantity' => '2',
                                'total_discount' => '0.00',
                                'tax' => '5.50000',
                                'restock_date' => null,
                                'created_at' => '2020-02-29 23:59:59',
                                'updated_at' => '2020-02-29 23:59:59',
                                'Product' => $dealerOrderProductById[23],
                                'Warehouse' => [],
                                'InventoryTransfer' => [],
                                'DealerOrderRefundProduct' => [],
                                'FulfillmentProduct' => [
                                    [
                                        'id' => '13',
                                        'fulfillment_id' => '10',
                                        'dealer_order_product_id' => '13',
                                        'quantity' => '2',
                                    ],
                                ],
                                'quantity' => 2,
                                'remaining_quantity' => 0,
                                'refunded_quantity' => 0,
                                'is_ordered' => false,
                                'is_refunded' => false,
                                'unit_price' => '100.00',
                                'total_price' => '200.00',
                                'remaining_price' => '0.00',
                                'refunded_price' => '0.00',
                                'discount' => [],
                                'WarehouseProduct' => [
                                    'id' => null,
                                    'warehouse_id' => null,
                                    'product_id' => null,
                                    'quantity' => null,
                                    'reserved_quantity' => null,
                                    'restock_date' => null,
                                    'available_quantity' => null,
                                ],
                                'InventoryTransferProduct' => [
                                    'id' => null,
                                    'inventory_transfer_id' => null,
                                    'product_id' => null,
                                    'quantity' => null,
                                    'accepted_quantity' => null,
                                    'remaining_quantity' => null,
                                    'reserved_quantity' => null,
                                    'available_quantity' => null,
                                ],
                            ],
                        ],
                        'DealerOrderRefund' => [],
                        'Fulfillment' => [
                            [
                                'id' => '10',
                                'dealer_order_id' => '10',
                                'warehouse_id' => '2',
                                'name' => '#SE0012502-F1',
                                'courier_id' => null,
                                'tracking_number' => null,
                                'tracking_url' => null,
                                'Warehouse' => [
                                    'id' => '2',
                                    'name' => 'East Coast',
                                ],
                                'Courier' => [
                                ],
                                'FulfillmentProduct' => [
                                    [
                                        'id' => '12',
                                        'fulfillment_id' => '10',
                                        'order_product_id' => null,
                                        'dealer_order_product_id' => '12',
                                        'quantity' => '2',
                                    ],
                                    [
                                        'id' => '13',
                                        'fulfillment_id' => '10',
                                        'order_product_id' => null,
                                        'dealer_order_product_id' => '13',
                                        'quantity' => '2',
                                    ],
                                ],
                                'created_at' => '2020-03-01 00:00:00'
                            ],
                        ],
                        'fulfillment_status' => 'fulfilled'
                    ],
                    'BillingAddress' => [
                        'id' => '1',
                        'order_id' => '1',
                        'type' => 'billing',
                        'first_name' => 'Aron',
                        'last_name' => 'Schmidt',
                        'company_name' => 'ShipEarly',
                        'address1' => '2400 Nipigon Rd.',
                        'address2' => '',
                        'city' => 'Thunder Bay',
                        'country_id' => '39',
                        'state_id' => '611',
                        'zipcode' => 'P7C 4W1',
                        'telephone' => '(*************',
                        'latitude' => '48.400059',
                        'longitude' => '-89.269111',
                        'created_at' => '2020-02-29 23:59:58',
                    ],
                    'Fulfillment' => [
                    ],
                    'OrderProduct' => [
                        [
                            'id' => '1372',
                            'order_id' => '1',
                            'product_id' => '21',
                            'warehouse_id' => null,
                            'inventory_transfer_id' => null,
                            'quantity' => '2',
                            'total_tax' => '234.00',
                            'total_price' => '2400.00',
                            'total_discount' => '600',
                            'totalPriceConversion' => '0',
                            'total_sales_commission' => '0.00',
                            'total_retailer_commission' => '0.00',
                            'status' => 'Active',
                            'cancelled_at' => null,
                            'notes' => null,
                            'restock_date' => null,
                            'created' => '2020-02-29 23:59:58',
                            'modified' => '2020-02-29 23:59:58',
                            'Product' => $ProductById[21],
                            'Warehouse' => [],
                            'InventoryTransfer' => [],
                            'FulfillmentProduct' => [
                            ],
                            'OrderRefundProduct' => [
                            ],
                            'remaining_quantity' => 2,
                            'refunded_quantity' => 0,
                            'is_ordered' => true,
                            'is_refunded' => false,
                            'unit_price' => '1200.00',
                            'remaining_price' => '2400.00',
                            'refunded_price' => '0.00',
                            'discount' => [],
                            'WarehouseProduct' => [
                                'id' => null,
                                'warehouse_id' => null,
                                'product_id' => null,
                                'quantity' => null,
                                'reserved_quantity' => null,
                                'restock_date' => null,
                                'available_quantity' => null,
                            ],
                            'InventoryTransferProduct' => [
                                'id' => null,
                                'inventory_transfer_id' => null,
                                'product_id' => null,
                                'quantity' => null,
                                'accepted_quantity' => null,
                                'remaining_quantity' => null,
                                'reserved_quantity' => null,
                                'available_quantity' => null,
                            ],
                        ],
                        [
                            'id' => '1373',
                            'order_id' => '1',
                            'product_id' => '23',
                            'warehouse_id' => null,
                            'inventory_transfer_id' => null,
                            'quantity' => '2',
                            'total_tax' => '78.00',
                            'total_price' => '799.98',
                            'total_discount' => '200',
                            'totalPriceConversion' => '0',
                            'total_sales_commission' => '0.00',
                            'total_retailer_commission' => '0.00',
                            'status' => 'Active',
                            'cancelled_at' => null,
                            'notes' => null,
                            'restock_date' => null,
                            'created' => '2020-02-29 23:59:58',
                            'modified' => '2020-02-29 23:59:58',
                            'Product' => $ProductById[23],
                            'Warehouse' => [],
                            'InventoryTransfer' => [],
                            'FulfillmentProduct' => [
                            ],
                            'OrderRefundProduct' => [
                            ],
                            'remaining_quantity' => 2,
                            'refunded_quantity' => 0,
                            'is_ordered' => true,
                            'is_refunded' => false,
                            'unit_price' => '399.99',
                            'remaining_price' => '799.98',
                            'refunded_price' => '0.00',
                            'discount' => [],
                            'WarehouseProduct' => [
                                'id' => null,
                                'warehouse_id' => null,
                                'product_id' => null,
                                'quantity' => null,
                                'reserved_quantity' => null,
                                'restock_date' => null,
                                'available_quantity' => null,
                            ],
                            'InventoryTransferProduct' => [
                                'id' => null,
                                'inventory_transfer_id' => null,
                                'product_id' => null,
                                'quantity' => null,
                                'accepted_quantity' => null,
                                'remaining_quantity' => null,
                                'reserved_quantity' => null,
                                'available_quantity' => null,
                            ],
                        ],
                    ],
                    'Discount' => [
                        'id' => null,
                        'code' => null,
                        'description' => null,
                        'is_b2b_discount' => null,
                        'b2b_hide_ineligible_products' => null,
                        'retailer_option' => null,
                        'retailer_values' => null,
                        'enable_free_freight' => null,
                    ],
                    'CreditTerm' => [
                        'id' => null,
                        'description' => null,
                    ],
                    'ManufacturerRetailer' => [
                        'id' => '3',
                        'b2b_tax' => '5.5000',
                        'enable_split_payment' => true,
                    ],
                    'OrderPayout' => [],
                    'OrderWarrantyImage' => [],
                ],
            ],
            'split_payment_refunded' => [
                'orderId' => '2',
                'expected' => [
                    'Order' => [
                        'id' => '2',
                        'user_id' => '8',
                        'retailer_id' => '7',
                        'branch_id' => null,
                        'store_associate_id' => '20',
                        'sales_rep_id' => '21',
                        'distributor_id' => null,
                        'created_by_user_id' => null,
                        'b2b_ship_to_user_id' => null,
                        'customerID' => '4037',
                        'customerEmail' => '<EMAIL>',
                        'preOrderId' => null,
                        'orderID' => '#SE0012503',
                        'parentOrderId' => null,
                        'source_id' => null,
                        'orderNO' => null,
                        'source_order_name' => null,
                        'purchase_order_number' => null,
                        'serial_number' => null,
                        'external_invoice_id' => null,
                        'order_type' => 'In_store',
                        'subType' => 'nonstock',
                        'is_install' => false,
                        'secretcode' => 'UW5NFZ',
                        'is_commission_retailer' => false,
                        'has_distributor' => false,
                        'requested_b2b_ship_date' => null,
                        'shipped_date' => '2020-03-15 23:59:59',
                        'dealer_qty_ordered' => [
                            'products' => [
                                21 => ['quantity' => 2, 'dealer_price' => '499.00'],
                                23 => ['quantity' => 2, 'dealer_price' => '100.00'],
                            ],
                            'currencytype' => 'CAD',
                            'b2b_tax' => '5.5000',
                            'shipping_amount' => '15.00',
                            'b2b_shipping_tax_option' => 1,
                            'splitpayment_percentage' => '2.00',
                            'splitpayment_amount' => '25.59',
                            'shipment_date' => '2020-03-15 23:59:59',
                        ],
                        'deliveryDate' => null,
                        'verification_image_url' => null,
                        'order_status' => 'Not picked up',
                        'payment_status' => '2',
                        'payment_method' => 'stripe',
                        'payment_method_subtype' => 'card',
                        'payment_captured_at' => '2020-03-15 19:37:34',
                        'total_price' => '3519.51',
                        'total_discount' => '800.00',
                        'shipping_discount' => '0.00',
                        'totalPriceConversion' => '0',
                        'total_sales_commission' => '0.00',
                        'shipearly_commission_fee' => '0.00',
                        'sales_commission_payment_method' => '',
                        'sales_commission_payment_id' => '',
                        'total_retailer_commission' => '0.00',
                        'retailer_commission_payment_method' => '',
                        'retailer_commission_payment_id' => '',
                        'total_labor_compensation' => '0.00',
                        'labor_compensation_payment_method' => '',
                        'currency_code' => 'USD',
                        'total_tax' => '312.87',
                        'tax_included' => false,
                        'shipping_amount' => '6.66',
                        'transactionID' => 'ch_1GO6lpLBFYPhk3KpV3E0f65w',
                        'stripe_account' => 'acct_17FVVxLBFYPhk3Kp',
                        'balance_transaction_id' => 'txn_1GO6u8LBFYPhk3KpE1ZwLew3',
                        'stripe_fees' => '79.17',
                        'risk_level' => 'not_assessed',
                        'discount_code' => 'BIKESFORDAYS',
                        'shipping_address1' => '2400 Nipigon Rd.',
                        'shipping_address2' => '',
                        'shipping_city' => 'Thunder Bay',
                        'shipping_state' => 'Ontario',
                        'shipping_country' => 'ca',
                        'shipping_zipcode' => 'P7C 4W1',
                        'latitude' => '48.400059',
                        'longitude' => '-89.269111',
                        'shipping_telephone' => '(*************',
                        'shipping_statecode' => '611',
                        'shipping_countrycode' => '39',
                        'total_qty_ordered' => '4',
                        'courier' => null,
                        'trackingno' => '',
                        'last_check_point' => '',
                        'unique_token' => null,
                        'masspay' => null,
                        'shipearlyFees' => '145.93',
                        'retailerAmount' => '3477.58',
                        'card_type' => 'Visa',
                        'last_four_digit' => '4242',
                        'fraud_check_cvc' => 'pass',
                        'fraud_check_address' => 'pass',
                        'fraud_check_postal_code' => 'pass',
                        'notes' => null,
                        'created_at' => '2020-03-15 19:37:33',
                        'updated_at' => '2020-03-15 23:59:59',
                        'is_dealerorder' => '0',
                        'payment_status_name' => 'Paid',
                        'customer_name' => 'Aron Schmidt',
                    ],
                    'User' => $User,
                    'Retailer' => [
                        'id' => '7',
                        'Branch' => '0',
                        'company_name' => 'Local Shop',
                    ],
                    'CreatedByUser' => [
                        'id' => null,
                        'company_name' => null,
                    ],
                    'Courier' => [
                        'id' => null,
                        'name' => null,
                    ],
                    'DealerOrder' => [
                        'id' => '11',
                        'user_id' => '8',
                        'order_id' => '2',
                        'source_id' => null,
                        'orderNO' => null,
                        'source_order_name' => null,
                        'total_price' => '1279.72',
                        'product_total_price' => '1198.00',
                        'total_discount' => '0.00',
                        'tax' => '5.50000',
                        'total_tax' => '66.72',
                        'shipping_name' => 'Domestic Price Rate',
                        'shipping_amount' => '15.00',
                        'shipping_tax_amount' => '0.83',
                        'splitpayment_percentage' => '2.00',
                        'splitpayment_amount' => '25.59',
                        'is_split_payment' => true,
                        'wholesale_charge_id' => '',
                        'wholesale_charge_amount' => '0.00',
                        'product_details' => '{"products":{"21":{"quantity":2,"dealer_price":"499.00"},"23":{"quantity":2,"dealer_price":"100.00"}},"currencytype":"CAD","b2b_tax":"5.5000","shipping_amount":"15.00","b2b_shipping_tax_option":1,"splitpayment_percentage":"2.00","splitpayment_amount":"25.59","shipment_date":"2020-03-15 23:59:59"}',
                        'payment_status' => '0',
                        'payment_method' => '',
                        'payment_reference_id' => '',
                        'shipment_date' => '2020-03-15 23:59:59',
                        'created_at' => '2020-03-15 19:37:34',
                        'updated_at' => '2020-03-16 00:00:00',
                        'DealerOrderProduct' => [
                            [
                                'id' => '14',
                                'user_id' => '8',
                                'order_id' => '2',
                                'dealer_order_id' => '11',
                                'product_id' => '21',
                                'warehouse_id' => null,
                                'inventory_transfer_id' => null,
                                'product_price' => '499.00',
                                'product_quantity' => '2',
                                'total_discount' => '0.00',
                                'tax' => '5.50000',
                                'restock_date' => null,
                                'created_at' => '2020-03-15 19:37:34',
                                'updated_at' => '2020-03-15 19:37:34',
                                'Product' => $dealerOrderProductById[21],
                                'Warehouse' => [],
                                'InventoryTransfer' => [],
                                'DealerOrderRefundProduct' => [
                                    [
                                        'id' => '1',
                                        'dealer_order_refund_id' => '1',
                                        'dealer_order_product_id' => '14',
                                        'quantity' => '1',
                                    ],
                                    [
                                        'id' => '4',
                                        'dealer_order_refund_id' => '3',
                                        'dealer_order_product_id' => '14',
                                        'quantity' => '1',
                                    ],
                                ],
                                'FulfillmentProduct' => [
                                    [
                                        'id' => '14',
                                        'fulfillment_id' => '11',
                                        'dealer_order_product_id' => '14',
                                        'quantity' => '2',
                                    ],
                                ],
                                'quantity' => 2,
                                'remaining_quantity' => 0,
                                'refunded_quantity' => 0,
                                'is_ordered' => false,
                                'is_refunded' => false,
                                'unit_price' => '499.00',
                                'total_price' => '998.00',
                                'remaining_price' => '0.00',
                                'refunded_price' => '0.00',
                                'discount' => [],
                                'WarehouseProduct' => [
                                    'id' => null,
                                    'warehouse_id' => null,
                                    'product_id' => null,
                                    'quantity' => null,
                                    'reserved_quantity' => null,
                                    'restock_date' => null,
                                    'available_quantity' => null,
                                ],
                                'InventoryTransferProduct' => [
                                    'id' => null,
                                    'inventory_transfer_id' => null,
                                    'product_id' => null,
                                    'quantity' => null,
                                    'accepted_quantity' => null,
                                    'remaining_quantity' => null,
                                    'reserved_quantity' => null,
                                    'available_quantity' => null,
                                ],
                            ],
                            [
                                'id' => '15',
                                'user_id' => '8',
                                'order_id' => '2',
                                'dealer_order_id' => '11',
                                'product_id' => '23',
                                'warehouse_id' => null,
                                'inventory_transfer_id' => null,
                                'product_price' => '100.00',
                                'product_quantity' => '2',
                                'total_discount' => '0.00',
                                'tax' => '5.50000',
                                'restock_date' => null,
                                'created_at' => '2020-03-15 19:37:34',
                                'updated_at' => '2020-03-15 19:37:34',
                                'Product' => $dealerOrderProductById[23],
                                'Warehouse' => [],
                                'InventoryTransfer' => [],
                                'DealerOrderRefundProduct' => [
                                    [
                                        'id' => '2',
                                        'dealer_order_refund_id' => '1',
                                        'dealer_order_product_id' => '15',
                                        'quantity' => '1',
                                    ],
                                    [
                                        'id' => '5',
                                        'dealer_order_refund_id' => '3',
                                        'dealer_order_product_id' => '15',
                                        'quantity' => '1',
                                    ],
                                ],
                                'FulfillmentProduct' => [
                                    [
                                        'id' => '15',
                                        'fulfillment_id' => '11',
                                        'dealer_order_product_id' => '15',
                                        'quantity' => '2',
                                    ],
                                ],
                                'quantity' => 2,
                                'remaining_quantity' => 0,
                                'refunded_quantity' => 0,
                                'is_ordered' => false,
                                'is_refunded' => false,
                                'unit_price' => '100.00',
                                'total_price' => '200.00',
                                'remaining_price' => '0.00',
                                'refunded_price' => '0.00',
                                'discount' => [],
                                'WarehouseProduct' => [
                                    'id' => null,
                                    'warehouse_id' => null,
                                    'product_id' => null,
                                    'quantity' => null,
                                    'reserved_quantity' => null,
                                    'restock_date' => null,
                                    'available_quantity' => null,
                                ],
                                'InventoryTransferProduct' => [
                                    'id' => null,
                                    'inventory_transfer_id' => null,
                                    'product_id' => null,
                                    'quantity' => null,
                                    'accepted_quantity' => null,
                                    'remaining_quantity' => null,
                                    'reserved_quantity' => null,
                                    'available_quantity' => null,
                                ],
                            ],
                        ],
                        'DealerOrderRefund' => [
                            [
                                'id' => '1',
                                'dealer_order_id' => '11',
                                'source_id' => null,
                                'transaction_id' => 'fr_fake1of2',
                                'amount' => '631.95',
                                'shipping_portion' => '0.00',
                                'tax_portion' => '32.95',
                                'reason' => null,
                                'created_at' => '2020-03-15 23:59:59',
                                'updated_at' => '2020-03-15 23:59:59',
                            ],
                            [
                                'id' => '3',
                                'dealer_order_id' => '11',
                                'source_id' => null,
                                'transaction_id' => 'fr_fake2of2',
                                'amount' => '647.77',
                                'shipping_portion' => '15.00',
                                'tax_portion' => '33.77',
                                'reason' => null,
                                'created_at' => '2020-03-16 00:00:00',
                                'updated_at' => '2020-03-16 00:00:00',
                            ],
                        ],
                        'Fulfillment' => [
                            [
                                'id' => '11',
                                'dealer_order_id' => '11',
                                'warehouse_id' => '2',
                                'name' => '#SE0012503-F1',
                                'courier_id' => null,
                                'tracking_number' => null,
                                'tracking_url' => null,
                                'Warehouse' => [
                                    'id' => '2',
                                    'name' => 'East Coast',
                                ],
                                'Courier' => [
                                ],
                                'FulfillmentProduct' => [
                                    [
                                        'id' => '14',
                                        'fulfillment_id' => '11',
                                        'order_product_id' => null,
                                        'dealer_order_product_id' => '14',
                                        'quantity' => '2',
                                    ],
                                    [
                                        'id' => '15',
                                        'fulfillment_id' => '11',
                                        'order_product_id' => null,
                                        'dealer_order_product_id' => '15',
                                        'quantity' => '2',
                                    ],
                                ],
                                'created_at' => '2020-03-15 23:59:59'
                            ],
                        ],
                        'fulfillment_status' => 'fulfilled'
                    ],
                    'BillingAddress' => [
                        'id' => '2',
                        'order_id' => '2',
                        'type' => 'billing',
                        'first_name' => 'Aron',
                        'last_name' => 'Schmidt',
                        'company_name' => 'ShipEarly',
                        'address1' => '2400 Nipigon Rd.',
                        'address2' => '',
                        'city' => 'Thunder Bay',
                        'country_id' => '39',
                        'state_id' => '611',
                        'zipcode' => 'P7C 4W1',
                        'telephone' => '(*************',
                        'latitude' => '48.400059',
                        'longitude' => '-89.269111',
                        'created_at' => '2020-03-15 19:37:33',
                    ],
                    'Fulfillment' => [],
                    'OrderProduct' => [
                        [
                            'id' => '1374',
                            'order_id' => '2',
                            'product_id' => '21',
                            'warehouse_id' => null,
                            'inventory_transfer_id' => null,
                            'quantity' => '2',
                            'total_tax' => '234.00',
                            'total_price' => '2400.00',
                            'total_discount' => '600',
                            'totalPriceConversion' => '0',
                            'total_sales_commission' => '0.00',
                            'total_retailer_commission' => '0.00',
                            'status' => 'Active',
                            'cancelled_at' => null,
                            'notes' => null,
                            'restock_date' => null,
                            'created' => '2020-03-15 19:37:40',
                            'modified' => '2020-03-15 19:37:40',
                            'Product' => $ProductById[21],
                            'Warehouse' => [],
                            'InventoryTransfer' => [],
                            'FulfillmentProduct' => [],
                            'OrderRefundProduct' => [
                            ],
                            'remaining_quantity' => 2,
                            'refunded_quantity' => 0,
                            'is_ordered' => true,
                            'is_refunded' => false,
                            'unit_price' => '1200.00',
                            'remaining_price' => '2400.00',
                            'refunded_price' => '0.00',
                            'discount' => [],
                            'WarehouseProduct' => [
                                'id' => null,
                                'warehouse_id' => null,
                                'product_id' => null,
                                'quantity' => null,
                                'reserved_quantity' => null,
                                'restock_date' => null,
                                'available_quantity' => null,
                            ],
                            'InventoryTransferProduct' => [
                                'id' => null,
                                'inventory_transfer_id' => null,
                                'product_id' => null,
                                'quantity' => null,
                                'accepted_quantity' => null,
                                'remaining_quantity' => null,
                                'reserved_quantity' => null,
                                'available_quantity' => null,
                            ],
                        ],
                        [
                            'id' => '1375',
                            'order_id' => '2',
                            'product_id' => '23',
                            'warehouse_id' => null,
                            'inventory_transfer_id' => null,
                            'quantity' => '2',
                            'total_tax' => '78.00',
                            'total_price' => '799.98',
                            'total_discount' => '200',
                            'totalPriceConversion' => '0',
                            'total_sales_commission' => '0.00',
                            'total_retailer_commission' => '0.00',
                            'status' => 'Active',
                            'cancelled_at' => null,
                            'notes' => null,
                            'restock_date' => null,
                            'created' => '2020-03-15 19:37:40',
                            'modified' => '2020-03-15 19:37:40',
                            'Product' => $ProductById[23],
                            'Warehouse' => [],
                            'InventoryTransfer' => [],
                            'FulfillmentProduct' => [],
                            'OrderRefundProduct' => [
                            ],
                            'remaining_quantity' => 2,
                            'refunded_quantity' => 0,
                            'is_ordered' => true,
                            'is_refunded' => false,
                            'unit_price' => '399.99',
                            'remaining_price' => '799.98',
                            'refunded_price' => '0.00',
                            'discount' => [],
                            'WarehouseProduct' => [
                                'id' => null,
                                'warehouse_id' => null,
                                'product_id' => null,
                                'quantity' => null,
                                'reserved_quantity' => null,
                                'restock_date' => null,
                                'available_quantity' => null,
                            ],
                            'InventoryTransferProduct' => [
                                'id' => null,
                                'inventory_transfer_id' => null,
                                'product_id' => null,
                                'quantity' => null,
                                'accepted_quantity' => null,
                                'remaining_quantity' => null,
                                'reserved_quantity' => null,
                                'available_quantity' => null,
                            ],
                        ],
                    ],
                    'Discount' => [
                        'id' => null,
                        'code' => null,
                        'description' => null,
                        'is_b2b_discount' => null,
                        'b2b_hide_ineligible_products' => null,
                        'retailer_option' => null,
                        'retailer_values' => null,
                        'enable_free_freight' => null,
                    ],
                    'CreditTerm' => [
                        'id' => null,
                        'description' => null,
                    ],
                    'ManufacturerRetailer' => [
                        'id' => '3',
                        'b2b_tax' => '5.5000',
                        'enable_split_payment' => true,
                    ],
                    'OrderPayout' => [],
                    'OrderWarrantyImage' => [],
                ],
            ],
            'purchase_order_on_file' => [
                'orderId' => '3',
                'expected' => [
                    'Order' => [
                        'id' => '3',
                        'user_id' => '8',
                        'retailer_id' => '7',
                        'branch_id' => null,
                        'store_associate_id' => null,
                        'sales_rep_id' => '21',
                        'distributor_id' => null,
                        'created_by_user_id' => '7',
                        'b2b_ship_to_user_id' => '17',
                        'customerID' => null,
                        'customerEmail' => '<EMAIL>',
                        'preOrderId' => null,
                        'orderID' => '#SE0012504',
                        'parentOrderId' => null,
                        'source_id' => null,
                        'orderNO' => null,
                        'source_order_name' => null,
                        'purchase_order_number' => '#PO-12345',
                        'serial_number' => null,
                        'external_invoice_id' => null,
                        'order_type' => 'wholesale',
                        'subType' => 'regular',
                        'is_install' => false,
                        'secretcode' => '',
                        'is_commission_retailer' => false,
                        'has_distributor' => false,
                        'requested_b2b_ship_date' => '2020-05-15',
                        'shipped_date' => null,
                        'dealer_qty_ordered' => [
                            'products' => [
                                21 => ['quantity' => 3, 'dealer_price' => '499.00'],
                                23 => ['quantity' => 2, 'dealer_price' => '100.00'],
                            ],
                            'currencytype' => 'USD',
                            'b2b_tax' => '5.5000',
                        ],
                        'deliveryDate' => null,
                        'verification_image_url' => null,
                        'order_status' => 'Purchase Order',
                        'payment_status' => '0',
                        'payment_method' => 'external',
                        'payment_method_subtype' => null,
                        'payment_captured_at' => null,
                        'total_price' => '1821.99',
                        'total_discount' => '0.00',
                        'shipping_discount' => '0.00',
                        'totalPriceConversion' => '0',
                        'total_sales_commission' => '0.00',
                        'shipearly_commission_fee' => '0.00',
                        'sales_commission_payment_method' => '',
                        'sales_commission_payment_id' => '',
                        'total_retailer_commission' => '0.00',
                        'retailer_commission_payment_method' => '',
                        'retailer_commission_payment_id' => '',
                        'total_labor_compensation' => '0.00',
                        'labor_compensation_payment_method' => '',
                        'currency_code' => 'USD',
                        'total_tax' => '94.99',
                        'tax_included' => false,
                        'shipping_amount' => '30.00',
                        'transactionID' => '',
                        'stripe_account' => null,
                        'balance_transaction_id' => '',
                        'stripe_fees' => '0.00',
                        'risk_level' => 'not_assessed',
                        'discount_code' => '',
                        'shipping_address1' => '216 Brodie St S',
                        'shipping_address2' => '',
                        'shipping_city' => 'Thunder Bay',
                        'shipping_state' => 'ON',
                        'shipping_country' => 'CA',
                        'shipping_zipcode' => 'P7E 1C2',
                        'latitude' => '48.381709',
                        'longitude' => '-89.246322',
                        'shipping_telephone' => '************',
                        'shipping_statecode' => '611',
                        'shipping_countrycode' => '39',
                        'total_qty_ordered' => '5',
                        'courier' => null,
                        'trackingno' => '',
                        'last_check_point' => '',
                        'unique_token' => null,
                        'masspay' => null,
                        'shipearlyFees' => '0.00',
                        'retailerAmount' => '1821.99',
                        'card_type' => null,
                        'last_four_digit' => null,
                        'fraud_check_cvc' => null,
                        'fraud_check_address' => null,
                        'fraud_check_postal_code' => null,
                        'notes' => 'Example notes...',
                        'created_at' => '2020-05-12 14:30:39',
                        'updated_at' => '2020-05-12 14:30:42',
                        'is_dealerorder' => '1',
                        'payment_status_name' => 'Authorized',
                        'customer_name' => 'Local Shop Branch',
                    ],
                    'User' => $User,
                    'Retailer' => [
                        'id' => '7',
                        'Branch' => '0',
                        'company_name' => 'Local Shop',
                    ],
                    'CreatedByUser' => [
                        'id' => '7',
                        'company_name' => 'Local Shop',
                    ],
                    'Courier' => [
                        'id' => null,
                        'name' => null,
                    ],
                    'DealerOrder' => [
                        'id' => null,
                        'user_id' => null,
                        'order_id' => null,
                        'source_id' => null,
                        'orderNO' => null,
                        'source_order_name' => null,
                        'total_price' => null,
                        'product_total_price' => null,
                        'total_discount' => null,
                        'tax' => null,
                        'total_tax' => null,
                        'shipping_name' => null,
                        'shipping_amount' => null,
                        'shipping_tax_amount' => null,
                        'splitpayment_percentage' => null,
                        'splitpayment_amount' => null,
                        'is_split_payment' => null,
                        'wholesale_charge_id' => null,
                        'wholesale_charge_amount' => null,
                        'product_details' => null,
                        'payment_status' => null,
                        'payment_method' => null,
                        'payment_reference_id' => null,
                        'shipment_date' => null,
                        'created_at' => null,
                        'updated_at' => null,
                        'fulfillment_status' => null
                    ],
                    'BillingAddress' => [
                        'id' => null,
                        'order_id' => null,
                        'type' => null,
                        'first_name' => null,
                        'last_name' => null,
                        'company_name' => null,
                        'address1' => null,
                        'address2' => null,
                        'city' => null,
                        'country_id' => null,
                        'state_id' => null,
                        'zipcode' => null,
                        'telephone' => null,
                        'latitude' => null,
                        'longitude' => null,
                        'created_at' => null,
                    ],
                    'Fulfillment' => [],
                    'OrderProduct' => [
                        [
                            'id' => '1376',
                            'order_id' => '3',
                            'product_id' => '21',
                            'warehouse_id' => '1',
                            'inventory_transfer_id' => null,
                            'quantity' => '1',
                            'total_tax' => '27.445',
                            'total_price' => '499.00',
                            'total_discount' => '0.00',
                            'totalPriceConversion' => '0',
                            'total_sales_commission' => '0.00',
                            'total_retailer_commission' => '0.00',
                            'status' => 'Active',
                            'cancelled_at' => null,
                            'notes' => null,
                            'restock_date' => null,
                            'created' => '2020-05-12 16:30:42',
                            'modified' => '2020-05-12 16:30:42',
                            'Product' => array_merge($ProductById[21], [
                                'InventoryTransfer' => [
                                    0 => [
                                        'id' => '1',
                                        'destination_warehouse_id' => '1',
                                        'name' => '#T0001',
                                        'expected_arrival_date' => $this->dateToday,
                                        'InventoryTransferProduct' => [
                                            'id' => '2',
                                            'inventory_transfer_id' => '1',
                                            'product_id' => '21',
                                            'available_quantity' => '13',
                                        ],
                                    ],
                                ],
                            ]),
                            'Warehouse' => [
                                'id' => '1',
                                'name' => 'West Coast',
                            ],
                            'InventoryTransfer' => [],
                            'FulfillmentProduct' => [],
                            'OrderRefundProduct' => [
                            ],
                            'remaining_quantity' => 1,
                            'refunded_quantity' => 0,
                            'is_ordered' => true,
                            'is_refunded' => false,
                            'unit_price' => '499.00',
                            'remaining_price' => '499.00',
                            'refunded_price' => '0.00',
                            'discount' => [],
                            'WarehouseProduct' => [
                                'id' => '7',
                                'warehouse_id' => '1',
                                'product_id' => '21',
                                'quantity' => '5',
                                'reserved_quantity' => '0',
                                'restock_date' => null,
                                'available_quantity' => '5',
                            ],
                            'InventoryTransferProduct' => [
                                'id' => null,
                                'inventory_transfer_id' => null,
                                'product_id' => null,
                                'quantity' => null,
                                'accepted_quantity' => null,
                                'remaining_quantity' => null,
                                'reserved_quantity' => null,
                                'available_quantity' => null,
                            ],
                        ],
                        [
                            'id' => '1378',
                            'order_id' => '3',
                            'product_id' => '23',
                            'warehouse_id' => '1',
                            'inventory_transfer_id' => '1',
                            'quantity' => '2',
                            'total_tax' => '11',
                            'total_price' => '200.00',
                            'total_discount' => '0.00',
                            'totalPriceConversion' => '0',
                            'total_sales_commission' => '0.00',
                            'total_retailer_commission' => '0.00',
                            'status' => 'Active',
                            'cancelled_at' => null,
                            'notes' => null,
                            'restock_date' => '2021-09-30 00:00:00',
                            'created' => '2020-05-12 16:30:42',
                            'modified' => '2020-05-12 16:30:42',
                            'Product' => array_merge($ProductById[23], [
                                'InventoryTransfer' => [
                                    0 => [
                                        'id' => '1',
                                        'destination_warehouse_id' => '1',
                                        'name' => '#T0001',
                                        'expected_arrival_date' => $this->dateToday,
                                        'InventoryTransferProduct' => [
                                            'id' => '1',
                                            'inventory_transfer_id' => '1',
                                            'product_id' => '23',
                                            'available_quantity' => '22',
                                        ],
                                    ],
                                ],
                            ]),
                            'Warehouse' => [
                                'id' => '1',
                                'name' => 'West Coast',
                            ],
                            'InventoryTransfer' => [
                                'id' => '1',
                                'destination_warehouse_id' => '1',
                                'name' => '#T0001',
                                'expected_arrival_date' => $this->dateToday,
                            ],
                            'FulfillmentProduct' => [],
                            'OrderRefundProduct' => [
                            ],
                            'remaining_quantity' => 2,
                            'refunded_quantity' => 0,
                            'is_ordered' => true,
                            'is_refunded' => false,
                            'unit_price' => '100.00',
                            'remaining_price' => '200.00',
                            'refunded_price' => '0.00',
                            'discount' => [],
                            'WarehouseProduct' => [
                                'id' => '13',
                                'warehouse_id' => '1',
                                'product_id' => '23',
                                'quantity' => '0',
                                'reserved_quantity' => '0',
                                'restock_date' => null,
                                'available_quantity' => '0',
                            ],
                            'InventoryTransferProduct' => [
                                'id' => '1',
                                'inventory_transfer_id' => '1',
                                'product_id' => '23',
                                'quantity' => '24',
                                'accepted_quantity' => '0',
                                'remaining_quantity' => '24',
                                'reserved_quantity' => '0',
                                'available_quantity' => '24',
                            ],
                        ],
                        [
                            'id' => '1377',
                            'order_id' => '3',
                            'product_id' => '21',
                            'warehouse_id' => '2',
                            'inventory_transfer_id' => null,
                            'quantity' => '2',
                            'total_tax' => '54.89',
                            'total_price' => '998.00',
                            'total_discount' => '0.00',
                            'totalPriceConversion' => '0',
                            'total_sales_commission' => '0.00',
                            'total_retailer_commission' => '0.00',
                            'status' => 'Active',
                            'cancelled_at' => null,
                            'notes' => null,
                            'restock_date' => null,
                            'created' => '2020-05-12 16:30:42',
                            'modified' => '2020-05-12 16:30:42',
                            'Product' => array_merge($ProductById[21], [
                                'InventoryTransfer' => [
                                    0 => [
                                        'id' => '3',
                                        'destination_warehouse_id' => '2',
                                        'name' => '#T0003',
                                        'expected_arrival_date' => $this->dateTomorrow,
                                        'InventoryTransferProduct' => [
                                            'id' => '6',
                                            'inventory_transfer_id' => '3',
                                            'product_id' => '21',
                                            'available_quantity' => '12',
                                        ],
                                    ],
                                ],
                            ]),
                            'Warehouse' => [
                                'id' => '2',
                                'name' => 'East Coast',
                            ],
                            'InventoryTransfer' => [],
                            'FulfillmentProduct' => [],
                            'OrderRefundProduct' => [
                            ],
                            'remaining_quantity' => 2,
                            'refunded_quantity' => 0,
                            'is_ordered' => true,
                            'is_refunded' => false,
                            'unit_price' => '499.00',
                            'remaining_price' => '998.00',
                            'refunded_price' => '0.00',
                            'discount' => [],
                            'WarehouseProduct' => [
                                'id' => '1',
                                'warehouse_id' => '2',
                                'product_id' => '21',
                                'quantity' => '169',
                                'reserved_quantity' => '0',
                                'restock_date' => null,
                                'available_quantity' => '169',
                            ],
                            'InventoryTransferProduct' => [
                                'id' => null,
                                'inventory_transfer_id' => null,
                                'product_id' => null,
                                'quantity' => null,
                                'accepted_quantity' => null,
                                'remaining_quantity' => null,
                                'reserved_quantity' => null,
                                'available_quantity' => null,
                            ],
                        ],
                    ],
                    'Discount' => [
                        'id' => null,
                        'code' => null,
                        'description' => null,
                        'is_b2b_discount' => null,
                        'b2b_hide_ineligible_products' => null,
                        'retailer_option' => null,
                        'retailer_values' => null,
                        'enable_free_freight' => null,
                    ],
                    'CreditTerm' => [
                        'id' => null,
                        'description' => null,
                    ],
                    'ManufacturerRetailer' => [
                        'id' => '3',
                        'b2b_tax' => '5.5000',
                        'enable_split_payment' => true,
                    ],
                    'OrderPayout' => [],
                    'OrderWarrantyImage' => [],
                ],
            ],
            'sell_direct' => [
                'orderId' => '4',
                'expected' => [
                    'Order' => [
                        'id' => '4',
                        'user_id' => '8',
                        'retailer_id' => null,
                        'branch_id' => null,
                        'store_associate_id' => null,
                        'sales_rep_id' => null,
                        'distributor_id' => null,
                        'created_by_user_id' => null,
                        'b2b_ship_to_user_id' => null,
                        'customerID' => '4037',
                        'customerEmail' => '<EMAIL>',
                        'preOrderId' => '990',
                        'orderID' => '#SE0012505',
                        'parentOrderId' => null,
                        'source_id' => '1001',
                        'orderNO' => '2001',
                        'source_order_name' => '#2001',
                        'purchase_order_number' => null,
                        'serial_number' => null,
                        'external_invoice_id' => null,
                        'order_type' => 'shipearly',
                        'subType' => 'Sell_direct',
                        'is_install' => false,
                        'secretcode' => '',
                        'is_commission_retailer' => false,
                        'has_distributor' => false,
                        'requested_b2b_ship_date' => null,
                        'shipped_date' => null,
                        'dealer_qty_ordered' => null,
                        'deliveryDate' => null,
                        'verification_image_url' => null,
                        'order_status' => 'paid',
                        'payment_status' => '2',
                        'payment_method' => 'stripe',
                        'payment_method_subtype' => 'card',
                        'payment_captured_at' => '2022-12-19 18:36:29',
                        'total_price' => '3519.51',
                        'total_discount' => '800.00',
                        'shipping_discount' => '0.00',
                        'totalPriceConversion' => '3519.51',
                        'total_sales_commission' => '0.00',
                        'shipearly_commission_fee' => '0.00',
                        'sales_commission_payment_method' => '',
                        'sales_commission_payment_id' => '',
                        'total_retailer_commission' => '0.00',
                        'retailer_commission_payment_method' => '',
                        'retailer_commission_payment_id' => '',
                        'total_labor_compensation' => '0.00',
                        'labor_compensation_payment_method' => '',
                        'currency_code' => 'USD',
                        'total_tax' => '312.87',
                        'tax_included' => false,
                        'shipping_amount' => '6.66',
                        'transactionID' => 'pi_fake',
                        'stripe_account' => 'acct_194DCmJKdns8gzMQ',
                        'balance_transaction_id' => 'txn_fake',
                        'stripe_fees' => '79.17',
                        'risk_level' => 'normal',
                        'discount_code' => 'BIKESFORDAYS',
                        'shipping_address1' => '2400 Nipigon Rd.',
                        'shipping_address2' => null,
                        'shipping_city' => 'Thunder Bay',
                        'shipping_state' => 'Ontario',
                        'shipping_country' => 'Canada',
                        'shipping_zipcode' => 'P7C 4W1',
                        'latitude' => '48.400059',
                        'longitude' => '-89.269111',
                        'shipping_telephone' => '+1 (*************',
                        'shipping_statecode' => 'ON',
                        'shipping_countrycode' => 'CA',
                        'total_qty_ordered' => '4',
                        'courier' => null,
                        'trackingno' => '',
                        'last_check_point' => '',
                        'unique_token' => null,
                        'masspay' => null,
                        'shipearlyFees' => '55.38',
                        'retailerAmount' => '3568.13',
                        'card_type' => null,
                        'last_four_digit' => null,
                        'fraud_check_cvc' => null,
                        'fraud_check_address' => null,
                        'fraud_check_postal_code' => null,
                        'notes' => null,
                        'created_at' => '2022-12-19 13:36:24',
                        'updated_at' => '2022-12-19 13:36:26',
                        'is_dealerorder' => '0',
                        'payment_status_name' => 'Paid',
                        'customer_name' => 'Aron Schmidt',
                    ],
                    'User' => $User,
                    'Retailer' => [
                        'id' => null,
                        'Branch' => null,
                        'company_name' => null,
                    ],
                    'CreatedByUser' => [
                        'id' => null,
                        'company_name' => null,
                    ],
                    'Courier' => [
                        'id' => null,
                        'name' => null,
                    ],
                    'DealerOrder' => [
                        'id' => null,
                        'user_id' => null,
                        'order_id' => null,
                        'source_id' => null,
                        'orderNO' => null,
                        'source_order_name' => null,
                        'total_price' => null,
                        'product_total_price' => null,
                        'total_discount' => null,
                        'tax' => null,
                        'total_tax' => null,
                        'shipping_name' => null,
                        'shipping_amount' => null,
                        'shipping_tax_amount' => null,
                        'splitpayment_percentage' => null,
                        'splitpayment_amount' => null,
                        'is_split_payment' => null,
                        'wholesale_charge_id' => null,
                        'wholesale_charge_amount' => null,
                        'product_details' => null,
                        'payment_status' => null,
                        'payment_method' => null,
                        'payment_reference_id' => null,
                        'shipment_date' => null,
                        'created_at' => null,
                        'updated_at' => null,
                        'fulfillment_status' => null
                    ],
                    'BillingAddress' => [
                        'id' => '3',
                        'order_id' => '4',
                        'type' => 'billing',
                        'first_name' => 'Aron',
                        'last_name' => 'Schmidt',
                        'company_name' => 'ShipEarly',
                        'address1' => '2400 Nipigon Rd.',
                        'address2' => '',
                        'city' => 'Thunder Bay',
                        'country_id' => '39',
                        'state_id' => '611',
                        'zipcode' => 'P7C 4W1',
                        'telephone' => '+1 (*************',
                        'latitude' => '48.400059',
                        'longitude' => '-89.269111',
                        'created_at' => '2022-12-19 18:36:23',
                    ],
                    'Fulfillment' => [
                        [
                            'id' => '14',
                            'order_id' => '4',
                            'warehouse_id' => '1',
                            'name' => '#SE0012505-F1',
                            'courier_id' => '82',
                            'tracking_number' => '406973316728',
                            'tracking_url' => 'https://www.fedex.com/fedextrack/?trknbr=406973316728',
                            'Warehouse' => [
                                'id' => '1',
                                'name' => 'West Coast',
                            ],
                            'Courier' => [
                                'id' => '82',
                                'name' => 'FedEx',
                            ],
                            'FulfillmentProduct' => [
                                [
                                    'id' => '18',
                                    'fulfillment_id' => '14',
                                    'order_product_id' => '1379',
                                    'dealer_order_product_id' => null,
                                    'quantity' => '1',
                                ],
                                [
                                    'id' => '19',
                                    'fulfillment_id' => '14',
                                    'order_product_id' => '1380',
                                    'dealer_order_product_id' => null,
                                    'quantity' => '1',
                                ],
                            ],
                        ],
                    ],
                    'OrderProduct' => [
                        [
                            'id' => '1379',
                            'order_id' => '4',
                            'product_id' => '21',
                            'warehouse_id' => '2',
                            'inventory_transfer_id' => null,
                            'quantity' => '2',
                            'total_tax' => '234.00',
                            'total_price' => '2400.00',
                            'total_discount' => '600.00',
                            'totalPriceConversion' => '2400.00',
                            'total_sales_commission' => '0.00',
                            'total_retailer_commission' => '0.00',
                            'status' => 'Active',
                            'cancelled_at' => null,
                            'notes' => null,
                            'restock_date' => null,
                            'created' => '2022-12-19 13:36:24',
                            'modified' => '2022-12-19 13:36:24',
                            'Product' => array_merge($ProductById[21], [
                                'InventoryTransfer' => [
                                    0 => [
                                        'id' => '3',
                                        'destination_warehouse_id' => '2',
                                        'name' => '#T0003',
                                        'expected_arrival_date' => $this->dateTomorrow,
                                        'InventoryTransferProduct' => [
                                            'id' => '6',
                                            'inventory_transfer_id' => '3',
                                            'product_id' => '21',
                                            'available_quantity' => '12',
                                        ],
                                    ],
                                ],
                            ]),
                            'Warehouse' => [
                                'id' => '2',
                                'name' => 'East Coast',
                            ],
                            'InventoryTransfer' => [],
                            'FulfillmentProduct' => [
                                [
                                    'id' => '18',
                                    'fulfillment_id' => '14',
                                    'order_product_id' => '1379',
                                    'quantity' => '1',
                                ],
                            ],
                            'OrderRefundProduct' => [
                            ],
                            'remaining_quantity' => 1,
                            'refunded_quantity' => 0,
                            'is_ordered' => true,
                            'is_refunded' => false,
                            'unit_price' => '1200.00',
                            'remaining_price' => '1200.00',
                            'refunded_price' => '0.00',
                            'discount' => [],
                            'WarehouseProduct' => [
                                'id' => '1',
                                'warehouse_id' => '2',
                                'product_id' => '21',
                                'quantity' => '169',
                                'reserved_quantity' => '0',
                                'restock_date' => null,
                                'available_quantity' => '169',
                            ],
                            'InventoryTransferProduct' => [
                                'id' => null,
                                'inventory_transfer_id' => null,
                                'product_id' => null,
                                'quantity' => null,
                                'accepted_quantity' => null,
                                'remaining_quantity' => null,
                                'reserved_quantity' => null,
                                'available_quantity' => null,
                            ],
                        ],
                        [
                            'id' => '1380',
                            'order_id' => '4',
                            'product_id' => '23',
                            'warehouse_id' => '2',
                            'inventory_transfer_id' => null,
                            'quantity' => '2',
                            'total_tax' => '78.00',
                            'total_price' => '799.98',
                            'total_discount' => '200.00',
                            'totalPriceConversion' => '799.98',
                            'total_sales_commission' => '0.00',
                            'total_retailer_commission' => '0.00',
                            'status' => 'Active',
                            'cancelled_at' => null,
                            'notes' => null,
                            'restock_date' => null,
                            'created' => '2020-12-10 12:47:44',
                            'modified' => '2020-12-10 12:47:44',
                            'Product' => $ProductById[23],
                            'Warehouse' => [
                                'id' => '2',
                                'name' => 'East Coast',
                            ],
                            'InventoryTransfer' => [],
                            'FulfillmentProduct' => [
                                [
                                    'id' => '19',
                                    'fulfillment_id' => '14',
                                    'order_product_id' => '1380',
                                    'quantity' => '1',
                                ],
                            ],
                            'OrderRefundProduct' => [
                            ],
                            'remaining_quantity' => 1,
                            'refunded_quantity' => 0,
                            'is_ordered' => true,
                            'is_refunded' => false,
                            'unit_price' => '399.99',
                            'remaining_price' => '399.99',
                            'refunded_price' => '0.00',
                            'discount' => [],
                            'WarehouseProduct' => [
                                'id' => '2',
                                'warehouse_id' => '2',
                                'product_id' => '23',
                                'quantity' => '314',
                                'reserved_quantity' => '0',
                                'restock_date' => null,
                                'available_quantity' => '314',
                            ],
                            'InventoryTransferProduct' => [
                                'id' => null,
                                'inventory_transfer_id' => null,
                                'product_id' => null,
                                'quantity' => null,
                                'accepted_quantity' => null,
                                'remaining_quantity' => null,
                                'reserved_quantity' => null,
                                'available_quantity' => null,
                            ],
                        ],
                    ],
                    'Discount' => [
                        'id' => null,
                        'code' => null,
                        'description' => null,
                        'is_b2b_discount' => null,
                        'b2b_hide_ineligible_products' => null,
                        'retailer_option' => null,
                        'retailer_values' => null,
                        'enable_free_freight' => null,
                    ],
                    'CreditTerm' => [
                        'id' => null,
                        'description' => null,
                    ],
                    'ManufacturerRetailer' => [
                        'id' => null,
                        'b2b_tax' => null,
                        'enable_split_payment' => null,
                    ],
                    'OrderPayout' => [],
                    'OrderWarrantyImage' => [],
                ],
            ],
        ];
    }

    private function seedPartialOrderFulfillment($orderId): array
    {
        /** @var Fulfillment $Fulfillment */
        $Fulfillment = ClassRegistry::init('Fulfillment');

        $orderProductQuantityById = $Fulfillment->FulfillmentProduct->OrderProduct->find('list', [
            'recursive' => -1,
            'conditions' => ['OrderProduct.order_id' => $orderId],
            'fields' => ['OrderProduct.id', 'OrderProduct.quantity'],
            'order' => ['OrderProduct.id' => 'ASC'],
        ]);

        $data = [
            'Fulfillment' => [
                'id' => null,
                'order_id' => $orderId,
                'warehouse_id' => '1',
                'source_id' => null,
                'name' => $Fulfillment->findNextName($orderId, 'order_id'),
                'courier_id' => '82',
                'tracking_number' => '406973316728',
                'tracking_url' => 'https://www.fedex.com/fedextrack/?trknbr=406973316728',
                'created_at' => '2021-02-03 18:55:13',
                'updated_at' => '2021-02-03 18:55:13',
            ],
            'FulfillmentProduct' => [],
        ];
        foreach ($orderProductQuantityById as $orderProductId => $quantity) {
            $data['FulfillmentProduct'][] = [
                'id' => null,
                'order_product_id' => $orderProductId,
                'quantity' => '1',
                'created_at' => '2021-02-03 18:55:13',
                'updated_at' => '2021-02-03 18:55:13',
            ];
        }

        $success = $Fulfillment->saveAssociated($data);
        $this->assertTrue((bool)$success, json_encode(['errors' => $Fulfillment->validationErrors]));

        return $data;
    }
}
