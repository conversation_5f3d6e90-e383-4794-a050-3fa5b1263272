<?php
App::uses('AppTestCase', 'TestSuite');
App::uses('OrderPayout', 'Model');

/**
 * OrderPayout Test Case.
 *
 * @property OrderPayout $OrderPayout
 */
class OrderPayoutTest extends AppTestCase
{
    public $fixtures = [
        'app.order_payout',
        'app.order',
    ];

    public function setUp()
    {
        parent::setUp();
        $this->OrderPayout = ClassRegistry::init('OrderPayout');
    }

    public function tearDown()
    {
        unset($this->OrderPayout);
        parent::tearDown();
    }
}
