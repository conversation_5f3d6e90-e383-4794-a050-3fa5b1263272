<?php
App::uses('AppTestCase', 'TestSuite');
App::uses('PriceBasedShippingRate', 'Model');
App::uses('PercentSource', 'Utility');

/**
 * PriceBasedShippingRate Test Case.
 *
 * Run with the following console command:
 *
 * ./app/Console/cake test app Model/PriceBasedShippingRate
 *
 * @property PriceBasedShippingRate $PriceBasedShippingRate
 */
class PriceBasedShippingRateTest extends AppTestCase
{
    const USER_ID = '12';
    const ZONE_ID = '3';
    const RATE_ID = '2';

    public $fixtures = [
        'app.price_based_shipping_rate',
        'app.shipping_zone',
        'app.user',
        'app.price_based_shipping_rate_category',
    ];

    public function setUp()
    {
        parent::setUp();
        $this->PriceBasedShippingRate = ClassRegistry::init('PriceBasedShippingRate');
    }

    public function tearDown()
    {
        unset($this->PriceBasedShippingRate);
        parent::tearDown();
    }

    public function testGetPriceBasedShippingRates()
    {
        $actual = $this->PriceBasedShippingRate->getPriceBasedShippingRates(static::USER_ID, static::ZONE_ID);
        $expected = [
            [
                'PriceBasedShippingRate' => [
                    'id' => '2',
                    'name' => 'Premium Shipping',
                    'min' => '2000.00',
                    'max' => '10000.00',
                    'unit' => 'USD',
                    'amount_type' => 'flat',
                    'percent_source' => PercentSource::PRODUCT_PRICE,
                    'amount' => '10.00',
                    'product_category' => 'Cycling,Flowers',
                    'free_shipping' => false,
                ],
            ],
            [
                'PriceBasedShippingRate' => [
                    'id' => '3',
                    'name' => 'Free Shipping',
                    'min' => '3000.00',
                    'max' => '10000.00',
                    'unit' => 'USD',
                    'amount_type' => 'flat',
                    'percent_source' => PercentSource::PRODUCT_PRICE,
                    'amount' => '0.00',
                    'product_category' => 'Cycling',
                    'free_shipping' => true,
                ],
            ],
        ];
        $this->assertEquals($expected, $actual);
    }

    public function testAddPriceBasedShippingRate()
    {
        $data = [
            [
                'id' => 0,
                'name' => 'New name',
                'min' => '100.00',
                'max' => '200.00',
                'unit' => 'CAD',
                'amount_type' => 'percent',
                'percent_source' => PercentSource::COMPARE_AT_PRICE,
                'free_shipping' => false,
                'amount' => '50.00',
                'product_category' => 'Accessory,Cycling,Flowers',
            ],
        ];

        $this->PriceBasedShippingRate->saveSetFromShippingZoneForm(static::USER_ID, static::ZONE_ID, $data, []);

        $id = $this->PriceBasedShippingRate->getLastInsertID();
        $this->assertNotEquals(static::RATE_ID, $id);

        $actual = $this->PriceBasedShippingRate->find('first', [
            'contain' => [
                'ShippingRateCategory' => ['fields' => ['id', 'shipping_rate_id', 'product_category']]
            ],
            'conditions' => compact('id'),
        ]);

        $this->assertNotEquals('2017-08-31 04:21:05', $actual['PriceBasedShippingRate']['created_at']);
        unset($actual['PriceBasedShippingRate']['created_at']);
        $this->assertNotEquals('2017-08-31 04:21:05', $actual['PriceBasedShippingRate']['updated_at']);
        unset($actual['PriceBasedShippingRate']['updated_at']);

        $expected = [
            'PriceBasedShippingRate' => [
                'id' => $id,
                'user_id' => static::USER_ID,
                'zone_id' => static::ZONE_ID,
                'name' => 'New name',
                'min' => '100.00',
                'max' => '200.00',
                'unit' => 'CAD',
                'amount_type' => 'percent',
                'percent_source' => PercentSource::COMPARE_AT_PRICE,
                'free_shipping' => false,
                'amount' => '50.00',
                'product_category' => '',
            ],
            'ShippingRateCategory' => [
                [
                    'id' => '4',
                    'shipping_rate_id' => $id,
                    'product_category' => 'Accessory',
                ],
                [
                    'id' => '5',
                    'shipping_rate_id' => $id,
                    'product_category' => 'Cycling',
                ],
                [
                    'id' => '6',
                    'shipping_rate_id' => $id,
                    'product_category' => 'Flowers',
                ],
            ],
        ];
        $this->assertEquals($expected, $actual);
    }

    public function testUpdatePriceBasedShippingRate()
    {
        $data = [
            [
                'id' => static::RATE_ID,
                'name' => 'New name',
                'min' => '100.00',
                'max' => '200.00',
                'unit' => 'CAD',
                'amount_type' => 'percent',
                'percent_source' => PercentSource::COMPARE_AT_PRICE,
                'free_shipping' => false,
                'amount' => '50.00',
                'product_category' => 'Accessory,Cycling',
            ],
        ];

        $this->PriceBasedShippingRate->saveSetFromShippingZoneForm(static::USER_ID, static::ZONE_ID, $data, []);

        $id = $this->PriceBasedShippingRate->id;
        $this->assertEquals(static::RATE_ID, $id);

        $actual = $this->PriceBasedShippingRate->find('first', [
            'contain' => [
                'ShippingRateCategory' => ['fields' => ['id', 'shipping_rate_id', 'product_category']]
            ],
            'conditions' => compact('id'),
        ]);

        $this->assertEquals('2017-08-31 04:21:05', $actual['PriceBasedShippingRate']['created_at']);
        unset($actual['PriceBasedShippingRate']['created_at']);
        $this->assertNotEquals('2017-08-31 04:21:05', $actual['PriceBasedShippingRate']['updated_at']);
        unset($actual['PriceBasedShippingRate']['updated_at']);

        $expected = [
            'PriceBasedShippingRate' => [
                'id' => static::RATE_ID,
                'user_id' => static::USER_ID,
                'zone_id' => static::ZONE_ID,
                'name' => 'New name',
                'min' => '100.00',
                'max' => '200.00',
                'unit' => 'CAD',
                'amount_type' => 'percent',
                'percent_source' => PercentSource::COMPARE_AT_PRICE,
                'free_shipping' => false,
                'amount' => '50.00',
                'product_category' => '',
            ],
            'ShippingRateCategory' => [
                [
                    'id' => '4',
                    'shipping_rate_id' => $id,
                    'product_category' => 'Accessory',
                ],
                [
                    'id' => '2',
                    'shipping_rate_id' => $id,
                    'product_category' => 'Cycling',
                ],
            ],
        ];
        $this->assertEquals($expected, $actual);
    }

    public function testSaveFreeShippingRateForcesAmountToZero()
    {
        $data = [
            [
                'id' => static::RATE_ID,
                'name' => 'New name',
                'min' => '100.00',
                'max' => '200.00',
                'unit' => 'CAD',
                'amount_type' => 'percent',
                'percent_source' => PercentSource::PRODUCT_PRICE,
                'free_shipping' => true,
                'amount' => '50.00',
                'product_category' => 'Cycling,Flowers',
            ],
        ];

        $this->PriceBasedShippingRate->saveSetFromShippingZoneForm(static::USER_ID, static::ZONE_ID, $data, []);

        $id = $this->PriceBasedShippingRate->id;
        $this->assertEquals(static::RATE_ID, $id);

        $this->assertTrue($this->PriceBasedShippingRate->fieldByConditions('free_shipping', compact('id')));
        $this->assertEquals('0.00', $this->PriceBasedShippingRate->fieldByConditions('amount', compact('id')));
    }

    public function testDelete()
    {
        $data = [
            [
                'id' => static::RATE_ID,
            ]
        ];

        $this->PriceBasedShippingRate->saveSetFromShippingZoneForm(static::USER_ID, static::ZONE_ID, [], $data);

        $this->assertFalse($this->PriceBasedShippingRate->exists(['PriceBasedShippingRate.id' => static::RATE_ID]));
    }
}
