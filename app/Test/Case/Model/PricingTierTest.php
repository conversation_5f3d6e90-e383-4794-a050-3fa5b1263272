<?php
App::uses('AppTestCase', 'TestSuite');
App::uses('PricingTier', 'Model');

/**
 * PricingTier Test Case.
 *
 * Run with the following console command:
 *
 * ./app/Console/cake test app Model/PricingTier
 *
 * @property PricingTier $PricingTier
 */
class PricingTierTest extends AppTestCase
{
    const USER_ID_BRAND = '8';
    const PRODUCT_ID = '21';

    public $fixtures = [
        'app.collection',
        'app.i18n',
        'app.manufacturer_retailer',
        'app.pricing_tier',
        'app.pricing_tiers_collection',
        'app.pricing_tiers_hidden_warehouse',
        'app.product',
        'app.product_non_applicable_order_type',
        'app.product_state_fee',
        'app.product_tier',
        'app.product_title',
        'app.product_variant_option',
        'app.storefront',
        'app.storefront_slide',
        'app.user',
        'app.warehouse',
    ];

    public function setUp()
    {
        parent::setUp();
        $this->PricingTier = ClassRegistry::init('PricingTier');

        // Seed lower case name to assert for case formatting
        $this->PricingTier->save(['id' => '3', 'pricingtiername' => 'tier 3']);
        $this->assertEmpty($this->PricingTier->validationErrors);
        $this->PricingTier->clear();
    }

    public function tearDown()
    {
        unset($this->PricingTier);
        parent::tearDown();
    }

    public function testGetSelectOptions()
    {
        $expected = [
            1 => 'Tier 1',
            2 => 'Tier 2',
            3 => 'Tier 3',
        ];

        $actual = $this->PricingTier->getSelectOptions(static::USER_ID_BRAND);

        $this->assertSame($expected, $actual);
    }

    public function testListNames()
    {
        $expected = [
            1 => 'Tier 1',
            2 => 'Tier 2',
            3 => 'tier 3',
        ];

        $actual = $this->PricingTier->listNames(static::USER_ID_BRAND);

        $this->assertSame($expected, $actual);
    }

    public function testFindAllForPricingTierForm()
    {
        $expected = [
            'PricingTier' => [
                0 => [
                    'id' => '1',
                    'user_id' => '8',
                    'storefront_id' => null,
                    'pricingtiername' => 'Tier 1',
                    'currencytype' => 'CAD',
                    'hide_delete' => '1',
                    'warehouses' => [2],
                    'collections' => [1],
                ],
                1 => [
                    'id' => '2',
                    'user_id' => '8',
                    'storefront_id' => null,
                    'pricingtiername' => 'Tier 2',
                    'currencytype' => 'CAD',
                    'hide_delete' => '0',
                    'warehouses' => [2, 1],
                    'collections' => [],
                ],
                2 => [
                    'id' => '3',
                    'user_id' => '8',
                    'storefront_id' => null,
                    'pricingtiername' => 'tier 3',
                    'currencytype' => 'CAD',
                    'hide_delete' => '1',
                    'warehouses' => [2, 1],
                    'collections' => [],
                ],
            ],
        ];

        $actual = $this->PricingTier->findAllForPricingTierForm(static::USER_ID_BRAND);

        $this->assertEquals($expected, $actual);
    }

    public function testSavePricingTierForm()
    {
        $userId = static::USER_ID_BRAND;
        $data = [
            'PricingTier' => [
                // Tier 1 cannot be deleted
                // Tier 2 will be deleted
                2 => [
                    'id' => '3',
                    'pricingtiername' => 'Tier 3',
                    'currencytype' => 'CAD',
                    'warehouses' => ['2'],
                    'collections' => ['1'],
                    'storefront_id' => '',
                    'hide_delete' => '1',
                ],
                3 => [
                    'id' => '',
                    'pricingtiername' => 'Tier 4',
                    'currencytype' => 'USD',
                    'warehouses' => ['1'],
                    'collections' => ['1'],
                    'storefront_id' => '',
                    'hide_delete' => '',
                ],
            ],
        ];

        $success = $this->PricingTier->savePricingTierForm($userId, $data);
        $newId = $this->PricingTier->id;

        $this->PricingTier->bindModel(['hasMany' => ['PricingTiersCollection', 'PricingTiersHiddenWarehouse']], false);
        $actual = $this->PricingTier->find('all', [
            'contain' => [
                'PricingTiersCollection' => [
                    'fields' => ['pricing_tier_id', 'collection_id'],
                    'order' => ['PricingTiersCollection.collection_id' => 'ASC'],
                ],
                'PricingTiersHiddenWarehouse' => [
                    'fields' => ['pricing_tier_id', 'warehouse_id'],
                    'order' => ['PricingTiersHiddenWarehouse.warehouse_id' => 'ASC'],
                ],
            ],
            'conditions' => ['PricingTier.user_id' => $userId],
            'fields' => ['id', 'storefront_id', 'pricingtiername', 'currencytype'],
            'order' => ['PricingTier.pricingtiername' => 'ASC'],
        ]);
        $this->PricingTier->unbindModel(['hasMany' => ['PricingTiersCollection', 'PricingTiersHiddenWarehouse']], false);

        $expected = [
            0 => [
                'PricingTier' => [
                    'id' => '1',
                    'storefront_id' => null,
                    'pricingtiername' => 'Tier 1',
                    'currencytype' => 'CAD',
                ],
                'PricingTiersCollection' => [
                    0 => [
                        'pricing_tier_id' => '1',
                        'collection_id' => '1',
                    ],
                ],
                'PricingTiersHiddenWarehouse' => [
                    0 => [
                        'pricing_tier_id' => '1',
                        'warehouse_id' => '1',
                    ],
                ],
            ],
            1 => [
                'PricingTier' => [
                    'id' => '3',
                    'storefront_id' => null,
                    'pricingtiername' => 'Tier 3',
                    'currencytype' => 'CAD',
                ],
                'PricingTiersCollection' => [
                    0 => [
                        'pricing_tier_id' => '3',
                        'collection_id' => '1',
                    ],
                ],
                'PricingTiersHiddenWarehouse' => [
                    0 => [
                        'pricing_tier_id' => '3',
                        'warehouse_id' => '1',
                    ],
                ],
            ],
            2 => [
                'PricingTier' => [
                    'id' => $newId,
                    'storefront_id' => null,
                    'pricingtiername' => 'Tier 4',
                    'currencytype' => 'USD',
                ],
                'PricingTiersCollection' => [
                    0 => [
                        'pricing_tier_id' => $newId,
                        'collection_id' => '1',
                    ],
                ],
                'PricingTiersHiddenWarehouse' => [
                    0 => [
                        'pricing_tier_id' => $newId,
                        'warehouse_id' => '2',
                    ],
                ],
            ],
        ];
        $this->assertEquals($expected, $actual);
        $this->assertTrue($success, json_encode(['errors' => $this->PricingTier->validationErrors]));
    }
}
