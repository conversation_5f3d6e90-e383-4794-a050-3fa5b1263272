<?php
App::uses('AppTestCase', 'TestSuite');
App::uses('ProductStateFee', 'Model');
App::uses('OrderType', 'Utility');

/**
 * ProductStateFee Test Case.
 *
 * Run with the following console command:
 *
 * ./app/Console/cake test app Model/ProductStateFee
 *
 * @property ProductStateFee $ProductStateFee
 */
class ProductStateFeeTest extends AppTestCase
{
    const USER_ID_BRAND = '12';
    const USER_ID_RETAILER = '15';
    const USER_ID_BRANCH = '27';
    const DEFAULT_TAX_RATE = '0.10';

    const PRODUCT_IDS = [
        'BICYCLE' => '25',
        'VARIANT-1' => '41',
        'VARIANT-2' => '42',
        'ANCILLARY-FEES' => '69',
    ];
    const VARIANT_IDS = [
        'BICYCLE' => '10723507649',
        'VARIANT-1' => '27557890305',
        'VARIANT-2' => '27557890369',
        'ANCILLARY-FEES' => '33363926057005',
    ];
    const COUNTRY_IDS = ['US' => '233', 'CA' => '39', 'MX' => '159'];
    const STATE_IDS = ['BC' => '606', 'ON' => '611', 'CA' => '5197', 'PA' => '5231', 'ROO' => '3293'];

    public $fixtures = [
        'app.b2b_ship_to_address',
        'app.brand_staff_permission',
        'app.manufacturer_retailer',
        'app.product',
        'app.product_non_applicable_order_type',
        'app.product_state_fee',
        'app.product_title',
        'app.product_variant_option',
        'app.staff_permission',
        'app.state',
        'app.user',
    ];

    public function setUp()
    {
        parent::setUp();
        $this->ProductStateFee = ClassRegistry::init('ProductStateFee');
    }

    public function tearDown()
    {
        unset($this->ProductStateFee);
        parent::tearDown();
    }

    public function testSaveNegativeFee()
    {
        $productId = static::PRODUCT_IDS['VARIANT-1'];
        $stateId = static::STATE_IDS['ON'];

        $actual = $this->ProductStateFee->save([
            'id' => $this->ProductStateFee->fieldByConditions('id', [
                'product_id' => $productId,
                'state_id' => $stateId,
            ]),
            'fee_amount' => '-0.01',
        ]);

        $this->assertFalse($actual, 'Save succeeded');
        $this->assertEquals(['fee_amount' => ['Fee cannot be negative']], $this->ProductStateFee->validationErrors, 'Missing validationErrors');
    }

    /**
     * @dataProvider providerApplyToDealerQtyOrderedProducts
     */
    public function testApplyToDealerQtyOrderedProducts($order, $dealer_qty_ordered, $expected)
    {
        $actual = $this->ProductStateFee->applyToDealerQtyOrderedProducts($order, $dealer_qty_ordered);

        $this->assertEquals($expected, $actual);
    }

    public function providerApplyToDealerQtyOrderedProducts(): array
    {
        $order = [
            'Order' => [
                'id' => '1',
                'user_id' => static::USER_ID_BRAND,
                'retailer_id' => static::USER_ID_RETAILER,
                'branch_id' => null,
                'b2b_ship_to_user_id' => null,
                'order_type' => OrderType::IN_STORE_PICKUP,
                'shipping_statecode' => static::STATE_IDS['ON'],
            ],
            'ManufacturerRetailer' => [
                'id' => '6',
                'b2b_tax' => '5.0000',
            ],
        ];
        $dealer_qty_ordered = [
            'products' => [
                static::PRODUCT_IDS['VARIANT-1'] => [
                    'quantity' => 2,
                    'dealer_price' => '0.00',
                    'warehouse_id' => null,
                ],
                static::PRODUCT_IDS['VARIANT-2'] => [
                    'quantity' => 3,
                    'dealer_price' => '0.00',
                    'warehouse_id' => '3',
                ],
            ],
            'currencytype' => 'CAD',
            'b2b_tax' => '5.0000',
        ];
        $expected = [
            'products' => [
                static::PRODUCT_IDS['VARIANT-1'] => [
                    'quantity' => 2,
                    'dealer_price' => '0.00',
                    'warehouse_id' => null,
                ],
                static::PRODUCT_IDS['VARIANT-2'] => [
                    'quantity' => 3,
                    'dealer_price' => '0.00',
                    'warehouse_id' => '3',
                ],
                static::PRODUCT_IDS['ANCILLARY-FEES'] => [
                    'quantity' => 5,
                    'dealer_price' => '2.53',
                    'tax_rate' => '0.026303',
                ],
            ],
            'currencytype' => 'CAD',
            'b2b_tax' => '5.0000',
        ];

        return [
            'add' => [
                'order' => $order,
                'dealer_qty_ordered' => $dealer_qty_ordered,
                'expected' => $expected,
            ],
            'update' => [
                'order' => $order,
                'dealer_qty_ordered' => (function($dealer_qty_ordered) {
                    $dealer_qty_ordered['products'][static::PRODUCT_IDS['ANCILLARY-FEES']] = [
                        'quantity' => 1,
                        'dealer_price' => '4.00',
                        'warehouse_id' => '3',
                        'tax_rate' => '0.055000',
                    ];

                    return $dealer_qty_ordered;
                })($dealer_qty_ordered),
                'expected' => (function($expected) {
                    $expected['products'][static::PRODUCT_IDS['ANCILLARY-FEES']] = [
                        'quantity' => 5,
                        'dealer_price' => '2.53',
                        'warehouse_id' => '3',
                        'tax_rate' => '0.026303',
                    ];

                    return $expected;
                })($expected),
            ],
            'remove' => [
                'order' => $order,
                'dealer_qty_ordered' => [
                    'products' => [
                        static::PRODUCT_IDS['BICYCLE'] => [
                            'quantity' => 1,
                            'dealer_price' => '349.99',
                            'warehouse_id' => '3',
                        ],
                        static::PRODUCT_IDS['ANCILLARY-FEES'] => [
                            'quantity' => 5,
                            'dealer_price' => '2.53',
                            'tax_rate' => '0.026303',
                        ],
                    ],
                    'currencytype' => 'CAD',
                    'b2b_tax' => '5.0000',
                ],
                'expected' => [
                    'products' => [
                        static::PRODUCT_IDS['BICYCLE'] => [
                            'quantity' => 1,
                            'dealer_price' => '349.99',
                            'warehouse_id' => '3',
                        ],
                    ],
                    'currencytype' => 'CAD',
                    'b2b_tax' => '5.0000',
                ],
            ],
            'invalid' => [
                'order' => $order,
                'dealer_qty_ordered' => ['foo' => 'bar'],
                'expected' => ['foo' => 'bar'],
            ],
        ];
    }

    /**
     * @dataProvider providerGetStateIdFromOrder
     */
    public function testGetStateIdFromOrder($order, $expected)
    {
        $actual = $this->callPrivateMethod([$this->ProductStateFee, '_getStateIdFromOrder'], $order);

        $this->assertEquals($expected, $actual);
    }

    public function providerGetStateIdFromOrder(): array
    {
        return [
            'ship_to_store' => [
                'order' => [
                    'Order' => [
                        'id' => '1',
                        'user_id' => static::USER_ID_BRAND,
                        'retailer_id' => static::USER_ID_RETAILER,
                        'branch_id' => null,
                        'b2b_ship_to_user_id' => null,
                        'order_type' => OrderType::IN_STORE_PICKUP,
                        'shipping_statecode' => static::STATE_IDS['CA'],
                    ],
                ],
                'expected' => static::STATE_IDS['ON'],
            ],
            'ship_from_store' => [
                'order' => [
                    'Order' => [
                        'id' => '1',
                        'user_id' => static::USER_ID_BRAND,
                        'retailer_id' => static::USER_ID_RETAILER,
                        'branch_id' => null,
                        'b2b_ship_to_user_id' => null,
                        'order_type' => OrderType::SHIP_FROM_STORE,
                        'shipping_statecode' => static::STATE_IDS['CA'],
                    ],
                ],
                'expected' => static::STATE_IDS['CA'],
            ],
            'wholesale' => [
                'order' => [
                    'Order' => [
                        'id' => '1',
                        'user_id' => static::USER_ID_BRAND,
                        'retailer_id' => static::USER_ID_BRANCH,
                        'branch_id' => static::USER_ID_BRANCH,
                        'b2b_ship_to_user_id' => static::USER_ID_RETAILER,
                        'order_type' => OrderType::WHOLESALE,
                        'shipping_statecode' => static::STATE_IDS['CA'],
                    ],
                ],
                'expected' => static::STATE_IDS['CA'],
            ],
        ];
    }

    /**
     * @dataProvider providerGetStateIdFromOrderNotice
     */
    public function testGetStateIdFromOrderNotice($order, $expectedNotice, $expected)
    {
        /**
         * @var Order|PHPUnit_Framework_MockObject_MockObject $Order
         * @see Order::field()
         * @see Order::initializeWithAssociatedModels()
         */
        $Order = $this->getMockForModel('Order', ['field', 'initializeWithAssociatedModels']);
        $Order->method('field')->with('shipping_statecode', ['Order.id' => '1'])->willReturn(static::STATE_IDS['CA']);

        $noticeEnabled = PHPUnit_Framework_Error_Notice::$enabled;
        PHPUnit_Framework_Error_Notice::$enabled = false;
        set_error_handler(function($code, $message) use ($expectedNotice) {
            $this->assertEquals($expectedNotice, $message);
        });

        try {
            $actual = $this->callPrivateMethod([$this->ProductStateFee, '_getStateIdFromOrder'], $order);

            $this->assertEquals($expected, $actual);
        } finally {
            restore_error_handler();
            PHPUnit_Framework_Error_Notice::$enabled = $noticeEnabled;
        }
    }

    public function providerGetStateIdFromOrderNotice(): array
    {
        return [
            'sell_direct' => [
                'order' => [
                    'Order' => [
                        'id' => '1',
                        'user_id' => static::USER_ID_BRAND,
                        'retailer_id' => null,
                        'branch_id' => null,
                        'b2b_ship_to_user_id' => null,
                        'order_type' => OrderType::SHIPEARLY,
                        'shipping_statecode' => 'CA',
                    ],
                ],
                'expectedNotice' => 'Undefined index: Retailer',
                'expected' => 0,
            ],
            'missing_shipping_statecode' => [
                'order' => [
                    'Order' => [
                        'id' => '1',
                        'user_id' => static::USER_ID_BRAND,
                        'retailer_id' => static::USER_ID_BRANCH,
                        'branch_id' => static::USER_ID_BRANCH,
                        'b2b_ship_to_user_id' => static::USER_ID_RETAILER,
                        'order_type' => OrderType::WHOLESALE,
                    ],
                ],
                'expectedNotice' => 'Undefined index: shipping_statecode',
                'expected' => static::STATE_IDS['CA'],
            ],
        ];
    }

    public function testAddFeeToShopifyCart()
    {
        $cart = [
            'original_total_price' => 119999,
            'total_price' => 119999,
            'item_count' => 6,
            'items' => [
                0 => [
                    'id' => (int)static::VARIANT_IDS['VARIANT-1'],
                    'quantity' => 2,
                    'variant_id' => (int)static::VARIANT_IDS['VARIANT-1'],
                    'line_price' => 30000,
                ],
                1 => [
                    'id' => (int)static::VARIANT_IDS['VARIANT-2'],
                    'quantity' => 3,
                    'variant_id' => (int)static::VARIANT_IDS['VARIANT-2'],
                    'line_price' => 20000,
                ],
                2 => [
                    'id' => (int)static::VARIANT_IDS['BICYCLE'],
                    'quantity' => 1,
                    'variant_id' => (int)static::VARIANT_IDS['BICYCLE'],
                    'line_price' => 69999,
                ],
            ],
            'items_subtotal_price' => 119999,
        ];

        $actual = $this->ProductStateFee->applyFeeToShopifyCart(static::USER_ID_BRAND, static::COUNTRY_IDS['CA'], static::STATE_IDS['ON'], $cart);

        $variantId = (int)static::VARIANT_IDS['ANCILLARY-FEES'];
        $unitFeeCents = 400;
        $totalFeeCents = 2000;

        $expected = [
            'original_total_price' => 121999,
            'total_price' => 121999,
            'item_count' => 11,
            'items' => array_merge($cart['items'], [
                [
                    'id' => $variantId,
                    'properties' => null,
                    'quantity' => 5,
                    'variant_id' => $variantId,
                    // Don't know Shopify's hash logic for 2nd half of 'key'
                    'key' => $variantId . ':',
                    'title' => 'Ancillary Fees',
                    'price' => $unitFeeCents,
                    'original_price' => $unitFeeCents,
                    'discounted_price' => $unitFeeCents,
                    'line_price' => $totalFeeCents,
                    'original_line_price' => $totalFeeCents,
                    'total_discount' => 0,
                    'discounts' => [],
                    'sku' => 'ANCILLARY-FEES',
                    'grams' => 0,
                    // 'vendor' should be saved to products but isn't
                    'vendor' => null, //'sirisha',
                    'taxable' => true,
                    'product_id' => *************,
                    'product_has_only_default_variant' => true,
                    'gift_card' => false,
                    'final_price' => $unitFeeCents,
                    'final_line_price' => $totalFeeCents,
                    // 'url' is not known to us
                    'url' => null, //'/products/ancillary-fees?variant=33363926057005',
                    // We don't know most 'featured_image' attributes nor do we use them
                    'featured_image' => [
                        'aspect_ratio' => null, //1.0,
                        'alt' => 'Ancillary Fees',
                        'height' => null, //900,
                        'url' => 'https://cdn.shopify.com/s/files/1/1091/1264/products/kisspng-fee-price-cost-service-tax-5adb8930b78e25.6644791015243369447519.jpg?v=1585068369',
                        'width' => null, //900,
                    ],
                    'image' => 'https://cdn.shopify.com/s/files/1/1091/1264/products/kisspng-fee-price-cost-service-tax-5adb8930b78e25.6644791015243369447519.jpg?v=1585068369',
                    // 'handle' is not saved to products but should be in case the brand changes it from the default
                    'handle' => 'ancillary-fees',
                    'requires_shipping' => false,
                    'product_type' => '',
                    'product_title' => 'Ancillary Fees',
                    'product_description' => 'Order fees required by legislation in select regions.',
                    'variant_title' => null,
                    'variant_options' => ['Default Title'],
                    'options_with_values' => [
                        ['name' => 'Title', 'value' => 'Default Title'],
                    ],
                    'line_level_discount_allocations' => [],
                    'line_level_total_discount' => 0,
                ],
            ]),
            'items_subtotal_price' => 121999,
        ];
        $this->assertEquals($expected, $actual);
    }

    public function testAddNonTaxableFeeToShopifyCart()
    {
        $cart = [
            'items' => [
                [
                    'id' => (int)static::VARIANT_IDS['VARIANT-2'],
                    'quantity' => 3,
                    'variant_id' => (int)static::VARIANT_IDS['VARIANT-2'],
                    'line_price' => 20000,
                ],
                [
                    'id' => (int)static::VARIANT_IDS['BICYCLE'],
                    'quantity' => 1,
                    'variant_id' => (int)static::VARIANT_IDS['BICYCLE'],
                    'line_price' => 69999,
                ],
            ],
        ];

        $actual = $this->ProductStateFee->applyFeeToShopifyCart(static::USER_ID_BRAND, static::COUNTRY_IDS['CA'], static::STATE_IDS['ON'], $cart);

        $this->assertFalse($actual['items'][2]['taxable'], 'Fee cart item taxable field');
    }

    public function testChangingStateRemovesFeeFromShopifyCart()
    {
        $differentCountryId = static::COUNTRY_IDS['US'];
        $differentStateId = static::STATE_IDS['CA'];

        $cart = [
            'original_total_price' => 121998,
            'total_price' => 121998,
            'item_count' => 7,
            'items' => [
                0 => [
                    'id' => (int)static::VARIANT_IDS['VARIANT-1'],
                    'quantity' => 2,
                    'variant_id' => (int)static::VARIANT_IDS['VARIANT-1'],
                    'line_price' => 30000,
                ],
                1 => [
                    'id' => (int)static::VARIANT_IDS['VARIANT-2'],
                    'quantity' => 3,
                    'variant_id' => (int)static::VARIANT_IDS['VARIANT-2'],
                    'line_price' => 20000,
                ],
                2 => [
                    'id' => (int)static::VARIANT_IDS['BICYCLE'],
                    'quantity' => 1,
                    'variant_id' => (int)static::VARIANT_IDS['BICYCLE'],
                    'line_price' => 69999,
                ],
                3 => [
                    'id' => (int)static::VARIANT_IDS['ANCILLARY-FEES'],
                    'quantity' => 1,
                    'variant_id' => (int)static::VARIANT_IDS['ANCILLARY-FEES'],
                    'line_price' => 1999,
                ],
            ],
            'items_subtotal_price' => 121998,
        ];

        $actual = $this->ProductStateFee->applyFeeToShopifyCart(static::USER_ID_BRAND, $differentCountryId, $differentStateId, $cart);

        $expectedItems = $cart['items'];
        unset($expectedItems[3]);

        $expected = [
            'original_total_price' => 119999,
            'total_price' => 119999,
            'item_count' => 6,
            'items' => $expectedItems,
            'items_subtotal_price' => 119999,
        ];
        $this->assertEquals($expected, $actual);
    }

    public function testChangingItemsRemovesFeeFromShopifyCart()
    {
        $cart = [
            'original_total_price' => 71998,
            'total_price' => 71998,
            'item_count' => 2,
            'items' => [
                [
                    'id' => (int)static::VARIANT_IDS['BICYCLE'],
                    'quantity' => 1,
                    'variant_id' => (int)static::VARIANT_IDS['BICYCLE'],
                    'line_price' => 69999,
                ],
                [
                    'id' => (int)static::VARIANT_IDS['ANCILLARY-FEES'],
                    'quantity' => 1,
                    'variant_id' => (int)static::VARIANT_IDS['ANCILLARY-FEES'],
                    'line_price' => 1999,
                ],
            ],
            'items_subtotal_price' => 71998,
        ];

        $actual = $this->ProductStateFee->applyFeeToShopifyCart(static::USER_ID_BRAND, static::COUNTRY_IDS['CA'], static::STATE_IDS['ON'], $cart);

        $expected = [
            'original_total_price' => 69999,
            'total_price' => 69999,
            'item_count' => 1,
            'items' => [
                [
                    'id' => (int)static::VARIANT_IDS['BICYCLE'],
                    'quantity' => 1,
                    'variant_id' => (int)static::VARIANT_IDS['BICYCLE'],
                    'line_price' => 69999,
                ],
            ],
            'items_subtotal_price' => 69999,
        ];
        $this->assertEquals($expected, $actual);
    }

    public function testZeroFeeRemovesFeeFromShopifyCart()
    {
        $this->ProductStateFee->updateAllJoinless(['fee_amount' => '0.00'], [
            'product_id' => static::PRODUCT_IDS,
            'state_id' => static::STATE_IDS['ON'],
        ]);

        $cart = [
            'original_total_price' => 121998,
            'total_price' => 121998,
            'item_count' => 7,
            'items' => [
                0 => [
                    'id' => (int)static::VARIANT_IDS['VARIANT-1'],
                    'quantity' => 2,
                    'variant_id' => (int)static::VARIANT_IDS['VARIANT-1'],
                    'line_price' => 30000,
                ],
                1 => [
                    'id' => (int)static::VARIANT_IDS['VARIANT-2'],
                    'quantity' => 3,
                    'variant_id' => (int)static::VARIANT_IDS['VARIANT-2'],
                    'line_price' => 20000,
                ],
                2 => [
                    'id' => (int)static::VARIANT_IDS['BICYCLE'],
                    'quantity' => 1,
                    'variant_id' => (int)static::VARIANT_IDS['BICYCLE'],
                    'line_price' => 69999,
                ],
                3 => [
                    'id' => (int)static::VARIANT_IDS['ANCILLARY-FEES'],
                    'quantity' => 1,
                    'variant_id' => (int)static::VARIANT_IDS['ANCILLARY-FEES'],
                    'line_price' => 1999,
                ],
            ],
            'items_subtotal_price' => 121998,
        ];

        $actual = $this->ProductStateFee->applyFeeToShopifyCart(static::USER_ID_BRAND, static::COUNTRY_IDS['CA'], static::STATE_IDS['ON'], $cart);

        $expectedItems = $cart['items'];
        unset($expectedItems[3]);

        $expected = [
            'original_total_price' => 119999,
            'total_price' => 119999,
            'item_count' => 6,
            'items' => $expectedItems,
            'items_subtotal_price' => 119999,
        ];
        $this->assertEquals($expected, $actual);
    }

    public function testAddFeeToPosRetailer()
    {
        $retailers_inv_user = [
            (int)static::VARIANT_IDS['VARIANT-1'] => [
                'qty' => 2,
            ],
            (int)static::VARIANT_IDS['VARIANT-2'] => [
                'qty' => 3,
            ],
            (int)static::VARIANT_IDS['BICYCLE'] => [
                'qty' => 1,
            ],
        ];

        $actual = $this->ProductStateFee->applyFeeToPosRetailer(
            $retailers_inv_user,
            static::USER_ID_BRAND,
            static::COUNTRY_IDS['CA'],
            static::STATE_IDS['ON'],
            'USD',
            static::DEFAULT_TAX_RATE
        );

        $unitFee = '4.00';
        $totalFee = '20.00';
        $totalFeeTax = '1.00';

        $expected = $retailers_inv_user;
        $expected[static::VARIANT_IDS['ANCILLARY-FEES']] = [
            'id' => static::PRODUCT_IDS['ANCILLARY-FEES'],
            'inventoryId' => (int)static::VARIANT_IDS['ANCILLARY-FEES'],
            'inventory' => 5,
            'currency' => 'USD',
            'isTax' => true,
            'tax_included' => false,
            'taxcode' => '',
            'qty' => 5,
            'unformatedunitprice' => $unitFee,
            'unitprice' => "<span class='Currency' style='white-space: nowrap;'>USD {$unitFee}</span>",
            'unformatedprice' => $totalFee,
            'price' => "<span class='Currency' style='white-space: nowrap;'>USD {$totalFee}</span>",
            'tax' => $totalFeeTax,
            'unformatedunitpricediscount' => $unitFee,
            'unitpricediscount' => "<span class='Currency' style='white-space: nowrap;'>USD {$unitFee}</span>",
            'unformatedpricediscount' => $totalFee,
            'pricediscount' => "<span class='Currency' style='white-space: nowrap;'>USD {$totalFee}</span>",
            'taxdiscount' => $totalFeeTax,
            'title' => 'Ancillary Fees',
            'isFeeProduct' => true
        ];
        $this->assertEquals($expected, $actual);
    }

    public function testAddNonTaxableFeeToPosRetailer()
    {
        $retailers_inv_user = [
            (int)static::VARIANT_IDS['VARIANT-2'] => [
                'qty' => 3,
            ],
            (int)static::VARIANT_IDS['BICYCLE'] => [
                'qty' => 1,
            ],
        ];

        $result = $this->ProductStateFee->applyFeeToPosRetailer(
            $retailers_inv_user,
            static::USER_ID_BRAND,
            static::COUNTRY_IDS['CA'],
            static::STATE_IDS['ON'],
            'USD',
            static::DEFAULT_TAX_RATE
        );
        $actual = array_intersect_key($result[static::VARIANT_IDS['ANCILLARY-FEES']], array_flip(['isTax', 'tax', 'taxdiscount']));

        $expected = [
            'isTax' => false,
            'tax' => '0.00',
            'taxdiscount' => '0.00',
        ];
        $this->assertEquals($expected, $actual);
    }

    public function testAddNoFeeToPosRetailer()
    {
        $differentCountryId = static::COUNTRY_IDS['US'];
        $differentStateId = static::STATE_IDS['CA'];

        $retailers_inv_user = [
            (int)static::VARIANT_IDS['VARIANT-1'] => [
                'qty' => 2,
            ],
            (int)static::VARIANT_IDS['VARIANT-2'] => [
                'qty' => 3,
            ],
            (int)static::VARIANT_IDS['BICYCLE'] => [
                'qty' => 1,
            ],
        ];

        $actual = $this->ProductStateFee->applyFeeToPosRetailer(
            $retailers_inv_user,
            static::USER_ID_BRAND,
            $differentCountryId,
            $differentStateId,
            'USD',
            static::DEFAULT_TAX_RATE
        );

        $this->assertEquals($retailers_inv_user, $actual);
    }

    public function testAddZeroFeeToPosRetailer()
    {
        $countryId = static::STATE_IDS['CA'];
        $stateId = static::STATE_IDS['ON'];
        $this->ProductStateFee->updateAllJoinless(['fee_amount' => '0.00'], [
            'product_id' => static::PRODUCT_IDS,
            'state_id' => $stateId,
        ]);

        $retailers_inv_user = [
            (int)static::VARIANT_IDS['VARIANT-1'] => [
                'qty' => 2,
            ],
            (int)static::VARIANT_IDS['VARIANT-2'] => [
                'qty' => 3,
            ],
            (int)static::VARIANT_IDS['BICYCLE'] => [
                'qty' => 1,
            ],
        ];

        $actual = $this->ProductStateFee->applyFeeToPosRetailer(
            $retailers_inv_user,
            static::USER_ID_BRAND,
            $countryId,
            $stateId,
            'USD',
            static::DEFAULT_TAX_RATE
        );

        $this->assertEquals($retailers_inv_user, $actual);
    }

    public function testUpdateFeeInPosRetailer()
    {
        // This shouldn't actually come up but lets prove the inventory adjustment isn't hard coded
        $inventory = 6;
        $qty = 5;
        $retailerCurrency = 'USD';
        $posInventory = [
            'inventoryId' => 1001,
            'inventory' => $inventory,
            // Pos handlers error out if there is a 'currency' mismatch so this case shouldn't actually come up
            // but lets prove that the value is preserved
            'currency' => 'CAD',
            'isTax' => false,
            'tax_rate' => '0.20',
        ];

        $retailers_inv_user = [
            (int)static::VARIANT_IDS['VARIANT-1'] => [
                'qty' => 2,
            ],
            (int)static::VARIANT_IDS['VARIANT-2'] => [
                'qty' => 3,
            ],
            (int)static::VARIANT_IDS['BICYCLE'] => [
                'qty' => 1,
            ],
            (int)static::VARIANT_IDS['ANCILLARY-FEES'] => [
                'id' => static::PRODUCT_IDS['ANCILLARY-FEES'],
                'inventoryId' => $posInventory['inventoryId'],
                'inventory' => $posInventory['inventory'],
                'currency' => $posInventory['currency'],
                'isTax' => $posInventory['isTax'],
                'taxcode' => '',
                'tax_rate' => $posInventory['tax_rate'],
                'qty' => $qty,
                'unformatedunitprice' => '0.00',
                'unitprice' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>USD 0.00</span>',
                'unformatedprice' => '0.00',
                'price' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>USD 0.00</span>',
                'tax' => '0.00',
                'unformatedunitpricediscount' => '0.00',
                'unitpricediscount' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>USD 0.00</span>',
                'unformatedpricediscount' => '0.00',
                'pricediscount' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>USD 0.00</span>',
                'taxdiscount' => '0.00',
                'a custom field' => 'is preserved',
            ],
        ];

        $actual = $this->ProductStateFee->applyFeeToPosRetailer(
            $retailers_inv_user,
            static::USER_ID_BRAND,
            static::COUNTRY_IDS['CA'],
            static::STATE_IDS['ON'],
            $retailerCurrency,
            static::DEFAULT_TAX_RATE
        );

        $unitFee = '4.00';
        $totalFee = '20.00';
        $totalFeeTax = '2.00';

        $expected = $retailers_inv_user;
        $expected[static::VARIANT_IDS['ANCILLARY-FEES']] = [
            'id' => static::PRODUCT_IDS['ANCILLARY-FEES'],
            'inventoryId' => 1001,
            'inventory' => $inventory,
            'currency' => 'CAD',
            'isTax' => true,
            'tax_included' => false,
            'taxcode' => '',
            'tax_rate' => '0.20',
            'qty' => $qty,
            'unformatedunitprice' => $unitFee,
            'unitprice' => "<span class='Currency' style='white-space: nowrap;'>USD {$unitFee}</span>",
            'unformatedprice' => $totalFee,
            'price' => "<span class='Currency' style='white-space: nowrap;'>USD {$totalFee}</span>",
            'tax' => $totalFeeTax,
            'unformatedunitpricediscount' => $unitFee,
            'unitpricediscount' => "<span class='Currency' style='white-space: nowrap;'>USD {$unitFee}</span>",
            'unformatedpricediscount' => $totalFee,
            'pricediscount' => "<span class='Currency' style='white-space: nowrap;'>USD {$totalFee}</span>",
            'taxdiscount' => $totalFeeTax,
            'a custom field' => 'is preserved',
            'title' => 'Ancillary Fees',
            'isFeeProduct' => true
        ];
        $this->assertEquals($expected, $actual);
    }

    public function testUpdateFeeInEcommerceRetailer()
    {
        $retailers_inv_user = [
            (int)static::VARIANT_IDS['VARIANT-1'] => [
                'qty' => 2,
            ],
            (int)static::VARIANT_IDS['VARIANT-2'] => [
                'qty' => 3,
            ],
            (int)static::VARIANT_IDS['BICYCLE'] => [
                'qty' => 1,
            ],
            (int)static::VARIANT_IDS['ANCILLARY-FEES'] => [
                'id' => static::PRODUCT_IDS['ANCILLARY-FEES'],
                'inventoryId' => (int)static::VARIANT_IDS['ANCILLARY-FEES'],
                'inventory' => 0,
                'weight' => 0.0,
                'currency' => 'USD',
                'isTax' => true,
                'tax_included' => false,
                'tax_name' => 'Tax',
                'tax_rate' => 0.1,
                'shipping_tax_name' => 'Shipping Tax',
                'shipping_tax_rate' => 0.1,
                'taxcode' => '',
                'assembly_option' => '0',
                'qty' => 1,
                'unformatedunitprice' => '0.00',
                'unitprice' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>USD 0.00</span>',
                'unformatedprice' => '0.00',
                'price' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>USD 0.00</span>',
                'tax' => '0.00',
                'unformatedunitpricediscount' => '0.00',
                'unitpricediscount' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>USD 0.00</span>',
                'unformatedpricediscount' => '0.00',
                'pricediscount' => '<span class=\'Currency\' style=\'white-space: nowrap;\'>USD 0.00</span>',
                'taxdiscount' => '0.00',
            ],
        ];

        $actual = $this->ProductStateFee->applyFeeToPosRetailer(
            $retailers_inv_user,
            static::USER_ID_BRAND,
            static::COUNTRY_IDS['CA'],
            static::STATE_IDS['ON'],
            'USD'
        );

        $unitFee = '4.00';
        $totalFee = '20.00';
        $totalFeeTax = '1.00';

        $expected = $retailers_inv_user;
        $expected[static::VARIANT_IDS['ANCILLARY-FEES']] = [
            'id' => static::PRODUCT_IDS['ANCILLARY-FEES'],
            'inventoryId' => (int)static::VARIANT_IDS['ANCILLARY-FEES'],
            'inventory' => 5,
            'weight' => 0.0,
            'currency' => 'USD',
            'isTax' => true,
            'tax_included' => false,
            'tax_name' => 'Tax',
            'tax_rate' => 0.1,
            'shipping_tax_name' => 'Shipping Tax',
            'shipping_tax_rate' => 0.1,
            'taxcode' => '',
            'assembly_option' => '0',
            'qty' => 5,
            'unformatedunitprice' => $unitFee,
            'unitprice' => "<span class='Currency' style='white-space: nowrap;'>USD {$unitFee}</span>",
            'unformatedprice' => $totalFee,
            'price' => "<span class='Currency' style='white-space: nowrap;'>USD {$totalFee}</span>",
            'tax' => $totalFeeTax,
            'unformatedunitpricediscount' => $unitFee,
            'unitpricediscount' => "<span class='Currency' style='white-space: nowrap;'>USD {$unitFee}</span>",
            'unformatedpricediscount' => $totalFee,
            'pricediscount' => "<span class='Currency' style='white-space: nowrap;'>USD {$totalFee}</span>",
            'taxdiscount' => $totalFeeTax,
            'title' => 'Ancillary Fees',
            'isFeeProduct' => true
        ];
        $this->assertEquals($expected, $actual);
    }

    /**
     * @dataProvider providerCalculateFeeProductPrices
     */
    public function testCalculateFeeProductPrices($quantityByProductId, $expected)
    {
        $brandId = static::USER_ID_BRAND;
        $countryId = static::COUNTRY_IDS['CA'];
        $stateId = static::STATE_IDS['ON'];

        $actual = $this->ProductStateFee->calculateFeeProductPrices($brandId, $countryId, $stateId, $quantityByProductId);

        $this->assertEquals($expected, $actual);
    }

    public function providerCalculateFeeProductPrices(): array
    {
        $feeProductId = static::PRODUCT_IDS['ANCILLARY-FEES'];
        $taxableProductId = static::PRODUCT_IDS['VARIANT-1'];
        $nonTaxableProductId = static::PRODUCT_IDS['VARIANT-2'];

        return [
            'taxable' => [
                'quantityByProductId' => [
                    $taxableProductId => 2,
                ],
                'expected' => [
                    $feeProductId => [
                        'id' => $feeProductId,
                        'quantity' => 2,
                        'product_price' => '5.0000',
                        'dealer_price' => '3.3300',
                        'is_taxable' => true,
                        'taxable_product_price' => '5.0000',
                        'taxable_dealer_price' => '3.3300',
                    ],
                ],
            ],
            'zero_taxable' => [
                'quantityByProductId' => [
                    $taxableProductId => 0,
                ],
                'expected' => [
                    $feeProductId => [
                        'id' => $feeProductId,
                        'quantity' => 0,
                        'product_price' => '5.0000',
                        'dealer_price' => '3.3300',
                        'is_taxable' => true,
                        'taxable_product_price' => '5.0000',
                        'taxable_dealer_price' => '3.3300',
                    ],
                ],
            ],
            'taxable_with_zero_non_taxable' => [
                'quantityByProductId' => [
                    $taxableProductId => 2,
                    $nonTaxableProductId => 0,
                ],
                'expected' => [
                    $feeProductId => [
                        'id' => $feeProductId,
                        'quantity' => 2,
                        'product_price' => '5.0000',
                        'dealer_price' => '3.3300',
                        'is_taxable' => true,
                        'taxable_product_price' => '5.0000',
                        'taxable_dealer_price' => '3.3300',
                    ],
                ],
            ],
            'non_taxable' => [
                'quantityByProductId' => [
                    $nonTaxableProductId => 2,
                ],
                'expected' => [
                    $feeProductId => [
                        'id' => $feeProductId,
                        'quantity' => 2,
                        'product_price' => '3.3300',
                        'dealer_price' => '2.0000',
                        'is_taxable' => false,
                        'taxable_product_price' => '0.0000',
                        'taxable_dealer_price' => '0.0000',
                    ],
                ],
            ],
            'zero_non_taxable' => [
                'quantityByProductId' => [
                    $nonTaxableProductId => 0,
                ],
                'expected' => [
                    $feeProductId => [
                        'id' => $feeProductId,
                        'quantity' => 0,
                        'product_price' => '3.3300',
                        'dealer_price' => '2.0000',
                        'is_taxable' => false,
                        'taxable_product_price' => '0.0000',
                        'taxable_dealer_price' => '0.0000',
                    ],
                ],
            ],
            'non_taxable_with_zero_taxable' => [
                'quantityByProductId' => [
                    $taxableProductId => 0,
                    $nonTaxableProductId => 2,
                ],
                'expected' => [
                    $feeProductId => [
                        'id' => $feeProductId,
                        'quantity' => 2,
                        'product_price' => '3.3300',
                        'dealer_price' => '2.0000',
                        'is_taxable' => false,
                        'taxable_product_price' => '0.0000',
                        'taxable_dealer_price' => '0.0000',
                    ],
                ],
            ],
            'combined_with_equal_quantity' => [
                'quantityByProductId' => [
                    $taxableProductId => 2,
                    $nonTaxableProductId => 2,
                ],
                'expected' => [
                    $feeProductId => [
                        'id' => $feeProductId,
                        'quantity' => 4,
                        'product_price' => '4.1650',
                        'dealer_price' => '2.6650',
                        'is_taxable' => true,
                        'taxable_product_price' => '2.5000',
                        'taxable_dealer_price' => '1.6650',
                    ],
                ],
            ],
            'combined_with_zero_quantity' => [
                'quantityByProductId' => [
                    $taxableProductId => 0,
                    $nonTaxableProductId => 0,
                ],
                'expected' => [
                    $feeProductId => [
                        'id' => $feeProductId,
                        'quantity' => 0,
                        'product_price' => '4.1650',
                        'dealer_price' => '2.6650',
                        'is_taxable' => true,
                        'taxable_product_price' => '2.5000',
                        'taxable_dealer_price' => '1.6650',
                    ],
                ],
            ],
            'combined_with_unequal_quantity' => [
                'quantityByProductId' => [
                    $taxableProductId => 2,
                    $nonTaxableProductId => 3,
                ],
                'expected' => [
                    $feeProductId => [
                        'id' => $feeProductId,
                        'quantity' => 5,
                        'product_price' => '3.9980',
                        'dealer_price' => '2.5320',
                        'is_taxable' => true,
                        'taxable_product_price' => '2.0000',
                        'taxable_dealer_price' => '1.3320',
                    ],
                ],
            ],
            'empty_request' => [
                'quantityByProductId' => [
                ],
                'expected' => [
                ],
            ],
        ];
    }
}
