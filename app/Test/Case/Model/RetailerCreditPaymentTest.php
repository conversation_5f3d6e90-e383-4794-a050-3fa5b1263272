<?php
App::uses('AppTestCase', 'TestSuite');
App::uses('RetailerCreditPayment', 'Model');

/**
 * RetailerCreditPayment Test Case.
 *
 * @property RetailerCreditPayment $RetailerCreditPayment
 */
class RetailerCreditPaymentTest extends AppTestCase
{
    public $fixtures = [
        'app.retailer_credit_payment',
        'app.retailer_credit',
        'app.user',
        'app.dealer_order',
        'app.order',
        'app.dealer_order_product',
        'app.product',
        'app.warehouse',
        'app.warehouse_product',
        'app.warehouse_product_reservation',
        'app.inventory_transfer',
        'app.inventory_transfer_product',
        'app.dealer_order_refund_product',
        'app.dealer_order_refund',
        'app.fulfillment_product',
        'app.fulfillment',
        'app.courier',
        'app.order_product',
        'app.retailer_credit_item',
    ];

    public function setUp()
    {
        parent::setUp();
        $this->RetailerCreditPayment = ClassRegistry::init('RetailerCreditPayment');
    }

    public function tearDown()
    {
        unset($this->RetailerCreditPayment);
        parent::tearDown();
    }
}
