<?php

use ShipEarlyApp\Lib\Utility\SupportedLanguages;

App::uses('AppTestCase', 'TestSuite');

/**
 * UserSetting Test Case.
 *
 * Run with the following console command:
 *
 * ./app/Console/cake test app Model/UserSetting
 *
 * @property UserSetting $UserSetting
 * @property I18nModel $I18nModel
 */
class UserSettingTest extends AppTestCase
{
    const USER_ID_BRAND = '12';
    const USER_SETTING_ID_BRAND = '7';

    public $fixtures = [
        'app.brand_staff_permission',
        'app.i18n',
        'app.page',
        'app.staff_permission',
        'app.user',
        'app.user_setting',
    ];

    public function setUp()
    {
        parent::setUp();
        $this->UserSetting = ClassRegistry::init('UserSetting');
        $this->I18nModel = ClassRegistry::init('I18nModel');

        $userId = static::USER_ID_BRAND;
        $id = static::USER_SETTING_ID_BRAND;

        if (!$this->UserSetting->Page->updateAllJoinless(
            ['Page.page_content' => '"Page[page_slug=return-policy] for {brand_email}"'],
            ['Page.page_slug' => 'return-policy']
        )) {
            throw new RuntimeException('Failed to seed Page return-policy');
        }

        $userSettingPolicyFields = [
            'UserSetting.return_policy',
            'UserSetting.privacy_policy',
            'UserSetting.terms_of_service',
            'UserSetting.checkout_head_snippet',
            'UserSetting.success_page_snippet',
        ];
        if (!$this->UserSetting->updateAllJoinless(
            array_map(
                fn(string $field): string => "'{$field} where user_id={$userId}'",
                array_combine($userSettingPolicyFields, $userSettingPolicyFields)
            ),
            ['UserSetting.id' => $id]
        )) {
            throw new RuntimeException('Failed to seed default policy values');
        }

        $i18nIds = $this->I18nModel->find('list', [
            'conditions' => ['I18nModel.model' => 'UserSetting', 'I18nModel.foreign_key' => $id],
            'fields' => ['field', 'id', 'locale'],
        ]);
        $success = $this->I18nModel->saveMany(array_reduce(static::localesSet(), function(array $list, string $locale) use ($id, $userId, $i18nIds): array {
            return array_merge($list, array_map(fn(string $field): array => [
                'id' => $i18nIds[$locale][$field] ?? null,
                'locale' => $locale,
                'model' => 'UserSetting',
                'foreign_key' => $id,
                'field' => $field,
                'content' => "i18n[locale={$locale}][model=UserSetting][field={$field}] where user_id={$userId}",
            ], ['return_policy', 'terms_of_service']));
        }, []));
        if (!$success) {
            throw new RuntimeException(json_encode(['message' => 'Failed to seed translated policy values', 'errors' => $this->I18nModel->validationErrors]));
        }
        $this->I18nModel->clear();
    }

    public function tearDown()
    {
        unset($this->UserSetting);
        parent::tearDown();
    }

    /**
     * @dataProvider providerFindForAdminEditPolicy
     */
    public function testFindForAdminEditPolicy($field)
    {
        $userId = static::USER_ID_BRAND;

        $actual = $this->UserSetting->findForAdminEditPolicy($userId, $field);

        $expected = [
            'UserSetting' => [
                'id' => '7',
                'user_id' => $userId,
                $field => "UserSetting.{$field} where user_id={$userId}",
            ],
            'User' => [
                'id' => $userId,
                'email_address' => '<EMAIL>',
            ],
        ];
        if (in_array($field, ['return_policy', 'terms_of_service'], true)) {
            $expected['UserSetting'][$field] = "i18n[locale={$this->UserSetting->locale}][model=UserSetting][field={$field}] where user_id={$userId}";
            $expected['UserSetting']['locale'] = $this->UserSetting->locale;
        }
        $this->assertEquals($expected, $actual);
    }

    public function providerFindForAdminEditPolicy(): array
    {
        $policyFields = [
            'return_policy',
            'privacy_policy',
            'terms_of_service',
            'checkout_head_snippet',
            'success_page_snippet',
        ];

        return array_map(
            function($field) {
                return compact('field');
            },
            array_combine($policyFields, $policyFields)
        );
    }

    public function testListPolicies()
    {
        $userId = static::USER_ID_BRAND;

        // Undocumented by CakePHP: Bypass TranslateBehavior by setting locale to a non-null falsey value
        $this->UserSetting->locale = '';
        $actual = $this->UserSetting->listPolicies($userId);

        $expected = [
            'return_policy' => "UserSetting.return_policy where user_id={$userId}",
            'privacy_policy' => "UserSetting.privacy_policy where user_id={$userId}",
            'terms_of_service' => "UserSetting.terms_of_service where user_id={$userId}",
        ];
        $this->assertEquals($expected, $actual);
    }

    /**
     * @dataProvider providerListTranslatedPolicies
     */
    public function testListTranslatedPolicies($locale)
    {
        $userId = static::USER_ID_BRAND;

        $this->UserSetting->locale = $locale;
        $actual = $this->UserSetting->listPolicies($userId);

        $expected = [
            'return_policy' => "i18n[locale={$locale}][model=UserSetting][field=return_policy] where user_id={$userId}",
            'privacy_policy' => "UserSetting.privacy_policy where user_id={$userId}",
            'terms_of_service' => "i18n[locale={$locale}][model=UserSetting][field=terms_of_service] where user_id={$userId}",
        ];
        $this->assertEquals($expected, $actual);
    }

    /**
     * @dataProvider providerListTranslatedPolicies
     */
    public function testListEmptyTranslatedPolicies($locale)
    {
        $userId = static::USER_ID_BRAND;
        $id = static::USER_SETTING_ID_BRAND;
        $this->I18nModel->updateAllJoinless(['I18nModel.content' => $this->I18nModel->value('')], [
            'I18nModel.model' => 'UserSetting',
            'I18nModel.foreign_key' => $id,
        ]);

        $this->UserSetting->locale = $locale;
        $actual = $this->UserSetting->listPolicies($userId);

        $expected = [
            'return_policy' => 'Page[page_slug=return-policy] for <a href=\"mailto:<EMAIL>\"><EMAIL></a>',
            'privacy_policy' => "UserSetting.privacy_policy where user_id={$userId}",
            'terms_of_service' => '',
        ];
        if ($locale === SupportedLanguages::DEFAULT_LOCALE) {
            $expected = array_merge($expected, [
                'return_policy' => "UserSetting.return_policy where user_id={$userId}",
                'terms_of_service' => "UserSetting.terms_of_service where user_id={$userId}",
            ]);
        }
        $this->assertEquals($expected, $actual);
    }

    public function providerListTranslatedPolicies(): array
    {
        return array_map(fn(string $locale): array => compact('locale'), static::localesSet());
    }

    public function testListSelectedCountries()
    {
        $userId = static::USER_ID_BRAND;

        $actual = $this->UserSetting->listSelectedCountries($userId);

        $expected = [
            '233' => 'United States',
            '39' => 'Canada',
        ];
        $this->assertEquals($expected, $actual);
    }

    /**
     * @dataProvider providerGetReturnPolicy
     */
    public function testGetReturnPolicy($locale)
    {
        $userId = static::USER_ID_BRAND;

        $this->UserSetting->locale = $locale;
        $actual = $this->UserSetting->getReturnPolicy($userId);

        $expected = "i18n[locale={$this->UserSetting->locale}][model=UserSetting][field=return_policy] where user_id={$userId}";
        $this->assertEquals($expected, $actual);
    }

    public function providerGetReturnPolicy(): array
    {
        return array_map(fn(string $locale): array => compact('locale'), static::localesSet());
    }

    /**
     * @dataProvider providerGetPageReturnPolicy
     */
    public function testGetPageReturnPolicy($userEmail, $expected)
    {
        $userId = static::USER_ID_BRAND;
        // Clear value with `save()` instead of `updateAll()` to also clear the translated value
        if (!$this->UserSetting->save(['id' => static::USER_SETTING_ID_BRAND, 'return_policy' => ''])) {
            throw new RuntimeException(json_encode(['message' => 'Failed to seed empty return policy', 'errors' => $this->UserSetting->validationErrors]));
        }
        $this->UserSetting->clear();

        $actual = $this->UserSetting->getReturnPolicy($userId, $userEmail);

        $this->assertEquals($expected, $actual);
    }

    public function providerGetPageReturnPolicy(): array
    {
        return [
            ['userEmail' => null, 'expected' => 'Page[page_slug=return-policy] for <a href="mailto:<EMAIL>"><EMAIL></a>'],
            ['userEmail' => '<EMAIL>', 'expected' => 'Page[page_slug=return-policy] for <a href="mailto:<EMAIL>"><EMAIL></a>'],
        ];
    }

    /**
     * @return array<string, string>
     */
    private static function localesSet(): array
    {
        return SupportedLanguages::getLocalesSet();
    }
}
