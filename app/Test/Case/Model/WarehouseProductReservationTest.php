<?php
App::uses('AppTestCase', 'TestSuite');
App::uses('WarehouseProductReservation', 'Model');

/**
 * WarehouseProductReservation Test Case.
 *
 * Run with the following console command:
 *
 * ./app/Console/cake test app Model/WarehouseProductReservation
 *
 * @property WarehouseProductReservation $WarehouseProductReservation
 * @property Btask $Btask
 */
class WarehouseProductReservationTest extends AppTestCase
{
    public $fixtures = [
        'app.btask',
        'app.order',
        'app.order_comment',
        'app.order_customer_message',
        'app.product',
        'app.product_state_fee',
        'app.user',
        'app.warehouse',
        'app.warehouse_product',
        'app.warehouse_product_reservation',
    ];

    public function setUp()
    {
        parent::setUp();
        $this->WarehouseProductReservation = ClassRegistry::init('WarehouseProductReservation');
        $this->Btask = ClassRegistry::init('Btask');

        $this->Btask->truncate();
    }

    public function tearDown()
    {
        unset(
            $this->Btask,
            $this->WarehouseProductReservation
        );
        parent::tearDown();
    }

    public function testReserveLineItemInventory()
    {
        /** @var PHPUnit_Framework_MockObject_MockObject $mock */
        $mock = $this->getMockForModel('Btask', ['queueInventoryReservation']);
        $mock->expects($this->any())->method('queueInventoryReservation')->willReturn(true);

        $this->WarehouseProductReservation->WarehouseProduct->updateAllJoinless(['reserved_quantity' => '0'], ['warehouse_id' => '1', 'product_id' => '21']);
        $this->WarehouseProductReservation->WarehouseProduct->updateAllJoinless(['reserved_quantity' => '0'], ['warehouse_id' => '1', 'product_id' => '23']);
        $this->WarehouseProductReservation->WarehouseProduct->updateAllJoinless(['reserved_quantity' => '1'], ['warehouse_id' => '2', 'product_id' => '21']);
        $this->WarehouseProductReservation->WarehouseProduct->updateAllJoinless(['reserved_quantity' => '1'], ['warehouse_id' => '2', 'product_id' => '23']);

        $items = [
            [
                'product_id' => '21',
                'warehouse_id' => '1',
                'inventory_transfer_id' => null,
                'quantity' => '1',
            ],
            [
                'product_id' => '23',
                'warehouse_id' => '1',
                'inventory_transfer_id' => null,
                'quantity' => '2',
            ],
            [
                'product_id' => '21',
                'warehouse_id' => '2',
                'inventory_transfer_id' => null,
                'quantity' => '1',
            ],
            [
                'product_id' => '23',
                'warehouse_id' => '2',
                'inventory_transfer_id' => null,
                'quantity' => '2',
            ],
        ];
        $this->assertTrue($this->WarehouseProductReservation->reserveLineItemSet('1', $items), 'Reserve succeeded');

        $expected = [
            [
                'WarehouseProductReservation' => [
                    'order_id' => '1',
                    'quantity' => '1',
                ],
                'WarehouseProduct' => [
                    'warehouse_id' => '1',
                    'product_id' => '21',
                    'quantity' => '5',
                    'reserved_quantity' => '1',
                ]
            ],
            [
                'WarehouseProductReservation' => [
                    'order_id' => '1',
                    'quantity' => '2',
                ],
                'WarehouseProduct' => [
                    'warehouse_id' => '1',
                    'product_id' => '23',
                    'quantity' => '0',
                    'reserved_quantity' => '2',
                ],
            ],
            [
                'WarehouseProductReservation' => [
                    'order_id' => '1',
                    'quantity' => '1',
                ],
                'WarehouseProduct' => [
                    'warehouse_id' => '2',
                    'product_id' => '21',
                    'quantity' => '169',
                    'reserved_quantity' => '2',
                ],
            ],
            [
                'WarehouseProductReservation' => [
                    'order_id' => '1',
                    'quantity' => '2',
                ],
                'WarehouseProduct' => [
                    'warehouse_id' => '2',
                    'product_id' => '23',
                    'quantity' => '314',
                    'reserved_quantity' => '3',
                ],
            ],
        ];
        $this->assertEquals($expected, $this->findAllWarehouseProductReservations('1', ['1', '2'], ['21', '23']));
    }

    public function testReserveLineItemNotStockedInWarehouseInventory()
    {
        /** @var PHPUnit_Framework_MockObject_MockObject $mock */
        $mock = $this->getMockForModel('Btask', ['queueInventoryReservation']);
        $mock->expects($this->any())->method('queueInventoryReservation')->willReturn(true);

        $this->WarehouseProductReservation->WarehouseProduct->updateAllJoinless(['reserved_quantity' => '0'], ['warehouse_id' => '1', 'product_id' => '21']);
        $this->WarehouseProductReservation->WarehouseProduct->deleteAllJoinless(['warehouse_id' => '1', 'product_id' => '23'], false);

        $items = [
            [
                'product_id' => '21',
                'warehouse_id' => '1',
                'inventory_transfer_id' => null,
                'quantity' => '1',
            ],
            [
                'product_id' => '23',
                'warehouse_id' => '1',
                'inventory_transfer_id' => null,
                'quantity' => '2',
            ],
            [
                'product_id' => '23',
                'warehouse_id' => null,
                'inventory_transfer_id' => null,
                'quantity' => '3',
            ],
        ];
        $this->assertTrue($this->WarehouseProductReservation->reserveLineItemSet('1', $items), 'Reserve succeeded');

        $expected = [
            [
                'WarehouseProductReservation' => [
                    'order_id' => '1',
                    'quantity' => '1',
                ],
                'WarehouseProduct' => [
                    'warehouse_id' => '1',
                    'product_id' => '21',
                    'quantity' => '5',
                    'reserved_quantity' => '1',
                ]
            ],
        ];
        $this->assertEquals($expected, $this->findAllWarehouseProductReservations('1', '1', ['21', '23']));
    }

    public function testReleaseLineItemInventory()
    {
        /** @var PHPUnit_Framework_MockObject_MockObject $mock */
        $mock = $this->getMockForModel('Btask', ['queueInventoryReservation']);
        $mock->expects($this->any())->method('queueInventoryReservation')->willReturn(true);

        // Seed based on testReserveLineItemInventory
        $orderId = '1';
        $this->WarehouseProductReservation->saveMany([
            [
                'WarehouseProduct' => ['id' => '7', 'reserved_quantity' => '1'],
                'WarehouseProductReservation' => ['order_id' => $orderId, 'quantity' => '1'],
            ],
            [
                'WarehouseProduct' => ['id' => '13', 'reserved_quantity' => '2'],
                'WarehouseProductReservation' => ['order_id' => $orderId, 'quantity' => '2'],
            ],
            [
                'WarehouseProduct' => ['id' => '1', 'reserved_quantity' => '2'],
                'WarehouseProductReservation' => ['order_id' => $orderId, 'quantity' => '1'],
            ],
            [
                'WarehouseProduct' => ['id' => '2', 'reserved_quantity' => '3'],
                'WarehouseProductReservation' => ['order_id' => $orderId, 'quantity' => '2'],
            ],
        ], ['deep' => true, 'callbacks' => false]);

        $this->assertTrue($this->WarehouseProductReservation->release('1'), 'Release succeeded');

        $expected = [
            [
                'WarehouseProductReservation' => [
                    'order_id' => null,
                    'quantity' => null,
                ],
                'WarehouseProduct' => [
                    'warehouse_id' => '1',
                    'product_id' => '21',
                    'quantity' => '5',
                    'reserved_quantity' => '0',
                ]
            ],
            [
                'WarehouseProductReservation' => [
                    'order_id' => null,
                    'quantity' => null,
                ],
                'WarehouseProduct' => [
                    'warehouse_id' => '1',
                    'product_id' => '23',
                    'quantity' => '0',
                    'reserved_quantity' => '0',
                ],
            ],
            [
                'WarehouseProductReservation' => [
                    'order_id' => null,
                    'quantity' => null,
                ],
                'WarehouseProduct' => [
                    'warehouse_id' => '2',
                    'product_id' => '21',
                    'quantity' => '169',
                    'reserved_quantity' => '1',
                ],
            ],
            [
                'WarehouseProductReservation' => [
                    'order_id' => null,
                    'quantity' => null,
                ],
                'WarehouseProduct' => [
                    'warehouse_id' => '2',
                    'product_id' => '23',
                    'quantity' => '314',
                    'reserved_quantity' => '1',
                ],
            ],
        ];
        $this->assertEquals($expected, $this->findAllWarehouseProductReservations('1', ['1', '2'], ['21', '23']));
    }

    public function testFullfillLineItemInventory()
    {
        /** @var PHPUnit_Framework_MockObject_MockObject $mock */
        $mock = $this->getMockForModel('Btask', ['queueInventoryReservation']);
        $mock->expects($this->any())->method('queueInventoryReservation')->willReturn(true);

        // Seed based on testReserveLineItemInventory
        $orderId = '1';
        $this->WarehouseProductReservation->saveMany([
            [
                'WarehouseProduct' => ['id' => '7', 'reserved_quantity' => '1'],
                'WarehouseProductReservation' => ['order_id' => $orderId, 'quantity' => '1'],
            ],
            [
                'WarehouseProduct' => ['id' => '13', 'reserved_quantity' => '2'],
                'WarehouseProductReservation' => ['order_id' => $orderId, 'quantity' => '2'],
            ],
            [
                'WarehouseProduct' => ['id' => '1', 'reserved_quantity' => '2'],
                'WarehouseProductReservation' => ['order_id' => $orderId, 'quantity' => '1'],
            ],
            [
                'WarehouseProduct' => ['id' => '2', 'reserved_quantity' => '3'],
                'WarehouseProductReservation' => ['order_id' => $orderId, 'quantity' => '2'],
            ],
        ], ['deep' => true, 'callbacks' => false]);

        $this->assertTrue($this->WarehouseProductReservation->fulfill('1'), 'Fulfill succeeded');

        $expected = [
            [
                'WarehouseProductReservation' => [
                    'order_id' => null,
                    'quantity' => null,
                ],
                'WarehouseProduct' => [
                    'warehouse_id' => '1',
                    'product_id' => '21',
                    'quantity' => '4',
                    'reserved_quantity' => '0',
                ]
            ],
            [
                'WarehouseProductReservation' => [
                    'order_id' => null,
                    'quantity' => null,
                ],
                'WarehouseProduct' => [
                    'warehouse_id' => '1',
                    'product_id' => '23',
                    'quantity' => '-2',
                    'reserved_quantity' => '0',
                ],
            ],
            [
                'WarehouseProductReservation' => [
                    'order_id' => null,
                    'quantity' => null,
                ],
                'WarehouseProduct' => [
                    'warehouse_id' => '2',
                    'product_id' => '21',
                    'quantity' => '168',
                    'reserved_quantity' => '1',
                ],
            ],
            [
                'WarehouseProductReservation' => [
                    'order_id' => null,
                    'quantity' => null,
                ],
                'WarehouseProduct' => [
                    'warehouse_id' => '2',
                    'product_id' => '23',
                    'quantity' => '312',
                    'reserved_quantity' => '1',
                ],
            ],
        ];
        $this->assertEquals($expected, $this->findAllWarehouseProductReservations('1', ['1', '2'], ['21', '23']));
    }

    public function testUpdatingReservationsReleasesExcludedItems()
    {
        /** @var PHPUnit_Framework_MockObject_MockObject $mock */
        $mock = $this->getMockForModel('Btask', ['queueInventoryReservation']);
        $mock->expects($this->any())->method('queueInventoryReservation')->willReturn(true);

        $items = [
            [
                'product_id' => '21',
                'warehouse_id' => '1',
                'inventory_transfer_id' => null,
                'quantity' => '1',
            ],
            [
                'product_id' => '23',
                'warehouse_id' => '1',
                'inventory_transfer_id' => null,
                'quantity' => '2',
            ],
        ];
        $this->assertTrue($this->WarehouseProductReservation->reserveLineItemSet('1', $items), 'Reserve succeeded');

        $items = [
            [
                'product_id' => '21',
                'warehouse_id' => '1',
                'inventory_transfer_id' => null,
                'quantity' => '2',
            ],
        ];
        $this->assertTrue($this->WarehouseProductReservation->reserveLineItemSet('1', $items), 'Update reservation succeeded');

        $expected = [
            [
                'WarehouseProductReservation' => [
                    'order_id' => '1',
                    'quantity' => '2',
                ],
                'WarehouseProduct' => [
                    'warehouse_id' => '1',
                    'product_id' => '21',
                    'quantity' => '5',
                    'reserved_quantity' => '2',
                ]
            ],
            [
                'WarehouseProductReservation' => [
                    'order_id' => null,
                    'quantity' => null,
                ],
                'WarehouseProduct' => [
                    'warehouse_id' => '1',
                    'product_id' => '23',
                    'quantity' => '0',
                    'reserved_quantity' => '0',
                ],
            ],
        ];
        $this->assertEquals($expected, $this->findAllWarehouseProductReservations('1', '1', ['21', '23']));
    }

    public function testUpdatingReservationsReleasesZeroedItems()
    {
        /** @var PHPUnit_Framework_MockObject_MockObject $mock */
        $mock = $this->getMockForModel('Btask', ['queueInventoryReservation']);
        $mock->expects($this->any())->method('queueInventoryReservation')->willReturn(true);

        $items = [
            [
                'product_id' => '21',
                'warehouse_id' => '1',
                'inventory_transfer_id' => null,
                'quantity' => '1',
            ],
            [
                'product_id' => '23',
                'warehouse_id' => '1',
                'inventory_transfer_id' => null,
                'quantity' => '2',
            ],
        ];
        $this->assertTrue($this->WarehouseProductReservation->reserveLineItemSet('1', $items), 'Reserve succeeded');

        $items = [
            [
                'product_id' => '21',
                'warehouse_id' => '1',
                'inventory_transfer_id' => null,
                'quantity' => '2',
            ],
            [
                'product_id' => '23',
                'warehouse_id' => '1',
                'inventory_transfer_id' => null,
                'quantity' => '0',
            ],
        ];
        $this->assertTrue($this->WarehouseProductReservation->reserveLineItemSet('1', $items), 'Update reservation succeeded');

        $expected = [
            [
                'WarehouseProductReservation' => [
                    'order_id' => '1',
                    'quantity' => '2',
                ],
                'WarehouseProduct' => [
                    'warehouse_id' => '1',
                    'product_id' => '21',
                    'quantity' => '5',
                    'reserved_quantity' => '2',
                ]
            ],
            [
                'WarehouseProductReservation' => [
                    'order_id' => null,
                    'quantity' => null,
                ],
                'WarehouseProduct' => [
                    'warehouse_id' => '1',
                    'product_id' => '23',
                    'quantity' => '0',
                    'reserved_quantity' => '0',
                ],
            ],
        ];
        $this->assertEquals($expected, $this->findAllWarehouseProductReservations('1', '1', ['21', '23']));
    }

    public function testUpdatingReservationsReleasesFullyReplacedItems()
    {
        /** @var PHPUnit_Framework_MockObject_MockObject $mock */
        $mock = $this->getMockForModel('Btask', ['queueInventoryReservation']);
        $mock->expects($this->any())->method('queueInventoryReservation')->willReturn(true);

        $items = [
            [
                'product_id' => '23',
                'warehouse_id' => '1',
                'inventory_transfer_id' => null,
                'quantity' => '1',
            ],
        ];
        $this->assertTrue($this->WarehouseProductReservation->reserveLineItemSet('1', $items), 'Reserve succeeded');

        $items = [
            [
                'product_id' => '21',
                'warehouse_id' => '1',
                'inventory_transfer_id' => null,
                'quantity' => '2',
            ],
        ];
        $this->assertTrue($this->WarehouseProductReservation->reserveLineItemSet('1', $items), 'Update reservation succeeded');

        $expected = [
            [
                'WarehouseProductReservation' => [
                    'order_id' => '1',
                    'quantity' => '2',
                ],
                'WarehouseProduct' => [
                    'warehouse_id' => '1',
                    'product_id' => '21',
                    'quantity' => '5',
                    'reserved_quantity' => '2',
                ]
            ],
            [
                'WarehouseProductReservation' => [
                    'order_id' => null,
                    'quantity' => null,
                ],
                'WarehouseProduct' => [
                    'warehouse_id' => '1',
                    'product_id' => '23',
                    'quantity' => '0',
                    'reserved_quantity' => '0',
                ],
            ],
        ];
        $this->assertEquals($expected, $this->findAllWarehouseProductReservations('1', '1', ['21', '23']));
    }

    public function testReserveAndReleaseQueuesBtasks()
    {
        $items = [
            [
                'product_id' => '21',
                'warehouse_id' => '1',
                'inventory_transfer_id' => null,
                'quantity' => '1',
            ],
            [
                'product_id' => '23',
                'warehouse_id' => '1',
                'inventory_transfer_id' => null,
                'quantity' => '2',
            ],
        ];
        $this->assertTrue($this->WarehouseProductReservation->reserveLineItemSet('1', $items), 'Reserve succeeded');

        $expected = [
            [
                'Btask' => [
                    'status' => Btask::STATUS_OPEN,
                    'type' => Btask::TYPE_INVENTORY_RESERVATION,
                    'data' => json_encode(['warehouse_product_id' => '7']),
                ],
            ],
            [
                'Btask' => [
                    'status' => Btask::STATUS_OPEN,
                    'type' => Btask::TYPE_INVENTORY_RESERVATION,
                    'data' => json_encode(['warehouse_product_id' => '13']),
                ],
            ],
        ];
        $this->assertEquals($expected, $this->dequeueAllBtasks());

        $items = [
            [
                'product_id' => '21',
                'warehouse_id' => '1',
                'inventory_transfer_id' => null,
                'quantity' => '2',
            ],
        ];
        $this->assertTrue($this->WarehouseProductReservation->reserveLineItemSet('1', $items), 'Update reservation succeeded');

        $expected = [
            [
                'Btask' => [
                    'status' => Btask::STATUS_OPEN,
                    'type' => Btask::TYPE_INVENTORY_RESERVATION,
                    'data' => json_encode(['warehouse_product_id' => '7']),
                ],
            ],
            [
                'Btask' => [
                    'status' => Btask::STATUS_OPEN,
                    'type' => Btask::TYPE_INVENTORY_RESERVATION,
                    'data' => json_encode(['warehouse_product_id' => '13']),
                ],
            ],
        ];
        $this->assertEquals($expected, $this->dequeueAllBtasks());

        $this->assertTrue($this->WarehouseProductReservation->release('1'), 'Release succeeded');

        $expected = [
            [
                'Btask' => [
                    'status' => Btask::STATUS_OPEN,
                    'type' => Btask::TYPE_INVENTORY_RESERVATION,
                    'data' => json_encode(['warehouse_product_id' => '7']),
                ],
            ],
        ];
        $this->assertEquals($expected, $this->dequeueAllBtasks());
    }

    public function testInvalidReservationUpdateDoesNotChangeAnything()
    {
        $orderId = '1';
        $this->WarehouseProductReservation->saveMany([
            [
                'WarehouseProduct' => ['id' => '7', 'reserved_quantity' => '1'],
                'WarehouseProductReservation' => ['order_id' => $orderId, 'quantity' => '1'],
            ],
            [
                'WarehouseProduct' => ['id' => '13', 'reserved_quantity' => '2'],
                'WarehouseProductReservation' => ['order_id' => $orderId, 'quantity' => '2'],
            ],
        ], ['deep' => true, 'callbacks' => false]);

        $items = [
            [
                'product_id' => '21',
                'warehouse_id' => '1',
                'inventory_transfer_id' => null,
                'quantity' => '-1',
            ],
        ];
        $this->assertFalse($this->WarehouseProductReservation->reserveLineItemSet($orderId, $items), 'Update reservation succeeded');

        $expected = [
            [
                'WarehouseProductReservation' => [
                    'order_id' => '1',
                    'quantity' => '1',
                ],
                'WarehouseProduct' => [
                    'warehouse_id' => '1',
                    'product_id' => '21',
                    'quantity' => '5',
                    'reserved_quantity' => '1',
                ]
            ],
            [
                'WarehouseProductReservation' => [
                    'order_id' => '1',
                    'quantity' => '2',
                ],
                'WarehouseProduct' => [
                    'warehouse_id' => '1',
                    'product_id' => '23',
                    'quantity' => '0',
                    'reserved_quantity' => '2',
                ],
            ],
        ];
        $this->assertEquals($expected, $this->findAllWarehouseProductReservations($orderId, '1', ['21', '23']), 'Reservations changed');
        $this->assertEmpty($this->dequeueAllBtasks(), 'Btasks changed');
    }

    private function findAllWarehouseProductReservations($orderId, $warehouseIds, $productIds): array
    {
        $WarehouseProduct = $this->WarehouseProductReservation->WarehouseProduct;

        $reservations = $WarehouseProduct->find('all', [
            'joins' => [
                [
                    'table' => $this->WarehouseProductReservation->table,
                    'alias' => $this->WarehouseProductReservation->alias,
                    'type' => 'LEFT',
                    'conditions' => [
                        "{$this->WarehouseProductReservation->alias}.warehouse_product_id" => $WarehouseProduct->primaryKeyIdentifier(),
                        "{$this->WarehouseProductReservation->alias}.order_id" => $orderId,
                    ],
                ],
            ],
            'conditions' => [
                'WarehouseProduct.warehouse_id' => $warehouseIds,
                'WarehouseProduct.product_id' => $productIds,
            ],
            'fields' => [
                'WarehouseProductReservation.order_id',
                'WarehouseProductReservation.quantity',
                'WarehouseProduct.id',
                'WarehouseProduct.warehouse_id',
                'WarehouseProduct.product_id',
                'WarehouseProduct.quantity',
                'WarehouseProduct.reserved_quantity',
            ],
            'order' => [
                'WarehouseProduct.warehouse_id' => 'ASC',
                'WarehouseProduct.product_id' => 'ASC',
            ],
        ]);

        return array_map(function($reservation) {
            unset($reservation['WarehouseProduct']['id']);

            return $reservation;
        }, $reservations);
    }

    private function dequeueAllBtasks()
    {
        $tasks = $this->Btask->find('all');

        $this->Btask->truncate();

        return array_map(function($task) {
            unset($task['Btask']['id']);
            unset($task['Btask']['created']);
            return $task;
        }, $tasks);
    }
}
