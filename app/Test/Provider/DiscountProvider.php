<?php

use ShipEarlyApp\Lib\Globals\AppGlobalMethods;

App::uses('DiscountRule', 'Model');
App::uses('DiscountOptions', 'Utility/Discount');
App::uses('DiscountOrderOptions', 'Utility/Discount');

class DiscountProvider
{
    const DISCOUNT_UUID = '58fa1800-0ca0-42e3-ada1-04d57f000002';
    const USER_ID = '8';
    const BELL_ID = 17533140807;
    const BIKE_BLUE_ID = 20016274759;
    const BIKE_RED_ID = 17532934023;

    public static function defaultDiscountFormData(): array
    {
        $end_date = 'Dec 31, ' . static::date('Y');

        return [
            'Discount' => [
                'id' => '1',
                'code' => 'BIKESFORDAYS',
                'description' => '25.00% off titles (Super Bell, Super Bike)',
                'is_b2b_discount' => false,
                'b2b_discount_type' => null,
                'b2b_hide_ineligible_products' => false,
                'shipping_window_start_date' => null,
                'shipping_window_end_date' => null,
                'start_date' => 'Jan 1, 2019',
                'end_date' => $end_date,
                'retailer_option' => 'select_retailers',
                'retailer_values' => [
                    'select_retailers' => ['7', '10'],
                ],
                'usage_limit_option' => 'unlimited',
                'usage_limit_quantity' => '0',
                'limit_per_customer' => false,
                'is_active' => true,
                'is_enabled' => true,
                'is_automatic' => false,
                'option' => 'percent_off',
                'prerequisite_subtotal' => '0.00',
                'prerequisite_quantity' => '0',
                'is_auto_add_y' => '0',
                'enable_free_freight' => false,
            ],
            'DiscountRule' => [
                0 => [
                    'id' => '1',
                    'discount_id' => '1',
                    'option' => 'percent_off',
                    'option_amount' => '25.00',
                    'prerequisite_subtotal' => '0.00',
                    'prerequisite_quantity' => '0',
                    'order_option' => 'product_title',
                    'order_values' => [
                        'product_title' => ['Super Bell', 'Super Bike'],
                    ],
                    'auto_add_sku_quantities' => [],
                    'is_buy_x_get_y' => '0',
                    'is_auto_add_y' => '0',
                    'exclude_shipping_rates_above' => false,
                ],
            ],
            'CreditTerm' => [
            ],
        ];
    }

    public static function defaultDiscountInfo(): array
    {
        return [
            'id' => '1',
            'code' => 'BIKESFORDAYS',
            'retailer_option' => 'select_retailers',
            'retailer_values' => '7,10',
            'usage_limit_option' => 'unlimited',
            'usage_limit_quantity' => '0',
            'limit_per_customer' => false,
            'is_active' => true,
            'is_automatic' => false,
            'usage_count' => '7',
            'b2b_hide_ineligible_products' => false,
            'enable_free_freight' => false,
            'DiscountRule' => [
                [
                    'id' => '1',
                    'discount_id' => '1',
                    'option' => 'percent_off',
                    'option_amount' => '25.00',
                    'prerequisite_subtotal' => '0.00',
                    'prerequisite_quantity' => '0',
                    'prerequisite_option' => null,
                    'prerequisite_values' => null,
                    'order_quantity' => '0',
                    'order_option' => 'product_title',
                    'order_values' => '["Super Bell","Super Bike"]',
                    'auto_add_sku_quantities' => null,
                ],
            ],
        ];
    }

    public static function eCommerceDiscountInfo()
    {
        $discountInfo = static::defaultDiscountInfo();
        unset($discountInfo['b2b_hide_ineligible_products']);

        return array_merge($discountInfo, [
            'name' => $discountInfo['code'],
        ]);
    }

    public static function defaultB2bCatalogueDiscountInfo(): array
    {
        return [
            'id' => '2',
            'code' => 'B2BFORDAYS',
            'description' => '25.00% off variant (BICYCLE-1)',
            'is_b2b_discount' => true,
            'b2b_hide_ineligible_products' => false,
            'retailer_option' => 'tiers',
            'retailer_values' => '1',
            'enable_free_freight' => false,
            'DiscountRule' => [
                [
                    'id' => '2',
                    'discount_id' => '2',
                    'option' => 'percent_off',
                    'option_amount' => '25.00',
                    'prerequisite_subtotal' => '0.00',
                    'prerequisite_quantity' => '0',
                    'prerequisite_option' => null,
                    'prerequisite_values' => null,
                    'order_quantity' => '0',
                    'order_option' => 'product_variant',
                    'order_values' => '["BICYCLE-1"]',
                    'auto_add_sku_quantities' => null,
                ],
            ],
        ];
    }

    public static function defaultB2bCartDiscountInfo(): array
    {
        return array_merge(static::defaultB2bCatalogueDiscountInfo(), [
            'b2b_discount_type' => 'booking',
            'enable_free_freight' => false,
        ]);
    }

    public static function defaultItems(): array
    {
        return [
            [
                'id' => 17533140807,
                'properties' => null,
                'quantity' => 2,
                'variant_id' => 17533140807,
                'key' => '17533140807:8799e5c50c490ac93e9421da9e49100c',
                'title' => 'Super Bell',
                'price' => 59999,
                'original_price' => 59999,
                'discounted_price' => 59999,
                'line_price' => 119998,
                'original_line_price' => 119998,
                'total_discount' => 0,
                'discounts' => [],
                'sku' => 'BELL',
                'grams' => 0,
                'vendor' => 'aron-shipearly',
                'product_id' => 5572320903,
                'gift_card' => false,
                'url' => '/products/super-bell?variant=17533140807',
                'image' => null,
                'handle' => 'super-bell',
                'requires_shipping' => true,
                'product_type' => 'Accessories',
                'product_title' => 'Super Bell',
                'product_description' => '',
                'variant_title' => null,
                'variant_options' => ['Default Title'],
            ],
            [
                'id' => 20016274759,
                'properties' => null,
                'quantity' => 2,
                'variant_id' => 20016274759,
                'key' => '20016274759:45a63f853daad116d59153d210f4fbb2',
                'title' => 'Super Bike - Blue',
                'price' => 39999,
                'original_price' => 39999,
                'discounted_price' => 39999,
                'line_price' => 79998,
                'original_line_price' => 79998,
                'total_discount' => 0,
                'discounts' => [],
                'sku' => 'BICYCLE-2',
                'grams' => 4536,
                'vendor' => 'aron-shipearly',
                'product_id' => 5572298951,
                'gift_card' => false,
                'url' => '/products/super-bike?variant=20016274759',
                'image' => null,
                'handle' => 'super-bike',
                'requires_shipping' => true,
                'product_type' => 'Bikes',
                'product_title' => 'Super Bike',
                'product_description' => '',
                'variant_title' => 'Blue',
                'variant_options' => ['Blue'],
            ],
            [
                'id' => 17532934023,
                'properties' => null,
                'quantity' => 2,
                'variant_id' => 17532934023,
                'key' => '17532934023:4e1ff7977e78345c262af31534df7b36',
                'title' => 'Super Bike - Red',
                'price' => 120000,
                'original_price' => 120000,
                'discounted_price' => 120000,
                'line_price' => 240000,
                'original_line_price' => 240000,
                'total_discount' => 0,
                'discounts' => [],
                'sku' => 'BICYCLE-1',
                'grams' => 4536,
                'vendor' => 'aron-shipearly',
                'product_id' => 5572298951,
                'gift_card' => false,
                'url' => '/products/super-bike?variant=17532934023',
                'image' => null,
                'handle' => 'super-bike',
                'requires_shipping' => true,
                'product_type' => 'Bikes',
                'product_title' => 'Super Bike',
                'product_description' => '',
                'variant_title' => 'Red',
                'variant_options' => ['Red'],
            ],
            [
                'id' => 31763766837299,
                'properties' => null,
                'quantity' => 2,
                'variant_id' => 31763766837299,
                'key' => '31763766837299:',
                'title' => 'Ancillary Fees',
                'price' => 333,
                'original_price' => 333,
                'discounted_price' => 333,
                'line_price' => 666,
                'original_line_price' => 666,
                'total_discount' => 0,
                'discounts' => [],
                'sku' => 'ANCILLARY-FEES',
                'grams' => 0,
                'vendor' => null,
                'taxable' => true,
                'product_id' => *************,
                'product_has_only_default_variant' => true,
                'gift_card' => false,
                'final_price' => 333,
                'final_line_price' => 666,
                'url' => null,
                'featured_image' => [
                    'aspect_ratio' => null,
                    'alt' => 'Ancillary Fees',
                    'height' => null,
                    'url' => 'https://cdn.shopify.com/s/files/1/1205/5522/products/paintcare_PngItem_2298255.png?v=1584979564',
                    'width' => null,
                ],
                'image' => 'https://cdn.shopify.com/s/files/1/1205/5522/products/paintcare_PngItem_2298255.png?v=1584979564',
                'handle' => 'ancillary-fees',
                'requires_shipping' => false,
                'product_type' => '',
                'product_title' => 'Ancillary Fees',
                'product_description' => '',
                'variant_title' => null,
                'variant_options' => [
                    0 => 'Default Title',
                ],
                'options_with_values' => [
                    0 => ['name' => 'Title', 'value' => 'Default Title'],
                ],
                'line_level_discount_allocations' => [],
                'line_level_total_discount' => 0,
            ],
        ];
    }

    public static function defaultOrderProductItems()
    {
        return [
            [
                'id' => 1,
                'unit_price' => 10.00,
                'total_price' => 20.00,
                'quantity' => 2,
                'Product' => [
                    'id' => '21',
                    'product_type' => 'Type 1',
                    'product_name' => 'Super Bike',
                    'product_sku' => 'Sku 1',
                    'is_fee_product' => '0',
                    'Tag' => [
                        [
                            'name' => 'Tag 1',
                        ],
                        [
                            'name' => 'Tag 2',
                        ],
                    ],
                    'Collection' => [
                        [
                            'title' => 'Collection 1',
                        ],
                        [
                            'title' => 'Collection 2',
                        ],
                    ],
                ],
            ],
            [
                'id' => '-1',
                'unit_price' => '3.00',
                'total_price' => '6.00',
                'quantity' => '2',
                'Product' => [
                    'id' => '68',
                    'product_type' => '',
                    'product_name' => 'Ancillary Fees',
                    'product_sku' => 'ANCILLARY-FEES',
                    'is_fee_product' => '1',
                    'Tag' => [
                        ['name' => 'Fee'],
                        ['name' => 'Hidden'],
                    ],
                    'Collection' => [
                    ],
                ],
            ],
        ];
    }

    public static function defaultProductItems()
    {
        return [
            [
                'id' => '21',
                'dealer_price' => 10.00,
                'product_name' => 'Super Bike',
                'product_type' => 'Type 1',
                'product_sku' => 'Sku 1',
                'is_fee_product' => '0',
                'Tag' => [
                    [
                        'name' => 'Tag 1',
                    ],
                    [
                        'name' => 'Tag 2',
                    ],
                ],
                'Collection' => [
                    [
                        'title' => 'Collection 1',
                    ],
                    [
                        'title' => 'Collection 2',
                    ],
                ],
            ],
            [
                'id' => '68',
                'dealer_price' => '3.00',
                'product_name' => 'Ancillary Fees',
                'product_type' => '',
                'product_sku' => 'ANCILLARY-FEES',
                'is_fee_product' => '1',
                'Tag' => [
                    ['name' => 'Fee'],
                    ['name' => 'Hidden'],
                ],
                'Collection' => [
                ],
            ],
        ];
    }

    public static function defaultB2bCartItems()
    {
        return [
            [
                'id' => 1,
                'dealer_price' => 10.00,
                'total_price' => 20.00,
                'quantity' => 2,
                'Product' => [
                    'id' => '21',
                    'product_type' => 'Type 1',
                    'product_name' => 'Super Bike',
                    'product_sku' => 'Sku 1',
                    'is_fee_product' => '0',
                    'Tag' => [
                        [
                            'name' => 'Tag 1',
                        ],
                        [
                            'name' => 'Tag 2',
                        ],
                    ],
                    'Collection' => [
                        [
                            'title' => 'Collection 1',
                        ],
                        [
                            'title' => 'Collection 2',
                        ],
                    ],
                ],
            ],
            [
                'id' => '-1',
                'quantity' => '2',
                'dealer_price' => '3.00',
                'Product' => [
                    'id' => '68',
                    'product_type' => '',
                    'product_sku' => 'ANCILLARY-FEES',
                    'product_name' => 'Ancillary Fees',
                    'is_fee_product' => '1',
                    'Collection' => [
                    ],
                    'Tag' => [
                        ['name' => 'Fee'],
                        ['name' => 'Hidden'],
                    ],
                ],
            ],
        ];
    }

    public static function testFindForEcommerceByCode(): array
    {
        $items = static::defaultItems();
        $expected = static::eCommerceDiscountInfo();

        return [
            'valid' => [
                'code' => $expected['code'],
                'items' => $items,
                'expected' => $expected,
            ],
            'invalid_code' => [
                'code' => 'INVALID_CODE',
                'items' => $items,
                'expected' => [],
            ],
            'invalid_items' => [
                'code' => $expected['code'],
                'items' => array_filter($items, fn(array $item) => $item['product_title'] === 'Ancillary Fees'),
                'expected' => [],
            ],
        ];
    }

    public static function testFindForEcommerceByAuto(): array
    {
        $items = static::defaultItems();
        $expected = static::eCommerceDiscountInfo();
        $expected['is_automatic'] = true;

        return [
            'valid_items' => [
                'items' => $items,
                'expected' => $expected,
            ],
            'invalid_items' => [
                'items' => array_filter($items, fn(array $item) => $item['product_title'] === 'Ancillary Fees'),
                'expected' => [],
            ],
        ];
    }

    public static function testProcessOrderOptionsWithValidValues(): array
    {
        $defaultItems = array_map(
            fn(array $item): array => array_merge($item, [
                'tags' => ($item['handle'] === 'super-bike') ? ['Bike'] : [],
                'collections' => ['Home page'],
            ]),
            Hash::combine(static::defaultItems(), '{n}.id', '{n}')
        );

        return [
            'buy_x_get_y' => [
                'orderOptions' => [
                    'prerequisite_subtotal' => '0.00',
                    'prerequisite_quantity' => '2',
                    'prerequisite_option' => 'category',
                    'prerequisite_values' => '["Accessories"]',
                    'order_quantity' => '3',
                    'order_option' => 'category',
                    'order_values' => '["Bikes"]',
                ],
                'expectedDiscountDiff' => [
                    'items' => [
                        20016274759 => array_merge($defaultItems[20016274759], [
                            'quantity' => 1,
                            'price' => 399.99,
                            'line_price' => 399.99,
                            'total_discount' => '100.00',
                        ]),
                        17532934023 => array_merge($defaultItems[17532934023], [
                            'quantity' => 2,
                            'price' => 1200.00,
                            'line_price' => 2400.00,
                            'total_discount' => '600.00',
                        ]),
                    ],
                    'auto_add_items' => [],
                ],
            ],
            'buy_x_get_y_any' => [
                'orderOptions' => [
                    'option' => 'amount_off',
                    'option_amount' => '800.00',
                    'prerequisite_subtotal' => '0.00',
                    'prerequisite_quantity' => '2',
                    'prerequisite_option' => 'category',
                    'prerequisite_values' => '["Accessories"]',
                    'order_quantity' => '0',
                    'order_option' => 'category',
                    'order_values' => '["Bikes"]',
                ],
                'expectedDiscountDiff' => [
                    'items' => [
                        20016274759 => array_merge($defaultItems[20016274759], [
                            'quantity' => 2,
                            'price' => 399.99,
                            'line_price' => 799.98,
                            'total_discount' => '200.00',
                        ]),
                        17532934023 => array_merge($defaultItems[17532934023], [
                            'quantity' => 2,
                            'price' => 1200.00,
                            'line_price' => 2400.00,
                            'total_discount' => '600.00',
                        ]),
                    ],
                    'auto_add_items' => [],
                ],
            ],
            'buy_x_auto_add_y' => [
                'orderOptions' => [
                    'prerequisite_subtotal' => '0.00',
                    'prerequisite_quantity' => '2',
                    'prerequisite_option' => 'category',
                    'prerequisite_values' => '["Accessories"]',
                    'order_quantity' => '7',
                    'order_option' => 'product_variant',
                    'order_values' => '["BICYCLE-1","BICYCLE-2"]',
                    'auto_add_sku_quantities' => '{"BICYCLE-1":3,"BICYCLE-2":4}',
                ],
                'expectedDiscountDiff' => [
                    'items' => [
                        20016274759 => array_merge($defaultItems[20016274759], [
                            'quantity' => 2,
                            'price' => 399.99,
                            'line_price' => 799.98,
                            'total_discount' => '200.00',
                        ]),
                        17532934023 => array_merge($defaultItems[17532934023], [
                            'quantity' => 2,
                            'price' => 1200.00,
                            'line_price' => 2400.00,
                            'total_discount' => '600.00',
                        ]),
                    ],
                    'auto_add_items' => [
                        ['sku' => 'BICYCLE-1', 'quantity' => 1],
                        ['sku' => 'BICYCLE-2', 'quantity' => 2],
                    ],
                ],
            ],
            'spend_x_get_y' => [
                'orderOptions' => [
                    'prerequisite_subtotal' => '1199.98',
                    'prerequisite_quantity' => '0',
                    'prerequisite_option' => 'category',
                    'prerequisite_values' => '["Accessories"]',
                    'order_quantity' => '3',
                    'order_option' => 'category',
                    'order_values' => '["Bikes"]',
                ],
                'expectedDiscountDiff' => [
                    'items' => [
                        20016274759 => array_merge($defaultItems[20016274759], [
                            'quantity' => 1,
                            'price' => 399.99,
                            'line_price' => 399.99,
                            'total_discount' => '100.00',
                        ]),
                        17532934023 => array_merge($defaultItems[17532934023], [
                            'quantity' => 2,
                            'price' => 1200.00,
                            'line_price' => 2400.00,
                            'total_discount' => '600.00',
                        ]),
                    ],
                    'auto_add_items' => [],
                ],
            ],
            'all' => [
                'orderOptions' => [
                    'prerequisite_subtotal' => '0.00',
                    'prerequisite_quantity' => '0',
                    'prerequisite_option' => null,
                    'prerequisite_values' => null,
                    'order_quantity' => '0',
                    'order_option' => 'all',
                    'order_values' => '[]',
                ],
                'expectedDiscountDiff' => [
                    'items' => [
                        17533140807 => array_merge($defaultItems[17533140807], [
                            'quantity' => 2,
                            'price' => 599.99,
                            'line_price' => 1199.98,
                            'total_discount' => '300.00',
                        ]),
                        20016274759 => array_merge($defaultItems[20016274759], [
                            'quantity' => 2,
                            'price' => 399.99,
                            'line_price' => 799.98,
                            'total_discount' => '200.00',
                        ]),
                        17532934023 => array_merge($defaultItems[17532934023], [
                            'quantity' => 2,
                            'price' => 1200.00,
                            'line_price' => 2400.00,
                            'total_discount' => '600.00',
                        ]),
                    ],
                    'auto_add_items' => [],
                ],
            ],
            'category' => [
                'orderOptions' => [
                    'prerequisite_subtotal' => '0.00',
                    'prerequisite_quantity' => '0',
                    'prerequisite_option' => null,
                    'prerequisite_values' => null,
                    'order_quantity' => '0',
                    'order_option' => 'category',
                    'order_values' => '["Accessories"]',
                ],
                'expectedDiscountDiff' => [
                    'items' => [
                        17533140807 => array_merge($defaultItems[17533140807], [
                            'quantity' => 2,
                            'price' => 599.99,
                            'line_price' => 1199.98,
                            'total_discount' => '300.00',
                        ]),
                    ],
                    'auto_add_items' => [],
                ],
            ],
            'product_title' => [
                'orderOptions' => [
                    'prerequisite_subtotal' => '0.00',
                    'prerequisite_quantity' => '0',
                    'prerequisite_option' => null,
                    'prerequisite_values' => null,
                    'order_quantity' => '0',
                    'order_option' => 'product_title',
                    'order_values' => '["Super Bike"]',
                ],
                'expectedDiscountDiff' => [
                    'items' => [
                        20016274759 => array_merge($defaultItems[20016274759], [
                            'quantity' => 2,
                            'price' => 399.99,
                            'line_price' => 799.98,
                            'total_discount' => '200.00',
                        ]),
                        17532934023 => array_merge($defaultItems[17532934023], [
                            'quantity' => 2,
                            'price' => 1200.00,
                            'line_price' => 2400.00,
                            'total_discount' => '600.00',
                        ]),
                    ],
                    'auto_add_items' => [],
                ],
            ],
            'product_variant' => [
                'orderOptions' => [
                    'prerequisite_subtotal' => '0.00',
                    'prerequisite_quantity' => '0',
                    'prerequisite_option' => null,
                    'prerequisite_values' => null,
                    'order_quantity' => '0',
                    'order_option' => 'product_variant',
                    'order_values' => '["BELL","BICYCLE-1"]',
                ],
                'expectedDiscountDiff' => [
                    'items' => [
                        17533140807 => array_merge($defaultItems[17533140807], [
                            'quantity' => 2,
                            'price' => 599.99,
                            'line_price' => 1199.98,
                            'total_discount' => '300.00',
                        ]),
                        17532934023 => array_merge($defaultItems[17532934023], [
                            'quantity' => 2,
                            'price' => 1200.00,
                            'line_price' => 2400.00,
                            'total_discount' => '600.00',
                        ]),
                    ],
                    'auto_add_items' => [],
                ],
            ],
        ];
    }

    public static function testProcessOrderOptionsWithInvalidSubtotal(): array
    {
        return array_filter(static::testProcessOrderOptionsWithValidValues(), function($case) {
            return !$case['orderOptions']['prerequisite_option'] || $case['orderOptions']['prerequisite_subtotal'] > 0.00;
        });
    }

    public static function testProcessOrderOptionsWithInvalidQuantity(): array
    {
        return array_filter(static::testProcessOrderOptionsWithValidValues(), function($case) {
            return !$case['orderOptions']['prerequisite_option'] || $case['orderOptions']['prerequisite_quantity'] > 0;
        });
    }

    public static function testProcessOrderOptionsWithInvalidValues(): array
    {
        return [
            // 'all' has no invalid order_values
            'category' => [
                'orderOptions' => [
                    'prerequisite_subtotal' => '0.00',
                    'prerequisite_quantity' => '0',
                    'prerequisite_option' => null,
                    'prerequisite_values' => null,
                    'order_quantity' => '0',
                    'order_option' => 'category',
                    'order_values' => '["Bogus Category"]',
                ],
            ],
            'product_title' => [
                'orderOptions' => [
                    'prerequisite_subtotal' => '0.00',
                    'prerequisite_quantity' => '0',
                    'prerequisite_option' => null,
                    'prerequisite_values' => null,
                    'order_quantity' => '0',
                    'order_option' => 'product_title',
                    'order_values' => '["Bogus Product"]',
                ],
            ],
            'product_variant' => [
                'orderOptions' => [
                    'prerequisite_subtotal' => '0.00',
                    'prerequisite_quantity' => '0',
                    'prerequisite_option' => null,
                    'prerequisite_values' => null,
                    'order_quantity' => '0',
                    'order_option' => 'product_variant',
                    'order_values' => '["BOGUS-1"]',
                ],
            ],
            'prerequisite_subtotal' => [
                'orderOptions' => [
                    'prerequisite_subtotal' => '3199.99',
                    'prerequisite_quantity' => '0',
                    'prerequisite_option' => null,
                    'prerequisite_values' => null,
                    'order_quantity' => '0',
                    'order_option' => 'category',
                    'order_values' => '["Bikes"]',
                ],
            ],
            'prerequisite_quantity' => [
                'orderOptions' => [
                    'prerequisite_subtotal' => '0.00',
                    'prerequisite_quantity' => '5',
                    'prerequisite_option' => null,
                    'prerequisite_values' => null,
                    'order_quantity' => '0',
                    'order_option' => 'category',
                    'order_values' => '["Bikes"]',
                ],
            ],
            'prerequisite_category' => [
                'discount' => [
                    'prerequisite_subtotal' => '0.00',
                    'prerequisite_quantity' => '2',
                    'prerequisite_option' => 'category',
                    'prerequisite_values' => '["Bogus Category"]',
                    'order_quantity' => '1',
                    'order_option' => 'category',
                    'order_values' => '["Bikes"]',
                ],
            ],
            'prerequisite_product_title' => [
                'discount' => [
                    'prerequisite_subtotal' => '0.00',
                    'prerequisite_quantity' => '2',
                    'prerequisite_option' => 'product_title',
                    'prerequisite_values' => '["Bogus Product"]',
                    'order_quantity' => '1',
                    'order_option' => 'category',
                    'order_values' => '["Bikes"]',
                ],
            ],
            'prerequisite_product_variant' => [
                'discount' => [
                    'prerequisite_subtotal' => '0.00',
                    'prerequisite_quantity' => '2',
                    'prerequisite_option' => 'product_variant',
                    'prerequisite_values' => '["BOGUS-1"]',
                    'order_quantity' => '1',
                    'order_option' => 'category',
                    'order_values' => '["Bikes"]',
                ],
            ],
        ];
    }

    public function testProcessOrderOptionsWithDeductedGetYQuantity(): array
    {
        $expectedDiscountDiff = [
            'items' => [
                20016274759 => [
                    'id' => 20016274759,
                    'properties' => null,
                    'quantity' => 1,
                    'variant_id' => 20016274759,
                    'key' => '20016274759:45a63f853daad116d59153d210f4fbb2',
                    'title' => 'Super Bike - Blue',
                    'price' => 399.99,
                    'original_price' => 39999,
                    'discounted_price' => 39999,
                    'line_price' => 399.99,
                    'original_line_price' => 79998,
                    'total_discount' => '100.00',
                    'discounts' => [],
                    'sku' => 'BICYCLE-2',
                    'grams' => 4536,
                    'vendor' => 'aron-shipearly',
                    'product_id' => 5572298951,
                    'gift_card' => false,
                    'url' => '/products/super-bike?variant=20016274759',
                    'image' => null,
                    'handle' => 'super-bike',
                    'requires_shipping' => true,
                    'product_type' => 'Bikes',
                    'product_title' => 'Super Bike',
                    'product_description' => '',
                    'variant_title' => 'Blue',
                    'variant_options' => [
                        0 => 'Blue',
                    ],
                    'tags' => [
                        0 => 'Bike',
                    ],
                    'collections' => [
                        0 => 'Home page',
                    ],
                ],
                // Removes 'Super Bike - Red' 2x1200.00 from items
            ],
            'auto_add_items' => [],
        ];

        return [
            'buy_x_get_y_full_overlap' => [
                'orderOptions' => [
                    'prerequisite_subtotal' => '0.00',
                    'prerequisite_quantity' => '3',
                    'prerequisite_option' => 'category',
                    'prerequisite_values' => '["Bikes"]',
                    'order_quantity' => '1',
                    'order_option' => 'category',
                    'order_values' => '["Bikes"]',
                ],
                'expectedDiscountDiff' => $expectedDiscountDiff,
            ],
            'spend_x_get_y_full_overlap' => [
                'orderOptions' => [
                    'prerequisite_subtotal' => '2400.01',
                    'prerequisite_quantity' => '0',
                    'prerequisite_option' => 'category',
                    'prerequisite_values' => '["Bikes"]',
                    'order_quantity' => '1',
                    'order_option' => 'category',
                    'order_values' => '["Bikes"]',
                ],
                'expectedDiscountDiff' => $expectedDiscountDiff,
            ],
            'buy_x_get_y_partial_overlap' => [
                'orderOptions' => [
                    'prerequisite_subtotal' => '0.00',
                    'prerequisite_quantity' => '3',
                    'prerequisite_option' => 'category',
                    'prerequisite_values' => '["Bikes"]',
                    'order_quantity' => '1',
                    'order_option' => 'product_variant',
                    'order_values' => '["BICYCLE-2"]',
                ],
                'expectedDiscountDiff' => $expectedDiscountDiff,
            ],
            'spend_x_get_y_partial_overlap' => [
                'orderOptions' => [
                    'prerequisite_subtotal' => '2400.01',
                    'prerequisite_quantity' => '0',
                    'prerequisite_option' => 'category',
                    'prerequisite_values' => '["Bikes"]',
                    'order_quantity' => '1',
                    'order_option' => 'product_variant',
                    'order_values' => '["BICYCLE-2"]',
                ],
                'expectedDiscountDiff' => $expectedDiscountDiff,
            ],
        ];
    }

    public static function testCalculateEcommerceItemDiscounts(): array
    {
        $discountOptions = [
            'percent off' => [
                'option' => 'percent_off',
                'option_amount' => '25.00'
            ],
            'amount off' => [
                'option' => 'amount_off',
                'option_amount' => '100.00'
            ],
            'free' => [
                'option' => 'free',
                'option_amount' => '0.00'
            ],
        ];
        $retailerOptions = [
            'retailer none' => [
                'retailer_option' => 'none',
                'retailer_values' => ''
            ],
            'retailer all' => [
                'retailer_option' => 'all',
                'retailer_values' => ''
            ],
            'retailer tier' => [
                'retailer_option' => 'tiers',
                'retailer_values' => '3'
            ],
            'retailer select' => [
                'retailer_option' => 'select_retailers',
                'retailer_values' => '7,10'
            ],
        ];
        $buyXGetYOptions = [
            'not buy x get y' => [
                'prerequisite_option' => null,
                'prerequisite_values' => null,
                'prerequisite_subtotal' => '0.00',
                'prerequisite_quantity' => '0',
                'order_option' => 'product_title',
                'order_values' => '["Super Bell","Super Bike"]',
                'order_quantity' => '0',
                'auto_add_sku_quantities' => null,
            ],
            'buy x get y has y' => [
                'prerequisite_option' => 'product_title',
                'prerequisite_values' => '["Super Bike"]',
                'prerequisite_subtotal' => '0.00',
                'prerequisite_quantity' => '2',
                'order_option' => 'product_title',
                'order_values' => '["Super Bell"]',
                'order_quantity' => '2',
                'auto_add_sku_quantities' => null,
            ],
            'buy x get y missing y' => [
                'prerequisite_option' => 'product_title',
                'prerequisite_values' => '["Super Bike"]',
                'prerequisite_subtotal' => '0.00',
                'prerequisite_quantity' => '2',
                'order_option' => 'product_title',
                'order_values' => '["Bogus Title"]',
                'order_quantity' => '2',
                'auto_add_sku_quantities' => null,
            ],
            'buy x auto add y' => [
                'prerequisite_option' => 'product_title',
                'prerequisite_values' => '["Super Bike"]',
                'prerequisite_subtotal' => '0.00',
                'prerequisite_quantity' => '2',
                'order_option' => 'product_variant',
                'order_values' => '["Bogus-1"]',
                'order_quantity' => '2',
                'auto_add_sku_quantities' => '{"Bogus-1":2}',
            ],
        ];
        $discountTests = array();
        foreach ($discountOptions as $discountName => $discount) {
            foreach ($retailerOptions as $retailerName => $retailer) {
                foreach ($buyXGetYOptions as $buyXGetYName => $buyXGetY) {

                    $testCase = "{$discountName} / {$retailerName} / {$buyXGetYName}";
                    $option = $discount['option'];
                    $option_amount = $discount['option_amount'];

                    $retailer_option = $retailer['retailer_option'];
                    $retailer_values = $retailer['retailer_values'];

                    $prerequisite_option = $buyXGetY['prerequisite_option'];
                    $prerequisite_values = $buyXGetY['prerequisite_values'];
                    $prerequisite_subtotal = $buyXGetY['prerequisite_subtotal'];
                    $prerequisite_quantity = $buyXGetY['prerequisite_quantity'];
                    $order_option = $buyXGetY['order_option'];
                    $order_values = $buyXGetY['order_values'];
                    $order_quantity = $buyXGetY['order_quantity'];
                    $auto_add_sku_quantities = $buyXGetY['auto_add_sku_quantities'];

                    $DiscountRule = [
                        0 => compact(
                            'option',
                            'option_amount',
                            'prerequisite_option',
                            'prerequisite_values',
                            'prerequisite_subtotal',
                            'prerequisite_quantity',
                            'order_option',
                            'order_values',
                            'order_quantity',
                            'auto_add_sku_quantities',
                        ),
                    ];
                    $discountInfo = compact(
                        'retailer_option',
                        'retailer_values',
                        'DiscountRule'
                    );

                    $isBuyXGetY = DiscountRule::isBuyXGetY($discountInfo['DiscountRule'][0]) ? 'isBuyXGetY' : 'notBuyXGetY';
                    $isBuyXGetY = !strpos($order_values, 'Bogus') ? $isBuyXGetY : 'isBuyXGetYMissingY';
                    $items = [
                        ['id' => self::BELL_ID, 'line_price' => '1199.98', 'quantity' => 2],
                        ['id' => self::BIKE_BLUE_ID, 'line_price' => '799.98', 'quantity' => 2],
                        ['id' => self::BIKE_RED_ID, 'line_price' => '2400.00', 'quantity' => 2],
                    ];
                    $items = Hash::combine($items, '{n}.id', '{n}');
                    $discount_amounts = [
                        self::BELL_ID => [
                            'isBuyXGetY' => ['percent_off' => '300.00', 'amount_off' => '100.00', 'free' => '1199.98'],
                            'isBuyXGetYMissingY' => ['percent_off' => '0.00', 'amount_off' => '0.00', 'free' => '0.00'],
                            'notBuyXGetY' => ['percent_off' => '300.00', 'amount_off' => '27.27', 'free' => '1199.98']
                        ],
                        self::BIKE_BLUE_ID => [
                            'isBuyXGetY' => ['percent_off' => '0.00', 'amount_off' => '0.00', 'free' => '0.00'],
                            'isBuyXGetYMissingY' =>['percent_off' => '0.00', 'amount_off' => '0.00', 'free' => '0.00'],
                            'notBuyXGetY' => ['percent_off' => '200.00', 'amount_off' => '18.18', 'free' => '799.98']
                        ],
                        self::BIKE_RED_ID => [
                            'isBuyXGetY' => ['percent_off' => '0', 'amount_off' => '0.00', 'free' => '0.00'],
                            'isBuyXGetYMissingY' => ['percent_off' => '0', 'amount_off' => '0.00', 'free' => '0.00'],
                            'notBuyXGetY' => ['percent_off' => '600.00', 'amount_off' => '54.55', 'free' => '2400.00']
                        ],
                    ];
                    $filtered_retailers = [
                        'all' => ['7', '10', '17'],
                        'tiers' => ['10'],
                        'select_retailers' => ['7', '10'],
                        'none' => [],
                    ];
                    $expectedDiscounts = array_map(function($item) use ($discount_amounts, $option, $filtered_retailers, $retailer_option, $order_quantity, $isBuyXGetY) {

                        $id = $item['id'];
                        $discount_amount = $discount_amounts[$id][$isBuyXGetY][$option];
                        $discount_quantity = $order_quantity ?: $item['quantity'];
                        $retailers = $filtered_retailers[$retailer_option];
                        $is_product_discount = ($option === DiscountOptions::PERCENT_OFF && $isBuyXGetY === 'notBuyXGetY');
                        return compact('id', 'discount_amount', 'discount_quantity', 'retailers', 'is_product_discount');
                    }, $items);

                    $expectedDiscounts = array_filter($expectedDiscounts, function($expectedDiscount){
                        return $expectedDiscount['discount_amount'] > 0;
                    });

                    $discountTests[$testCase] = [$discountInfo, $expectedDiscounts];
                }
            }
        }
        return $discountTests;
    }

    public static function testCalculateOverlappingMultiRuleEcommerceItemDiscounts(): array
    {
        $itemIdMap = array_column(static::defaultItems(), 'id', 'sku');
        $maxedOutDiscountItems = [
            $itemIdMap['BELL'] => [
                'id' => $itemIdMap['BELL'],
                'discount_amount' => '1799.97',
                'discount_quantity' => 3,
                'is_product_discount' => false,
                'retailers' => ['7', '10'],
            ],
            $itemIdMap['BICYCLE-2'] => [
                'id' => $itemIdMap['BICYCLE-2'],
                'discount_amount' => '1199.97',
                'discount_quantity' => 3,
                'is_product_discount' => false,
                'retailers' => ['7', '10'],
            ],
            $itemIdMap['BICYCLE-1'] => [
                'id' => $itemIdMap['BICYCLE-1'],
                'discount_amount' => '3600.00',
                'discount_quantity' => 3,
                'is_product_discount' => false,
                'retailers' => ['7', '10'],
            ],
        ];

        return [
            'flat_sum_limited_to_max' => [
                'discountRules' => [
                    [
                        'option' => 'amount_off',
                        'option_amount' => format_number(array_sum(array_column($maxedOutDiscountItems, 'discount_amount'))),
                        'prerequisite_subtotal' => '0.00',
                        'prerequisite_quantity' => '0',
                        'prerequisite_option' => null,
                        'prerequisite_values' => null,
                        'order_quantity' => '0',
                        'order_option' => DiscountOrderOptions::ALL,
                        'order_values' => '',
                        'auto_add_sku_quantities' => null,
                    ],
                    [
                        'option' => 'amount_off',
                        'option_amount' => '150.00',
                        'prerequisite_subtotal' => '0.00',
                        'prerequisite_quantity' => '0',
                        'prerequisite_option' => null,
                        'prerequisite_values' => null,
                        'order_quantity' => '0',
                        'order_option' => DiscountOrderOptions::ALL,
                        'order_values' => '',
                        'auto_add_sku_quantities' => null,
                    ],
                ],
                'expected' => $maxedOutDiscountItems,
            ],
            'flat_overlapping_sum' => [
                'discountRules' => [
                    [
                        'option' => 'amount_off',
                        'option_amount' => '150.00',
                        'prerequisite_subtotal' => '0.00',
                        'prerequisite_quantity' => '0',
                        'prerequisite_option' => null,
                        'prerequisite_values' => null,
                        'order_quantity' => '0',
                        'order_option' => DiscountOrderOptions::ALL,
                        'order_values' => '',
                        'auto_add_sku_quantities' => null,
                    ],
                    [
                        'option' => 'amount_off',
                        'option_amount' => '150.00',
                        'prerequisite_subtotal' => '0.00',
                        'prerequisite_quantity' => '0',
                        'prerequisite_option' => null,
                        'prerequisite_values' => null,
                        'order_quantity' => '0',
                        'order_option' => DiscountOrderOptions::ALL,
                        'order_values' => '',
                        'auto_add_sku_quantities' => null,
                    ],
                ],
                'expected' => (function(array $discountItems) use ($itemIdMap): array {
                    $discountItems[$itemIdMap['BELL']]['discount_amount'] = '81.82';
                    $discountItems[$itemIdMap['BICYCLE-2']]['discount_amount'] = '54.54';
                    $discountItems[$itemIdMap['BICYCLE-1']]['discount_amount'] = '163.64';

                    return $discountItems;
                })($maxedOutDiscountItems),
            ],
            'buy_x_get_y' => [
                'discountRules' => [
                    [
                        'option' => 'amount_off',
                        'option_amount' => '150.00',
                        'prerequisite_subtotal' => '0.00',
                        'prerequisite_quantity' => '1',
                        'prerequisite_option' => DiscountOrderOptions::PRODUCT_VARIANT,
                        'prerequisite_values' => '["BELL"]',
                        'order_quantity' => '2',
                        'order_option' => DiscountOrderOptions::PRODUCT_VARIANT,
                        'order_values' => '["BELL"]',
                        'auto_add_sku_quantities' => null,
                    ],
                    [
                        'option' => 'amount_off',
                        'option_amount' => '150.00',
                        'prerequisite_subtotal' => '0.00',
                        'prerequisite_quantity' => '1',
                        'prerequisite_option' => DiscountOrderOptions::PRODUCT_VARIANT,
                        'prerequisite_values' => '["BELL"]',
                        'order_quantity' => '2',
                        'order_option' => DiscountOrderOptions::PRODUCT_VARIANT,
                        'order_values' => '["BELL"]',
                        'auto_add_sku_quantities' => null,
                    ],
                    [
                        'option' => 'amount_off',
                        'option_amount' => '400.00',
                        'prerequisite_subtotal' => '0.00',
                        'prerequisite_quantity' => '1',
                        'prerequisite_option' => DiscountOrderOptions::PRODUCT_VARIANT,
                        'prerequisite_values' => '["BICYCLE-2"]',
                        'order_quantity' => '1',
                        'order_option' => DiscountOrderOptions::PRODUCT_VARIANT,
                        'order_values' => '["BICYCLE-2"]',
                        'auto_add_sku_quantities' => null,
                    ],
                    [
                        'option' => 'amount_off',
                        'option_amount' => '400.00',
                        'prerequisite_subtotal' => '0.00',
                        'prerequisite_quantity' => '1',
                        'prerequisite_option' => DiscountOrderOptions::PRODUCT_VARIANT,
                        'prerequisite_values' => '["BICYCLE-2"]',
                        'order_quantity' => '1',
                        'order_option' => DiscountOrderOptions::PRODUCT_VARIANT,
                        'order_values' => '["BICYCLE-2"]',
                        'auto_add_sku_quantities' => null,
                    ],
                    [
                        'option' => 'amount_off',
                        'option_amount' => '2400.01',
                        'prerequisite_subtotal' => '0.00',
                        'prerequisite_quantity' => '1',
                        'prerequisite_option' => DiscountOrderOptions::PRODUCT_VARIANT,
                        'prerequisite_values' => '["BICYCLE-1"]',
                        'order_quantity' => '2',
                        'order_option' => DiscountOrderOptions::PRODUCT_VARIANT,
                        'order_values' => '["BICYCLE-1"]',
                        'auto_add_sku_quantities' => null,
                    ],
                    [
                        'option' => 'amount_off',
                        'option_amount' => '1200.01',
                        'prerequisite_subtotal' => '0.00',
                        'prerequisite_quantity' => '1',
                        'prerequisite_option' => DiscountOrderOptions::PRODUCT_VARIANT,
                        'prerequisite_values' => '["BICYCLE-1"]',
                        'order_quantity' => '1',
                        'order_option' => DiscountOrderOptions::PRODUCT_VARIANT,
                        'order_values' => '["BICYCLE-1"]',
                        'auto_add_sku_quantities' => null,
                    ],
                ],
                'expected' => (function(array $discountItems) use ($itemIdMap): array {
                    // Limits quantity
                    $discountItems[$itemIdMap['BELL']] = array_merge($discountItems[$itemIdMap['BELL']], [
                        'discount_amount' => '300.00',
                        'discount_quantity' => 3,
                    ]);
                    // Limits price based on quantity
                    $discountItems[$itemIdMap['BICYCLE-2']] = array_merge($discountItems[$itemIdMap['BICYCLE-2']], [
                        'discount_amount' => '799.98',
                        'discount_quantity' => 2,
                    ]);
                    // Limits both
                    $discountItems[$itemIdMap['BICYCLE-1']] = array_merge($discountItems[$itemIdMap['BICYCLE-1']], [
                        'discount_amount' => '3600.00',
                        'discount_quantity' => 3,
                    ]);

                    return $discountItems;
                })($maxedOutDiscountItems),
            ],
            'buy_x_get_y_any' => [
                'discountRules' => [
                    [
                        'option' => 'amount_off',
                        'option_amount' => '150.00',
                        'prerequisite_subtotal' => '0.00',
                        'prerequisite_quantity' => '1',
                        'prerequisite_option' => DiscountOrderOptions::PRODUCT_VARIANT,
                        'prerequisite_values' => '["BELL"]',
                        'order_quantity' => '0',
                        'order_option' => DiscountOrderOptions::PRODUCT_VARIANT,
                        'order_values' => '["BELL"]',
                        'auto_add_sku_quantities' => null,
                    ],
                    [
                        'option' => 'amount_off',
                        'option_amount' => '150.00',
                        'prerequisite_subtotal' => '0.00',
                        'prerequisite_quantity' => '1',
                        'prerequisite_option' => DiscountOrderOptions::PRODUCT_VARIANT,
                        'prerequisite_values' => '["BELL"]',
                        'order_quantity' => '0',
                        'order_option' => DiscountOrderOptions::PRODUCT_VARIANT,
                        'order_values' => '["BELL"]',
                        'auto_add_sku_quantities' => null,
                    ],
                    [
                        'option' => 'amount_off',
                        'option_amount' => format_number(array_sum(array_column($maxedOutDiscountItems, 'discount_amount'))),
                        'prerequisite_subtotal' => '0.00',
                        'prerequisite_quantity' => '1',
                        'prerequisite_option' => DiscountOrderOptions::PRODUCT_VARIANT,
                        'prerequisite_values' => '["BICYCLE-1"]',
                        'order_quantity' => '0',
                        'order_option' => DiscountOrderOptions::PRODUCT_VARIANT,
                        'order_values' => '["BICYCLE-2"]',
                        'auto_add_sku_quantities' => null,
                    ],
                    [
                        'option' => 'amount_off',
                        'option_amount' => '150.00',
                        'prerequisite_subtotal' => '0.00',
                        'prerequisite_quantity' => '1',
                        'prerequisite_option' => DiscountOrderOptions::PRODUCT_VARIANT,
                        'prerequisite_values' => '["BICYCLE-1"]',
                        'order_quantity' => '0',
                        'order_option' => DiscountOrderOptions::PRODUCT_VARIANT,
                        'order_values' => '["BICYCLE-2"]',
                        'auto_add_sku_quantities' => null,
                    ],
                    [
                        'option' => 'amount_off',
                        'option_amount' => format_number(array_sum(array_column($maxedOutDiscountItems, 'discount_amount'))),
                        'prerequisite_subtotal' => '0.00',
                        'prerequisite_quantity' => '2',
                        'prerequisite_option' => DiscountOrderOptions::PRODUCT_VARIANT,
                        'prerequisite_values' => '["BICYCLE-1"]',
                        'order_quantity' => '0',
                        'order_option' => DiscountOrderOptions::PRODUCT_VARIANT,
                        'order_values' => '["BICYCLE-1"]',
                        'auto_add_sku_quantities' => null,
                    ],
                    [
                        'option' => 'amount_off',
                        'option_amount' => '150.00',
                        'prerequisite_subtotal' => '0.00',
                        'prerequisite_quantity' => '2',
                        'prerequisite_option' => DiscountOrderOptions::PRODUCT_VARIANT,
                        'prerequisite_values' => '["BICYCLE-1"]',
                        'order_quantity' => '0',
                        'order_option' => DiscountOrderOptions::PRODUCT_VARIANT,
                        'order_values' => '["BICYCLE-1"]',
                        'auto_add_sku_quantities' => null,
                    ],
                ],
                'expected' => (function(array $discountItems) use ($itemIdMap): array {
                    // Simple sum
                    $discountItems[$itemIdMap['BELL']] = array_merge($discountItems[$itemIdMap['BELL']], [
                        'discount_amount' => '300.00',
                        'discount_quantity' => 3,
                    ]);
                    // Limits price
                    $discountItems[$itemIdMap['BICYCLE-2']] = array_merge($discountItems[$itemIdMap['BICYCLE-2']], [
                        'discount_amount' => '1199.97',
                        'discount_quantity' => 3,
                    ]);
                    // Excludes prerequisite
                    $discountItems[$itemIdMap['BICYCLE-1']] = array_merge($discountItems[$itemIdMap['BICYCLE-1']], [
                        'discount_amount' => format_number(1200.00 + 150.00),
                        'discount_quantity' => 2,
                    ]);

                    return $discountItems;
                })($maxedOutDiscountItems),
            ],
        ];
    }

    public static function testFindManageDiscountsForm(): array
    {
        $default = static::defaultDiscountFormData();
        $defaultBuyXGetY = array_merge($default, [
            'Discount' => array_diff_key(array_merge($default['Discount'], [
                'description' => 'Buy 2 of category (Accessories), Get 1 of titles (Super Bell, Super Bike) 25.00% off',
                'option' => 'buy_x_get_y',
            ]), array_flip(['prerequisite_subtotal', 'prerequisite_quantity'])),
            'DiscountRule' => [
                0 => [
                    'id' => $default['DiscountRule'][0]['id'],
                    'discount_id' => $default['DiscountRule'][0]['discount_id'],
                    'option' => 'buy_x_get_y',
                    'auto_add_sku_quantities' => [],
                    'is_buy_x_get_y' => '1',
                    'is_auto_add_y' => '0',
                    'exclude_shipping_rates_above' => false,
                    'buy_x_get_y_option' => 'percent_off',
                    'buy_x_get_y_amount' => '25.00',
                    'buy_x_value_type' => 'quantity',
                    'buy_x_quantity' => '2',
                    'buy_x_subtotal' => null,
                    'buy_x_option' => 'category',
                    'buy_x_values' => [
                        'category' => ['Accessories'],
                    ],
                    'get_y_quantity' => '1',
                    'get_y_option' => 'product_title',
                    'get_y_values' => [
                        'product_title' => ['Super Bell', 'Super Bike'],
                    ],
                    'get_y_any_quantity' => false,
                    'auto_add_y' => [],
                ],
            ],
        ]);

        return [
            'standard' => [
                'seedUpdate' => [
                ],
                'expected' => $default,
            ],
            'prerequisite_subtotal' => [
                'seedUpdate' => [
                    'Discount' => [
                        'id' => $default['Discount']['id'],
                        'description' => '25.00% off titles (Super Bell, Super Bike) Applies To: Orders Over 0.01',
                    ],
                    'DiscountRule' => [
                        0 => [
                            'id' => $default['DiscountRule'][0]['id'],
                            'prerequisite_subtotal' => '0.01',
                        ],
                    ],
                ],
                'expected' => array_merge($default, [
                    'Discount' => array_merge($default['Discount'], [
                        'description' => '25.00% off titles (Super Bell, Super Bike) Applies To: Orders Over 0.01',
                        'prerequisite_subtotal' => '0.01',
                        'prerequisite_option' => 'subtotal',
                        'enable_free_freight' => false,
                    ]),
                    'DiscountRule' => [
                        0 => array_merge($default['DiscountRule'][0], [
                            'prerequisite_subtotal' => '0.01',
                            'prerequisite_option' => 'subtotal',
                        ]),
                    ],
                ]),
            ],
            'prerequisite_quantity' => [
                'seedUpdate' => [
                    'Discount' => [
                        'id' => $default['Discount']['id'],
                        'description' => '25.00% off titles (Super Bell, Super Bike) Applies To: Orders with Quantity Over 2',
                    ],
                    'DiscountRule' => [
                        0 => [
                            'id' => $default['DiscountRule'][0]['id'],
                            'prerequisite_quantity' => '2',
                        ],
                    ],
                ],
                'expected' => array_merge($default, [
                    'Discount' => array_merge($default['Discount'], [
                        'description' => '25.00% off titles (Super Bell, Super Bike) Applies To: Orders with Quantity Over 2',
                        'prerequisite_quantity' => '2',
                        'prerequisite_option' => 'quantity',
                        'enable_free_freight' => false,
                    ]),
                    'DiscountRule' => [
                        0 => array_merge($default['DiscountRule'][0], [
                            'prerequisite_quantity' => '2',
                            'prerequisite_option' => 'quantity',
                        ]),
                    ],
                ]),
            ],
            'buy_x_get_y' => [
                'seedUpdate' => [
                    'Discount' => [
                        'id' => $defaultBuyXGetY['Discount']['id'],
                        'description' => 'Buy 2 of category (Accessories), Get 1 of titles (Super Bell, Super Bike) 25.00% off',
                    ],
                    'DiscountRule' => [
                        0 => [
                            'id' => $defaultBuyXGetY['DiscountRule'][0]['id'],
                            'prerequisite_quantity' => '2',
                            'prerequisite_option' => 'category',
                            'prerequisite_values' => '["Accessories"]',
                            'order_quantity' => '1',
                        ],
                    ],
                ],
                'expected' => $defaultBuyXGetY,
            ],
            'buy_x_get_y_any' => [
                'seedUpdate' => [
                    'Discount' => [
                        'id' => $defaultBuyXGetY['Discount']['id'],
                        'description' => 'Buy 2 of category (Accessories), Get titles (Super Bell, Super Bike) 25.00% off',
                    ],
                    'DiscountRule' => [
                        0 => [
                            'id' => $defaultBuyXGetY['DiscountRule'][0]['id'],
                            'prerequisite_quantity' => '2',
                            'prerequisite_option' => 'category',
                            'prerequisite_values' => '["Accessories"]',
                            'order_quantity' => '0',
                        ],
                    ],
                ],
                'expected' => array_merge($defaultBuyXGetY, [
                    'Discount' => array_merge($defaultBuyXGetY['Discount'], [
                        'description' => 'Buy 2 of category (Accessories), Get titles (Super Bell, Super Bike) 25.00% off',
                    ]),
                    'DiscountRule' => [
                        0 => array_merge($defaultBuyXGetY['DiscountRule'][0], [
                            'get_y_quantity' => null,
                            'get_y_any_quantity' => true,
                        ]),
                    ],
                ]),
            ],
            'buy_x_auto_add_y' => [
                'seedUpdate' => [
                    'Discount' => [
                        'id' => $default['Discount']['id'],
                        'description' => 'Buy 2 of category (Accessories), Get 2 of BICYCLE-2, 3 of BICYCLE-3 25.00% off',
                    ],
                    'DiscountRule' => [
                        0 => [
                            'id' => $default['DiscountRule'][0]['id'],
                            'prerequisite_quantity' => '2',
                            'prerequisite_option' => 'category',
                            'prerequisite_values' => '["Accessories"]',
                            'order_quantity' => '5',
                            'auto_add_sku_quantities' => '{"BICYCLE-2":2,"BICYCLE-3":3}',
                        ],
                    ],
                ],
                'expected' => array_merge($defaultBuyXGetY, [
                    'Discount' => array_merge($defaultBuyXGetY['Discount'], [
                        'description' => 'Buy 2 of category (Accessories), Get 2 of BICYCLE-2, 3 of BICYCLE-3 25.00% off',
                        'is_auto_add_y' => '1',
                        'enable_free_freight' => false,
                    ]),
                    'DiscountRule' => [
                        0 => array_merge($defaultBuyXGetY['DiscountRule'][0], [
                            'auto_add_sku_quantities' => [
                                'BICYCLE-2' => '2',
                                'BICYCLE-3' => '3',
                            ],
                            'is_auto_add_y' => '1',
                            'get_y_quantity' => '5',
                            'auto_add_y' => [
                                ['sku' => 'BICYCLE-2', 'quantity' => '2'],
                                ['sku' => 'BICYCLE-3', 'quantity' => '3'],
                            ],
                        ]),
                    ],
                ]),
            ],
        ];
    }

    public static function testReadBuyXGetYForm(): array
    {
        return [
            'default' => [
                'data' => [
                    'id' => '1',
                    'option' => 'percent_off',
                    'option_amount' => '25.00',
                    'exclude_shipping_rates_above' => '0',
                    'buy_x_value_type' => 'quantity',
                    'buy_x_option' => 'product_title',
                    'buy_x_quantity' => '',
                    'buy_x_subtotal' => '',
                    'buy_x_values' => [
                        'product_title' => '',
                        'product_variant' => '',
                        'category' => '',
                    ],
                    'get_y_option' => 'product_title',
                    'get_y_quantity' => '',
                    'get_y_any_quantity' => '0',
                    'get_y_values' => [
                        'product_title' => '',
                        'product_variant' => '',
                        'category' => '',
                    ],
                    'auto_add_y' => [
                        ['quantity' => '', 'sku' => ''],
                    ],
                    'buy_x_get_y_option' => 'amount_off',
                    'buy_x_get_y_amount' => '',
                    'order_option' => 'product_title',
                    'order_values' => [
                        'category' => '',
                        'product_title' => ['Super Bell', 'Super Bike'],
                        'product_variant' => '',
                        'product_collection' => '',
                        'product_tag' => '',
                    ],
                    'is_auto_add_y' => false,
                    'prerequisite_option' => '',
                    'prerequisite_subtotal' => '0.00',
                    'prerequisite_quantity' => '0',
                ],
                'expected' => [
                    'id' => '1',
                    'option' => 'percent_off',
                    'option_amount' => '25.00',
                    'exclude_shipping_rates_above' => '0',
                    'order_option' => 'product_title',
                    'order_values' => [
                        'category' => '',
                        'product_title' => ['Super Bell', 'Super Bike'],
                        'product_variant' => '',
                        'product_collection' => '',
                        'product_tag' => '',
                    ],
                    'is_auto_add_y' => false,
                    'prerequisite_option' => null,
                    'prerequisite_subtotal' => '0.00',
                    'prerequisite_quantity' => '0',
                    'is_buy_x_get_y' => false,
                    'prerequisite_values' => null,
                    'order_quantity' => '0',
                    'auto_add_sku_quantities' => null,
                ],
            ],
            'buy_x_get_y' => [
                'data' => [
                    'id' => '1',
                    'option' => 'buy_x_get_y',
                    'option_amount' => '25.00',
                    'exclude_shipping_rates_above' => '0',
                    'buy_x_value_type' => 'quantity',
                    'buy_x_option' => 'category',
                    'buy_x_quantity' => '2',
                    'buy_x_subtotal' => '9.99',
                    'buy_x_values' => [
                        'product_title' => '',
                        'product_variant' => '',
                        'category' => ['Accessories'],
                    ],
                    'get_y_option' => 'category',
                    'get_y_quantity' => '1',
                    'get_y_any_quantity' => '0',
                    'get_y_values' => [
                        'product_title' => '',
                        'product_variant' => '',
                        'category' => ['Bikes'],
                    ],
                    'auto_add_y' => [
                        ['quantity' => '', 'sku' => ''],
                    ],
                    'buy_x_get_y_option' => 'percent_off',
                    'buy_x_get_y_amount' => '25.00',
                    'order_option' => 'product_title',
                    'order_values' => [
                        'category' => '',
                        'product_title' => ['Super Bell', 'Super Bike'],
                        'product_variant' => '',
                        'product_collection' => '',
                        'product_tag' => '',
                    ],
                    'is_auto_add_y' => '0',
                    'prerequisite_option' => '',
                    'prerequisite_subtotal' => '0.00',
                    'prerequisite_quantity' => '0',
                ],
                'expected' => [
                    'id' => '1',
                    'option' => 'percent_off',
                    'option_amount' => '25.00',
                    'exclude_shipping_rates_above' => '0',
                    'order_option' => 'category',
                    'order_values' => [
                        'product_title' => '',
                        'product_variant' => '',
                        'category' => ['Bikes'],
                    ],
                    'is_auto_add_y' => '0',
                    'prerequisite_option' => 'category',
                    'prerequisite_subtotal' => '0.00',
                    'prerequisite_quantity' => '2',
                    'is_buy_x_get_y' => true,
                    'prerequisite_values' => [
                        'product_title' => '',
                        'product_variant' => '',
                        'category' => ['Accessories'],
                    ],
                    'auto_add_sku_quantities' => null,
                    'order_quantity' => '1',
                ],
            ],
            'buy_x_get_y_any' => [
                'data' => [
                    'id' => '1',
                    'option' => 'buy_x_get_y',
                    'option_amount' => '25.00',
                    'exclude_shipping_rates_above' => '0',
                    'buy_x_value_type' => 'quantity',
                    'buy_x_option' => 'category',
                    'buy_x_quantity' => '2',
                    'buy_x_subtotal' => '9.99',
                    'buy_x_values' => [
                        'product_title' => '',
                        'product_variant' => '',
                        'category' => ['Accessories'],
                    ],
                    'get_y_option' => 'category',
                    'get_y_quantity' => '',
                    'get_y_any_quantity' => '1',
                    'get_y_values' => [
                        'product_title' => '',
                        'product_variant' => '',
                        'category' => ['Bikes'],
                    ],
                    'auto_add_y' => [
                        ['quantity' => '', 'sku' => ''],
                    ],
                    'buy_x_get_y_option' => 'percent_off',
                    'buy_x_get_y_amount' => '25.00',
                    'order_option' => 'product_title',
                    'order_values' => [
                        'category' => '',
                        'product_title' => ['Super Bell', 'Super Bike'],
                        'product_variant' => '',
                        'product_collection' => '',
                        'product_tag' => '',
                    ],
                    'is_auto_add_y' => '0',
                    'prerequisite_option' => '',
                    'prerequisite_subtotal' => '0.00',
                    'prerequisite_quantity' => '0',
                ],
                'expected' => [
                    'id' => '1',
                    'option' => 'percent_off',
                    'option_amount' => '25.00',
                    'exclude_shipping_rates_above' => '0',
                    'order_option' => 'category',
                    'order_values' => [
                        'product_title' => '',
                        'product_variant' => '',
                        'category' => ['Bikes'],
                    ],
                    'is_auto_add_y' => '0',
                    'prerequisite_option' => 'category',
                    'prerequisite_subtotal' => '0.00',
                    'prerequisite_quantity' => '2',
                    'is_buy_x_get_y' => true,
                    'prerequisite_values' => [
                        'product_title' => '',
                        'product_variant' => '',
                        'category' => ['Accessories'],
                    ],
                    'auto_add_sku_quantities' => null,
                    'order_quantity' => '0',
                ],
            ],
            'buy_x_auto_add_y' => [
                'data' => [
                    'id' => '1',
                    'option' => 'buy_x_get_y',
                    'option_amount' => '25.00',
                    'exclude_shipping_rates_above' => '0',
                    'buy_x_value_type' => 'quantity',
                    'buy_x_option' => 'category',
                    'buy_x_quantity' => '2',
                    'buy_x_subtotal' => '9.99',
                    'buy_x_values' => [
                        'product_title' => '',
                        'product_variant' => '',
                        'category' => ['Accessories'],
                    ],
                    'get_y_option' => 'category',
                    'get_y_quantity' => '1',
                    'get_y_any_quantity' => '0',
                    'get_y_values' => [
                        'product_title' => '',
                        'product_variant' => '',
                        'category' => ['Bikes'],
                    ],
                    'auto_add_y' => [
                        ['quantity' => '3', 'sku' => 'BICYCLE-3'],
                        ['quantity' => '2', 'sku' => 'BICYCLE-2'],
                    ],
                    'buy_x_get_y_option' => 'percent_off',
                    'buy_x_get_y_amount' => '25.00',
                    'order_option' => 'product_title',
                    'order_values' => [
                        'category' => '',
                        'product_title' => ['Super Bell', 'Super Bike'],
                        'product_variant' => '',
                        'product_collection' => '',
                        'product_tag' => '',
                    ],
                    'is_auto_add_y' => '1',
                    'prerequisite_option' => '',
                    'prerequisite_subtotal' => '0.00',
                    'prerequisite_quantity' => '0',
                ],
                'expected' => [
                    'id' => '1',
                    'option' => 'percent_off',
                    'option_amount' => '25.00',
                    'exclude_shipping_rates_above' => '0',
                    'order_option' => 'product_variant',
                    'order_values' => [
                        'product_variant' => [
                            'BICYCLE-2',
                            'BICYCLE-3',
                        ],
                    ],
                    'is_auto_add_y' => '1',
                    'prerequisite_option' => 'category',
                    'prerequisite_subtotal' => '0.00',
                    'prerequisite_quantity' => '2',
                    'is_buy_x_get_y' => true,
                    'prerequisite_values' => [
                        'product_title' => '',
                        'product_variant' => '',
                        'category' => ['Accessories'],
                    ],
                    'auto_add_sku_quantities' => [
                        'BICYCLE-2' => 2,
                        'BICYCLE-3' => 3,
                    ],
                    'order_quantity' => 5,
                ],
            ],
        ];
    }

    public static function testCalculateLineItemDiscount(): array
    {
        return [
            'amountOff' => [
                'option' => 'amount_off',
                'option_amount' => 50.00,
                'line_price' => 500.00,
                'subtotal' => 500.00,
                'expected' => 50.00,
            ],
            'percentOff' => [
                'option' => 'percent_off',
                'option_amount' => 50.00,
                'line_price' => 500.00,
                'subtotal' => 500.00,
                'expected' => 250.00,
            ],
            'freeShipping' => [
                'option' => 'free_shipping',
                'option_amount' => 50.00,
                'line_price' => 500.00,
                'subtotal' => 500.00,
                // Should ignore all other arguments and return 0.00
                'expected' => 0.00,
            ],
            'free' => [
                'option' => 'free',
                'option_amount' => 50.00, // Should ignore option_amount
                'line_price' => 500.00,
                'subtotal' => 500.00,
                'expected' => 500.00,
            ],
            'percentOffSubtotalGreaterThanLine' => [
                'option' => 'percent_off',
                'option_amount' => 50.00,
                'line_price' => 500.00,
                'subtotal' => 1000.00,
                'expected' => 250.00,
            ],
            'amountOffSubtotalGreaterThanLine' => [
                'option' => 'amount_off',
                'option_amount' => 50.00,
                'line_price' => 500.00,
                'subtotal' => 1000.00,
                'expected' => 25.00,
            ],
            'amountOffZero' => [
                'option' => 'amount_off',
                'option_amount' => 0.00,
                'line_price' => 500.00,
                'subtotal' => 500.00,
                'expected' => 0.00,
            ],
            'percentOffZero' => [
                'option' => 'percent_off',
                'option_amount' => 0.00,
                'line_price' => 500.00,
                'subtotal' => 500.00,
                'expected' => 0.00,
            ],
            'amountOffNegative' => [
                'option' => 'amount_off',
                'option_amount' => -50.00,
                'line_price' => 500.00,
                'subtotal' => 500.00,
                'expected' => 0.00,
            ],
            'percentOffNegative' => [
                'option' => 'percent_off',
                'option_amount' => -50.00,
                'line_price' => 500.00,
                'subtotal' => 500.00,
                'expected' => 0.00,
            ],
            'linePriceZero' => [
                'option' => 'percent_off',
                'option_amount' => 50.00,
                'line_price' => 0.00,
                'subtotal' => 500.00,
                'expected' => 0.00,
            ],
            'subtotalZeroPercentOff' => [
                'option' => 'percent_off',
                'option_amount' => 50.00,
                'line_price' => 500.00,
                'subtotal' => 0.00,
                'expected' => 250.00,
            ],
            'subtotalZeroAmountOff' => [
                'option' => 'amount_off',
                'option_amount' => 50.00,
                'line_price' => 500.00,
                'subtotal' => 0.00,
                'expected' => 0.00,
            ],
            'amountOffGreaterThanSubtotal' => [
                'option' => 'amount_off',
                'option_amount' => 5000.00,
                'line_price' => 500.00,
                'subtotal' => 500.00,
                'expected' => 500.00,
            ],
            'percentOffGreaterThan100' => [
                'option' => 'percent_off',
                'option_amount' => 110.00,
                'line_price' => 500.00,
                'subtotal' => 500.00,
                'expected' => 500.00,
            ],
        ];
    }

    public function testCalculateShippingDiscount(): array
    {
        return [
            'free_shipping' => [
                'option' => 'free_shipping',
                'optionAmount' => 0.00,
                'shippingAmount' => 25.00,
                'expected' => 25.00,
            ],
            'free_shipping_excludes_shipping_above_option_amount' => [
                'option' => 'free_shipping',
                'optionAmount' => 24.99,
                'shippingAmount' => 25.00,
                'expected' => 0.00,
            ],
            'free_shipping_excludes_shipping_below_option_amount' => [
                'option' => 'free_shipping',
                'optionAmount' => 25.01,
                'shippingAmount' => 25.00,
                'expected' => 25.00,
            ],
            'fixes_negative_discount' => [
                'option' => 'free_shipping',
                'optionAmount' => 0.00,
                'shippingAmount' => -25.00,
                'expected' => 0.00,
            ],
        ];
    }

    public static function testCalculateB2bCartItemDiscountsProvider()
    {
        $items = static::defaultB2bCartItems();

        return [
            'is b2b discount' => [
                'discount' => array_merge(static::defaultDiscountInfo(), ['is_b2b_discount' => true]),
                'brandId' => 8,
                'items' => $items,
                'expected' => [
                    0 => array_merge($items[0], [
                        'discount' => [
                            'id' => '21',
                            'discount_amount' => '5.00',
                            'discount_quantity' => 2,
                            'retailers' => [
                                0 => '7',
                                1 => '10',
                            ],
                            'is_product_discount' => true,
                        ],
                    ]),
                    1 => array_merge($items[1], [
                        'discount' => [
                        ],
                    ]),
                ],
            ],
            'not b2b discount' => [
                'discount' => array_merge(static::defaultDiscountInfo(), ['is_b2b_discount' => false]),
                'brandId' => 8,
                'items' => $items,
                'expected' => Hash::insert($items, '{n}.discount', []),
            ],
        ];
    }

    public static function testProductListItemDiscountsProvider()
    {
        $items = static::defaultProductItems();

        return [
            'is b2b discount' => [
                'discount' => array_merge(static::defaultDiscountInfo(), ['is_b2b_discount' => true]),
                'brandId' => 8,
                'items' => $items,
                'expected' => [
                    0 => array_merge($items[0], [
                        'discount' => [
                            'id' => '21',
                            'discount_amount' => '2.50',
                            'discount_quantity' => 1,
                            'retailers' => [
                                0 => '7',
                                1 => '10',
                            ],
                            'is_product_discount' => true,
                        ],
                    ]),
                    1 => array_merge($items[1], [
                        'discount' => [],
                    ]),
                ],
            ],
            'not b2b discount' => [
                'discount' => array_merge(static::defaultDiscountInfo(), ['is_b2b_discount' => false]),
                'brandId' => 8,
                'items' => $items,
                'expected' => Hash::insert($items, '{n}.discount', []),
            ],
        ];
    }

    public static function testOrderProductDiscountsProvider()
    {
        $items = static::defaultOrderProductItems();

        return [
            'is b2b discount' => [
                'discount' => array_merge(static::defaultDiscountInfo(), ['is_b2b_discount' => true]),
                'brandId' => 8,
                'items' => $items,
                'expected' => [
                    0 => array_merge($items[0], [
                        'discount' => [
                            'id' => '21',
                            'discount_amount' => '5.00',
                            'discount_quantity' => 2,
                            'retailers' => [
                                0 => '7',
                                1 => '10',
                            ],
                            'is_product_discount' => true,
                        ],
                    ]),
                    1 => array_merge($items[1], [
                        'discount' => [],
                    ]),
                ],
            ],
            'not b2b discount' => [
                'discount' => array_merge(static::defaultDiscountInfo(), ['is_b2b_discount' => false]),
                'brandId' => 8,
                'items' => $items,
                'expected' => Hash::insert($items, '{n}.discount', []),
            ],
        ];
    }

    public static function testGetAvailableB2bDiscounts(): array
    {
        return [
            'default' => [
                'brandId' => 8,
                'retailerId' => 7,
                'expected' => [
                    [
                        'Discount' => [
                            'id' => '2',
                            'code' => 'B2BFORDAYS',
                            'description' => '25.00% off variant (BICYCLE-1)',
                            'b2b_discount_type' => 'booking',
                            'retailer_option' => 'tiers',
                            'retailer_values' => '1',
                        ],
                    ],
                ],
            ],
        ];
    }

    /**
     * Format a local time/date.
     *
     * @param string $format The format of the outputted date string.
     * @param int|null $timestamp [optional] Default value: time(). The optional timestamp parameter is an integer Unix timestamp
     * that defaults to the current local time if a timestamp is not given.
     * @return string|null Formatted date string. If the provided timestamp cannot be parsed,
     * NULL is returned and an E_WARNING level error is emitted.
     * @see \date
     */
    private static function date(string $format = 'Y-m-d H:i:s', ?int $timestamp = null): ?string
    {
        return AppGlobalMethods::instance()->date($format, $timestamp);
    }
}
