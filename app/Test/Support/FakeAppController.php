<?php

namespace ShipEarlyApp\Test\Support;

use App;
use CakeLog;
use Configuration;
use Controller;
use RequestHandlerComponent;

App::uses('Controller', 'Controller');
App::uses('CakeLog', 'Log');

/**
 * Class FakeAppController.
 *
 * Fake controller that is faster and cleaner to set up than AppController.
 * Extend it to contain models and components under test.
 *
 * @property RequestHandlerComponent $RequestHandler
 *
 * @property Configuration $Configuration
 */
class FakeAppController extends Controller
{
    protected $_mergeParent = __CLASS__;

    public $components = ['RequestHandler'];

    public $uses = ['Configuration'];

    public function beforeFilter()
    {
        parent::beforeFilter();
        $this->Configuration->bootstrap();
    }

    public function _webServiceLog($msg)
    {
        if (is_array($msg) || is_object($msg)) {
            $msg = json_encode($msg);
        }
        $msg = $this->_getLogBacktrace() . " - " . $msg;
        return CakeLog::debug($msg, 'webService');
    }

    public function _webhookServiceLog($msg)
    {
        if (is_array($msg) || is_object($msg)) {
            $msg = json_encode($msg);
        }
        $msg = $this->_getLogBacktrace() . " - " . $msg;
        return CakeLog::debug($msg, 'webhookService');
    }

    private function _getLogBacktrace()
    {
        $backtrace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 3);
        $functionSignature = '[Stack Trace Unavailable]';
        if (isset($backtrace[2])) {
            $caller = $backtrace[2];
            $functionSignature = $caller['class'] . $caller['type'] . $caller['function'];
        }
        $line = isset($backtrace[1]['line']) ? $backtrace[1]['line'] : "??";
        return "{$functionSignature}, line {$line}";
    }

}
