<?php

namespace ShipEarlyApp\Test\Support;

use App;
use PhpExcelComponent;

App::uses('PhpExcelComponent', 'Controller/Component');

class FakePhpExcelComponent extends PhpExcelComponent
{
    public $isWorksheetCreated = false;
    public $defaultFont = [];
    public $tableHeaders = [];
    public $tableRows = [];
    public $isTableFooterSet = false;
    public $renderedFile = [];
    public $importTableMap = [];

    public function createWorksheet()
    {
        $this->isWorksheetCreated = true;
        return $this;
    }

    public function setDefaultFont($name, $size)
    {
        $this->defaultFont = compact('name', 'size');
        return $this;
    }

    public function addTableHeader($data, $params = array())
    {
        $this->tableHeaders[] = compact('data', 'params');
        return $this;
    }

    public function addTableRow(array $data, $data_types = array())
    {
        $this->tableRows[] = compact('data', 'data_types');
        return $this;
    }

    public function addTableFooter()
    {
        $this->isTableFooterSet = true;
        return $this;
    }

    public function render($filename = 'export.xlsx', $writer = 'Xlsx')
    {
        $this->renderedFile = compact('filename', 'writer');
    }

    public function extractTableData(string $file, string $fileName = null): array
    {
        $tableMap = $this->importTableMap;

        // Compatability for tests that do not assign explicit row keys
        if (array_key_first($tableMap) === 0) {
            /** @see SpoutComponent::mapTableData() */
            // Key by spreadsheet row number starting from 1 and offset by header row
            $firstRow = 2;
            $finalRow = $firstRow + (count($tableMap) - 1);
            $tableMap = array_combine(range($firstRow, $finalRow), $tableMap);
        }

        return $tableMap;
    }
}
