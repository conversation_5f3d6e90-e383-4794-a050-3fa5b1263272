<?php

use JsonSchema\Constraints\Constraint;

class AppTestConstraintStringMatchesJsonSchema extends PHPUnit_Framework_Constraint_IsJson
{
    const JSON_OPTIONS = JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE;

    protected $validator;
    protected $schema;

    /**
     * @param array|object $schema
     */
    public function __construct($schema)
    {
        if (!is_array($schema) && !is_object($schema)) {
            throw PHPUnit_Util_InvalidArgumentHelper::factory(1, 'array or object');
        }
        parent::__construct();
        $this->validator = new \JsonSchema\Validator();
        $this->schema = $schema;
    }

    protected function matches($other)
    {
        if (!parent::matches($other)) {
            return false;
        }

        $value = json_decode($other);
        $this->validator->validate($value, $this->schema, Constraint::CHECK_MODE_COERCE_TYPES);

        return $this->validator->isValid();
    }

    protected function failureDescription($other)
    {
        if (!$this->validator->isValid()) {
            $decoded = json_decode_if_array($other);
            if ($decoded !== null) {
                $other = json_encode($decoded, static::JSON_OPTIONS);
            }

            return sprintf('%s is valid JSON matching schema', $this->exporter->shortenedExport($other));
        }

        return parent::failureDescription($other);
    }

    protected function additionalFailureDescription($other)
    {
        $errors = $this->validator->getErrors();
        if ($errors) {
            return json_encode(compact('errors'), JSON_PRETTY_PRINT | static::JSON_OPTIONS);
        }

        return parent::additionalFailureDescription($other);
    }
}
