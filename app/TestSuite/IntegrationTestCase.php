<?php
App::uses('ShimIntegrationTestCase', 'Shim.TestSuite');
App::uses('AppTestCaseTrait', 'TestSuite');

/**
 * Wrapper for ShimIntegrationTestCase.
 *
 * {@inheritDoc}
 *
 * @package app.TestSuite
 */
abstract class IntegrationTestCase extends ShimIntegrationTestCase
{
    use AppTestCaseTrait;

    // Preserve FK constraints in test db
    public $dropTables = false;

    public function __construct($name = null, array $data = [], $dataName = '')
    {
        // Fix ShimIntegrationTestCase trying to override a CakeTest constructor
        // that no longer exists resulting in PHPUnit annotations such as
        // @dataProvider not working.
        PHPUnit_Framework_TestCase::__construct($name, $data, $dataName);
    }

    protected function _sendRequest($url, $method, $data = [])
    {
        // Fix ShimIntegrationTestCase not resetting HTTP headers assigned to
        // $_ENV and $_SERVER if an exception is thrown.
        $envBackup = $_ENV;
        $serverBackup = $_SERVER;

        try {
            if (isset($url['subdomain'])) {
                /**
                 * Fix CakeRequest initializing with an empty URL when `$_SERVER['REQUEST_URI']` contains a subdomain.
                 *
                 * This is only an issue because `ControllerTestCase::_testAction()` assigns `$_SERVER['REQUEST_URI'] = $url;`
                 * which fails a check in `CakeRequest::_url()` that the domain matches `Configure::read('App.fullBaseUrl');`.
                 * Fixed by assigning the path to `$_SERVER['PATH_INFO']` which is read with higher priority.
                 *
                 * @see CakeRequest::_url()
                 * @see ControllerTestCase::_testAction()
                 */
                $_SERVER['PATH_INFO'] = Router::url(array_diff_key($url, array_flip(['subdomain'])));
            }

            return parent::_sendRequest($url, $method, $data);
        } finally {
            $_ENV = $envBackup;
            $_SERVER = $serverBackup;
        }
    }

    /**
     * Perform a OPTIONS request using the current request data.
     *
     * The response of the dispatched request will be stored as
     * a property. You can use various assert methods to check the
     * response.
     *
     * @param string|array $url The url to request.
     * @return void
     */
    public function options($url)
    {
        $this->_sendRequest($url, 'OPTIONS');
    }
}
