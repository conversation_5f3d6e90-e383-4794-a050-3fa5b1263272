<?php

/**
 * Represents an enum for B2bCart.order_type.
 */
class B2bCartType
{
    const REGULAR = 'regular';
    const RENTAL = 'rental';
    const EMPLOYEE = 'employee';
    const DEMO = 'demo';
    const BOOKING = 'booking';
    const SPECIAL = 'special';
    const WARRANTY = 'warranty';

    public static function getB2bCartTypes()
    {
        return [
            static::REGULAR,
            static::RENTAL,
            static::EMPLOYEE,
            static::DEMO,
            static::BOOKING,
            static::SPECIAL,
            static::WARRANTY,
        ];
    }

    /**
     * Get nice labels for b2b cart types or the label for a specific type
     * @param string|null $type The type for the label you want
     * @return string|string[] A string for the label of a cart type or an array of labels indexed by type ing $type is not passed.
     */
    public static function getLabels(string $type = null)
    {
        $labels = [
            static::REGULAR => __('Regular'),
            static::RENTAL =>  __('Rental'),
            static::EMPLOYEE => __('Employee'),
            static::DEMO => __('Demo'),
            static::BOOKING => __('Booking'),
            static::SPECIAL => __('Special'),
            static::WARRANTY => __('Warranty'),
        ];

        if ($type !== null) {
            return $labels[$type] ?? '';
        }

        return $labels;
    }

    public static function getOrderTypeLabel(string $type = null)
    {
        $labels = [
            static::REGULAR => __('Regular Order'),
            static::RENTAL => __('Rental Order'),
            static::EMPLOYEE => __('Employee Order'),
            static::DEMO => __('Demo Order'),
            static::BOOKING => __('Booking Order'),
            static::SPECIAL => __('Special Order'),
            static::WARRANTY => __('Warranty Order'),
        ];

        if ($type !== null) {
            return $labels[$type] ?? '';
        }

        return $labels;
    }
}
