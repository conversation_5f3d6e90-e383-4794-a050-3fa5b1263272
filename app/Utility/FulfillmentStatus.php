<?php

/**
 * Represents an enum for Order.fulfillment_status and DealerOrder.fulfillment_status.
 */
class FulfillmentStatus
{
    const UNFULFILLED = 'unfulfilled';
    const FULFILLED = 'fulfilled';
    const PARTIALLY_FULFILLED = 'partially_fulfilled';
    const CANCELLED = 'cancelled';

    /**
     * Returns the user-facing value representing a fulfillment_status column.
     *
     * @param string $fulfillmentStatus
     * @return string
     */
    public static function getLabel(string $fulfillmentStatus): string
    {
        $options = static::getSelectOptions();

        return $options[$fulfillmentStatus] ?? '';
    }

    /**
     * Returns a mapping of fulfillment_status values to labels that can be used as select input options.
     *
     * @return string[]
     */
    public static function getSelectOptions(): array
    {
        return [
            static::UNFULFILLED => __('Unfulfilled'),
            static::FULFILLED => __('Fulfilled'),
            static::PARTIALLY_FULFILLED => __('Partially Fulfilled'),
            static::CANCELLED => __('Cancelled'),
        ];
    }

    /**
     * @param int[] $itemQuantities Collection of 'quantity', 'fulfilled_quantity', 'refunded_quantity', and 'unfulfilled_quantity' fields.
     * @return string FulfillmentStatus
     */
    public static function deriveFulfillmentStatus(array $itemQuantities): string
    {
        $totalQuantity = (int)array_sum(array_column($itemQuantities, 'quantity'));
        $totalFulfilled = (int)array_sum(array_column($itemQuantities, 'fulfilled_quantity'));
        $totalRefunded = (int)array_sum(array_column($itemQuantities, 'refunded_quantity'));
        $totalUnfulfilled = (int)array_sum(array_column($itemQuantities, 'unfulfilled_quantity'));

        $status = static::UNFULFILLED;
        if ($totalFulfilled > 0) {
            $status = ($totalUnfulfilled === 0) ? static::FULFILLED : static::PARTIALLY_FULFILLED;
        } elseif ($totalRefunded === $totalQuantity) {
            $status = static::CANCELLED;
        }

        return $status;
    }
}
