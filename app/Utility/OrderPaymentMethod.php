<?php

/**
 * Represents an enum for Order.payment_method.
 */
class OrderPaymentMethod
{
    const STRIPE = 'stripe';
    const CREDIT = 'credit';
    const EXTERNAL = 'external';
    const ALL = [
        self::STRIPE,
        self::CREDIT,
        self::EXTERNAL,
    ];

    /**
     * Returns the user-facing value representing a payment_method column.
     *
     * @param string $paymentMethod
     * @return string
     */
    public static function getLabel(string $paymentMethod): string
    {
        $options = static::getSelectOptions();

        return (string)($options[$paymentMethod] ?: ucwords($paymentMethod));
    }

    /**
     * Returns the user-facing value representing a payment_method column.
     *
     * @param string $paymentMethod
     * @return string
     */
    public static function getEnableLabel(string $paymentMethod): string
    {
        $options = [
            static::CREDIT => __('Enable On Account'),
            static::STRIPE => __('Enable Credit Card'),
            static::EXTERNAL => __('Enable On File Payment'),
        ];

        return (string)($options[$paymentMethod] ?: 'Enable ' . ucwords($paymentMethod));
    }

    /**
     * Returns a mapping of payment_method value to label that can be used as select options.
     *
     * @return string[]
     */
    public static function getSelectOptions(): array
    {
        return [
            static::CREDIT => __('On Account'),
            static::STRIPE => __('Credit Card'),
            static::EXTERNAL => __('On File Payment'),
        ];
    }
}
