<?php

App::uses('StripePaymentType', 'Stripe.Enum');

/**
 * Represents an enum for Order.payment_method_subtype.
 */
class OrderPaymentMethodSubtype
{
    const CARD = 'card';
    const CARD_AFFIRM = 'card_affirm';
    const AFFIRM = 'affirm';
    const KLARNA = 'klarna';

    const ALL = [
        self::CARD,
        self::CARD_AFFIRM,
        self::AFFIRM,
        self::KLARNA,
    ];

    /**
     * Returns the user-facing value representing a payment_method_subtype column.
     *
     * @param string $paymentMethodSubtype
     * @return string
     */
    public static function getLabel(string $paymentMethodSubtype): string
    {
        $options = static::getSelectOptions();

        return (string)($options[$paymentMethodSubtype] ?: ucwords($paymentMethodSubtype));
    }

    /**
     * Returns a mapping of payment_method_subtype value to label that can be used as select options.
     *
     * @return string[]
     */
    public static function getSelectOptions(): array
    {
        return [
            static::CARD => __('Credit Card'),
            static::CARD_AFFIRM => __('Affirm'),
            static::AFFIRM => __('Affirm'),
            static::KLARNA => __('Klarna'),
        ];
    }

    public static function toStripePaymentType(string $paymentMethodSubtype): ?string
    {
        $toStripePaymentType = [
            static::CARD => StripePaymentType::CARD,
            static::CARD_AFFIRM => StripePaymentType::CARD,
            static::AFFIRM => StripePaymentType::AFFIRM,
            static::KLARNA => StripePaymentType::KLARNA,
        ];

        return $toStripePaymentType[$paymentMethodSubtype] ?? null;
    }
}
