<?php

/**
 * Contains collection of Product statuses and related helper functions
 */
class ProductStatus
{
    const ENABLED = 'Enabled';
    const DISABLED = 'Disabled';
    const ORDERED = 'Ordered';
    const DELETED = 'Deleted';
    const PREORDER = 'pre-order';
    const INCOMPLETE = 'Incomplete';
    const STATUSES = [
        self::ENABLED,
        self::DISABLED,
        self::ORDERED,
        self::DELETED,
        self::PREORDER,
        self::INCOMPLETE,
    ];

    public static function getAllOptions(): array
    {
        return [
            static::ENABLED => __('Active'),
            static::DISABLED => __('Deactivated'),
            static::ORDERED => __('Ordered'),
            static::DELETED => __('Discontinued'),
            static::PREORDER => __('Pre-order'),
            static::INCOMPLETE => __('Incomplete'),
        ];
    }

    public static function getValidOptions(): array
    {
        return array_intersect_key(static::getAllOptions(), array_flip([
            static::ENABLED,
            static::DISABLED,
        ]));
    }

    /**
     * @param string $productStatusValue
     * @return string
     */
    public static function getStatusName($productStatusValue)
    {
        return static::getAllOptions()[$productStatusValue] ?? $productStatusValue;
    }

    /**
     * @param string $productStatusName
     * @return string
     */
    public static function getStatusValue($productStatusName)
    {
        return array_flip(static::getAllOptions())[$productStatusName] ?? $productStatusName;
    }
}
