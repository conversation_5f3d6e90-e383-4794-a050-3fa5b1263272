<?php


class RetailerCreditStatus
{
    public const PENDING = 'pending';
    public const OVERDUE = 'overdue';
    public const PAID = 'paid';

    public static function getPaymentStatuses(){
        return [
            static::PENDING,
            static::OVERDUE,
            static::PAID
        ];
    }

    public static function getStatusLabels(string $status = null)
    {
        $labels = [
            static::PENDING => __('Pending'),
            static::OVERDUE => __('Overdue'),
            static::PAID => __('Paid'),
        ];

        if ($status !== null){
            return $labels[$status] ?? '';
        }

        return $labels;
    }
}
