<?php
App::uses('OrderType', 'Utility');

/**
 *
 * Contains function to handle shipping types and collection of possible shipping types
 * 
 */
class ShippingMethod
{    
    const SHIP_EARLY = 'shipearly_shipearly';
    const SHIP_FROM_STORE = 'shipfromstore_shipfromstore';
    const SELL_DIRECT = 'selldirect_selldirect';
    const SUB_TYPE_LOCAL_INSTALL = 'localinstall';
    const SUB_TYPE_LOCAL_DELIVERY = 'localdelivery';

    const SHIPPING_TYPE_TO_ORDER_TYPE = [
        self::SHIP_EARLY => OrderType::IN_STORE_PICKUP,
        self::SHIP_FROM_STORE => OrderType::SHIP_FROM_STORE,
        self::SELL_DIRECT => OrderType::SELL_DIRECT,
        self::SUB_TYPE_LOCAL_DELIVERY => OrderType::LOCAL_DELIVERY,
    ];

    /**
     * 
     * @param string $shippingMethod 
     * @param string $subType 
     * @return string 
     * @throws InvalidArgumentException 
     */
    public static function getTypeByShippingMethod($shippingMethod, $subType){
        if(!array_has_any(self::SHIPPING_TYPE_TO_ORDER_TYPE, function($val,$key) use ($shippingMethod, $subType){ return $key === $shippingMethod || $key === $subType; }))
        {
            throw new InvalidArgumentException("{$shippingMethod} is an invalid shipping method.");
        }
        return self::SHIPPING_TYPE_TO_ORDER_TYPE[$subType] ?? self::SHIPPING_TYPE_TO_ORDER_TYPE[$shippingMethod];
    }
}
