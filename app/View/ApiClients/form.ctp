<?php
/**
 * @var AppView $this
 * @var string $title
 */

/** @var array $breadcrumbs */
$breadcrumbs = [
	__('API Clients') => ['controller' => 'api_clients', 'action' => 'index'],
	(!empty($title) ? $title : __('New')) => $this->request->here,
];

$this->assign('title', implode(' / ', array_keys($breadcrumbs)));
?>
<?php echo $this->Form->create('ApiClient', [
	'id' => 'ApiClientForm',
	'inputDefaults' => array('div' => 'form-group', 'class' => 'form-control'),
]); ?>
<div class="row header-card">
	<div class="col-sm-9 col-12">
		<?php echo $this->Layout->breadcrumbHeader($breadcrumbs); ?>
	</div>
	<div class="col-md-3 col-12">
		<div class="header-card-controls">
			<?php echo $this->Html->link(__('Cancel'), ['controller' => 'api_clients', 'action' => 'index'], ['id' => 'cancel', 'class' => 'btn btn-default']); ?>
			<button type="submit" id="save" class="btn btn-primary"><?= __('Save'); ?></button>
		</div>
	</div>
</div>
<div class="row">
	<div class="offset-md-1 col-md-10 col-12">
		<div class="next-card">
			<h4><?= __('App Details'); ?></h4>
			<?php echo $this->Form->input('name', [
				'label' => __('Name')
			]); ?>
		</div>
	<?php if ($this->request->data('ApiClient.client_id')) { ?>
		<div class="next-card">
			<h4><?= __('API Keys'); ?></h4>
			<?php echo $this->Form->input('client_id', [
				'type' => 'text',
				'readonly' => true,
				'style' => 'cursor: text;',
				'label' => __('Client ID'),
			]); ?>
			<?php echo $this->Form->input('client_secret', [
				'type' => 'password',
				'readonly' => true,
				'style' => 'cursor: text;',
				'between' => '<div class="input-group">',
				'label' => __('Client Secret'),
				'after' => '<button type="button" id="ToggleClientSecret" class="btn btn-outline-secondary">' . __('Show') . '</button></div>',
			]); ?>
		</div>
	<?php } ?>
	</div>
</div>
<?php echo $this->Form->end(); ?>
<?php $this->start('script'); ?>
<script type="text/javascript">
$(function() {
	$('#ToggleClientSecret').on('click', function() {
		var $this = $(this);
		var $input = $this.siblings('input');
		if ($input.attr('type') === 'password') {
			$this.html('<?= __('Hide'); ?>');
			$input.attr('type', 'text');
		} else {
			$this.html('<?= __('Show'); ?>');
			$input.attr('type', 'password');
		}
	});
});
</script>
<?php $this->end(); ?>
