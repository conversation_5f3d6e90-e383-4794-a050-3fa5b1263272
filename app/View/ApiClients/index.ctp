<?php
/**
 * @var AppView $this
 * @var array $clients
 */

$this->assign('title', __('API Clients'));

$this->Paginator->options(['style' => 'color: #333333;']);
?>
<div class="row header-card align-items-center">
	<div class="col-auto me-auto">
		<?php echo $this->Layout->breadcrumbHeader([
			$this->fetch('title') => $this->request->here,
		]); ?>
	</div>
	<div class="col-auto">
		<?php echo $this->Html->link(__('Create a new client'), ['controller' => 'api_clients', 'action' => 'add'], ['class' => 'btn btn-primary']); ?>
	</div>

</div>
<div class="card">
	<div class="card-body">
		<div class="table-responsive">
			<table class="table table-hover table-condensed">
				<thead>
					<tr>
						<th><?php echo $this->Paginator->sort('name', __('Name')); ?></th>
						<th><?php echo $this->Paginator->sort('client_id', __('Client ID')); ?></th>
						<th><?php echo $this->Paginator->sort('created_at', __('Created')); ?></th>
						<th><!-- Controls --></th>
					</tr>
				</thead>
				<tbody id="B2bPriceBasedRateRows">
				<?php foreach ($clients as $client) { ?>
					<tr>
						<td><?php echo $this->Html->link($client['ApiClient']['name'], ['controller' => 'api_clients', 'action' => 'edit', 'client_id' => $client['ApiClient']['client_id']], ['class' => 'link']); ?></td>
						<td><?php echo $client['ApiClient']['client_id']; ?></td>
						<td><?php echo $this->Shipearly->formatTime($client['ApiClient']['created_at']); ?></td>
						<td><?php echo $this->Form->postLink(
								'<i class="fas fa-trash" aria-hidden="true"></i> ' . __('Delete'),
								['controller' => 'api_clients', 'action' => 'delete', $client['ApiClient']['client_id']],
								['method' => 'DELETE', 'class' => 'btn btn-default text-nowrap', 'escapeTitle' => false],
								__('Are you sure you want to delete %s? This will revoke all credentials and cannot be reversed.', $client['ApiClient']['name'])
							); ?></td>
					</tr>
				<?php } ?>
				</tbody>
			</table>
		</div>
	</div>
</div>
