<?php

App::uses('User', 'Model');

/**
 * @var AppView $this
 * @var array $shipearly_user
 * @var array $b2bCarts
 * @var array $retailerOptions
 * @var array $b2bCartTypeOptions
 * @var string $userAlias
 * @var string $title_for_layout
 * @var bool $can_edit
 */
?>
<?php
$formStart = $this->Form->create('B2bCart', [
    'id' => 'B2bCartIndexForm',
    'type' => 'GET',
    'inputDefaults' => ['label' => false, 'div' => false],
]);

$retailerFilterInput = $this->Form->input('retailerFilter', [
    'type' => 'select',
    'id' => 'retailerFilter',
    'class' => 'rows',
    'options' => $retailerOptions,
    'value' => $this->request->query('retailerFilter'),
    'label' => '',
    'empty' => __('All Retailers'),
]);

$b2bCartTypeFilterInput = $this->Form->input('b2bCartTypeFilter', [
    'type' => 'select',
    'id' => 'b2bCartTypeFilter',
    'class' => 'rows',
    'options' => $b2bCartTypeOptions,
    'value' => $this->request->query('b2bCartTypeFilter'),
    'label' => '',
    'empty' => __('All Order Types'),
]);

$formEnd = $this->Form->end();

$retailerColumnDisplay = in_array($shipearly_user['User']['user_type'], [User::TYPE_SALES_REP, User::TYPE_MANUFACTURER])
    ? ''
    : 'display: none;';
$brandColumnDisplay = in_array($shipearly_user['User']['user_type'], [User::TYPE_SALES_REP, User::TYPE_RETAILER])
    ? ''
    : 'display: none;';
?>
<?php echo $this->Html->css('view/draft_orders'); ?>
<div class="d-flex justify-content-between align-items-center">
    <div>
        <?php echo $this->element('content_header'); ?>
    </div>
    <?php if ($b2bCarts) { ?>
        <div>
            <button type="button" class="btn btn-primary js-select-cart">
                <i class="fas fa-plus"></i> <?= __('Create Order'); ?>
            </button>
        </div>
    <?php } ?>
</div>
<?php if ($b2bCarts) { ?>
    <div class="card">
        <div class="card-body">
            <?php echo $formStart; ?>
            <div class="btn-group">
                <?php if ($retailerOptions) { ?>
                    <?= $retailerFilterInput ?>
                <?php } ?>
            </div>
            <div class="btn-group">
                <?php if ($retailerOptions) { ?>
                    <?= $b2bCartTypeFilterInput ?>
                <?php } ?>
            </div>
            <?php echo $formEnd; ?>

            <div id="B2bCartIndex" class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th><!--View--></th>
                            <th style="<?php echo $brandColumnDisplay; ?>"><?= __('Brand'); ?></th>
                            <th style="<?php echo $retailerColumnDisplay; ?>"><?= __('Retailer'); ?></th>
                            <th><?= __('Location'); ?></th>
                            <th><?= __('Account ID'); ?></th>
                            <th><?= __('PO #'); ?></th>
                            <th><?= __('Type'); ?></th>
                            <th><?= __('Discount'); ?></th>
                            <th><?= __('Quantity'); ?></th>
                            <th style="text-align: right;"><?= __('Total'); ?></th>
                            <th><?= __('Last Modified'); ?></th>
                            <th><!--Delete--></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($b2bCarts as $b2bCart) {
                            echo $this->element('B2bCarts/b2b_cart_row', [
                                'b2bCart' => $b2bCart,
                                'can_edit' => $can_edit,
                                'userAlias' => $userAlias,
                                'brandColumnDisplay' => $brandColumnDisplay,
                                'retailerColumnDisplay' => $retailerColumnDisplay,
                            ]);
                        } ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
<?php } else { ?>
    <div id="B2bCartIndex" class="d-flex justify-content-center align-items-center">
        <div class="text-center">
            <h3 class="text-sm font-semibold"><?= __('No Orders'); ?></h3>
            <p class="mt-1 text-sm"><?= __('Get started by creating a new order.'); ?></p>
            <div>
                <button type="button" class="btn btn-primary js-select-cart">
                    <i class="fas fa-plus"></i> <?= __('Create Order'); ?>
                </button>
            </div>
        </div>
    </div>
<?php } ?>

<script type="text/javascript">
    $(function() {
        $('#retailerFilter, #b2bCartTypeFilter').on('change', function() {
            $('#B2bCartIndexForm').submit();
        })
    });
</script>
