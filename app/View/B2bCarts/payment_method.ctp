<?php
/**
 * @var AppView $this
 * @var array $b2bCart
 * @var string[] $address
 * @var string[] $paymentMethodOptions
 * @var string|null $stripeAccount
 * @var string|null $customerSessionClientSecret
 * @var string[] $creditTermOptions
 */

App::uses('OrderPaymentMethod', 'Utility');

$this->request->data = $b2bCart;
?>
<?= $this->Form->create('B2bCart', [
    'id' => 'B2bCartPaymentMethodForm',
    'url' => null,
    'default' => false,
    'class' => 'bootbox-form',
    'inputDefaults' => ['label' => false, 'div' => 'form-group', 'class' => 'form-control'],
]) ?>
    <div class="clearfix"><?= $this->Layout->contentHeader() ?></div>
<?php if ($paymentMethodOptions) { ?>
    <?= $this->Form->input('payment_method', [
        'type' => 'select',
        'id' => 'B2bCartPaymentMethod_',
        'options' => $paymentMethodOptions,
        'class' => 'form-select',
    ]) ?>
    <div id="B2bCartStripeBlock" class="form-group">
        <div id="payment-element"></div>
        <div id="payment-element-error" role="alert" class="help-block"></div>
    </div>
    <div id="B2bCartCreditTermBlock" class="form-group">
        <?= $this->Form->input('credit_term_id', [
            'type' => 'select',
            'id' => 'B2bCartCreditTerm_',
            'options' => $creditTermOptions,
            'label' => 'Select a credit term',
            'div' => false,
            'class' => 'form-select' . (count($creditTermOptions) <= 1 ? ' bg-none!' : ''),
        ]) ?>
    </div>
<?php } else { ?>
    <?= $this->Html->para('form-control-plaintext', "No payment methods eligible. Please contact {$b2bCart['User']['company_name']}."); ?>
<?php } ?>
    <?= $this->Form->submit(null, ['class' => 'btn btn-primary', 'div' => ['style' => 'text-align: right;']]) ?>
<?= $this->Form->end() ?>
<?php $this->start('script'); ?>
<script>
(function($) {
    $('#B2bCartPaymentMethod_').on('change', function() {
        $('#B2bCartStripeBlock').toggle(this.value === <?= json_encode(OrderPaymentMethod::STRIPE) ?>);
        $('#B2bCartCreditTermBlock').toggle(this.value === <?= json_encode(OrderPaymentMethod::CREDIT) ?>);
    }).trigger('change');

    const stripe = Stripe(<?= json_encode(STRIPE_PUBLISHABLE_KEY) ?>, {
        apiVersion: <?= json_encode(STRIPE_API_VERSION) ?>,
        stripeAccount: <?= $stripeAccount ? json_encode($stripeAccount) : 'undefined' ?>,
    });
    const elements = stripe.elements(<?= json_encode([
        'mode' => 'setup',
        'currency' => strtolower($b2bCart['B2bCart']['currency_code']) ?: null,
        'paymentMethodTypes' => ['card'],
        'customerSessionClientSecret' => $customerSessionClientSecret ?: null,
    ], JSON_PRETTY_PRINT) ?>);
    const paymentElement = elements.create('payment', <?= json_encode([
        'defaultValues' => [
            'billingDetails' => [
                'name' => trim($address['first_name'] . ' ' . $address['last_name']),
                'email' => $address['email_address'],
                'phone' => $address['telephone'],
                'address' => [
                    'line1' => $address['address1'],
                    'line2' => $address['address2'],
                    'city' => $address['city'],
                    'state' => $address['state_code'],
                    'country' => $address['country_code'],
                    'postal_code' => $address['zipcode'],
                ],
            ],
        ],
        'business' => ['name' => $b2bCart['User']['company_name']],
    ], JSON_PRETTY_PRINT) ?>);
    paymentElement.mount('#payment-element');

    const handleSubmit = async (form) => {
        if (!stripe) {
            return Promise.reject('Stripe.js hasn\'t yet loaded. Make sure to disable form submission until Stripe.js has loaded.');
        }

        setLoading(true);

        const { error: submitError } = await elements.submit();
        if (submitError) {
            return handleError(submitError);
        }

        const { client_secret: clientSecret } = await new Promise((resolve, reject) => $.post(
            '<?= $this->Url->build(['controller' => 'b2b_carts', 'action' => 'place_order', 'filter' => 'preorder', 'id' => $b2bCart['B2bCart']['id']]) ?>',
            $(form).serialize(),
            resolve,
            'json'
        ).fail(reject));

        const { setupIntent, error } = await stripe.confirmSetup({
            elements,
            clientSecret,
            confirmParams: {
                return_url: '<?= $this->Url->build(['controller' => 'b2b_carts', 'action' => 'place_order', 'id' => $b2bCart['B2bCart']['id']], true) ?>',
            },
            redirect: 'if_required',
        });

        if (error) {
            return handleError(error);
        }

        return setupIntent;
    };
    const handleError = (error) => {
        $('#payment-element-error').text(error.message);
        setLoading(false);

        return Promise.reject(error);
    };
    const setLoading = (isLoading) => {
        const $form = $('#B2bCartPaymentMethodForm');
        $form.find(':submit').prop('disabled', isLoading);
        $form.trigger('setLoading', isLoading);
    };

    window.handleStripeSubmit = handleSubmit;
})(jQuery);
</script>
<?php $this->end(); ?>
