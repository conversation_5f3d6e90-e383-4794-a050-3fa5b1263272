<?php

App::uses('B2bShippingRate', 'Model');

/**
 * @var AppView $this
 * @var string $type
 * @var string $rateId
 * @var string[] $productCategories
 * @var string[] $productTitles
 * @var string $currency_code
 * @var array $rate
 */

$title_for_layout = 'Edit Rate';
if ($type === B2bShippingRate::TYPE_PRICE) {
    $title_for_layout = (!empty($rate['B2bShippingRate']['key']) ? 'Edit Price Rate' : 'Add Price Rate');
} elseif ($type === B2bShippingRate::TYPE_UNIT) {
    $title_for_layout = (!empty($rate['B2bShippingRate']['key']) ? 'Edit Unit Rate' : 'Add Unit Rate');
} elseif ($type === B2bShippingRate::TYPE_WEIGHT) {
    $title_for_layout = (!empty($rate['B2bShippingRate']['key']) ? 'Edit Weight Rate' : 'Add Weight Rate');
}
$this->assign('title', $title_for_layout);

$minRangeInputOptions = [
    'type' => 'number',
    'min' => '0',
    'step' => '0.01',
    'placeholder' => '0.00',
    'div' => 'form-group col-sm-6',
    'label' => 'Minimum Value',
];
$maxRangeInputOptions = [
    'type' => 'number',
    'min' => '0',
    'step' => '0.01',
    'placeholder' => 'No limit',
    'required' => false,
    'div' => 'form-group col-sm-6',
    'label' => 'Maximum Value',
];
if ($type === B2bShippingRate::TYPE_PRICE) {
    $minRangeInputOptions = array_merge($minRangeInputOptions, [
        'label' => 'Minimum Order Price',
        'between' => "<div class=\"input-group\"><span class=\"input-group-text\">{$currency_code}</span>",
        'after' => '</div>',
    ]);
    $maxRangeInputOptions = array_merge($maxRangeInputOptions, [
        'label' => 'Maximum Order Price',
        'between' => "<div class=\"input-group\"><span class=\"input-group-text\">{$currency_code}</span>",
        'after' => '</div>',
    ]);
} elseif ($type === B2bShippingRate::TYPE_UNIT) {
    $minRangeInputOptions = array_merge($minRangeInputOptions, [
        'step' => '1',
        'placeholder' => '0',
        'label' => 'Minimum Order Quantity',
    ]);
    $maxRangeInputOptions = array_merge($maxRangeInputOptions, [
        'step' => '1',
        'label' => 'Maximum Order Quantity',
    ]);
} elseif ($type === B2bShippingRate::TYPE_WEIGHT) {
    $minRangeInputOptions = array_merge($minRangeInputOptions, [
        'label' => 'Minimum Order Weight',
        'between' => '<div class="input-group">',
        'after' => '<span class="input-group-text">lb</span></div>',
    ]);
    $maxRangeInputOptions = array_merge($maxRangeInputOptions, [
        'placeholder' => '25.00',
        'required' => true,
        'label' => 'Maximum Order Weight',
        'between' => '<div class="input-group">',
        'after' => '<span class="input-group-text">lb</span></div>',
    ]);
}

$this->request->data = (array)$rate;
if ($type === B2bShippingRate::TYPE_UNIT) {
    $this->request->data['B2bShippingRate']['min_range'] = (int)$this->request->data['B2bShippingRate']['min_range'];
    $this->request->data['B2bShippingRate']['max_range'] = (int)$this->request->data['B2bShippingRate']['max_range'];
}
if (is_numeric($this->request->data['B2bShippingRate']['price'])) {
    $this->request->data['B2bShippingRate']['free'] = ((float)$this->request->data['B2bShippingRate']['price'] === 0.00);
}
?>
<style type="text/css">
#B2bShippingRateForm .section > h5 {
	margin-top: 10px;
	margin-bottom: 10px;
}

#B2bShippingRateForm .btn-group.bootstrap-select {
	width: 100%;
}
#B2bShippingRateForm button.dropdown-toggle {
	width: 100% !important;
}
</style>
<?php echo $this->Form->create('B2bShippingRate', array(
	'id' => 'B2bShippingRateForm',
	'url' => ['controller' => 'b2b_shipping_zones', 'action' => 'ajax_b2b_shipping_rate_row', $type, $rateId],
	'default' => false,
	'class' => 'bootbox-form',
	'inputDefaults' => array(
		'required' => true,
		'class' => 'form-control',
		'div' => 'form-group',
		'label' => null,
	),
)); ?>
	<div class="clearfix">
		<?= $this->Layout->contentHeader($title_for_layout) ?>
	</div>
	<div class="section">
		<?php
		echo $this->Form->hidden('key');
		echo $this->Form->hidden('type');
		echo $this->Form->input('name', array(
			'placeholder' => 'Standard Shipping',
			'after' => $this->Html->tag('small', 'Customers see this at checkout', ['class' => 'form-text text-muted']),
		));
		?>
		</div>
	<div class="section">
		<div>
			<?php echo $this->Form->input('order_option', array(
				'type' => 'radio',
				'options' => array(
					'product_category' => 'Product categories',
					'product_title' => 'Product Titles',
				),
				'default' => !empty($rate['B2bShippingRate']['product_titles']) ? 'product_title' : 'product_category',
				'class' => 'form-check-input',
				'legend' => false,
				'before' => '<label>Select Product category or Product Title</label><br /><div class="form-check form-check-inline">',
				'separator' => '</div><div class="form-check form-check-inline">',
				'after' => '</div>',
			)); ?>
		</div>
		<div id="order_category_div" class="order-values-div row">
			<div>
				<?php echo $this->Form->input('product_categories', array(
					'id' => 'order_category',
					'type' => 'select',
					'options' => $productCategories,
					'class' => 'selectpicker',
					'data-none-selected-text' => 'All Products',
					'data-live-search' => 'true',
					'data-dropup-auto' => 'false',
					'required' => false,
					'multiple' => true,
					'label' => 'List of Categories',
				)); ?>
			</div>
		</div>
		<div id="order_product_title_div" class="order-values-div row">
			<div>
				<?php echo $this->Form->input('product_titles', array(
					'id' => 'order_titles',
					'type' => 'select',
					'options' => $productTitles,
					'class' => 'selectpicker',
					'data-none-selected-text' => 'All Products',
					'data-live-search' => 'true',
					'data-dropup-auto' => 'false',
					'required' => false,
					'multiple' => true,
					'label' =>'List of Product Titles',
				)); ?>
			</div>
		</div>
	</div>
	<div class="section">
		<h5>Range</h5>
		<?= $this->Html->div('row', implode('', [
			$this->Form->input('min_range', $minRangeInputOptions),
			$this->Form->input('max_range', $maxRangeInputOptions)
		])) ?>
	</div>
	<div class="section">
		<h5>Rate</h5>
		<?php
		echo $this->Form->input('free', array(
			'type' => 'checkbox',
			'id' => 'B2bShippingRateFree',
			'required' => false,
			'class' => false,
			'between' => ' ',
			'label' => 'Free Shipping Rate',
		));
		echo $this->Html->div('row',
			$this->Form->hidden('price', ['id' => 'B2bShippingRatePrice_', 'value' => '0.00']) .
			$this->Form->input('price', array(
				'type' => 'number',
				'id' => 'B2bShippingRatePrice',
				'min' => '0',
				'step' => '0.01',
				'placeholder' => '0.00',
				'div' => 'form-group col-sm-6',
				'label' => 'Rate Amount',
				'between' => "<div class=\"input-group\"><span class=\"input-group-text\">{$currency_code}</span>",
				'after' => ($type === B2bShippingRate::TYPE_UNIT ? '<span class="input-group-text">Per Unit</span>' : '') . '</div>',
			))
		);
		?>
	</div>
<?php echo $this->Form->end(['class' => 'btn btn-primary', 'div' => ['style' => 'text-align: right;']]); ?>
<script type="text/javascript">
$(function() {
	var handleFree = function() {
		$('#B2bShippingRatePrice').prop('disabled', $('#B2bShippingRateFree').is(':checked'));
	};
	$('#B2bShippingRateFree').change(handleFree);
	handleFree();
});
</script>
<script type="text/javascript">
$(function() {
	if (/Android|webOS|iPhone|iPad|iPod|BlackBerry/i.test(navigator.userAgent)) {
		$('#B2bShippingRateForm .selectpicker').selectpicker('mobile');
	} else {
		$('#B2bShippingRateForm .selectpicker').selectpicker();
	}

	// If inside a modal
	var $modal_content_head = $('.modal .modal-body #B2bShippingRateForm .con1-head1').hide();
	if ($modal_content_head.length) {
		$modal_content_head.closest('.modal').find('.modal-header > h4')
			.html($modal_content_head.find('.content-head').html());
	}
});
</script>
<script type="text/javascript">
$(document).ready(function() {
    $("input[name='data[B2bShippingRate][order_option]']").change(function() {
        var selectedOption = $(this).val();
        $("#order_category_div").hide();
        $("#order_product_title_div").hide();

        if (selectedOption === 'product_category') {
            $("#order_category_div").show();
        } else if (selectedOption === 'product_title') {
            $("#order_product_title_div").show();
        }
    });
	$("input[name='data[B2bShippingRate][order_option]']:checked").change();

	$('#B2bShippingRateForm').submit(function() {
		var selectedOption = $("input[name='data[B2bShippingRate][order_option]']:checked").val();
		if (selectedOption === 'product_category') {
            $("#order_product_title_div option:selected").prop('selected', false);
        } else if (selectedOption === 'product_title') {
            $("#order_category_div option:selected").prop('selected', false);
        }
	});
});
</script>

