<?php

/**
 * @var AppView $this
 * @var string $title_for_layout
 * @var array $shipearly_user
 * @var array $countryOptions
 */
?>
<?php

$formStart = $this->Form->create('bank_account_add', [
    'id' => 'bank_account_add_form',
    'inputDefaults' => [
        'div' => false,
        'label' => [
            'class' => 'col-4 control-label',
        ],
        'between' => '<div class="col-8">',
        'after' => '</div>',
    ],
]);

$country = $this->Form->input(
    'country',
    [
        'id' => 'country',
        'type' => 'select',
        'class' => 'add-account-input selectpicker form-control',
        'options' => $countryOptions,
        'empty' => __('Country'),

    ]
);

$formEnd = $this->Form->end();
?>


<?php
echo $formStart;
?>

<div class="container">
    <div id="country-select" class="form-horizontal">
        <div class="form-group">
            <?= $country ?>
        </div>
    </div>
    <div id="mandate-info" class="add-account-info hidden">
        <div id="mandate-text">
            <p><?= __("By clicking [accept], you authorize Shipearly to debit the bank account specified above for any amount owed for charges arising from your use of Shipearly's services and/or purchase of products from Shipearly, pursuant to Shipearly's website and terms, until this authorization is revoked. You may amend or cancel this authorization at any time by providing notice to Shipearly with 30 (thirty) days notice."); ?></p>
            <p><?= __("If you use Shipearly's services or purchase additional products periodically pursuant to Shipearly's terms, you authorize Shipearly to debit your bank account periodically. Payments that fall outside of the regular debits authorized above will only be debited after your authorization is obtained."); ?></p>
            <span id="accept-mandate" class="btn btn-primary">
                <h2><?= __('Accept'); ?></h2>
            </span>
        </div>
    </div>
    <div id="micro-deposit-info" class="add-account-info hidden">
        <p><?= __('Your account needs to be verified before you can begin using it to make payments'); ?></p>
        <p><?= __('You will receive and email at %s with instructions on how to verify your account.', '<strong id="billing-email"></strong>'); ?></p>
    </div>
    <div id="success-info" class="add-account-info hidden">
        <p><?= __('Your account was successfully added. It can now be used to make payments.'); ?></p>
    </div>
    <div id="error-info" class="add-account-info hidden">
        <p><?= __('There was an error adding your account. Please try again.'); ?></p>
    </div>
    <div id="stripe-loading" class="add-account-info hidden">
        <div>
            <span class="fas fa-circle-notch fa-spin fa-5x"></span>
        </div>
        <div>
            <span><?= __('Please Wait...'); ?></span>
        </div>
    </div>
</div>
<?php
echo $formEnd;
?>
