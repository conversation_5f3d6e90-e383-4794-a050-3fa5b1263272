<?php
/**
 * @var AppView $this
 * @var array $shipearly_user
 * @var string $breadcrumb
 * @var array $staff
 * @var string[] $roleOptions
 * @var string[] $branchOptions
 * @var array $storeTiming
 * @var bool $enableDelete
 */

$this->request->data = $staff;
$staffId = $this->request->data('StaffUser.id');

$breadcrumbHeader = $this->Layout->breadcrumbHeader([
	__('Staff') => ['controller' => 'brand_staff', 'action' => 'index'],
	h($breadcrumb),
]);

$formStart = $this->Form->create('BrandStaff', [
	'url' => !empty($staffId)
		? ['controller' => 'brand_staff', 'action' => 'edit', $staffId]
		: ['controller' => 'brand_staff', 'action' => 'add'],
	'inputDefaults' => [
		'class' => 'form-control',
		'label' => null,
		'div' => 'form-group col-sm-6',
	],
]);

$cancelButton = $this->Html->link('Cancel', ['controller' => 'brand_staff', 'action' => 'index'], ['id' => 'cancel', 'class' => 'btn btn-default']);
$submitButton = $this->Form->button('Save', ['type' => 'submit', 'id' => 'save', 'class' => 'btn btn-primary']);

$requiredStar = $this->Html->tag('span', '*', ['class' => 'required_stars']);
$detailsInputs = $this->Form->inputs([
	'Contactpersons.firstname' => [
		'required' => true,
		'label' => 'First Name',
		'between' => $requiredStar,
	],
	'Contactpersons.lastname' => [
		'required' => true,
		'label' => 'Last Name',
		'between' => $requiredStar,
	],
	'StaffUser.email_address' => [
		'required' => true,
		'label' => 'Email',
		'between' => $requiredStar,
	],
	'CompanyTelephone.value' => [
		'required' => true,
		'label' => 'Phone',
		'between' => $requiredStar,
	],
	'BrandStaff.role_id' => [
		'id' => 'BrandStaffRoleId',
		'type' => 'select',
		'options' => $roleOptions,
		'empty' => 'None',
		'label' => 'Role',
	],
	'StaffUser.language_code' => [
		'type' => 'select',
		'options' => $languageOptions,
		'class' => 'selectpicker form-control',
		'data-live-search' => 'true',
		'label' => __('Preferred Language'),
		'between' => $requiredStar,
	],
], null, ['fieldset' => false]);

$hasFullPermissionsInput = $this->Form->input('BrandStaff.has_full_permissions', [
	'id' => 'BrandStaffHasFullPermissions',
	'type' => 'checkbox',
	'class' => false,
	'div' => 'checkbox',
	'between' => ' ',
	'label' => 'This staff account will have full permissions',
]);

$navigationPermissionInputs = $this->BrandStaff->permissionsTable('BrandStaff', [
	Permissions::NAME_PRODUCTS => ['label' => 'Products'],
	Permissions::NAME_ORDERS => ['label' => 'Orders / Wholesale'],
	Permissions::NAME_B2B_ORDERS => ['label' => 'Create/Edit Purchase Orders'],
	Permissions::NAME_INVENTORY => ['label' => 'Inventory'],
	Permissions::NAME_RETAILERS => ['label' => 'Retailers'],
	Permissions::NAME_DISCOUNTS => ['label' => 'Discounts'],
	Permissions::NAME_RETAILER_CREDITS => ['label' => 'Retailer Credits'],
	Permissions::NAME_STOREFRONTS => ['label' => 'Storefronts'],
]);

$settingsPermissionInputs = $this->BrandStaff->permissionsTable('BrandStaff', [
	Permissions::NAME_API_SETTINGS => ['label' => 'API'],
	Permissions::NAME_INTEGRATION_SETTINGS => ['label' => 'Integrations'],
	Permissions::NAME_CHECKOUT_SETTINGS => ['label' => 'Checkout'],
	Permissions::NAME_SHIPMENT_SETTINGS => ['label' => 'Shipping'],
	Permissions::NAME_TAX_SETTINGS => ['label' => 'Taxes'],
	Permissions::NAME_NOTIFICATION_SETTINGS => ['label' => 'Notifications'],
	Permissions::NAME_STAFF_SETTINGS => ['label' => 'Staff'],
]);

$formEnd = $this->Form->end();

$deleteButton = '';
if (!empty($staffId)) {
	$deleteButton = $this->Form->postLink('Delete',
		['controller' => 'brand_staff', 'action' => 'delete', $staffId],
		['method' => 'delete', 'class' => 'btn btn-default'],
		__('Delete this staff account? This operation cannot be undone.')
	);
}
?>
<?= $formStart ?>
<div class="row header-card">
	<div class="col-sm-9 col-12">
		<?= $breadcrumbHeader ?>
	</div>
	<div class="col-sm-3 col-12">
		<div class="header-card-controls">
			<?= $cancelButton ?>
			<?= $submitButton ?>
		</div>
	</div>
</div>
<div class="row">
	<div class="offset-md-1 col-md-10 col-12">
		<div class="next-card">
			<div class="row">
				<div class="col-12">
					<h4>Details</h4>
					<div class="row">
						<?= $detailsInputs ?>
					</div>
				</div>
			</div>
		</div>
		<div class="next-card">
			<h4>Permissions</h4>
			<div class="row">
				<div class="col-12">
					<?= $hasFullPermissionsInput ?>
				</div>
			</div>
			<div class="row">
				<div class="col-12">
					<div id="PermissionOptions" class="row">
						<div class="col-6">
							<h5>General</h5>
							<?= $navigationPermissionInputs ?>
						</div>
						<div class="col-6">
							<h5>Settings</h5>
							<?= $settingsPermissionInputs ?>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
<?= $formEnd ?>
<div class="row">
	<div class="footer-card">
		<div class="col-12">
			<?= $deleteButton ?>
		</div>
	</div>
</div>
<?php $this->start('script'); ?>
<script type="text/javascript">
$(function() {
	$('#BrandStaffRoleId').combobox({
		bsVersion: '5',
		clearIfNoMatch: false,
		sorter: function(items) {
			var matches = Object.getPrototypeOf(this).sorter.call(this, items);

			var term = this.query;
			if (!term) {
				return matches;
			}

			var filterOptions = function(options, pattern, flags) {
				var matcher = new RegExp(pattern, flags);
				return $.grep(options, function(value) {
					return matcher.test(value);
				});
			};
			var escapedTerm = $.ui.autocomplete.escapeRegex(term);
			var exactMatches = filterOptions(matches, '^' + escapedTerm + '$', 'i');

			if (exactMatches.length === 0) {
				matches.unshift('<strong>Add</strong> ' + term);
				this.options.items += 1;
			}
			return matches;
		},
		highlighter: function(item) {
			return item;
		}
	}).each(function(index, element) {
		var _combobox = $(element).data('combobox');

		// Set the name of the generated text input
		_combobox.$element.prop('name', 'data[BrandStaff][role_name]');

		_combobox.updater = function (item) {
			if (item.indexOf('<strong>Add</strong> ') === 0) {
				return this.query;
			}
			return item;
		}
	});

	$('#BrandStaffHasFullPermissions').on('change', function() {
		$('#PermissionOptions :input').prop('disabled', this.checked);
	}).trigger('change');
});
</script>
<?php $this->end(); ?>
