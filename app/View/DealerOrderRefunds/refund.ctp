<?php

use ShipEarlyApp\Lib\Utility\UserSiteType;

/**
 * @var AppView $this
 * @var array $order
 * @var float $balanceBefore
 * @var float $balanceAfter
 */

$isCancelForm = $this->Orders->isCancellableDealerPayment($order);

$this->assign('title', $isCancelForm ? __('Cancel Order') : __('Refund') . ' ' . $order['Order']['Retailer']['company_name']);

if ($isCancelForm) {
    foreach ($order['DealerOrderProduct'] as $item) {
        $this->request->data['DealerOrderRefundProduct'][$item['id']]['quantity'] = $item['adjusted_quantity'];
    }
    $this->request->data['DealerOrderRefund']['shipping_portion'] = $order['DealerOrder']['adjusted_shipping'];
}

/** @var string[] $warehouseNames */
$warehouseNames = Hash::combine($order['DealerOrderProduct'], '{n}.Warehouse.id', '{n}.Warehouse.name');
?>
<style type="text/css">
#DealerOrderRefundForm tfoot input[type="text"] {
	text-align: right;
	width: 5em;
	margin-left: 5px;
}
#DealerOrderRefundForm tbody input[type="number"] {
	text-align: center;
	width: 3em;
}
#DealerOrderRefundForm input[readonly="readonly"] {
	background-color: transparent;
	border-color: transparent;
	box-shadow: none;
}
</style>

<div class="clearfix"><?= $this->Layout->contentHeader() ?></div>
<?php echo $this->Form->create('DealerOrderRefund', [
	'id' => 'DealerOrderRefundForm',
	'inputDefaults' => ['label' => false, 'div' => false],
]); ?>
	<table class="order-popup-products-table">
		<tbody>
		<?php foreach (Hash::combine($order['DealerOrderProduct'], '{n}.product_id', '{n}', '{n}.warehouse_id') as $warehouseId => $items) { ?>
			<?= $this->OrderProducts->orderPopupWarehouseTr($warehouseId, $warehouseNames[$warehouseId] ?? '') ?>

			<?php foreach ($items as $item) { ?>
				<tr class="main">
					<td class="product-thumbnail-container">
						<div class="badge badge-quantity">
							<?php echo $item['adjusted_quantity']; ?>
						</div>
						<span>
							<a href="<?php echo $this->Html->url(['controller' => 'products', 'action' => 'view', 'uuid' => $item['Product']['uuid']]); ?>" target="_blank">
								<img class="product-thumbnail" src="<?php echo $item['Product']['product_image']; ?>" />
							</a>
						</span>
					</td>
					<td>
						<a href="<?php echo $this->Html->url(['controller' => 'products', 'action' => 'view', 'uuid' => $item['Product']['uuid']]); ?>" target="_blank">
							<p class="no-of-retails" style="text-align: left;">
								<span><?php echo $item['Product']['product_title']; ?></span>
							</p>
						</a>
						(<?php echo $item['Product']['product_sku']; ?>)
						<br />
						UPC <?php echo $item['Product']['product_upc']; ?>
					</td>
					<td><?php echo $this->Currency->formatAmount($item['unit_price'], $order['Order']['currency_code']); ?></td>
					<td align="right" style="white-space: nowrap;">
						<span>x</span>
						<?php echo $this->Form->hidden("DealerOrderRefundProduct.{$item['id']}.dealer_order_product_id", ['value' => $item['id']]); ?>
						<?php echo $this->Form->input("DealerOrderRefundProduct.{$item['id']}.quantity", array(
							'type' => 'number',
							'default' => 0,
							'min' => 0,
							'max' => $item['adjusted_quantity'],
							'step' => 1,
							'class' => 'js-dealerorderproduct-quantity',
							'readonly' => $isCancelForm,
						)); ?>
					</td>
					<td align="right">
						<span class="js-dealerorderproduct-totalprice" data-unit-value="<?php echo $item['unit_price']; ?>" data-value="0.00">0.00</span>
						<span class="js-dealerorderproduct-totaldiscount" data-unit-value="<?php echo $item['unit_discount']; ?>" data-value="0.00" style="display: none;">0.00</span>
						<span class="js-dealerorderproduct-totaltax" data-unit-value="<?php echo $item['unit_tax']; ?>" data-value="0.00" style="display: none;">0.00</span>
					</td>
				</tr>
			<?php } ?>
		<?php } ?>
		</tbody>
		<tfoot>
			<tr>
				<td colspan="4" align="right"><?= __('Subtotal'); ?></td>
				<td align="right">
					<span class="js-refund-subtotal" data-value="0.00">0.00</span>
					<span class="js-refund-subtotal-tax" data-value="0.00" style="display: none;">0.00</span>
				</td>
			</tr>
			<tr<?php echo ($order['DealerOrder']['total_discount'] > 0) ? '' : ' style="display: none;"' ?>>
				<td colspan="4" align="right"><?= __('Discount'); ?></td>
				<td align="right">
					<span class="js-refund-discount" data-value="0.00">0.00</span>
				</td>
			</tr>
			<tr>
				<td colspan="4" align="right"><?= __('Shipping'); ?> <?= __('(%s Remaining)', '<span>' . format_number($order['DealerOrder']['adjusted_shipping']) . '</span>'); ?></td>
				<td align="right">
					<?php echo $this->Form->input('shipping_portion', [
						'id' => 'RefundShippingPortion',
						'type' => ($order['DealerOrder']['adjusted_shipping'] <= 0) ? 'hidden' : 'text',
						'default' => '0.00',
						'readonly' => $isCancelForm,
						'data-tax-rate' => $order['DealerOrder']['shipping_tax_rate'],
					]); ?>
				<?php if ($order['DealerOrder']['adjusted_shipping'] <= 0) { ?>
					<span>0.00</span>
				<?php } ?>
				</td>
			</tr>
			<tr>
				<td colspan="4" align="right"><?= __('Tax'); ?></td>
				<td align="right">
					<span class="js-refund-taxes" data-value="0.00" data-is-inclusive="<?= json_encode($order['Order']['tax_included']) ?>">0.00</span>
				</td>
			</tr>
			<tr>
				<td colspan="4" align="right"><?= __('Dealer Refund'); ?></td>
				<td align="right">
					<?php echo $this->Form->input('amount', array(
						'id' => 'RefundAmount',
						'type' => 'text',
						'default' => '0.00',
						'readonly' => $isCancelForm,
					)); ?>
				</td>
			</tr>
			<tr>
				<td colspan="4" align="right"><?= __('Balance Before Refund'); ?></td>
				<td align="right">
					<span class="js-refund-available" data-value="<?php echo $balanceBefore; ?>"><?php echo $balanceBefore; ?></span>
				</td>
			</tr>
			<tr>
				<td colspan="4" align="right"><?= __('Balance After Refund'); ?></td>
				<td align="right">
					<span class="js-refund-balance" data-value="<?php echo $balanceAfter; ?>"><?php echo $balanceAfter; ?></span>
				</td>
			</tr>
		</tfoot>
	</table>
	<?php echo $this->Form->input('reason', array(
		'type' => 'text',
		'class' => 'form-control',
		'autocomplete' => 'off',
		'label' => __('Reason for refund'),
		'div' => 'form-group',
	)); ?>
	<?php if ($order['DealerOrder']['source_id'] && in_array($order['Order']['User']['site_type'], [UserSiteType::SHOPIFY], true)) { ?>
		<?php echo $this->Form->input('place_ecommerce_refund', array(
			'type' => 'checkbox',
			'checked' => true,
			'label' => __('Place refund in %s', $order['Order']['User']['site_type']),
			'between' => ' ',
			'div' => 'checkbox',
		)); ?>
		<p>
			<?= __('Inventory for refunded items will not be adjusted automatically.<br />
			To adjust please do so manually outside ShipEarly.'); ?>
		</p>
	<?php } ?>
<?php echo $this->Form->end(['style' => 'display: none;', 'div' => false]); ?>

<script type="text/javascript">
$(function() {
	$('#DealerOrderRefundForm').on('submit', function(e) {
		e.preventDefault();
		var $form = $(this);
		var $formInputs = $form.find(':input');
		var $modalSubmitButton = $form.closest('.modal').find('.modal-footer > button.btn-primary');
		$.ajax({
			type: 'POST',
			url: $form.attr('action'),
			data: $form.serialize(),
			dataType: 'JSON',
			cache: false,
			beforeSend: function() {
				$formInputs.prop('disabled', true);
				$form.find('.errors').remove();
				$modalSubmitButton.text('Processing');
			},
			success: function(res) {
				if (res.status === 'ok') {
					window.location.reload(true);
				} else {
					setError(res.message);
				}
			},
			error: function(request, status, error) {
				setError('Error: ' + error);
			}
		});

		function setError(message) {
			$form.prepend('<div class="errors"><ul><li>' + message + '</li></ul></div>');
			$formInputs.prop('disabled', false);
			$modalSubmitButton.text('Process');
		}
	}).on('change', '.js-dealerorderproduct-quantity', function() {
		var $this = $(this);
		var quantity = parseInt($this.val()) || 0;
		$this.val(quantity);
		var $row = $this.closest('tr');

		var $totalPrice = $row.find('.js-dealerorderproduct-totalprice');
		var price = Number($totalPrice.data('unit-value'));
		var lineTotal = (price * quantity);
		setMoneyLabelValue($totalPrice, lineTotal);

		var $totalDiscount = $row.find('.js-dealerorderproduct-totaldiscount');
		var discount = Number($totalDiscount.data('unit-value'));
		var lineDiscount = (discount * quantity);
		setMoneyLabelValue($totalDiscount, lineDiscount);

		var $totalTax = $row.find('.js-dealerorderproduct-totaltax');
		var tax = Number($totalTax.data('unit-value'));
		var lineTax = (tax * quantity);
		setMoneyLabelValue($totalTax, lineTax);

		var subtotal = 0.0;
		$('.js-dealerorderproduct-totalprice').each(function() {
			subtotal += getMoneyLabelValue($(this));
		});

		var subtotalDiscount = 0.0;
		$('.js-dealerorderproduct-totaldiscount').each(function() {
			subtotalDiscount += getMoneyLabelValue($(this));
		});

		var subtotalTax = 0.0;
		$('.js-dealerorderproduct-totaltax').each(function() {
			subtotalTax += getMoneyLabelValue($(this));
		});

		setMoneyLabelValue($('.js-refund-subtotal'), subtotal);
		setMoneyLabelValue($('.js-refund-discount'), subtotalDiscount);
		setMoneyLabelValue($('.js-refund-subtotal-tax'), subtotalTax);

		$('#RefundShippingPortion').change();
	});

	$('#RefundShippingPortion').change(function() {
		var $subtotalLabel = $('.js-refund-subtotal');
		var $subtotalTaxLabel = $('.js-refund-subtotal-tax');
		var $discountLabel = $('.js-refund-discount');
		var $shippingInput = $(this);
		var $taxTotalLabel = $('.js-refund-taxes');
		var $refundAmountInput = $('#RefundAmount');

		var subtotal = getMoneyLabelValue($subtotalLabel);
		var discount = getMoneyLabelValue($discountLabel);
		var subtotalTax = getMoneyLabelValue($subtotalTaxLabel);
		var shipping = getMoneyInputValue($shippingInput);

		var shippingTaxRate = $shippingInput.data('tax-rate');
		var taxIncluded = $taxTotalLabel.data('is-inclusive');

		var shippingTax = shipping * shippingTaxRate;
		if (taxIncluded) {
			shippingTax /= (1 + shippingTaxRate);
		}
		var taxTotal = subtotalTax + shippingTax;

		setMoneyLabelValue($taxTotalLabel, taxTotal);

		var refundAmount = (subtotal - discount) + shipping;
		if (!taxIncluded) {
			refundAmount += taxTotal;
		}

		setMoneyInputValue($refundAmountInput, refundAmount);
	});

	$('#RefundAmount').change(function() {
		var refundAmount = getMoneyInputValue($(this));
		var refundAvailable = getMoneyLabelValue($('.js-refund-available'));
		var refundBalance = refundAvailable - refundAmount;
		setMoneyLabelValue($('.js-refund-balance'), refundBalance);
	});

	// Init input values
	$('.js-dealerorderproduct-quantity').trigger('change');

	function getMoneyInputValue($element) {
		var value = Number($element.val()) || 0;
		$element.val(value.toFixed(2));
		return value;
	}

	function setMoneyInputValue($element, value) {
		$element.val(value.toFixed(2));
		$element.change();
	}

	function getMoneyLabelValue($element) {
		return Number($element.data('value')) || 0;
	}

	function setMoneyLabelValue($element, value) {
		$element.data('value', value);
		return $element.text(value.toFixed(2));
	}
});
</script>
