<?php
/**
 * @var AppView $this
 * @var bool $userHasEditPermission
 * @var array $discounts
 * @var array $paging
 * @var int $count_discounts
 * @var string $currencycode
 * @var string $exportLink
 */
?>
<div id="discounts-list" class="table-responsive">
	<table id="discountsTable" class="table table-striped table-hover">
		<thead>
			<tr>
				<th class="col-4" data-sorter="false" onclick="shipearlySort('Discount.code')">Code</th>
				<th data-sorter="false" onclick="shipearlySort('Discount.is_b2b_discount')">B2B</th>
				<th data-sorter="false" onclick="shipearlySort('Discount.is_automatic')">Auto</th>
				<th data-sorter="false" onclick="shipearlySort('Discount.usage_count')">Used</th>
				<th data-sorter="false">Status</th>
				<th data-sorter="false" onclick="shipearlySort('Discount.start_date')">Date</th>
				<th data-sorter="false">Action</th>
			</tr>
		</thead>
		<tbody>
		<?php foreach ($discounts as $discount) { ?>
			<tr class="main">
				<td class="discount_td">
				<div class="discount_code">
					<?php echo $this->Html->link(
						$this->Html->tag('span', $discount['Discount']['code'], ['escape' => true]),
						['controller' => 'discounts', 'action' => 'manage_discounts', 'token' => $discount['Discount']['uuid']],
						['class' => 'discount-code', 'escapeTitle' => false]
					); ?>
					<?php echo $this->Discount->descriptionElement($discount['Discount']['description']); ?>
				</div>
				</td>
				<td><?php echo ($discount['Discount']['is_b2b_discount']) ? 'Yes' : 'No'; ?></td>
				<td><?php echo ($discount['Discount']['is_automatic']) ? 'Yes' : 'No'; ?></td>
				<td>
					<span class="<?php echo $this->Discount->hasAvailableUses($discount) ? '' : 'pricingError'; ?>"><?php echo $this->Discount->usageText($discount); ?></span>
				</td>
				<td>
					<span class="status_pill <?php echo $this->Discount->statusClass($discount); ?>"><?php echo $this->Discount->statusText($discount); ?></span>
				</td>
				<td class="text-nowrap"><?php echo $this->Discount->dateString($discount)?></td>
				<td><?php
					/** @var string $statusButtonText */
					$statusButtonText = (!$discount['Discount']['is_enabled']) ? 'Enable' : 'Disable';
					echo $this->Form->postButton($statusButtonText,
						['controller' => 'discounts', 'action' => 'setStatus', 'id' => $discount['Discount']['id'], 'option' => $statusButtonText],
						[
							'title' => (!$discount['Discount']['is_enabled']) ? 'Code will be enabled' : 'Code will be disabled',
							'class' => 'btn btn-default discount-tooltip',
							'disabled' => (!$userHasEditPermission),
						]
					);
				?></td>
			</tr>
		<?php } ?>
		<?php if (count($discounts) == 0) { ?>
			<tr><td colspan="10" style="text-align: center;">No Discounts</td></tr>
		<?php } ?>
		</tbody>
	</table>
</div>
<?php if ($count_discounts > 0) { ?>
<div class='export-box order_export_box'>
	<a href="<?php echo $exportLink; ?>">
		<button type="button" class='btn btn-primary reset-link'>Export</button>
	</a>
</div>
<?php } ?>
<div id="pagination"></div>

<style>
.discount_td {
    text-align: left!important;
}

td span.Currency {
    float: none;
}

.discount_code span {
    white-space: nowrap;
    display: block;
}

.tooltip {
    z-index: 0;
}

.main {
    cursor: pointer;
}

.discount_code {
    float: left;
    padding-left: 40px;
}

.discount_code span {
    float: left;
    color: #5E5E5E;
    font-size: 1.25em;
    font-weight: bold;
}

.discount_code div {
    float: left;
    clear: left;
}

.discount_code span:hover {
    text-decoration: none;
}
</style>
<script type="text/javascript">
(function($) {
	$(function() {
		$('.discount-tooltip').tooltipster();

		$('#js-discounts-list > div.export-box.order_export_box > a').on('click', function(e) {
			e.preventDefault();
			var exportele = $(e.target).parent();
			var exportlink = $(exportele).attr('href');
			window.open(exportlink, "_blank");
		});
	});

	$('#discountsTable').tablesorter({ widgets: ['zebra'] });

	$('#pagination').pagination({
		items: <?php echo $count_discounts; ?>,
		itemsOnPage: <?php echo $paging['rowsPerPage']; ?>,
		currentPage: <?php echo $paging['pageNum']; ?>,
		onPageClick: function(pageNumber, event) {
			$('#pageNumber').val(pageNumber);
			updateTable();
		},
		onInit: window.paginationInit,
	});
	
	initDiscountTooltips();
})(jQuery);
</script>
<script>
    $(document).ready(function() {
        $('#discountsTable > tbody').on('mousedown', 'tr.main', function(downEvent) {
            const href = $(this).find('.discount-code').attr('href');
            $(document).one('mouseup', function(e) {
                if (e.target !== downEvent.target) {
                    return;
                }
                if ((e.which === 1 && e.ctrlKey) || e.which === 2) {
                    window.open(href, '_blank');
                } else if (e.which === 1) {
                    window.location.href = href;
                }
            });
        });
    });
</script>
