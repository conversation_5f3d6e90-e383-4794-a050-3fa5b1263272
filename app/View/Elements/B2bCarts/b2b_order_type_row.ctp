<?php

/**
 * @var AppView $this
 * @var array $orderType
 * @var int $brandId
 * @var int $retailerId
 * @var int $locationId
 * @var string $currencyCode
 * 
 */

App::uses('B2bCartType', 'Utility');

$type = $orderType['type'];
$typeDisplay = B2bCartType::getLabels($type);
$discountId = $orderType['Discount']['id'] ?? null;
$description = $this->Discount->descriptionElement($orderType['description'], 2);
$discountCode = $orderType['Discount']['code'] ?? '';
$typeText = $typeDisplay . ($discountCode ? " ({$discountCode})" : '');

$icon = $this->B2bCarts->getCartTypeIcon($type)
?>
<tr class='js-order-type-row' data-type='<?= $type ?>' data-location-id='<?= $locationId ?>' data-brand-id='<?= $brandId ?>' data-discount-id='<?= $discountId ?>'>
    <td>
        <div class="container">

            <div class='row'>
                <div class='col-1 align-self-center'>
                    <span class="fa <?php echo $icon; ?> fa-2x"></span>
                </div>

                <div class='col-10 text-start'>
                    <span class='h4'><?= $typeText ?></span>
                    <?= $description ?>
                </div>
                <div class='col-1 align-self-center'>
                    <span class="fas fa-angle-right fa-2x"></span>
                </div>
            </div>
        </div>
    </td>
</tr>
