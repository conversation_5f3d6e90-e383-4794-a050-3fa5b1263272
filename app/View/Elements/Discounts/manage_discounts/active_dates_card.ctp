<?php
/**
 * @var AppView $this
 */
?>
<div class="next-card">
    <fieldset class="js-booking-show">
        <legend><?= __('Shipping Window') ?></legend>
        <div class="row">
            <div class="col-6">
                <?= $this->Form->input('shipping_window_start_date', [
                    'id' => 'shipping_window_start_date',
                    'type' => 'text',
                    'autocomplete' => 'off',
                    'data-date-format' => DATEPICKER_JS_FORMAT,
                    'label' => ['text' => __('Start Date'), 'class' => 'form-label'],
                ]) ?>
            </div>
            <div class="col-6">
                <?= $this->Form->input('shipping_window_end_date', [
                    'id' => 'shipping_window_end_date',
                    'type' => 'text',
                    'autocomplete' => 'off',
                    'data-date-format' => DATEPICKER_JS_FORMAT,
                    'label' => ['text' => __('End Date'), 'class' => 'form-label'],
                ]) ?>
            </div>
        </div>
    </fieldset>
    <fieldset>
        <legend><?= __('Active Dates') ?></legend>
        <div class="row">
            <div class="col-6">
                <?= $this->Form->input('start_date', [
                    'id' => 'start_date',
                    'type' => 'text',
                    'autocomplete' => 'off',
                    'data-date-format' => DATEPICKER_JS_FORMAT,
                    'label' => ['text' => __('Start Date'), 'class' => 'form-label'],
                ]) ?>
            </div>
            <div class="col-6">
                <?= $this->Form->input('end_date', [
                    'id' => 'end_date',
                    'type' => 'text',
                    'autocomplete' => 'off',
                    'data-date-format' => DATEPICKER_JS_FORMAT,
                    'label' => ['text' => __('End Date'), 'class' => 'form-label'],
                ]) ?>
            </div>
        </div>
    </fieldset>
</div>
