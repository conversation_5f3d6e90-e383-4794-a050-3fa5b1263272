<?php

App::uses('DiscountBuyXGetYOptions', 'Utility/Discount');
App::uses('DiscountBuyXGetYPrerequisiteOptions', 'Utility/Discount');
App::uses('DiscountBuyXValueTypes', 'Utility/Discount');
App::uses('DiscountOrderOptions', 'Utility/Discount');

/**
 * @var AppView $this
 * @var int|string $key
 * @var string[] $productCategories
 * @var string[] $productTitles
 * @var string[] $productVariants
 * @var string[] $productCollections
 * @var string[] $productTags
 */

$getYAnyQuantityTooltip = sprintf(
    '<i class="fas fa-info-circle" data-tooltipster="%s" title="%s"></i>',
    h(json_encode(['side' => 'right'])),
    __('If selected, the discount will be applied across all qualifying items in the cart')
);

$this->Form->inputDefaults([
    'class' => 'form-control',
    'label' => ['class' => 'form-label'],
    'div' => 'form-group',
    // Rely on jQuery Validate
    'required' => false,
]);
?>
<div class="discount-rule-card next-card" data-key="<?= $key ?>">
    <button type="button" name="remove_discount_rule" class="close btn-close" aria-label="<?= __('Remove Discount Rule') ?>"></button>
    <?= $this->Form->hidden("DiscountRule.{$key}.id") ?>
    <div class="js-buy_x_get_y-show">
        <fieldset>
            <legend><?= __('Customer Buys') ?></legend>
            <?= $this->Form->input("DiscountRule.{$key}.buy_x_value_type", [
                'type' => 'radio',
                'options' => DiscountBuyXValueTypes::optionList(),
                'default' => DiscountBuyXValueTypes::QUANTITY,
                'class' => 'form-check-input',
                'data-discount-option-type' => 'buy_x_value_type',
                'label' => ['class' => 'form-check-label'],
                'legend' => false,
                'before' => '<div class="form-check">',
                'separator' => '</div><div class="form-check">',
                'after' => '</div>',
            ]) ?>
        </fieldset>
        <fieldset>
            <legend><?= __('Any items from') ?></legend>
            <?= $this->Form->input("DiscountRule.{$key}.buy_x_option", [
                'type' => 'radio',
                'options' => DiscountBuyXGetYPrerequisiteOptions::optionList(),
                'default' => DiscountBuyXGetYPrerequisiteOptions::PRODUCT_TITLE,
                'class' => 'form-check-input',
                'data-discount-option-type' => 'buy_x',
                'label' => ['class' => 'form-check-label'],
                'legend' => false,
                'before' => '<div class="form-check form-check-inline">',
                'separator' => '</div><div class="form-check form-check-inline">',
                'after' => '</div>',
            ]) ?>
            <div class="row">
                <div class="col-6">
                    <div data-discount-option="buy_x_value_type" data-discount-option-value="quantity">
                        <?= $this->Form->input("DiscountRule.{$key}.buy_x_quantity", [
                            'type' => 'number',
                            'min' => 0,
                            'step' => 1,
                            'placeholder' => '0',
                            'class' => 'form-control',
                            'label' => ['text' => __('Quantity'), 'class' => 'form-label'],
                        ]) ?>
                    </div>
                    <div data-discount-option="buy_x_value_type" data-discount-option-value="subtotal">
                        <?= $this->Form->input("DiscountRule.{$key}.buy_x_subtotal", [
                            'type' => 'number',
                            'min' => 0,
                            'step' => 0.01,
                            'placeholder' => '0.00',
                            'class' => 'form-control decimal',
                            'label' => ['text' => __('Amount'), 'class' => 'form-label'],
                            'between' => '<div class="input-group">',
                            'after' => '<span class="input-group-text">$</span></div>',
                        ]) ?>
                    </div>
                </div>
                <div class="col-6">
                    <div data-discount-option="buy_x" data-discount-option-value="product_title">
                        <?= $this->Form->input("DiscountRule.{$key}.buy_x_values.product_title", [
                            'type' => 'select',
                            'options' => $productTitles,
                            'class' => 'selectpicker',
                            'data-live-search' => 'true',
                            'data-dropup-auto' => 'false',
                            'multiple' => true,
                            'label' => ['text' => __('List of Product Titles'), 'class' => 'form-label'],
                        ]) ?>
                    </div>
                    <div data-discount-option="buy_x" data-discount-option-value="product_variant">
                        <?= $this->Form->input("DiscountRule.{$key}.buy_x_values.product_variant", [
                            'type' => 'select',
                            'options' => $productVariants,
                            'class' => 'selectpicker',
                            'data-live-search' => 'true',
                            'data-dropup-auto' => 'false',
                            'multiple' => true,
                            'label' => ['text' => __('List of Product Variants'), 'class' => 'form-label'],
                        ]) ?>
                    </div>
                    <div data-discount-option="buy_x" data-discount-option-value="category">
                        <?= $this->Form->input("DiscountRule.{$key}.buy_x_values.category", [
                            'type' => 'select',
                            'options' => $productCategories,
                            'class' => 'selectpicker',
                            'data-live-search' => 'true',
                            'data-dropup-auto' => 'false',
                            'multiple' => true,
                            'label' => ['text' => __('List of Categories'), 'class' => 'form-label'],
                        ]) ?>
                    </div>
                </div>
            </div>
        </fieldset>
        <fieldset>
            <legend><?= __('Customer Gets') ?></legend>
            <div class="js-auto_add_y-hide">
                <?= $this->Form->input("DiscountRule.{$key}.get_y_option", [
                    'type' => 'radio',
                    'options' => DiscountBuyXGetYPrerequisiteOptions::optionList(),
                    'default' => DiscountBuyXGetYPrerequisiteOptions::PRODUCT_TITLE,
                    'class' => 'form-check-input',
                    'data-discount-option-type' => 'get_y',
                    'label' => ['class' => 'form-check-label'],
                    'legend' => false,
                    'before' => '<div class="form-check form-check-inline">',
                    'separator' => '</div><div class="form-check form-check-inline">',
                    'after' => '</div>',
                ]) ?>
                <?= sprintf(
                    '<div id="%s" class="form-text text-muted mt-0 mb-2">%s</div>',
                    $this->Form->domId("DiscountRule.{$key}.get_y_quantity_help"),
                    __('Customers must add the quantity of items specified below to their cart')
                ) ?>
                <div class="row">
                    <div class="col-6">
                        <?= $this->Form->input("DiscountRule.{$key}.get_y_quantity", [
                            'type' => 'number',
                            'min' => 0,
                            'step' => 1,
                            'placeholder' => '0',
                            'disabled' => (bool)$this->Form->value("DiscountRule.{$key}.get_y_any_quantity"),
                            'class' => 'form-control',
                            'aria-describedby' => $this->Form->domId("DiscountRule.{$key}.get_y_quantity_help"),
                            'label' => ['text' => __('Quantity'), 'class' => 'form-label'],
                            'between' => '<div class="input-group">',
                            'after' => sprintf(
                                '<div class="input-group-text">%s</div></div>',
                                $this->Form->input("DiscountRule.{$key}.get_y_any_quantity", [
                                    'type' => 'checkbox',
                                    'class' => 'form-check-input',
                                    'label' => ['text' => __('Any') . '&nbsp;' . $getYAnyQuantityTooltip, 'class' => 'form-check-label'],
                                    'div' => 'form-check',
                                ])
                            ),
                        ]) ?>
                    </div>
                    <div class="col-6">
                        <div data-discount-option="get_y" data-discount-option-value="product_title">
                            <?= $this->Form->input("DiscountRule.{$key}.get_y_values.product_title", [
                                'type' => 'select',
                                'options' => $productTitles,
                                'class' => 'selectpicker',
                                'data-live-search' => 'true',
                                'data-dropup-auto' => 'false',
                                'multiple' => true,
                                'label' => ['text' => __('List of Product Titles'), 'class' => 'form-label'],
                            ]) ?>
                        </div>
                        <div data-discount-option="get_y" data-discount-option-value="product_variant">
                            <?= $this->Form->input("DiscountRule.{$key}.get_y_values.product_variant", [
                                'type' => 'select',
                                'options' => $productVariants,
                                'class' => 'selectpicker',
                                'data-live-search' => 'true',
                                'data-dropup-auto' => 'false',
                                'multiple' => true,
                                'label' => ['text' => __('List of Product Variants'), 'class' => 'form-label'],
                            ]) ?>
                        </div>
                        <div data-discount-option="get_y" data-discount-option-value="category">
                            <?= $this->Form->input("DiscountRule.{$key}.get_y_values.category", [
                                'type' => 'select',
                                'options' => $productCategories,
                                'class' => 'selectpicker',
                                'data-live-search' => 'true',
                                'data-dropup-auto' => 'false',
                                'multiple' => true,
                                'label' => ['text' => __('List of Categories'), 'class' => 'form-label'],
                            ]) ?>
                        </div>
                    </div>
                </div>
            </div>
            <div class="js-auto_add_y-show">
                <?php foreach (array_keys((array)$this->request->data("DiscountRule.{$key}.auto_add_y")) ?: [0] as $autoAddYKey) { ?>
                    <?= $this->element('Discounts/manage_discounts/discount_rule_card/auto_add_y_row', [
                        'ruleKey' => $key,
                        'key' => $autoAddYKey,
                        'productVariants' => $productVariants,
                    ]) ?>
                <?php } ?>
                <div class="text-center mb-[15px]">
                    <button type="button" name="add_auto_add_y_row" class="btn btn-primary"><i class="fas fa-plus"></i>&nbsp;<?= __('Add Customer Gets Variant') ?></button>
                </div>
            </div>
        </fieldset>
        <fieldset>
            <legend><?= __('At a Discounted Value') ?></legend>
            <div class="row">
                <div class="col-6">
                    <?= $this->Form->input("DiscountRule.{$key}.buy_x_get_y_option", [
                        'type' => 'select',
                        'options' => DiscountBuyXGetYOptions::optionList(),
                        'class' => 'selectpicker',
                        'aria-describedby' => $this->Form->domId("DiscountRule.{$key}.buy_x_get_y_option_help"),
                        'label' => ['text' => __('Discount Type'), 'class' => 'form-label'],
                        'after' => sprintf(
                            '<div id="%s" class="form-text text-muted">%s</div>',
                            $this->Form->domId("DiscountRule.{$key}.buy_x_get_y_option_help"),
                            __('Discount will be applied proportionally across all eligible items')
                        ),
                    ]) ?>
                </div>
                <div class="col-6 js-buy_x_get_y_free-hide">
                    <?= $this->Form->input("DiscountRule.{$key}.buy_x_get_y_amount", [
                        'placeholder' => '0.00',
                        'class' => 'form-control decimal',
                        'label' => ['text' => __('Discount Value'), 'class' => 'form-label'],
                        'between' => '<div class="input-group" >',
                        'after' => '<span class="buy_x_get_y-sign input-group-text">$</span></div>',
                    ]) ?>
                </div>
            </div>
        </fieldset>
    </div>
    <div class="js-buy_x_get_y-hide">
        <fieldset>
            <div class="row">
                <div class="col-6">
                    <?= $this->Form->input("DiscountRule.{$key}.option_amount", [
                        'min' => 0,
                        'placeholder' => '0.00',
                        'class' => 'form-control decimal',
                        'label' => ['text' => __('Discount Value'), 'class' => 'form-label font-bold'],
                        'between' => '<div class="input-group mb-1">',
                        'after' => '<span class="discount_option-sign input-group-text">$</span></div>',
                    ]) ?>
                    <div class="js-free-shipping-show">
                        <?= $this->Form->input("DiscountRule.{$key}.exclude_shipping_rates_above", [
                            'type' => 'checkbox',
                            'class' => 'form-check-input',
                            'label' => ['text' => __('Exclude shipping rates over a certain amount'), 'class' => 'form-check-label'],
                            'div' => 'form-group form-check',
                        ]) ?>
                    </div>
                </div>
            </div>
        </fieldset>
        <fieldset>
            <legend><?= __('Applies To') ?></legend>
            <?= $this->Form->input("DiscountRule.{$key}.order_option", [
                'type' => 'radio',
                'options' => DiscountOrderOptions::optionList(),
                'default' => DiscountOrderOptions::ALL,
                'class' => 'form-check-input',
                'data-discount-option-type' => 'order',
                'label' => ['class' => 'form-check-label'],
                'legend' => false,
                'before' => '<div class="form-check form-check-inline">',
                'separator' => '</div><div class="form-check form-check-inline">',
                'after' => '</div>',
            ]) ?>
            <div class="row">
                <div class="col-6">
                    <div data-discount-option="order" data-discount-option-value="category">
                        <?= $this->Form->input("DiscountRule.{$key}.order_values.category", [
                            'type' => 'select',
                            'options' => $productCategories,
                            'class' => 'selectpicker',
                            'data-live-search' => 'true',
                            'data-dropup-auto' => 'false',
                            'multiple' => true,
                            'label' => ['text' => __('List of Categories'), 'class' => 'form-label'],
                        ]) ?>
                    </div>
                    <div data-discount-option="order" data-discount-option-value="product_title">
                        <?= $this->Form->input("DiscountRule.{$key}.order_values.product_title", [
                            'type' => 'select',
                            'options' => $productTitles,
                            'class' => 'selectpicker',
                            'data-live-search' => 'true',
                            'data-dropup-auto' => 'false',
                            'multiple' => true,
                            'label' => ['text' => __('List of Product Titles'), 'class' => 'form-label'],
                        ]) ?>
                    </div>
                    <div data-discount-option="order" data-discount-option-value="product_variant">
                        <?= $this->Form->input("DiscountRule.{$key}.order_values.product_variant", [
                            'type' => 'select',
                            'options' => $productVariants,
                            'class' => 'selectpicker',
                            'data-live-search' => 'true',
                            'data-dropup-auto' => 'false',
                            'multiple' => true,
                            'label' => ['text' => __('List of Product Variants'), 'class' => 'form-label'],
                        ]) ?>
                    </div>
                    <div data-discount-option="order" data-discount-option-value="product_collection">
                        <?= $this->Form->input("DiscountRule.{$key}.order_values.product_collection", [
                            'type' => 'select',
                            'options' => $productCollections,
                            'class' => 'selectpicker',
                            'data-live-search' => 'true',
                            'data-dropup-auto' => 'false',
                            'multiple' => true,
                            'label' => ['text' => __('List of Collections'), 'class' => 'form-label'],
                        ]) ?>
                    </div>
                    <div data-discount-option="order" data-discount-option-value="product_tag">
                        <?= $this->Form->input("DiscountRule.{$key}.order_values.product_tag", [
                            'type' => 'select',
                            'options' => $productTags,
                            'class' => 'selectpicker',
                            'data-live-search' => 'true',
                            'data-dropup-auto' => 'false',
                            'multiple' => true,
                            'label' => ['text' => __('List of Product Tags'), 'class' => 'form-label'],
                        ]) ?>
                    </div>
                </div>
            </div>
        </fieldset>
    </div>
</div>
