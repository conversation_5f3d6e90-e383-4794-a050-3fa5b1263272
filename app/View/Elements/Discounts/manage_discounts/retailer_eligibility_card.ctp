<?php

App::uses('DiscountRetailerOptions', 'Utility/Discount');

/**
 * @var AppView $this
 * @var bool $userHasEditPermission
 * @var string[] $tiersList
 * @var string[] $storesList
 * @var array $creditTerms
 * @var int[] $selectedCreditTermIds
 */

?>
<div class="next-card">
    <fieldset>
        <legend><?= __('Retailers') ?></legend>
        <?= $this->Form->input('retailer_option', [
            'type' => 'radio',
            'options' => DiscountRetailerOptions::optionList(),
            'default' => DiscountRetailerOptions::NONE,
            'class' => 'form-check-input',
            'data-discount-option-type' => 'retailer',
            'label' => ['class' => 'form-check-label'],
            'legend' => false,
            'before' => '<div class="form-check form-check-inline">',
            'separator' => '</div><div class="form-check form-check-inline">',
            'after' => '</div>',
        ]) ?>
        <div class="row">
            <div class="col-6">
                <div data-discount-option="retailer" data-discount-option-value="tiers">
                    <?= $this->Form->input('Discount.retailer_values.tiers', [
                        'id' => 'tierIds',
                        'type' => 'select',
                        'options' => $tiersList,
                        'class' => 'selectpicker',
                        'data-live-search' => 'true',
                        'data-dropup-auto' => 'false',
                        'multiple' => true,
                        'label' => ['text' => __('Pricing Tiers'), 'class' => 'form-label'],
                    ]) ?>
                </div>
                <div data-discount-option="retailer" data-discount-option-value="select_retailers">
                    <?= $this->Form->input('Discount.retailer_values.select_retailers', [
                        'id' => 'storeIds',
                        'type' => 'select',
                        'options' => $storesList,
                        'class' => 'selectpicker',
                        'data-live-search' => 'true',
                        'data-dropup-auto' => 'false',
                        'multiple' => true,
                        'label' => ['text' => __('List of Retailers'), 'class' => 'form-label'],
                    ]) ?>
                </div>
            </div>
        </div>
    </fieldset>
    <div class="js-b2b-show">
        <div class="row">
            <div class="col-6">
                <?= $this->Form->input('CreditTerm.id', [
                    'id' => 'credit_term_ids',
                    'type' => 'select',
                    'multiple' => true,
                    'data-placeholder' => __('Default Credit Terms'),
                    'options' => $creditTerms,
                    'class' => 'selectpicker',
                    'selected' => $selectedCreditTermIds,
                    'label' => ['text' => __('Credit Terms'), 'class' => 'form-label font-bold'],
                    'after' => ($userHasEditPermission)
                        ? $this->Html->link(
                            __('Add or edit credit terms') . '&nbsp;<i class="fas fa-external-link-alt"></i>',
                            ['controller' => 'creditTerms', 'action' => 'creditTermEdit'],
                            ['target' => '_blank', 'escapeTitle' => false]
                        )
                        : '',
                ]) ?>
            </div>
        </div>
    </div>
</div>
