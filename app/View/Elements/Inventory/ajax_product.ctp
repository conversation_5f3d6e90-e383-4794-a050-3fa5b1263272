<table class="table table-striped table-hover" id="prdct-tbl">
	<thead>
		<tr>
			<th width="10%" data-sorter="false" onclick="shipearlySort('ManufacturerRetailer.status')">Status</th>
			<th width="20%" data-sorter="false" onclick="shipearlySort('User.company_name')">Retailer</th>
			<th width="15%" data-sorter="false" onclick="shipearlySort('User.city')">City</th>
			<th width="15%" data-sorter="false" onclick="shipearlySort('Countries.country_name')">Country</th>
			<th width="10%" data-sorter="false" onclick="shipearlySort('OrderCount.no_of_order')"># of Orders</th>
			<th width="10%" data-sorter="false" onclick="shipearlySort('OrderCount.no_of_items')"># of items</th>
			<th width="10%" data-sorter="false" onclick="shipearlySort('OrderCount.total_value')">value</th>
			<th width="10%" data-sorter="false" onclick="shipearlySort('Store.inventoryCount')">Inventory</th>
		</tr>
	</thead>
	<tbody>
		<?php $n = 1; ?>
		<?php foreach ($all_products as $product) { ?>
			<tr>
				<td>
					<?php
						$bStatus = 'false'; 
						if($product['ManufacturerRetailer']['status'] == 1) { 
							$bStatus = 'true';
						}
					?>
					<div style="width: 120px;float: right; text-align: center;">
						<div class="toggles toggle-light" data-toggle-on="<?php echo $bStatus; ?>" data-id="<?php echo $product['ManufacturerRetailer']['retailer_id']; ?>" data-height="30">
						</div>
					</div>
				 </td>
				<td class='desc-align'>
					<a class="header-link" href="../contact/<?php echo $product['User']['id']; ?>">
						<?php echo $product['User']['company_name']; ?>
					</a>
				</td>
				<td><?php echo $product['User']['city']; ?></td>
				<td><?php echo $product['Countries']['country_name']; ?></td>
				<td><?php echo (empty($product['OrderCount']['no_of_order'])? 0 : $product['OrderCount']['no_of_order']); ?></td>
				<td><?php echo (empty($product['OrderCount']['no_of_items'])? 0 : $product['OrderCount']['no_of_items']); ?></td>
				<td><?php echo (empty($product['OrderCount']['no_of_items'])? $this->Currency->formatCurrency(0,$product['User']['currency_code']) : $this->Currency->formatCurrency($product['OrderCount']['total_value'], $product['User']['currency_code'])); ?></td>
				<td><?php echo (empty($product['Store']['inventoryCount'])? 0 : $product['Store']['inventoryCount']); ?></td>
			</tr>
			<?php $n++; ?>
		<?php } ?>
		<?php
		if(count($all_products) == 0) {
			echo "<tr><td colspan='8'><center>No Retailers</center></td></tr>";
		}
		?>
	</tbody>
<input type="hidden" name="no_of_rows" id="no_of_rows" value="<?php echo $n; ?>">
</table>
