<?php

/**
 * @var AppView $this
 * @var array $shipearly_user
 * @var array $order
 * @var array $creditTermsOptions
 * @var array $paymentOptions
 * @var bool $userHasEditPermission
 * @var string $orderNO
 * @var bool $show_secret_code
 */
?>
<?php

$userType = $shipearly_user['User']['user_type'];

?>

<div class="col-12 col-sm-6 order-popup-info">
    <div class="card-group h-100">
        <div class="card card-default">
            <div class="card-body">
                <strong class="card-title h6"><?= __('Order Details'); ?></strong>
                <?php if (
                    in_array($userType, [User::TYPE_MANUFACTURER, User::TYPE_BRAND_STAFF, User::TYPE_SALES_REP], true)
                    && $userHasEditPermission
                    && in_array($order['Order']['order_status'], [OrderStatus::PROCESSING, OrderStatus::PURCHASE_ORDER], true)
                ) { ?>
                    <div>
                        <strong>Purchase Order No:</strong>
                        <?php echo $this->Form->input('Order.purchase_order_number', array(
                            'type' => 'text',
                            'maxlength' => 50,
                            'id' => 'PurchaseOrderNo',
                            'value' => $order['Order']['purchase_order_number'],
                            'placeholder' => __('Purchase Order No:'),
                            'class' => 'order-header-input form-control',
                            'div' => false,
                            'label' => false,
                        )); ?>
                        <span class="ajax-loading" style="display: none;"><?= __('Saving') ?><span class="fas fa-circle-notch fa-spin"></span></span>
                        <span class="ajax-success" style="display: none;"><?= __('Saved') ?><span class="fas fa-check"></span></span>
                        <button type="button" id="SavePurchaseOrderNo" class="order-header-save btn btn-default hidden" value="<?php echo Router::url(['controller' => 'orders', 'action' => 'save_purchase_order_number', $order['Order']['id']]); ?>"></button>
                    </div>
                <?php } elseif (!empty($order['Order']['purchase_order_number'])) { ?>
                    <?= $this->Orders->orderHeaderInfo(__('Purchase Order No'), h($order['Order']['purchase_order_number'])); ?>
                <?php } ?>
                <?php if (!empty($orderNO)) { ?>
                    <?= $this->Orders->orderHeaderInfo(__('Ecommerce ID'), $orderNO) ?>
                <?php } ?>
                <?php if ($order['Order']['order_type'] !== Order::TYPE_WHOLESALE) { ?>
                    <?= $this->Orders->orderHeaderInfo(__('Brand'), $this->Shipearly->makeContactLink(['User' => $order['User']])) ?>
                    <?= $this->Orders->orderHeaderInfo(__('Store'), $this->Shipearly->makeContactLink(['User' => $order['Retailer']])) ?>
                <?php } ?>
                <?php if (!empty($order['CreatedByUser']['company_name'])) { ?>
                    <?= $this->Orders->orderHeaderInfo(__('Created By'), $this->Shipearly->makeContactLink(['User' => $order['CreatedByUser']])) ?>
                <?php } ?>
                <?php if (
                    $order['Order']['order_type'] === Order::TYPE_WHOLESALE &&
                    (!empty($order['Order']['requested_b2b_ship_date']) || ($userType === User::TYPE_MANUFACTURER && !$order['Order']['shipped_date']))
                ) { ?>
                    <div>
                        <strong><?= __('Requested Ship Date') ?>:</strong>
                        <span class="notranslate">
                            <?php if ($userType === User::TYPE_MANUFACTURER && !$order['Order']['shipped_date'] && $userHasEditPermission) { ?>
                                <?php
                                echo $this->Form->input('Order.requested_b2b_ship_date', [
                                    'type' => 'text',
                                    'id' => 'OrderRequestedB2bShipDate',
                                    'value' => $order['Order']['requested_b2b_ship_date'] ? format_datetime($order['Order']['requested_b2b_ship_date'], DATEPICKER_FORMAT) : null,
                                    'autocomplete' => 'off',
                                    'class' => 'order-header-input form-control',
                                    'data-date-format' => DATEPICKER_JS_FORMAT,
                                    'data-date-start-date' => date(DATEPICKER_FORMAT),
                                    'data-date-clear-btn' => true,
                                    'data-date-autoclose' => true,
                                    'label' => false,
                                    'div' => false,
                                ]);
                                ?>
                                <span class="ajax-loading" style="display: none;"><?= __('Saving') ?><span class="fas fa-circle-notch fa-spin"></span></span>
                                <span class="ajax-success" style="display: none;"><?= __('Saved') ?><span class="fas fa-check"></span></span>
                                <button type="button" id="SaveRequestedB2bShipDate" class="order-header-save btn btn-default hidden" value="<?php echo Router::url(['controller' => 'orders', 'action' => 'save_requested_b2b_ship_date', $order['Order']['id']]); ?>"></button>
                                <small class="help-block"></small>
                            <?php } else { ?>
                                <?php echo format_datetime($order['Order']['requested_b2b_ship_date'], DATE_FORMAT); ?>
                            <?php } ?>
                        </span>
                    </div>
                <?php } ?>
                <?php if ($userType === User::TYPE_MANUFACTURER && !Order::filterOrderType($order['Order']['order_type']) !== Order::TYPE_SELL_DIRECT) { ?>
                    <?php if ($userHasEditPermission) { ?>
                        <div>
                            <strong>Internal Order #:</strong>
                            <?php echo $this->Form->input('Order.external_invoice_id', array(
                                'type' => 'text',
                                'maxlength' => 50,
                                'id' => 'ExternalInvoiceId',
                                'value' => $order['Order']['external_invoice_id'],
                                'placeholder' => __('Internal Order #'),
                                'class' => 'order-header-input form-control',
                                'div' => false,
                                'label' => false,
                            )); ?>
                            <span class="ajax-loading" style="display: none;"><?= __('Saving') ?><span class="fas fa-circle-notch fa-spin"></span></span>
                            <span class="ajax-success" style="display: none;"><?= __('Saved') ?><span class="fas fa-check"></span></span>
                            <button type="button" id="SaveExternalInvoiceId" class="order-header-save btn btn-default hidden" value="<?php echo Router::url(['controller' => 'orders', 'action' => 'save_external_invoice_id', $order['Order']['id']]); ?>"></button>
                            <small class="help-block"></small>
                        </div>
                    <?php } elseif (!empty($order['Order']['external_invoice_id'])) { ?>
                        <?= $this->Orders->orderHeaderInfo(__('Internal Order #'), h($order['Order']['external_invoice_id'])); ?>
                    <?php } ?>
                <?php } ?>
                <?php if (!empty($order['Order']['secretcode']) && $show_secret_code) { ?>
                    <?= $this->Orders->orderHeaderInfo(__('Verification Code'), $order['Order']['secretcode']) ?>
                    <?php if ($this->Orders->canShowMarkAsDeliveredButton($shipearly_user['User'], $order, $userHasEditPermission)) { ?>
                        <div style="text-align:center;margin-top:15px;">
                            <?= $this->Form->postLink(__('Mark as Delivered'), ['controller' => 'orders', 'action' => 'mark_delivered', $order['Order']['id']], [
                                'class' => 'btn btn-primary btn-xs',
                                'method' => 'PUT',
                                'confirm' => __('Are you sure you wish to apply the verification code and mark the order as delivered?'),
                            ]) ?>
                        </div>
                    <?php } ?>
                <?php } ?>
            </div>
        </div>
        <div class="card card-default">
            <div class="card-body">
                <strong class="card-title h6"><?= __('Payment') ?></strong>
                <?php if (!empty($order['Order']['payment_method'])) { ?>
                    <?php if (
                        $userType === User::TYPE_MANUFACTURER
                        && $userHasEditPermission
                        && (
                            $order['Order']['order_status'] === OrderStatus::PURCHASE_ORDER
                            || (
                                $order['Order']['order_status'] === OrderStatus::PROCESSING
                                && $order['Order']['payment_method'] !== OrderPaymentMethod::STRIPE
                            )
                        )
                    ) { ?>
                        <div>
                            <?= $this->Form->control('Order.payment_method', [
                                'id' => 'PaymentOption',
                                'type' => 'select',
                                'options' => $paymentOptions,
                                'value' => $order['Order']['payment_method'],
                                'class' => 'order-header-input form-select notranslate',
                                'style' => 'max-width: 150px; display: inline-block;',
                                'label' => ['text' => __('Payment Method') . ': ', 'class' => 'inline font-bold'],
                            ]) ?>
                            <span class="ajax-loading" style="display: none;"><?= __('Saving') ?><span class="fas fa-circle-notch fa-spin"></span></span>
                            <span class="ajax-success" style="display: none;"><?= __('Saved') ?><span class="fas fa-check"></span></span>
                            <button type="button" id="SavePaymentOption" class="order-header-save btn btn-default hidden" value="<?= Router::url(['controller' => 'orders', 'action' => 'save_payment_option', $order['Order']['id']]) ?>"></button>
                            <small class="help-block"></small>
                        </div>
                    <?php } else { ?>
                        <?= $this->Orders->orderHeaderInfo(__('Payment Method'), $this->Orders->getPaymentMethod($order)) ?>
                    <?php } ?>
                <?php } ?>
                <?php if (!empty($order['Order']['risk_level'])) { ?>
                    <?= $this->Orders->orderHeaderInfo(__('Risk Level'), $this->Orders->getRiskLevelHtml((string)$order['Order']['risk_level'])) ?>
                <?php } ?>
                <?php if(!empty($order['Order']['fraud_check_cvc']) || !empty($order['Order']['fraud_check_address']) || !empty($order['Order']['fraud_check_postal_code'])){ ?>
                    <div><strong>Fraud Checks</strong></div>
                    <div class="ps-3">
                        <?php if (!empty($order['Order']['fraud_check_cvc'])) { ?>
                            <?= $this->Orders->orderHeaderInfo(__('CVC'), $this->Orders->getFraudCheckResultHtml((string)$order['Order']['fraud_check_cvc'])) ?>
                        <?php } ?>
                        <?php if (!empty($order['Order']['fraud_check_address'])) { ?>
                            <?= $this->Orders->orderHeaderInfo(__('Address'), $this->Orders->getFraudCheckResultHtml((string)$order['Order']['fraud_check_address'])) ?>
                        <?php } ?>
                        <?php if (!empty($order['Order']['fraud_check_postal_code'])) { ?>
                            <?= $this->Orders->orderHeaderInfo(__('Postal Code'), $this->Orders->getFraudCheckResultHtml((string)$order['Order']['fraud_check_postal_code'])) ?>
                        <?php } ?>
                    </div>
                <?php }?>
                <?php if (!empty($order['CreditTerm']['description'])) { ?>
                    <?php if ($userType === User::TYPE_MANUFACTURER && $userHasEditPermission && in_array($order['Order']['order_status'], [OrderStatus::PROCESSING, OrderStatus::PURCHASE_ORDER])) { ?>
                        <div>
                            <strong><?= __('Credit Term') ?>:</strong>
                            <span class="notranslate">
                                <?php
                                echo $this->Form->select('Order.CreditTerm.id', $creditTermsOptions, [
                                    'value' => $order['CreditTerm']['id'],
                                    'class' => 'order-header-input form-control',
                                    'id' => 'CreditTerm',
                                    'label' => false,
                                    'div' => false,
                                    'style' => 'max-width: 150px; display: inline-block;',
                                    'empty' => false,
                                ]);
                                ?>
                                <span class="ajax-loading" style="display: none;"><?= __('Saving') ?><span class="fas fa-circle-notch fa-spin"></span></span>
                                <span class="ajax-success" style="display: none;"><?= __('Saved') ?><span class="fas fa-check"></span></span>
                                <button type="button" id="SaveCreditTerm" class="order-header-save btn btn-default hidden" value="<?php echo Router::url(['controller' => 'orders', 'action' => 'save_credit_term', $order['Order']['id']]); ?>"></button>
                                <small class="help-block"></small>
                            </span>
                        </div>
                    <?php } else { ?>
                        <?= $this->Orders->orderHeaderInfo(__('Credit Term'), h($order['CreditTerm']['description'])) ?>
                    <?php } ?>
                <?php } ?>
            </div>
        </div>
    </div>
</div>
