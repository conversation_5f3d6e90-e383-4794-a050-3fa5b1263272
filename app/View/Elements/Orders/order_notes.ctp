<?php
/**
 * @var AppView $this
 * @var array $shipearly_user
 * @var array $order
 */
?>
<?php
$isEditable = (
	in_array($shipearly_user['User']['user_type'], [User::TYPE_MANUFACTURER, User::TYPE_BRAND_STAFF], true) &&
	$this->Permissions->userHasPermission($shipearly_user['User'], Permissions::NAME_ORDERS, Permissions::LEVEL_EDIT)
);

$order_id = (int)$order['Order']['id'];
?>
<div class="col-12 col-sm-6 order-popup-info">
    <div class="next-card h-100">
        <div class="d-flex align-items-center">
            <h3><?= __('Order Notes'); ?></h3>
            <?php if (isset($order) && isset($order['Order']['serial_number']) && !empty($order['Order']['serial_number'])): ?>
                <span style="margin-left:5px;"><?= sprintf('(%s: %s)', __('Serial No'), $order['Order']['serial_number']) ?></span>
            <?php endif; ?>
            <?php if (isset($order) && isset($order['Order']['total_labor_compensation']) && $order['Order']['total_labor_compensation'] > 0): ?>
                <span style="margin-left:5px;">
                    <?= sprintf('(%s: $%s', __('Labor Compensation'), number_format($order['Order']['total_labor_compensation'], 2)) ?>
                    <?php if (isset($order['Order']['labor_compensation_payment_method']) && !empty($order['Order']['labor_compensation_payment_method'])): ?>
                        <?= ' (' . $order['Order']['labor_compensation_payment_method'] . ')' ?>
                    <?php endif; ?>
                    )
                </span>
            <?php endif; ?>
        </div>
        <div class="row">
            <div class="col mt-3">
                <?= $this->Form->control('notes', [
                    'type' => 'textarea',
                    'class' => 'form-control',
                    'label' => ['class' => 'visually-hidden'],
                    'placeholder' => __('Enter order notes here...'),
                    'rows' => 3,
                    'value' => isset($order) ? $order['Order']['notes'] : '',
                    'error' => ['attributes' => ['class' => 'help-block']],
                    'id' => 'order-notes-textarea',
                    'readonly' => false,
                    'disabled' => !$isEditable,
                ]); ?>
                <input type="hidden" id="existing-notes" value="<?= isset($order) ? $order['Order']['notes'] : ''; ?>">
            </div>
        </div>
    </div>
</div>

<?php $this->start('script'); ?>
<script>
$('#order-notes-textarea').on('blur', function() {
    const newNotes = $(this).val();
    const orderId = <?= json_encode($order_id); ?>;

    const existingNotes = $('#existing-notes').val();
    if (newNotes !== existingNotes) {
        $.ajax({
            url: '<?= $this->Url->build(['controller' => 'Orders', 'action' => 'updateNotes']); ?>',
            type: 'POST',
            data: {
                notes: newNotes,
                order_id: orderId,
            },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    $('#existing-notes').val(newNotes);
                }
            },
        });
    }
});
</script>
<?php $this->end(); ?>
