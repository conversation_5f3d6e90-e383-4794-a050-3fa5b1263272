<?php

/**
 * @var AppView $this
 * @var array $shipearly_user
 * @var array $order
 * @var string $CustomerAddress
 * @var string $BillingAddress
 * @var string $RetailerAddress
 * @var string $BrandAddress
 */

/** @var bool $showFulfilledByAddress */
$showFulfilledByAddress = $order['Order']['is_commission_retailer'] || (in_array($shipearly_user['User']['user_type'], ['Manufacturer', 'SalesRep']) &&
    !in_array(Order::filterOrderType($order['Order']['order_type']), [Order::TYPE_WHOLESALE, Order::TYPE_SELL_DIRECT]));

?>
<div class="col-12 col-sm-6 order-popup-info">
    <div class="card-group h-100">
        <div class="card card-default text-center">
            <div class="card-body">
                <strong class="card-title h6"><?php echo ($order['Order']['order_type'] == Order::TYPE_WHOLESALE) ? __('Ship To') : __('Customer Shipping') ?></strong>
                <address><?php echo $CustomerAddress; ?></address>
            </div>
        </div>
        <div class="card card-default text-center">
            <div class="card-body">
                <strong class="card-title h6">
                    <?php
                    if (!$showFulfilledByAddress && !empty($BillingAddress)) {
                        echo __('Customer Billing');
                    } else {
                        echo (!$order['Order']['is_commission_retailer']) ? __('Fulfilled By') : __('Serviced By');
                    }
                    ?>
                </strong>
                <address>
                    <?php
                    if (!$showFulfilledByAddress) {
                        echo !empty($BillingAddress) ? $BillingAddress : $BrandAddress;
                    } else {
                        echo $RetailerAddress;
                    }
                    ?>
                </address>
            </div>
        </div>
    </div>
</div>
