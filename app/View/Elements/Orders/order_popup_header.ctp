<?php

/**
 * @var AppView $this
 * @var array $shipearly_user
 * @var array $order
 * @var string $invoiceLink
 * @var array $customTitles
 *
 * used in order_details_panel.ctp
 * @var bool $userHasEditPermission
 * @var string $orderNO
 * @var bool $show_secret_code
 *
 * Used in order_popup_address_table.ctp
 * @var string $CustomerAddress
 * @var string $BillingAddress
 * @var string $RetailerAddress
 * @var string $BrandAddress
 *
 * Used for Refund button/dropdown
 * @var float $orderRefund
 * @var float $refund_total
 * @var float $orderTotal
 * @var float $dealerTotal
 * @var float $dealerOrderRefund
 */
?>
<?php
$refund_total = $refund_total ?? 0;

$userType = $shipearly_user['User']['user_type'];

$onclick = '';

if ($order['Order']['order_status'] == OrderStatus::DEALER_ORDER) {
	$onclick = 'event.preventDefault(); alert("Pricing must be confirmed before the invoice can be printed.")';
	$invoiceLink = '';
}
$printInvoiceButton = $this->Html->link(__('Print Order'), $invoiceLink, [
	'class' => 'btn btn-secondary print-invoice',
	'target' => '_blank',
	'onClick' => $onclick,
]);

$orderType = OrderType::filterOrderType($order['Order']['order_type']);

$orderStatus = $order['Order']['order_status'];
$isRetailerNeedToConfirm = ($userType === User::TYPE_RETAILER &&
	$orderStatus === OrderStatus::NEED_TO_CONFIRM
);

/** @var bool $can_message_customer */
$can_message_customer = (
	$order['Order']['order_type'] !== Order::TYPE_WHOLESALE &&
	$orderStatus !== OrderStatus::NEED_TO_CONFIRM
);
?>
<div class="order-popup-header container container-fluid">
	<div class="row align-items-center">
		<div class="col" style="display: flex; align-items: center;">
			<h1 class="notranslate order-number">
				<span><?php echo $order['Order']['orderID']; ?></span>
				<span class="badge order-type">
					<?php
					if ($orderType === OrderType::LOCAL_DELIVERY) {
						echo $customTitles['local_delivery_title'];
					} elseif ($orderType === OrderType::IN_STORE_PICKUP) {
						echo $customTitles['instore_pickup_title'];
					} elseif ($orderType === OrderType::SHIP_FROM_STORE) {
						echo $customTitles['ship_from_store_title'];
					} elseif ($orderType === OrderType::SELL_DIRECT) {
						echo $customTitles['ship_to_door_title'];
					} else {
						echo OrderType::getFulfillType($order['Order']['order_type'], $order['Order']['subType']);
					}
					?>
				</span>
			</h1>
			<span style="margin-left:10px;">
				<?php
				$paymentStatus = (int)$order['Order']['payment_status'];
				if (in_array($paymentStatus, [(int)OrderPaymentStatus::PAID, (int)OrderPaymentStatus::VOIDED], true)) {
					echo '<span class="fulfillment-pill">' . OrderPaymentStatus::getLabel($paymentStatus) . '<span class="fulfillment-pill__circle-filled"></span></span>';
				} else {
					echo '<span class="fulfillment-pill-unfulfilled">' . __('Unpaid') . '<span class="fulfillment-pill__circle"></span></span>';
				}
				?>
			</span>
		</div>
		<div class="col-auto ms-auto d-none d-md-flex align-items-center gap-2">
			<?php if ($this->request->param('filter') == 'dealerorderTable') { ?>
				<?php if ($userHasEditPermission && $this->Orders->canShowDealerOrderRefundButton($order, $dealerOrderRefund, $dealerTotal)) { ?>
					<button type="button" class="btn btn-secondary refund-button js-dealerorder-refund" style="width: 115px;"><?= __('Refund'); ?></button>
				<?php } ?>
			<?php } else { ?>
				<?php if ($this->Orders->canShowVerificationCodeButtons($shipearly_user['User'], $order, $userHasEditPermission)) { ?>
					<?php if (!$order['Order']['is_commission_retailer']) { ?>
						<button type="button" class="btn btn-secondary refund-button js-order-refund" style="width: 135px;"><?= __('Refund'); ?></button>
					<?php } ?>
				<?php } ?>
				<?php if ($userHasEditPermission && ($this->Orders->canShowOrderRefundButton($order, $refund_total, $orderTotal) || $this->Orders->canShowDealerOrderRefundButton($order, $dealerOrderRefund, $dealerTotal))) { ?>
					<div class="dropdown">
						<button class="btn btn-secondary refund-button dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
							<?= __('Refund'); ?>
						</button>
						<ul class="dropdown-menu">
							<?php if ($this->Orders->canShowOrderRefundButton($order, $refund_total, $orderTotal)) { ?>
								<li>
									<a class="dropdown-item js-order-refund" href="#"><?= $this->Orders->isCancellablePayment($order)
										? __('Cancel Order')
										: __('Refund Customer')
									?></a>
								</li>
							<?php } ?>
							<?php if ($this->Orders->canShowDealerOrderRefundButton($order, $dealerOrderRefund, $dealerTotal)) { ?>
								<li>
									<a class="dropdown-item js-dealerorder-refund" href="#"><?= $this->Orders->isCancellableDealerPayment($order)
										? __('Cancel Order')
										: __('Refund Dealer')
									?></a>
								</li>
							<?php } ?>
						</ul>
					</div>
				<?php } ?>
			<?php } ?>
			<?= $printInvoiceButton ?>
			<?php if ($userHasEditPermission && $orderType === OrderType::WHOLESALE && in_array($userType, [User::TYPE_RETAILER, User::TYPE_MANUFACTURER, User::TYPE_SALES_REP], true)) { ?>
				<?= $this->Html->link(__('Re-order'), '#', [
					'class' => 'btn btn-secondary js-reorder-button',
					'data-order-id' => $order['Order']['id']
				]); ?>
			<?php } ?>
		</div>
		<div class="col-auto ms-auto d-md-none">
			<div class="dropdown">
				<button class="btn btn-outline-secondary" type="button" data-bs-toggle="dropdown" aria-expanded="false">
					&hellip;
				</button>
				<ul class="dropdown-menu dropdown-hellip">
					<?php if ($this->request->param('filter') == 'dealerorderTable') { ?>
						<?php if ($userHasEditPermission && $this->Orders->canShowDealerOrderRefundButton($order, $dealerOrderRefund, $dealerTotal)) { ?>
							<li><a class="dropdown-item js-dealerorder-refund" href="#"><?= __('Refund'); ?></a></li>
						<?php } ?>
					<?php } else { ?>
						<?php if ($this->Orders->canShowVerificationCodeButtons($shipearly_user['User'], $order, $userHasEditPermission) && !$order['Order']['is_commission_retailer']) { ?>
							<li><a class="dropdown-item js-order-refund" href="#"><?= __('Refund'); ?></a></li>
						<?php } ?>
						<?php if ($userHasEditPermission && ($this->Orders->canShowOrderRefundButton($order, $refund_total, $orderTotal) || $this->Orders->canShowDealerOrderRefundButton($order, $dealerOrderRefund, $dealerTotal))) { ?>
							<?php if ($this->Orders->canShowOrderRefundButton($order, $refund_total, $orderTotal)) { ?>
								<li>
									<a class="dropdown-item js-order-refund" href="#"><?= $this->Orders->isCancellablePayment($order)
										? __('Cancel Order')
										: __('Refund Customer') ?></a>
								</li>
							<?php } ?>
							<?php if ($this->Orders->canShowDealerOrderRefundButton($order, $dealerOrderRefund, $dealerTotal)) { ?>
								<li>
									<a class="dropdown-item js-dealerorder-refund" href="#"><?= $this->Orders->isCancellableDealerPayment($order)
										? __('Cancel Order')
										: __('Refund Dealer') ?></a>
								</li>
							<?php } ?>
						<?php } ?>
					<?php } ?>
					<li>
						<?= $this->Html->link(__('Print Order'), $invoiceLink, [
							'class' => 'dropdown-item print-invoice',
							'target' => '_blank',
							'onClick' => $onclick,
						]) ?>
					</li>
					<?php if ($userHasEditPermission && $orderType === OrderType::WHOLESALE && in_array($userType, [User::TYPE_RETAILER, User::TYPE_MANUFACTURER, User::TYPE_SALES_REP], true)) { ?>
						<li>
							<a class="dropdown-item js-reorder-button" href="#" data-order-id="<?= $order['Order']['id'] ?>"><?= __('Re-order'); ?></a>
						</li>
					<?php } ?>
				</ul>
			</div>
		</div>
	</div>
	<div class="row mt-2">
		<div class="col">
			<h4><small><?= $this->Shipearly->formatTime($order['Order']['created_at']); ?> <?= __('from'); ?> <?= $this->Shipearly->makeContactLink(['User' => $order['User']]); ?></small></h4>
		</div>
	</div>
</div>
<div class="order-popup-header">
	<div class="row flex-wrap">
		<?php if (!$isRetailerNeedToConfirm) {
			echo $this->element('Orders/order_popup_address_table', compact('order', 'CustomerAddress', 'BillingAddress', 'RetailerAddress', 'BrandAddress'));
		} ?>
		<?php echo $this->element('Orders/order_details_panel', compact('order', 'userHasEditPermission', 'orderNO', 'show_secret_code')); ?>
	</div>
</div>
<?php if ($can_message_customer) { ?>
	<div class="order-popup-header">
		<div class="row">
			<div class="col-12">
				<?php echo $this->Html->link(
					__('Message Customer'),
					['controller' => 'orders', 'action' => 'message_customer', $order['Order']['id']],
					['class' => 'ajax-message-customer btn btn-primary btn-block w-100']
				); ?>
			</div>
		</div>
	</div>
<?php } ?>
<?php $this->start('script'); ?>
<script type="text/javascript">
$(function() {

	function preventBlur(e){
		e.stopPropagation();
	}

	$('#OrderRequestedB2bShipDate').datepicker({ "language": moment.locale() }).on('hide', function() {
		$('#OrderRequestedB2bShipDate').parent().find('.order-header-save').click();
	}).on('blur', preventBlur)
	.on('changeDate', function(e){
		<?php
		// datepickers will blur before selecting the date causing issues with clickToEdit()
		// so we use preventBlur() too stop the clickToEdit() blur handler
		// and call it after the date has been selected.
		?>
		$(this).off('blur', preventBlur);
		$(this).blur()
		$(this).on('blur', preventBlur);
	});

	$('.order-header-input').clickToEdit();

	$('.order-header-input').on('input', function() {
		$(this).parent().find('.order-header-save').find('.fas').hide();
	})
	$('#ExternalInvoiceId').on('blur', function(e){
		$(this).parent().find('.order-header-save').click();
	});
	$('#PurchaseOrderNo').on('blur', function(e){
		$(this).parent().find('.order-header-save').click();
	});
	$('#CreditTerm').on('change', function(e){
		$(this).parent().find('.order-header-save').click();
	});
	$('#PaymentOption').on('change', function(e){
		$(this).parent().find('.order-header-save').click();
	});
	$('.order-header-save').click(function() {
		var $button = $(this);
		var $parent = $button.parent();
		var $inputs = $parent.find('.order-header-input, .order-header-save');
		var $error = $parent.find('.help-block');
		$.ajax({
			type: 'POST',
			url: $button.val(),
			data: $inputs.serialize(),
			dataType: 'json',
			beforeSend: function() {
				$parent.find('.ajax-loading').show();
				$parent.find('.ajax-success').hide();
				$inputs.prop('disabled', true);
			},
			success: function() {
				$parent.find('.ajax-success').show();
				$error.empty();
			},
			error: function(jqXHR) {
				if (jqXHR.status >= 500) {
					$error.text(jqXHR.statusText);
				} else if (jqXHR.responseJSON) {
					$error.text(jqXHR.responseJSON.message);
				}
			},
			complete: function() {
				$parent.find('.ajax-loading').hide();
				$inputs.prop('disabled', false);
			}
		});
	});

});
</script>
<script type="text/javascript">
$(document).ready(function() {
    $('.js-order-refund').on('click', function(e) {
        e.preventDefault();
        $.get("<?php echo Router::url(['controller' => 'order_refunds', 'action' => 'refund', 'order_id' => $order['Order']['id']]); ?>", function(result) {
            bootbox.dialog({
                title: "<?= __('Refund Payments') ?>",
                message: result,
                className: 'bootbox--form modal--view',
                buttons: {
                    Cancel: {
                        label: "<?= __('Cancel'); ?>",
                        callback: function() {
                            bootbox.hideAll();
                        }
                    },
                    Process: {
                        label: "<?= __('Process'); ?>",
                        callback: function() {
                            $('#refundForm input[type="submit"]').click();
                            return false;
                        }
                    }
                }
            });
        });
    });
    $('.js-dealerorder-refund').on('click', function(e) {
        e.preventDefault();
        $.get("<?= Router::url(['controller' => 'dealer_order_refunds', 'action' => 'refund', 'order_id' => $order['Order']['id']]) ?>", function(result) {
            bootbox.dialog({
                title: "<?= __('Refund') . ' ' . $order['Retailer']['company_name'] ?>",
                message: result,
                className: 'bootbox--form modal--view',
                buttons: {
                    Cancel: {
                        label: "<?= __('Cancel') ?>",
                        callback: function() {
                            bootbox.hideAll();
                        }
                    },
                    Process: {
                        label: "<?= __('Process') ?>",
                        callback: function() {
                            $(this).find('form').find('input[type="submit"], button[type="submit"]').click();
                            return false;
                        }
                    }
                }
            });
        });
    });
});
</script>
<script type="text/javascript">
$(document).ready(function() {
	$('.js-reorder-button').on('click', function(e) {
		e.preventDefault();

		var orderId = $(this).data('order-id');
		$.ajax({
			type: 'POST',
			url: '<?= $this->Url->build(['controller' => 'b2b_carts', 'action' => 'reorder']) ?>',
			data: {
				order_id: orderId,
			},
			success: function(response) {
				let json = response;
				if (json.success) {
					bootbox.alert('Items have been added to your cart.', function () {
						window.location.href = '<?= $this->Url->build(['controller' => 'b2b_carts', 'action' => 'view']) ?>/' + json.cart_id;
					});
				} else {
					bootbox.alert(json.message || 'There was an error reordering the items.');
				}
			},
			error: function() {
				bootbox.alert('There was an error trying to reorder the items.');
			}
		});
	});
});
</script>
<?php $this->end(); ?>
