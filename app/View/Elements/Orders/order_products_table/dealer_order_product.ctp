<?php

/**
 * @var AppView $this
 * @var int $warehouseId
 * @var int $pid
 * @var array $value From $dealer_qty_ordered['products'][$pid]
 * @var string $currency_code
 * @var bool $removable Optional. Change the quantity badge to a remove button.
 * @var bool $enable_price_edit Optional.
 * @var bool $enable_quantity_edit Optional.
 */
?>
<?php

$lineTotalPrice = $this->Price->priceDisplay(
	(float)$value['line_total'],
	(string)$currency_code,
	(array)($value['discount'] ?? [])
);

$warehouseId = (int)($value['warehouse_id'] ?? $warehouseId ?? 0);
$removable = !empty($removable);
$enable_price_edit = !empty($enable_price_edit);
$enable_quantity_edit = !empty($enable_quantity_edit);

// Needs to be reapplied for Ajax rendering
/** @var array $inputDefaults */
$inputDefaults = ['error' => ['attributes' => ['class' => 'help-block']], 'label' => false, 'div' => false];

$value['warehouse_id'] = $warehouseId;
?>
<tr>
	<td class="product-thumbnail-container">
		<?php if ($removable) { ?>
			<button type="button" class="badge badge-quantity js-remove-product" aria-hidden="true">×</button>
		<?php } else { ?>
			<div class="badge badge-quantity"><?php echo $value['quantity']; ?></div>
		<?php } ?>
		<span>
			<a href="<?php echo BASE_PATH."products/".$value['Product']['uuid']; ?>" target="_blank">
				<img class="product-thumbnail" src="<?php echo $value['Product']['product_image']; ?>" />
			</a>
		</span>
	</td>
	<td class="notranslate">
		<a href="<?php echo BASE_PATH."products/".$value['Product']['uuid']; ?>" target="_blank">
			<p class="no-of-retails" style="display: inline-block; text-align: left;">
				<span><?php echo $value['Product']['product_name']; ?></span>
			</p>
		</a>
		<span class="pill-product-variant"><?php echo $value['Product']['variant_options']; ?></span>
		<br />
		SKU: <?php echo $value['Product']['product_sku']; ?>
		<?php if(!empty($value['restock_date'])) { ?>
			<br />
			Estimated Ship Date: <?php echo format_datetime($value['restock_date'], DATE_FORMAT) ?>
		<?php } ?>
		<?= $this->Form->hidden("Order.inventory_transfer_id.{$warehouseId}.{$pid}", ['value' => $value['inventory_transfer_id'] ?? null]) ?>
		<?= $this->Form->hidden("Order.restock_date.{$warehouseId}.{$pid}", ['value' => $value['restock_date'] ?? null]) ?>
	</td>
	<td>
		<?php
		echo $this->Form->input("Order.dealer_quantity.{$warehouseId}.{$pid}", array(
			'type' => ($enable_quantity_edit) ? 'number' : 'hidden',
			'min' => 0,
			'value' => $value['quantity'],
			'class' => 'stock number-validate js-dealer-quantity',
		) + $inputDefaults);
		if (!$enable_quantity_edit) {
			echo $value['quantity'];
		}
		?>
	</td>
	<td>&times;</td>
	<td style="text-align: right;">
		<?php
		echo $this->Form->input("Order.dealer_price.{$warehouseId}.{$pid}", array(
			'type' => ($enable_price_edit) ? 'text' : 'hidden',
			'default' => format_number($value['dealer_price']),
			'data-warehouse-id' => $warehouseId,
			'data-product-id' => $pid,
			'data-product-price' => $value['Product']['product_price'],
			'class' => 'order-popup-textbox number-validate js-dealer-price',
		) + $inputDefaults);
		if (!$enable_price_edit) {
			echo $this->Currency->formatAmount($value['dealer_price'], $currency_code, true);
		}
		?>
	</td>
	<td>
		<?php echo $lineTotalPrice ?>
	</td>
</tr>
