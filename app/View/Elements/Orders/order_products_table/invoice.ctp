<?php
/**
 * @var AppView $this
 * @var array $order
 */
?>
<?php
App::uses('OrderType', 'Utility');
?>
<?php
$orderType = OrderType::filterOrderType($order['Order']['order_type']);

$orderedProducts = array_filter($order['OrderProduct'], function($item) {
    return $item['is_ordered'];
});

$refundedProducts = array_filter($order['OrderProduct'], function($item) {
    return $item['is_refunded'];
});

$orderProductMap = Hash::combine($order['OrderProduct'], '{n}.id', '{n}');
?>
<?php if ($orderedProducts) { ?>
<div class="next-card">
	<table class="order-popup-products-table">
		<thead>
			<tr>
				<th colspan="10">
					<span class="fulfillment-pill-unfulfilled" style="font-weight: normal;">
						<?= __('Unfulfilled'); ?>
						<span class="fulfillment-pill__circle"></span>
					</span>
				</th>
			</tr>
		</thead>
		<tbody>
		<?php foreach ($orderedProducts as $item) {
			echo $this->element('Orders/order_products_table/invoice_product', [
				'value' => array_merge($item, [
					'quantity' => $item['remaining_quantity'],
					'total_price' => $item['remaining_price'],
				]),
				'currency_code' => $order['Order']['currency_code'],
			]);
		} ?>
		</tbody>
	</table>
</div>
<?php } ?>
<?php foreach ($order['Fulfillment'] as $fulfillment) { ?>
<div class="next-card">
    <table class="order-popup-products-table">
        <caption><?= $this->Orders->getFulfillmentTableCaptionContent($fulfillment) ?></caption>
        <tbody><?php
        foreach ($fulfillment['FulfillmentProduct'] as $fulfillmentItem) {
            $item = $orderProductMap[$fulfillmentItem['order_product_id']];
            echo $this->element('Orders/order_products_table/invoice_product', [
                'value' => array_merge($item, [
                    'quantity' => $fulfillmentItem['quantity'],
                    'total_price' => ($item['unit_price'] * $fulfillmentItem['quantity']),
                ]),
                'currency_code' => $order['Order']['currency_code'],
            ]);
        }
        ?></tbody>
    </table>
</div>
<?php } ?>
<?php if ($refundedProducts) { ?>
<div class="next-card">
	<table class="order-popup-products-table">
		<thead>
			<tr>
				<th colspan="10">
					<span class="fulfillment-pill" style="font-weight: normal;">
						<?= __('Refunded'); ?>
						<span class="fulfillment-pill__circle-filled"></span>
					</span>
				</th>
			</tr>
		</thead>
		<tbody>
		<?php foreach ($refundedProducts as $item) {
			echo $this->element('Orders/order_products_table/invoice_product', [
				'value' => array_merge($item, [
					'quantity' => $item['refunded_quantity'],
					'total_price' => $item['refunded_price'],
				]),
				'currency_code' => $order['Order']['currency_code'],
			]);
		} ?>
		</tbody>
	</table>
</div>
<?php } ?>
