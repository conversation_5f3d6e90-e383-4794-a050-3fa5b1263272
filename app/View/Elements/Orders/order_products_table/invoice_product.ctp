<?php
/**
 * @var AppView $this
 * @var array $value From $order['OrderProduct']
 * @var string $currency_code
 */
?>
<?php
$lineTotalPrice = $this->Price->priceDisplay(
	(float)$value['total_price'],
	(string)$currency_code,
	(array)($value['discount'] ?? [])
);
?>
<tr class="main">
	<td class="product-thumbnail-container notranslate">
		<div class="badge badge-quantity"><?php echo $value['quantity']; ?></div>
		<span>
			<a href="<?php echo BASE_PATH . 'products/' . $value['Product']['uuid']; ?>" target="_blank">
				<img class="product-thumbnail" src="<?php echo $value['Product']['product_image'] ?: BASE_PATH . 'images/no_img.gif'; ?>" />
			</a>
		</span>
	</td>
	<td class="notranslate">
		<a href="<?php echo BASE_PATH . 'products/' . $value['Product']['uuid']; ?>" target="_blank">
			<p class="no-of-retails" style="text-align: left;">
				<span><?php echo $value['Product']['product_name']; ?></span>
			</p>
		</a>
		<span class="pill-product-variant"><?php echo $value['Product']['variant_options']; ?></span>
		<br />
		SKU: <?php echo $value['Product']['product_sku']; ?>
		<?php if(!empty($value['restock_date']) && !empty($value['is_ordered'])) { ?>
		<br />
		Estimated Ship Date: <?php echo format_datetime($value['restock_date'], DATE_FORMAT) ?>
		<?php } ?>    
	</td>
	<td style="text-align: right;">
		<?php echo $lineTotalPrice ?>
	</td>
</tr>
