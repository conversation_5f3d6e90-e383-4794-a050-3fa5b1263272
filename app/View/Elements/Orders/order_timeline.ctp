<?php

/**
 * @var AppView $this
 * @var int $order_id
 * @var array $order
 * @var TimelineEvent[] $timeline_logs
 */
?>
<div class="order-timeline">
	<div class="order-timeline-header">
		<h3><?= __('Timeline'); ?></h3>
	</div>
	<?php if (!empty($order['OrderWarrantyImage'])): ?>
		<div class="order-timeline-warranty-images">
			<h6><?= __('Warranty Images'); ?></h6>
			<div class="warranty-image-list mt-2 mb-2">
				<?php foreach ($order['OrderWarrantyImage'] as $image): ?>
					<a href="<?= h($image['warranty_image']); ?>" target="_blank" class="warranty-image-preview me-2">
						<img src="<?= h($image['warranty_image']); ?>" alt="Warranty Image" class="warranty-image" style="max-height: 50px;">
					</a>
				<?php endforeach; ?>
			</div>
		</div>
	<?php endif; ?>
	<div class="order-timeline-comment-box"><?php echo $this->element('OrderComments/form', compact('order_id')); ?></div>
	<ol class="order-timeline-feed list-unstyled">
		<?php foreach ($timeline_logs as $log) { ?>
			<li id="<?php echo $log->key; ?>" class="order-timeline-event" data-type="<?php echo $log->type; ?>">
				<?php if ($log->type === 'comment') { ?>
					<?php echo $this->element('OrderComments/view', ['orderComment' => $log->details]); ?>
				<?php } else { ?>
					<div class="row">
						<div class="col-sm-8 col-6">
							<?php if (!empty($log->details)) { ?>
								<span class="collapsed" data-bs-toggle="collapse" data-bs-target="#<?php echo $log->key; ?> .order-timeline-details">
									<span class="order-timeline-message"><?php echo $log->message; ?></span>
									<span class="fas fa-caret-right"></span>
									<span class="fas fa-caret-down"></span>
								</span>
							<?php } else { ?>
								<span class="order-timeline-message"><?php echo $log->message; ?></span>
							<?php } ?>
						</div>
						<div class="col-sm-4 col-6">
							<div class="order-timeline-timestamp"><?php echo $this->Shipearly->formatTime($log->timestamp); ?></div>
						</div>
					</div>
					<?php if (!empty($log->details)) { ?>
						<div class="order-timeline-details collapse">
							<?php if ($log->type === 'customer-message') { ?>
								<p><?php echo 'From: ' . h($log->details['OrderCustomerMessage']['from']); ?></p>
								<blockquote><?php echo nl2br($log->details['OrderCustomerMessage']['content']); ?></blockquote>
							<?php } elseif ($log->type === 'delivery') { ?>
								<figure>
									<figcaption><?= __('Verification Image'); ?></figcaption>
									<a href="<?php echo $log->details; ?>" class="verification-image-preview" target="_blank">
										<img src="<?php echo $log->details; ?>" alt="Verification Image Thumbnail" class="img-fluid" style="display: inline; max-height: 100px;" />
									</a>
								</figure>
							<?php } elseif (is_array($log->details)) { ?>
								<table class="order-timeline-description-list">
									<?php foreach ($log->details as $list) { ?>
										<tr>
											<th><?php echo $list['label']; ?></th>
											<td><?php echo $list['value']; ?></td>
										</tr>
									<?php } ?>
								</table>
							<?php } else { ?>
								<?php echo $log->details; ?>
							<?php } ?>
						</div>
					<?php } ?>
				<?php } ?>
			</li>
		<?php } ?>
	</ol>
</div>
<?php $this->start('script'); ?>
<script type="text/javascript">
	$(function() {
		$('.order-timeline-comment-box').on('submit', 'form', function(e) {
			e.preventDefault();
			var $form = $(this);
			var $submit = $form.find('[type="submit"]');
			var $input = $form.find(':input');

			$submit.prop('disabled', true);
			$input.prop('readonly', true);
			$.post($form.attr('action'), $form.serialize(), function(data) {
				var id = data['OrderComment']['id'];
				var $newComment = $('<li id="comment_' + id + '" class="order-timeline-event collapse" data-type="comment"></li>');
				$newComment.prependTo('.order-timeline-feed');
				$newComment.load("<?php echo rtrim(Router::url(['controller' => 'order_comments', 'action' => 'view', '[method]' => 'GET'] + compact('order_id') + ['id' => 0]), '0'); ?>" + id, function() {
					$newComment.collapse('show');
				});
				$form.closest('.order-timeline-comment-box').load("<?php echo Router::url(['controller' => 'order_comments', 'action' => 'add'] + compact('order_id')); ?>");
			}, 'json').fail(function() {
				$submit.prop('disabled', false);
				$input.prop('readonly', false);
				displayFlash({
					error: 'An error occurred. Please, try again.'
				});
			});
		});
	});
</script>
<script type="text/javascript">
	$(function() {
		$('.verification-image-preview').on('click', function(e) {
			e.preventDefault();
			$.get(
				"<?php echo Router::url(['controller' => 'orders', 'action' => 'verification_image', $order_id]); ?>",
				function(data) {
					bootbox.alert({
						size: 'small',
						title: 'Verification Image',
						message: data,
						backdrop: true
					});
				}
			);
		});
	});
</script>
<?php $this->end(); ?>
