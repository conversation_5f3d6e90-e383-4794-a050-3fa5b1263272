<?php

/**
 * @var AppView $this
 * @var array $product
 * @var int $selectedBrandId
 * @var int $selectedWarehouseId
 * @var int $cartId
 * @var string $b2bCartType
 */

$this->assign('title', 'Variants');
?>
<table class="table table-striped table-hover table-condensed">
    <thead>
    <tr>
        <th><span class="visually-hidden"><?= __('Thumbnail') ?></span></th>
        <th><?= __("Variant"); ?></th>
        <th class="hidden-xs"><?= __('SKU/UPC'); ?></th>
        <th><?= __('MSRP'); ?></th>
        <th><?= __('Stock'); ?></th>
        <th><?= __('Order QTY'); ?></th>
        <th><span class="visually-hidden"><?= __('Min/Max Order Quantity') ?></span></th>
        <th class="consumer-view-hide hidden-xs"><?= __('Unit Price'); ?></th>
        <th class="consumer-view-hide"><?= __('Line Total'); ?></th>
        <th><span class="visually-hidden"><?= __('Add to Order') ?></span></th>
    </tr>
    </thead>
    <tbody>
    <?php foreach ($product['Variants'] as $idx => $variant) {
        echo $this->element('Products/product_list_item_variant', [
            'idx' => $idx,
            'product' => $product,
            'variant' => $variant,
            'selectedBrandId' => $selectedBrandId,
            'selectedWarehouseId' => $selectedWarehouseId,
            'cartId' => $cartId,
            'b2bCartType' => $b2bCartType,
            'showAddToCartButton' => true,
            'showMsrp' => true,
            'editSKU' => false,
            'editUPC' => false,
            'editVariantImage' => false,
        ]);
    } ?>
    </tbody>
</table>
