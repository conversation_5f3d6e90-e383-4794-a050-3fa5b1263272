<?php
/**
 * @var AppView $this
 * @var array $storeTiming
 */
?>
<table id="StoreTimingTable">
	<?php foreach ($storeTiming as $key => $timing) { ?>
		<tr data-key="<?php echo $key; ?>">
			<th><?php echo $timing['weekday']['long']; ?></th>
			<td><?php echo (!$timing['Closed']) ? $timing['start'] . ' - ' . $timing['end'] : '<span class="text-muted">' . __('Closed') . '	</span>'; ?></td>
		</tr>
	<?php } ?>
</table>
<?php echo $this->Form->hidden('User.store_timing', ['value' => User::encodeStoreTiming($storeTiming)]); ?>
