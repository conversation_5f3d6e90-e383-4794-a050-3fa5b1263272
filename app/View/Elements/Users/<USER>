<?php

App::uses('StripePaymentType', 'Stripe.Enum');

/**
 * @var AppView $this
 * @var array $stripeUserCapabilities
 * @var array $profile
 */
?>
<div class="cont-cmpny-logo">
    <div class="cont-cmpy-details">
    <?php foreach (StripePaymentType::getUserHasCapabilityByPaymentType($stripeUserCapabilities) as $availableMethod => $hasCapability) { ?>
        <h5>
            <i class="far <?= $hasCapability ? 'fa-check-circle' : 'fa-times-circle' ?>"></i>
            <span><?= ucwords($availableMethod) ?></span>
        </h5>
    <?php } ?>
    </div>
</div>
