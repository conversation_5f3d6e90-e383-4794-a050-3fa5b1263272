<?php

use ShipEarlyApp\Lib\Utility\UserSiteType;

/**
 * @var AppView $this
 * @var array $shipearly_user
 * @var string $title_for_layout
 * @var string[] $country
 * @var string[] $selectedCountry
 * @var bool $map
 * @var bool $msrp
 * @var bool $shippingTaxOption
 */
?>
<script type="text/javascript">
(function() {
	var MAGENTO = <?= json_encode(UserSiteType::MAGENTO) ?>;
	var SHIPEARLY = <?= json_encode(UserSiteType::SHIPEARLY) ?>;
	var SHOPIFY = <?= json_encode(UserSiteType::SHOPIFY) ?>;
	var WOOCOMMERCE = <?= json_encode(UserSiteType::WOOCOMMERCE) ?>;

	var apiCredentialDefaults = ['#shop_url', '#api_key', '#secret_key', '#webhook_shared_secret'].reduce(function(acc, selector) {
		var $input = $(selector);
		acc[selector] = {
			available: false,
			label: $(`label[for="${$input.attr('id')}"]`).text(),
			placeholder: null, // Default to same as label
		};
		return acc;
	}, {});
	window.ecommerceChange = function() {
		var $countryListGroup = $('#countryRestriction');
		var $abandonCartGroup = $('#abandonCartNotification');

		$countryListGroup.hide();
		$abandonCartGroup.hide();

		var apiCredentialConfigs = {};
		var site_type = $('#site_type').val();
		if (site_type === SHIPEARLY) {
			$countryListGroup.show();
			$abandonCartGroup.show();
		} else if (site_type === SHOPIFY) {
			$countryListGroup.show();
			$abandonCartGroup.show();

			apiCredentialConfigs['#shop_url'] = { available: true, label: 'Shop Domain', placeholder: 'example.myshopify.com' };
			apiCredentialConfigs['#secret_key'] = { available: true, label: 'Admin API access token', placeholder: 'shpat_XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX' };
			apiCredentialConfigs['#webhook_shared_secret'] = { available: true };
		} else if (site_type === MAGENTO) {
			$('#resetsubmit').removeAttr('disabled');

			apiCredentialConfigs['#shop_url'] = { available: true };
			apiCredentialConfigs['#api_key'] = { available: true };
			apiCredentialConfigs['#secret_key'] = { available: true };
		} else if (site_type === WOOCOMMERCE) {
			$countryListGroup.show();

			apiCredentialConfigs['#shop_url'] = { available: true };
			apiCredentialConfigs['#api_key'] = { available: true, label: 'Consumer Key' };
			apiCredentialConfigs['#secret_key'] = { available: true, label: 'Consumer Secret' };
		}

		Object.keys(apiCredentialDefaults).forEach(function(selector) {
			var $input = $(selector);
			if (!$input.length) {
				return;
			}

			var config = { ...apiCredentialDefaults[selector], ...apiCredentialConfigs[selector] };
			if (!config.placeholder && config.placeholder !== '') {
				config.placeholder = config.label;
			}

			$input.closest('.form-group').toggle(config.available);
			if (!config.available) {
				return;
			}
			$(`label[for="${$input.attr('id')}"]`).text(config.label);
			$input.attr('placeholder', config.placeholder);
		});
	}

	window.ecommerceChange();
})();
$(function() {
	$('#public-methods').multiSelect();

	$('#select-all').click(function() {
		$('#public-methods').multiSelect('select_all');
		return false;
	});

	$('#deselect-all').click(function() {
		$('#public-methods').multiSelect('deselect_all');
		return false;
	});

	$('#brand_accent_color').on('change input', function() {
		var $this = $(this);
		var color = $this.val();
		if (color.indexOf('#') === 0 && color.length === parseInt($this.attr('maxlength'))) {
			$('#brand_accent_color_preview').css('background-color', color);
		} else {
			$('#brand_accent_color_preview').css('background-color', '');
		}
	}).change();

	$('#site_type').on('change', function() {
		window.ecommerceChange();
	});
});
</script>
