<?php
/**
 * @var AppView $this
 */
?>
<script type="text/javascript">
$(function() {
	$('.js-inventory-import').click(function() {
		var $this = $(this);
		var action = $this.data('action');
		var retailerName = $this.data('retailer-name');

		bootbox.dialog({
			size: 'small',
			title: (retailerName ? ("<?= __('Upload Inventory for'); ?> " + retailerName) : '<?= __('Upload Inventory'); ?>'),
			message: `<?php
				echo $this->Form->create('Store', array('url' => array('controller' => 'locations', 'action' => 'importInventory'), 'type' => 'file', 'class' => 'bootbox-form', 'inputDefaults' => array('label' => false, 'div' => false)));
				echo $this->Html->para(null, __('Manually uploaded inventory will be reset daily.') . '<br>' . __('The expected file format is an Excel or CSV file with the same layout as the export file.'));
				echo $this->Form->file('inventory_upload', ['id' => 'InventoryFile']);
				echo $this->Form->end(['style' => 'display: none;', 'div' => false]);
			?>`,
			buttons: {
				Cancel: {
					label: "<?= __('Cancel'); ?>",
					callback: function() {}
				},
				OK: function() {
					$(this).find('form').submit();
					return false;
				}
			}
		}).on('shown.bs.modal', function() {
			$(this).find('form').attr('action', action);
		});
	});
});
</script>
