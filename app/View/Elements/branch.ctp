<?php
/**
 * @var AppView $this
 * @var array $shipearly_user
 * @var array $branchs
 */
?>
<style type="text/css">
.deleteInstorePickup,
.deleteLocalDelivery {
	cursor: pointer;
}
.error {
	color: #ff0000;
}
.no-of-retails span {
    color: gray;
    font-weight: bold;
    font-size: 15px;
}
.no-of-retails span:hover {
    text-decoration: none
}
</style>
<?php echo $this->Form->create('Location', [
	'id' => 'LocationIndex',
	'type' => 'PUT',
	'default' => false,
	'inputDefaults' => [
		'class' => 'form-control',
		'div' => false,
		'label' => false,
	],
]); ?>
	<table id="timeTable" class="table table-striped table-hover" style="border-collapse: collapse;">
		<thead>
			<tr>
				<th><?= __('Location Name'); ?></th>
				<th><?= __('Status'); ?></th>
				<th><?= __('Action'); ?></th>
				<th><?= __('Tax Rate'); ?></th>
				<th><?= __('Tax Name'); ?></th>
				<th><?= __('Scheduling'); ?></th>
				<th><?= __('Inventory'); ?></th>
			</tr>
		</thead>
		<tbody>
		<?php foreach ($branchs as $value) { ?>
			<tr>
				<td class="notranslate"><?php echo $this->Shipearly->makeContactLink($value) . ' ' . str_replace('_,_', ', ', $value['User']['address']) . ', ' . $value['User']['city']; ?></td>
				<td><?php echo User::getStatusName($value['User']['status']); ?></td>
				<td><?php echo implode(' <span class="notranslate">|</span> ', array_filter([
							$this->Shipearly->displayEditBranchLink($value['User']['id'], $shipearly_user['User']['id']),
							$this->Shipearly->displayActivateBranchLink($value['User']['status'], $value['User']['id'], $shipearly_user['User']['id']),
							($value['User']['Branch'] == $shipearly_user['User']['id'])
								? '<a href="' . BASE_PATH . 'accessLevel/' . $value['User']['id'] . '" title="' . __('Permission') . '"><i class="fas fa-lock"></i></a>'
								: null,
							'<a href="' . BASE_PATH . 'storehours/' . $value['User']['id'] . '" title="' . __('Store Timing') . '"><i class="far fa-clock"></i></a>',
					])); ?></td>
				<td> <!-- Tax Rate -->
					<?php echo $this->Form->input("User.{$value['User']['id']}.defaultTax", array(
						'type' => 'text',
						'placeholder' => '0,0',
						'title' => __('Example: 7.2 for 7.2%'),
						'value' => format_number($value['User']['defaultTax'], 3),
						'class' => 'form-control inventory-settings__default-tax',
						'style' => 'width: 100px; margin: 0 auto;',
						'data-href' => $this->Url->build(['controller' => 'branchs', 'action' => 'saveTaxData', 'id' => $value['User']['id']]),
					)); ?>
				</td>
				<td> <!-- Tax Name -->
					<?php echo $this->Form->input("User.{$value['User']['id']}.defaultTaxName", array(
						'type' => 'text',
						'placeholder' => __('Tax'),
						'title' => __('Tax Name'),
						'value' => $value['User']['defaultTaxName'],
						'class' => 'form-control inventory-settings__default-tax',
						'style' => 'width: 100px; margin: 0 auto;',
						'data-href' => $this->Url->build(['controller' => 'branchs', 'action' => 'saveTaxData', 'id' => $value['User']['id']]),
					)); ?>
				</td>
				<td>
					<button type="button" class="btn btn-primary editschedule"
							data-retailer-id="<?php echo $value['User']['id']; ?>"
							data-retailer-name="<?php echo $value['User']['company_name']; ?>"
					><?= __('Edit'); ?></button>
				</td>
				<td>
					<?php if ($value['User']['has_active_integration']) : ?>
						<a href="<?= $this->Url->build(['controller' => 'users', 'action' => 'inventory_settings', 'id' => $value['User']['id']]) ?>">
							<?= __('Connected') ?>
						</a>
					<?php else : ?>
						<a href="<?= $this->Url->build(['controller' => 'users', 'action' => 'inventory_settings', 'id' => $value['User']['id']]) ?>" class="btn btn-primary">
							<?= __('Sync'); ?>
						</a>
					<?php endif; ?>
				</td>
			</tr>
		<?php } ?>
		<?php if (count($branchs) == 0) { ?>
			<tr><td colspan="10"><?= __('No Locations'); ?></td></tr>
		<?php } ?>
		</tbody>
	</table>
<?php echo $this->Form->end(['style' => 'display: none;', 'div' => false]); ?>
<!-- Scheduling Setup -->
<div class="modal fade" id="schedulingmodaldiv" role="dialog" aria-labelledby="gridSystemModalLabel">
	<div class="modal-dialog shipment-setting-modal" role="document">
		<div class="modal-content" style="background-color: #fff !important;">
			<div class="modal-header">
				<h2>
					<a href="#" onclick="javascript:void(0)">
						<p class="no-of-retails" style="text-align: center;">
							<span id='retailerName' style="text-decoration: none;"></span>
						</p>
					</a>
				</h2>
			</div>
			<div class="modal-body" id="schedulingmodalbody">
				<p id="retailerAddress" style="text-align: center;"></p>
				<div>
					<div class="row" style="margin-bottom:10px;">
						<div class="col-md-4"><?= __('Store Pickup'); ?></div>
						<div class="col-md-7">
							<button type="button" class="btn btn-sm btn-primary addInStorePickup pull-right"><?= __('Add New'); ?></button>
						</div>
					</div>
					<div class="row instorepickupentry" id="instorepickupentry" style="margin-bottom:10px;">
						<input type="hidden" class="instorepickup_id" value="0" />
						<div class="col-md-4">
							<input type="text" class="form-control instorepickup_name" placeholder="<?= __('Store pickup name'); ?>" />
						</div>
						<div class="col-md-7">
							<input type="text" class="form-control instorepickup_link" placeholder="<?= __('Calendly Link for Store pickup'); ?>" />
						</div>
						<div class="col-md-1 deleteInstorePickup">X</div>
					</div>
					<br />
					<div class="row" style="margin-bottom:10px;">
						<div class="col-md-4"><?= __('Local Delivery'); ?></div>
						<div class="col-md-7">
							<button type="button" class="btn btn-sm btn-primary addLocalDelivery pull-right"><?= __('Add New'); ?></button>
						</div>
					</div>
					<div class="row localdeliveryentry" id="localdeliveryentry" style="margin-bottom:10px;">
						<input type="hidden" class="localdelivery_id" value="0" />
						<div class="col-md-4">
							<input type="text" class="form-control localdelivery_name" placeholder="<?= __('Local Delivery Name'); ?>" />
						</div>
						<div class="col-md-7">
							<input type="text" class="form-control localdelivery_link" placeholder="<?= __('Calendly Link for Local Delivery'); ?>" />
						</div>
						<div class="col-md-1 deleteLocalDelivery">X</div>
					</div>
					<div class="row">
						<div class="col-md-12">
							<p><?= __("We've partnered with Calendly to make scheduling appointment without back and forth emails or phone calls with customers easier. Basic accounts are free but for best service the $8/month plan can cover all your stores and local delivery to sync with your calender and set customizable rules."); ?></p>
							<p class="error"></p>
						</div>
					</div>
				</div>
			</div><!-- /.modal-body -->
			<div class="modal-footer">
				<button type="submit" id="schedulingCancel" name="deleteZoneCancel" class="btn btn-default" data-bs-dismiss="modal"><?= __('Cancel'); ?></button>
				<button type="submit" id="schedulingOk" name="schedulingOk" class="btn btn-default update_scheduleDetails" data-value=""><?= __('Update'); ?></button>
			</div>
		</div><!-- /.modal-content -->
	</div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<script type="text/javascript">
$(document).ready(function() {
    $('.form-control.inventory-settings__default-tax').clickToEdit('(empty)').on('blur', function(e) {
        $.ajax({
            type: 'POST',
            url: $(this).data('href'),
            data: $(this).closest('tr').find('.form-control.inventory-settings__default-tax').serialize(),
            dataType: 'json',
            error: function(xhr, status, error) {
                var message = 'Error occurred while saving data';
                if (xhr && xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                }
                alert(message);
            }
        });
    });
});

$(function() {
	var user_id = "";
	var instorepickup_delete_id = [];
	var localdelivery_delete_id = [];
	var hasSchedule = false;

	$('.editschedule').on('click', function(event) {
		event.preventDefault();
		$('#schedulingmodaldiv').modal('show');
		$('#retailerName').html($(this).attr('data-retailer-name'));
		user_id = $(this).attr('data-retailer-id');

		var instore = 0;
		var localdelivery = 0;
		$.ajax({
			type: "GET",
			url: "<?php echo BASE_PATH; ?>locations/schedule",
			data: { "user_id": user_id },
			success: function(result) {
				var response = JSON.parse(result);
				if (response.status == 'success') {
					var userschedules = response.data;
					hasSchedule = true;
					$.each(userschedules, function(i, v) {
						var schedule = v['UserSchedule'];
						if (schedule.order_type == 'In_store') {
							if (instore > 0) {
								var template = $('.instorepickupentry');
								$(template).not(':first').remove();
								var templatecopy = template.clone();
								templatecopy.insertAfter(template);
								var instorepickup_id = $(templatecopy).find('.instorepickup_id');
								$(instorepickup_id).val(schedule.id)
								var instorepickup_name = $(templatecopy).find('.instorepickup_name');
								$(instorepickup_name).val(schedule.schedule_name);
								var instorepickup_link = $(templatecopy).find('.instorepickup_link');
								$(instorepickup_link).val(schedule.schedule_link);
								templatecopy.bind("click", function(evt) {
									if ($(evt.target).html() == "X") {
										deleteEntry(templatecopy);
									}
								});
							} else {
								var template = $('.instorepickupentry');
								$(template).not(':first').remove();
								$('.instorepickup_id').val(schedule.id);
								$('.instorepickup_name').val(schedule.schedule_name);
								$('.instorepickup_link').val(schedule.schedule_link);
							}
							instore += 1;
						}
						else {
							if (localdelivery > 0) {
								var template = $('.localdeliveryentry');
								$(template).not(':first').remove();
								var templatecopy = template.clone();
								templatecopy.insertAfter(template);
								var localdelivery_id = $(templatecopy).find('.localdelivery_id');
								$(localdelivery_id).val(schedule.id)
								var localdelivery_name = $(templatecopy).find('.localdelivery_name');
								$(localdelivery_name).val(schedule.schedule_name);
								var localdelivery_link = $(templatecopy).find('.localdelivery_link');
								$(localdelivery_link).val(schedule.schedule_link);
								templatecopy.bind("click", function(evt) {
									if ($(evt.target).html() == "X") {
										deleteEntry(templatecopy);
									}
								});
							}
							else {
								var template = $('.localdeliveryentry');
								$(template).not(':first').remove();
								$('.localdelivery_id').val(schedule.id);
								$('.localdelivery_name').val(schedule.schedule_name);
								$('.localdelivery_link').val(schedule.schedule_link);
							}
							localdelivery += 1;
						}
					});

					if (userschedules.length == 0) {
						var template = $('.instorepickupentry');
						$($(template).find('.instorepickup_id')).val("0");
						$($(template).find('.instorepickup_name')).val("");
						$($(template).find('.instorepickup_link')).val("");
						if (template.length > 1) {
							$(template).not(':first').remove();
						}

						template = $('.localdeliveryentry');
						$($(template).find('.localdelivery_id')).val("0");
						$($(template).find('.localdelivery_name')).val("");
						$($(template).find('.localdelivery_link')).val("");
						if (template.length > 1) {
							$(template).not(':first').remove();
						}
					}
				}
			},
			error: function(request, status, error) {
				console.error("Error processing page");
			}
		});
	});

	$('.addInStorePickup').on('click', function(event) {
		event.preventDefault();
		var template = $('#instorepickupentry');
		var templatecopy = template.clone();
		templatecopy.insertAfter(template);
		var instorepickup_id = $(templatecopy).find('.instorepickup_id');
		$(instorepickup_id).val("0");
		var instorepickup_name = $(templatecopy).find('.instorepickup_name');
		$(instorepickup_name).val("");
		var instorepickup_link = $(templatecopy).find('.instorepickup_link');
		$(instorepickup_link).val("");
		templatecopy.bind("click", function(evt) {
			if ($(evt.target).html() == "X") {
				deleteEntry(templatecopy);
			}
		});
	});

	$('.addLocalDelivery').on('click', function(event) {
		event.preventDefault();
		var template = $('#localdeliveryentry');
		var templatecopy = template.clone();
		templatecopy.insertAfter(template);
		var localdelivery_id = $(templatecopy).find('.localdelivery_id');
		$(localdelivery_id).val("0");
		var localdelivery_name = $(templatecopy).find('.localdelivery_name');
		$(localdelivery_name).val("");
		var localdelivery_link = $(templatecopy).find('.localdelivery_link');
		$(localdelivery_link).val("");
		templatecopy.bind("click", function(evt) {
			if ($(evt.target).html() == "X") {
				deleteEntry(templatecopy);
			}
		});
	});

	$('#schedulingOk').on('click', function(event) {
		event.preventDefault();
		var instorepickup_id = [];
		var instorepickup_name = [];
		var instorepickup_link = [];
		var localdelivery_id = [];
		var localdelivery_name = [];
		var localdelivery_link = [];
		$('p.error').html("");
		$.each($('#schedulingmodalbody > div input[type=text]'), function(i, v) {
			if ($(v).attr('class') == 'form-control instorepickup_name' && $(v).val() != '') {
				instorepickup_name.push($(v).val());
				instorepickup_id.push($(v).parent().prev().val());
			}
			else if ($(v).attr('class') == 'form-control instorepickup_link' && $(v).val() != '') {
				instorepickup_link.push($(v).val());
			}
			else if ($(v).attr('class') == 'form-control localdelivery_name' && $(v).val() != '') {
				localdelivery_name.push($(v).val());
				localdelivery_id.push($(v).parent().prev().val());
			}
			else if ($(v).attr('class') == 'form-control localdelivery_link' && $(v).val() != '') {
				localdelivery_link.push($(v).val());
			}
		});

		if (instorepickup_name.length != instorepickup_link.length) {
			$('p.error').html("Please provide valid entry for InStore Pickup Schedule name/link");
			return false;
		}
		if (localdelivery_name.length != localdelivery_link.length) {
			$('p.error').html("Please provide valid entry for Local Delivery Schedule name/link");
			return false;
		}
		if (instorepickup_name.length > 0 || localdelivery_name.length > 0 || hasSchedule) {
			$.ajax({
				type: "POST",
				url: "<?php echo BASE_PATH; ?>locations/schedule/save",
				data: {
					"user_id": user_id,
					"instorepickup_ids": JSON.stringify(instorepickup_id),
					"instorepickup_names": JSON.stringify(instorepickup_name),
					"instorepickup_links": JSON.stringify(instorepickup_link),
					"localdelivery_ids": JSON.stringify(localdelivery_id),
					"localdelivery_names": JSON.stringify(localdelivery_name),
					"localdelivery_links": JSON.stringify(localdelivery_link),
					"instorepickup_delete_ids": JSON.stringify(instorepickup_delete_id),
					"localdelivery_delete_ids": JSON.stringify(localdelivery_delete_id)
				},
				dataType: 'json',
				success: function(result) {
					console.log(result);
					window.location.reload();
				},
				error: function(request, status, error) {
					console.error("Error processing page");
				}
			});
		}
	});

	function deleteEntry(element) {
		var $element = $(element);
		if ($element.find('.instorepickup_id').length != 0) {
			instorepickup_delete_id.push($element.find('.instorepickup_id').val());
		}
		else if ($element.find('.localdelivery_id').length != 0) {
			localdelivery_delete_id.push($element.find('.localdelivery_id').val());
		}
		$element.remove();
	}
});
</script>
<?php echo $this->element('Users/configuration/retailer_inventory_import_popup_script'); ?>
