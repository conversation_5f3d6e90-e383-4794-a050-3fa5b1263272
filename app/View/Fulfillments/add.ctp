<?php

use ShipEarlyApp\Lib\Utility\UserSiteType;

/**
 * @var AppView $this
 * @var bool $isDealerOrder
 * @var bool $isCreditOrder
 * @var bool $isShipFromStoreShipmentStatus
 * @var string $orderAlias
 * @var string $orderProductAlias
 * @var string $orderProductKey
 * @var array $order
 * @var string $customerAddress
 * @var array $warehouseOptions
 * @var array $courierOptions
 */
?>
<?php
App::uses('User', 'Model');
App::uses('OrderType', 'Utility');

if (!$this->request->is('ajax')) {
    $this->Html->css('order-popup.css', ['inline' => false, 'once' => true]);
}

$selectedWarehouseId = $this->request->data('Fulfillment.warehouse_id');

foreach ($order[$orderProductAlias] as &$orderProduct) {
    foreach (array_keys($warehouseOptions) as $warehouseId) {
        $orderProduct['AvailableQuantities'][$warehouseId] = array_key_exists($warehouseId, $orderProduct['AvailableQuantities'])
            ? $this->Inventory->formatBrandInventory($orderProduct['AvailableQuantities'][$warehouseId], null)
            : __('Not Stocked');
    }
}

$this->assign('title', "Fulfill {$order['Order']['orderID']}");
?>
<?= $this->Form->create('Fulfillment', [
    'id' => 'FulfillmentForm',
    'inputDefaults' => [
        'class' => 'form-control',
        'div' => 'form-group',
    ],
]) ?>
<div class="clearfix"><?= $this->Layout->contentHeader() ?></div>
<div class="row">
    <div class="col-6">
        <strong>Customer Shipping</strong>
        <address><?= $customerAddress ?></address>
    </div>
    <div class="col-6">
        <div class="form-group">
            <strong>Shipping Details</strong>
            <div>Shipping Paid: <?= $order[$orderAlias]['shipping_amount'] ?></div>
        </div>
        <?php
        if (!$isShipFromStoreShipmentStatus) {
            echo $this->Form->input('warehouse_id', [
                'type' => 'select',
                'id' => 'FulfillmentWarehouseId',
                'options' => $warehouseOptions,
                'class' => 'selectpicker form-control',
            ]);
        }
        ?>
    </div>
</div>
<table class="order-popup-products-table" style="margin-bottom: 10px;">
    <thead>
        <tr>
            <th colspan="2"><span class="visually-hidden"><?= __('Product') ?></span></th>
            <th><?= __('Available') ?></th>
            <th><?= __('Fulfill') ?></th>
        </tr>
    </thead>
    <tbody>
    <?php foreach (Hash::combine($order[$orderProductAlias], '{n}.product_id', '{n}', '{n}.warehouse_id') as $warehouseId => $orderProducts) { ?>
        <?= $this->OrderProducts->orderPopupWarehouseTr($warehouseId, current($orderProducts)['Warehouse']['name'] ?? '') ?>

        <?php foreach ($orderProducts as $orderProduct) { ?>
            <tr class="main">
                <td class="product-thumbnail-container">
                    <div class="badge badge-quantity"><?= $orderProduct['remaining_quantity'] ?></div>
                    <span>
                        <a href="<?= BASE_PATH . 'products/' . $orderProduct['Product']['uuid'] ?>" target="_blank">
                            <img class="product-thumbnail" src="<?= $orderProduct['Product']['product_image'] ?: BASE_PATH . 'images/no_img.gif' ?>"  alt="thumbnail" />
                        </a>
                    </span>
                </td>
                <td>
                    <a href="<?= BASE_PATH . 'products/' . $orderProduct['Product']['uuid'] ?>" target="_blank">
                        <p class="no-of-retails" style="text-align: left;">
                            <span><?= $orderProduct['Product']['product_title'] ?></span>
                        </p>
                    </a>
                    (<?= $orderProduct['Product']['product_sku'] ?>)
                    <br />
                    UPC <?= $orderProduct['Product']['product_upc'] ?>
                </td>
                <td style="width: 125px;" class="available-quantity"
                    data-available-quantities='<?= json_encode($orderProduct['AvailableQuantities']) ?>'>
                    <div><?= $orderProduct['AvailableQuantities'][$selectedWarehouseId] ?></div>
                </td>
                <td style="width: 125px;">
                    <?= $this->Form->hidden("FulfillmentProduct.{$orderProduct['id']}.{$orderProductKey}", ['value' => $orderProduct['id']]) ?>
                    <?= $this->Form->input("FulfillmentProduct.{$orderProduct['id']}.quantity", [
                        'type' => 'number',
                        'min' => 0,
                        'max' => $orderProduct['remaining_quantity'],
                        'step' => 1,
                        'readonly' => $isShipFromStoreShipmentStatus,
                        'class' => 'js-orderproduct-quantity form-control',
                        'label' => false,
                        'div' => false,
                        'between' => '<div class="input-group">',
                        'after' => "<span class=\"input-group-text\">of {$orderProduct['remaining_quantity']}</span></div>",
                    ]) ?>
                </td>
            </tr>
        <?php } ?>
    <?php } ?>
    </tbody>
</table>
<div class="row">
    <div class="col-6">
        <?= $this->Form->input('courier_id', [
            'type' => 'select',
            'id' => 'FulfillmentCourierId',
            'options' => [['other' => 'Other'], 'Carriers' => $courierOptions],
            'showParents' => true,
            'empty' => 'None',
            'class' => 'selectpicker form-control',
            'data-live-search' => 'true',
            'data-dropup-auto' => 'false',
            'label' => 'Shipping Carrier (Optional)',
        ]) ?>
    </div>
    <div class="col-6">
        <?= $this->Form->input('tracking_number', [
            'type' => 'text',
            'id' => 'FulfillmentTrackingNumber',
            'div' => 'fulfillment-tracking-field form-group',
        ]) ?>
    </div>
</div>
<div class="row">
    <div class="col-12">
        <?= $this->Form->input('tracking_url', [
            'type' => 'text',
            'id' => 'FulfillmentTrackingUrl',
            'div' => 'fulfillment-tracking-field form-group',
        ]) ?>
    </div>
</div>
<?php if (
    $order[$orderAlias]['source_id'] &&
    in_array($order['User']['site_type'], [UserSiteType::SHOPIFY], true) &&
    !$isShipFromStoreShipmentStatus
) { ?>
    <?= $this->Form->input('place_ecommerce_fulfillment', [
        'type' => 'checkbox',
        'id' => 'FulfillmentPlaceEcommerceFulfillment',
        'checked' => true,
        'class' => 'form-check-input',
        'label' => 'Mark items as fulfilled in ' . $order['User']['site_type'],
        'div' => 'form-check',
    ]) ?>
<?php } ?>
<?php if ($isCreditOrder && $isDealerOrder) { ?>
    <?= $this->Form->input('create_retailer_credit', [
        'type' => 'checkbox',
        'id' => 'FulfillmentCreateRetailerCredit',
        'checked' => true,
        'class' => 'form-check-input',
        'label' => 'Create credit invoice for Retailer',
        'div' => 'form-check',
    ]) ?>
<?php } ?>
<?= $this->Form->end(['label' => 'Fulfill', 'class' => 'btn btn-primary', 'div' => ['style' => 'text-align: right;']]) ?>
<?php $this->start('script'); ?>
<script>
$(function() {
    $('#FulfillmentForm').on('submit', function() {
        $(this).find('input[type="submit"], button[type="submit"]').prop('disabled', true);
    });

    $('#FulfillmentCourierId').on('change', function() {
        var disabled = !this.value;
        $('.fulfillment-tracking-field').toggle(!disabled)
            .find(':input').prop('disabled', disabled);

        $('#FulfillmentTrackingNumber').prop('required', this.value && this.value !== 'other');
        $('#FulfillmentTrackingUrl').prop('required', this.value === 'other');
    }).trigger('change');

    $('#FulfillmentWarehouseId').on('change', function() {
        var $warehouseId = $(this);
        var selectedWarehouseId = $warehouseId.val();
        $('.available-quantity').each(function() {
            var availableQuantities = $(this).data('available-quantities');
            $(this).text(availableQuantities[selectedWarehouseId]);
        });

        $(`input[name="${$('#FulfillmentPlaceEcommerceFulfillment').attr('name')}"]`)
            .prop('disabled', !$warehouseId.find('option:selected').data('source-id'));
    }).trigger('change');
});
</script>
<?php $this->end(); ?>
