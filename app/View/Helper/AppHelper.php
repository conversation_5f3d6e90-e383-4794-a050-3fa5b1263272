<?php
/**
 * Application level View Helper
 *
 * This file is application-wide helper file. You can put all
 * application-wide helper-related methods here.
 *
 * CakePHP(tm) : Rapid Development Framework (https://cakephp.org)
 * Copyright (c) Cake Software Foundation, Inc. (https://cakefoundation.org)
 *
 * Licensed under The MIT License
 * For full copyright and license information, please see the LICENSE.txt
 * Redistributions of files must retain the above copyright notice.
 *
 * @copyright     Copyright (c) Cake Software Foundation, Inc. (https://cakefoundation.org)
 * @link          https://cakephp.org CakePHP(tm) Project
 * @package       app.View.Helper
 * @since         CakePHP(tm) v 0.2.9
 * @license       https://opensource.org/licenses/mit-license.php MIT License
 */

App::uses('Helper', 'View');

/**
 * Application helper
 *
 * Add your application-wide methods in the class below, your helpers
 * will inherit them.
 *
 * @package       app.View.Helper
 */
class AppHelper extends Helper
{
    /**
     * Wrapper for static calls to AuthComponent::user.
     *
     * @param string|null $key
     * @return array|null|string
     * @see AuthComponent::user
     */
    protected function _authUser(?string $key = null)
    {
        return AuthComponent::user($key);
    }

        /**
     * @param string|string[] $classes1
     * @param string|string[] $classes2
     * @return string
     */
    protected function _mergeClasses($classes1, $classes2): string
    {
        if (is_string($classes1)) {
            $classes1 = explode(' ', $classes1);
        }
        if (is_string($classes2)) {
            $classes2 = explode(' ', $classes2);
        }
        return implode(' ', array_unique(array_filter(array_merge((array)$classes1, (array)$classes2))));
    }
}
