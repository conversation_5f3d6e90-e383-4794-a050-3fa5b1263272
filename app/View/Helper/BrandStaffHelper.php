<?php
App::uses('AppHelper', 'View/Helper');
App::uses('Permissions', 'Utility');

/**
 * BrandStaff Helper.
 *
 * @property FormHelper $Form
 * @property HtmlHelper $Html
 * @property ShipearlyHelper $Shipearly
 */
class BrandStaffHelper extends AppHelper
{
    public $helpers = ['Form', 'Html', 'Shipearly'];

    public function indexTable(array $staffUsers): string
    {
        $tableModel = [
            'Name' => function($staff) {
                return $this->Html->link($staff['StaffUser']['company_name'],
                    ['controller' => 'brand_staff', 'action' => 'view', 'staff_id' => $staff['BrandStaff']['staff_id']],
                    ['class' => 'link']
                );
            },
            'Email' => function($staff) {
                return $staff['StaffUser']['email_address'];
            },
            'Role' => function($staff) {
                return $staff['Role']['name'];
            },
            'Access' => function($staff) {
                return $staff['BrandStaff']['has_full_permissions'] ? 'Full' : 'Restricted';
            },
            'Last Login' => function($staff) {
                return $this->Shipearly->formatTime($staff['StaffUser']['last_login'], 'Never');
            },
        ];

        return $this->_indexTable($tableModel, $staffUsers);
    }

    public function adminIndexTable(array $staffUsers): string
    {
        $tableModel = [
            'Name' => function($staff) {
                return $staff['StaffUser']['company_name'];
            },
            'Email' => function($staff) {
                return $staff['StaffUser']['email_address'];
            },
            'Role' => function($staff) {
                return $staff['Role']['name'];
            },
            'Access' => function($staff) {
                return $staff['BrandStaff']['has_full_permissions'] ? 'Full' : 'Restricted';
            },
            'Last Login' => function($staff) {
                return $this->Shipearly->formatTime($staff['StaffUser']['last_login'], 'Never');
            },
            '' => function($staff) {
                return $this->Form->button('Login As', [
                    'type' => 'button',
                    'class' => 'btn btn-mini btn-warning fR',
                    'data-login-as-url' => $this->url(['admin' => true, 'controller' => 'users', 'action' => 'login_as', 'id' => $staff['StaffUser']['id']]),
                ]);
            },
        ];

        return $this->_indexTable($tableModel, $staffUsers);
    }

    private function _indexTable(array $tableModel, array $staffUsers)
    {
        $headers = array_keys($tableModel);

        $rows = [];
        foreach ($staffUsers as $staff) {
            $rows[] = array_map(
                function($cellCallback) use ($staff) {
                    return call_user_func($cellCallback, $staff);
                },
                array_values($tableModel)
            );
        }
        if (!$rows) {
            $rows[] = [
                [__('No Staff'), ['colspan' => count($headers), 'style' => 'text-align: center;']],
            ];
        }

        return <<<HTML
<table class="table table-striped table-hover">
    <thead>
        {$this->Html->tableHeaders($headers)}
    </thead>
    <tbody>
        {$this->Html->tableCells($rows)}
    </tbody>
</table>
HTML;
    }

    public function permissionsTable(string $modelAlias, array $permissionFields): string
    {
        $tableModel = [
            [
                'header' => '',
                'cellCallback' => function($options) use ($modelAlias){
                    return implode('', [
                        $this->Form->hidden("{$modelAlias}.Permission.{$options['name']}.name", ['value' => $options['name']]),
                        $this->Form->label("{$modelAlias}.Permission.{$options['name']}.level", $options['label'], ['style' => 'cursor: pointer;']),
                    ]);
                },
            ],
            [
                'header' => __('Edit'),
                'cellCallback' => function($options) use ($modelAlias){
                    return $this->Form->checkbox("{$modelAlias}.Permission.{$options['name']}.level", [
                        'value' => Permissions::LEVEL_EDIT,
                        'hiddenField' => true,
                    ]);
                },
            ],
        ];

        $headers = array_column($tableModel, 'header');

        $rows = [];

        foreach ($permissionFields as $name => $options) {
            $options['name'] = $options['name'] ?? $name;

            $rows[] = array_map(function($cellCallback) use ($options) {
                return call_user_func($cellCallback, $options);
            }, array_column($tableModel, 'cellCallback'));
        }

        return <<<HTML
<table class="table table-striped table-hover table-condensed">
    <thead>
        {$this->Html->tableHeaders($headers, null, ['style' => 'cursor: default;'])}
    </thead>
    <tbody>
        {$this->Html->tableCells($rows)}
    </tbody>
</table>
HTML;
    }
}
