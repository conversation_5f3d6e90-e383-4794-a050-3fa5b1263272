<?php

use ShipEarlyApp\Lib\Utility\SupportedLanguages;

App::uses('App<PERSON><PERSON><PERSON>', 'View/Helper');
App::uses('User', 'Model');
App::uses('AuthComponent', 'Controller/Component');

/**
 * Class CheckoutSettingsHelper.
 *
 * @property FormHelper $Form
 * @property HtmlHelper $Html
 */
class CheckoutSettingsHelper extends AppHelper
{
    public $helpers = ['Form', 'Html'];

    /**
     * @var null|string
     */
    public $locale = null;

    /**
     * @var null|string[]
     */
    public $messageDefaults = null;

    public function textInput(string $fieldName, string $label, string $placeholder = null, array $options = array()): string
    {
        return $this->Form->input($fieldName, $options + [
            'type' => 'text',
            'class' => 'form-control',
            'placeholder' => $placeholder,
            'label' => $label,
            'div' => ['class' => 'form-group'],
            'error' => ['attributes' => ['class' => 'help-block']],
        ]);
    }

    public function checkboxInput(string $fieldName, string $label, array $options = array()): string
    {
        return $this->Form->input($fieldName, $options + [
            'type' => 'checkbox',
            'between' => ' ',
            'label' => $label,
            'div' => true,
            'error' => ['attributes' => ['class' => 'help-block']],
        ]);
    }

    /**
     * @param string $fieldName
     * @param string $label
     * @param array $options
     * @param bool|string $defaultValue Default value for the input or TRUE to look it up using $fieldName
     * @param string|null $locale
     * @return string
     */
    public function localizedMessageInput(string $fieldName, string $label, array $options = array(), $defaultValue = true, string $locale = null): string
    {
        $locale = $locale ?? $this->_getLocale();

        $fullFieldName = $fieldName;
        if (strpos($fieldName, '.') === false) {
            $fullFieldName = "{$this->Form->defaultModel}.{$locale}.{$fieldName}";
        }

        if ($defaultValue === true) {
            $defaultValue = $this->_getDefaultValue($fieldName);
        }

        return $this->textInput($fullFieldName, $label, $defaultValue, $options);
    }

    private function _getLocale()
    {
        if ($this->locale === null) {
            $this->locale = $this->_View->viewVars['locale'] ?? SupportedLanguages::DEFAULT_LOCALE;
        }
        return $this->locale;
    }

    private function _getDefaultValue($fieldName)
    {
        if ($this->messageDefaults === null) {
            $contactEmail = $this->request->data['User']['email_address'] ?? AuthComponent::user('email_address');
            $this->messageDefaults = $this->_View->viewVars['messageDefaults'] ?? User::getMessageDefaults($contactEmail);
        }
        return $this->messageDefaults[$fieldName] ?? null;
    }
}
