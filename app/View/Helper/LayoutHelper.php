<?php
App::uses('App<PERSON>elper', 'View/Helper');
App::uses('ProductStatus', 'Utility');

/**
 * Class LayoutHelper.
 *
 * @property FormHelper $Form
 * @property HtmlHelper $Html
 */
class LayoutHelper extends AppHelper
{
    public $helpers = array('Form', 'Html');

    /**
     * @param string $title
     * @param string|string[] $class
     * @param string $style
     * @return string
     */
    public function contentHeader($title = null, $class = null, $style = null)
    {
        $title = $title ?: $this->_View->fetch('title') ?: $this->_View->get('title_for_layout');
        $head = $this->Html->tag('h1', (string)$title, ['class' => 'content-head']);
        return $this->Html->tag('div', $head, array(
            'class' => $this->_mergeClasses('con1-head1', $class),
            'style' => $style ?: null,
        ));
    }

    /**
     * @param array $titleUrls Key-value pairs ['title' => $url]
     * @return string
     */
    public function breadcrumbHeader(array $titleUrls)
    {
        $path = array();
        foreach ($titleUrls as $title => $url) {
            if (is_numeric($title)) {
                $title = $url;
                $url = false;
            }
            $path[] = ($url !== false)
                ? "<a href=\"{$this->url($url)}\" class=\"link\">{$title}</a>"
                : "<span>{$title}</span>";
        }
        return '<h4 class="breadcrumb-header">' . implode(' / ', $path) . '</h4>';
    }

    /**
     * @param string $text
     * @param array|null|string $url
     * @param array $options
     * @return string
     * @see HtmlHelper::link
     */
    public function headerButtonLink($text, $url, array $options = []): string
    {
        $options['class'] = $this->_mergeClasses('btn btn-primary btn-lg btn-content-header', $options['class'] ?? '');

        return $this->Html->link($text, $url, $options);
    }

    public function ordersFilterBar($fullfilltype, $userType)
    {
        $exclude = [
            OrderType::WHOLESALE,
            OrderType::RETAILER_ORDER,
        ];
        if (!in_array($userType, User::TYPES_BRAND, true)) {
            $exclude[] = OrderType::SELL_DIRECT;
        }

        $options = OrderType::getFulfillTypeOptions($exclude);

        return $this->Form->input('order_type', [
            'type' => 'select',
            'id' => 'orderTypeSearch',
            'class' => 'rows notranslate',
            'options' => $options,
            'default' => $fullfilltype,
            'multiple' => true,
            'data-placeholder' => __('List All Order Types'),
            'data-live-search' => 'true',
            'onChange' => 'filterTable();',
            'data-width' => '195px',
        ]);
    }

    public function wholesaleFilterBar($orderType)
    {
        $options = OrderType::getWholesaleTypeOptions();

        return $this->Form->input('order_type', [
            'type' => 'select',
            'id' => 'orderTypeSearch',
            'class' => 'rows notranslate',
            'options' => $options,
            'default' => $orderType,
            'empty' => __('List All Order Types'),
            'data-live-search' => 'true',
            'onChange' => 'filterTable();',
            'data-width' => '195px',
        ]);
    }

    public function productsFilterBar($orderStatus)
    {
        return $this->filterBar($orderStatus, array(
            Product::STATUS_ENABLED => Product::getStatusName(Product::STATUS_ENABLED),
            Product::STATUS_INCOMPLETE => Product::getStatusName(Product::STATUS_INCOMPLETE),
            Product::STATUS_DISABLED => Product::getStatusName(Product::STATUS_DISABLED),
            '' => __('All'),
        ), array(
            'class' => 'filtermenu pro_filter',
        ));
    }

    public function filterBar($currentValue, array $options, $ulAttributes = array())
    {
        $currentValue = (string)$currentValue;

        $ulAttributes['class'] = $this->_mergeClasses('filtermenu list-inline nav nav-fill', Hash::get($ulAttributes, 'class', ''));

        $out = '';
        foreach ($options as $value => $label) {
            $value = (string)$value;
            $on = ($currentValue === $value) ? 'on' : '';
            $out .= $this->Html->tag('li', $label, array(
                'data-value' => $value,
                'class' => $this->_mergeClasses($on, ['filter-point', 'nav-item']),
            ));
        }
        return $this->Html->tag('ul', $out, $ulAttributes);
    }

    public function productsFilterDropdown($orderStatus)
    {
        $options = [
            ProductStatus::ENABLED => ProductStatus::getStatusName(ProductStatus::ENABLED),
            ProductStatus::INCOMPLETE => ProductStatus::getStatusName(ProductStatus::INCOMPLETE),
            ProductStatus::DISABLED => ProductStatus::getStatusName(ProductStatus::DISABLED),
        ];

        return $this->Form->input('product_status', [
            'type' => 'select',
            'id' => 'productStatusSearch',
            'class' => 'rows notranslate',
            'options' => $options,
            'default' => $orderStatus,
            'empty' => __('Select Status'),
            'data-live-search' => 'true',
            'onChange' => 'filterTable();',
            'data-width' => '195px',
            'label' => ['class' => 'visually-hidden'],
        ]);
    }

    public function filterDropdown($fieldName, array $options, array $attributes = []): string
    {
        return $this->Form->input($fieldName, array_merge([
            'type' => 'select',
            'empty' => __('All'),
            'options' => $options,
            'onChange' => 'filterTable();',
            'class' => 'rows notranslate',
            'between' => ' ',
            'label' => null,
            'div' => false,
        ], $attributes));
    }

    /**
     * Pagination select dropdown.
     *
     * @param int $noRecords Number of records displayed
     * @param int $count Total number of records
     * @param string $name Form input name
     * @param string $onChange JS event handler
     * @param bool $min_50 Make 50 the first option instead of 10
     * @param bool $dropAlign Add class 'dropAlign' to the element
     * @return string Form select input
     * @see FormHelper::input
     * @see FormHelper::select
     */
    public function noRecordsSelect($noRecords, $count, $name = 'noRecords', $onChange = null, $min_50 = false, $dropAlign = false, $idSuffix = null)
    {
        $selectOptions = array();
        $value = null;
        if (!$min_50) {
            $selectOptions['10'] = '10';
            if ($noRecords == 10) {
                $value = '10';
            }
        }
        if ($min_50 || $count > 10) {
            $selectOptions['50'] = '50';
            if ($noRecords == 50) {
                $value = '50';
            }
        }
        if ($count > 50) {
            $selectOptions['100'] = '100';
            if ($noRecords == 100) {
                $value = '100';
            }
        }
        if (in_array($count, array_keys($selectOptions))) {
            // Hack to keep options keys unique
            $count += 1;
        }
        $selectOptions[$count] = __('ALL');
        if ($noRecords == $count) {
            $value = $count;
        }

        return $this->Form->input('noRecords', array(
            'type' => 'select',
            'options' => $selectOptions,
            'value' => $value,
            'name' => $name,
            'id' => 'noRecords' . $idSuffix,
            'class' => $this->_mergeClasses('rows table-drop', $dropAlign ? 'dropAlign' : ''),
            'onChange' => $onChange ?: null,
            'div' => false,
            'data-width' => '80px',
            'label' => $dropAlign ? ['text' => __('Show'), 'style' => 'width: 50px;'] : __('Show'),
        ));
    }

    public function pagingControls($noRecords, $count, array $options = [])
    {
        $options += [
            'name' => 'noRecords',
            'onChange' => null,
            'min_50' => false,
            'showSyncBar' => false,
            'idSuffix' => '',
        ];

        $idSuffix = (string)$options['idSuffix'];

        $syncBar = '';
        if ($options['showSyncBar']) {
            $syncBar = <<<HTML
<div class="blue-btn">
	<span>Synchronizing</span>
	{$this->Html->image('/images/icons/synchronizing.gif', ['alt' => 'spinner', 'aria-hidden' => 'true'])}
</div>
HTML;
        }

        return <<<HTML
<div class="row">
	<div class="col-sm-2 col-12" style="text-align: center;">
		<div class="select_div drop-down" style="min-width: 125px;">
			{$this->noRecordsSelect($noRecords, $count, $options['name'], $options['onChange'], $options['min_50'], false, $idSuffix)}
		</div>
	</div>
	<div class="col-sm-8 col-12" style="text-align: center;">
		<div id="pagination{$idSuffix}" style="padding-top: 15px; min-width: 350px;"></div>
	</div>
	<div class="col-sm-2 col-12" style="text-align: center;">
		{$syncBar}
	</div>
</div>
HTML;
    }

    public function pagingControlsWithoutShow(): string
    {

        return <<<HTML
<div class="row">
    <div class="col-12" style="text-align: center;">
		<div id="pagination" style="padding-top: 15px; min-width: 350px;"></div>
	</div>
</div>
HTML;
    }
}
