<?php
App::uses('App<PERSON>elper', 'View/Helper');
App::uses('FulfillmentStatus', 'Utility');
App::uses('OrderPaymentMethod', 'Utility');
App::uses('OrderPaymentMethodSubtype', 'Utility');
App::uses('OrderPaymentStatus', 'Utility');
App::uses('OrderType', 'Utility');
App::uses('StripeCardBrand', 'Stripe.Enum');

/**
 * Orders Helper.
 *
 * @property HtmlHelper $Html
 * @property FormHelper $Form
 */
class OrdersHelper extends AppHelper
{
    public $helpers = [
        'Html',
        'Form',
    ];

    /**
     * @param string $label
     * @param string|null $value
     * @return string
     */
    public function orderHeaderInfo(string $label, ?string $value)
    {
        return <<<HTML
    <div><strong>{$label}: </strong> <span class="notranslate">{$value}</span></div>
HTML;
    }

    /**
     * Gets the payment method of the order.
     *
     * @param array $order
     * @return string
     */
    public function getPaymentMethod(array $order): string
    {
        if ($order['Order']['payment_method'] === OrderPaymentMethod::STRIPE) {
            $subtype = $order['Order']['payment_method_subtype'];
            $subtypeLabel = ($subtype) ? OrderPaymentMethodSubtype::getLabel($subtype) : null;

            if ($order['Order']['card_type'] && $order['Order']['last_four_digit']) {
                $cardLabel = trim(StripeCardBrand::getLabel($order['Order']['card_type']) . ' ' . str_pad($order['Order']['last_four_digit'], 4, '0', STR_PAD_LEFT));

                return ($subtype === OrderPaymentMethodSubtype::CARD_AFFIRM) ? trim($subtypeLabel . ' ' . $cardLabel) : $cardLabel;
            } elseif ($subtype) {
                return $subtypeLabel;
            }
        }

        return OrderPaymentMethod::getLabel((string)$order['Order']['payment_method']);
    }

    public function getRiskLevelHtml(string $riskLevel): string
    {
        $riskLevelCssClasses = [
            Order::RISK_LEVEL_NORMAL => 'text-success',
            Order::RISK_LEVEL_ELEVATED => 'text-warning',
            Order::RISK_LEVEL_HIGHEST => 'text-danger',
        ];

        return $this->Html->tag('span', Order::getRiskLevelName($riskLevel), [
            'class' => $riskLevelCssClasses[$riskLevel] ?? null,
        ]);
    }

    public function getFraudCheckResultHtml(string $fraudCheckResult): string
    {
        $riskLevelCssClasses = [
            Order::FRAUD_CHECK_PASS => 'text-success',
            Order::FRAUD_CHECK_FAIL => 'text-danger',
            Order::FRAUD_CHECK_UNAVAILABLE => 'text-warning',
            Order::FRAUD_CHECK_UNCHECKED => 'text-warning',
        ];

        return $this->Html->tag('span', Order::getFraudCheckResultName($fraudCheckResult), [
            'class' => $riskLevelCssClasses[$fraudCheckResult] ?? null,
        ]);
    }

    /**
     * Get billing full name of order.
     *
     * @param array $order
     * @return string
     */
    public function getBillingFullName($order)
    {
        return $order['Order']['customer_name'];
    }

    /**
     * Get billing CITY, STATE  of order.
     *
     * @param array $order
     * @return string
     */
    public function getBillingCityState($order)
    {
        if (empty($order['Order']['shipping_city'])) {
            return 'Failed to retrieve shipping_city';
        }
        if (empty($order['Order']['shipping_state'])) {
            return 'Failed to retrieve shipping_state';
        }

        return $order['Order']['shipping_city'] . ', ' . $order['Order']['shipping_state'];
    }

    /**
     * @param array $fulfillment
     * @return string HTML
     */
    public function getFulfillmentTableCaptionContent(array $fulfillment): string
    {
        $fulfillmentLabel = __('Fulfillment') . ' ' . $fulfillment['name'];

        return <<<HTML
<div class="row">
    <div class="col-6" style="text-align: left;">
        <div class="fulfillment-pill">
            {$fulfillmentLabel}
            <span class="fulfillment-pill__circle-filled"></span>
        </div>
        {$this->getFulfillmentTrackingDiv($fulfillment)}
    </div>
    <div class="col-6" style="text-align: right;">
        <span class="order-popup-warehouse">{$fulfillment['Warehouse']['name']}</span>
    </div>
</div>
HTML;
    }

    /**
     * @param array $fulfillment
     * @return string HTML
     */
    private function getFulfillmentTrackingDiv(array $fulfillment): string
    {
        $trackingLink = $this->getTrackingLink($fulfillment['tracking_url'], $fulfillment['tracking_number']);

        if (!$trackingLink) {
            return '';
        }

        return $this->Html->div(null, ($fulfillment['Courier']['name'] ?? __('Tracking')) . ': ' . $trackingLink);
    }

    /**
     * Returns a tracking link.
     *
     * @param string|null $trackingUrl
     * @param string|null $linkText
     * @return string HTML link to $trackingUrl if provided, $linkText as plain text otherwise.
     */
    public function getTrackingLink(?string $trackingUrl, ?string $linkText = null): string
    {
        $linkText = $linkText ?: $trackingUrl;

        if (!$linkText) {
            return '';
        }

        $link = $this->Html->tag('span', $linkText, ['escape' => true, 'class' => 'bs-font-family']);
        if ($trackingUrl) {
            $link = $this->Html->link($link, $trackingUrl, ['target' => '_blank', 'escape' => false, 'class' => 'no-of-retails']);
        }

        return $link;
    }

    public function canShowShipFromStoreTrackingButton(array $authUser, array $order): bool
    {
        return (
            $authUser['user_type'] === User::TYPE_RETAILER &&
            $order['Order']['order_type'] === OrderType::SHIP_FROM_STORE &&
            in_array($order['Order']['order_status'], OrderStatus::getShipFromStoreToConsumerStatuses(), true)
        );
    }

    public function canShowConsumerFulfillmentButton(array $authUser, array $order, array $warehouseOptions): bool
    {
        return (
            (
                in_array($authUser['user_type'], [User::TYPE_MANUFACTURER, User::TYPE_BRAND_STAFF], true)
                || $order['Order']['distributor_id'] == $authUser['id']
            ) &&
            count($warehouseOptions) > 0 &&
            (
                OrderType::filterOrderType($order['Order']['order_type']) === OrderType::SELL_DIRECT ||
                ($order['Order']['is_commission_retailer'] && $order['Order']['order_status'] === OrderStatus::PENDING)
            ) &&
            array_sum(array_column($order['OrderProduct'], 'remaining_quantity')) > 0
        );
    }

    public function canShowDealerFulfillmentButton(array $authUser, array $order, array $warehouseOptions): bool
    {
        return (
            (
                in_array($authUser['user_type'], [User::TYPE_MANUFACTURER, User::TYPE_BRAND_STAFF], true)
                || $order['Order']['distributor_id'] == $authUser['id']
            ) &&
            count($warehouseOptions) > 0 &&
            !empty($order['DealerOrder']['id']) &&
            array_sum(array_column($order['DealerOrder']['DealerOrderProduct'], 'remaining_quantity')) > 0
        );
    }

    public function canShowVerificationCodeButtons(array $authUser, array $order, bool $userHasEditPermission): bool
    {
        return (
            $userHasEditPermission &&
            in_array($authUser['user_type'], User::TYPES_RETAILER) &&
            $this->canMarkOrderAsDelivered($order)
        );
    }

    public function canShowMarkAsDeliveredButton(array $authUser, array $order, bool $userHasEditPermission): bool
    {
        return (
            $userHasEditPermission &&
            in_array($authUser['user_type'], User::TYPES_BRAND) &&
            $this->canMarkOrderAsDelivered($order)
        );
    }

    protected function canMarkOrderAsDelivered(array $order): bool
    {
        return (
            $this->request->param('filter') !== 'dealerorderTable' &&
            in_array($order['Order']['order_type'], [OrderType::IN_STORE_PICKUP, OrderType::LOCAL_DELIVERY]) &&
            in_array($order['Order']['order_status'], [OrderStatus::NOT_PICKED_UP, OrderStatus::READY_FOR_DELIVERY])
        );
    }

    public function isCancellablePayment(array $order): bool
    {
        return $this->canCapturePayment($order);
    }

    public function canCapturePayment(array $order): bool
    {
        return (
            in_array($this->_authUser('user_type'), User::TYPES_BRAND, true)
            && $order['Order']['payment_status'] != OrderPaymentStatus::PAID
            && !in_array($order['Order']['order_status'], OrderStatus::STATUSES_REFUNDED, true)
            && (
                $order['Order']['is_commission_retailer']
                || OrderType::filterOrderType($order['Order']['order_type']) === OrderType::SELL_DIRECT
            )
        );
    }

    public function canShowOrderRefundButton(array $order, $totalRefundValue, $totalOrderValue): bool
    {
        $userType = $this->_authUser('user_type');
        $orderType = OrderType::filterOrderType($order['Order']['order_type']);
        $orderStatus = $order['Order']['order_status'];
        $refundPeriod = SHIPEARLY_RETAILER_REFUND_PERIOD;

        return (
            (
                $userType === User::TYPE_MANUFACTURER ||
                (
                    $userType === User::TYPE_RETAILER &&
                    !$order['Order']['is_commission_retailer'] &&
                    in_array($orderStatus, [OrderStatus::DELIVERED, OrderStatus::OPEN], true)
                )
            || (
                $userType === User::TYPE_SALES_REP &&
                $order['Order']['distributor_id'] == $this->_authUser('id') &&
                (
                    $order['Order']['is_commission_retailer'] ||
                    $orderType === OrderType::SELL_DIRECT
                )
            )
            ) &&
            $orderType !== OrderType::WHOLESALE &&
            !empty($order['Order']['transactionID']) &&
            strtotime($refundPeriod . ' AGO') < strtotime($order['Order']['created_at']) &&
            round($totalRefundValue, 2) < round($totalOrderValue, 2)
        );
    }

    public function isCancellableDealerPayment(array $order): bool
    {
        return (
            in_array($this->_authUser('user_type'), User::TYPES_BRAND, true)
            && !empty($order['DealerOrder']['id'])
            && $order['Order']['payment_method'] === OrderPaymentMethod::STRIPE
            && $order['Order']['payment_status'] != OrderPaymentStatus::PAID
            && !in_array($order['Order']['order_status'], OrderStatus::STATUSES_REFUNDED, true)
        );
    }

    public function canShowDealerOrderRefundButton(array $order, $totalRefundValue, $totalOrderValue): bool
    {
        return (
            $this->_authUser('user_type') === User::TYPE_MANUFACTURER &&
            !empty($order['DealerOrder']['id']) &&
            round($totalRefundValue, 2) < round($totalOrderValue, 2)
        );
    }

    public function addProductsInput($orderId)
    {
        return $this->_View->element('Orders/order_products_table/add_products_input', ['orderId' => $orderId]);
    }

    public function orderStatusFilterSelect(array $options)
    {
        $options = array_map('__', $options);
        return $this->Form->input('order_status', [
            'type' => 'select',
            'id' => 'order_status',
            'class' => 'rows',
            'onChange' => 'filterTable();',
            'options' => $options,
            'multiple' => true,
            'data-placeholder' => __('Order Status'),
            'data-width' => '195px',
            'div' => ['class' => 'col-auto'],
        ]);
    }

    public function fulfillmentStatusFilterSelect(array $options)
    {
        return $this->Form->input('fulfillment_status', [
            'type' => 'select',
            'id' => 'fulfillment_status',
            'class' => 'rows',
            'onChange' => 'filterTable();',
            'options' => $options,
            'multiple' => true,
            'data-placeholder' => __('Fulfillment Status'),
            'data-width' => '195px',
            'div' => ['class' => 'col-auto'],
        ]);
    }

    public function paymentStatusFilterSelect(array $options)
    {
        return $this->Form->input('payment_status', [
            'type' => 'select',
            'id' => 'payment_status',
            'class' => 'rows',
            'onChange' => 'filterTable();',
            'options' => $options,
            'multiple' => true,
            'data-placeholder' => __('Payment Status'),
            'data-width' => '195px',
            'div' => ['class' => 'col-auto'],
        ]);
    }

    public function paymentTypeFilterSelect(array $options)
    {
        return $this->Form->input('payment_method', [
            'type' => 'select',
            'id' => 'payment_method',
            'class' => 'rows',
            'onChange' => 'filterTable();',
            'options' => $options,
            'multiple' => true,
            'data-placeholder' => __('Payment Type'),
            'data-width' => '195px',
            'div' => ['class' => 'col-auto'],
        ]);
    }

    public function displayFulfillmentStatus($fulfillmentStatus) {
        $pillClass = 'fulfillment-pill';
        $circleClass = 'fulfillment-pill__circle-filled';

        if ($fulfillmentStatus === 'unfulfilled' || $fulfillmentStatus === null ) {
            $pillClass .= '-unfulfilled';
            $circleClass = 'fulfillment-pill__circle';
        } elseif ($fulfillmentStatus === 'partially_fulfilled') {
            $pillClass .= '-partialfulfilled';
            $circleClass = 'fulfillment-pill__circle-half-filled';
        }

        $statusLabel = FulfillmentStatus::getLabel($fulfillmentStatus ?: FulfillmentStatus::UNFULFILLED);

        $output = '<span class="' . $pillClass . '">';
        $output .= $statusLabel;
        $output .= '<span class="' . $circleClass . '"></span></span>';

        return $output;
    }

    public function displayCompanyName(string $brandName, string $retailerName, string $userType, ?int $retailerId = null): string
    {
        if (in_array($userType, User::TYPES_BRAND, true)) {
            if (!empty($retailerName) && $retailerId !== null) {
                $url = Router::url([
                    'controller' => 'users',
                    'action' => 'contact',
                    'id' => $retailerId
                ]);
                return $this->Html->link($retailerName, $url, ['class' => 'notranslate']);
            } else {
                return $this->Html->tag('span', h($brandName), ['class' => 'notranslate']);
            }
        }

        return $this->Html->tag('span', h($brandName), ['class' => 'notranslate']);
    }
}
