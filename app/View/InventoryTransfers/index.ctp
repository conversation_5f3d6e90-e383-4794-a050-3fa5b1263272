<?php
/**
 * @var AppView $this
 * @var int $limit
 * @var int $page
 * @var int $count
 * @var array $records
 * @var array $warehouseOptions
 */

if (!$this->request->is('ajax')) {
    $this->extend('/Common/index');
}

// Wrap inputs for parent view blocks with a similar form context
$this->Form->create(false, ['type' => 'GET', 'inputDefaults' => ['label' => false, 'div' => false]]);

$this->assign('title', 'Transfers');

$this->assign('header_buttons', implode(' ', [
    $this->InventoryTransfers->addButton(),
]));

$this->assign('tabs', implode(' ', [
    $this->Form->hidden('status'),
    $this->InventoryTransfers->statusTabs((string)$this->request->data('status')),
]));

$this->assign('filters', implode(' ', [
    $this->Layout->filterDropdown('destination', $warehouseOptions),
]));

$this->Form->end();
?>
<div class="table-responsive">
    <table class="table table-striped table-hover">
        <thead><?= $this->InventoryTransfers->indexTableHeaders() ?></thead>
        <tbody><?= $this->InventoryTransfers->indexTableCells($records) ?></tbody>
    </table>
</div>
<?= $this->Layout->pagingControls($limit, $count, [
    'name' => 'limit',
    'onChange' => 'filterTable();',
    'min_50' => true,
]) ?>
<?php $this->start('script'); ?>
<?php if (!$this->request->is('ajax')) { ?>
    <script>
    $(function() {
        $('#order_frm').on('click', '.js-edit', function(e) {
            e.preventDefault();
            var $this = $(this);
            $.get($this.attr('href'), function(data) {
                bootbox.dialog({
                    title: $this.data('title') || '<!-- Empty Title -->',
                    message: data,
                    className: 'modal--view bootbox--form'
                });
            });
        });
    });
    </script>
<?php } else { ?>
    <script>
    $(function() {
        if( /Android|webOS|iPhone|iPad|iPod|BlackBerry/i.test(navigator.userAgent) ) {
            $('#noRecords').selectpicker('mobile');
        } else {
            $('#noRecords').selectpicker();
        }
    });
    </script>
<?php } ?>
<script>
$(function() {
    if (!!(window.history && history.replaceState)) {
        history.replaceState(null, null, "<?= $this->request->here() ?>");
    }

    $('#pagination').pagination({
        items: <?= $count ?>,
        itemsOnPage: <?= $limit ?>,
        currentPage: <?= $page ?>,
        cssStyle: 'light-theme',
        onPageClick: function(pageNumber) {
            $('#pageNumber').val(pageNumber);
            updateTable();
        },
        onInit: window.paginationInit,
    });
});
</script>
<?php $this->end(); ?>
