<?php

/**
 * @var AppView $this
 * @var string $lang
 * @var array $order
 * @var array $orderProducts
 * @var array $user
 * @var string $billToAddress
 * @var string $shipToAddress
 * @var string $fromAddress
 * @var string $termsAndConditions
 * @var string $vatNumber
 * @var array $creditTerm
 */

$this->layout = '';
$this->assign('title', $this->request->params['file']);

$currencyCode = (string)$order['currency_code'];
?>
<!DOCTYPE html>
<html lang="<?= $lang ?>">
<head>
    <title><?= $this->fetch('title') . ' - ' . SITE_NAME ?></title>
    <?= $this->Html->charset() . PHP_EOL ?>
    <?php echo $this->Html->css([
        'Pdf/invoice.css',
    ], ['plugin' => false, 'once' => true]); ?>

</head>
<body>
    <div id="order-invoice" class="container container-fluid">
        <div class="row header-row">
            <div class="col-xs-2">
                <h1><small><?= h($order['orderID']) ?></small></h1>
            </div>
            <div class="col-xs-10 text-right">
                <?php if ($user['avatar']) {
                    echo $this->Html->image($user['avatar'], [
                        'pathPrefix' => 'files/users/',
                        'alt' => h($user['company_name']) . ' Logo',
                        'style' => 'display: inline; max-height: 100px; max-width: 100%;',
                    ]);
                } ?>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-8 address-block">
                <address><?= $fromAddress ?></address>
            </div>
            <div class="col-xs-4 text-center">
                <?php
                $orderHeaderInfo = [];
                $orderHeaderInfo[] = ['label' => __('Order Date'), 'value' => date(DATE_FORMAT, strtotime($order['created_at']))];
                $shipDate = (string)($order['shipped_date'] ?? $order['requested_b2b_ship_date'] ?? '');
                if (!empty($shipDate)) {
                    $orderHeaderInfo[] = ['label' => __('Ship Date'), 'value' => date(DATE_FORMAT, strtotime($shipDate))];
                }
                if (!empty($order['purchase_order_number'])) {
                    $orderHeaderInfo[] = ['label' => __('Purchase Order No'), 'value' => $order['purchase_order_number']];
                }
                if (!empty($order['external_invoice_id'])) {
                    $orderHeaderInfo[] = ['label' => __('Internal Order #'), 'value' => $order['external_invoice_id']];
                }
                if (!empty($vatNumber)) {
                    $orderHeaderInfo[] = ['label' => __('VAT ID #'), 'value' => $vatNumber];
                }
                foreach ($orderHeaderInfo as $header) {
                    echo $this->Orders->orderHeaderInfo($header['label'], (string)h($header['value'])) . '<hr />';
                }
                ?>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-6 address-block">
                <strong><?= __('Bill To') ?>:</strong>
                <hr/>
                <address><?= $billToAddress ?></address>
            </div>
            <div class="col-xs-6 address-block">
                <strong><?= __('Ship To') ?>:</strong>
                <hr/>
                <address><?= $shipToAddress ?></address>
            </div>
        </div>
    <?php if ($order['notes']) { ?>
        <div class="row">
            <strong class="col-xs-2"><?= __('Notes') ?>:</strong>
            <p class="col-xs-10"><?= $order['notes'] ?></p>
        </div>
    <?php } ?>
        <table class="invoice-items table table-condensed table-striped">
            <?php
            $formatAmount = function($amount) use ($currencyCode): string {
                /** @var AppView $this */
                return (string)h($this->Currency->formatAmount($amount, $currencyCode, true));
            };
            $tableColumnModel = [
                [
                    'label' => __('SKU'),
                    'valueCallback' => fn(array $product): string => (string)h($product['Product']['product_sku']),
                ],
                [
                    'label' => __('Item Description'),
                    'valueCallback' => fn(array $product): string => (string)h($product['Product']['product_title']),
                    'refundCallback' => fn(array $product): string => sprintf(
                        '<span class="invoice-item-refunded-title">%s</span>',
                        __('Refund') . ': ' . h($product['Product']['product_title'])
                    ),
                ],
                [
                    'label' => __('Barcode'),
                    'valueCallback' => fn(array $product): string => (string)h($product['Product']['product_upc']),
                ],
                [
                    'label' => __('Unit Price'),
                    'valueCallback' => fn(array $product): string => $formatAmount($product['total_price'] / $product['quantity']),
                ],
                [
                    'label' => __('Quantity'),
                    'valueCallback' => fn(array $product): string => (string)h($product['quantity']),
                    'refundCallback' => fn(array $product): string => (string)h($product['refundedQuantity']),
                ],
                [
                    'label' => __('Total Price'),
                    'valueCallback' => fn(array $product): string => $formatAmount($product['total_price']),
                    'refundCallback' => fn(array $product): string => $formatAmount(-$product['refundedQuantity'] * ($product['total_price'] / $product['quantity'])),
                ],
            ];
            ?>
            <thead>
                <tr>
                <?php foreach ($tableColumnModel as $column) { ?>
                    <th><?= $column['label'] ?></th>
                <?php } ?>
                </tr>
            </thead>
            <tbody>
            <?php foreach ($orderProducts as $product) { ?>
                <tr>
                <?php foreach ($tableColumnModel as $column) { ?>
                    <td><?= call_user_func($column['valueCallback'], $product) ?></td>
                <?php } ?>
                </tr>
                <?php if ($product['refundedQuantity'] > 0) { ?>
                    <tr class="invoice-item-refunded">
                    <?php foreach ($tableColumnModel as $column) { ?>
                        <td><?= isset($column['refundCallback']) ? call_user_func($column['refundCallback'], $product) : '<!-- empty -->' ?></td>
                    <?php } ?>
                    </tr>
                <?php } ?>
            <?php } ?>
            </tbody>
            <tfoot>
            <?php
            $footers = [];
            $footers[] = ['label' => __('Subtotal'), 'value' => $formatAmount($order['subtotal'])];
            if ($order['total_discount'] > 0) {
                $footers[] = ['label' => __('Discount'), 'value' => $formatAmount(-$order['total_discount'])];
            }
            $footers[] = ['label' => __('Shipping'), 'value' => $formatAmount($order['shipping_amount'])];
            $footers[] = ['label' => __('Tax') . ($order['tax_included'] ? ' (Inc)' : ''), 'value' => $formatAmount($order['total_tax'])];
            if ($order['total_refund'] > 0) {
                $footers[] = ['label' => __('Refund'), 'value' => $formatAmount(-$order['total_refund'])];
            }
            $footers[] = ['label' => __('Total'), 'value' => $formatAmount($order['total_price'])];
            ?>
            <?php foreach ($footers as $footer) { ?>
                <tr>
                    <td colspan="<?= count($tableColumnModel) - 1 ?>"><?= $footer['label'] ?></td>
                    <td><?= $footer['value'] ?></td>
                </tr>
            <?php } ?>
            </tfoot>
        </table>
        <div class="text-right">
            <strong><?= __('Payment Method') ?>:</strong>
            <span><?= h(
                ($order['payment_method'] === OrderPaymentMethod::CREDIT && $creditTerm['description'])
                    ? $creditTerm['description']
                    : $this->Orders->getPaymentMethod(['Order' => $order])
            ) ?></span>
        </div>
        <?php if ($order['payment_method'] !== OrderPaymentMethod::STRIPE && !empty($termsAndConditions)): ?>
            <div class="text-right">
                <strong><?= __('Credit Terms & Conditions') ?>:</strong>
                <span><?= h($termsAndConditions) ?></span>
            </div>
        <?php endif; ?>
    </div>
</body>
</html>
