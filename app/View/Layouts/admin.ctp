<?php
/**
 * @var AppView $this
 * @var string $lang
 */

$this->append('css', $this->element('Layouts/admin/styles'));
$this->append('sidebar', $this->element('Layouts/admin/sidebar'));
$this->append('head_script', $this->element('Layouts/validate-messages'));
$this->append('script', $this->element('Layouts/admin/scripts'));
?>
<!DOCTYPE html>
<html lang="<?= $lang ?>">
<head>
	<?php echo $this->Html->charset() . PHP_EOL; ?>
	<?php echo implode(PHP_EOL . "\t", [
		$this->Html->meta(['name' => 'viewport', 'content' => 'width=device-width, initial-scale=1.0']),
	]) . PHP_EOL; ?>

	<title><?php echo $this->fetch('title') . ' - ' . SITE_NAME; ?></title>
	<?php echo $this->Html->meta('icon', ($subdomain_settings['favicon_url'] ?? '') ?: BASE_PATH . 'images/icons/favicon.jpg') . PHP_EOL; ?>

	<?php echo implode(PHP_EOL . "\t", [
		$this->Html->meta('description', META_DESCRIPTION),
		$this->Html->meta('keywords', META_KEYWORDS),
		$this->Html->meta(['name' => 'author', 'content' => '']),
	]) . PHP_EOL; ?>

	<!--[if lt IE 10]>
		<script type="text/javascript" language="javascript" src="js/PIE.js"></script>
		<script type="text/javascript" src="js/roundedcorner.js"></script>
	<![endif]-->
	<?php echo $this->Html->css([
		'dist/tailwind.min.css',
		'style.css',
		'latest.css',
		'admin_bootstrap.css',
		'admin_bootstrap-responsive.css',
		'font-awesome/css/all.min.css',
		'jquery-ui.min.css',
		'jqGrid.min.css',
		'admin_style.css',
		'dev.css',
		'jquery.comiseo.daterangepicker.css',
	], ['plugin' => false, 'once' => true]); ?>
	<?php echo $this->Html->script([
		'jquery.min.js',
		'jquery.validate.js',
		'bootstrap-modal.js',
		'bootstrap-transition.min.js',
		'bootstrap-collapse.js',
		'grid.min.js',
		'jqGrid.min.js',
		'bootbox.min.js',
		'//code.jquery.com/ui/1.10.4/jquery-ui.js',
		'moment.js',
		'jquery.comiseo.daterangepicker.js',
		'jquery.shipearly.js',
	], ['plugin' => false, 'once' => true]); ?>

<?php
	echo $this->fetch('meta');
	echo $this->fetch('css');
	echo $this->fetch('head_script');
?>

</head>
	<body>
		<div class="wrapper">
			<div class="navbar navbar-static-top">
				<div class="navbar-inner">
					<a class="brand" href="<?= ADMIN_PATH ?>">
						<img src="<?= BASE_PATH . 'images/ShipEarly_Logo.png' ?>" alt="ShipEarly" />
					</a>
					<ul class="nav pull-right">
						<li><span class="log_user">Logged in as Admin</span></li>
						<li><a href="<?= ADMIN_PATH . 'users/logout' ?>" class="logout">Logout<i class="fas fa-sign-out-alt"></i></a></li>
					</ul>
				</div>
			</div><!-- /.navbar -->
			<div class="container-fluid">
				<div class="row-fluid">
					<div class="span3 sidebar sidebar_drop">
						<?php echo $this->fetch('sidebar'); ?>
					</div><!-- /.sidebar -->
					<div class="span9 page_content table_page manage_admin">
						<?php echo $this->Flash->render(); ?>
						<div class="box-text">
							<?php echo $this->element('display_message'); ?>
						</div>
						<?php echo $this->fetch('content'); ?>
					</div><!-- /.page_content -->
				</div>
			</div><!-- /.container-fluid -->
		</div><!-- /.wrapper -->
		<footer>
			<p class="fL"></p>
			<p class="" style="text-align: center;">&copy; <?php echo SITE_NAME; ?>. All Rights Reserved.</p> <!-- fR -->
		</footer>
		<?php echo $this->fetch('script'); ?>

		<?php echo $this->element('sql_dump'); ?>
	</body>
</html>
