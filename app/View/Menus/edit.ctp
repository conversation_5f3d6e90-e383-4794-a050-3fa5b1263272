<?php

use ShipEarlyApp\Lib\Utility\SupportedLanguages;

/**
 * @var AppView $this
 * @var array $menu
 * @var string $lang
 */
?>

<?php
$defaultLocale = SupportedLanguages::toLocale($lang) ?: SupportedLanguages::DEFAULT_LOCALE;
$selectedLocale = $this->request->query('lang') ?: $defaultLocale;
$languageByLocale = SupportedLanguages::getOptionsByLocale();
?>

<style>
    #menu-name-edit-icon:hover .fa-pen {
        color: #000 !important;
    }
    .menu-item:hover {
        border-color: var(--accent-color) !important;
        box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    }
    #menu-name-display:hover {
        color: #000 !important;
    }
    @media (min-width: 767px) and (max-width: 1024px) {
        #menu-name-display {
            margin-bottom: 0.7rem;
        }
    }
    @media (max-width: 767px) {
        #menu-name-display {
            margin-bottom: 0.5rem;
        }
    }
    @media (min-width: 1025px) {
        #menu-name-display {
            margin-bottom: 0;
        }
    }
</style>

<?php echo $this->Form->create('Menu'); ?>
<?php echo $this->Form->input('id', ['type' => 'hidden']); ?>
<div class="container-fluid">
    <div class="mx-auto" style="max-width: 1000px;">
        <div class="mx-auto row align-items-center mt-2">
            <div class="col-12 d-flex flex-wrap justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <a href="<?= $this->Url->build(['controller' => 'menus', 'action' => 'index']) ?>"
                    class="me-3 text-muted text-decoration-none d-flex align-items-center"
                    title="Back to menus">
                        <i class="fa-solid fa-chevron-left fs-5"></i>
                    </a>

                    <h1 class="content-head h4" id="menu-name-display" style="cursor: pointer;">
                        <?= h($menu['Menu']['name']) ?>
                    </h1>

                    <span id="menu-name-edit-icon" class="ms-2 d-inline-flex align-items-center">
                        <i class="fa-solid fa-pen fs-6 text-muted"></i>
                    </span>

                    <input type="text"
                        id="menu-name-input"
                        class="form-control form-control-sm ms-2"
                        style="display: none; max-width: 400px;"
                        value="<?= h($menu['Menu']['name']) ?>"
                        data-id="<?= $menu['Menu']['id'] ?>" />
                </div>
                <div class="d-flex align-items-center">
                    <div class="ms-1">
                        <?= $this->Form->input('locale', [
                            'id' => 'locale',
                            'type' => 'select',
                            'default' => $selectedLocale,
                            'options' => $languageByLocale,
                            'label' => false,
                            'class' => 'form-select',
                            'style' => 'width: auto; height: 33px; margin-left: 10px;',
                        ]); ?>
                    </div>
                </div>
            </div>
        </div>
        <div class="card mt-2 mx-auto" style="max-width: 1000px; width: 100%;">
            <div class="card-body">
                <h1 class="content-head" style="font-size: 20px;"><?= __('Menu Items') ?></h1>
                <div id="menu-items-list" class="d-flex flex-column gap-2 mt-2">
                    <?php foreach ($menu['MenuItem'] as $index => $item): ?>
                        <?= $this->Form->hidden("MenuItem.{$index}.id", ['value' => $item['id']]); ?>
                        <div class="menu-item d-flex align-items-center bg-white border rounded px-3 py-2" data-id="<?= $item['id'] ?>" style="cursor: grab;">
                            <span class="me-3 text-muted fs-5"><i class="fa-solid fa-grip-vertical"></i></span>
                            <div class="flex-grow-1 menu-item-label-view" data-id="<?= $item['id'] ?>" style="cursor:pointer;">
                                <?= h($item['label']) ?>
                            </div>
                            <div class="flex-grow-1 menu-item-edit d-none w-100" data-id="<?= $item['id'] ?>">
                                <div class="row g-2">
                                    <div class="col-md-6">
                                        <?= $this->Form->control("MenuItem.{$index}.label", [
                                            'label' => false,
                                            'value' => $item['label'],
                                            'class' => 'form-control form-control-sm menu-inline-label',
                                            'data-id' => $item['id'],
                                            'data-type' => 'label',
                                            'placeholder' => __('Label'),
                                        ]) ?>
                                    </div>
                                    <div class="col-md-6">
                                        <?= $this->Form->control("MenuItem.{$index}.url", [
                                            'label' => false,
                                            'value' => $item['url'],
                                            'class' => 'form-control form-control-sm menu-inline-url',
                                            'data-id' => $item['id'],
                                            'data-type' => 'url',
                                            'placeholder' => __('URL'),
                                        ]) ?>
                                    </div>
                                </div>
                            </div>
                            <button type="button" class="btn btn-sm btn-link ms-2 remove-item" style="color:#5E5E5E;" data-id="<?= h($item['id']) ?>">
                                <i class="fa-solid fa-trash-can"></i>
                            </button>
                        </div>
                    <?php endforeach; ?>
                </div>
                <div class="card mt-3 border rounded px-2 py-2 bg-white">
                    <div class="d-flex justify-content-start align-items-center">
                        <button type="button" class="btn p-0 text-primary" id="add-menu-item">
                            <i class="fa fa-plus me-1"></i><?= __('Add menu item') ?>
                        </button>
                        <button type="submit" class="btn btn-primary ms-2" id="update-button-container" style="display: none;">
                            <?= __('Save') ?>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<input type="hidden" name="data[Menu][name]" id="menu-name-hidden" value="<?= h($menu['Menu']['name']) ?>" />
<?php echo $this->Form->end(); ?>

<script>
const menuItemTranslations = <?= json_encode($menu['MenuItem']) ?>;

$(document).ready(function () {
    const nameDisplay = $('#menu-name-display');
    const nameInput = $('#menu-name-input');
    const nameHidden = $('#menu-name-hidden');

    nameDisplay.on('click', function () {
        $('#menu-name-edit-icon').attr('style', 'display: none !important');
        nameInput.val(nameDisplay.text().trim());
        nameDisplay.hide();
        nameInput.show().focus();
    });

    nameInput.on('blur', function () {
        const newName = nameInput.val().trim();
        const menuId = nameInput.data('id');
        const selectedLocale = $('#locale').val();

        if (newName !== '') {
            nameHidden.val(newName);
            $.ajax({
                type: 'POST',
                url: "<?= $this->Url->build(['controller' => 'menus', 'action' => 'updateName']) ?>",
                data: {
                    id: menuId,
                    name: newName,
                    locale: selectedLocale,
                },
                dataType: 'json',
                success: function (response) {
                    if (response.success) {
                        nameDisplay.text(newName);
                        window.location.reload();
                    } else {
                        alert(response.message || 'Failed to update name.');
                    }
                },
                error: function () {
                    alert('Error saving name.');
                }
            });
        }

        nameInput.hide();
        nameDisplay.show();
    });

    nameInput.on('keypress', function (e) {
        if (e.which === 13) {
            nameInput.blur();
        }
    });

    const menuTranslations = <?= json_encode($menu['Menu']) ?>;
    const removedItems = [];

    $(document).on('focus', '.menu-url-input', function () {
        const $input = $(this);
        if ($input.val() === '') {
            $input.val($input.data('original'));
        }
    });

    $('#locale').on('change', function () {
        const selectedLocale = $(this).val();
        const translation = menuTranslations[selectedLocale];
        if (translation && translation.name) {
            $('#menu-name-display').text(translation.name);
            $('#menu-name-input').val(translation.name);
            $('#menu-name-hidden').val(translation.name);
        }
        menuItemTranslations.forEach((item, index) => {
            const translation = item[selectedLocale];
            if (translation) {
                $(`.menu-item-label-view[data-id="${item.id}"]`).text(translation.label);
                $(`input[name="data[MenuItem][${index}][label]"]`).val(translation.label);
                $(`input[name="data[MenuItem][${index}][url]"]`).val(translation.url);
            }
        });
    });

    $(document).on('click', '.remove-item', function () {
        const group = $(this).closest('.menu-item-group');
        const itemId = $(this).data('id');
        if (itemId) {
            if (!confirm('Are you sure you want to delete this item?')) return;
            $.post('<?= $this->Url->build(['controller' => 'menus', 'action' => 'delete_menu_item']) ?>/' + itemId, {}, function (response) {
                if (response.success) {
                    group.remove();
                    window.location.reload();
                } else {
                    alert('Failed to delete the item.');
                }
            }, 'json').fail(function () {
                alert('AJAX request failed.');
            });
        } else {
            group.remove();
        }
    });

    $(document).on('click', '.menu-item-label-view', function () {
        const id = $(this).data('id');
        $(`.menu-item-label-view[data-id="${id}"]`).addClass('d-none');
        $(`.menu-item-edit[data-id="${id}"]`).removeClass('d-none').find('input:first').focus();
    });

    $(document).on('blur', '.menu-inline-label, .menu-inline-url', function () {
        const $input = $(this);
        const id = $input.data('id');
        const field = $input.data('type');
        const value = $input.val().trim();
        const selectedLocale = $('#locale').val();

        if (!id || !field) return;

        $.ajax({
            type: 'POST',
            url: '<?= $this->Url->build(['controller' => 'menus', 'action' => 'update_menu_item_field']) ?>',
            data: { id, field, value, selectedLocale },
            dataType: 'json',
            success: function (res) {
                if (!res.success) {
                    alert('Failed to save.');
                }
            },
            error: function () {
                alert('Error occurred while saving.');
            }
        });
    });

    new Sortable(document.getElementById('menu-items-list'), {
        animation: 150,
        handle: '.menu-item',
        onEnd: function () {
            let itemOrder = [];
            $('#menu-items-list .menu-item').each(function () {
                itemOrder.push($(this).data('id'));
            });

            $.ajax({
                type: 'POST',
                url: "<?= $this->Url->build(['controller' => 'menus', 'action' => 'reorder_menu_items']) ?>",
                data: {
                    order: itemOrder,
                    menu_id: <?= (int)$menu['Menu']['id'] ?>
                },
                dataType: 'json',
                success: function (response) {
                    if (!response.success) {
                        alert(response.message || 'Failed to update item order.');
                    }
                },
                error: function () {
                    alert('Error occurred while saving item order.');
                }
            });
        }
    });
    let itemIndex = <?= count($menu['MenuItem']) ?>;

    $('#add-menu-item').on('click', function () {
        const container = $('#menu-items-list');
        const labelPlaceholder = "<?= h(__('Label')) ?>";
        const urlPlaceholder = "<?= h(__('URL')) ?>";

        const newItem = $(`
            <div class="menu-item d-flex align-items-center bg-white border rounded px-3 py-2" style="cursor: grab;">
                <span class="me-3 text-muted fs-5">⋮⋮</span>
                <div class="flex-grow-1 menu-item-edit w-100">
                    <div class="row g-2">
                        <div class="col-md-6">
                            <input type="text" name="data[MenuItem][${itemIndex}][label]" class="form-control form-control-sm" placeholder="${labelPlaceholder}" />
                        </div>
                        <div class="col-md-6">
                            <input type="text" name="data[MenuItem][${itemIndex}][url]" class="form-control form-control-sm" placeholder="${urlPlaceholder}" />
                        </div>
                    </div>
                </div>
                <button type="button" class="btn btn-sm btn-link ms-2 remove-item" style="color:#5E5E5E;">
                    <i class="fa-solid fa-trash-can"></i>
                </button>
            </div>
        `);

        container.append(newItem);
        itemIndex++;

        $('#update-button-container').show();
    });
});
</script>
