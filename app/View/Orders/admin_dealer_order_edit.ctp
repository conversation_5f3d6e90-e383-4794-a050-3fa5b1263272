<?php
/**
 * @var AppView $this
 * @var array $dealerOrder
 */
?>
<?php echo $this->Form->create('Order', array(
	'id' => 'AdminDealerOrderEditForm',
	'inputDefaults' => array('label' => false, 'div' => false),
)); ?>
<table class="order-popup-products-table">
	<tbody class="js-added-products">
		<?php foreach ($dealerOrder['DealerOrderProduct'] as $item) {
			echo $item['Warehouse']['name'] ? "<th>{$item['Warehouse']['name']}</th>" : '';
			echo $this->element('Orders/order_products_table/processing_order_product', [
				'pid' => $item['product_id'],
				'value' => array_merge($item, [
					'quantity' => $item['product_quantity'],
					'dealer_price' => $item['product_price'],
					'line_total' => $item['product_quantity'] * $item['product_price'],
				]),
				'currency_code' => $dealerOrder['Order']['currency_code'],
				'removable' => false,
				'enable_price_edit' => true,
				'enable_quantity_edit' => true,
			]);
		} ?>
	</tbody>
	<tfoot>
		<tr>
			<th>Shipping</th>
			<td><?php echo $this->Form->input('Order.dealer_shipping_amount', array(
					'type' => 'text',
					'id' => 'dealerShippingAmount',
					'class' => 'order-popup-textbox',
					'default' => number_format($dealerOrder['DealerOrder']['shipping_amount'], 2, '.', ''),
					'error' => array('attributes' => array('class' => 'help-block')),
				)); ?>
			</td>
		</tr>
	</tfoot>
</table>
<?php echo $this->Form->end(); ?>
<script type="text/javascript">
$(function() {
	$('.js-dealer-quantity').attr('min', 1);
});
</script>
