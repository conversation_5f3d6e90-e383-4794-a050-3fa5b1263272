<?php
/**
 * @var AppView $this
 * @var array $CourierList
 */
?>
<?php echo $this->Form->create('Order', array(
	'id' => 'newdealershipmenttracking',
	'url' => BASE_PATH . 'dealerordershipmenttracking',
	'default' => !$this->request->data['Order']['is_dealerorder'],
	'inputDefaults' => array(
		'label' => false,
		'div' => false,
		'error' => array('attributes' => array('class' => 'help-block'))
	)
)); ?>
	<?php echo $this->Form->hidden('id', array('id' => 'OrderID')); ?>
	<div class="form-group">
		<label>Courier</label>
		<div class="controls courier">
			<?php echo $this->Form->input('courier', array(
				'type' => 'select',
				'empty' => 'Select Couriers',
				'options' => $CourierList,
				'data-live-search' => "true",
				'id' => 'courier',
				'class' => 'select-input selectpicker picker',
			)); ?>
			<span class="help-block"></span>
		</div>
	</div>
	<div class="form-group">
		<label>Tracking No</label>
		<div class="controls">
			<?php echo $this->Form->input('trackingno', array(
				'type' => 'text',
				'id' => 'trackingno',
				'class' => 'pop-text',
			)); ?>
			<span class="help-block"></span>
		</div>
	</div>
	<div style="text-align: right;">
		<button type="submit" id="dealer_update_order" class="btn btn-primary">Update</button>
	</div>
<?php echo $this->Form->end(); ?>
<script type="text/javascript">
$(function() {
	if( /Android|webOS|iPhone|iPad|iPod|BlackBerry/i.test(navigator.userAgent) ) {
		$('#newdealershipmenttracking .selectpicker').selectpicker('mobile');
	} else {
		$('#newdealershipmenttracking .selectpicker').selectpicker();
	}

	$('#newdealershipmenttracking').validate({
		rules: {
			courier: 'required',
			trackingno: 'required'
		},
		messages: {},
		errorClass: 'error-class',
		errorPlacement: function(error, element) {
			element.closest('.form-group').find('.help-block').html(error.text());
		}
	});
});
</script>
