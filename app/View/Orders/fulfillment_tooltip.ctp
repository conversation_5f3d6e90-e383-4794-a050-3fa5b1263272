<?php
/**
 * @var AppView $this
 * @var array $shipearly_user
 * @var array $order
*/
?>
<?php
$dealerOrderProductMap = Hash::combine($order['DealerOrder']['DealerOrderProduct'] ?? [], '{n}.id', '{n}');
?>
<?php
$tooltipLines = [];
if (!empty($order['DealerOrder']['Fulfillment'])) {
    $tooltipLines[] = $this->Orders->displayFulfillmentStatus('fulfilled');
    foreach ($order['DealerOrder']['Fulfillment'] as $fulfillment) {
        $warehouseName = !empty($fulfillment['Warehouse']['name']) ? h($fulfillment['Warehouse']['name']) : 'Unknown Warehouse';
        $fulfilledDate = !empty($fulfillment['created_at']) ? format_datetime($fulfillment['created_at'], DATE_FORMAT) : 'Date not available';
        $tooltipLines[] = <<<HTML
            <div style="border: 1px solid #ddd; padding: 10px; border-radius: 8px; box-shadow: 0 2px 5px rgba(0,0,0,0.2); margin-top: 5px;">
                <strong>$warehouseName</strong> ($fulfilledDate)
                <div>
        HTML;
        foreach ($fulfillment['FulfillmentProduct'] as $fulfillmentItem) {
            $productId = $fulfillmentItem['dealer_order_product_id'] ?? null;
            $product = $dealerOrderProductMap[$productId] ?? [];
            if ($product && isset($product['Product']['product_name'])) {
                $productName = h($product['Product']['product_name']);
                $productVariant = h($product['Product']['variant_options']);
                $productQuantity = isset($fulfillmentItem['quantity']) ? $fulfillmentItem['quantity'] : '0';
                $productImage = $product['Product']['product_image'] ?: BASE_PATH . 'images/no_img.gif';
                $tooltipLines[] = <<<HTML
                    <div style="display: flex; align-items: center; margin-bottom: 5px;">
                        <img class="product-thumbnail" src="$productImage" alt="" style="width: 40px;"/>
                        <div>
                            <span>$productName x $productQuantity</span><br/>
                            <span class="pill-product-variant">$productVariant</span>
                        </div>
                    </div>
                HTML;
            }
        }
        $tooltipLines[] = "</div></div>";
    }
}
$unfulfilledItems = array_filter($order['DealerOrder']['DealerOrderProduct'] ?? [], fn($item) => $item['remaining_quantity'] > 0);
if (!empty($unfulfilledItems)) {
    $tooltipLines[] = $this->Orders->displayFulfillmentStatus('unfulfilled');
    $warehouses = [];
    foreach ($unfulfilledItems as $unfulfillment) {
        $warehouseName = !empty($unfulfillment['Warehouse']['name']) ? h($unfulfillment['Warehouse']['name']) : 'Unknown Warehouse';
        $warehouses[$warehouseName][] = $unfulfillment;
    }
    foreach ($warehouses as $warehouseName => $products) {
        $tooltipLines[] = <<<HTML
            <div style="border: 1px solid #ddd; padding: 10px; border-radius: 8px; box-shadow: 0 2px 5px rgba(0,0,0,0.2); margin-top: 5px;">
                <strong>$warehouseName</strong>
                <div>
        HTML;
        foreach ($products as $product) {
            $productName = h($product['Product']['product_name']);
            $productVariant = h($product['Product']['variant_options']);
            $productQuantity = $product['remaining_quantity'];
            $productImage = $product['Product']['product_image'] ?: BASE_PATH . 'images/no_img.gif';

            $tooltipLines[] = <<<HTML
                <div style="display: flex; align-items: center; margin-top: 5px;">
                    <img class="product-thumbnail" src="$productImage" alt="" style="width: 40px;"/>
                    <div>
                        <span>$productName x $productQuantity</span><br/>
                        <span class="pill-product-variant">$productVariant</span>
                    </div>
                </div>
            HTML;
        }
        $tooltipLines[] = "</div></div>";
    }
}

if (empty($order['DealerOrder']['DealerOrderProduct']) && !empty($order['Order']['dealer_qty_ordered'])) {
    $orderedProducts = array_filter($order['OrderProduct'], function($item) {
        return $item['is_ordered'];
    });
    $tooltipLines[] = $this->Orders->displayFulfillmentStatus('unfulfilled');
    $warehouses = [];
    foreach ($orderedProducts as $orderProduct) {
        if (isset($orderProduct['Warehouse']) && isset($orderProduct['Product'])) {
            $warehouseName = !empty($orderProduct['Warehouse']['name']) ? h($orderProduct['Warehouse']['name']) : 'Unknown Warehouse';
            $warehouses[$warehouseName][] = $orderProduct;
        }
    }
    foreach ($warehouses as $warehouseName => $products) {
        $tooltipLines[] = <<<HTML
            <div style="border: 1px solid #ddd; padding: 10px; border-radius: 8px; box-shadow: 0 2px 5px rgba(0,0,0,0.2); margin-top: 5px;">
                <strong>$warehouseName</strong>
                <div>
        HTML;
        foreach ($products as $product) {
            $productName = h($product['Product']['product_name']);
            $productVariant = h($product['Product']['variant_options']);
            $productQuantity = $product['quantity'];
            $productImage = $product['Product']['product_image'] ?: BASE_PATH . 'images/no_img.gif';

            $tooltipLines[] = <<<HTML
                <div style="display: flex; align-items: center; margin-top: 5px;">
                    <img class="product-thumbnail" src="$productImage" alt="" style="width: 40px;"/>
                    <div>
                        <span>$productName x $productQuantity</span><br/>
                        <span class="pill-product-variant">$productVariant</span>
                    </div>
                </div>
            HTML;
        }
        $tooltipLines[] = "</div></div>";
    }
}
$tooltipContent = implode("</br>", $tooltipLines);
?>
<?php echo $tooltipContent ?>
