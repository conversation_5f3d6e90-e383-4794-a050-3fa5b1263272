<?php
/**
 * @var AppView $this
 * @var array $shipearly_user
 * @var array $order
 * @var float $subTotal
 * @var float $orderTotal
 * @var array $refunds
 * @var float[] $refundTotals
 * @var float $refund_total
 * @var array $timeline_logs
 * @var array $dealer_qty_ordered
 * @var string $CustomerAddress
 * @var string $BillingAddress
 * @var string $RetailerAddress
 * @var string $BrandAddress
 * @var bool $userHasEditPermission
 * @var string[] $warehouseOptions
 * @var string $CourierName
 * @var string $orderNO
 * @var bool $show_secret_code
 * @var array $schedule_details
 * @var string $invoiceLink
 *
 * Used in element Orders/dealerorder_invoice_totals
 *
 * @var bool $show_balance
 * @var bool $enable_discount_edit
 * @var bool $enable_shipping_edit
 * @var bool $enable_shipping_name_edit
 * @var bool $enable_notes_edit
 * @var string $currencyCode
 * @var float $dealerSubtotal
 * @var float $dealerDiscount
 * @var float $shippingAmount
 * @var string[] $shippingRateOptions
 * @var float $dealerTaxes
 * @var float $dealerTotal
 * @var float $orderTotal
 * @var float $orderFees
 * @var float $orderRefund
 * @var float $dealerOrderRefund
 * @var float $wholesaleChargeAmount
 * @var float $orderBalance
 * @var string $notes
 */
?>
<?php
App::uses('OrderType', 'Utility');
App::uses('OrderStatus', 'Utility');
?>
<style>
.btn.fulfillment-button {
	position: absolute;
	width: 150px;
	height: 33.5px;
	right: 0;
	margin-right: 30px;
	margin-top: 10px;
	background-color: #77933b;
	border-color: #77933b;
}
.btn.fulfillment-button:hover {
    background-color: #5e732f;
}
</style>
<?php
$userType = $shipearly_user['User']['user_type'];
$orderType = $order['Order']['order_type'];
$orderStatus = $order['Order']['order_status'];

$showOrderProductEdit = (
    $userHasEditPermission &&
    $this->request->param('filter') !== 'dealerorderTable' &&
    $userType === User::TYPE_MANUFACTURER &&
    in_array($orderStatus, [OrderStatus::NEED_TO_CONFIRM, OrderStatus::DEALER_ORDER]) &&
    empty($refunds) &&
    $order['Order']['total_discount'] <= 0
);
if ($orderStatus === OrderStatus::PROCESSING) {
    $canShowFulfillmentButton = $this->Orders->canShowDealerFulfillmentButton($shipearly_user['User'], $order, $warehouseOptions);
	$fulfillmentButtonUrl = ['controller' => 'fulfillments', 'action' => 'add', 'dealer_order_id' => $order['DealerOrder']['id']];
} else {
    $canShowFulfillmentButton =  $this->Orders->canShowConsumerFulfillmentButton($shipearly_user['User'], $order, $warehouseOptions);
	$fulfillmentButtonUrl = ['controller' => 'fulfillments', 'action' => 'add', 'order_id' => $order['Order']['id']];
}
$showFulfillmentButton = (
    $userHasEditPermission && 
    (
        $canShowFulfillmentButton || $this->Orders->canShowShipFromStoreTrackingButton($shipearly_user['User'], $order)
    )
);
$scheduleOptions = Hash::combine((array)($schedule_details ?? []), '{n}.UserSchedule.id', '{n}.UserSchedule.schedule_name');
?>
<?php echo $this->Html->css('order-popup'); ?>
<div class="order-popup">
	<?php echo $this->element('Orders/order_popup_header', compact('order', 'userHasEditPermission', 'CourierName', 'orderNO', 'show_secret_code', 'invoiceLink')); ?>
	<div class="buttons-container">
        <?php if ($showOrderProductEdit) { ?>
            <button class="js-orderproduct-edit btn btn-xs pull-right" style="display: inline-block;">
                <i class="fas fa-edit"></i> <?= __('Edit'); ?>
            </button>
            <button class="js-orderproduct-cancel btn btn-xs pull-right" style="display: none;">
                <i class="fas fa-times-circle"></i> <?= __('Cancel'); ?>
            </button>
        <?php } ?>
    </div>
	<div id="OrderProducts">
	<?php if ($showFulfillmentButton) { ?>
		<?= $this->Html->link(
			__('Fulfill Item'),
			$fulfillmentButtonUrl,
			['class' => 'btn btn-primary fulfillment-button js-fulfillment']
		) ?>
	<?php } ?>
	<?php
	if ($orderType === OrderType::WHOLESALE) {
		echo $this->element('Orders/order_products_table/wholesale_invoice', compact('order'));
	} elseif ($this->request->param('filter') === 'dealerorderTable') {
		echo $this->element('Orders/order_products_table/dealerorder_invoice', compact('order'));
	} else {
		echo $this->element('Orders/order_products_table/invoice', compact('order'));
	}
	?>
	</div>
<?php if ($this->request->param('filter') == 'dealerorderTable') { ?>
	<?php echo $this->element('Orders/dealerorder_invoice_totals'); ?>
<?php } else { ?>
	<?php echo $this->element('Orders/order_invoice_totals'); ?>
	<table class="order-popup-totals-table">
		<tfoot>
		<?php if ($this->Orders->canShowVerificationCodeButtons($shipearly_user['User'], $order, $userHasEditPermission)) { ?>
			<tr>
				<td colspan="3" align="center">
					<a id="enterCode" href="<?php echo $this->Html->url(['controller' => 'orders', 'action' => 'verification_code', $order['Order']['id']]); ?>">
						<p class="ship-btn"><?= __('Enter Verification Code'); ?></p>
					</a>
				</td>
			</tr>
			<tr>
				<td colspan="3" align="center">
					<a id="resetVerificationCode" href="javascript:void(0);" class="btn btn-primary reset-code"><?php
						echo ($orderType !== OrderType::LOCAL_DELIVERY)
							? __('Order Ready for Pickup / Resend Verification Code')
							: __('Schedule Delivery / Resend Verification Code');
					?></a>
				</td>
			</tr>
		<?php } ?>
		</tfoot>
	</table>
<?php } ?>
	<div class="order-popup-header mt-3">
		<div class="row flex-wrap">
			<?php echo $this->element('Orders/order_notes', ['order' => $order]); ?>
			<?php echo $this->element('Orders/order_tags', ['order_id' => $order['Order']['id']]); ?>
		</div>
	</div>
	<?php echo $this->element('Orders/order_timeline', ['order_id' => $order['Order']['id']] + compact('timeline_logs')); ?>
</div>
<style>
#refundForm .form-group {
	margin: 0;
}
#refundForm .form-group:not(:first-child) {
	padding-top: 8px;
}
#refundForm .form-group input[type="text"] {
	width: 100%;
	padding: 6px;
}
#refundForm .form-group label {
	width: 100%;
}
#refundForm .errors {
	font-weight: 700;
	color: red;
}

#capture {
	width: 150px;
	float: right;
	margin-top: 10px;
}
</style>
<template id="resetVerificationCodeMessage">
	<?= __('Please confirm to send the customer an email their order is ready for pickup, schedule a pickup time and resend the verification code') ?>
	<div class="row" style="margin-left: 0; margin-top: 20px;">
		<div class="col-md-3 col-sm-12">
			<label for="schedule_id"><?= __('Calender to Send') ?>:</label>
		</div>
		<div class="col-md-5 col-sm-12">
			<select class="form-control" id="schedule_id">
			<?php if (!$scheduleOptions) { ?>
				<option value="0"><?= __('Send without Calendar') ?></option>
			<?php } ?>
			<?php foreach ($scheduleOptions as $id => $name) { ?>
				<option value="<?= $id ?>"><?= $name ?></option>
			<?php } ?>
			</select>
		</div>
		<?php if (!$scheduleOptions && !$shipearly_user['User']['Branch']) { ?>
			<div class="col-md-4 col-sm-12">
				<a href="/locations" target="_blank" class="link"><?= __('Click to Integrate Calendar') ?></a>
			</div>
		<?php } ?>
	</div>
</template>
<script type="text/javascript">
$(function() {
	$("#resetVerificationCode").on('click', function() {
		bootbox.confirm($('#resetVerificationCodeMessage').html(), function(result) {
			if (result === true) {
				location.href = "<?= BASE_PATH . $order['Order']['id'] . '/' . $orderType . '/' ?>" + $('#schedule_id').val() + "/reset";
			}
			bootbox.hideAll();
		});
	});
});
</script>
<script type="text/javascript">
$(function() {
	$('#enterCode').click(function(e) {
		e.preventDefault();
		$.get(this.href, function(data) {
			bootbox.dialog({
				title: ' ', // To take up vertical space
				message: data
			});
		})
	});
});
</script>
<script type="text/javascript">
$(function() {
	$('.js-orderproduct-edit').click(function() {
		$.get(
			"<?php echo Router::url(['controller' => 'orders', 'action' => 'ajax_order_product', 'OrderID' => $order['Order']['id']]); ?>",
			function(data) {
				$('.js-orderproduct-edit').hide();
				$('.js-orderproduct-cancel').show();

				var $orderProducts = $('#OrderProducts');
				$('#OrderProductsCancel').html($orderProducts.html());
				$orderProducts.html(data);
			}
		);
	});
	$('.js-orderproduct-cancel').click(function() {
		$('.js-orderproduct-edit').show();
		$('.js-orderproduct-cancel').hide();
		$('#OrderProducts').html($('#OrderProductsCancel').html());
	});
});
</script>
<div id="hidden" class="hide">
	<div id="OrderProductsCancel"></div>
</div>
