<?php
/**
 * @var AppView $this
 * @var array $products
 */

echo json_encode(array_map(
    function($product) {
        /* @var AppView $this */
        $product['Product']['brand_inventory'] = $this->Inventory->formatOrderProductSearchInventory($product);
        $product['Product']['product_price'] = $product['Product']['dealer_price'];

        $autocompleteFields = [
            'category' => $product['Warehouse']['name'] ?? null,
            'label' => $product['Product']['product_title'],
            'value' => $product['Product']['product_title'],
        ];

        return $autocompleteFields + $product;
    },
    $products
));
