<?php
/**
 * @var AppView $this
 */
?>
<style type="text/css">
.controls > input[type="checkbox"] {
	float: left;
	margin-right: 8px;
	margin-top: 4px;
}

.controls {
	margin: 2px 10px 0 0;
}

.control-group {
	float: left;
	margin: 8px 10px 0 0 !important;
}

.filter_box .filter_content > a.btn {
	margin: 11px 0 0 !important;
}

.jqgrow td {
	text-align: left !important;
}

.no-configuration {
	height: 25px;
	color: red;
	text-align: center;
	font-size: 14px;
	padding-top: 10px;
	padding-bottom: 10px;
}
</style>
<div class="form_title">
	<h1>Manage Settings</h1>
	<div class="fR">
		<a href="<?php echo ADMIN_PATH . 'editsetting'; ?>" class="btn btn-mini btn-primary fR">Edit settings</a>
	</div>
</div>
<?php echo $this->element("display_message"); ?>
<div class="grid_container">
	<div id="main_grid">
		<table id="jqGrid01">
		</table>
		<div id="jqGridPager01">
		</div>
	</div>
</div>
<script type="text/javascript">
(function($) {
	$(function() {
		$("#jqGrid01").jqGrid({
			url: '<?php echo ADMIN_PATH . 'pages/ajax_configuration'; ?>',
			datatype: 'json',
			height: 'auto',
			rowNum: 0,
			colNames: ['S.No', 'Name', 'value'],
			colModel: [
				{ name: 'id', index: 'id', width: 20, sortable: false },
				{ name: 'name', index: 'name', width: 30, sortable: false },
				{ name: 'value', index: 'value', width: 50, sortable: false }
			],
			gridComplete: function() {
				var $grid = $('#jqGrid01');
				if (!$grid.getGridParam('records')) {
					$grid.html('<tr><td class="no-configuration">No Configuration!</td></tr>');
				}
			},
			loadComplete: function() {
				$('.sidebar_drop').css('min-height', $('.wrapper').height());
			}
		});
	});
})(jQuery);
</script>
