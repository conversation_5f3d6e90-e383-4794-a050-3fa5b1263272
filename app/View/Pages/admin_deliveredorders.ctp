<style>
.control-group {
    float: left;
    margin: 8px 10px 0 0 !important;
}
.filter_box .filter_content > a.btn {
	margin: 11px 0 0 !important;
}
</style>
<script type="text/javascript">
var unselect = [];
var admin_path = "<?php echo BASE_PATH; ?>admin/";
function search_condition() {
	document.getElementById("manage_user").submit();
}

function addmasspay() {
	var uncheckednames = []; 
	var checkednames = [];
	var grid = $("#jqGrid01"),i;
    var paydatas = grid.getDataIDs();
    for(var i = 1; i <= paydatas.length; i++){
    	var name = grid.jqGrid('getCell', paydatas[i-1], 'orderID');
    	var unname = grid.jqGrid('getCell', paydatas[i-1], 'Masspay');
    	if($('#jqg_jqGrid01_'+i).prop('checked')) {
    		if(unname != 1) {
    			checkednames.push(name);
    		}
    	} else {
    		if(unname == 1) {
    			uncheckednames.push(name);
    		}
    	}
    }

	$.ajax({
		type: "POST",
		url: admin_path+"addtomasspay",
		data: { checkednames: checkednames, uncheckednames: uncheckednames }
	}).done(function( msg ) {
		$('.msg_panel, .alert-success').show();
		$('#jqGrid01').trigger( 'reloadGrid' );
	});

	return false;
}

function gotomasspay() {
	window.location = "<?php echo BASE_PATH; ?>admin/pages/masspayment";
}

</script>
<?php  echo $this->Form->create('User', array('id'=>'manage_user','class'=>'form-signin','inputDefaults'=>array("label"=>false,"div"=>false))); ?>  
<div class="form_title">
	<h1>Manage Payments</h1>
	<div class="fR"> <a onclick="javascript:addmasspay();" class="btn btn-mini btn-primary fR">Save selection</a> </div>
</div>
<?php echo $this->element("display_message"); ?>
<!-- <div class="filter_box"> <a class="btn btn-mini btn-primary filter_btn fR"><i class="fas fa-filter"></i>Filter</a>
	<div class="filter_content bdr_rds_6">
		<label>Filter By : </label>
		<div class="control-group">
			<!--<label class="control-label" for="">Page</label>
            <div class="controls">
            	<?php echo $this->Form->input( 'User.email_address', array( 'id'=>'email_address', 'type'=>'text', 'style'=>'width:115px', 'placeholder' => 'Email address' ) ); ?>
            </div>
            <div class="controls">
				<?php echo $this->Form->input( 'User.status', array( 'id'=>'status', 'type'=>'checkbox', 'value' => 'Register', 'label' => "Unapproved users" ) ); ?>
            </div>
		</div>
		<a class="btn btn-small" onclick="javascript:search_condition();">Go</a> <a  class="close"><i class="fas fa-times"></i></a> 
	</div>
</div> -->
<?php  echo $this->Form->end();?>
<script type="text/javascript">
	$("a.filter_btn").click(function ( ) {
		$(this).hide('slow');
		$('.filter_content').show('slow');
	});
	$(".filter_box a.close").click(function ( ) {
		$('.filter_content').hide('slow');
		$('a.filter_btn').show('slow');
	});
</script>
<div class="msg_panel" style="display: none;">
	<div class="alert alert-success"> 
        <button type="button" class="close" data-dismiss="msg_panel" aria-hidden="true">&times;</button>
		<div id="addmass">Mass payment list has been updated.</div>
	</div>
	<div class="alert alert-danger alert-dismissable" style="display: none;">
		<button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
		<div id="errormass"></div>		
	</div>
</div>
<div class="grid_container">
	<div id="main_grid">
    	<table id="jqGrid01"></table>
       	<div id="jqGridPager01">
            	<script type="text/javascript">
	            	$(function(){
						jQuery("#jqGrid01").jqGrid({
								sortable: true, 	
								direction: "ltr",
								url:"<?php echo ADMIN_PATH."pages/ajax_deliveredorders"; ?>",
								datatype: "json",
					            height:'auto',
					            rowNum: "<?php $rows = $this->Session->read('Payment.rows'); echo !empty($rows)?$rows:20; ?>",
					            rowList: [10,20,30],
					            colNames:['Masspay', 'Company Name', 'Order status', 'Order ID', 'Fullfill Type', 'Total ($)', 'Date Created'],
					            colModel:[
					            	{name:'Masspay',index:'Masspay', width:90, sortable:true, hidden: true},
					            	{name:'Status',index:'Status', width:90, sortable:true},
					            	{name:'Retailer',index:'Retailer', width:90, sortable:true},
					            	{name:'orderID',index:'orderID', width:90, sortable:true},
					            	{name:'order_type',index:'order_type', width:90, sortable:true},
					            	{name:'total_price',index:'total_price', width:50, sortable:true},
					            	{name:'created',index:'created_at', width:90, sortable:true}
					            ],
					            pager: "#jqGridPager01",
					            viewrecords: true,            
					            hidegrid:false,
					            altRows: true,
					            sortorder: "<?php $sort = $this->Session->read('Payment.sort'); echo !empty($sort)?$sort:'DESC'; ?>",
								sortname: "<?php $sidx = $this->Session->read('Payment.sidx'); echo !empty($sidx)?$sidx:'created_at'; ?>",
								multiselect: true,
					            gridComplete: function() {
							    	var recs = jQuery("#jqGrid01").getGridParam("records");
							        if (recs == 0) {
							        	var display_text = "<tr><td style='height:25px;color:red;text-align:center;font-size:14px;padding-top:10px;padding-bottom:10px;'>No Orders added yet!</td></tr>";
							         	jQuery('#jqGrid01').html(display_text);
							     	}
							 	},
							    loadComplete: function(data, response) {
							        jQuery(".sidebar_drop").css('min-height', jQuery(".wrapper").height());
									var paydata = $("#jqGrid01").getDataIDs();
					                for(var i = 0; i < paydata.length; i++){
					                    if ($("#jqGrid01").getRowData(paydata[i])['Masspay'] == 1 ) {
					                        $("#jqGrid01").setSelection(paydata[i], false);
					                    }
					                }
								}							 		
					        });
						});
			<?php if( !empty( $this->request->data['User']['email_address'] ) || !empty( $this->request->data['User']['status'] ) ) { ?>
				$('.filter_content').show('slow');
				$('a.filter_btn').hide('slow');
			<?php } ?>
            </script>
		</div>
	</div>
</div>
<div class="form_title" style="border-bottom: none;">
	<div class="fR"> <a onclick="javascript:addmasspay();" class="btn btn-mini btn-primary fR">Save selection</a> </div>
</div>
<div class="form_title" style="border-bottom: none;">
	<div class="fR"> <a onclick="javascript:gotomasspay();" class="btn btn-mini btn-primary fR">Confirm</a> </div>
</div>
<style type="text/css">
	.controls > input[type="checkbox"] {
	    float: left;
	    margin-right: 8px;
	    margin-top: 4px;
	}
	.controls {
	    margin: 2px 10px 0 0;
	}
</style>
