<?php
/**
 * @var AppView $this
 * @var array $data
 */
?>
<div class="form_title">
	<h1>Edit Settings</h1>
	<div class="fR">
		<a href="javascript:void(0);" onClick="history.back(-1);" class="btn btn-mini btn-primary fR">
			<i class="fas fa-chevron-left"></i>Back
		</a>
	</div>
</div>
<div class="form_content">
	<?php echo $this->Form->create('Configuration', array(
		'id' => 'pagefrm',
		'class' => 'form-horizontal shrink_form',
		'type' => 'file',
		'inputDefaults' => array('label' => false, 'div' => false),
	)); ?>
	<?php foreach ($data as $key => $value) { ?>
		<div class="control-group">
			<?php echo $this->Form->label($value['Configuration']['id'] . '.Configuration.value', $value['Configuration']['name'], ['class' => 'control-label']); ?>
			<div class="controls">
				<?php echo $this->Form->hidden($value['Configuration']['id'] . '.Configuration.name', ['value' => $value['Configuration']['name']]); ?>
				<?php echo $this->Form->input($value['Configuration']['id'] . '.Configuration.value', array(
					'type' => 'text',
					'style' => 'width:500px; height:25px;',
					'value' => $value['Configuration']['value'],
				)); ?>
				<div class="clear"></div>
				<span class="help-block"></span>
			</div>
		</div>
	<?php } ?>
		<div class="control-group">
			<div class="controls">
				<button id="submit_btn" class="btn btn-primary">Update</button>
			</div>
		</div>
	<?php echo $this->Form->end(); ?>
</div>
