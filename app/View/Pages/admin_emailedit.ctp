<?php

use ShipEarlyApp\Lib\Utility\SupportedLanguages;

/**
 * @var AppView $this
 * @var string $title_for_layout
 */
?>
<?php $this->start('script') ?>
<?= $this->Html->script('vendor/tinymce/tinymce.min.js', ['plugin' => false]) ?>
<script>
tinymce.init({
    selector: "#content_",
    promotion: false,
    relative_urls: false,
    remove_script_host: false,
    theme: "silver",
    plugins: [
        "advlist", "autolink", "lists", "link", "image", "charmap", "preview", "anchor", "pagebreak",
        "searchreplace", "wordcount", "visualblocks", "visualchars", "code", "fullscreen",
        "insertdatetime", "media", "nonbreaking", "save", "table", "directionality",
        "emoticons"
    ],
    toolbar1: "insertfile undo redo | styleselect | bold italic | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | link image | print preview media | forecolor backcolor emoticons",
    image_advtab: true,
	font_family_formats: 'Arial=arial,helvetica,sans-serif;' + 'Arial Black=arial black,sans-serif;' + 'Comic Sans MS=comic sans ms,sans-serif;' + 'Courier New=courier new,courier,monospace;' + 'Georgia=georgia,palatino,serif;' + 'Helvetica=helvetica,arial,sans-serif;' + 'Impact=impact,sans-serif;' + 'Roboto=roboto,sans-serif;' + 'Tahoma=tahoma,arial,helvetica,sans-serif;' + 'Terminal=terminal,monaco,monospace;' + 'Times New Roman=times new roman,times,serif;' + 'Trebuchet MS=trebuchet ms,geneva,sans-serif;' + 'Verdana=verdana,geneva,sans-serif;',
});
</script>
<?php $this->end() ?>
<style type="text/css">
#pagefrm .control-group .controls {
	width: 510px;
}
#pagefrm .controls textarea, 
#pagefrm .controls input:not([type="checkbox"]) {
	width: calc(100% - 10px);
}
</style>
<div class="form_title">
	<h1><?php echo $title_for_layout; ?></h1>
	<div class="fR">
		<a href="javascript:void(0);" onClick="history.back(-1);" class="btn btn-mini btn-primary fR"><i class="fas fa-chevron-left"></i>Back</a>
	</div>
</div>
<div class="form_content">
	<?php echo $this->Form->create('EmailTemplate', [
		'id' => 'pagefrm',
		'class' => 'form-horizontal shrink_form',
		'inputDefaults' => [
			'div' => 'control-group',
			'label' => ['class' => 'control-label'],
			'between' => '<div class="controls">',
			'after' => '<div class="clear"></div><span class="help-block"></span></div>',
		],
	]); ?>
	<?php echo $this->Form->hidden('id', ['id' => 'id']); ?>
	<?php echo $this->Form->hidden('template_name', ['id' => 'template_name']); ?>
	<?php echo $this->Form->input('locale', [
		'id' => 'locale',
		'type' => 'select',
		'default' => $this->request->query('lang'),
		'options' => SupportedLanguages::getOptionsByLocale(),
		'label' => ['text' => 'Language', 'class' => 'control-label'],
	]); ?>
	<?php echo $this->Form->input('alias', [
		'id' => 'alias',
		'placeholder' => $this->request->data['EmailTemplate']['template_name'],
		'label' => ['text' => 'Template Alias', 'class' => 'control-label'],
	]); ?>
	<?php echo $this->Form->input('subject', [
		'id' => 'subject',
		'label' => ['text' => 'Email Subject', 'class' => 'control-label'],
	]); ?>
	<?php echo $this->Form->input('bcc_admin', [
		'id' => 'bcc_admin',
		'type' => 'checkbox',
		'format' => ['before', 'label', 'between', 'input', 'after', 'error'],
	]); ?>
	<?php echo $this->Form->input('description', [
		'id' => 'description',
		'type' => 'textarea',
		'style' => 'resize: vertical;',
		'label' => ['text' => 'Email Description', 'class' => 'control-label'],
	]); ?>
	<?php echo $this->Form->input('content', [
		'id' => 'content_',
		'type' => 'textarea',
		'style' => 'resize: vertical;',
		'label' => ['text' => 'Email Content', 'class' => 'control-label'],
	]); ?>
	<div class="control-group">
		<div class="controls">
			<button type="submit" id="submit_btn" class="btn btn-primary">Update</button>
		</div>
	</div>
	<?php echo $this->Form->end();?>
</div>
<script type="text/javascript">
$(function() {
	$('#content_, #description').on('input', function() {
		this.style.height = '1px';
		this.style.height = this.scrollHeight + 'px';
	}).trigger('input');
	$('#locale').on('change', function() {
		var searchParams = new URLSearchParams(window.location.search);
		searchParams.set('lang', this.value);
		console.log(searchParams);
		window.location.search = searchParams.toString();
	});
});
</script>
