<div class="form_title">
    <h1><?php echo $title_for_layout . " - " . $this->data['Category']['category_name']; ?></h1>
    <div class="fR"> <a href="javascript:;" onClick="history.back(-1);" class="btn btn-mini btn-primary fR"><i class="fas fa-chevron-left"></i>Back</a> </div>
</div>
<div class="form_content">
    <input id= "admin_path" type ="hidden" value="<?php echo ADMIN_PATH; ?>" />
    <?php echo $this->Form->create('Category', array('id' => 'pagefrm', 'class' => 'form-horizontal shrink_form', 'type' => 'file', 'inputDefaults' => array("label" => false, "div" => false))); ?>
    <?php echo $this->Form->input('Category.id', array('type' => 'hidden', 'id' => 'id')); ?>
    <div class="control-group" id="control-group-category_name">
        <label class="control-label" for="">Area Name<span class="req-star">&#42;</span></label>
        <div class="controls">
            <?php echo $this->Form->input('Category.category_name', array('id' => 'category_name', 'label' => false, 'div' => false, 'type' => 'text', 'style' => 'width:300px;height:25px;font-size:14px;')); ?>
            <div class="clear"></div>
            <span class='help-block' id="invalid-category" style="color:#EA1414;"></span>
        </div>
    </div>
    <div class="control-group">
        <label class="control-label" for="">&nbsp;</label>
        <div class="controls"> <button id ="submit_btn" class="btn btn-primary">Update</div>
    </div>
    <?php echo $this->Form->end(); ?>
</div>
<script>
    $('#category_name').on("click", function() {
        $('#invalid-category').hide(); 
        $('#control-group-category_name').attr('class','control-group');
    });
    
    $('#pagefrm').submit(function() {
        $('#invalid-category').hide();
        if ($('#category_name').val() == "") {
            $('#control-group-category_name').attr('class','control-group error');
            $('#invalid-category').show();
            $('#invalid-category').html('Please enter area name');
            return false;
        }
    });
</script>
