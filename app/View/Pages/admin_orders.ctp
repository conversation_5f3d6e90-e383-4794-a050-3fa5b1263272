<style>
.control-group {
    float: left;
    margin: 8px 10px 0 0 !important;
}
.filter_box .filter_content > a.btn {
	margin: 11px 0 0 !important;
}
</style>
<script type="text/javascript">
var admin_path = "<?php echo BASE_PATH; ?>admin/";
function search_condition(){
	document.getElementById("manage_order").submit();
}
</script>
<?php  echo $this->Form->create('User', array('id'=>'manage_order','class'=>'form-signin','inputDefaults'=>array("label"=>false,"div"=>false))); ?>  
<div class="form_title">
	<h1>Manage Orders</h1>
</div>
<?php echo $this->element("display_message"); ?>
<div class="filter_box"> <a class="btn btn-mini btn-primary filter_btn fR"><i class="fas fa-filter"></i>Filter</a>
	<div class="filter_content bdr_rds_6">
		<label>Filter By KeyWords: </label>
		<div class="control-group">
            <div class="controls">
            	<?php echo $this->Form->input( 'Order.search', array( 'id'=>'keyword', 'type'=>'text', 'style'=>'width:115px', 'placeholder' => 'Key Words', 'value' => $orderKey) ); ?>
				<a class="btn btn-small" onclick="javascript:search_condition();">Go</a>
            </div>
		</div>
		<a  class="close"><i class="fas fa-times"></i></a> 
	</div>
	<div class="filter_content bdr_rds_6">
		<label>Filter By date: </label>
		<div class="control-group">
            <div class="controls">
            	<?php echo $this->Form->input( 'Order.date', array( 'id'=>'date', 'type'=>'text', 'placeholder' => '', 'value' => $date) ); ?>
            </div>
		</div>
	</div>
</div>
<?php  echo $this->Form->end();?>
<script type="text/javascript">
	$("a.filter_btn").click(function ( ) {
		$(this).hide('slow');
		$('.filter_content').show('slow');
	});
	$(".filter_box a.close").click(function ( ) {
		$('.filter_content').hide('slow');
		$('a.filter_btn').show('slow');
	});
</script>
<div class="grid_container">
	<div id="main_grid">
    	<table id="jqGrid01"></table>
       	<div id="jqGridPager01">
            	<script type="text/javascript">
		            $(function(){
						jQuery("#jqGrid01").jqGrid({
							sortable: true,	
							direction: "ltr",
							url:"<?php echo ADMIN_PATH."pages/ajax_orders"; ?>",
							datatype: "json",
				            height:'auto',
				            rowNum: "<?php $rows = $this->Session->read('Order.rows'); echo !empty($rows)?$rows:20; ?>",
				            rowList: [10,20,50,100],
							colNames: ['Order ID', 'Brand Name', 'Retailer Name', 'Order status', 'Payment status', 'Fullfill Type', 'Total ($)', 'Date Created'],
							colModel: [
								{name: 'orderID', index: 'orderID', width: 70, sortable: true},
								{name: 'Brand', index: 'Brand', width: 100, sortable: false},
								{name: 'Retailer', index: 'Retailer', width: 100, sortable: false},
								{name: 'Order_status', index: 'Order_status', width: 90, sortable: true},
								{name: 'Payment_status', index: 'Payment_status', width: 70, sortable: true},
								{name: 'order_type', index: 'order_type', width: 70, sortable: true},
								{name: 'total_price', index: 'total_price', width: 90, sortable: false},
								{name: 'created_at', index: 'created_at', width: 70, sortable: true}
							],
				            pager: "#jqGridPager01",
				            viewrecords: true,            
				            hidegrid:false,
				            altRows: true,
				            sortorder: 'DESC',
							sortname: 'created_at',
				            gridComplete: function() {
						    	var recs = jQuery("#jqGrid01").getGridParam("records");
						        if (recs == 0) {
						        	var display_text = "<tr><td style='height:25px;color:red;text-align:center;font-size:14px;padding-top:10px;padding-bottom:10px;'>No Orders added yet!</td></tr>";
						         	jQuery('#jqGrid01').html(display_text);
						     	}
						 	},
						    loadComplete: function(data, response) {
						        jQuery(".sidebar_drop").css('min-height', jQuery(".wrapper").height() + 75);
						    }							 		
				        });
					});
				<?php if( !empty( $this->request->data['User']['email_address'] ) || !empty( $this->request->data['User']['status'] ) ) { ?>
					$('.filter_content').show('slow');
					$('a.filter_btn').hide('slow');
				<?php } ?>
            </script>
		</div>
	</div>
</div>
<style type="text/css">
	.controls > input[type="checkbox"] {
	    float: left;
	    margin-right: 8px;
	    margin-top: 4px;
	}
	.controls {
	    margin: 2px 10px 0 0;
	}
</style>
 <script>
	$(function() { 
		$("#date").daterangepicker({
			datepickerOptions : {
				numberOfMonths : 2
			}
		}); 
	});
</script>
<style type="text/css">
	.ui-button-icon-secondary.ui-icon.ui-icon-triangle-1-s {
	    position: absolute;
	    top: 30%;
	}
	.comiseo-daterangepicker-triggerbutton {
	    position: relative !important;
	}
</style>
