<?php
/**
 * @var AppView $this
 * @var bool $userHasEditPermission
 */
?>
<?php
$this->assign('title', "{$this->request->data['Product']['product_sku']} Pricing");

$formStart = $this->Form->create('ProductPricing', [
    'id' => 'ProductPricingEditForm',
    'default' => ($userHasEditPermission),
    'inputDefaults' => ['label' => false, 'div' => false],
]);

$basePrice = isset($this->request->data['Product']['compare_at_price'])
    ? format_number($this->request->data['Product']['compare_at_price'])
    : 'None';

$b2BPriceTooltipContent = __('Inserting blank pricing removes the item from catalog');

$retailTableModel = [
    [
        'label' => 'Currency',
        'thOptions' => ['class' => 'retail-pricing-column--currency_code'],
        'cellOptions' => ['class' => 'retail-pricing-column--currency_code'],
        'valueCallback' => function($record, $idx) {
            /** @var AppView $this */

            return implode('', [
                $this->Form->hidden("UserCurrency.{$idx}.ProductPrice.currency_code", [
                    'type' => 'hidden',
                    'value' => $record['currency_code'],
                    'disabled' => ($record['currency_code'] === $this->request->data['Product']['currency']),
                ]),
                h($record['currency_code']),
            ]);
        },
    ],
    [
        'label' => 'Retail Price',
        'thOptions' => ['class' => 'retail-pricing-column--price'],
        'cellOptions' => ['class' => 'retail-pricing-column--price'],
        'valueCallback' => function($record, $idx) {
            /** @var AppView $this */

            return $this->Form->input("UserCurrency.{$idx}.ProductPrice.price", [
                'type' => 'number',
                'min' => '0.00',
                'step' => '0.01',
                'required' => true,
                'disabled' => ($record['currency_code'] === $this->request->data['Product']['currency']),
                'class' => 'form-control number-validate',
            ]);
        },
    ],
    [
        'label' => 'Base Price',
        'thOptions' => ['class' => 'retail-pricing-column--base_price'],
        'cellOptions' => ['class' => 'retail-pricing-column--base_price'],
        'valueCallback' => function($record, $idx) {
            /** @var AppView $this */

            return $this->Form->input("UserCurrency.{$idx}.ProductPrice.compare_at_price", [
                'type' => 'number',
                'min' => '0.00',
                'step' => '0.01',
                'placeholder' => 'None',
                'required' => false,
                'disabled' => ($record['currency_code'] === $this->request->data['Product']['currency']),
                'class' => 'form-control number-validate',
                'value' => isset($record['ProductPrice']['compare_at_price']) ? $this->Currency->formatAsDecimal($record['ProductPrice']['compare_at_price'], $record['currency_code']) : null,
            ]);
        },
    ],
];

$retailTableHeaders = $this->Html->tableHeaders(array_map(function($column) {
    $thValue = $column['label'] ?? '';
    $thOptions = $column['thOptions'] ?? [];

    return [$thValue => $thOptions];
}, $retailTableModel));

if (count($this->request->data['UserCurrency']) > 0) {
    $retailTableCells = $this->Html->tableCells(array_map(function($record, $idx) use ($retailTableModel) {
        return array_map(function($column) use ($record, $idx) {
            $valueCallback = $column['valueCallback'] ?? null;
            $callback = is_callable($valueCallback)
                ? $valueCallback
                : function($record) use ($valueCallback) {
                    return h(Hash::get($record, $valueCallback, $valueCallback));
                };

            $value = call_user_func($callback, $record, $idx);
            $cellOptions = $column['cellOptions'] ?? [];

            // HtmlHelper::tableCells will only accept cellOptions from cells in this format
            return [0 => $value, 1 => $cellOptions];
        }, $retailTableModel);
    }, $this->request->data['UserCurrency'], array_keys($this->request->data['UserCurrency'])));
} else {
    $retailTableCells = $this->Html->tableCells([]);
}


$dealerTableModel = [
    [
        'label' => 'Tier Name',
        'thOptions' => ['class' => 'dealer-pricing-column--pricingtiername'],
        'cellOptions' => ['class' => 'dealer-pricing-column--pricingtiername'],
        'valueCallback' => function($record, $idx) {
            /** @var AppView $this */

            return implode('', [
                $this->Form->hidden("PricingTier.{$idx}.ProductTier.pricingtierid", [
                    'type' => 'hidden',
                    'value' => $record['id'],
                ]),
                h($record['pricingtiername']),
            ]);
        },
    ],
    [
        'label' => 'Retail Price',
        'thOptions' => ['class' => 'dealer-pricing-column--product_price'],
        'cellOptions' => ['class' => 'dealer-pricing-column--product_price'],
        'valueCallback' => 'N/A',
    ],
    [
        'label' => 'B2B Price ' . $this->Html->tag('span', '<i class="fas fa-circle-info"></i>', [
            'escape' => false,
            'class' => 'ml-1',
            'tabindex' => '0',
            'data-tooltipster' => json_encode([
                'content' => $b2BPriceTooltipContent,
            ]),
            'title' => $b2BPriceTooltipContent,
        ]),
        'thOptions' => ['class' => 'dealer-pricing-column--dealer_price'],
        'cellOptions' => ['class' => 'dealer-pricing-column--dealer_price'],
        'valueCallback' => function($record, $idx) {
            /** @var AppView $this */

            return $this->Form->input("PricingTier.{$idx}.ProductTier.dealer_price", [
                'type' => 'number',
                'min' => '0.00',
                'max' => $this->request->data['Product']['product_price'],
                'step' => '0.01',
                'required' => false,
                'class' => 'form-control number-validate',
                'style' => 'width: 126px; margin: 0 auto;',
            ]);
        },
    ],
    [
        'label' => 'B2B Base Price',
        'thOptions' => ['class' => 'dealer-pricing-column--dealer_base_price'],
        'cellOptions' => ['class' => 'dealer-pricing-column--dealer_base_price'],
        'valueCallback' => function($record, $idx) {
            /** @var AppView $this */

            return $this->Form->input("PricingTier.{$idx}.ProductTier.dealer_base_price", [
                'type' => 'number',
                'min' => '0.00',
                'max' => $this->request->data['Product']['product_price'],
                'step' => '0.01',
                'placeholder' => 'None',
                'required' => false,
                'class' => 'form-control number-validate',
                'style' => 'width: 126px; margin: 0 auto;',
            ]);
        },
    ],
    [
        'label' => 'B2B Margin',
        'thOptions' => ['class' => 'dealer-pricing-column--dealer_margin_percent'],
        'cellOptions' => ['class' => 'dealer-pricing-column--dealer_margin_percent'],
        'valueCallback' => 'N/A',
    ],
    [
        'label' => 'Ship to Store',
        'thOptions' => ['class' => 'dealer-pricing-column--alt_nonstock_dealer_price'],
        'cellOptions' => ['class' => 'dealer-pricing-column--alt_nonstock_dealer_price'],
        'valueCallback' => function($record, $idx) {
            /** @var AppView $this */

            return $this->Form->input("PricingTier.{$idx}.ProductTier.alt_nonstock_dealer_price", [
                'type' => 'number',
                'min' => '0.00',
                'max' => $this->request->data['Product']['product_price'],
                'step' => '0.01',
                'placeholder' => 'Use B2B Price',
                'required' => false,
                'class' => 'form-control number-validate',
                'style' => 'width: 126px; margin: 0 auto;',
            ]);
        },
    ],
    [
        'label' => 'STS Margin',
        'thOptions' => ['class' => 'dealer-pricing-column--alt_nonstock_dealer_margin_percent'],
        'cellOptions' => ['class' => 'dealer-pricing-column--alt_nonstock_dealer_margin_percent'],
        'valueCallback' => 'N/A',
    ],
    [
        'label' => 'Commission',
        'thOptions' => ['class' => 'dealer-pricing-column--commission'],
        'cellOptions' => ['class' => 'dealer-pricing-column--commission'],
        'valueCallback' => function($record, $idx) {
            /** @var AppView $this */
    
            return $this->Form->input("PricingTier.{$idx}.ProductTier.commission", [
                'type' => 'number',
                'min' => '0.00',
                'step' => '0.01',
                'placeholder' => '0.00',
                'required' => true,
                'class' => 'form-control number-validate',
                'style' => 'width: 126px; margin: 0 auto;',
            ]);
        },
    ],
    [
        'label' => 'Currency',
        'thOptions' => ['class' => 'dealer-pricing-column--currencytype'],
        'cellOptions' => ['class' => 'dealer-pricing-column--currencytype'],
        'valueCallback' => function($record) {
            /** @var AppView $this */

            return $this->Html->tag('span', $record['currencytype'], [
                'data-currency-code' => $record['currencytype'],
                'escape' => true,
            ]);
        },
    ],
];

$dealerTableHeaders = $this->Html->tableHeaders(array_map(function($column) {
    $thValue = $column['label'] ?? '';
    $thOptions = $column['thOptions'] ?? [];

    return [$thValue => $thOptions];
}, $dealerTableModel));

if (count($this->request->data['PricingTier']) > 0) {
    $dealerTableCells = $this->Html->tableCells(array_map(function($record, $idx) use ($dealerTableModel) {
        return array_map(function($column) use ($record, $idx) {
            $valueCallback = $column['valueCallback'] ?? null;
            $callback = is_callable($valueCallback)
                ? $valueCallback
                : function($record) use ($valueCallback) {
                    return h(Hash::get($record, $valueCallback, $valueCallback));
                };

            $value = call_user_func($callback, $record, $idx);
            $cellOptions = $column['cellOptions'] ?? [];

            // HtmlHelper::tableCells will only accept cellOptions from cells in this format
            return [0 => $value, 1 => $cellOptions];
        }, $dealerTableModel);
    }, $this->request->data['PricingTier'], array_keys($this->request->data['PricingTier'])));
} else {
    $dealerTableCells = $this->Html->tableCells([[
        ['Please update pricing tiers in the retailers tab', ['colspan' => count($dealerTableModel), 'style' => 'text-align: center;']],
    ]]);
}

$formEnd = $this->Form->end(($userHasEditPermission) ? ['class' => 'btn btn-primary', 'div' => ['style' => 'text-align: right;']] : null);
?>
<?php $this->start('css'); ?>
<style>
#ProductPricingEditForm input[type="number"] {
    text-align: right;
}
</style>
<?php $this->end(); ?>
<div class="clearfix"><?= $this->Layout->contentHeader() ?></div>
<?= $formStart ?>
<div class="table-responsive">
    <table class="retail-pricing table table-hover table-condensed">
        <thead><?= $retailTableHeaders ?></thead>
        <tbody><?= $retailTableCells ?></tbody>
    </table>
</div>
<div class="table-responsive">
    <table class="dealer-pricing table table-hover table-condensed">
        <thead><?= $dealerTableHeaders ?></thead>
        <tbody><?= $dealerTableCells ?></tbody>
    </table>
</div>
<?= $formEnd ?>
<?php $this->start('script'); ?>
<script type="text/javascript">
$(function() {
    $('#ProductPricingEditForm').on('change', 'td.retail-pricing-column--price input[type="number"]', function() {
        var $this = $(this);
        var currencyCode = $this.closest('tr').find('td.retail-pricing-column--currency_code :input').val();
        var $targetRows = $('#ProductPricingEditForm table.dealer-pricing > tbody > tr').has('td.dealer-pricing-column--currencytype [data-currency-code="' + currencyCode + '"]');

        var $productPrice = $targetRows.find('.dealer-pricing-column--product_price');
        var $dealerPriceInput = $targetRows.find('.dealer-pricing-column--dealer_price input[type="number"]');
        var $dealerBasePriceInput = $targetRows.find('.dealer-pricing-column--dealer_base_price input[type="number"]');
        var $altPriceInput = $targetRows.find('.dealer-pricing-column--alt_nonstock_dealer_price input[type="number"]');
        var $commissionInput = $targetRows.find('.dealer-pricing-column--commission input[type="number"]');

        var price = ($this.val() !== '') ? Number($this.val()).toFixed(2) : '';

        $this.val(price);
        $productPrice.text(price || 'N/A');
        $dealerPriceInput.attr('max', price);
        $dealerBasePriceInput.attr('max', price);
        $altPriceInput.attr('max', price);

        $dealerPriceInput.trigger('change');
    }).on('change', 'td.dealer-pricing-column--dealer_price input[type="number"]', function() {
        var $this = $(this);
        var $row = $this.closest('tr');
        var $margin = $row.find('td.dealer-pricing-column--dealer_margin_percent');
        var $altPrice = $row.find('td.dealer-pricing-column--alt_nonstock_dealer_price input[type="number"]');

        var dealerPrice = ($this.val() !== '') ? Number($this.val()).toFixed(2) : '';

        var productPriceNumber = Number($this.attr('max'));
        var margin = (dealerPrice !== '' && productPriceNumber > 0)
            ? ((productPriceNumber - Number(dealerPrice)) / productPriceNumber * 100).toFixed(1) + '%'
            : '';

        $this.val(dealerPrice);
        $margin.text(margin || 'N/A');

        $altPrice.trigger('change');
    }).on('change', 'td.dealer-pricing-column--alt_nonstock_dealer_price input[type="number"]', function() {
        var $this = $(this);
        var $row = $this.closest('tr');
        var $margin = $row.find('td.dealer-pricing-column--alt_nonstock_dealer_margin_percent');

        var dealerPrice = ($this.val() !== '') ? Number($this.val()).toFixed(2) : '';

        var productPriceNumber = Number($this.attr('max'));
        var margin = (dealerPrice !== '' && productPriceNumber > 0)
            ? ((productPriceNumber - Number(dealerPrice)) / productPriceNumber * 100).toFixed(1) + '%'
            : '';

        $this.val(dealerPrice);
        $margin.text(margin || $row.find('td.dealer-pricing-column--dealer_margin_percent').text());
    }).on('change', 'td.dealer-pricing-column--commission input[type="number"]', function() {
        var $this = $(this);
        var commission = ($this.val() !== '') ? Number($this.val()).toFixed(2) : '';
        $this.val(commission);
    }).find('td.retail-pricing-column--price input[type="number"], td.dealer-pricing-column--dealer_price input[type="number"]').trigger('change');
});
</script>
<?php $this->end(); ?>
