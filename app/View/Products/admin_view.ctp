<?php
/**
 * @var AppView $this
 * @var array $product
 */
?>
<div class="box-text">
	<div class="msg_panel watch" style="display: none;">
		<button type="button" class="close">&times;</button>
		<div id="display_messages" class="alert alert-success msg_panel"></div>
	</div>
</div>


<div class="form_title">
  <h1 class="">PRODUCT PREVIEW</h1>
</div>

<div>
	<div class='shadow-box productSold'>
		<table class='box-text'>
			<tr>
				<td class='widget-iden' style="padding: 15px;" colspan="2">
					<span>
						<h3 class="order-det widget-cls" ><?php echo $product['Product']['product_title']; ?></h3>
					</span>
					<span style="float: right; padding: 0 0px 0 20px; width:230px">
						<span class="msrpPrice">
						<h3 class='order-det msrp-align' >MSRP</h3>
						<h3>: <?php echo $this->Currency->formatCurrency($product['Product']['product_price'], $product['User']['currency_code']); ?></h3>
						</span>
					</span>
				</td>
			</tr>
	        <tr>
	        	<td  class="widthSet" style="padding: 0 0px 0px 15px;">
					<div class="product-image">
						<?php if( !empty( $product['Product']['product_image'] ) ) { ?>
							<div class='wid-pic-avail'>
								<a href="<?php echo BASE_PATH . 'admin/products/' . $product['Product']['uuid']; ?>">
									<img src="<?php echo $product['Product']['product_image']; ?>" style="padding: 10px; max-height: 150px; max-width: 150px;"/>
								</a>
							</div>
						<?php } else { ?>
							<div class='wid-pic' >
								<img src='<?php echo BASE_PATH; ?>images/icons/ShipEarly_default_img.png' />
							</div>
						<?php } ?>			
						<?php if( $product['ProductImage'] ) { ?>
							<p>
							<?php foreach ($product['ProductImage'] as $productImage) { ?>
								<a class="fancybox" href="<?php echo $productImage['image_url']; ?>" data-fancybox-group="gallery">
									<img src="<?php echo $productImage['image_url']; ?>" alt="" style="max-width: 150px; max-height: 150px;"/>
								</a>
							<?php } ?>
							</p>
						<?php } ?>
					</div>
				</td>
				<td  style="padding: 0 0px 0px 15px;">
					<table class='pro-title productSold' style="border: none;">
						<tr>
							<td colspan="2">
								<div class='box-text' style="margin: 10px 20px 10px 0;">
									<div class="perfect-scrollbar"><?php echo $product['Product']['product_description']; ?></div>
									<div class="clear"></div>
								</div>
							</td>
						</tr>
					</table>
				</td>
			</tr>
		</table>
	</div>
</div>

<script type="text/javascript" src="<?php echo BASE_PATH; ?>source/jquery.fancybox.js"></script>
<link rel="stylesheet" type="text/css" href="<?php echo BASE_PATH; ?>source/jquery.fancybox.css" media="screen" />
<script>
	$(document).ready(function() {
		$('.fancybox').fancybox();
		// Change title type, overlay closing speed
		$(".fancybox-effects-a").fancybox({
			helpers: {
				title : {
					type : 'outside'
				},
				overlay : {
					speedOut : 0
				}
			}
		});
		// Disable opening and closing animations, change title type
		$(".fancybox-effects-b").fancybox({
			openEffect  : 'none',
			closeEffect	: 'none',

			helpers : {
				title : {
					type : 'over'
				}
			}
		});
	});
	function fetchLikeCount(url){
	    return $.Deferred(function(defer){
	        $.ajax({
	            dataType: 'jsonp',
	            url: 'https://api.facebook.com/method/fql.query?callback=callback',
	            data: {
	                query: 'SELECT like_count FROM link_stat WHERE url="' + url + '"',
	                format: 'JSON'
	            }
	        }).then(function(res){
	            try{
	                var count = res[0].like_count;
	                defer.resolve(count);
	            }catch(e){
	                reject();
	            }
	        }, reject);
	        function reject(){
	            defer.reject(';(');
	        };
	    }).promise();
	}	
<?php if(isset($facebook)) { ?>
	$(function(){                                        
		fetchLikeCount('<?php echo $facebook; ?>').always(function(res){
	        $('.social-count.fb-share .social-box').html(res+'<br/> fans');
	    });
	});
<?php } ?>


$("#addMedialink").click(function(){
	var media = "<?php echo BASE_PATH . $product['Product']['id'] . '/addmedia' ?>";
	window.location = media;
});
if( /Android|webOS|iPhone|iPad|iPod|BlackBerry/i.test(navigator.userAgent) ) {
	$(".product-status").selectpicker('mobile');
} else {
	$(".product-status").selectpicker();
}
$('.perfect-scrollbar').perfectScrollbar();
</script>
