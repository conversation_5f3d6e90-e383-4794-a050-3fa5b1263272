<?php if($shipearly_user['User']['user_type'] == 'admin') { ?>
	<div class='form_title media_info'>
		<h1>
			<a href="<?php echo BASE_PATH; ?>admin/products/<?php echo $uid['Product']['uuid']; ?>"> 
				<?php echo $uid['Product']['product_title']; ?> - Media information
			</a>
		</h1>
	</div>
<?php } else { ?>
	<div class='con1-head1 media_info'>
		<a href="<?php echo BASE_PATH; ?>products/<?php echo $uid['Product']['uuid']; ?>"> <h1 class='content-head'><?php echo $uid['Product']['product_title']; ?> - Media information</h1></a>
	</div>
	<br/>
	<br/>
<?php } ?>
<div class='shadow-box productSold'>
	<div class='title'>
	<img src='<?php echo BASE_PATH; ?>images/icons/icon-media.png' /> Media <?php if($uid['Product']['user_id'] == $shipearly_user['User']['id']) { echo "<a href='".BASE_PATH. $uid['Product']['id']."/addmedia'><button class='btn btn-primary reset-link'>Add Media</button></a>"; } ?> </div>
	<?php foreach ($product_details as $key => $value) {
		if($value['ProductMeta']['meta_key'] = 'media' ) { 
			$title = json_decode($value['ProductMeta']['meta_value']);
	 ?>
		<div class='box-text pro-review'>
				<img class="media" src='<?php echo BASE_PATH; ?>images/icons/preview_arrow.png' />
				<a href="<?php echo BASE_PATH. $value['ProductMeta']['id']; ?>/media"><h3 class="order-det widget-cls"><?php echo $title->title; ?></h3></a>
		</div>
	<?php }} ?>													
</div>