<?php 
	if(!$this->request->is('ajax')) {
		echo $this->Html->script( array( 'jquery.validate' ) );
	}
?>
<?php echo $this->Form->create('Productquery', array('id'=>'Productquery','class'=>'form-signin','inputDefaults'=>array("label"=>false,"div"=>false))); ?>
	<input type="hidden" id="productID" name="data[Productquery][id]" class="pop-text" value="<?php echo $product_details; ?>">
	<div class="form-group">
		<div class="controls">
	 		<?php echo $this->Form->label('comment',"Product enquiry" );?>
	 		<?php echo $this->Form->input('comment', array('id' => 'comment115', 'class'=>'pop-text', 'type' => 'textarea', 'error' => array('attributes' => array('class' => 'help-block')))); ?>
	 		<span class='help-block'></span>
		</div>
	</div>
 	<button type='submit' id='addproductquery' class='btn btn-primary reset-link'>Send</button>
</form>
<style type="text/css">
	.controls label {
		width: 100%;
	}
</style>
<script type="text/javascript">
	jQuery.validator.setDefaults({
		errorPlacement: function(error,element) {
			element.closest('.form-group').find('.help-block').html(error.text());
		}
	});

	$(document).ready(function() {
		$('#Productquery').validate({
			submitHandler: function(form) {
			$("#addproductquery").attr("disabled", "true");
				form.submit();
			},
			rules: {
				comment115: {
					required: true,
					minlength: 25,
					maxlength: 500
				}
			},
			messages: {
				comment115: {
					required: "Comment Field can't be empty",					
					minlength: "Comment should be minimum 25 chars",
					maxlength: "Comment should not exced 500 chars"
				}
			}
		});
	});
</script>
