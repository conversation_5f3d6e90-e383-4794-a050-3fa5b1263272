<?php
/**
 * @var AppView $this
 * @var string $title_for_layout
 * @var array $shipearly_user
 * @var bool $userHasEditPermission
 * @var array $all_products
 * @var string $orderStatus
 * @var int $noRecords
 * @var int $count_products
 * @var int $initialPage
 * @var array $paging
 */
?>
<?php
$saveButton = ($userHasEditPermission)
    ? $this->Form->button('Save', [
        'type' => 'button',
        'id' => 'saveButton',
        'class' => 'btn btn-primary',
    ])
    : '';

$syncButton = ($userHasEditPermission)
    ? $this->Form->button('Sync Products', [
        'type' => 'button',
        'id' => 'synchronize',
        'class' => 'btn btn-primary',
    ])
    : '';

$productStatusFilter = $this->Layout->productsFilterDropdown($orderStatus);

$bootboxEditFormClassName = ($userHasEditPermission)
    ? 'bootbox--form modal--view'
    : 'bootbox--form bootbox--form-disabled modal--view';
?>
<?php $this->start('css'); ?>
<style type="text/css">
#b2b_max_order_quantity, #b2b_min_order_quantity {
	margin: 0;
}
</style>
<?php $this->end(); ?>
<div class="clearfix">
	<div class="row align-items-center">
		<div class="col-12 d-flex flex-wrap justify-content-between align-items-center">
			<div><?php echo $this->Layout->contentHeader($title_for_layout); ?></div>
			<div class="d-flex flex-wrap">
				<div class="col-auto mb-2">
					<div class="d-flex align-items-center">
						<button type="button" class="btn btn-primary" id="searchFilterButton" data-tooltipster="<?= h(json_encode(['trigger' => 'hover', 'delay' => 100, 'zIndex' => 2147483648])) ?>" title="<?= __('Search and Filter') ?>">
							<i class="fas fa-search me-1"></i> <i class="fas fa-filter"></i>
						</button>
						<button type="button" class="btn btn-secondary" id="cancelFilterBtn" style="display: none; margin-left: 10px;">
							<?= __('Cancel') ?>
						</button>
					</div>
				</div>
				<div class="col-auto mb-2 ms-2">
					<?= $syncButton ?>
				</div>
				<div class="col-auto mb-2 ms-2">
					<?= $saveButton ?>
				</div>
				<div class="col-auto mb-2 ms-2">
				<?php if ($userHasEditPermission) { ?>
					<div class="dropdown">
						<button type="button" title="Import" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown">
							<span class="filter-option pull-left">Import</span>&nbsp;<span class="bs-caret"><span class="caret"></span></span>
						</button>
						<ul class="dropdown-menu">
							<li><?= $this->Html->link(
								__('Product Details'),
								['controller' => 'products', 'action' => 'import'],
								['id' => 'importProducts', 'class' => 'dropdown-item', 'data-title' => __('Import Product Files')]
							) ?></li>
							<li><?= $this->Html->link(
								__('Product Translations'),
								['controller' => 'products', 'action' => 'import_translations'],
								['id' => 'importProductTranslations', 'class' => 'dropdown-item', 'data-title' => __('Import Product Translations')]
							) ?></li>
							<li><?= $this->Html->link(
								__('Product Titles Sort'),
								['controller' => 'products', 'action' => 'productTitleSortImport'],
								['id' => 'importProductTitleSort', 'class' => 'dropdown-item', 'data-title' => __('Import Product Title Sort Order')]
							) ?></li>
							<li><?= $this->Html->link(
								__('Product Variants Sort'),
								['controller' => 'products', 'action' => 'productVariantSortImport'],
								['id' => 'importVariantsSortButton', 'class' => 'dropdown-item', 'data-title' => __('Import Product Variant Sort Order')]
							) ?></li>
						</ul>
					</div>
				<?php } ?>
				</div>
				<div class="col-auto mb-2 ms-2">
					<div class="dropdown">
						<button type="button" title="Export" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown">
							<span class="filter-option pull-left">Export</span>&nbsp;<span class="bs-caret"><span class="caret"></span></span>
						</button>
						<ul class="dropdown-menu">
							<li><?= $this->Html->link(
								__('Product Details'),
								['controller' => 'products', 'action' => 'export'],
								['id' => 'exportButton', 'class' => 'dropdown-item']
							) ?></li>
							<li><?= $this->Html->link(
								__('Product Translations'),
								['controller' => 'products', 'action' => 'export_translations'],
								['id' => 'exportProductTranslationsButton', 'class' => 'dropdown-item']
							) ?></li>
							<li><?= $this->Html->link(
								__('Product Titles Sort'),
								['controller' => 'products', 'action' => 'productTitleSortExport'],
								['class' => 'dropdown-item']
							) ?></li>
							<li><?= $this->Html->link(
								__('Product Variants Sort'),
								['controller' => 'products', 'action' => 'productVariantSortExport'],
								['class' => 'dropdown-item']
							) ?></li>
						</ul>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
<div id="searchFilterPanel" style="display:none;">
    <div class="row mb-3">
        <div class="col-12">
            <?= $this->Form->create('Product', [
                'id' => 'ProductSearchForm',
                'default' => false,
                'class' => 'form-signin',
                'inputDefaults' => ['label' => false, 'div' => false],
            ]) ?>
                <?= $this->Form->input('search', [
                    'type' => 'text',
                    'id' => 'ProductSearch',
                    'placeholder' => __('Search') . '...',
                    'class' => 'form-control',
                    'style' => 'width: 100%',
                    'label' => ['class' => 'visually-hidden'],
                    'after' => $this->Form->submit(null, ['class' => 'submit', 'div' => false, 'style' => 'display: none;']),
                ]) ?>
            <?= $this->Form->end() ?>
        </div>
    </div>
    <div class="row mb-3">
        <div class="col-12">
            <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="addFilterButton" data-bs-toggle="dropdown" aria-expanded="false">
                <?= __('Add Filter +') ?>
            </button>
            <ul class="filter-dropdown-menu dropdown-menu" aria-labelledby="addFilterButton">
				<li><a class="dropdown-item" href="#" data-filter="collection"><?= __('Collections') ?></a></li>
				<li><a class="dropdown-item" href="#" data-filter="type"><?= __('Categories') ?></a></li>
				<?php foreach ($variantOptionsByName as $variantOptionId => $variantDetails): ?>
					<li>
						<a class="dropdown-item" href="#" data-filter="variantOption-<?= h($variantOptionId) ?>">
							<?= h($variantDetails['name']) ?>
                        </a>
                    </li>
                <?php endforeach; ?>
				<li><a class="dropdown-item" href="#" data-filter="stockOption"><?= __('Product Availability') ?></a></li>
				<li><a class="dropdown-item" href="#" data-filter="product_status"><?= __('Product Status') ?></a></li>
            </ul>

            <div id="filterTemplates" class="row g-2" style="margin-top:5px;" >
				<div id="collectionTemplate" class="col-auto" style="display:none;">
                    <?= $this->Form->input('collection', [
                        'id' => 'ProductFilterCollections',
                        'empty' => __('All Collections'),
                        'options' => $collectionOptions,
                        'value' => $this->request->query('collection'),
                        'class' => 'selectpicker form-control form-select',
                        'data-width' => '195px',
						'data-live-search' => true,
                        'label' => ['text' => __('Collections'), 'class' => 'visually-hidden'],
                    ]); ?>
                </div>
				<div id="typeTemplate" class="col-auto" style="display:none;">
                    <?= $this->Form->input('type', [
                        'id' => 'ProductFilterTypes',
                        'empty' => __('All Categories'),
                        'options' => $productTypeOptions,
                        'value' => $this->request->query('type'),
                        'class' => 'selectpicker form-control form-select',
                        'data-width' => '195px',
						'data-live-search' => true,
                        'label' => ['text' => __('Categories'), 'class' => 'visually-hidden'],
                    ]); ?>
                </div>
				<?php foreach ($variantOptionsByName as $variantOptionId => $variantDetails): ?>
					<div id="variantOption-<?= h($variantOptionId) ?>Template" class="col-auto" style="display:none;">
						<?= $this->Form->input('variants[' . h($variantOptionId) . ']', [
							'id' => 'ProductFilterVariant-' . h($variantOptionId),
							'type' => 'select',
							'options' => $variantDetails['values'],
							'data-placeholder' => __('All') . ' ' . h(Inflector::pluralize($variantDetails['name'])),
							'multiple' => true,
							'class' => 'selectpicker form-control form-select',
							'data-width' => '195px',
							'data-live-search' => 'true',
							'label' => ['text' => h($variantDetails['name']), 'class' => 'visually-hidden'],
							'value' => $this->request->query('variants[' . h($variantOptionId) . ']')
						]); ?>
					</div>
				<?php endforeach; ?>
				<div id="stockOptionTemplate" class="col-auto" style="display:none;">
					<?= $this->Form->input('stockOption', [
						'id' => 'ProductFilterStock',
						'title' => __('All Products'),
						'options' => $stockOptions,
						'value' => $this->request->query('stockOption'),
						'class' => 'selectpicker form-control form-select',
						'data-width' => '195px',
						'label' => ['text' => __('Stock Option'), 'class' => 'visually-hidden'],
					]); ?>
				</div>
				<div id="product_statusTemplate" class="col-auto" style="display:none;"><?= $productStatusFilter; ?></div>
            </div>
        </div>
    </div>
</div>
<div class="card">
	<?php echo $this->Form->create('Product', array(
		'id' => 'order_frm',
		'class' => 'form-signin',
		'inputDefaults' => array('label' => false, 'div' => false),
	)); ?>
		<div class="box-text">
			<input type="hidden" id="order_status" name="data[Products][product_status]" value="<?php echo $orderStatus; ?>" />
		</div>
		<div class="card-body">
			<div id="products_list" class="table-responsive">
				<?php echo $this->element('Products/productlist'); ?>
			</div>
			<?php echo $this->Layout->pagingControls($noRecords, $count_products, [
				'name' => 'data[Products][noRecords]',
				'onChange' => 'pagination();',
				'showSyncBar' => !empty($showSyncBar),
			]); ?>
			<!-- Hidden fields used by JS shipearlySort() -->
			<input type="hidden" id='pageNumber' name="pageNumber" value="<?php echo $initialPage; ?>" />
			<input type="hidden" id='sortField' name="sortField" value="Product.sort_order" />
			<input type="hidden" id='sortOrder' name="sortOrder" value="ASC" />
			<input type="submit" id='save' name="save" value="true" style="display:none;" />
				<?php echo $this->Form->end(); ?>
		</div>
</div>

<?php echo $this->Html->css('/simplePagination.css', ['inline' => false]); ?>
<?php echo $this->Html->script('/jquery.simplePagination.js', ['inline' => false]); ?>
<?php $this->start('script'); ?>
<script type="text/javascript">
$(function() {
	$(window).load(function(){
		var hash = window.location.hash;
		if (hash.length) {
			var pageNo = hash.substr(6);
			$('#pagination').pagination('selectPage', pageNo);
		}
	});

	$('#prdct-tbl').tablesorter({ widgets: ['zebra'] });

	$(document).on('click', '.acc-product-invoice', function(e) {
		e.preventDefault();
		$.get($(this).data('href'), function(data) {
			bootbox.dialog({
				title: 'Update Product Details',
				message: data,
				className: "<?= $bootboxEditFormClassName ?>"
			}).on('shown.bs.modal', function() {
				$('#perPage').val($('#noRecords').val());
				$('#pageNo').val(window.location.hash);
			});
		});
	}).on('click', '.pricing-product', function(e) {
		e.preventDefault();
		$.get($(this).data('href'), function(data) {
			bootbox.dialog({
				title: 'Dealer Pricing',
				message: data,
				className: "<?= $bootboxEditFormClassName ?>"
			});
		});
	});
});
</script>
<script>
$(document).ready(function() {
	$('#saveButton').click(function() {
		$('#save').click();
	});
    $('#searchFilterButton').click(function() {
        $('#searchFilterPanel').toggle();
        $('#cancelFilterBtn').toggle($('#searchFilterPanel').is(':visible'));

		if ($('#searchFilterButton').is(':visible')) {
			$('#ProductSearch').focus();
		}
    });
    $('#cancelFilterBtn').click(function() {
		updateTableToDefault();
    });
    $('.filter-dropdown-menu .dropdown-item').click(function(e) {
        e.preventDefault();
        var filterType = $(this).data('filter');
        $('#' + filterType + 'Template').show();
    });
	function updateTableToDefault() {
		window.location = $('#order_frm').attr('action');
	}
});
</script>
<script type="text/javascript">
	function pagination() {
		$('form#order_frm').submit();
	}
	function updateProduct(activity) {
		var flag = false;
		var no_of_rows = $('#no_of_rows').val();
		for( var k = 1; k < no_of_rows; k++ ) {
			if( $("#user_check_" + k).is(':checked') ) {
				flag = true;
			}
		}
		if( !flag ) {
			alert( "Please selece atleast one product." );
			return false;
		}
		if( confirm( "Are you sure want to " + activity + " this product?" ) ) {
			$('#activity').val(activity);
			$('form#order_frm').submit();
		}
	}
	function shipearlySort(field) {
		var current_field = $('#sortField').val();
		var current_order = $('#sortOrder').val();

		if(field == current_field && current_order == 'ASC') {
			order = 'DESC';
		} else if(field == current_field && current_order == 'DESC') {
			order = 'ASC';
		} else {
			order = 'DESC';
		}

		$('#sortField').val(field);
		$('#sortOrder').val(order);

		var pageNumber = $('#pageNumber').val();
		updateTable(pageNumber);
	}
	function updateTable(pageNumber) {
		var noRecords = <?php echo $noRecords; ?>;
		var order_status = $('#order_status').val();
		var count_products = "<?php echo $count_products; ?>";
		var selectedVariants = {};
        $('select[id^="ProductFilterVariant"]').each(function() {
            var variantId = $(this).attr('id').replace('ProductFilterVariant-', '');
            var selectedValues = $(this).val();
            if (!selectedValues || selectedValues.length === 0) {
                selectedVariants[variantId] = '';
            } else {
                selectedVariants[variantId] = selectedValues;
            }
        });
		$.ajax({
			type: "POST",
			url: "<?php echo BASE_PATH; ?>products/ajax_index/",
			data: {
				pageNumber: pageNumber,
				noRecords: noRecords,
				product_status: order_status,
				collection: $('#ProductFilterCollections').val(),
				categories: $('#ProductFilterTypes').val(),
				variants: selectedVariants,
				stock: $('#ProductFilterStock').val(),
				count_products: count_products,
				sortField: $('#sortField').val(),
				sortOrder: $('#sortOrder').val(),
				search: $('#ProductSearch').val()
			}
		}).done(function(data) {
			$('#products_list').html(data);
			$("#prdct-tbl").tablesorter({ widgets: ['zebra'] });
		});
	}
	$('#ProductFilterCollections, #ProductFilterTypes, #ProductFilterStock, select[id^="ProductFilterVariant"]').on('change', function() {
        filterTable();
    });
	$(function() {
		$('#ProductSearchForm').submit(function() {
			var pageNumber = 1;
			$('#pageNumber').val(pageNumber);
			updateTable(pageNumber);
		});
	});
	function filterTable() {
		var selectedValue = $('#productStatusSearch').val();
		$('#order_status').val(selectedValue);
		updateTable(1);
	}
	$('#exportButton, #exportProductTranslationsButton').click(function(e) {
		e.preventDefault();
		var orderStatus = $('#order_status').val();
		var collection = $('#ProductFilterCollections').val();
		var categories = $('#ProductFilterTypes').val();
		var stock = $('#ProductFilterStock').val();
		var variants = {};
		var search = $('#ProductSearch').val();

		$('select[id^="ProductFilterVariant"]').each(function() {
			var variantId = $(this).attr('id').replace('ProductFilterVariant-', '');
			var selectedValues = $(this).val();
			if (!selectedValues || selectedValues.length === 0) {
				variants[variantId] = '';
			} else {
				variants[variantId] = selectedValues;
			}
		});

		var exportUrl = new URL(this.href);
		exportUrl.searchParams.set('type', orderStatus);
		exportUrl.searchParams.set('collection', collection);
		exportUrl.searchParams.set('categories', categories);
		exportUrl.searchParams.set('stock', stock);
		exportUrl.searchParams.set('variants', JSON.stringify(variants));
		exportUrl.searchParams.set('search', search);

		window.location.href = exportUrl.toString();
	});
</script>
<script type="text/javascript">
$(function() {
	$('#importProducts, #importProductTranslations, #importProductTitleSort, #importVariantsSortButton').on('click', function(e) {
		e.preventDefault();
		const $this = $(this);

		const template = document.querySelector('#ProductImportFormTemplate').content.cloneNode(true);
		template.querySelector('form').action = $this.attr('href');

		bootbox.dialog({
			size: 'large',
			title: $this.data('title') || '<!-- Empty Title -->',
			message: template,
			className: 'bootbox--form',
			buttons: {
				Cancel: {
					label: "<?= __('Cancel') ?>",
					callback: function() {
						bootbox.hideAll();
					}
				},
				Submit: {
					label: "<?= __('Import') ?>",
					callback: function() {
						$(this).find('form input[type="submit"]').click();
						return false;
					}
				}
			}
		}).on('submit', 'form', function() {
			shipearlyPopup.showLoading();
		});
	});
});
</script>
<template id="ProductImportFormTemplate" style="display: none;">
<?php
	echo $this->Form->create('Product', [
		'url' => 'javascript:void(0);',
		'type' => 'file',
		'class' => 'bootbox-form',
		'inputDefaults' => ['label' => false, 'div' => false],
	]);
		echo $this->Html->para(null, __('Please upload the exported file with your changes'));
		echo $this->Form->file('upload', ['required' => true]);
		echo $this->Form->submit();
	echo $this->Form->end() . PHP_EOL;
?>
</template>
<?php $this->end(); ?>
