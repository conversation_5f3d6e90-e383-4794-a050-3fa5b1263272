<?php if($shipearly_user['User']['user_type'] == 'admin') { ?>
	<div class='form_title media_info'>
		<h1>
			<a href="<?php echo BASE_PATH; ?>admin/products/<?php echo $uid['Product']['uuid']; ?>"> 
				<?php echo $uid['Product']['product_title']; ?> - Media information
			</a>
		</h1>
	</div>
<?php } else { ?>
	<div class='media_info'>
		<a href="<?php echo BASE_PATH; ?>products/<?php echo $uid['Product']['uuid']; ?>"> <h1 class='content-head'><?php echo $uid['Product']['product_title']; ?> - Media information</h1></a>
	</div>
<?php } ?>
<div class="clear"></div>
<div class="report-box">
	<?php if($uid['Product']['user_id'] == $shipearly_user['User']['id']) { ?>
		<div class='export-box'>
			<a href="<?php echo BASE_PATH . $id; ?>/deletemedia"><button class='btn btn-primary reset-link' id='deleteMedia'>Delete media</button></a>
		</div>
		<div class='export-box'>
			<a href="<?php echo BASE_PATH . $id; ?>/editmedia"><button class='btn btn-primary reset-link'>Edit media</button></a>
		</div>
	<?php } ?>
	<div>
		<h2><?php echo $media->title; ?></h2>
		<p class="cms_content"><?php echo $media->content; ?></p>
	</div>
</div>
<script>
	$("#deleteMedia").click( function(e)
	{
		e.preventDefault();
		bootbox.confirm("Are you sure, you want to delete?", function(result) {
			if(result == true) {
				redirectURL = $("#deleteMedia").parent().attr('href');
				location.href = redirectURL;
			}
			bootbox.hideAll();
		});
	});
</script>
<style>
	.cms_content { margin-top:20px; padding:0 30px 0 10px; }
</style>