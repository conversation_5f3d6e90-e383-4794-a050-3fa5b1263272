<?php
/**
 * @var AppView $this
 * @var array $all_products
 * @var int $noRecords
 * @var int $count_products
 * @var array $paging
 */

if (empty($all_products)) {
	echo $this->element('message', array('title' => __('You have no products on your watch list'), 'message' => ''));
	return true;
}
?>
<div class="product-con marginTopRem">
	<?php echo $this->element('content_header'); ?>
	<div class="box-text pro-slider">
		<div id="products_list" class="table-responsive">
		<?php foreach ($all_products as $product) { ?>
			<div class="list">
				<?php echo $this->element('product', array(
					'product' => $product,
					'additional_class' => 'wid-single-con',
				), array(
					'cache' => array('config' => 'product', 'key' => 'ProductElement_' . $product['Product']['id'])
				)); ?>
			</div>
		<?php } ?>
		</div>
		<div class="clear-cls"></div>
	<?php if (!empty($paging)) { ?>
		<div id="pagination"></div>
		<script type="text/javascript" src="<?php echo BASE_PATH; ?>jquery.simplePagination.js"></script>
		<link type="text/css" rel="stylesheet" href="<?php echo BASE_PATH; ?>simplePagination.css"/>
		<script>
		$(function() {
			var noRecords = "<?php echo $noRecords; ?>";
			var count_products = "<?php echo $count_products; ?>";
			$('#pagination').pagination({
				items: count_products,
				itemsOnPage: noRecords,
				cssStyle: 'light-theme',
				onPageClick: function(pageNumber) {
					$.ajax({
						type: 'POST',
						url: "<?php echo Router::url(['controller' => 'products', 'action' => 'ajax_watch_products']); ?>",
						data: {
							pageNumber: pageNumber,
							noRecords: noRecords,
							count_products: count_products
						}
					}).done(function(data) {
						$('#products_list').html(data);
					});
				},
				onInit: window.paginationInit,
			});
		});
		</script>
	<?php } ?>
	</div>
</div>
