<?php
/**
 * @var AppView $this
 * @var array $reportSummaries
 * @var array $paging
 * @var int $totalRows
 */
?>
<div id="orders_list" class="table-responsive">
	<table id="commissionIndexTable" class="table table-striped table-hover">
		<thead>
			<tr>
				<th data-sorter="false" onclick="shipearlySort('Order.period_end')">Period Ending</th>
				<th data-sorter="false" onclick="shipearlySort('Order.report_count')"># of Payees</th>
				<th data-sorter="false" onclick="shipearlySort('Order.order_count')"># of Transactions</th>
				<th data-sorter="false" onclick="shipearlySort('Order.report_total')">Payment Due</th>
			</tr>
		</thead>
		<tbody>
		<?php foreach ($reportSummaries as $idx => $summary) { ?>
			<tr>
				<td>
					<?php echo $this->Html->link(
						'<span>' . date(DATE_FORMAT, strtotime($summary['Order']['period_end'])) . '</span>',
						['action' => 'dealerpayouts', $summary['Order']['period_end']],
						array(
							'class' => 'no-of-retails',
							'escapeTitle' => false,
						)
					); ?>
				</td>
				<td>
					<?php echo $summary['Order']['report_count']; ?>
				</td>
				<td>
					<?php echo $summary['Order']['order_count']; ?>
				</td>
				<td>
					<?php echo $this->Currency->formatCurrency($summary['Order']['report_total'], $summary['Brand']['currency_code']); ?>
				</td>
			</tr>
		<?php } ?>
		<?php if ($totalRows == 0) { ?>
			<tr><td colspan="10" style="text-align: center;">No Reports</td></tr>
		<?php } ?>
		</tbody>
	</table>
</div>
<?= $this->Layout->pagingControlsWithoutShow() ?>
<script type="text/javascript">
$('#pagination').pagination({
	items: <?php echo $totalRows; ?>,
	itemsOnPage: <?php echo $paging['rowsPerPage']; ?>,
	currentPage: <?php echo $paging['pageNum']; ?>,
	onPageClick: function(pageNumber, event) {
		$('#pageNumber').val(pageNumber);
		updateTable();
	},
	onInit: window.paginationInit,
});
</script>
