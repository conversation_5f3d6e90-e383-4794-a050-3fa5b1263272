<?php
/**
 * @var AppView $this
 * @var string $title_for_layout
 * @var string $userType
 * @var int $userId
 * @var int $totalRows
 * @var int $rowsPerPage
 */

$is_admin = ($this->request->param('prefix') === 'admin');
?>
<style type="text/css">
#js-splitpayments-list input[type="text"] {
	width: 100%;
}
</style>
<?php echo $this->element('content_header', ['class' => 'reports']); ?>
<div class="clear"></div>
<?php echo $this->Form->create('Report', array('id' => 'order_frm', 'default' => ($is_admin), 'inputDefaults' => array('label' => false, 'div' => false))); ?>
<?php if ($totalRows > 10) { ?>
<div class="select_div drop-down pull-left" style="padding-top:8px;">
	<?php echo $this->element('noRecords_select', array(
		'name' => 'data[Order][noRecords]',
		'noRecords' => $rowsPerPage,
		'count' => $totalRows,
		'onChange' => 'updateTable();',
	)); ?>
</div>
<div class="clear"></div>
<?php } ?>
<div class="card">
	<div class="card-body">
		<div id="js-splitpayments-list">
			<!-- Table from Ajax call -->
		</div>
	</div>
</div>
<!-- Hidden fields used by JS -->
<input type="hidden" id='pageNumber' name="pageNumber" value="1" />
<input type="hidden" id='sortField' name="sortField" value="DealerOrder.period_end" />
<input type="hidden" id='sortOrder' name="sortOrder" value="DESC" />
<?php if ($is_admin) { ?>
	<?php echo $this->Form->end(array('label' => 'Save', 'class' => 'btn btn-primary')); ?>
<?php } else { ?>
	<?php echo $this->Form->end(); ?>
<?php } ?>

<link type="text/css" rel="stylesheet" href="<?php echo BASE_PATH; ?>simplePagination.css"/>
<script type="text/javascript" src="<?php echo BASE_PATH; ?>jquery.simplePagination.js"></script>

<script type="text/javascript">
function shipearlySort(field) {
	var current_field = $('#sortField').val();
	var current_order = $('#sortOrder').val();
	
	if(field == current_field && current_order == 'ASC') {
		order = 'DESC';
	} else if(field == current_field && current_order == 'DESC') {
		order = 'ASC';
	} else {
		order = 'DESC';
	}
	
	$('#sortField').val(field);
	$('#sortOrder').val(order);
	
	var pageNumber = $('#pageNumber').val();
	updateTable();
}

function updateTable() {
	$.ajax({
		url: "<?php echo $this->Html->url(array('action' => 'ajax_splitpayments', ($is_admin) ? $userType : '', ($is_admin) ? $userId : '')); ?>",
		data: {
			pageNo: $('#pageNumber').val(),
			noRecords: $('#noRecords').val(),
			sortField: $('#sortField').val(),
			sortOrder: $('#sortOrder').val(),
			totalRows: <?php echo $totalRows; ?>
		}
	}).done(function(data) {
		$('#js-splitpayments-list').html(data);
	});
}

$(function() {
	updateTable();
});
</script>
