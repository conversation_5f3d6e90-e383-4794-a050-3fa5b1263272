<?php
/**
 * @var AppView $this
 * @var string $report_name
 * @var string $currentUrl
 * @var array $shipearly_user
 * @var bool $single_store
 * @var array $storesList
 * @var string $start_date
 * @var string $end_date
 * @var bool $show_report_type_option
 * @var bool $show_report_currency_option
 * @var array $salesRepOptions
 * @var array $brandList
 * @var string $report_type_option
 * @var array $ordersChartOptions
 * @var string $ordersChartData JSON
 * @var string $ordersTableData JSON
 * @var string $ordersTableColumns JSON
 * @var string $ordersTableColumnDefs JSON
 * @var array $currencyOptions
 * @var string $selectedCurrencyOption
 */

$userType = $shipearly_user['User']['user_type'];

$single_store = !empty($single_store);

$selectedCurrencyOption = $selectedCurrencyOption ?? '';

$start_date = format_datetime($start_date ?? '1 YEAR AGO', 'Y-m-d');
$end_date = format_datetime($end_date ?? 'TODAY', 'Y-m-d');
?>
<?php
echo $this->Html->css([
	'jquery.dataTables.min',
	'daterangepicker',
	'reports',
], ['inline' => false]);
echo $this->Html->script([
	'amcharts/amcharts',
	'amcharts/serial',
	'amcharts/themes/light',
	'jquery.dataTables.min',
	'moment',
	'daterangepicker',
	'reports',
], ['inline' => false]);
?>

<div class="report-header header-card card card-body">
	<div class="row">
		<div class="col-lg-12 col-md-12 col-12">
			<?php echo $this->Layout->breadcrumbHeader([
				'Reports' => BASE_PATH . 'reports',
				h($report_name),
			]); ?>
		</div>
	</div>
	<div class="row align-items-end g-2">
		<div class="col-lg-2 col-md-3 col-6">
			<label for="daterange"><?= __('Date Range') ?></label>
			<input type="text" id="daterange" name="daterange" value="<?php echo format_datetime($start_date, 'M d, Y') . ' - ' . format_datetime($end_date, 'M d, Y'); ?>" class="form-control" />
		</div>
		<div class="col-lg-2 col-md-3 col-6 <?php echo ($show_report_currency_option == 1) ? '' : 'hidden'; ?>">
			<div><label for="report_currency_option"><?= __('Order Currency') ?></label></div>
			<?php 
			echo $this->Form->select('currencyOption', $currencyOptions, [
				'id' => 'report_currency_option', 
				'class' => 'selectpicker form-control', 
				'empty' => false, 
				'selected' => $selectedCurrencyOption,
				'label' => 'Order Currency',
			]);
			?>
		</div>
		<div class="col-lg-2 col-md-3 col-6 <?php echo ($show_report_type_option == 1) ? '' : 'hidden'; ?>">
			<div><label for="report_type_option"><?= __('Order Type') ?></label></div>
			<select class="selectpicker form-control" id="report_type_option">
				<?php if ($userType === User::TYPE_SALES_REP || in_array($this->request->param('name'), ['salesbyretailers'], true)) : ?>
					<option value="" selected="selected">Retail</option>
					<option value="">Wholesale</option>
				<?php else : ?>
					<option value="" selected="selected">Direct</option>
					<option value="">Combined</option>
					<option value="">Retail</option>
					<option value="">Wholesale</option>
					<option value="">All</option>
				<?php endif; ?>
			</select>
		</div>
		<div class="col-lg-2 col-md-3 col-6 <?php echo !empty($storesList) ? '' : 'hidden'; ?>">
			<div><label for="storeIds"><?= __('Account') ?></label></div>
			<?php echo $this->Form->select('storeIds', $storesList, array(
				($single_store ? 'empty' : 'data-placeholder') => 'All',
				'id' => 'storeIds',
				'class' => 'selectpicker form-control',
				'multiple' => !$single_store,
				'data-live-search' => 'true',
				'data-dropup-auto' => 'false',
			)); ?>
		</div>
		<?php if ($userType !== User::TYPE_SALES_REP) : ?>
			<div class="col-lg-2 col-md-3 col-6 <?php echo !empty($salesRepOptions) ? '' : 'hidden'; ?>">
				<div><label for="salesRepOptions"><?= __('Sales Rep/Distributor') ?></label></div>
				<?php echo $this->Form->select('sales_rep', $salesRepOptions, array(
					'id' => 'sales_rep',
					'class' => 'selectpicker form-control',
					'data-placeholder' => 'All',
					'multiple' => true,
					'data-live-search' => 'true',
					'data-dropup-auto' => 'false',
				)); ?>
			</div>
		<?php else: ?>
			<div class="col-lg-2 col-md-3 col-6 <?php echo !empty($brandList) ? '' : 'hidden'; ?>">
				<div><label for="brandList"><?= __('Brand') ?></label></div>
				<?php echo $this->Form->select('brand', $brandList, array(
					'id' => 'brand_list',
					'class' => 'selectpicker form-control',
					($single_store ? 'empty' : 'data-placeholder') => 'All',
					'multiple' => !$single_store,
					'data-live-search' => 'true',
					'data-dropup-auto' => 'false',
				)); ?>
			</div>
		<?php endif; ?>
		<div class="col-auto ms-auto">
			<a class="btn btn-primary" href="#" id="export_report"><?= __('Export') ?></a>
		</div>
	</div>
</div>
<div class="card card-body chart">
	<div id="chartdiv">
	</div>
	<div id="tablediv" class="card-body border-top">
		<table id="table" class="display">
			<thead>
			</thead>
		</table>
	</div>
</div>
<?php $this->start('script'); ?>
<script type="text/javascript">
var daterange = {
	start: moment('<?php echo $start_date; ?>').format('YYYY-MM-DD'),
	end: moment('<?php echo $end_date; ?>').format('YYYY-MM-DD')
};

var reportoptions = {
	'data': <?php echo $ordersChartData; ?>,
	'valueAxes': {
		'title': '<?php echo $ordersChartOptions["title"]; ?>'
	},
	'startDuration': 2,
	'graphs': {
		'valueField': '<?php echo $ordersChartOptions["valueField"]; ?>'
	},
	'categoryField': '<?php echo $ordersChartOptions["categoryField"]; ?>'
};

var tableoptions = {
	"data": <?php echo $ordersTableData; ?>,
	"columns": <?php echo $ordersTableColumns; ?>,
	"columndefs": <?php echo $ordersTableColumnDefs; ?>,
	"base_url": "<?php echo BASE_PATH; ?>",
	"currencyCode": "<?= $selectedCurrencyOption; ?>",
	"file_name": "<?php echo "{$report_name}_{$selectedCurrencyOption}_"; ?>" + moment(daterange.start).format("MMM_D_YYYY") + "to" + moment(daterange.end).format("MMM_D_YYYY")
};

<?php if (isset($report_type_option)) { ?>
	$("#report_type_option option").filter(function() {
		return $.trim($(this).text()) == '<?php echo $report_type_option; ?>';
	}).prop('selected', true);
	$('#report_type_option').selectpicker('refresh');
<?php } ?>

$(function() {
	$('.selectpicker').selectpicker();

	$('#report_type_option, #report_currency_option').change(function(e) {
		e.preventDefault();
		loadDetails();
	});

	var reports = new Reports('chartdiv');

	//Draw Chart
	reports.setChartOption(reportoptions);
	reports.drawChart();

	//Draw Table
	reports.setTableOption(tableoptions);
	reports.drawTable();

	//Set DateRange
	$('.input-daterange').datepicker({ "language": moment.locale() });
	$('#daterangestart').daterangepicker({
		locale: {
			format: 'MMM D, YYYY'
		},
		singleDatePicker: true
	});
	$('#daterangeend').daterangepicker({
		locale: {
			format: 'MMM D, YYYY'
		},
		singleDatePicker: true
	});
	$('input[name="daterange"]').daterangepicker({
		locale: {
			format: 'MMM D, YYYY'
		},
		startDate: moment(daterange.start).format("MMM D, YYYY"),
		endDate: moment(daterange.end).format("MMM D, YYYY"),
		"linkedCalendars": false,
		"autoApply": true,
		"opens": "center",
		"showDropdowns": true,
		ranges: {
			'Today': [moment(), moment()],
			'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
			'Last 7 Days': [moment().subtract(6, 'days'), moment()],
			'Last 30 Days': [moment().subtract(29, 'days'), moment()],
			'This Month': [moment().startOf('month'), moment().endOf('month')],
			'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
		}
	}, function(start, end, label) {
		daterange.start = moment(start).format("YYYY-MM-DD");
		daterange.end = moment(end).format("YYYY-MM-DD");
		tableoptions.file_name = "<?php echo $report_name; ?>" + moment(start).format("MMM_D_YYYY") + "to" + moment(end).format("MMM_D_YYYY");
		loadDetails();
	});

	$('#storeIds, #sales_rep, #brand_list').on('change', function() {
		loadDetails();
	});

	loadDetails();
});

function loadDetails() {
	//Set Store ID
	var store_id = "";
	if ($('#storeIds').val() != undefined) {
		store_id = $('#storeIds').val();
	}
	var sales_rep = "";
    if ($('#sales_rep').val() != undefined) {
        sales_rep = $('#sales_rep').val();
    }
	var brand_list = "";
	if ($('#brand_list').val() != undefined) {
        brand_list = $('#brand_list').val();
    }

	new Promise(function(resolve, reject) {
		$.ajax({
			type: "POST",
			url: "<?php echo $currentUrl; ?>",
			data: { 
				"daterange": daterange, 
				"reporttypeoption": $("#report_type_option option:selected").text(), 
				"store_id": store_id,
				"selectedCurrencyOption": $("#report_currency_option option:selected").text(),
				"sales_rep": sales_rep,
				"brand_list": brand_list,
			},
			dataType: 'json',
			success: function(result) {
				if (result.success) {
					resolve(result);
				}
			},
			error: function(request, status, error) {
				console.error("Error processing page");
			}
		});
	}).then(function(result) {
		var reports = new Reports('chartdiv');

		//Draw Chart
		reportoptions.valueAxes.title = result.ordersChartOptions.title;
		reportoptions.data = result.ordersChartData;
		reports.setChartOption(reportoptions);
		reports.drawChart();

		//Draw Table
		tableoptions.data = result.ordersTableData;
		tableoptions.columndefs = result.ordersTableColumnDefs;
		tableoptions.currencyCode = $("#report_currency_option option:selected").text();
		reports.setTableOption(tableoptions);
		reports.reDrawTable();
	});
}
</script>
<?php $this->end(); ?>
