<?php
/**
 * @var AppView $this
 * @var string $title_for_layout
 * @var array $queryParams
 * @var bool $userHasEditPermission
 * @var int $count
 * @var array $retailers
 * @var string[] $brandOptions
 * @var string[] $tierOptions
 */
?>

<?php
echo $this->Html->css(array('/boostrap/css/bootstrap-checkbox'));
echo $this->Html->script(array('/boostrap/js/bootstrap-checkbox'));
?>
<?php
$saveButton = ($userHasEditPermission)
    ? $this->Form->button('Save', [
        'type' => 'submit',
        'id' => 'save',
        'name' => 'save',
        'value' => 'save',
        'class' => 'btn btn-primary rp-sbtn',
    ])
    : '';
?>
<style type="text/css">
.filtermenu li a {
	color: #FFFFFF;
}
</style>
<?php echo $this->Form->create('ManufacturerRetailer', array(
	'id' => 'order_frm',
	'class' => 'form-signin',
	'inputDefaults' => array('label' => false, 'div' => false),
)); ?>
<div class="row align-items-center my-2">
	<div class="col-sm-auto col-12 me-auto" id="retailer_details">
		<h1 class="content-head"><?php echo $title_for_layout; ?></h1>
	</div>
	<div class="col-sm-auto col-12 row">
		<div class="col-sm-auto col-12">
			<div class="usersearch search">
				<input id="RetailerSearch" type="text" class="input" name="search" value="<?php echo $queryParams['search']; ?>" placeholder="<?= __('Search'); ?>..." style="width: 240px"/>
				<input type="submit" class="submit" value="" />
			</div>
		</div>
	</div>
</div>
<div class="clearfix">
	<div class="d-flex flex-wrap justify-content-end">
		<div class="col-auto">
			<div class="select_div drop-down pull-right">
				<?php echo $this->Form->input('brand', array(
					'type' => 'select',
					'name' => 'brand',
					'options' => $brandOptions,
					'value' => $queryParams['brand'],
					'id' => 'BrandFilter',
					'class' => 'rows',
				)); ?>
			</div>
		</div>
		<div class="col-auto ms-2">
			<div style="float: right;">
				<?= $saveButton ?>
			</div>
		</div>
	</div>
</div>
<div class="card mt-2">
	<div class="card-body">
		<div class='clear-cls'></div>
		<div id="products_list" class="table-responsive">
			<?php echo $this->element('Retailers/ajax_index_salesrep', compact('retailers', 'tierOptions')); ?>
		</div>
		<?php echo $this->Layout->pagingControls($queryParams['show'], $count, [
			'name' => 'show',
		]); ?>
	</div>
</div>
<!-- Hidden fields used by JS shipearlySort -->
<input type="hidden" id="pageNumber" name="page" value="<?php echo $queryParams['page']; ?>" />
<input type="hidden" id="sortField" name="sort" value="<?php echo $queryParams['sort']; ?>" />
<input type="hidden" id="sortOrder" name="order" value="<?php echo $queryParams['order']; ?>" />
<?php echo $this->Form->end(); ?>
<script type="text/javascript" src="<?php echo BASE_PATH; ?>jquery.simplePagination.js"></script>
<link type="text/css" rel="stylesheet" href="<?php echo BASE_PATH; ?>simplePagination.css"/>
<script>
function shipearlySort(field) {
	var current_field = $('#sortField').val();
	var current_order = $('#sortOrder').val();

	if(field == current_field && current_order == 'ASC') {
		order = 'DESC';
	} else if(field == current_field && current_order == 'DESC') {
		order = 'ASC';
	} else {
		order = 'DESC';
	}

	$('#sortField').val(field);
	$('#sortOrder').val(order);

	updateTable();
}

function updateTable() {
	var $form = $('form#order_frm');
	var $getInputs = $form.find('input, select').not('[name^="data["], [name="_method"], [type="submit"]');
	$.get($form.attr('action'), $getInputs.serialize(), function(data) {
		$('#products_list').html(data);
		initToggle();
	});
}

$(function() {
	$('.search .submit').click(function(e) {
		e.preventDefault();
		submitFormAsGet();
	});

	$('#noRecords, #BrandFilter').change(function(e) {
		e.preventDefault();
		filterTable();
	});

	$('.filtermenu').on('click', '.filter-point', function(e) {
		e.preventDefault();
		var $this = $(this);

		$('.filtermenu .filter-point').removeClass('on');
		$this.addClass('on');

		$('#StatusFilter').val($this.data('value'));
		filterTable();
	});

	function filterTable() {
		$('#pageNumber').val(1);
		updateTable();
	}

	function submitFormAsGet() {
		$('#pageNumber').val(1);
		var $form = $('form#order_frm');

		var $postInputs = $form.find(':input').filter('[name^="data["], [name="_method"], [type="submit"]');
		$postInputs.prop('disabled', true);

		$form.attr('method', 'get').submit();
	}
});
</script>
