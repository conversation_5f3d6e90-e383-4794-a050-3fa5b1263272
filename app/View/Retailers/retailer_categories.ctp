<?php

use ShipEarlyApp\Lib\Utility\SupportedLanguages;

/**
 * @var AppView $this
 * @var array $shipearly_user
 * @var array $category
 * @var array $retailers
 * @var array $paging
 * @var string $search
 */
?>

<?php
$categoryId = $category['RetailCategory']['id'];

$imageUrl = $category['RetailCategory']['image_url'];
$previousImage = basename($imageUrl);

$selectedLocale = $this->request->query('lang') ?: SupportedLanguages::DEFAULT_LOCALE;
$languageByLocale = SupportedLanguages::getOptionsByLocale();

$isEditable = $this->Permissions->userHasPermission($shipearly_user['User'], Permissions::NAME_RETAILERS, Permissions::LEVEL_EDIT);
?>

<style>
#category-title-input {
    width: auto;
    border: 1px solid #ccc;
    padding: 5px;
}
.editable-content {
    border: 1px solid #ccc;
    padding: 10px;
    font-size: 16px;
    color: #333;
    outline: none;
    border-radius: 5px;
    margin-top: 10px;
    height: 30px;
    display: flex;
    align-items: center;
}
.editable-content:focus {
    border-color: #007bff;
    background-color: #fff;
}
.editable-content[contenteditable="true"] {
    cursor: text;
}
.image-upload-box {
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #ddd;
    border-radius: 10px;
    height: 200px;
    cursor: pointer;
    transition: border-color 0.3s;
}
.upload-label {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    color: #666;
    font-size: 16px;
    pointer-events: none;
}
.upload-label span {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 5px;
}
#collection_image {
    display: none;
}
.collection-image-preview {
    width: 200px;
    height: auto;
    max-height: 250px;
    object-fit: cover;
}
.save-button {
    position: absolute;
    right: 30px;
}
.upload-position {
    position: absolute;
    top: 0px;
    right: 10px;
}
.sortable-list tr {
    cursor: grab;
}
#retailerTable tbody tr:hover .image-box img {
    filter: grayscale(100%);
}
</style>
<div class="container-fluid">
    <div class="mt-3 d-flex justify-content-between align-items-center flex-wrap">
        <div class="me-3 overflow-hidden">
            <h1 class="content-head mb-0 text-truncate" style="white-space: nowrap;">
                <?= h($category['RetailCategory'][$selectedLocale]['title'] ?? $category['RetailCategory']['title']); ?>
            </h1>
        </div>

        <?php if ($isEditable): ?>
            <div class="d-flex align-items-center">
                <div style="margin-right: 6rem;">
                    <?= $this->Form->input('locale', [
                        'id' => 'locale',
                        'type' => 'select',
                        'default' => $selectedLocale,
                        'options' => $languageByLocale,
                        'label' => false,
                        'class' => 'form-select',
                        'style' => 'height: 33px; min-width: 120px;',
                    ]); ?>
                </div>
                <button type="button" id="save-button" class="btn btn-primary save-button">
                    <?= __('Update') ?>
                </button>
            </div>
        <?php endif; ?>
    </div>
    <div class="d-flex flex-column-reverse flex-md-row gap-2">
        <div class="flex-grow-1">
            <?php if ($isEditable) { ?>
            <div class="card mt-3">
                <div class="card-body">
                    <div class="clearfix">
                        <h3><?= __('Category Title') ?></h3>
                        <input
                            type="text"
                            id="category-title-input"
                            class="editable-content"
                            value="<?= h($category['RetailCategory'][$selectedLocale]['title'] ?? $category['RetailCategory']['title']) ?>"
                            style="width: 100%;"
                        />
                    </div>
                </div>
            </div>
            <?php } ?>
            <div class="card mt-3">
                <div class="card-body">
                    <div class="clearfix mt-3">
                    <?php if ($isEditable) { ?>
                        <h3><?= __('Search Retailers') ?></h3>
                        <div class="input-group" style="margin-top: 10px;">
                            <span class="input-group-text">
                                <i class="fas fa-search"></i>
                            </span>
                            <input type="text"
                                placeholder="<?= __('Search retailers') ?>" 
                                class="form-control js-retailer-search" 
                                aria-label="<?= __('Retailer Search') ?>" />

                            <button type="button" class="btn btn-primary js-add-retailers">
                                <?= __('Add Selected') ?>
                            </button>
                            
                            <button type="button" class="btn btn-secondary js-browse-retailers" style="margin-left: 5px;">
                                <?= __('Browse Retailers') ?>
                            </button>
                        </div>
                        <div class="selected-retailers-list d-flex flex-wrap mt-3"></div>
                    <?php } ?>
                    </div>
                    <div class="table-responsive mt-3">
                        <table id="retailerTable" class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th style="text-align: left;" data-sorter="false" onclick="shipearlySort('Retailer.company_name')">
                                        <?php echo __('Retailer Name'); ?>
                                    </th>
                                    <th><?php echo __('Status'); ?></th>
                                    <?php if ($isEditable): ?>
                                        <th><?php echo __('Actions'); ?></th>
                                    <?php endif; ?>
                                </tr>
                            </thead>
                            <tbody id="retailer-table-body" class="<?= $isEditable ? 'sortable-list' : '' ?>">
                                <?php if (!empty($retailers)): ?>
                                    <?php foreach ($retailers as $retailer):
                                    ?>
                                        <tr data-retailer-id="<?= h($retailer['Retailer']['id']) ?>">
                                            <td class="col-title">
                                                <div style="display: flex; align-items: center;">
                                                    <?php
                                                    $imageSrc = !empty($retailer['Retailer']['avatar'])
                                                        ? BASE_PATH . 'files/users/' . $retailer['Retailer']['avatar']
                                                        : BASE_PATH . 'images/no_img.gif';
                                                    ?>
                                                    <div class="image-box">
                                                        <img src="<?= $imageSrc ?>" alt="<?= h($retailer['Retailer']['company_name']) ?>" class="product-thumbnail" />
                                                    </div>
                                                    <a href="<?= $this->Url->build(['controller' => 'users', 'action' => 'contact', 'id' => $retailer['Retailer']['id']]) ?>">
                                                        <div>
                                                            <strong style="display: block; color: black;"><?= h($retailer['Retailer']['company_name']) ?></strong>
                                                        </div>
                                                    </a>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="fulfillment-pill <?= h(strtolower($retailer['Retailer']['status'])) ?>">
                                                    <?= h($retailer['Retailer']['status']) ?>
                                                </span>
                                            </td>
                                            <?php if ($isEditable): ?>
                                                <td>
                                                    <button class="btn js-remove-retailer" aria-label="<?= __('Remove Retailer') ?>" data-retailer-id="<?= h($retailer['Retailer']['id']) ?>">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </td>
                                            <?php endif; ?>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="<?= $isEditable ? 3 : 2 ?>"><?= __('No retailers found in this category.'); ?></td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="d-flex flex-column gap-2 flex-basis-min">
           <div class="card retailer-details h-40 mt-3">
                <div class="card-body mt-3" style="width: 300px;">
                    <h3><?= __('Category Image') ?></h3>
                    <form id="signup" action="<?php echo Router::url(['controller' => 'retailers', 'action' => 'uploadCategoryImage', $categoryId]); ?>" method="post" enctype="multipart/form-data">
                        <div class="form-group mt-3">
                            <div class="image-upload-box">
                                <output id="collection-image-list">
                                    <?php if (!empty($category['RetailCategory']['image_url'])) { ?>
                                        <img class="collection-image-preview" src="<?= h($imageUrl) ?>" title="<?= h($previousImage) ?>" style="max-width: 100%;" />
                                        <?php if ($isEditable) { ?>
                                        <div>
                                            <button type="button" id="upload-collection-click" class="upload-click upload-position btn btn-primary mt-3"><?= __('Edit'); ?></button>
                                        </div>
                                        <?php } ?>
                                    <?php } else { ?>
                                        <?php if ($isEditable) { ?>
                                        <span>
                                            <button type="button" id="upload-collection-click" class="upload-click btn btn-primary"><?= __('Add Image'); ?></button>
                                        </span>
                                        <?php } ?>
                                    <?php } ?>
                                </output>
                                <?php echo $this->Form->input('collection_image', [
                                    'id' => 'collection_image',
                                    'type' => 'file',
                                    'class' => 'pop-text',
                                    'label' => false,
                                    'style' => 'display: none;',
                                ]); ?>
                                <?php echo $this->Form->input('hidden_collection_image', [
                                    'id' => 'hidden_collection_image',
                                    'type' => 'hidden',
                                    'value' => $category['RetailCategory']['image_url'],
                                    'label' => false,
                                ]); ?>
                            </div>
                            <span class="help-block" id="invalid-collection-image"></span>
                        </div>
                        <button type="button" id="cancel-button" class="btn btn-secondary" style="display: none;"><?= __('Cancel') ?></button>
                    </form>
                </div>
            </div>
            <div class="card retailer-details h-40">
                <div class="card-body mt-3" style="width: 300px;">
                    <h3><?= __('Category Color') ?></h3>
                    <div class="mt-3">
                        <?= $this->Form->control('color', [
                            'id' => 'categoryColor',
                            'type' => 'color',
                            'default' => '#FFFFFF',
                            'value' => !empty($category['RetailCategory']['color']) ? $category['RetailCategory']['color'] : '#FFFFFF',
                            'label' => ['text' => __('Category Color'), 'class' => 'form-label visually-hidden'],
                            'style' => 'width: 100%;',
                            'class' => 'form-control',
                        ]) ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$this->Html->css('/simplePagination.css', ['plugin' => false, 'inline' => false]);
$this->Html->script('/jquery.simplePagination.js', ['plugin' => false, 'inline' => false]);
?>
<?= $this->Layout->pagingControlsWithoutShow() ?>
<input type="hidden" id="noRecords" value="<?php echo h($paging['limit']); ?>" />
<input type="hidden" id="pageNumber" value="<?php echo h($paging['current']); ?>" />
<input type="hidden" id="counts" value="<?php echo h($paging['total']); ?>" />

<script>
    const categoryTitlesByLocale = <?= json_encode([
        'eng' => $category['RetailCategory']['eng']['title'] ?? '',
        'fra' => $category['RetailCategory']['fra']['title'] ?? '',
        'spa' => $category['RetailCategory']['spa']['title'] ?? '',
        'deu' => $category['RetailCategory']['deu']['title'] ?? '',
        'ita' => $category['RetailCategory']['ita']['title'] ?? '',
        'nld' => $category['RetailCategory']['nld']['title'] ?? '',
        'por' => $category['RetailCategory']['por']['title'] ?? '',
    ]) ?>;

    $(document).ready(function() {
        $('#locale').change(function() {
            const selectedLocale = $(this).val();
            const localizedTitle = categoryTitlesByLocale[selectedLocale] || '';
            $('.content-head').text(localizedTitle);
            $('#category-title-input').val(localizedTitle);
        });
    });
</script>
<?php if ($isEditable) { ?>
    <script>
        $(document).ready(function() {
            var sortable = new Sortable(document.getElementById('retailer-table-body'), {
                handle: 'td',
                onEnd: function(evt) {
                    var retailerOrder = [];
                    $('#retailer-table-body tr').each(function() {
                        retailerOrder.push($(this).data('retailer-id'));
                    });
                    $.ajax({
                        type: 'POST',
                        url: "<?= $this->Url->build(['controller' => 'retailers', 'action' => 'update_category_retailer_order', $categoryId]); ?>",
                        data: {
                            order: retailerOrder,
                        },
                        success: function(response) {
                            if (response.success) {
                                alert('Retailer order updated successfully.');
                            } else {
                                alert('Failed to update retailer order.');
                            }
                        },
                        dataType: 'json'
                    });
                }
            });
        });
    </script>
<?php } ?>
<script type="text/javascript">
    $('#invalid-collection-image').hide();
    var collectionImage = "<?php echo htmlspecialchars($imageUrl); ?>";
    var previousImage = "<?php echo htmlspecialchars($previousImage); ?>";

    $("#save-button").click(function() {
        $('#signup').submit();
    });
    $('#signup').submit(function(e) {
        e.preventDefault();

        if (!$("#signup").valid()) {
            $('.msg_panel').html(FlashErrorUpdate.replace("{{FlashErrorUpdate}}", "Please make sure that you have entered all the mandatory fields in all the sections"));
            return false;
        }

        if ((collectionImage == '') && (!$("#collection_image").val())) {
            $('#invalid-collection-image').show();
            $('#invalid-collection-image').html('Please upload a category image');
            return false;
        } else {
            $('#invalid-collection-image').hide();
        }

        var formData = new FormData(this);

        $.ajax({
            type: 'POST',
            url: $(this).attr('action'),
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                alert('Category updated successfully.');
            },
            error: function(xhr, status, error) {
                alert('Failed to upload category image: ' + error);
            }
        });
    });

    $("#collection_image").change(function() {
        var file = this.files[0];
        if (file) {
            $('#collection-image-list').html(
                $('<img class="collection-image-preview">')
                    .attr('src', URL.createObjectURL(file))
                    .attr('title', encodeURI(file.name))
            );
            $('#cancel-button').show();
        } else {
            $('#cancel-button').hide();
        }
    });

    $(".upload-click").click(function() {
        $("#collection_image").trigger("click");
    });

    $("#cancel-button").click(function() {
        $('#collection_image').val('');
        if (previousImage) {
            $('#collection-image-list').html(
                $('<img class="collection-image-preview">')
                    .attr('src', collectionImage)
                    .attr('title', encodeURI(previousImage))
            );
        } else {
            $('#collection-image-list').html('');
        }
        $('#invalid-collection-image').hide();
        $('#cancel-button').hide();
    });
</script>
<script>
$('#categoryColor').on('change', function () {
    var newColor = $(this).val();
    var categoryId = <?= json_encode($categoryId); ?>;

    $.ajax({
        type: 'POST',
        url: "<?= Router::url(['controller' => 'retailers', 'action' => 'updateCategoryColor']); ?>",
        data: {
            id: categoryId,
            color: newColor
        },
        dataType: 'json'
    }).done(function(response) {
        if (response.success) {
            console.log('Color updated successfully.');
        } else {
            alert(response.message || 'Failed to update color.');
        }
    }).fail(function() {
        alert('Error updating category color.');
    });
});
</script>
<script>
$('#retailerTable').on('click', '.js-remove-retailer', function(e) {
    e.preventDefault();
    var $button = $(this);
    var retailerId = $button.data('retailer-id');
    var categoryId = <?= json_encode($categoryId); ?>;

    if (confirm('Are you sure you want to remove this retailer from the category?')) {
        $.ajax({
            type: 'POST',
            url: "<?= Router::url(['controller' => 'retailers', 'action' => 'ajax_remove_category_retailer']); ?>",
            data: {
                category_id: categoryId,
                retailer_id: retailerId
            },
            dataType: 'json'
        }).done(function(data) {
            if (data.success) {
                $button.closest('tr').remove();
                alert('Retailer removed successfully.');
            } else {
                alert('Failed to remove retailer. Please try again.');
            }
        }).fail(function() {
            alert('Error removing retailer. Please try again.');
        });
    }
});
$('#save-button').on('click', function() {
    var newTitle = $('#category-title-input').val();
    var categoryId = <?php echo json_encode($categoryId); ?>;
    var locale = $('#locale').val();

    $.ajax({
        type: 'POST',
        url: "<?php echo Router::url(['controller' => 'retailers', 'action' => 'updateCategoriesTitle']); ?>",
        data: {
            id: categoryId,
            title: newTitle,
            locale: locale
        },
        dataType: 'json'
    });
});
</script>
<script type="text/javascript">
$(document).on('click', '.js-browse-retailers', function () {
    $.ajax({
        url: "<?= $this->Url->build(['controller' => 'retailers', 'action' => 'ajax_browse_retailers', $categoryId]); ?>",
        method: 'GET',
        success: function (html) {
            bootbox.dialog({
                title: "Browse Retailers",
                message: html,
                size: 'large',
                buttons: {
                    cancel: {
                        label: "Cancel",
                        className: 'btn-secondary'
                    },
                    confirm: {
                        label: "Add Selected",
                        className: 'btn-primary',
                        callback: function () {
                            let selectedIds = [];
                            $('.retailer-checkbox:checked').each(function () {
                                selectedIds.push($(this).val());
                            });

                            if (selectedIds.length === 0) {
                                alert('Please select at least one retailer.');
                                return false;
                            }
                            $.ajax({
                                type: 'POST',
                                url: "<?= $this->Url->build(['controller' => 'retailers', 'action' => 'ajax_add_category_retailers']); ?>",
                                contentType: 'application/json',
                                dataType: 'json',
                                data: JSON.stringify({
                                    category_id: <?= json_encode($categoryId); ?>,
                                    retailer_ids: selectedIds,
                                    mode: 'replace',
                                }),
                                success: function (res) {
                                    if (res.success) {
                                        location.reload();
                                    } else {
                                        alert(res.message || "Error adding retailers.");
                                    }
                                },
                                error: function () {
                                    alert("Something went wrong while adding retailers.");
                                }
                            });
                        }
                    }
                }
            });
        },
        error: function () {
            alert("Failed to load retailer list.");
        }
    });
});
$(function() {
    const selectedRetailers = [];
    $('.js-retailer-search').autocomplete({
        source: function(request, response) {
            $.ajax({
                type: 'GET',
                url: "<?php echo Router::url(['controller' => 'retailers', 'action' => 'ajax_add_category_retailer_search', $categoryId]); ?>",
                data: { 
                    term: request.term,
                    exclude: selectedRetailers.map(r => ({ retailer_id: r.id }))
                },
                dataType: 'json'
            }).done(function(data) {
                response(data);
            }).fail(function(xhr, status, error) {
                console.error("Retailer search failed:", status, error);
                response([]);
            });
        },
        select: function(event, ui) {
            const retailer = ui.item.Retailer;

            if (!selectedRetailers.some(r => r.id === retailer.id)) {
                selectedRetailers.push(retailer);

                $('.selected-retailers-list').append(
                    `<div class="selected-retailer js-added-retailer" data-retailer-id="${retailer.id}" style="margin-left:10px;">
                        ${retailer.company_name} <button class="remove-retailer" data-id="${retailer.id}">×</button>
                    </div>`
                );
            }

            $(this).val('');
            return false;
        },
        minLength: 2
    });

    $(document).on('click', '.remove-retailer', function() {
        const retailerId = $(this).data('id');
        $(this).parent().remove();
        const index = selectedRetailers.findIndex(r => r.id === retailerId);
        if (index !== -1) selectedRetailers.splice(index, 1);
    });

    $('.js-add-retailers').click(function(e) {
        e.preventDefault();

        const categoryId = <?php echo json_encode($categoryId); ?>;
        const retailerIds = selectedRetailers.map(r => r.id);

        if (retailerIds.length === 0) {
            alert("Please select at least one retailer.");
            return;
        }

        $.ajax({
            type: 'POST',
            url: "<?php echo Router::url(['controller' => 'retailers', 'action' => 'ajax_add_category_retailers']); ?>",
            contentType: 'application/json',
            data: JSON.stringify({
                category_id: categoryId,
                retailer_ids: retailerIds,
                mode: 'append',
            }),
            dataType: 'json'
        }).done(function(data) {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || 'Some retailers were not added.');
            }
        }).fail(function() {
            alert('Error adding retailers to category.');
        });
    });
});
</script>
<script>
function shipearlySort(field) {
    var $field = $('#sortField');
    var $order = $('#sortOrder');

    var order = 'DESC';
    if (field === $field.val() && order === $order.val()) {
        order = 'ASC';
    }

    $field.val(field);
    $order.val(order);

    updateTable($('#pageNumber').val());
}
function updateTable(pageNumber) {
    var searchQuery = $('.js-product-search').val();
    $.ajax({
        type: "GET",
        url: "<?php echo $this->Url->build(['controller' => 'retailers', 'action' => 'retailer_categories', 'id' => $categoryId]); ?>",
        data: {
            pageNumber: pageNumber,
            search: searchQuery,
            sortField: $('#sortField').val(),
            sortOrder: $('#sortOrder').val(),
        }
    }).done(function(data) {
        var $data = $('<div></div>').html(data);
        $('#retailer-table-body').html($data.find('#retailer-table-body').html());
        $('#counts').val($data.find('#counts').val());
        $('#pagination').pagination('updateItems', $('#counts').val());
    });
}
</script>
<script type="text/javascript">
$(function() {
    $('#pagination').pagination({
        items: $('#counts').val(),
        itemsOnPage: $('#noRecords').val(),
        currentPage: $('#pageNumber').val(),
        cssStyle: 'light-theme',
        onPageClick: function(pageNumber, event) {
            $('#pageNumber').val(pageNumber);
            updateTable(pageNumber);
        },
        onInit: window.paginationInit,
    });
});
</script>

<input type="hidden" id="sortField" name="sortField" value="<?= $this->request->query('sortField') ?? 'RetailCategoriesRetailer.retailer_order' ?>" />
<input type="hidden" id="sortOrder" name="sortOrder" value="<?= $this->request->query('sortOrder') ?? 'ASC' ?>" />
