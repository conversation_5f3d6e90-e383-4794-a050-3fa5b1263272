<?php
/**
 * @var AppView $this
 */
?>
<?php echo $this->Form->create('User', array(
	'id' => 'SalesRepSearchForm',
	'type' => 'GET',
	'url' => ['controller' => 'sales_reps', 'action' => 'sales_rep_form'],
	'default' => false,
	'inputDefaults' => array(
		'label' => false,
		'div' => false,
		'class' => 'form-control',
		'required' => true,
	),
)); ?>
	<div class="form-group">
		<?php echo $this->Form->label('email_address'); ?>
		<div class="input-group">
			<?php echo $this->Form->input('email_address', array(
				'id' => 'SalesRepSearchEmail',
				'type' => 'email',
			)); ?>
			<div class="input-group-btn">
				<?php echo $this->Form->button('Search', array(
					'type' => 'submit',
					'class' => 'btn btn-primary',
				)); ?>
			</div>
		</div>
	</div>
<?php echo $this->Form->end(); ?>
<div id="SalesRepSearchResults">
</div>

<script type="text/javascript">
$(function() {
	$('#SalesRepSearchForm').submit(function() {
		var $form = $(this);
		$('#SalesRepSearchResults').load($form.attr('action'), $form.serialize());
	});

	var formSubmitted = false;
	$('#SalesRepSearchResults').on('submit', '#SalesRepForm', function(e) {
		e.preventDefault();
		if (formSubmitted) return;
		formSubmitted = true;

		$(this).append($('#SalesRepSearchEmail').clone().attr('type', 'hidden'));
		this.submit();
	});

	$('#SalesRepSearchEmail').focus();
});
</script>
