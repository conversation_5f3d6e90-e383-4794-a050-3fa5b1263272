<?php
/**
 * @var AppView $this
 * @var string $title_for_layout
 * @var array $shipearly_user
 * @var int $retailerId
 * @var string $usertype
 * @var int $count_users
 * @var array $paging
 * @var array $users
 */
?>
<div class="search-head">
	<h1 class='content-head'><?php echo $title_for_layout; ?></h1>
</div>
<div class='usersearch search'>
		<input type='text' class='input' name="search" value="<?php if(isset($this->request->data['search'])) { echo $this->request->data['search']; } ?>" placeholder="<?= __('Search'); ?>..." />
		<input type='submit' class='submit' value='' />
</div>
<div id="search_list">
	<?php echo $this->element('usersearch'); ?>
	<?php echo $this->Html->script('tablesorter/jquery.tablesorter.min'); ?>
	<script type="text/javascript">
		$("#timeTable").tablesorter( {widgets: ['zebra'] } );
	</script>
</div>
<?php
$urldata = explode("/", $_SERVER["REQUEST_URI"]);
?>
<input type='hidden' id="retailer_id" value = <?php echo $urldata[sizeof($urldata)-1]; ?> />
<style>
.usersearch {
	background: url("images/icons/top-search.png") no-repeat scroll 0 0 rgba(0, 0, 0, 0);
    float: right;
    width: 285px;
    height: 47px;
    margin: 0;
    padding: 0 0 0 15px;
}
#content > div#search_list {
    margin-top: 80px;
}
.usersearch .input {
    width: 82%;
}
.usersearch .submit {
    margin: 5px;
    width: 30px;
}
.table_links {
	color: #333333;
	text-decoration: underline;
}

.search .input {
	color: #000;
}
</style>
<?php if( !empty( $paging ) ) { ?>
	<div id="pagination"></div>
	<script type="text/javascript" src="<?php echo BASE_PATH; ?>jquery.simplePagination.js"></script>
	<link type="text/css" rel="stylesheet" href="<?php echo BASE_PATH; ?>simplePagination.css"/>
	<script type="text/javascript">
		$(function() {
			$('#pagination').pagination({
				items: <?php echo $count_users; ?>,
				itemsOnPage: <?php echo PAGINATION; ?>,
				cssStyle: 'light-theme',
				onPageClick: function(pageNumber, event) {
					updateTable(pageNumber);
				},
				onInit: window.paginationInit,
			});

			$('input[name="search"]').on('keypress', function (e) {
				if (e.which === 13) {
					e.preventDefault();
					updateTable(1);
				}
			});

			initToggle();
		});

		function pagination() {
			$('form#user_frm').submit();
		}

		function updateTable(pageNumber) {
			$.ajax({
				type: 'POST',
				url: "<?php echo Router::url(['controller' => 'Search', 'action' => 'ajax_search', 'admin' => true]); ?>",
				data: {
					id: "<?php echo $shipearly_user['User']['id']; ?>",
					view: 'admin',
					search: $('input[name="search"]').val(),
					pageNumber: pageNumber
				}
			}).done(function(data) {
				$('#search_list').html(data);
				$('#timeTable').tablesorter({ widgets: ['zebra'] });
				initToggle();
			});
		}

		function initToggle() {
			$('input.brand_connect[type="checkbox"]').on('click', function() {
				var $this = $(this);

				var id = $this.attr('id');
				var url = $this.is(':checked')
					? "<?php echo BASE_PATH . 'admin/adddealer/'; ?>" + id
					: "<?php echo BASE_PATH . 'admin/removedealer/'; ?>" + id;

				$.post(url, { retailer_id: $('#retailer_id').val() }, function(data) {
					var result = '';
					if (data.success) {
						result = FlashSuccessUpdate.replace("{{FlashSuccessUpdate}}", data.success);
					}
					if (data.error) {
						result = FlashErrorUpdate.replace("{{FlashErrorUpdate}}", data.error);
					}
					$('.msg_panel').html(result);
				}, 'json');
			})
		}
	</script>
	<style type="text/css">
	.retail_order span,
	.retail-home span {
		position: relative;
		top: 22px;
	}
	.fixedflash {
		width: 70%;
	}
	</style>
<?php } ?>
