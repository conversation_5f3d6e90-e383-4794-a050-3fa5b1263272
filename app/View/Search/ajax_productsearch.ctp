<div id="products_list" class="table-responsive">
	<?php /*$key = 1;*/ foreach( $all_products as $product ) { ?>
		<div class="list">
			<?php echo $this->element("product", array('product' => $product, 'additional_class' => 'wid-single-con'), array('cache' => array('config' => 'product', 'key' => 'ProductElement_FULL_'.$product['Product']['id']))); ?>
		</div>
		<?php //if($key%3 == 0 && $key != 0) { echo '<div class="clear"></div>'; } $key++;  ?>
	<?php } ?>
</div>
