<?php

use ShipEarlyApp\Lib\Utility\UserSiteType;

App::uses('User', 'Model');
App::uses('PercentSource', 'Utility');

/**
 * @var AppView $this
 * @var array $shipearly_user
 * @var array $subdomain_settings
 * @var string $currencycode
 * @var array $countries
 * @var array $distributorOptions
 * @var int|null $distributorId
 * @var array $usercarrierlist
 * @var array $productCategories
 * @var string[] $usercarrierlist [$carrier_name => $carrier_id]
 * @var string[] $productCategories
 * @var array $weightBasedShippingPrices
 * @var array $priceBasedShippingRates
 * @var array $unitBasedShippingRates
 * @var array $usershippingcarrierrates_data
 * @var int $zoneid
 * @var string $zonename
 * @var string $zoneoption
 * @var string[] $countrycodes
 * @var string[] $countrynames [$country_id => $country_name]
 * @var string[] $countryflags [$country_id => $country_icon]
 * @var array $usercountries
 * @var array $statesarray
 * @var string $states Json encoded
 * @var array $zonestatebycountry
 */

$userIsBrand = in_array($shipearly_user['User']['user_type'], User::TYPES_BRAND, true);

/** @var string $accent_color */
$accent_color = $subdomain_settings['accent_color'] ?? null;
?>
<style type="text/css">
.modal-header {
	border-bottom: 1px solid #131313;
	box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

#countrylist, #statelist {
	height: 300px;
	overflow-y: scroll;
}

#countrylist li:hover, #statelist li:hover {
	background-color: #e5e5e5;
}

#countrylist li span, #statelist li span {
	padding: 0 0 8px 8px;
}

.ui-banner {
	display: -webkit-flex;
	display: -ms-flexbox;
	display: flex;
	margin: 10px 10px 0;
	background-color: #d3dbe2;
	border: 1px solid rgba(0,0,0,0.1);
	border-radius: 3px;
	color: #151617;
}

.ui-banner--status-critical {
	background-color: #ff9797;
	color: #1a0f0f;
}

a.editcountry,
a.editrate,
a.editpricerate,
a.editunitrate,
a.editcarrier,
a.edit-tax-override {
	color: <?php echo $accent_color ?: '#009fff'; ?>;
}

.table > thead > tr > th:first-child,
.table > tbody > tr > td:first-child {
	text-align: left;
}
.disabled {
	opacity: 0.25;
}
.box-text a {
	color: <?php echo $accent_color ?: '#428bca'; ?>;
}
.countryzoneelement {
	padding:5px 10px 5px 15px;
}
.error {
	color : #ff0000;
	font-size : 12px;
	z-index: 2;
}
.critical {
	background-color: #ff9797;
	color: #1a0f0f;
}
.warning {
	background-color: #fff7b2;
	color: #1a1912;
}
.addZone_delete:hover {
	background-color: #ff5d5d;
	color: #fff;
}
#country_zones {
	padding-top: 10px;
}
#carrier_rates {
	padding-top:20px;
}
.move_right {
	text-align:right;
}
#priceForm > div:nth-child(4) > div > div > div {
	width: 100%;
}
@media (max-width: 979px) and (min-width: 768px) {
	.row {
		margin-left: -10px;
	}
}
</style>

<div class="header-card box-text">
	<div class="row">
		<div class="col-sm-9 col-12">
			<?php echo $this->Layout->breadcrumbHeader([
				'Shipping' => ['controller' => 'users', 'action' => 'shipment_setting'],
				h($zonename),
			]); ?>
		</div>
		<div class="col-sm-3 col-12">
			<div class="header-card-controls">
				<button type="submit" id="cancelShippingZone" name="cancel" class="btn btn-default">Cancel</button>
				<button type="submit" id="saveShippingZone" name="saveShippingZone" class="btn btn-primary">Save</button>
			</div>
		</div>
	</div>
</div>
<div class="row next-card" id="errormsg" style="display:none;">
</div>
<div class="row">
	<div class="offset-md-1 col-md-10 col-12">
		<div class="next-card">
			<div class="row">
				<div class="col-12">
					<h4>Zone Name</h4>
					<form id="zoneForm" name="zoneForm">
						<div class="form-group">
							<input type="hidden" name="shipping_zone[id]" id="zone_id" />
							<input name="shipping_zone[name]" placeholder="e.g. North America, Europe" class="form-control" type="text" id="zone_name">
							<input type="hidden" name="shipping_zone[type]" id="zone_option" value="<?php echo $zoneoption; ?>" />
						</div>
					</form>
				</div>
			</div>
		</div>
		<div class="next-card">
			<div class="row">
				<div class="col-md-8 col-12">
					<h4>Countries</h4>
				</div>
				<div class="col-md-4">
					<div class="d-flex justify-content-end">
					<?php if ($userIsBrand) { ?>
						<div class="form-group d-flex align-items-center me-2">
							<label for="distributor_id" class="text-nowrap me-2">Distributed by:</label>
							<select id="distributor_id" name="distributor_id" class="selectpicker" data-live-search="true" data-dropup-auto="false" data-width="180px">
								<option value=""><?= $shipearly_user['User']['company_name'] ?></option>
								<?php foreach ($distributorOptions as $Id => $name): ?>
									<?php if ($Id == $distributorId) { ?>
										<option value="<?= $Id ?>" selected="selected"><?= $name ?></option>
									<?php } else {?>
										<option value="<?= $Id ?>"><?= $name ?></option>
									<?php } ?>
								<?php endforeach; ?>
							</select>
						</div>
					<?php } ?>
						<div>
							<button name="button" type="button" class="btn btn-primary text-nowrap" id="addcountries">Add Countries</button>
						</div>
					</div>
				</div>
			</div>
			<div id="country_zones">
			</div>
		</div>
	<?php if (!$userIsBrand || $shipearly_user['User']['site_type'] === UserSiteType::SHOPIFY) { ?>
		<div class="next-card">
			<div class="row">
				<div class="col-md-8 col-12">
					<h4>Weight Based Rates</h4>
				</div>
				<div class="col-md-4 col-12 move_right">
					<button name="button" type="button" class="btn btn-primary" id="addweights">Add Rate</button>
				</div>
			</div>
			<div id="weight_rates" class="table-responsive">
				<table id="weight_rates_tbl" class="table table-hover table-condensed">
					<thead>
						<tr>
							<th>Name</th>
							<th>Category</th>
							<th>Range</th>
							<th>Rate Amount</th>
							<th class="col-2"><!--Edit--></th>
						</tr>
					</thead>
					<tbody>
					</tbody>
				</table>
			</div>
		</div>
		<div class="next-card">
			<div class="row">
				<div class="col-md-8 col-12">
					<h4>Price Based Rates</h4>
				</div>
				<div class="col-md-4 col-12 move_right">
					<button name="button" type="button" class="btn btn-primary" id="addprices">Add Rate</button>
				</div>
			</div>
			<div id="price_rates" class="table-responsive">
				<table id="price_rates_tbl" class="table table-hover table-condensed">
					<thead>
						<tr>
							<th>Name</th>
							<th>Category</th>
							<th>Range</th>
							<th>Rate Amount</th>
							<th class="col-2"><!--Edit--></th>
						</tr>
					</thead>
					<tbody>
					</tbody>
				</table>
			</div>
		</div>
	<?php if (FEATURE_TOGGLE_CONSUMER_SHIPPING_UNIT_RATES) { ?>
		<div class="next-card">
			<div class="row">
				<div class="col-md-8 col-12">
					<h4>Unit Based Rates</h4>
				</div>
				<div class="col-md-4 col-12 move_right">
					<button name="button" type="button" class="btn btn-primary" id="addunits">Add Rate</button>
				</div>
			</div>
			<div id="unit_rates" class="table-responsive">
				<table id="unit_rates_tbl" class="table table-hover table-condensed">
					<thead>
						<tr>
							<th>Name</th>
							<th>Category</th>
							<th>Range</th>
							<th>Rate Amount</th>
							<th class="col-2"><!--Edit--></th>
						</tr>
					</thead>
					<tbody>
					</tbody>
				</table>
			</div>
		</div>
	<?php } ?>
		<?php if ($userIsBrand) { ?>
			<div class="next-card" id="shipcarrierrates">
				<div class="row">
					<div class="col-8 ol-xs-12">
						<h4>Carrier-calculated Rates</h4>
					</div>
					<div class="col-md-4 col-12 move_right">
						<button name="button" type="button" class="btn btn-primary" id="addCarrier">Add Rate</button>
					</div>
				</div>
				<div id="carrier_rates">
				</div>
			</div>
			<div class="next-card" id="taxoverrides">
				<div class="row">
					<div class="col-8 ol-xs-12">
						<h4>Tax Overrides</h4>
					</div>
					<div class="col-md-4 col-12 move_right">
						<button type="button" class="btn btn-primary js-edit-tax-override" data-url="<?php echo $this->Html->url(['controller' => 'shipping_zones', 'action' => 'tax_override_form']); ?>">Add</button>
					</div>
				</div>
				<div id="tax_overrides" class="table-responsive">
				</div>
			</div>
		<?php } ?>
	<?php } ?>
	</div>
</div>
<div class="row">
	<div class="col-12">
		<div class="footer-card">
			<button type="button" id="deleteShippingZone" name="cancel" class="btn btn-default addZone_delete" >Delete Zone</button>
		</div>
	</div>
</div>
<!-- Country Modal View -->
<div class="modal fade" id= "modaldiv" role="dialog" aria-labelledby="gridSystemModalLabel">
	<div class="modal-dialog shipment-setting-modal" role="document">
		<div class="modal-content" style="background-color: #fff !important;">
			<div class="modal-header">
				<h4 class="modal-title shipmentmodaltitle" id="gridSystemModalLabel">
					Add Countries
				</h4>
				<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
			</div>
			<div class="modal-body">
				<div id="countrylist">
					<ul>
						<li>
							<span><input type="checkbox" class="restofworld" /></span>
							<span>Rest of the World</span>
							<hr />
						</li>
					<?php foreach ($countries as $value) { ?>
						<?php
						$country_state_count = $value['0']['state_counts'];
						$select_country_state_count = array_key_exists($value['C']['id'], $usercountries)
							? count($usercountries[$value['C']['id']])
							: $country_state_count;
						$states_count = ($value['C']['id'] == 39)
							? "({$select_country_state_count} of {$country_state_count} provinces)"
							: "({$select_country_state_count} of {$country_state_count} states)";
						$checked = in_array($value['C']['country_name'], $countrynames)
							? 'checked'
							: '';
						?>
						<li>
							<span><input type="checkbox" class="countrychk" value="<?php echo $value['C']['id']; ?>" <?php echo $checked; ?> /></span>
							<span><?php echo $value['C']['country_name']; ?></span>
							<span><?php echo $states_count; ?></span>
							<span></span>
							<input type="hidden" name="selected_states_count" value="<?php echo $select_country_state_count; ?>" />
							<input type="hidden" name="country_states_count" value="<?php echo $country_state_count; ?>" />
							<input type="hidden" name="country_code_v" value="<?php echo $value['C']['id']; ?>" />
							<hr />
						</li>
					<?php } ?>
					</ul>
				</div>
			</div><!-- /.modal-body -->
			<div class="modal-footer">
				<button type="submit" id="ccancel" name="ccancel" class="btn btn-default" data-bs-dismiss="modal">Cancel</button>
				<button type="submit" id="save" name="save" class="btn btn-primary">Add</button>
			</div>
		</div><!-- /.modal-content -->
	</div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<!-- State Modal View -->
<div class="modal fade" id= "statemodaldiv" role="dialog" aria-labelledby="gridSystemModalLabel">
	<div class="modal-dialog shipment-setting-modal" role="document">
		<div class="modal-content" style="background-color: #fff !important;">
			<div class="modal-header">
				<h4 class="modal-title shipmentmodaltitle" id="gridSystemModalLabel">
					Edit provinces / states <input type="hidden" id="state_country_id" />
				</h4>
				<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
			</div>
			<div class="modal-body">
				<div id="statelist">
				</div>
			</div><!-- /.modal-body -->
			<div class="modal-footer">
				<button type="submit" id="statesCancel" name="statesCancel" class="btn btn-default" data-bs-dismiss="modal">Cancel</button>
				<button type="submit" id="saveStates" name="saveStates" class="btn btn-primary">Done</button>
			</div>
		</div><!-- /.modal-content -->
	</div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<!-- Weight Based Rate Modal View -->
<div class="modal fade" id= "weightmodaldiv" role="dialog" aria-labelledby="gridSystemModalLabel">
	<div class="modal-dialog shipment-setting-modal" role="document">
		<div class="modal-content" style="background-color: #fff !important;">
			<div class="modal-header">
				<h4 class="modal-title shipmentmodaltitle" id="gridSystemModalLabelWeight">
					Edit weight rate
				</h4>
				<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
				<p> </p>
			</div>
			<div class="modal-body">
				<div id="weightratedetails">
					<form id="weightForm" name="weightForm">
						<div class="form-group">
							<input type="hidden" id="weight_rate_id" value="" />
							<label for="weight_rate_name">Name</label>
							<input type="text" class="form-control" id="weight_rate_name" name="weight_rate_name" aria-describedby="weight_rate_nameHelp" placeholder="Standard Weight Rate">
							<small id="weight_rate_nameHelp" class="form-text text-muted">Customers see this at checkout.</small>
						</div>
						<div class="form-group">
							<label for="weight_rate_category">Product Category</label>
							<div class="row">
								<div class="col-8">
									<select class="selectpicker" id="weight_rate_category" name="weight_rate_category" multiple data-live-search="true" data-dropup-auto="false" data-none-selected-text="All Products">
									<?php foreach($productCategories as $key => $value) {?>
										<option value="<?php echo $key; ?>"><?php echo $value; ?></option>
									<?php } ?>
									</select>
								</div>
							</div>
						</div>
						<div class="form-group">
							<label for="weight_rate_range">Range</label>
							<div class="row">
								<div class="col-6">
									<div class="input-group">
										<input type="number" class="form-control" id="weight_rate_range_min" name="weight_rate_range_min" placeholder="0">
										<span class="input-group-text">lb</span>
									</div>
								</div>
								<div class="col-6">
									<div class="input-group">
										<input type="number" class="form-control" id="weight_rate_range_max" name="weight_rate_range_max" placeholder="25">
										<span class="input-group-text">lb</span>
									</div>
								</div>
							</div>
						</div>
						<div class="form-group">
							<label for="weight_rate_amount">Rate amount</label>
							<div class="row">
								<div class="col-6">
									<div class="input-group">
										<?php echo $this->Form->select('weight_rate_amount_type', [
											'flat' => 'Flat Rate',
											'percent' => '% of Sale',
										], [
											'id' => 'weight_rate_amount_type',
											'name' => 'weight_rate_amount_type',
											'empty' => false,
											'class' => 'selectpicker',
											'data-width' => 'auto',
										]); ?>
										<input type="text" class="form-control" id="weight_rate_amount" name="weight_rate_amount" placeholder="0.00">
									</div>
								</div>
								<div class="col-6">
									<?= $this->Form->select('weight_rate_percent_source', PercentSource::getAllOptions(), [
										'id' => 'weight_rate_percent_source',
										'name' => 'weight_rate_percent_source',
										'empty' => false,
										'class' => 'form-control',
									]) ?>
								</div>
							</div>
						</div>
						<input type="hidden" name="weight_category_names" id="weight_category_names" />
					</form>
				</div>
			</div><!-- /.modal-body -->
			<div class="modal-footer">
				<button type="submit" id="wcancel" class="btn btn-default" data-bs-dismiss="modal">Cancel</button>
				<button type="submit" id="saveWeights" name="saveWeights" class="btn btn-primary">Add</button>
			</div>
		</div><!-- /.modal-content -->
	</div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<!-- Price Based Rate Modal View -->
<div class="modal fade" id= "pricemodaldiv" role="dialog" aria-labelledby="gridSystemModalLabel">
	<div class="modal-dialog shipment-setting-modal" role="document">
		<div class="modal-content" style="background-color: #fff !important;">
			<div class="modal-header">
				<h4 class="modal-title shipmentmodaltitle" id="gridSystemModalLabelPrice">
					Edit price rate
				</h4>
				<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
				<p> </p>
			</div>
			<div class="modal-body">
				<div id="priceratedetails">
					<form id="priceForm" name="priceForm">
						<div class="form-group">
							<input type="hidden" id="price_rate_id" value="" />
							<label for="price_rate_name">Name</label>
							<input type="text" class="form-control" id="price_rate_name" name="price_rate_name" aria-describedby="price_rate_nameHelp" placeholder="Standard Price Rate">
							<small id="price_rate_nameHelp" class="form-text text-muted">Customers see this at checkout.</small>
						</div>
						<div class="form-group">
							<label for="price_rate_category">Product Category</label>
							<div class="row">
								<div class="col-8">
									<select class="selectpicker" id="price_rate_category" name="price_rate_category" multiple data-live-search="true" data-dropup-auto="false" data-none-selected-text="All Products">
									<?php foreach($productCategories as $key => $value) {?>
										<option value="<?php echo $key; ?>"><?php echo $value; ?></option>
									<?php } ?>
									</select>
								</div>
							</div>
						</div>
						<div class="form-group">
							<label for="price_rate_range">Range</label>
							<div class="row">
								<div class="col-6">
									<label for="price_rate_range_min">Minimum order price</label>
									<div class="input-group">
										<span class="input-group-text"><?php echo $currencycode; ?></span>
										<input type="number" class="form-control decimal" id="price_rate_range_min" name="price_rate_range_min" placeholder="0">
									</div>
								</div>
								<div class="col-6">
									<label for="price_rate_range_max">Maximum order price</label>
									<div class="input-group">
										<span class="input-group-text"><?php echo $currencycode; ?></span>
										<input type="number" class="form-control decimal" id="price_rate_range_max" name="price_rate_range_max" placeholder="25">
									</div>
								</div>
							</div>
						</div>
						<div class="form-group">
							<label for="price_rate_">Rate</label>
							<div class="form-group">
								<label class="checkbox">
									<input type="checkbox" name="price_free_shipping" id="price_free_shipping">Free shipping rate
								</label>
							</div>
							<label for="price_rate_amount">Rate amount</label>
							<div class="row">
								<div class="col-6">
									<div class="input-group">
										<?php echo $this->Form->select('price_rate_amount_type', [
											'flat' => 'Flat Rate',
											'percent' => '% of Sale',
										], [
											'id' => 'price_rate_amount_type',
											'name' => 'price_rate_amount_type',
											'empty' => false,
											'class' => 'selectpicker',
											'data-width' => 'auto',
										]); ?>
										<input type="number" class="form-control decimal" id="price_rate_amount" name="price_rate_amount" placeholder="0">
									</div>
								</div>
								<div class="col-6">
									<?= $this->Form->select('price_rate_percent_source', PercentSource::getAllOptions(), [
										'id' => 'price_rate_percent_source',
										'name' => 'price_rate_percent_source',
										'empty' => false,
										'class' => 'form-control',
									]) ?>
								</div>
							</div>
						</div>
						<input type="hidden" name="price_category_names" id="price_category_names" />
					</form>
				</div>
			</div><!-- /.modal-body -->
			<div class="modal-footer">
				<button type="submit" id="pcancel" class="btn btn-default" data-bs-dismiss="modal">Cancel</button>
				<button type="submit" id="savePrices" name="savePrices" class="btn btn-primary">Add</button>
			</div>
		</div><!-- /.modal-content -->
	</div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<!-- Unit Based Rate Modal View -->
<div class="modal fade" id= "unitmodaldiv" role="dialog" aria-labelledby="gridSystemModalLabel">
	<div class="modal-dialog shipment-setting-modal" role="document">
		<div class="modal-content" style="background-color: #fff !important;">
			<div class="modal-header">
				<h4 class="modal-title shipmentmodaltitle" id="gridSystemModalLabelUnit">
					Edit unit rate
				</h4>
				<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
				<p> </p>
			</div>
			<div class="modal-body">
				<div id="unitratedetails">
					<form id="unitForm" name="unitForm">
						<div class="form-group">
							<input type="hidden" id="unit_rate_id" value="" />
							<label for="unit_rate_name">Name</label>
							<input type="text" class="form-control" id="unit_rate_name" name="unit_rate_name" aria-describedby="unit_rate_nameHelp" placeholder="Standard Unit Rate">
							<small id="unit_rate_nameHelp" class="form-text text-muted">Customers see this at checkout.</small>
						</div>
						<div class="form-group">
							<label for="unit_rate_category">Product Category</label>
							<div class="row">
								<div class="col-8">
									<select class="selectpicker" id="unit_rate_category" name="unit_rate_category" multiple data-live-search="true" data-dropup-auto="false" data-none-selected-text="All Products">
									<?php foreach($productCategories as $key => $value) {?>
										<option value="<?php echo $key; ?>"><?php echo $value; ?></option>
									<?php } ?>
									</select>
								</div>
							</div>
						</div>
						<div class="form-group">
							<label for="unit_rate_range">Range</label>
							<div class="row">
								<div class="col-6">
									<label for="unit_rate_range_min">Minimum order units</label>
										<input type="number" class="form-control" id="unit_rate_range_min" name="unit_rate_range_min" placeholder="0">
								</div>
								<div class="col-6">
									<label for="unit_rate_range_max">Maximum order units</label>
										<input type="number" class="form-control" id="unit_rate_range_max" name="unit_rate_range_max" placeholder="25">
								</div>
							</div>
						</div>
						<div class="form-group">
							<label for="unit_rate_">Rate</label>
							<div class="form-group">
								<label class="checkbox">
									<input type="checkbox" name="unit_free_shipping" id="unit_free_shipping">Free shipping rate
								</label>
							</div>
							<label for="unit_rate_amount">Rate amount</label>
							<div class="row">
								<div class="col-6">
									<div class="input-group">
										<?php echo $this->Form->select('unit_rate_amount_type', [
											'flat' => 'Flat Rate',
											'percent' => '% of Sale',
										], [
											'id' => 'unit_rate_amount_type',
											'name' => 'unit_rate_amount_type',
											'empty' => false,
											'class' => 'selectpicker',
											'data-width' => 'auto',
										]); ?>
										<input type="number" class="form-control decimal" id="unit_rate_amount" name="unit_rate_amount" placeholder="0">
									</div>
								</div>
								<div class="col-6">
									<?= $this->Form->select('unit_rate_percent_source', PercentSource::getAllOptions(), [
										'id' => 'unit_rate_percent_source',
										'name' => 'unit_rate_percent_source',
										'empty' => false,
										'class' => 'form-control',
									]) ?>
								</div>
							</div>
						</div>
						<input type="hidden" name="unit_category_names" id="unit_category_names" />
					</form>
				</div>
			</div><!-- /.modal-body -->
			<div class="modal-footer">
				<button type="submit" id="pcancel" class="btn btn-default" data-bs-dismiss="modal">Cancel</button>
				<button type="submit" id="saveUnits" name="saveUnits" class="btn btn-primary">Add</button>
			</div>
		</div><!-- /.modal-content -->
	</div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<!-- Delete Zone Confirmation -->
<div class="modal fade" id= "deletezonemodaldiv" role="dialog" aria-labelledby="gridSystemModalLabel">
	<div class="modal-dialog shipment-setting-modal" role="document">
		<div class="modal-content" style="background-color: #fff !important;">
			<div class="modal-header">
				<h4 class="modal-title shipmentmodaltitle" id="gridSystemModalLabel">
					Delete <?php echo h($zonename);?>
				</h4>
				<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
			</div>
			<div class="modal-body">
				<p>Are you sure you want to delete this Zone and its associated rates?</p>
			</div><!-- /.modal-body -->
			<div class="modal-footer">
				<button type="submit" id="deleteZoneCancel" name="deleteZoneCancel" class="btn btn-default" data-bs-dismiss="modal">Cancel</button>
				<button type="submit" id="deleteZoneOk" name="deleteZoneOk" class="btn btn-default addZone_delete" data-value = '<?php echo $zoneid;?>'>Delete</button>
			</div>
		</div><!-- /.modal-content -->
	</div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<!-- Carrier Modal View -->
<div class="modal fade" id= "carriermodaldiv" role="dialog" aria-labelledby="gridSystemModalLabel">
	<div class="modal-dialog shipment-setting-modal" role="document">
		<div class="modal-content" style="background-color: #fff !important;">
			<div class="modal-header">
				<h4 class="modal-title shipmentmodaltitle" id="gridSystemModalLabel">
					Edit carrier-calculated Rate <input type="hidden" id="carrier_rate_id" />
				</h4>
				<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
			</div>
			<div class="modal-body">
				<div class="row">
					<p>Select a courier</p>
					<p>
						<select class="selectpicker" id="carrier_id">
						<?php foreach($usercarrierlist as $key => $value) { ?>
							<option value=<?php echo $value; ?> ><?php echo $key; ?></option>
						<?php } ?>
						</select>
					</p>
				</div>
				<div class="row">
					<hr class="ship-stgshrline">
				</div>
				<div class="row">
					<p>Services</p>
					<p>Rates are based on your customer's address and the weight of their order</p>
				</div>
				<div class="row">
					<hr class="ship-stgshrline">
				</div>
				<div class="row">
					<p>Rate adjustments</p>
					<p>Increase carrier-calculated rates</p>
					<p>
					<div class="form-group">
						<div class="row">
							<div class="col-6">
								<label for="carrier_percentage">Percentage</label>
								<div class="input-group">
									<input type="number" class="form-control" id="carrier_percentage" name="carrier_percentage" placeholder="0" value="0">
									<span class="input-group-text">%</span>
								</div>
							</div>
							<div class="col-6">
								<label for="carrier_flat_fee">Flat Fee</label>
								<div class="input-group">
									<span class="input-group-text"><?php echo $currencycode; ?></span>
									<input type="number" class="form-control" id="carrier_flat_fee" name="carrier_flat_fee" placeholder="0" value="0">
								</div>
							</div>
						</div>
					</div>
					</p>
				</div>
			</div><!-- /.modal-body -->
			<div class="modal-footer">
				<button type="submit" id="carrierCancel" name="carrierCancel" class="btn btn-default" data-bs-dismiss="modal">Cancel</button>
				<button type="submit" id="saveCarrier" name="saveCarrier" class="btn btn-primary">Done</button>
			</div>
		</div><!-- /.modal-content -->
	</div><!-- /.modal-dialog -->
</div><!-- /.modal -->
<div class="clear"></div>

<script type="text/javascript">
var countries = [];
var currency_code = "<?php echo $currencycode; ?>";
var tempcountries = [];
var usercountries = <?php echo json_encode($usercountries); ?>;
var states = <?php echo $states; ?>;
var zonestatesbycountry = <?php echo json_encode($zonestatebycountry); ?>;
var selectedstates = [];
var selectedrates = [];
var selectedpricerates = [];
var selectedunitrates = [];
var zoneoption = "<?php echo $zoneoption; ?>";

//Preload selectedstates with states in zones
if (!$.isEmptyObject(zonestatesbycountry)) {
	$.each(zonestatesbycountry, function(i, v) {
		selectedstates.push({ id: i, states: v });
	});
}

var deleterates = [];
var deletepricerates = [];
var deleteunitrates = [];
var selectedcarriers = [];
var selectedcarriers_details = [];
var deletedcarriers_details = [];

$('document').ready(function() {
	if (zoneoption == 'wholesale') {
		$('#shipcarrierrates').hide();
		$('#taxoverrides').hide();
	}

	var $country_zones = $('#country_zones');
	var country_zone_element = '<div class="row countryzoneelement">' +
			'<div class="col-8" style="background: url(country_icon) no-repeat; background-size: 20px 20px; padding-left:30px;">' +
				'Country Name' +
				'<input type="hidden" name="country_codes[]" class="country_codes" value="country_code_val" />' +
				'<span id="states_counter"> Count</span>' +
			'</div>' +
			'<div class="col-2">' +
			'</div>' +
			'<div class="col-2" style="padding-left: 0px;">' +
				'<a href="#" data-value="e_country_code_val" class="editcountry">Edit</a>' +
				'<button type="button" class="btn-close country float-end" aria-label="Close" data-value="a_country_code_val"></button>' +
			'</div>' +
		'</div>';
	var carrier_zone_element = '<div class="row carrierzoneelement">' +
			'<div class="col-8">' +
				'carrier_name' +
				'<input type="hidden" name="zone_user_carrier_id[]" class="zone_user_carrier_id" value="zone_carrier_val" />' +
				'<input type="hidden" name="zone_carrier_id[]" class="zone_carrier_id" value="zone_carrier_id_val" />' +
				'<span id="zone_carrier_rate"> carrier rate </span>' +
			'</div>' +
			'<div class="col-2">' +
			'</div>' +
			'<div class="col-2" style="padding-left: 0px;">' +
				'<a href="#" data-value="e_zone_carrier_val" class="editcarrier">Edit</a>' +
				'<button type="button" class="btn-close carrier float-end" aria-label="Close" data-value="a_zone_carrier_val"></button>' +
			'</div>' +
		'</div>';

	//Populate Zone Data
	$('#zone_id').val('<?php echo $zoneid; ?>');
	$('#zone_name').val('<?php echo addslashes($zonename); ?>');

	//Popuplate Country Data
<?php $countryZones = array(); ?>
<?php if (!empty($countrynames)) {
	foreach ($countrynames as $key => $value) {
		$countryflag = !empty($countryflags[$key]) ? $countryflags[$key] : 'globe.png';
		$totalstatescount = !empty($statesarray[$key]) ? count($statesarray[$key]) : 0;
		$zonestatescount = !empty($zonestatebycountry[$key]) ? count($zonestatebycountry[$key]) : 0;

		// Text replaced in country_zone_element
		$countryZones[] = array(
			'Country Name' => $value,
			'country_icon' => BASE_PATH . 'images/flags/' . $countryflag,
			'states_counter' => $key,
			'Count' => "({$zonestatescount} of {$totalstatescount})",
			'country_code_val' => $key,
			'a_country_code_val' => $key,
			'e_country_code_val' => $key,
		);
	}
?>
	countries = countries.concat(<?php echo json_encode(array_map('strval', array_keys($countrynames))); ?>);
	disableRestOfWorld(true);
<?php } elseif (isset($countrycodes) && $countrycodes[0] == "9999") {
	$key = 9999;
	$countryflag = 'globe.png';
	// Text replaced in country_zone_element
	$countryZones[] = array(
		'Country Name' => 'Rest of the World',
		'country_icon' => BASE_PATH . 'images/flags/' . $countryflag,
		'states_counter' => 0,
		'Count' => '',
		'country_code_val' => $key,
		'a_country_code_val' => $key,
		'e_country_code_val' => $key,
		'Edit' => '',
	);
?>
	disableRestOfWorld(false);
<?php } ?>
<?php if (!empty($countryZones)) { ?>
	$.each(
		<?php echo json_encode(h($countryZones)); ?>,
		function(index, countryZone) {
			var content = country_zone_element;
			$.each(countryZone, function(key, value) {
				content = content.replace(key, value);
			});
			$country_zones.append(content);
		}
	);
<?php } ?>
	initCountryActions();

	//Populate Weight Rates
<?php if (isset($weightBasedShippingPrices)) { ?>
	$.each(
		<?php echo json_encode(h(array_column($weightBasedShippingPrices, 'WeightBasedShippingPrice'))); ?>,
		function(index, rate) {
			$('#weight_rates_tbl tbody').append(buildWeightBasedShippingRateRow(rate));
		}
	);
	selectedrates = selectedrates.concat(<?php echo json_encode(array_column($weightBasedShippingPrices, 'WeightBasedShippingPrice')); ?>);
<?php } ?>
	initWeightRateActions();

	//Populate Price Rates
<?php if (isset($priceBasedShippingRates)) { ?>
	$.each(
		<?php echo json_encode(h(array_column($priceBasedShippingRates, 'PriceBasedShippingRate'))); ?>,
		function(index, rate) {
			$("#price_rates_tbl tbody").append(buildPriceBasedShippingRateRow(rate));
		}
	);
	selectedpricerates = selectedpricerates.concat(<?php echo json_encode(array_column($priceBasedShippingRates, 'PriceBasedShippingRate')); ?>);
<?php } ?>
	initPriceRateActions();

	//Populate Unit Rates
<?php if (isset($unitBasedShippingRates)) { ?>
	$.each(
		<?php echo json_encode(h(array_column($unitBasedShippingRates, 'UnitBasedShippingRate'))); ?>,
		function(index, rate) {
			$("#unit_rates_tbl tbody").append(buildUnitBasedShippingRateRow(rate));
		}
	);
	selectedunitrates = selectedunitrates.concat(<?php echo json_encode(array_column($unitBasedShippingRates, 'UnitBasedShippingRate')); ?>);
<?php } ?>
	initUnitRateActions();

	countryadd();

	//Populate Carrier Rates
<?php if (isset($usershippingcarrierrates_data)) {
	$carriers = array_map(function($value) {
		return array_intersect_key($value, array_flip(array(
			'id', 'carrier_id', 'carrier_fullname', 'rates'
		)));
	}, $usershippingcarrierrates_data);
?>
	$.each(
		<?php echo json_encode(h($carriers)); ?>,
		function(index, userCarrier) {
			var c_percent = userCarrier['rates']['percentage'];
			var c_flat_fee = userCarrier['rates']['flat_fee'];

			var rate_text = '';
			if (parseFloat(c_percent) > 0 || parseFloat(c_flat_fee) > 0) {
				rate_text = 'Rate adjustment: ';
				rate_text += (parseFloat(c_percent) > 0) ? c_percent + '%' : '';
				rate_text += (parseFloat(c_percent) > 0 && parseFloat(c_flat_fee) > 0) ? ' + ' : '';
				rate_text += (parseFloat(c_flat_fee) > 0) ? formatCurrency(c_flat_fee) : '';
			}

			var templateValues = {
				'carrier_name': userCarrier['carrier_fullname'],
				'carrier rate': rate_text,
				'zone_carrier_val': userCarrier['id'],
				'zone_carrier_id_val': userCarrier['carrier_id'],
				'a_zone_carrier_val': userCarrier['id'],
				'e_zone_carrier_val': userCarrier['id'],
			};

			var content = carrier_zone_element;
			$.each(templateValues, function(key, value) {
				content = content.replace(key, value);
			});
			$('#carrier_rates').append(content);
		}
	);
	selectedcarriers = selectedcarriers.concat(<?php echo json_encode(array_column($carriers, 'carrier_id')); ?>);
	selectedcarriers_details = selectedcarriers_details.concat(<?php echo json_encode($carriers); ?>);
<?php } ?>
	initCarrierActions();

	function buildWeightBasedShippingRateRow(rate) {
		return '<tr>' +
			'<td>' + rate['name'] + '</td>' +
			'<td>' + (rate['product_category'] || 'All Products') + '</td>' +
			'<td style="white-space: nowrap;">' + rate['min'] + ' ' + rate['unit'] + ' - ' + rate['max'] + ' ' + rate['unit'] + '</td>' +
			'<td>' + formatCurrency(rate['amount']) + (rate['amount_type'] === 'percent' ? ' %' : '') + '</td>' +
			'<td>' +
				'<div class="d-flex justify-content-between">' +
					'<a href="#" data-value="' + rate['id'] + '" class="editrate">Edit</a>' +
					'<button type="button" class="btn-close weightrate float-end" aria-label="Close" data-value="' + rate['name'] + '">' +
						'' +
					'</button>' +
				'</div>' +
			'</td>' +
		'</tr>';
	}

	function buildPriceBasedShippingRateRow(rate) {
		return '<tr>' +
			'<td>' + rate['name'] + '</td>' +
			'<td>' + (rate['product_category'] || 'All Products') + '</td>' +
			'<td style="white-space: nowrap;">' + formatCurrency(rate['min']) + ' - ' + formatCurrency(rate['max']) + '</td>' +
			'<td>' + formatCurrency(rate['amount']) + (rate['amount_type'] === 'percent' ? ' %' : '') + '</td>' +
			'<td>' +
				'<div class="d-flex justify-content-between">' +
					'<a href="#" data-value="' + rate['id'] + '" class="editpricerate">Edit</a>' +
					'<button type="button" class="btn-close pricerate float-end" aria-label="Close" data-value="' + rate['name'] + '">' +
						'' +
					'</button>' +
				'</div>' +
			'</td>' +
		'</tr>';
	}
	function buildUnitBasedShippingRateRow(rate) {
		return '<tr>' +
			'<td>' + rate['name'] + '</td>' +
			'<td>' + (rate['product_category'] || 'All Products') + '</td>' +
			'<td style="white-space: nowrap;">' + rate['min'] + ' - ' + rate['max'] + '</td>' +
			'<td>' + formatCurrency(rate['amount']) + (rate['amount_type'] === 'percent' ? ' %' : '') + '</td>' +
			'<td>' +
				'<div class="d-flex justify-content-between">' +
					'<a href="#" data-value="' + rate['id'] + '" class="editunitrate">Edit</a>' +
					'<button type="button" class="btn-close unitrate float-end" aria-label="Close" data-value="' + rate['name'] + '">' +
						'' +
					'</button>' +
				'</div>' +
			'</td>' +
		'</tr>';
	}

	$('#modaldiv').on('shown.bs.modal', function() {
		//Disable Rest of World if already setup
		if ($.inArray(9999, usercountries) > -1) {
			disableRestOfWorld(true);
		}
	});

	$('#addcountries').on("click", function() {
		if ($('input.country_codes').length == 0) {
			disableRestOfWorld(false);
		}
		else if ($('input.country_codes').length == 1 && $('input.country_codes').val() == 9999) {
			disableRestOfWorld(false);
			$('input[type=checkbox].restofworld').prop('checked', true);
		}
		else {
			disableRestOfWorld(true);
		}
		$('#modaldiv').modal('show');
		disableCountryByValue();
	});

	$('input[type=checkbox].restofworld').on("click", function() {
		if ($('input[type=checkbox].restofworld').is(':checked')) {
			$('input[type=checkbox].countrychk').prop('disabled', true);
		}
		else {
			$.each($('input[type=checkbox].countrychk'), function() {
				if ($(this).parent().next().next().next().text() != 'already in another shipping zone') {
					$(this).prop('disabled', false);
				}
			});
		}
	});

	$('#addweights').on("click", function() {
		$('#weight_rate_id').val('');
		$('#weight_rate_name').val('');
		$('#weight_rate_range_min').val('0');
		$('#weight_rate_range_max').val('');
		$('#weight_rate_amount_type').prop('selectedIndex', 0).selectpicker('refresh');
		$('#weight_rate_percent_source').prop('selectedIndex', 0).hide();
		$('#weight_rate_amount').val('0.00');
		$('#gridSystemModalLabelWeight').html('Add weight rate');
		$('#weightmodaldiv').modal('show');
	});

	$('#addprices').on("click", function() {
		$('#price_rate_id').val('');
		$('#price_rate_name').val('');
		$('#price_rate_range_min').val('0');
		$('#price_rate_range_max').val('');
		$('#price_rate_amount_type').prop('selectedIndex', 0).selectpicker('refresh');
		$('#price_rate_percent_source').prop('selectedIndex', 0).hide();
		$('#price_rate_amount').val('0.00');
		$('#gridSystemModalLabelPrice').html('Add price rate');
		$('#pricemodaldiv').modal('show');
	});
	$('#addunits').on("click", function() {
		$('#unit_rate_id').val('');
		$('#unit_rate_name').val('');
		$('#unit_rate_range_min').val('0');
		$('#unit_rate_range_max').val('');
		$('#unit_rate_amount_type').prop('selectedIndex', 0).selectpicker('refresh');
		$('#unit_rate_percent_source').prop('selectedIndex', 0).hide();
		$('#unit_rate_amount').val('0.00');
		$('#gridSystemModalLabelUnit').html('Add unit rate');
		$('#unitmodaldiv').modal('show');
	});

	$('#deleteShippingZone').on("click", function() {
		$('#deletezonemodaldiv').modal('show');
	});

	$('#deleteZoneOk').on("click", function() {
		var data = {};
		var id = $(this).attr('data-value');
		var url = "<?php echo BASE_PATH; ?>/shipment/shippingzones/remove/" + id;
		ZoneInfo(url, 'POST', data);
	});

	$('#addCarrier').on("click", function() {
		$('#carriermodaldiv').modal('show');
	});

	if ($('#tax_overrides').length) {
		$.get('<?= Router::url(['controller' => 'shipping_zones', 'action' => 'ajax_tax_override_table', $zoneid]) ?>', function(data) {
			$('#tax_overrides').html(data);
		});
	}
	$(document).on('click', '.js-edit-tax-override', function(e) {
		e.preventDefault();
		var urlParts = $(this).data('url').split('?');
		$.post(urlParts[0], urlParts[1], function(data) {
			bootbox.dialog({
				title: 'Add Tax Override',
				message: data,
				className: 'bootbox--form modal--shipping',
				buttons: {
					Cancel: function() {
						bootbox.hideAll();
					},
					Save: function() {
						$(this).find('form input[type="submit"]').click();
						return false;
					}
				}
			});
		});
	}).on('click', '.js-remove-tax-override', function(e) {
		e.preventDefault();
		$(this).closest('tr').remove();
	}).on('submit', '#ZoneTaxOverrideForm', function(e) {
		e.preventDefault();
		var $form = $(this);
		var id = $form.find('#ZoneTaxOverrideId').val();
		var key = $form.find('#ZoneTaxOverrideKey').val();
		var url = '<?php echo Router::url(['controller' => 'shipping_zones', 'action' => 'ajax_tax_override_row']); ?>/' + id;
		$.post(url, $form.serialize(), function(data) {
			if (key) {
				bootbox.hideAll();
				$('#tax_overrides tbody tr[data-key="' + key + '"]').replaceWith(data);
			} else if (!$('#tax_overrides tbody tr[data-key="' + $(data).filter('tr[data-key]').data('key') + '"]').length) {
				bootbox.hideAll();
				$('#tax_overrides tbody').append(data);
			} else {
				alert('Name already taken for this override type.');
			}
		});
	});

	$('#save').on("click", function() {
		$('input[type=checkbox].countrychk').each(function(i, v) {
			if ($(this).is(':checked')) {
				if (!checkCountryInList($(this).val())) {
					var content = country_zone_element.replace("Country Name", $(this).parent().next().text());
					content = content.replace("states_counter", $(this).val());
					content = content.replace("Count", $(this).parent().next().next().text());
					content = content.replace("country_code_val", $(this).val());
					content = content.replace("a_country_code_val", $(this).val());
					content = content.replace("e_country_code_val", $(this).val());
					$country_zones.append(content);
					tempcountries.push($(this).val());
				}
			}
		});

		if ($('input[type=checkbox].restofworld').is(':checked')) {
			$country_zones.html("");
			countries = [];
			selectedstates = [];
			var content = country_zone_element.replace("Country Name", $('input[type=checkbox].restofworld').parent().next().text());
			content = content.replace("states_counter", 0);
			content = content.replace("Count", "");
			content = content.replace("country_code_val", 9999);
			content = content.replace("a_country_code_val", 9999);
			content = content.replace("e_country_code_val", 9999);
			content = content.replace("Edit", "");
			$country_zones.append(content);
		}

		$('input:checkbox').removeAttr('checked');
		$('#modaldiv').modal('hide');
	});

	$('#saveStates').on('click', function() {
		var country_code = $('#state_country_id').val();
		var country_states_sel = {
			"id": country_code,
			"states": []
		};
		$('input[type=checkbox].statechk').each(function(i, v) {
			if ($(this).is(':checked')) {
				country_states_sel["states"].push($(this).val());
			}
		});
		$('input:checkbox').removeAttr('checked');
		var position = -1;
		$.each(selectedstates, function(i, v) {
			if (v.id == country_code) {
				position = i;
			}
		});
		if (position >= 0) {
			selectedstates.splice(position, 1);
		}
		selectedstates.push(country_states_sel);
		addSelectedStates(country_states_sel);
		$('#statemodaldiv').modal('hide');
	});

	$('#saveWeights').on('click', function() {
		// Update selected categories if user did not click off picker
		$weightcategorypicker.trigger('select2:close');
		//validate weight form
		if (!$("#weightForm").valid()) {
			return false;
		}

		var field_id = $('#weight_rate_id').val();
		var rate = {
			id: field_id,
			name: $('#weight_rate_name').val(),
			min: $('#weight_rate_range_min').val(),
			max: $('#weight_rate_range_max').val(),
			unit: 'lb',
			amount_type: $('#weight_rate_amount_type').val(),
			percent_source: $('#weight_rate_percent_source').val(),
			amount: $('#weight_rate_amount').val(),
			product_category: $('#weight_category_names').val()
		};
		var newRowContent = buildWeightBasedShippingRateRow(rate);

		if (!field_id) {
			$('#weight_rates_tbl tbody').append(newRowContent);
			selectedrates.push(rate);
		}
		else {
			$('#weight_rates_tbl tr a[data-value="' + field_id + '"]').closest('tr').html($(newRowContent).html());

			var position = -1;
			$.each(selectedrates, function(i, v) {
				if (v.id == field_id) {
					position = i;
				}
			});
			if (position >= 0) {
				selectedrates[position] = rate;
			}
		}
		$('#weightmodaldiv').modal('hide');
	});

	$('#savePrices').on('click', function() {
		// Update selected categories if user did not click off picker
		$pricecategorypicker.trigger('select2:close');
		//validate price form
		if (!$("#priceForm").valid()) {
			return false;
		}

		var field_id = $('#price_rate_id').val();
		var rate = {
			id: field_id,
			name: $('#price_rate_name').val(),
			min: $('#price_rate_range_min').val(),
			max: $('#price_rate_range_max').val(),
			unit: currency_code,
			amount_type: $('#price_rate_amount_type').val(),
			percent_source: $('#price_rate_percent_source').val(),
			amount: $('#price_rate_amount').val(),
			product_category: $('#price_category_names').val(),
			free_shipping: $('#price_free_shipping').is(':checked') ? '1' : '0'
		};
		var newRowContent = buildPriceBasedShippingRateRow(rate);

		if (!field_id) {
			$('#price_rates_tbl tbody').append(newRowContent);
			selectedpricerates.push(rate);
		}
		else {
			$('#price_rates_tbl tr a[data-value=' + field_id + ']').closest('tr').html($(newRowContent).html());

			var position = -1;
			$.each(selectedpricerates, function(i, v) {
				if (v.id == field_id) {
					position = i;
				}
			});
			if (position >= 0) {
				selectedpricerates[position] = rate;
			}
		}
		$('#pricemodaldiv').modal('hide');
	});

	$('#saveUnits').on('click', function() {
		// Update selected categories if user did not click off picker
		$unitcategorypicker.trigger('select2:close');
		//validate unit form
		if (!$("#unitForm").valid()) {
			return false;
		}

		var field_id = $('#unit_rate_id').val();
		var rate = {
			id: field_id,
			name: $('#unit_rate_name').val(),
			min: $('#unit_rate_range_min').val(),
			max: $('#unit_rate_range_max').val(),
			amount_type: $('#unit_rate_amount_type').val(),
			percent_source: $('#unit_rate_percent_source').val(),
			amount: $('#unit_rate_amount').val(),
			product_category: $('#unit_category_names').val(),
			free_shipping: $('#unit_free_shipping').is(':checked') ? '1' : '0'
		};
		var newRowContent = buildUnitBasedShippingRateRow(rate);

		if (!field_id) {
			$('#unit_rates_tbl tbody').append(newRowContent);
			selectedunitrates.push(rate);
		}
		else {
			$('#unit_rates_tbl tr a[data-value=' + field_id + ']').closest('tr').html($(newRowContent).html());

			var position = -1;
			$.each(selectedunitrates, function(i, v) {
				if (v.id == field_id) {
					position = i;
				}
			});
			if (position >= 0) {
				selectedunitrates[position] = rate;
			}
		}
		$('#unitmodaldiv').modal('hide');
	});

	$('#saveCarrier').on('click', function() {
		var c_id = $('#carrier_id').val();
		var c_percent = $('#carrier_percentage').val();
		var c_flat_fee = $('#carrier_flat_fee').val();
		var rate_text = "";
		//Rate adjustment
		if (parseFloat(c_percent) > 0 || parseFloat(c_flat_fee) > 0) {
			rate_text = "Rate adjustment: ";
			rate_text += parseFloat(c_percent) > 0 ? c_percent + "%" : "";
			rate_text += (parseFloat(c_percent) > 0 && parseFloat(c_flat_fee) > 0) ? " + " : "";
			rate_text += parseFloat(c_flat_fee) > 0 ? formatCurrency(c_flat_fee) : "";
		}
		var carrier_rates = $('#carrier_rates');
		var content = carrier_zone_element;
		var position = -1;
		if ($.inArray(c_id, selectedcarriers) == -1) {
			selectedcarriers.push(c_id);
			var c_data = { "id": 0, "carrier_id": c_id, "rates": { "percentage": c_percent, "flat_fee": c_flat_fee } };
			selectedcarriers_details.push(c_data);
			content = content.replace("carrier_name", $('#carrier_id option:selected').text());
			content = content.replace("carrier rate", rate_text);
			content = content.replace("zone_carrier_id_val", c_id);
			content = content.replace("zone_carrier_val", 0);
			content = content.replace("a_zone_carrier_val", 0);
			content = content.replace("e_zone_carrier_val", 0);
			carrier_rates.append(content);

			$('#carriermodaldiv').modal('hide');
		}
		else {
			$.each(selectedcarriers_details, function(i, v) {
				if (v.carrier_id == parseInt(c_id)) {
					position = i;
				}
			});
			if (position >= 0) {
				var id = selectedcarriers_details[position]["id"];
				selectedcarriers_details[position]["rates"] = { "percentage": c_percent, "flat_fee": c_flat_fee };
				$('input.zone_carrier_id[value="' + c_id + '"]').closest('.carrierzoneelement').remove();
				content = content.replace("carrier_name", $('#carrier_id option:selected').text());
				content = content.replace("carrier rate", rate_text);
				content = content.replace("zone_carrier_id_val", c_id);
				content = content.replace("zone_carrier_val", id);
				content = content.replace("a_zone_carrier_val", id);
				content = content.replace("e_zone_carrier_val", id);
				carrier_rates.append(content);

				$('#carriermodaldiv').modal('hide');
			}
		}
	});

	//Setup Weight Form Validation
	// override jquery validate plugin defaults
	$.validator.setDefaults({
		highlight: function(element) {
			$(element).closest('.form-group').addClass('has-error');
		},
		unhighlight: function(element) {
			$(element).closest('.form-group').removeClass('has-error');
		},
		errorElement: 'span',
		errorClass: 'help-block',
		errorPlacement: function(error, element) {
			if (element.parent('.input-group').length) {
				error.insertAfter(element.parent());
			} else {
				error.insertAfter(element);
			}
		},
		success: function(label) {
			label.text('');
		}
	});

	$("#zoneForm").validate({
		rules: {
			zone_name: "required"
		},
		messages: {
			zone_name: "Provide a valid name"
		}
	});

	$("form[name='weightForm']").validate({
		rules: {
			weight_rate_name: "required",
			weight_rate_range_min: "required",
			weight_rate_range_max: "required",
			weight_rate_amount: "required"
		},
		messages: {
			weight_rate_name: "Please provide a name",
			weight_rate_range_min: "Enter a valid minimum weight",
			weight_rate_range_max: "Enter a valid maximum weight",
			weight_rate_amount: "Enter weight amount"
		}
	});

	$("form[name='priceForm']").validate({
		rules: {
			price_rate_name: "required",
			price_rate_range_min: "required",
			price_rate_range_max: "required",
			price_rate_amount: "required"
		},
		messages: {
			price_rate_name: "Please provide a name",
			price_rate_range_min: "Enter a valid minimum price",
			price_rate_range_max: "Enter a valid maximum price",
			price_rate_amount: "Enter price amount"
		}
	});

	$("form[name='unitForm']").validate({
		rules: {
			unit_rate_name: "required",
			unit_rate_range_min: "required",
			unit_rate_range_max: "required",
			unit_rate_amount: "required"
		},
		messages: {
			unit_rate_name: "Please provide a name",
			unit_rate_range_min: "Enter a valid minimum unit",
			unit_rate_range_max: "Enter a valid maximum unit",
			unit_rate_amount: "Enter unit amount"
		}
	});

	$('#cancelShippingZone').on('click', function() {
		window.location = "<?php echo BASE_PATH; ?>shipment/configuration";
	});

	$('input.decimal').on('change', function() {
		$(this).val(formatDecimal($(this).val()));
	});

	$('.selectpicker').selectpicker();
	var $weightcategorypicker = $('#weight_rate_category');
	$weightcategorypicker.on('select2:close', function() {
		if ($('#weight_rate_category').val() != null) {
			$('#weight_category_names').val($('#weight_rate_category').val());
		}
		else {
			$('#weight_category_names').val("");
		}
	});

	var $pricecategorypicker = $('#price_rate_category');
	$pricecategorypicker.on('select2:close', function() {
		if ($('#price_rate_category').val() != null) {
			$('#price_category_names').val($('#price_rate_category').val());
		}
		else {
			$('#price_category_names').val("");
		}
	});

	$('#price_free_shipping').on('change', function() {
		if ($(this).is(':checked')) {
			$('#price_rate_amount').val('0.00');
		}
	});

	var $unitcategorypicker = $('#unit_rate_category');
	$unitcategorypicker.on('select2:close', function() {
		if ($('#unit_rate_category').val() != null) {
			$('#unit_category_names').val($('#unit_rate_category').val());
		}
		else {
			$('#unit_category_names').val("");
		}
	});

	$('#unit_free_shipping').on('change', function() {
		if ($(this).is(':checked')) {
			$('#unit_rate_amount').val('0.00');
		}
	});

	$('#weight_rate_amount_type').on('change', function() {
		$('#weight_rate_percent_source').toggle((this.value === 'percent'));
	});
	$('#price_rate_amount_type').on('change', function() {
		$('#price_rate_percent_source').toggle((this.value === 'percent'));
	});
	$('#unit_rate_amount_type').on('change', function() {
		$('#unit_rate_percent_source').toggle((this.value === 'percent'));
	});
});

function checkCountryInList(country_code) {
	var exists = false;
	// Country list is empty
	// So we need to add states for the first country
	var empty_country_list = $('input.country_codes').length == 0;
	if (empty_country_list) {
		statesadd(country_code);
	}
	$('input.country_codes').each(function() {
		if ($(this).val() == country_code) {
			exists = true;
		} else {
			//Load the state/provinces for the country
			statesadd(country_code);
		}
	});
	return exists;
}

function initCountryActions() {
	$('#country_zones').on('click', '.country', function() {
		var country_code = $(this).attr('data-value');
		//Remove Country
		var position = -1;
		$.each(countries, function(i, v) {
			if (v == country_code) {
				position = i;
			}
		});
		if (position >= 0) {
			countries.splice(position, 1);
		}

		//Remove Country States
		position = -1;
		$.each(selectedstates, function(i, v) {
			if (v.id == country_code) {
				position = i;
			}
		});
		if (position >= 0) {
			selectedstates.splice(position, 1);
		}

		//Uncheck from List of Country in Popup Modal
		$('input:checkbox.countrychk').each(function() {
			if ($(this).val() == country_code) {
				$(this).prop('checked', false);
			}
		});

		$(this).closest('.countryzoneelement').remove();
	}).on('click', 'a.editcountry', function() {
		statesadd($(this).attr('data-value'));
		$('#statemodaldiv').modal('show');
	});
}

function countryadd() {
	$('#saveShippingZone').on('click', function() {
		if (!$('#zoneForm').valid()) {
			return false;
		}
		else {
			countries = [];
			$('.country_codes').each(function() {
				countries.push($(this).val());
			});
			if (countries.length <= 0) {
				$('#errormsg').html("Zone must contain at least one country");
				$('#errormsg').addClass('critical').show();
				return false;
			}
			if (
				($('#addweights').length && selectedrates.length <= 0)
				&& ($('#addprices').length && selectedpricerates.length <= 0)
				&& ($('#addunits').length && selectedunitrates.length <= 0)
				&& ($('#addCarrier').length && selectedcarriers_details.length <= 0)
			) {
				$('#errormsg').html("You need to add at least one rate in this shipping zone");
				$('#errormsg').addClass('warning').show();
				return false;
			}
			$.ajax({
				type: "POST",
				url: "<?php echo BASE_PATH; ?>shipment/manageshippingzones/save",
				data: {
					"zone_id": $('#zone_id').val(),
					"zone_name": $('#zone_name').val(),
					"zone_option": $('#zone_option').val(),
					"distributor_id": $(':input[name="distributor_id"]').val(),
					"countries": JSON.parse(JSON.stringify(countries)),
					"states": JSON.stringify(selectedstates),
					"weights": JSON.stringify(selectedrates),
					"deleteweights": JSON.stringify(deleterates),
					"prices": JSON.stringify(selectedpricerates),
					"deleteprices": JSON.stringify(deletepricerates),
					"units": JSON.stringify(selectedunitrates),
					"deleteunits": JSON.stringify(deleteunitrates),
					"carriers": JSON.stringify(selectedcarriers_details),
					"deletecarriers": JSON.stringify(deletedcarriers_details),
					"ZoneTaxOverride": $('.js-tax-override-row-form').map(function() {
						var formArray = $(this).find('input').serializeArray();
						var jsonFormData = {};
						for (var i = 0; i < formArray.length; i++){
							var name = formArray[i]['name'].split('[').pop().slice(0, -1);
							jsonFormData[name] = formArray[i]['value'];
						}
						// Double wrap array result because jQuery.map likes to concat 2D arrays
						return [jsonFormData];
					}).get()
				},
				dataType: 'json',
				success: function(result) {
					if (result.id) {
						window.location = "<?php echo BASE_PATH; ?>shipment/shippingzones/edit/" + result.id;
					}
				},
				error: function(request, status, error) {
					alert("Error processing page");
				}
			});
		}
	});
}

function statesadd(country_id) {
	$('#statelist').html("");
	var list = "<ul>";
	if (states[country_id] == undefined) {
		$.ajax({
			type: "POST",
			url: "<?php echo BASE_PATH; ?>getstatesbycountry",
			data: { "country_id": country_id },
			dataType: 'json',
			async: false,
			success: function(result) {
				states[country_id] = result;

				//Add all states/remaining to selectedstates
				var difference = [];
				$.grep(Object.keys(states[country_id]), function(el) {
					if ($.inArray(el, usercountries[country_id]) <= -1) {
						difference.push(el);
					}
				});
				selectedstates.push({ id: country_id, states: difference });
			},
			error: function(request, status, error) {
				console.error("Error processing page");
			}
		});
	}
	$.each(states[country_id], function(i, v) {
		var checked = ($.inArray("" + i, zonestatesbycountry[country_id]) > -1) ? "checked" : "";
		//check rest of the country's state if country exist in another with not all states
		if ($.inArray("" + country_id, countries) <= -1) {
			checked = ($.inArray(i, usercountries[country_id]) <= -1) ? "checked" : "";
		}
		//disable states in country that are in another zone
		var disablestate = "";
		var disableclass = "";
		var txtmessage = "";
		if ($.inArray("" + country_id, countries) > -1 && checked == "") {
			if ($.inArray(i, usercountries[country_id]) > -1) {
				disablestate = "disabled";
				disableclass = "disabled";
				txtmessage = " (already in another shipping zone)";
			}
		}
		if ($.inArray("" + country_id, tempcountries) > -1 && checked == "") {
			if ($.inArray(i, usercountries[country_id]) > -1) {
				disablestate = "disabled";
				disableclass = "disabled";
				txtmessage = " (already in another shipping zone)";
			}
		}
		list += '<li class="' + disableclass + '">' +
				'<span><input type="checkbox" class="statechk" value="' + i + '" ' + checked + ' ' + disablestate + '  /></span>' +
				'<span>' + v + '</span>' +
				'<span>' + txtmessage + '</span>' +
				'<hr />' +
			'</li>';
	});
	list += "<ul>";
	$('#state_country_id').val(country_id);
	$('#statelist').append(list);

}

function addSelectedStates(selected_states) {
	$('#' + selected_states.id).html(" (" + selected_states.states.length + " of " + Object.keys(states[selected_states.id]).length + ")");
}

function initWeightRateActions() {
	$('#weight_rates_tbl').on('click', '.weightrate', function() {
		var $this = $(this);
		var $row = $this.closest('tr');

		var rate_id = $row.find('a.editrate').attr('data-value');
		var rate_name = $this.attr('data-value');

		var position = selectedrates.findIndex(function(v) {
			return (rate_id) ? v.id == rate_id : !v.id && v.name === rate_name;
		});
		if (position >= 0) {
			if (rate_id) {
				deleterates.push(selectedrates[position]);
			}
			selectedrates.splice(position, 1);

			$row.remove();
		}
	}).on('click', 'a.editrate', function() {
		var $this = $(this);
		var $row = $this.closest('tr');

		var rate_id = $this.attr('data-value');
		var rate_name = $row.find('.weightrate').attr('data-value');

		var position = selectedrates.findIndex(function(v) {
			return (rate_id) ? v.id == rate_id : !v.id && v.name === rate_name;
		});
		if (position >= 0) {
			var data = selectedrates[position];
			$('#gridSystemModalLabelWeight').html("Edit weight rate");
			$('#weight_rate_id').val(data.id);
			$('#weight_rate_name').val(data.name);
			$('#weight_rate_range_min').val(data.min);
			$('#weight_rate_range_max').val(data.max);
			$('#weight_rate_amount_type').val(data.amount_type).selectpicker('refresh');
			$('#weight_rate_percent_source').val(data.percent_source).toggle(data.amount_type === 'percent');
			$('#weight_rate_amount').val(formatDecimal(data.amount));
			$('#weight_rate_category').val(data.product_category.split(',')).selectpicker('refresh');
			$('#weight_category_names').val(data.product_category);
		}
		$('#weightmodaldiv').modal('show');
	});
}

function initPriceRateActions() {
	$('#price_rates_tbl').on('click', '.pricerate', function() {
		var $this = $(this);
		var $row = $this.closest('tr');

		var rate_id = $row.find('a.editpricerate').attr('data-value');
		var rate_name = $this.attr('data-value');

		var position = selectedpricerates.findIndex(function(v) {
			return (rate_id) ? v.id == rate_id : !v.id && v.name === rate_name;
		});
		if (position >= 0) {
			if (rate_id) {
				deletepricerates.push(selectedpricerates[position]);
			}
			selectedpricerates.splice(position, 1);

			$row.remove();
		}
	}).on('click', 'a.editpricerate', function() {
		var $this = $(this);
		var $row = $this.closest('tr');

		var rate_id = $this.attr('data-value');
		var rate_name = $row.find('.pricerate').attr('data-value');

		var position = selectedpricerates.findIndex(function(v) {
			return (rate_id) ? v.id == rate_id : !v.id && v.name === rate_name;
		});
		if (position >= 0) {
			var data = selectedpricerates[position];
			$('#gridSystemModalLabelPrice').html("Edit price rate");
			$('#price_rate_id').val(data.id);
			$('#price_rate_name').val(data.name);
			$('#price_rate_range_min').val(formatDecimal(data.min));
			$('#price_rate_range_max').val(formatDecimal(data.max));
			$('#price_free_shipping').prop('checked', (data.free_shipping == '1'))
			$('#price_rate_amount_type').val(data.amount_type).selectpicker('refresh');
			$('#price_rate_percent_source').val(data.percent_source).toggle(data.amount_type === 'percent');
			$('#price_rate_amount').val(formatDecimal(data.amount));
			$('#price_rate_category').val(data.product_category.split(',')).selectpicker('refresh');
			$('#price_category_names').val(data.product_category);
		}
		$('#pricemodaldiv').modal('show');
	});
}

function initUnitRateActions() {
	$('#unit_rates_tbl').on('click', '.unitrate', function() {
		var $this = $(this);
		var $row = $this.closest('tr');

		var rate_id = $row.find('a.editunitrate').attr('data-value');
		var rate_name = $this.attr('data-value');

		var position = selectedunitrates.findIndex(function(v) {
			return (rate_id) ? v.id == rate_id : !v.id && v.name === rate_name;
		});
		if (position >= 0) {
			if (rate_id) {
				deleteunitrates.push(selectedunitrates[position]);
			}
			selectedunitrates.splice(position, 1);

			$row.remove();
		}
	}).on('click', 'a.editunitrate', function() {
		var $this = $(this);
		var $row = $this.closest('tr');

		var rate_id = $this.attr('data-value');
		var rate_name = $row.find('.unitrate').attr('data-value');

		var position = selectedunitrates.findIndex(function(v) {
			return (rate_id) ? v.id == rate_id : !v.id && v.name === rate_name;
		});
		if (position >= 0) {
			var data = selectedunitrates[position];
			$('#gridSystemModalLabelUnit').html("Edit unit rate");
			$('#unit_rate_id').val(data.id);
			$('#unit_rate_name').val(data.name);
			$('#unit_rate_range_min').val(formatDecimal(data.min));
			$('#unit_rate_range_max').val(formatDecimal(data.max));
			$('#unit_free_shipping').prop('checked', (data.free_shipping == '1'))
			$('#unit_rate_amount_type').val(data.amount_type).selectpicker('refresh');
			$('#unit_rate_percent_source').val(data.percent_source).toggle(data.amount_type === 'percent');
			$('#unit_rate_amount').val(formatDecimal(data.amount));
			$('#unit_rate_category').val(data.product_category.split(',')).selectpicker('refresh');
			$('#unit_category_names').val(data.product_category);
		}
		$('#unitmodaldiv').modal('show');
	});
}

function disableRestOfWorld(boolval) {
	//Disable Rest of the World Addition if Countries exists in Zone
	if (boolval) {
		$('#countrylist > ul > li:nth-child(1) > span:nth-child(1) > input[type="checkbox"]').prop('disabled', true);
		$('#countrylist > ul > li:nth-child(1)').addClass('disabled');
	}
	else {
		$('#countrylist > ul > li:nth-child(1) > span:nth-child(1) > input[type="checkbox"]').prop('disabled', false);
		$('#countrylist > ul > li:nth-child(1)').removeClass('disabled');
	}
}

function disableCountryByValue() {
	var position = -1;
	var country_code = 0;
	var country_state_count = 0;
	var country_selected_state_count = 0;

	$('input[name=selected_states_count]').each(function() {
		country_code = $(this).next().next().val();
		country_state_count = $(this).next().val();
		country_selected_state_count = $(this).val();
		//Disable already selected country with all states
		if (country_state_count == country_selected_state_count && getCountOfStates(country_code) == country_selected_state_count) {
			$(this).parent().find('*').prop('disabled', true);
			$(this).parent().addClass('disabled');
			$(this).prev().html("already in another shipping zone");
		}
		else if (getCountOfStates(country_code) > 0) {
			var remstatescount = parseInt(country_state_count) - parseInt(country_selected_state_count);
			$(this).prev().prev().html("(" + remstatescount + " of " + country_state_count + ")");
		}
	});
}

function getCountOfStates(country_code) {
	if ($.inArray(country_code, countries) > -1) {
		return 0;
	}
	var count = 0;
	if (usercountries[country_code] !== undefined) {
		count = usercountries[country_code].length
	}
	return count;
}

function ZoneInfo(ajaxUrl, method, dataInfo) {
	$.ajax({
		type: method,
		async: false,
		url: ajaxUrl,
		data: dataInfo,
		dataType: 'json',
		success: function(result) {
			window.location = "<?php echo BASE_PATH; ?>shipment/configuration";
		},
		error: function(request, status, error) {
		}
	});
}

function initCarrierActions() {
	$('#carrier_rates').on('click', 'a.editcarrier', function() {
		var $this = $(this);
		var $row = $this.closest('.carrierzoneelement');

		var option_id = $this.attr('data-value');
		var carrier_id = $row.find('input.zone_carrier_id').val();

		var position = selectedcarriers_details.findIndex(function(v) {
			return (option_id) ? v.id == option_id : !v.id && v.carrier_id == carrier_id;
		});
		if (position >= 0) {
			var data = selectedcarriers_details[position];
			$('#carrier_id').val(data.carrier_id)
				.trigger('change');
			$('#carrier_percentage').val(data.rates.percentage);
			$('#carrier_flat_fee').val(data.rates.flat_fee);
		}
		$('#carriermodaldiv').modal('show');
	}).on('click', 'button.btn-close.carrier', function() {
		var $this = $(this);
		var $row = $this.closest('.carrierzoneelement');

		var option_id = $this.attr('data-value');
		var carrier_id = $row.find('input.zone_carrier_id').val();

		var position = selectedcarriers_details.findIndex(function(v) {
			return (option_id) ? v.id == option_id : !v.id && v.carrier_id == carrier_id;
		});
		if (position >= 0) {
			var data = selectedcarriers_details[position];
			if (option_id) {
				deletedcarriers_details.push(data);
			}
			selectedcarriers.splice($.inArray(data.carrier_id, selectedcarriers), 1);
			selectedcarriers_details.splice(position, 1);

			$row.remove();
		}
	});
}

function formatCurrency(total) {
	var neg = (total < 0);
	return (neg ? '-' : '') + formatDecimal(Math.abs(total)).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}

function formatDecimal(total) {
	if (isNaN(total)) {
		total = 0;
	}
	return parseFloat(total).toFixed(2);
}
</script>
