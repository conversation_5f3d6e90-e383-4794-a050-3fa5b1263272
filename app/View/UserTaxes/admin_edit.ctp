<?php
/**
 * @var AppView $this
 * @var string $countryName
 * @var string[] $states
 */

$this->layout = 'admin';
$this->assign('title', 'Tax Settings / ' . h($countryName));
?>
<?php echo $this->Form->create('StateTax', [
	'id' => 'EditStateTax',
	'inputDefaults' => [
		'label' => false,
		'div' => false,
		'error' => ['attributes' => ['class' => 'help-block']],
	],
]); ?>
	<div class="form_title">
		<div class="row-fluid">
			<div class="span8">
				<?php echo $this->Layout->breadcrumbHeader([
					'Tax Settings' => ['admin' => true, 'controller' => 'user_taxes', 'action' => 'index'],
					h($countryName),
				]); ?>
			</div>
			<div class="span4">
				<div style="text-align: right;">
					<button type="submit" class="btn btn-primary">Save</button>
				</div>
			</div>
		</div>
	</div>
	<div id="CountryTaxBlock" class="row-fluid">
		<div class="span4">
			<h2>Country Tax</h2>
			<p>Base tax rate to be used if a state tax rate is not specified.</p>
		</div>
		<div class="span8">
			<div class="next-card">
				<div class="control-group">
					<?php echo $this->Form->label('CountryTax.percentage', $countryName, ['class' => 'control-label']); ?>
					<div style="display: inline-block;">
						<?php echo $this->Form->input('CountryTax.percentage', [
							'id' => 'CountryTaxPercentage',
							'type' => 'number',
							'min' => '0',
							'step' => '0.001',
							'placeholder' => '0.000',
							'between' => '<div class="input-append">',
							'after' => '<span class="add-on">%</span></div>',
						]); ?>
					</div>
					<div style="display: inline-block;">
						<?php echo $this->Form->input('CountryTax.name', [
							'id' => 'CountryTaxName',
							'placeholder' => 'Tax',
							'title' => 'Tax Name',
						]); ?>
					</div>
					<?php echo $this->Form->input('CountryTax.includes_shipping', [
						'id' => 'CountryTaxIncludesShipping',
						'type' => 'checkbox',
						'default' => true,
						'required' => false,
						'class' => 'mt-1',
						'div' => 'checkbox',
						'label' => ['text' => 'Charge tax on shipping rates', 'class' => 'inline-block'],
					]); ?>
					<?php echo $this->Form->input('CountryTax.included_in_prices', [
						'id' => 'CountryTaxIncludedInPrices',
						'type' => 'checkbox',
						'default' => false,
						'required' => false,
						'class' => 'mt-1',
						'div' => 'checkbox',
						'label' => ['text' => 'Include sales tax in product price and shipping rate', 'class' => 'inline-block'],
					]); ?>
				</div>
			</div>
		</div>
	</div>
	<div id="StateTaxBlock" class="row-fluid">
		<div class="span4">
			<h2>State Taxes</h2>
			<p>State tax rates that override the country tax rate. Clearing a state tax rate makes the state follow the country tax rate.</p>
		</div>
		<div class="span8">
			<div class="next-card">
			<?php foreach ($states as $stateId => $stateName) { ?>
				<div class="control-group">
					<?php echo $this->Form->hidden("{$stateId}.state_id", ['value' => $stateId]); ?>
					<?php echo $this->Form->label("{$stateId}.percentage", $stateName, ['class' => 'control-label']); ?>
					<?php echo $this->Form->input("{$stateId}.uses_api", [
						'type' => 'radio',
						'options' => [
							'0' => implode(' ', [
								$this->Form->input("{$stateId}.percentage", [
									'type' => 'number',
									'min' => '0',
									'step' => '0.001',
									'placeholder' => $this->request->data['CountryTax']['percentage'] ?? '0.000',
									'required' => false,
									'disabled' => $this->request->data("StateTax.{$stateId}.uses_api"),
									'class' => 'state-tax-percentage',
									'div' => 'inline-block w-[245px]',
									'between' => '<div class="input-append">',
									'after' => '<span class="add-on">%</span></div>',
								]),
								$this->Form->input("{$stateId}.name", [
									'placeholder' => $this->request->data['CountryTax']['name'] ?? 'Tax',
									'title' => 'Tax Name',
									'disabled' => $this->request->data("StateTax.{$stateId}.name") === null,
									'class' => 'state-tax-name',
									'div' => 'inline-block',
								]),
								'<br />',
								$this->Form->input("{$stateId}.includes_shipping", [
									'type' => 'checkbox',
									'default' => $this->request->data['CountryTax']['includes_shipping'] ?? true,
									'disabled' => $this->request->data("StateTax.{$stateId}.includes_shipping") === null || $this->request->data("StateTax.{$stateId}.uses_api"),
									'class' => 'state-tax-includes-shipping mt-1',
									'div' => 'checkbox inline-block w-[245px]',
									'label' => ['text' => 'Charge tax on shipping rates', 'class' => 'inline-block'],
								]),
								$this->Form->input("{$stateId}.uses_origin_based_rates", [
									'type' => 'checkbox',
									'default' => false,
									'class' => 'state-tax-uses-origin-based-rate mt-1',
									'div' => 'checkbox inline-block',
									'label' => ['text' => 'Uses origin-based rates', 'class' => 'inline-block'],
								]),
							]),
							'1' => 'Use Api',
						],
						'default' => '0',
						'class' => 'state-tax-uses-api',
						'legend' => false,
						'label' => ['class' => 'inline-block'],
						'before' => '<div class="radio">',
						'separator' => '</div><div class="radio">',
						'after' => '</div>',
					]); ?>
				</div>
			<?php } ?>
			</div>
		</div>
	</div>
	<div class="footer-card" style="text-align: center;">
		<a href="#top" class="no-of-retails">
			<span>Back to top</span>
		</a>
	</div>
<?php echo $this->Form->end(); ?>
<?php $this->start('script'); ?>
<script>
$(function() {
	$('#CountryTaxPercentage').on('change input', function() {
		$('input.state-tax-percentage').attr('placeholder', this.value || this.placeholder);
	});
	$('#CountryTaxName').on('change input', function() {
		$('input.state-tax-name').attr('placeholder', this.value || this.placeholder);
	});
	$('#CountryTaxIncludesShipping').on('change', function() {
		var checked = this.checked;
		$('input.state-tax-includes-shipping').each(function() {
			var $checkbox = $(this);
			if ($checkbox.prop('disabled')) {
				$checkbox.prop('checked', checked);
			}
		});
	});
	$('#StateTaxBlock').on('change input', 'input.state-tax-percentage', function() {
		var value = this.value;
		var $group = $(this).closest('.control-group');
		var $name = $group.find('input.state-tax-name');
		var $includes_shipping = $group.find('input.state-tax-includes-shipping');
		var $uses_origin_based_rates = $group.find('input.state-tax-uses-origin-based-rate');

		var isUnsetTax = (value === '' && !$uses_origin_based_rates.prop('checked'));
		if (isUnsetTax) {
			$name.val('');
			$includes_shipping.prop('checked', $('#CountryTaxIncludesShipping').prop('checked'));
		}
		$name.prop('disabled', isUnsetTax);
		// Also disable the hidden checkbox input
		$group.find(`input[name="${$includes_shipping.attr('name')}"]`).prop('disabled', isUnsetTax);
	}).on('change', 'input.state-tax-uses-origin-based-rate', function() {
		var $this = $(this);
		var $group = $(this).closest('.control-group');
		if ($group.find('input.state-tax-uses-api:checked').val() === '1') {
			return;
		}
		var $tax = $group.find('input.state-tax-percentage');
		var countryTax = $('#CountryTaxPercentage').val();
		if ($this.prop('checked') && $tax.val() === '') {
			$tax.val(countryTax);
		} else if (!$this.prop('checked') && $tax.val() === countryTax) {
			$tax.val('');
		}
		$tax.trigger('change');
	}).on('change', 'input.state-tax-uses-api', function() {
		var $group = $(this).closest('.control-group');
		var $tax = $group.find('input.state-tax-percentage');
		var $name = $group.find('input.state-tax-name');
		var $includes_shipping = $group.find('input.state-tax-includes-shipping');
		if (this.value === '1') {
			$tax.prop('disabled', true);
			$name.prop('disabled', false);
			// Also disable the hidden checkbox input
			$group.find(`input[name="${$includes_shipping.attr('name')}"]`).prop('disabled', true);
		} else {
			$tax.prop('disabled', false).trigger('change');
		}
	});
});
</script>
<?php $this->end(); ?>
