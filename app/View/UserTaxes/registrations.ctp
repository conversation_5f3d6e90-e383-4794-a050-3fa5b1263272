<?php
/**
 * @var AppView $this
 * @var string $countryName
 * @var array $stateTaxes
 */

$this->assign('title', __('Tax Settings') . ' / ' . h($countryName));

$countryCode = (string)$this->request->param('country_code');
$countryIsUS = ($countryCode === 'US');

$stateLabel = (string)($countryIsUS ? __('State') : __('Province'));

$description = (string)(($countryIsUS)
	? __('To charge sales tax in a state, you need to be registered with the appropriate government agency.')
	: __('To charge sales tax in a province, you need to be registered with the appropriate government agency.')
);
?>
<?php $this->start('css'); ?>
<style>
#TaxRegistrations h2 {
	margin-bottom: 10px;
}

#TaxRegistrations p {
	text-align: start;
}

#TaxRegistrationsTable th,
#TaxRegistrationsTable td {
	white-space: nowrap;
}

#TaxRegistrationsTable th:first-child,
#TaxRegistrationsTable td:first-child {
	text-align: left;
	white-space: normal;
}

#TaxRegistrationsTable th:last-child,
#TaxRegistrationsTable td:last-child {
	text-align: right;
}
</style>
<?php $this->end(); ?>
<div id="TaxRegistrations">
	<div class="header-card">
		<div class="row">
			<div class="offset-md-1 col-md-10 col-12">
				<?= $this->Layout->breadcrumbHeader([
					__('Tax Settings') => ['controller' => 'user_taxes', 'action' => 'index'],
					h($countryName),
				]) ?>
			</div>
		</div>
	</div>
	<div class="row">
		<div class="offset-md-1 col-md-10 col-12">
			<div class="row">
				<div class="col-sm-4 col-12">
					<h2><?= __('Sales Tax'); ?></h2>
					<p><?= $description; ?></p>
				</div>
				<div class="col-sm-8 col-12">
					<div class="next-card">
					<?php if ($countryIsUS) { ?>
						<div style="text-align: right;">
							<?= $this->Html->link(__('Add Tax Registration'),
								['controller' => 'user_taxes', 'action' => 'edit_registration', 'country_code' => $countryCode],
								['class' => 'btn btn-primary ajax-edit-registration']
							) ?>
						</div>
					<?php } ?>
						<div class="table-responsive">
							<table id="TaxRegistrationsTable" class="table table-hover table-condensed">
								<thead>
									<tr>
										<th><?= $stateLabel ?></th>
										<th><?= __('Tax Rate') ?></th>
										<th><?= __('Tax Name') ?></th>
										<th><?= __('Sales Tax ID') ?></th>
										<th><span class="visually-hidden"><?= __('Actions') ?></span></th>
									</tr>
								</thead>
								<tbody>
								<?php foreach ($stateTaxes as $stateId => $tax) { ?>
									<tr>
										<td><?= h($tax['state_name']) ?></td>
										<td><?= ($tax['uses_api'])
											? sprintf('<div data-tooltipster="" title="%s">%s</div>', __('Looked up on demand'), __('Automatic'))
											: sprintf('<div>%s %%</div>', h($tax['tax_percentage']))
												. ($tax['uses_manual_rate'] ? sprintf('<div>(%s)</div>', __('Manual')) : '')
										?></td>
										<td><?= h($tax['name']) ?></td>
										<td><?= h($tax['tax_id_number']) ?></td>
										<td>
											<?= $this->Html->link(__('Edit'),
												['controller' => 'user_taxes', 'action' => 'edit_registration', 'country_code' => $countryCode, 'state_id' => $stateId],
												['class' => 'btn btn-default ajax-edit-registration']
											) ?>
											<?= ($countryIsUS)
												? $this->Html->div('d-inline-block', $this->Form->postLink(
													'<i class="far fa-trash-alt" aria-hidden="true"></i>',
													['controller' => 'user_taxes', 'action' => 'delete', $tax['id']],
													[
														'method' => 'DELETE',
														'escapeTitle' => false,
														'confirm' => __('Are you sure you want to stop collecting tax from %s?', $tax['state_name']),
														'class' => 'btn btn-default' . ($tax['is_local'] ? ' disabled' : ''),
													]
												), ['data-tooltipster' => '', 'title' => ($tax['is_local'] ? __('Local state is always registered') : __('Remove State'))])
												: ''
											?>
										</td>
									</tr>
								<?php } ?>
								</tbody>
							</table>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="footer-card" style="text-align: center;">
		<a href="#top" class="no-of-retails">
			<span><?= __('Back to top'); ?></span>
		</a>
	</div>
</div>
<?php $this->start('script'); ?>
<script>
$(function() {
	$('.ajax-edit-registration').on('click', function(e) {
		e.preventDefault();
		$.get(this.href, function(data) {
			bootbox.dialog({
				title: '<?= __('Tax Registration'); ?>',
				message: data,
				className: 'bootbox--form modal--view modal--shipping'
			});
		});
	});
});
</script>
<?php $this->end(); ?>
