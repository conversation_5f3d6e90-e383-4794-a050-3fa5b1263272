<?php
/**
 * @var AppView $this
 * @var array $shipearly_user
 * @var array $Contactpersons
 * @var array $country
 * @var array $states
 * @var string $telephone
 * @var string[] $timezoneOptions
 * @var string $facebook
 * @var string $twitter
 */
?>
<?php echo $this->Html->script(array('signup')); ?>
	<?php echo $this->element('content_header', ['title_for_layout' => __('My Account')]); ?>
<?php
if (User::revertAuthParent($shipearly_user['User'])['user_type'] === User::TYPE_STAFF) {
	echo $this->element('Users/profile/profile_store_associate');
} elseif ($shipearly_user['User']['user_type'] === User::TYPE_SALES_REP) {
	echo $this->element('Users/profile/profile_sales_rep');
} else {
	echo $this->element('Users/profile/profile_brand_retailer');
}
?>
	<script type="text/javascript">
		$('#invalid-company-logo').hide();
		var user_type = "<?php echo $shipearly_user['User']['user_type'];?>";
		var companyLogo = "<?php echo $shipearly_user['User']['avatar']; ?>";
		$('#signup').submit(function() {
			if (!$("#signup").valid()) {
				$('.msg_panel').html(FlashErrorUpdate.replace("{{FlashErrorUpdate}}", "Please make sure that you have entered all the mandatory fields in all the sections"));
			}
			if(user_type == 'Manufacturer') {
				if ((companyLogo == '') && (!$("#avatar").val())) {
					$('#invalid-company-logo').show();
					$('#invalid-company-logo').html('please upload company logo');
					return false;
				} else {
					 $('#invalid-company-logo').hide();
				}
			}
		});
		$("#avatar").imagePreview(function(src, file) {
			$('#list').html(
				$('<img class="avatar-preview">')
					.attr('src', src)
					.attr('title', encodeURI(file.name))
			);
		});
		$("#avatar").change(function() {
			if(user_type == 'Manufacturer') {
				if ((companyLogo == '') && (!$("#avatar").val())) {
					$('#invalid-company-logo').show();
					$('#invalid-company-logo').html('please upload company logo');
					return false;
				} else {
					$('#invalid-company-logo').hide();
					$("#resetsubmit").removeAttr("disabled", "true");
				}
			}
		});
		$("#contactInformation").on('click', function () {
			if(user_type == 'Manufacturer') {
				if ((companyLogo == '') && (!$("#avatar").val()) || (!$("#company_name").val())) {
					 $('.msg_panel').html(FlashErrorUpdate.replace("{{FlashErrorUpdate}}", "Please make sure that you have entered all the mandatory fields"));
					 return false;
				}
			}
		});
		$("#storeTimings").on('click', function () {
			if(user_type == 'Manufacturer') {
				if ((companyLogo == '') && (!$("#avatar").val()) || (!$("#company_name").val())) {
					 $('.msg_panel').html(FlashErrorUpdate.replace("{{FlashErrorUpdate}}", "Please make sure that you have entered all the mandatory fields"));
					 return false;
				}
			}
		});
		$(".upload-click").click(function () {
			$("#avatar").trigger("click");
			$("#resetsubmit").removeAttr("disabled", "true");
		});
	</script>
<script type="text/javascript">
$(function() {
	$('#country_id').bindCountryChangeEvents({
		getStatesUrl: "<?= Router::url(['controller' => 'users', 'action' => 'getstates']) ?>",
		getTimezonesUrl: "<?= Router::url(['controller' => 'users', 'action' => 'get_timezone_options']) ?>",
		statesSelector: '#state_id',
		zipcodeSelector: '#zipcode',
		timezonesSelector: '#timezone',
		telephoneSelector: '#telephone'
	});
});
</script>
