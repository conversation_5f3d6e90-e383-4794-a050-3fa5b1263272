<?php
/**
 * @var AppView $this
 */
?>
<?php echo $this->element('content_header', ['title_for_layout' => __('Subscription')]); ?>
<div class="clear">
    <!-- <h3>Hi Manufacture</h3> -->
    <?php if(empty($subscribed)) {?>
        <!-- <p>You are currently subscribed under the "Core module" by default, and you need to pay $400/Month to activate your subscription.</p>
        <p>Kindly proceed to make the payment and activate your susbcription.</p> -->
        <div class="list_option">
            <div>
                <i class="fas fa-times-circle"> </i>
                <span><?= __('Core Application'); ?></span>
            </div>
            <button class="btn btn-primary reset-link" id="customButton" style="display: none;"><?= __('Subscribe'); ?></button>
        </div>
    <?php } else { ?>
        <!-- <p>You are currently subscribed under the "Core module" by default, and your subscription is active now.</p> -->
        <div class="list_option">
            <div>
                <i class="fas fa-check-circle"> </i>
                <span><?= __('Core Application'); ?></span>
            </div>
        </div>
    <?php } ?>
</div>

<script src="https://checkout.stripe.com/v2/checkout.js"></script>
<script>
$('#customButton').click(function(){
    $('#customButton').prop('disabled', true);
    var key = "<?php echo STRIPE_PUBLISHABLE_KEY; ?>";
    var emailAdd = "<?php echo $user['User']['email_address']; ?>";
    var token = function(res){
        var $input = $('<input type=hidden name=stripeToken />').val(res.id);
        shipearlyPopup.showLoading();
        $.ajax({
            type: "POST",
            url: "<?php echo BASE_PATH; ?>shipearly/subscription?id=<?php echo $user['User']['id']; ?>",
            data: res,
            dataType: 'json'
        }).done(function(data) {
            if(data.redirectUrl) {
                window.location = data.redirectUrl;
            }
            if(data.responseStatus == "error") {
                shipearlyPopup.hideLoading();
                location.reload();
            }
        });
    };

    StripeCheckout.open({
        key:         key,
        email:       emailAdd,
        name:        "<?= __('Card Information'); ?>",
        panelLabel:  "<?= __('Subscribe'); ?>",
        allowRememberMe: false,
        token:       token
    });
});
$(window).load(function(){
    $('#customButton').show();
});
</script>
<style type="text/css">
.list_option > div {
    margin-left: 0px;
}
</style>
