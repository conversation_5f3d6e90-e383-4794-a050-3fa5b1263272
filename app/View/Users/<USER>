<?php
/**
 * @var AppView $this
 * @var array $shipearly_user
 * @var array|null $_shipearly_user
 * @var string $title_for_layout
 * @var string[] $country
 * @var string[] $selectedCountry
 * @var bool $map
 * @var bool $msrp
 * @var bool $shippingTaxOption
 */

if (isset($_shipearly_user)) {
	$shipearly_user = $_shipearly_user;
}

$this->Html->script('signup.js', ['inline' => false]);
?>
<style>
.email-address {
    position: absolute;
    top: 20px;
    right: 20px;
	font-size: 16px;
}
@media (max-width: 767px) {
    .email-address {
        position: static;
        margin-top: 10px;
        text-align: center;
        font-size: 14px;
    }
}
</style>
<div class="clearfix">
	<?php echo $this->element('content_header'); ?>
</div>
<?php if ($shipearly_user['User']['user_type'] === USER::TYPE_RETAILER) { ?>
	<div class="email-address">
		<?php
			$inventoryEmail = INVENTORY_EMAIL_ALIAS ?: INVENTORY_EMAIL;
			echo __('Inventory Email') . ': ' . substr_replace($inventoryEmail, '+' . substr($shipearly_user['User']['uuid'], 0, 8) . '-' . $shipearly_user['User']['id'], strpos($inventoryEmail, '@'), 0);
		?>
	</div>
<?php } ?>
<div class="col-md-10 offset-md-2 inventory">
	<div class="modal-body">
		<?php echo $this->Form->create('User', array(
			'id' => 'signup',
			'class' => 'form-signin',
			'type' => 'file',
			'inputDefaults' => array('label' => false, 'div' => false),
		)); ?>
			<?php
			if ($shipearly_user['User']['user_type'] == 'Manufacturer') {
				echo $this->element('Users/configuration/brand_ecommerce_form', ['shipearly_user' => $shipearly_user]);
			} else {
				echo $this->element('Users/configuration/retailer_inventory_form', ['shipearly_user' => $shipearly_user]);
			}
			?>
			<div class="reset-con">
				<button type="submit" id="resetsubmit" name="resetsubmit" class="btn btn-primary reset-link"><?= __('Update'); ?></button>
			</div>
		<?php echo $this->Form->end(); ?>
	</div>
</div>

<?php
if ($shipearly_user['User']['user_type'] == 'Manufacturer') {
	echo $this->element('Users/configuration/brand_ecommerce_script', ['shipearly_user' => $shipearly_user]);
} else {
	echo $this->element('Users/configuration/retailer_inventory_script', ['shipearly_user' => $shipearly_user]);
}
?>
