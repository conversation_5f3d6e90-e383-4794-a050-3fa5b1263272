<?php

use ShipEarlyApp\Lib\Utility\UserSiteType;

/**
 * @var AppView $this
 * @var string $title_for_submit
 * @var string[] $country
 * @var string[] $states
 * @var string[] $timezoneOptions
 */

/** @var bool $in_branch_action */
$in_branch_action = in_array($this->request->param('action'), ['editBranch', 'addBranch']);
/** @var string $user_type */
$user_type = $this->request->param('user_type');
?>
<?php $this->start('css'); ?>
<style type="text/css">
.pop-text,
.btn.dropdown-toggle.btn-default {
	width: 100%;
}
</style>
<?php $this->end(); ?>
<div class="clearfix">
	<?php echo $this->Layout->contentHeader(); ?>
</div>
<div class="card card-body col-12 col-sm-10 offset-sm-1">
<?php if ($user_type === User::TYPE_STAFF) { ?>
	<?php echo $this->Form->create('Retailer', [
		'id' => 'RetailerSearchForm',
		'type' => 'GET',
		'url' => ['controller' => 'users', 'action' => 'retailer_search'],
		'default' => false,
		'inputDefaults' => ['label' => false, 'div' => false],
	]); ?>
	<div class="col-md-10 offset-md-2 sign_up">
		<div class="form-group">
			<div class="controls">
				<?php echo $this->Form->input('zipcode', [
					'type' => 'text',
					'id' => 'RetailerZipcode',
					'placeholder' => __('Enter a Zip/Postal Code'),
					'class' => 'pop-text js-keyfilter-zip',
					'label' => __('Retailer Search'),
					'error' => ['attributes' => ['class' => 'help-block']],
				]); ?>
				<button type="submit" class="btn btn-primary"><?= __('Search'); ?></button>
				<span class="help-block"></span>
			</div>
		</div>
	</div>
	<?php echo $this->Form->end(); ?>
<?php } ?>
	<?php echo $this->Form->create('User', [
		'id' => 'signup',
		'class' => 'form-signin',
		'inputDefaults' => ['label' => false, 'div' => false],
	]); ?>
		<div class="col-md-10 offset-md-2 sign_up">
		<?php if ($user_type === User::TYPE_STAFF) { ?>
			<div class="form-group" >
				<div class="controls" id="retailer-container">
					<?php echo $this->Form->input('Branch', [
						'type' => 'select',
						'id' => 'retailer_id',
						'empty' => __('Select a Retailer'),
						'class' => 'select-input form-control',
						'label' => __('Retailer'),
						'error' => ['attributes' => ['class' => 'help-block']],
					]); ?>
					<span class="required_stars">*</span>
				</div>
				<div class="controls">
					<span class="help-block"></span>
				</div>
				<hr />
			</div>
		<?php } else { ?>
			<div class="form-group" >
				<div class="controls">
					<?php echo $this->Form->input('company_name', [
						'type' => 'text',
						'id' => 'company_name',
						'placeholder' => __('Company Name'),
						'class' => 'pop-text',
						'label' => __('Company Name'),
						'error' => ['attributes' => ['class' => 'help-block']],
					]); ?>
					<span class="required_stars">*</span>
					<span class="help-block"></span>
				</div>
			</div>
		<?php } ?>
			<div class="form-group" >
				<div class="controls">
					<?php echo $this->Form->input('first_name', [
						'type' => 'text',
						'id' => 'first_name',
						'placeholder' => __('First Name'),
						'class' => 'pop-text',
						'label' => __('First Name'),
						'error' => ['attributes' => ['class' => 'help-block']],
					]); ?>
					<span class="required_stars">*</span>
					<span class="help-block"></span>
				</div>
			</div>
			<div class="form-group" >
				<div class="controls">
					<?php echo $this->Form->input('last_name', [
						'type' => 'text',
						'id' => 'last_name',
						'placeholder' => __('Last Name'),
						'class' => 'pop-text',
						'label' => __('Last Name'),
						'error' => ['attributes' => ['class' => 'help-block']],
					]); ?>
					<span class="required_stars">*</span>
					<span class="help-block"></span>
				</div>
			</div>
			<div class="form-group" >
				<div class="controls">
					<?php echo $this->Form->input('email_address', [
						'type' => 'text',
						'id' => 'email_address',
						'placeholder' => __('Contact email address'),
						'class' => 'pop-text',
						'label' => __('Contact email address'),
						'error' => ['attributes' => ['class' => 'help-block']],
					]); ?>
					<span class="required_stars">*</span>
					<span class="help-block"></span>
				</div>
			</div>
		<?php if (!$in_branch_action) { ?>
			<div class="form-group">
				<div class="controls">
					<?php echo $this->Form->input('password', [
						'type' => 'password',
						'id' => 'password',
						'value' => '',
						'placeholder' => __('Password'),
						'class' => 'pop-text',
						'label' => __('Password'),
						'error' => ['attributes' => ['class' => 'help-block']],
					]); ?>
					<span class="required_stars">*</span>
					<span class="help-block"></span>
				</div>
			</div>
			<div class="form-group">
				<div class="controls">
					<?php echo $this->Form->input('confirm_password', [
						'type' => 'password',
						'id' => 'confirm_password',
						'value' => '',
						'placeholder' => __('Confirm Password'),
						'class' => 'pop-text',
						'label' => __('Confirm Password'),
						'error' => ['attributes' => ['class' => 'help-block']],
					]); ?>
					<span class="required_stars">*</span>
					<span class="help-block"></span>
				</div>
			</div>
		<?php } ?>
		</div>
	<?php if ($user_type === User::TYPE_STAFF) { ?>
		<?php echo $this->Layout->contentHeader('Personal Address'); ?>
	<?php } ?>
		<div class="col-md-10 offset-md-2 sign_up clear">
			<div class="form-group">
				<div class="controls">
					<?php echo $this->Form->input('address1', [
						'type' => 'text',
						'id' => 'address1',
						'placeholder' => __('Street Line 1'),
						'class' => 'pop-text',
						'label' => __('Street Line 1'),
						'error' => ['attributes' => ['class' => 'help-block']],
					]); ?>
					<span class="required_stars">*</span>
					<span class="help-block"></span>
				</div>
			</div>
			<div class="form-group">
				<div class="controls">
					<?php echo $this->Form->input('address2', [
						'type' => 'text',
						'id' => 'address2',
						'placeholder' => __('Street Line 2'),
						'class' => 'pop-text',
						'label' => __('Street Line 2'),
						'error' => ['attributes' => ['class' => 'help-block']],
					]); ?>
					<span class="help-block"></span>
				</div>
			</div>
			<div class="form-group">
				<div class="controls">
					<?php echo $this->Form->input('city', [
						'type' => 'text',
						'id' => 'city',
						'placeholder' => __('City'),
						'class' => 'pop-text',
						'label' => __('City'),
						'error' => ['attributes' => ['class' => 'help-block']],
					]); ?>
					<span class="required_stars">*</span>
					<span class="help-block"></span>
				</div>
			</div>
			<div class="form-group">
				<div class="controls">
					<?php echo $this->Form->input('country_id', [
						'type' => 'select',
						'id' => 'country_id',
						'empty' => __('Select Your Country'),
						'options' => $country,
						'class' => 'select-input form-control',
						'data-live-search' => 'true',
						'label' => __('Country'),
						'error' => ['attributes' => ['class' => 'help-block']],
					]); ?>
					<span class="required_stars">*</span>
					<span class="help-block"></span>
				</div>
			</div>
			<div class="form-group">
				<div class="controls">
					<?php echo $this->Form->input('state_id', [
						'type' => 'select',
						'id' => 'state_id',
						'empty' => __('Select Your State/Province'),
						'options' => $states,
						'class' => 'select-input form-control',
						'data-live-search' => 'true',
						'label' => __('State/Province'),
						'error' => ['attributes' => ['class' => 'help-block']],
					]); ?>
					<span class="required_stars">*</span>
					<span class="help-block"></span>
				</div>
			</div>
			<div class="form-group">
				<div class="controls">
					<?php echo $this->Form->input('zipcode', [
						'type' => 'text',
						'id' => 'zipcode',
						'placeholder' => __('Zip/Postal Code'),
						'class' => 'pop-text js-keyfilter-zip',
						'label' => __('Zip/Postal Code'),
						'error' => ['attributes' => ['class' => 'help-block']],
					]); ?>
					<span class="required_stars">*</span>
					<span class="help-block"></span>
				</div>
			</div>
			<div class="form-group">
				<div class="controls">
					<?php echo $this->Form->input('telephone', [
						'type' => 'text',
						'id' => 'telephone',
						'placeholder' => __('Phone Number'),
						'class' => 'pop-text',
						'label' => __('Phone Number'),
						'error' => ['attributes' => ['class' => 'help-block']],
						'autocomplete' => "tel"
					]); ?>
					<span class="required_stars">*</span>
					<span class="help-block"></span>
				</div>
			</div>
		<?php if ($user_type !== User::TYPE_STAFF) { ?>
			<div class="form-group">
				<div class="controls">
					<?php echo $this->Form->input('timezone', array(
						'type' => 'select',
						'id' => 'timezone',
						'empty' => __('Select a Time Zone'),
						'options' => $timezoneOptions,
						'class' => 'select-input form-control',
						'data-live-search' => 'true',
						'data-container' => '#content',
						'label' => __('Preferred Time Zone'),
						'error' => array('attributes' => array('class' => 'help-block')),
					)); ?>
					<span class="required_stars">*</span>
					<span class="help-block"></span>
				</div>
			</div>
		<?php } ?>
			<div class="form-group">
				<div class="controls">
					<?php echo $this->Form->input('language_code', array(
						'type' => 'select',
						'empty' => __('Select a Language'),
						'options' => $languageOptions,
						'class' => 'select-input form-control',
						'data-live-search' => 'true',
						'label' => __('Preferred Language'),
						'error' => array('attributes' => array('class' => 'help-block')),
					)); ?>
					<span class="help-block"></span>
				</div>
			</div>
		</div>
	<?php if ($user_type === User::TYPE_MANUFACTURER) { ?>
		<div class="col-md-10 offset-md-2 sign_up">
			<div class="form-group">
				<div class="controls">
					<?php echo $this->Form->input('site_type', [
						'type' => 'select',
						'id' => 'site_type',
						'empty' => __('eCommerce Platform'),
						'options' => UserSiteType::getAllOptions(),
						'class' => 'select-input form-control',
						'data-live-search' => 'true',
						'label' => __('eCommerce Platform'),
						'error' => ['attributes' => ['class' => 'help-block']],
					]); ?>
					<span class="required_stars">*</span>
					<span class="help-block"></span>
				</div>
			</div>
		</div>
	<?php } elseif ($user_type === User::TYPE_RETAILER || $in_branch_action) { ?>
		<div class="col-md-10 offset-md-2 sign_up">
			<div class="form-group">
				<div class="controls">
					<?php echo $this->Form->input('currency_code', [
						'type' => 'select',
						'id' => 'currency_code',
						'empty' => __('Select Your Primary Currency'),
						'options' => $this->Currency->supported,
						'class' => 'select-input form-control',
						'data-live-search' => 'true',
						'label' => __('Primary Currency'),
						'error' => ['attributes' => ['class' => 'help-block']],
					]); ?>
					<span class="required_stars">*</span>
					<div class="label label-info" style="display: inline-block; margin-left: 22%;"> * <?= __('Please choose the same primary currency that you are using in your inventory software'); ?></div>
					<span class="help-block" id="invalid-currency_type"></span>
				</div>
			</div>
			<div class="form-group">
				<div class="controls">
					<?php echo $this->Form->input('inventory_type', [
						'type' => 'select',
						'id' => 'inventory_type',
						'empty' => __('Select Your Inventory Software'),
						'options' => [
							'ascend_rms' => 'Ascend RMS',
							'citrus_lime'=> 'Citrus Lime',
							'clover' => 'Clover',
							'lightspeed_cloud' => 'Lightspeed Retail',
							'microsoft_rms' => 'Microsoft RMS',
							'vend_pos' => 'LightSpeed Retail (X-Series)',
							'quickbook_pos' => 'QuickBooks Desktop POS',
							'shopify_pos' => 'Shopify/Shopify POS',
							'square' => 'Square',
							'other' => 'Other',
							'none' => 'None',
						],
						'class' => 'select-input form-control',
						'data-live-search' => 'true',
						'label' => __('Inventory Software'),
						'error' => ['attributes' => ['class' => 'help-block']]
					]); ?>
					<span class="required_stars">*</span>
					<span class="help-block" id="invalid-inventory_type"></span>
				</div>
			</div>
			<div id="otherInventoryBlock" style="display: none;">
				<div class="form-group">
					<div class="controls">
						<?php echo $this->Form->input('otherInventory', [
							'type' => 'text',
							'id' => 'otherInventory',
							'placeholder' => __('Other Inventory Name'),
							'class' => 'pop-text',
							'label' => __('Other Inventory Name'),
							'error' => ['attributes' => ['class' => 'help-block']],
						]); ?>
						<span class="required_stars">*</span>
						<span class="help-block" id="invalid-otherInventory"></span>
					</div>
				</div>
			</div>
		</div>
	<?php } ?>
		<div class="col-md-10 offset-md-2">
			<div class="btn-align">
			<?php if (!$in_branch_action) { ?>
				<div class="form-group">
					<div class="controls" style="margin-left: 26%;">
						<?php echo $this->Form->input('termsConditions', [
							'type' => 'checkbox',
							'id' => 'termsConditions',
							'name' => 'termsConditions',
							'label' => [
								'class' => 'agree',
								'text' => __(
									'I agree to %s and %s',
									'<a href="javascript:void(0);" onclick="termsModel(\'#terms\'); return false;" class="link">' . __('the Terms of Use') . '</a>',
									'<a href="javascript:void(0);" onclick="termsModel(\'#policy\'); return false;" class="link">' . __('the Privacy policy') . '</a>'
								) . ' ' . '<span class="required_stars">*</span>',
							],
							'between' => ' ',
							'hiddenField' => false,
						]); ?>
						<span id="invalid-termsConditions" class="help-block"></span>
					</div>
				</div>
			<?php } ?>
				<div class="reset-con">
					<button type="button" id="resetsubmit" name="resetsubmit" class="btn btn-primary reset-link"><?php echo $title_for_submit; ?></button>
				</div>
			</div>
		</div>
	<?php echo $this->Form->end(); ?>
</div>
<?php echo $this->Html->script('signup.js', ['inline' => false]); ?>
<?php $this->start('script'); ?>
<script src="//maps.googleapis.com/maps/api/js?key=<?php echo GOOGLE_GEOCODING_API_KEY; ?>&libraries=places&v=3&callback=initAutocomplete" type="text/javascript" async defer></script>
<script type="text/javascript">
    window.initAutocomplete = function() {
        $('#address1').focus(function() {
            $(this).initAutocomplete('#address1', {
                address2InputSelector: '#address2',
                cityInputSelector: '#city',
                zipcodeInputSelector: '#zipcode',
                countrySelectSelector: '#country_id',
                stateSelectSelector: '#state_id'
            });
        });
    };
</script>
<script type="text/javascript">
$(function() {
	$('#country_id').bindCountryChangeEvents({
		getStatesUrl: "<?= Router::url(['controller' => 'users', 'action' => 'getstates']) ?>",
		getTimezonesUrl: "<?= Router::url(['controller' => 'users', 'action' => 'get_timezone_options']) ?>",
		statesSelector: '#state_id',
		zipcodeSelector: '#zipcode',
		timezonesSelector: '#timezone',
		telephoneSelector: '#telephone'
	});
});

function termsModel(type) {
	var user_type = "<?php echo ($user_type ?: User::TYPE_RETAILER); ?>";

	if (type === '#terms') {
		if (user_type === 'Manufacturer') {
			type = '#mterms';
		} else {
			type = '#rterms';
		}
	}

	var url;
	var title;
	if (type === '#policy') {
		url = '<?php echo BASE_PATH; ?>privacy-policy?popup';
		title = 'Privacy Policy';
	} else if (type === '#mterms') {
		url = '<?php echo BASE_PATH; ?>brand-terms-of-use-agreement?popup';
		title = 'Manufacturer Terms';
	} else if (type === '#rterms') {
		url = '<?php echo BASE_PATH; ?>retailer-terms-of-use-agreement?popup';
		title = 'Retailer Terms';
	}

	$.get(url, function(data) {
		bootbox.dialog({
			title: title,
			message: data,
			backdrop: true,
			className: 'modal--terms',
			buttons: {}
		});
	});
}
</script>
<script type="text/javascript">
jQuery(function($) {
	$('#RetailerSearchForm').submit(function() {
		var $form = $(this);
		if ($form.valid()) {
			$('#retailer_id').load($form.attr('action'), $form.serialize(), function() {
				$('#retailer_id').selectpicker('refresh');
				$('#retailer-container').show();
			});
		}
	});

	$('#inventory_type').on('change', function() {
		$('#otherInventoryBlock').toggle(this.value === 'other');
	}).trigger('change');
});
</script>
<?php $this->end(); ?>
