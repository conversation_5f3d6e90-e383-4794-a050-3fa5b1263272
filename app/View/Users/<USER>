<?php
/**
 * @var AppView $this
 * @var float $defaultInStoreRadius
 * @var float $defaultShipFromStoreRadius
 */
?>
<?php echo $this->Form->create('User', array(
	'class' => 'form-horizontal',
	'inputDefaults' => array(
		'min' => 0,
		'class' => 'span8',
		'style' => 'font-size: 12px;',
		'div' => 'controls',
		'label' => array('style' => 'width: 100%;'),
	),
)); ?>
	<?php echo $this->Form->input('in-store_radius', array(
		'placeholder' => "Default: {$defaultInStoreRadius}",
		'label' => array('text' => 'Store Pickup Radius', 'style' => 'width: 100%;'),
	)); ?>
	<?php echo $this->Form->input('ship_from_store_radius', array(
		'placeholder' => "Default: {$defaultShipFromStoreRadius}",
		'label' => array('text' => 'Ship From Store Radius', 'style' => 'width: 100%;'),
	)); ?>
<?php echo $this->Form->end(['style' => 'display: none;', 'div' => false]); ?>
