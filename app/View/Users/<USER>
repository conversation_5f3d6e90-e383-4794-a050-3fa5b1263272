<script src="https://checkout.stripe.com/v2/checkout.js"></script>
<script>
$(document).ready(function(){
    var key = "<?php echo STRIPE_PUBLISHABLE_KEY; ?>";
    var currency_code = "<?php echo $user['User']['currency_code']; ?>";
    var emailAdd = "<?php echo $user['User']['email_address']; ?>";
    var token = function(res){
            var $input = $('<input type=hidden name=stripeToken />').val(res.id);
            $.ajax({
                type: "POST",
                url: "<?php echo BASE_PATH; ?>stripepaymentdetails/<?php echo $user['User']['id']; ?>",
                data: res   
            }).done(function(data) {
                if(data == '1') {
                    window.location = "<?php echo BASE_PATH; ?>/activated/login";
                }
            });
    };

    StripeCheckout.open({
        key:             key,
        email:           emailAdd,
        currency:        currency_code,
        name:            'Card Information',
        panelLabel:      'Save',
        allowRememberMe: false,
        token:           token,
        closed: function() {
            window.location = "<?php echo BASE_PATH; ?>";
        }
    });
});
</script>
