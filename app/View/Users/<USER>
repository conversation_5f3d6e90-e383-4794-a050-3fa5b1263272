<?php
/**
 * @var AppView $this
 */
?>
<?php echo $this->Form->create('User', array(
	'id' => 'StoreAssociatePinForm',
	'class' => 'bootbox-form',
	'inputDefaults' => array('label' => false, 'div' => false),
)); ?>
	<?php echo $this->Form->input('store_associate_pin', array(
		'type' => 'text',
		'id' => 'StoreAssociatePin',
		'class' => 'bootbox-input bootbox-input-text form-control',
		'placeholder' => '00000',
		'required' => true,
		'pattern' => '^[0-9]{5}$',
		'oninvalid' => 'this.setCustomValidity("Pin must be a 5 digit number");',
		'oninput' => 'this.setCustomValidity("");',
		'autocomplete' => 'off',
	)); ?>
<?php echo $this->Form->end(['style' => 'display: none;', 'div' => false]); ?>
