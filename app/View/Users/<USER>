<?php
/**
 * @var AppView $this
 * @var string $title_for_layout
 * @var array $shipearly_user
 */
?>
<div class="form_title">
	<h1><?php echo $title_for_layout; ?></h1>
	<div class="fR"> <a href="javascript:;" onClick="history.back(-1);" class="btn btn-mini btn-primary fR"><i class="fas fa-chevron-left"></i>Back</a> </div>
</div>
<?php echo $this->Html->script( array( 'inventory_config' ) ); ?>
<div class="form_content">
	<?php echo $this->Form->create('User',array('id'=>'pagefrm', 'class'=>'form-horizontal shrink_form', 'type'=>'file', 'inputDefaults'=>array("label"=>false,"div"=>false)));?>

	<div class="control-group">
		<?php echo $this->Form->label('currency_code',"Select your currency type", ['class'=>'control-label'] );?>
       	<div class="controls">
			<?php echo $this->Form->input('currency_code', array('id'=>'currency_code','class'=>'select-input', 'data-live-search' => "true", 'type'=>'select', 'options' => $this->Currency->supported, 'empty'=>'Select your currency type', 'style' => 'margin-bottom: 0;', 'value'=> $shipearly_user['User']['currency_code'] , 'error' => array('attributes' => array('class' => 'help-block')))); ?>
	 		<span class="required_stars">*</span>
		</div>
		<div class='label label-info' style="display: inline-block; margin-left: 22%;"> * Please choose the same currency type that you are using in your inventory software
		</div>
	</div>

	<!-- Inventory software section -->
	<div class="control-group">
		<?php echo $this->Form->label('inventory_type',"Select your inventory software", ['class'=>'control-label']  );?>
		<div class="controls">
			<?php
				$inventoryType = $shipearly_user['User']['inventory_type'];
				if ($inventoryType == 'other' && $shipearly_user['User']['otherInventory'] == 'None') {
					$inventoryType = 'none';
				}
				else if(empty($inventoryType) && empty($shipearly_user['User']['otherInventory'])) {
					$inventoryType = 'none';
				}
				echo $this->Form->input('inventory_type', array(
					'id' => 'inventory_type',
					'class' => 'select-input',
					'data-live-search' => "true",
					'type' => 'select',
					'options' => array(
						'ascend_rms' => 'Ascend RMS',
						'citrus_lime'=> 'Citrus Lime',
						'clover' => 'Clover',
						'lightspeed_cloud' => 'Lightspeed Retail',
						'microsoft_rms' => 'Microsoft RMS',
						'vend_pos' => 'LightSpeed Retail (X-Series)',
						'quickbook_pos' => 'QuickBooks Desktop POS',
						'shopify_pos' => 'Shopify/Shopify POS',
						'square' => 'Square',
						'other' => 'Other',
						'none' => 'None'
					),
					'empty' => __('Select your inventory type'),
					'value' => $inventoryType,
					'error' => array(
						'attributes' => array('class' => 'help-block')
					)
				)); ?>
				<?php if($shipearly_user['User']['inventory_type'] == 'quickbook_pos') { ?>
						<a href="<?php echo BASE_PATH.'downloadFile?appkey='.$shipearly_user['User']['inventory_apiuser']; ?>" title="Download Qwc File"><span><i id="downloadIcon" class="fas fa-circle-arrow-down fa-lg" style="color: #000;"></i></span></a>
	            	<?php } ?>
	            	<span class="required_stars">*</span>
	            	<span class='help-block' id="invalid-inventory_type"></span>
		</div>
	</div>
	<!-- All Inventory -->
	<div id="inventory" style="display: none;">
					<div id="shopify_pos" class="AllInventory">
						<div class="control-group">
							<?php echo $this->Form->label('shopify_url',"Shopify Domain", ['class'=>'control-label'] );?>
							<div class="controls">
			                	<?php echo $this->Form->input('shopify_url', array('id'=>'shopify_url', 'type'=>'text', 'class'=>'pop-text', 'placeholder'=>'Shopify Domain', 'value'=>$shipearly_user['User']['shop_url'], 'error' => array('attributes' => array('class' => 'help-block')))); ?>
			                	<span class="required_stars">*</span>
			                	<span class='help-block' id="invalid-shopify_url"></span>
			                </div>
						</div>
					</div>

					<div id="lightspeed_connect" class="AllInventory">
						<div class="form-group">
							<div class="controls  text-center">
								<div class='reset-con btn btn-primary'>
									<a class='btn btn-primary' href="<?php echo BASE_PATH. 'lightspeed/connect'; ?>">Lightspeed Connect</a>
								</div>
			                </div>
						</div>
					</div>

					<div id="square_connect" class="AllInventory">
						<div class="form-group">
							<div class="controls  text-center">
								<div class='reset-con btn btn-primary'>
									<a class='btn btn-primary' href="<?php echo BASE_PATH. 'square/connect'; ?>">Square Connect</a>
								</div>
							</div>
						</div>
					</div>

					<div id="vend_connect" class="AllInventory">
						<div class="control-group">
							<?php echo $this->Form->label('inventory_password',"LightSpeed Retail (X-Series) Store", ['class'=>'control-label'] );?>
							<div class="controls">
                                <?php echo $this->Form->input('inventory_password', array('id'=>'vend_domain', 'type'=>'text', 'class'=>'pop-text', 'placeholder'=>'LightSpeed Retail (X-Series) Store', 'value'=>$shipearly_user['User']['inventory_password'], 'error' => array('attributes' => array('class' => 'help-block')))); ?>

			                	<span class="required_stars">*</span>
			                	<span class='help-block' id="invalid-inventory_password"></span>
			                </div>
							<div class="controls  text-center">
								<div class='reset-con btn btn-primary'>
									<a id= 'vend_access' class='btn btn-primary' href="#">LightSpeed Retail (X-Series) Connect</a>
								</div>
			                </div>
						</div>
					</div>

						<div class="control-group">
							<!--div class="controls"-->
							<?php echo $this->Form->label('inventory_apiuser',"API Key", ['class'=>'control-label col-sm-2'] );?>
							<?php echo $this->Form->input('inventory_apiuser', array('id'=>'inventory_apiuser', 'type'=>'text', 'class'=>' form-control', 'placeholder'=>'API Key', 'value'=>$shipearly_user['User']['inventory_apiuser'], 'error' => array('attributes' => array('class' => 'help-block')))); ?>
							<span class="required_stars">*</span>
							<div class='label label-info' style="display: inline-block; margin-left: 22%;"> * Please use different api keys for different stores to avoid api throttle limit</div>
							<span class='help-block' id="invalid-inventory_apiuser"></span>
			                <!--/div-->
						</div>
						<div class="control-group">
							<!--div class="controls"-->
							<?php echo $this->Form->label('inventory_password',"API Password", ['class'=>'control-label col-sm-2'] );?>
							<?php echo $this->Form->input('inventory_password', array('id'=>'inventory_password', 'type'=>'text', 'class'=>'form-control account_id', 'placeholder'=>'Lightspeed Retail Account ID', 'value'=>$shipearly_user['User']['inventory_password'], 'error' => array('attributes' => array('class' => 'help-block')))); ?>
							<span class="required_stars">*</span>
		                	<span class='help-block' id="invalid-inventory_password"></span>
			                <!--/div-->
						</div>
					
					<div id="inventory_information" class="AllInventory">
						<div class="form-group">
							<div class="controls">
								<?php echo $this->Form->label('Inventory_Store_ID',"Inventory Store ID", ['class'=>'control-label'] );?>
								<?php echo $this->Form->input('Inventory_Store_ID', array('id'=>'Inventory_Store_ID', 'type'=>'select', 'data-live-search' => "true", 'class'=>'select-input', 'placeholder'=>'Inventory Store ID', "readonly" => "readonly",'options' => array(), 'empty'=>'Select Store', 'value'=>$shipearly_user['User']['Inventory_Store_ID'], 'error' => array('attributes' => array('class' => 'help-block')))); ?>
								<span class="required_stars">*</span>
								<span class='help-block' id="invalid-Inventory_Store_ID"></span>
			                </div>
						</div>
						<div class="form-group">
							<div class="controls">
								<?php echo $this->Form->label('Inventory_Emp_ID',"Inventory Employee ID", ['class'=>'control-label'] );?>
			                	<?php echo $this->Form->input('Inventory_Emp_ID', array('id'=>'Inventory_Emp_ID', 'type'=>'select', 'class'=>'select-input', 'data-live-search' => "true", 'placeholder'=>'Inventory Employee ID', "readonly" => "readonly", 'options' => array(''), 'empty'=>'Select Employee', 'value'=>$shipearly_user['User']['Inventory_Emp_ID'], 'error' => array('attributes' => array('class' => 'help-block')))); ?>
		                		<span class="required_stars">*</span>
								<span class='help-block' id="invalid-Inventory_Emp_ID"></span>
			                </div>
						</div>
						<div class="form-group">
							<div class="controls">
								<?php echo $this->Form->label('Inventory_Reg_ID',"Inventory Register ID", ['class'=>'control-label'] );?>
			                	<?php echo $this->Form->input('Inventory_Reg_ID', array('id'=>'Inventory_Reg_ID', 'type'=>'select', 'class'=>'select-input', 'data-live-search' => "true", 'placeholder'=>'Inventory Register ID', "readonly" => "readonly",'options' => array(''), 'empty'=>'Select Register', 'value'=>$shipearly_user['User']['Inventory_Reg_ID'], 'error' => array('attributes' => array('class' => 'help-block')))); ?>
		                		<span class="required_stars">*</span>
								<span class='help-block' id="invalid-Inventory_Reg_ID"></span>
			                </div>
						</div>
					</div>
					<div id="square_information" class="AllInventory">
						<div class="control-group">
						<div class="controls">
							<?php echo $this->Form->input('Square_Store_ID', array(
								'type' => 'select',
								'id' => 'Square_Store_ID',
								'empty' => 'Select Location',
								'options' => array(),
								'value' => $shipearly_user['User']['Inventory_Store_ID'],
								'class' => 'select-input notranslate',
								'data-live-search' => 'true',
								'readonly' => true,
								'label' => 'Inventory Store ID',
								'between' => ' ',
								'error' => array('attributes' => array('class' => 'help-block')),
							)); ?>
							<span class="required_stars">*</span>
							<span class='help-block' id="invalid-Square_Store_ID"></span>
						</div>
						</div>
					</div>
				</div>

	<!-- Other Inventrory -->
	<div id="otherInventoryBlock" style="display: block;">
		<div class="control-group">
		<?php echo $this->Form->label('otherInventory',"Other Inventory Name", ['class'=>'control-label'] );?>
			<div class="controls">
            	<?php echo $this->Form->input('otherInventory', array('id'=>'otherInventory', 'type'=>'text', 'class'=>'pop-text', 'placeholder'=>'Other Inventory Name', 'value'=>$shipearly_user['User']['otherInventory'], 'error' => array('attributes' => array('class' => 'help-block')))); ?>
        		<span class="required_stars">*</span>
        	<span class='help-block' id="invalid-otherInventory"></span>
            </div>
		</div>
	</div>

	<!-- Tax Rate -->
	<div id="taxBlock">
		<div class="control-group">
			<?php echo $this->Form->label('defaultTax',__('Sales Tax Rate'), ['class'=>'control-label'] );?>
			<div class="controls">
	        	<?php echo $this->Form->input('defaultTax', array('id'=>'defaultTax', 'type'=>'text', 'class'=>'pop-text', 'value'=>$shipearly_user['User']['defaultTax'], 'placeholder'=>__('Sales Tax Rate'), 'error' => array('attributes' => array('class' => 'help-block')))); ?>
			<span class="required_stars">*</span>
			<div class='label label-info' style="display: inline-block; margin-left: 22%;"> * Example: 7.5 or 8.5 or 9.5</div>
	    	<span class='help-block' id="invalid-defaultTax"></span>
	        </div>
		</div>
	</div>

	<div class='reset-con'>
		<button type="submit" id="updatesubmit" name="updatesubmit" class="btn btn-primary reset-link">Update</button>
		<?php echo $this->Form->input('user_type', array('id'=>'user_type', 'type'=>'hidden', 'value' => $shipearly_user['User']['user_type'])); ?>
	</div>

	<?php echo $this->Form->end();?>
</div>
