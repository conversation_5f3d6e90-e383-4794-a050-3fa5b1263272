<?php
	if(!$this->request->is('ajax')) {
		echo $this->Html->css('/star-rating/jquery.rating');
		echo $this->Html->script(array('/star-rating/jquery.MetaData'));
	}
		echo $this->Html->script(array('/star-rating/jquery.rating.pack'));
?>
<?php echo $this->Html->script( array( 'review' ) ); ?>
<?php echo $this->Form->create('Review', array('id'=>'ReviewForm','class'=>'form-signin','inputDefaults'=>array("label"=>false,"div"=>false))); ?>
	<div class="form-group">
		<div class="controls">
	 		<?php echo $this->Form->label('title',"Title" );?>
	 		<?php echo $this->Form->input('title', array('id' => 'title', 'class'=>'pop-text', 'type' => 'text', 'placeholder'=>'title')); ?>
	        <span class="required_stars">*</span>
	 		<span class='help-block'></span>
		</div>
	</div>
	<div class="form-group">
		<div class="controls">
	 		<?php echo $this->Form->label('comment',"Comments" );?>
	 		<?php echo $this->Form->input('comment', array('id' => 'review_com', 'class'=>'pop-text', 'type' => 'textarea', 'placeholder'=>'Your comment')); ?>
	        <span class="required_stars">*</span>
	 		<span class='help-block'></span>
		</div>
	</div>
	<div class="form-group">
		<div class="controls">
	 		<?php echo $this->Form->label('rating',"Rating" );?>
	 		<?php echo $this->StarRating->stars(0, 5, 'rating'); ?>
	 		<span class='help-block'></span>
		</div>
	</div>	
 	<div class="modal-footer">
 		<button type='submit' id='addreview' class='btn btn-primary reset-link'>Save</button>
 	</div>
</form>
<style type="text/css">
	.controls label {
		width: 100%;
	}
</style>
