div.retailer-details {
    width: 100%;
    min-width: 30rem;
}

.retailer-details__label {
    color: var(--bs-gray);
    font-weight: 500;
}

.retailer-details__info {
    font-size: 1.25rem;
    font-weight: 300;
    margin-left: .5rem;
}

.retailer-details__logo {
    display: block;
    margin: 0 auto;
}

.retailer-details__name {
    font-size: 1.75rem;
    font-weight: 300;
}

.retailer-details__description {
    text-align: left;
}

.retailer-details__locations {
    text-align: center;
}


/********** Override .list_option styling **********/

.stripe-status {
    flex-direction: column;
}

.stripe-status .stripe-status__item {
    width: 100%;
}

.stripe-status__item.list_option>div {
    background: unset;
    box-shadow: unset;
    border: unset;
}

.stripe-status__item.list_option>div {
    background: unset;
    box-shadow: unset;
    border: unset;
    margin: 0;
    height: unset;
}

.stripe-status__item .fa-times-circle,
.stripe-status__item .fa-check-circle {
    line-height: unset;
    font-size: 2rem;
    margin: unset;
}

.stripe-status svg {
    vertical-align: unset;
}

/********** Override style.css **********/

.retailer-details .cont-phn {
    background: unset;
    color: var(--bs-primary);
}

.retailer-details .cont-mail {
    background: unset;
    color: var(--bs-primary);
}

/********** Override element style **********/
.retailer-details__store-hours span {
    color: var(--bs-primary) !important;
}


@media (max-width: 767px) {
    div.retailer-details {
        min-width: 0;
    }
}
