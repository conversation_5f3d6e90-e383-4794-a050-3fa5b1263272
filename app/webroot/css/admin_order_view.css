.tableImag {
	max-width: 150px !important;
	max-height: 100px !important;
	float: left;
}
#productTable td {
    padding: 0 10px;
}
tbody td,tfoot td, th {
    border: 1px solid;
}
.no-of-retails > span {
    line-height: 100px;
    padding: 20px;
}
thead td {
    padding: 5px !important;
}
.bootbox-body .controls {
	margin: 0;
}
.bootbox-body .form-group:not(:first-child) {
	padding-top: 8px;
}
.bootbox-body input[type="text"] {
	width: 98%;
	font-size: 14px;
}
.bootbox-body input[type="checkbox"] {
	margin-right: 5px;
}
.bootbox-body label {
	width: auto;
	font-size: 14px;
}
.refundTitle {
	padding-top: 15px;
}
.refundTable {
	width: 100%;
}
.refundTable th,
.refundTable td {
	padding: 5px 8px;
}
.refundTable th {
	background: grey;
	color: white;
	border-color: black;
}
.refundTable tr:nth-child(odd) td {
	background: #E6E6E6;
}