/*#region Dark Theme */
.select2-container--bootstrap-5 .select2-selection.select2-dark {
    color: var(--bs-white);
    background-color: var(--bs-gray-900);
    border: 1px solid var(--bs-gray-800);
}

.select2-container--bootstrap-5 .select2-dropdown.select2-dark {
    color: var(--bs-white);
    background-color: var(--bs-gray-900);
    border-color: var(--bs-gray-800);
}

.select2-container--bootstrap-5 .select2-dropdown.select2-dark .select2-search .select2-search__field {
    background-color: var(--bs-gray-900);
    border: 1px solid var(--bs-gray-800);
}

.select2-container--bootstrap-5 .select2-dropdown.select2-dark .select2-results__options .select2-results__option.select2-results__option--highlighted {
    color: var(--bs-white);
    background-color: var(--bs-gray-700);
}

.select2-container--bootstrap-5 .select2-dropdown.select2-dark .select2-results__options .select2-results__option.select2-results__option--selected,
.select2-container--bootstrap-5 .select2-dropdown .select2-results__options .select2-results__option[aria-selected="true"]:not(.select2-results__option--highlighted) {
    color: var(--bs-white);
    background-color: var(--bs-gray-700);
}

.select2-container--bootstrap-5.select2-container--disabled .select2-selection.select2-dark,
.select2-container--bootstrap-5.select2-container--disabled.select2-container--focus .select2-selection {
    color: #6c757d;
    background-color: #e9ecef;
    border-color: #ced4da;
}

/*#endregion Dark Theme */

.select2-container--bootstrap-5 .select2-dropdown .select2-results__options:not(.select2-results__options--nested) {
    max-height: 35rem;
}

/* Hide placeholder option on profile dropdown */
.select2-container--bootstrap-5 .select2-dropdown.select2-hide-empty .select2-results__options>li:first-of-type {
    display: none;
}

/* styling for legacy labels to be inline */
label[for="brand"]~.select2-container--bootstrap-5:first-of-type,
label[for="destination"]~.select2-container--bootstrap-5:first-of-type,
label[for="noRecords"]~.select2-container--bootstrap-5:first-of-type,
label[for="noRecordsDashboard"]~.select2-container--bootstrap-5:first-of-type,
label[for="warehouse"]~.select2-container--bootstrap-5:first-of-type,
label[for="SortBy"]~.select2-container--bootstrap-5:first-of-type {
    display: inline-block;
    margin-left: 0.5rem;
    width: auto !important;
}

li.select2-results__option--selected div.fas {
      visibility:visible !important;
}
