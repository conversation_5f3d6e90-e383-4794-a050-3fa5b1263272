/* Circular Content Carousel Style */
.ca-container{
	position:relative;
	margin:25px auto 20px auto;
	width:95%;
	height:375px;
	clear:both;
}
.ca-wrapper{
	width:100%;
	height:100%;
	position:relative;
}
.ca-item{
	position:relative;
	float:left;
	width:265px;
	height:100%;
	text-align:center;
}

.ca-nav span{
	width:43px;
	height:38px;
	background: url(../images/icons/ShipEarly_next.png) no-repeat;
	position:absolute;
	top:50%;
	margin-top:-19px;
	left:-40px;
	text-indent:-9000px;
	opacity:0.7;
	cursor:pointer;
	z-index:100;
	border-radius: 3px;
}
.ca-more{
	position: absolute;
	bottom: 10px;
	right:0px;
	padding:4px 15px;
	font-weight:bold;
	background: #ccbda2;
	text-align:center;
	color: white;
	font-family: "Georgia","Times New Roman",serif;
	font-style:italic;
	text-shadow:1px 1px 1px #897c63;
}
.ca-close{
	position:absolute;
	top:10px;
	right:10px;
	background:#fff url(../images/cross.png) no-repeat center center;
	width:27px;
	height:27px;
	text-indent:-9000px;
	outline:none;
	-moz-box-shadow:1px 1px 2px rgba(0,0,0,0.2);
	-webkit-box-shadow:1px 1px 2px rgba(0,0,0,0.2);
	box-shadow:1px 1px 2px rgba(0,0,0,0.2);
	opacity:0.7;
}
.ca-close:hover{
	opacity:1.0;
}
.ca-item-main{
	padding:20px;
	position:absolute;
	top:5px;
	left:5px;
	right:5px;
	bottom:5px;
	background:#fff;
	overflow:hidden;
	-moz-box-shadow:1px 1px 2px rgba(0,0,0,0.2);
	-webkit-box-shadow:1px 1px 2px rgba(0,0,0,0.2);
	box-shadow:1px 1px 2px rgba(0,0,0,0.2);
}
.ca-icon{
	width:233px;
	height:189px;
	position:relative;
	margin:0 auto;
	background:transparent url(../images/animal1.png) no-repeat center center;
}
.ca-item-2 .ca-icon{
	background-image:url(../images/animal2.png);
}
.ca-item-3 .ca-icon{
	background-image:url(../images/animal3.png);
}
.ca-item-4 .ca-icon{
	background-image:url(../images/animal4.png);
}
.ca-item-5 .ca-icon{
	background-image:url(../images/animal5.png);
}
.ca-item-6 .ca-icon{
	background-image:url(../images/animal6.png);
}
.ca-item-7 .ca-icon{
	background-image:url(../images/animal7.png);
}
.ca-item-8 .ca-icon{
	background-image:url(../images/animal8.png);
}
.ca-item h3{
	font-family: 'Coustard', sans-serif;
	text-transform:uppercase;
	font-size:30px;
	color:#000;
	margin-bottom:20px;
	height:85px;
	text-align:center;
	text-shadow: 0px 1px 1px #e4ebe9;
}
.ca-item h4{
	font-family: "Georgia","Times New Roman",serif;
	font-style:italic;
	font-size:12px;
	text-align:left;
	border-left:10px solid #b0ccc6;
	padding-left:10px;
	line-height:24px;
	margin:10px;
	position:relative;
}
.ca-item h4 span{
	text-indent:40px;
	display:block;
}
.ca-item h4  span.ca-quote{
	color:#f4eee3;
	font-size:100px;
	position:absolute;
	top:20px;
	left:0px;
	text-indent:0px;
}
.ca-content-wrapper{
	background:#b0ccc6;
	position:absolute;
	width:0px; /* expands to width of the wrapper minus 1 element */
	height:440px;
	top:5px;
	text-align:left;
	z-index:10000;
	overflow:hidden;
}
.ca-content{
	width:660px;
	overflow:hidden;
}
.ca-content-text{
	font-size: 14px;
	font-style: italic;
	font-family: "Georgia","Times New Roman",serif;
	margin:10px 20px;
	padding:10px 20px;
	line-height:24px;
}
.ca-content-text p{
	padding-bottom:5px;
}
.ca-content h6{
	margin:25px 20px 0px 35px;
	font-size:32px;
	padding-bottom:5px;
	color:#000;
	font-family: 'Coustard', sans-serif;
	color:#60817a;
	border-bottom:2px solid #99bcb4;
	text-shadow: 1px 1px 1px #99BCB4;
}
.ca-content ul{
	margin:20px 35px;
	height:30px;
}
.ca-content ul li{
	float:left;
	margin:0px 2px;
}
.ca-content ul li a{
	color:#fff;
	background:#000;
	padding:3px 6px;
	font-size:14px;
	font-family: "Georgia","Times New Roman",serif;
	font-style:italic;
}
.ca-content ul li a:hover{
	background:#fff;
	color:#000;
	text-shadow:none;
}
.ca-nav span{
	width:25px;
	height:38px;
	background:transparent url(../images/arrows.png) no-repeat top left;
	position:absolute;
	top:50%;
	margin-top:-19px;
	left:-40px;
	text-indent:-9000px;
	opacity:0.7;
	cursor:pointer;
	z-index:100;
}
.ca-nav span.ca-nav-next{
	background-position:top right;
	left:auto;
	right:-40px;
}
.ca-nav span:hover{
	opacity:1.0;
}


.ca-nav span.ca-nav-prev{
    left: auto;
    right: 0;
    top: 15px;
	transform:rotate(180deg);
	-ms-transform:rotate(180deg); /* IE 9 */
	-webkit-transform:rotate(180deg); 
	top:35%;
}
.ca-nav span.ca-nav-next {
    left: auto;
    right: -12px;
}
.ca-nav span:hover{
	opacity:1.0;
}
.breadcrumb > li + li:before{
	content:" " !important;
}