
:root {
    --bs-body-bg: #ebebeb;
    --bs-body-bg-rgb: 235, 235, 235;
    font-size: 87.5%;
}

* {
    margin: 0;
    padding: 0;
}

html,
body {
    height: 100% !important;
}

body {
    margin: 0 auto !important;
    outline: none !important;
    overflow: auto;
    padding: 0 !important;
    width: 100%;
    -webkit-font-smoothing: antialiased !important;
    -webkit-overflow-scrolling: touch;
    -webkit-text-size-adjust: 100%;
}

ul, ol {
    list-style-type: none;
}

a {
    text-decoration: none;
}

a img {
    border: 0;
}

table {
    border-collapse: collapse;
}

a:hover, a:focus, a:active {
    outline: none;
    text-decoration: none;
}

p {
    text-align: justify;
}

input:focus, textarea:focus, select:focus, select {
    outline: none;
}

h1, h2, h3, h4, h5, h6 {
    margin-bottom: 0;
    margin-top: 0;
}

/* What a silly thing to do... */
h3, .h3 {
    font-size: 16px !important;
}

button + button {
    margin-left: 5%;
}

h4 {
    color: #8a8a8a;
}

.category_tbl td label .menu-option {
    line-height: normal;
    margin: 15px 0 0;
    vertical-align: bottom;
}

.category_tbl td label {
    font-weight: normal;
}

ul, ol {
    margin: 0;
}

.order-det {
    color: #00A0FC;
}

.report-chart {
    width: 90%;
    height: auto;
    margin: 30px auto  10px;
}

.controls .btn-group.bootstrap-select.product-status {
    width: 100% !important;
}

/* Structure */
#wrapper {
    min-height: 100%;
    width: 100vw;
    overflow: hidden;
    display: flex;
    height: 100vh;
    flex-direction: row;
    align-content: space-between;
}

div.left-content {
    height: 100vh;
    position: sticky;
    top: 0;
    align-self: flex-start;
    z-index: 1000;
}

div.right-content {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    overflow: hidden auto;
    width: 100%;
}

div.bottom-content {
    display: flex;
    flex-direction: column;
    justify-content: space-between; 
}

div.content {
    position: relative;
    min-height: calc(100vh - 105px - 80px);
}

#header, .content, #footer {
    float: left;
}

#header {
    width: 100%;
    background-color: #292929;
    z-index: 2147483647;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80px;
    position: sticky;
    top: 0;
}


.content {
    margin-bottom: 25px;
}

#header .expand {
    display: none;
    color: #565656;
}
#content {
    width: 100%;
}


.product-con {
    float: left;
    width: 79%;
    margin-top: 30px;
}

.list {
    margin-top: 20px;
}

/* Structure */
/* Header */
.top-menu {
    float: left;
    flex: auto;
}

.logo {
    text-align: left;
    max-height: 100%;
    width: auto;
    max-width: 196px;
}
.logo img {
    max-height: 100%;
    max-width: 100%;
    width: auto;
    margin: auto;
}




.top-menu li {
    display: block;
    float: left;
    height: 80px;
}

.top-menu li a {
    display: block;
    height: 80px;
    padding-top: 20px;
    text-align: center;
    font-weight: bold;
}

.top-menu li a .top-menu-icon {
    height: 24px;
}

.top-menu li a .top-menu-icon img {
    display: inline;
    max-height: 100%;
}

.top-menu li a .top-menu-icon .fas {
    color: #fff;
    font-size: 24px;
}

.bootstrap-select.btn-group .dropdown-menu li {
    margin-bottom: 5px;
}

.top-menu li a p.notify {
    color: #fff;
    text-decoration: none;
    background: #2da2d7;
    display: inline-block;
    padding: 1px 5px;
    border-radius: 3px;
    margin-top: 3px;
}

.top-menu li:hover, .top-menu li.on {
    background-color: #292929;
}

.usersearch.search,
.search {
    background: url("../images/icons/top-search.png") no-repeat scroll 0 0 rgba(0, 0, 0, 0);
    background-color: #292929;
    width: 285px;
    height: 47px;
    border-radius: 0.5rem;
    margin-left: 5px;
    float: left;
}

.search .input {
    background-color: transparent;
    height: 40px;
    margin: 3px 0;
    padding: 0 15px;
    border: 0 none;
    box-shadow: none;
    line-height: 1;
    color: #fff;
    font-size: 14px;
    float: left;
}

.search .submit {
    background-color: transparent;
    width: 45px;
    height: 40px;
    margin: 3px 0;
    border: 0 none;
    cursor: pointer;
    float: right;
}

.profile {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}


.profile-pic:focus {
    outline: none;
}

.drop-down > label {
    font-size: 100%;
    vertical-align: 4px;
}

.profile .select2-container {
    min-width: 13rem;
}

.profile .dropdown-toggle.btn-inverse {
    background: transparent !important;
    color: #FFFFFF !important;
    border: none;
}

.bootstrap-select.btn-group.profile-pic .btn .caret {
    color: #FFFFFF !important;
    right: 5px;
    border-color: #FFFFFF transparent;
    border-width: 10px 9px 0;
    border-top: 10px solid #fff;
}

.table-drop.btn-group.bootstrap-select.table-drop {
    width: 80px !important;
}

.bootstrap-select.btn-group.table-drop .btn .caret {
    border-width: 10px 5px 0;
    margin-top: -4px;
    right: 8px;
}

.profile .dropdown-menu.open {
    background: #292929;
}

.profile .dropdown-menu.open ul {
    background: #292929;
}

.profile .dropdown-menu.open ul li {
    color: #fff;
}

.profile .dropdown-menu > li > a {
    color: #fff;
}

.profile p {
    float: left;
    line-height: 42px;
    color: #fff;
    padding-right: 10px;
    background: url(../images/icons/top-profile-arrow.jpg) right center no-repeat;
    padding-right: 15px;
    font-weight: bold;
}

.profile img {
    float: right;
}

.gn-menu-main, .gn-menu-main ul {
    background: url("../images/icons/bg-header.jpg") repeat-x scroll 0 0 rgba(0, 0, 0, 0) !important;
}

.gn-menu-main {
    position: relative;
    margin: 0 auto;
}

.gn-menu-wrapper {
    position: relative;
    margin: 0 auto;
}

.gn-scroller, .gn-menu-wrapper.gn-open-all {
    position: relative;
    width: 260px;
}

.cat {
    margin-left: 35%;
    margin-top: 12px;
}

/* Header */

.box-text.pro-review > img.media {
    float: left;
    padding-right: 15px;
}

/*slider*/

.slideshow {
    margin: 20px auto;
    padding: 0;
    clear: both;
}

.slideshow.slide {
    margin: 0;
    padding: 0
}

.slideshow, .slideshow.slide {
    height: 135px;
    width: 640px;
}

.prev-arrow {
    float: left;
    margin-top: 1%;
    position: absolute;
    vertical-align: middle;
    width: 10%;
}

.next-arrow {
    left: 90.5%;
    margin-top: -140px;
    position: relative;
    vertical-align: top;
    width: 8%;
    height: 135px;
}

.bx-default-pager {
    display: none;
}

.bx-wrapper .bx-prev {
    left: 70px;
    transform: rotate(180deg);
    -ms-transform: rotate(180deg); /* IE 9 */
    -webkit-transform: rotate(180deg);
}

.bx-wrapper .bx-next {
    top: 50px !important;
}

.product-con .bx-wrapper .bx-next {
    background: url(../images/icons/ShipEarly_next.png);
}

.bx-wrapper .bx-next:hover {
    background-position: 0 0;
}

.product-con .bx-wrapper .bx-prev {
    background: url("../images/icons/ShipEarly_next.png") repeat scroll 0 0 rgba(0, 0, 0, 0);
}

.pro-slider .bx-wrapper .bx-controls-direction .bx-prev {
    border-radius: 5px;
    float: right;
    top: 285px !important;
}

.pro-slider .bx-wrapper .bx-controls-direction .bx-next {
    float: right;
    right: -50px;
    margin-top: 100px;
}

.pro-slider .bx-wrapper .bx-controls-direction a {
    border-radius: 5px;
    background-repeat: no-repeat;
}

/*slider*/

/*breadcrumb*/
.filtermenu {
    background-color: #292929;
    font-size: 14px;
    height: 48px;
    width: 100%;
}

.pro_filter {
    height: 44px;
}

.filtermenu li {
    color: #FFFFFF;
    float: left;
    font-weight: bold;
    text-align: center;
    width: 20%;
    height: inherit;
    line-height: 45px;
}

.card .filtermenu {
    border-radius: var(--bs-border-radius) var(--bs-border-radius) 0 0;
}

.card .filtermenu li:first-of-type {
    border-top-left-radius: var(--bs-border-radius);
}

.card .filtermenu li:last-of-type {
    border-top-right-radius: var(--bs-border-radius);
}

.filtermenu li.on,
.filtermenu li.filter-point:hover,
.filtermenu li.active {
    background-color: #5A9FFA;
}

/*breadcrum*/
.dropdown_div {
    background: url(../images/icons/dropdown.jpg) no-repeat scroll right top #CCCCCC;
    border: none;
    height: 27px;
    overflow: hidden;
    width: 80%;
}

.rows_select {
    float: left;
    height: 23px;
    width: 8%;
}

.product-show img {
    margin: -22px;
}

.select_div > label {
    display: inline;
    margin-right: 3px;
    margin-top: 10px;
    width: 30px;
    vertical-align: middle;
}

.table-drop > label {
    float: right;
}

.dropdown_div {
    background: url(../images/icons/dropdown.jpg) no-repeat scroll right top #CCCCCC;
    border: none;
    height: 27px;
    overflow: hidden;
    width: 80%;
}

.rows_select {
    width: 8%;
    height: 23px;
}

.dropdown_div select {
    background: -webkit-linear-gradient(top, #dddddd, #ffffff);
    background: -moz-linear-gradient(top, #dddddd, #ffffff);
    background: -ms-linear-gradient(top, #dddddd, #ffffff);
    background: -o-linear-gradient(top, #dddddd, #ffffff);
    background: linear-gradient(to bottom, #dddddd, #ffffff);
    border-radius: 0 0 0 0;
    font-size: 1.2em;
    height: 25px;
    overflow: hidden;
    padding: 0px;
    text-indent: 0.01px;
    text-overflow: "";
    width: 100%;
    border: none;
}

/* Content */
.shadow-box {
    width: 100%;
    float: left;
    background: none;
}

.report-box {
    clear: both;
    float: right;
    width: 100%;
}

.video-box {
    width: 100%;
    margin-right: 15px;
}

.social-box1 {
    width: 125px;
    padding: 10px 5px;
    margin-right: 7px;
    text-align: center;
}

.social-box2 {
    padding: 10px 5px;
    width: 125px;
    clear: both;
}

.rate-box {
    padding: 10px 15px;
    width: 255px;
    height: auto;
    float: right;
    clear: both;
}

.shadow-box .title {
    float: left;
    line-height: 23px;
    margin-bottom: 25px;
    color: #000000;
    font-size: 16px;
    text-transform: uppercase;
    width: 100%;
}

.shadow-box .title a {
    color: #00A0FC;
    float: right;
    margin-right: 4%;
    font-weight: bold;
    text-transform: none;
}

.shadow-box .more {
    float: right;
}

.shadow-box .title img {
    float: left;
    margin-right: 10px;
}

.box-text {
    clear: both;
}

.cont-cmpny-desc h3 {
    margin-top: 35px;
}

.place-btn {
    background-image: url(../images/icons/ShipEarly_rightbtn.png);
    border-radius: 8px 8px 8px 8px;
    color: #FFFFFF;
    font-size: 20px;
    font-weight: bold;
    margin: 0 12%;
    padding: 8px;
    width: 200px;
}

.palce-btn {
    background-image: url("../images/icons/ShipEarly_rightbtn.png");
    color: #FFFFFF;
    font-size: 20px;
    font-weight: bold;

}

.blue-btn {
    background-color: #3FA9F5;
    border: 3px solid #DDDDDD;
    border-radius: 8px 8px 8px 8px;
    color: #FFFFFF;
    float: right;
    font-weight: bold;
    font-family: Arial, sans-serif;
    height: 40px;
    line-height: 34px;
    margin-top: 15px;
    margin-bottom: 5px;
    width: 150px;
    overflow: hidden;
    text-align: center;
}
.blue-btn img {
    margin-left: 5px;
    width: 20px;
}


.order-tbl p {
    color: #00a0fc;
    font-size: 18px;
    text-align: center;
    font-size: 16px;
    font-weight: bold;
}

.order-price {
    text-align: center;
    padding-top: 5px;
}

.order-price a {
    color: #7d7d7d;
    text-decoration: underline;
}

.report-align, .rate-box p {
    text-align: center;
}

.btn-preview {
    border: 1px solid #1090c3;
    background-color: #1ab0ec;
    background-image: -webkit-gradient(linear, left top, left bottom, from(#1ab0ec), to(#1a92c2));
    background-image: -webkit-linear-gradient(top, #1ab0ec, #1a92c2);
    background-image: -moz-linear-gradient(top, #1ab0ec, #1a92c2);
    background-image: -ms-linear-gradient(top, #1ab0ec, #1a92c2);
    background-image: -o-linear-gradient(top, #1ab0ec, #1a92c2);
}

.btn-save {
    background-color: #3093c7;
    background-image: -webkit-gradient(linear, left top, left bottom, from(#3093c7), to(#1c5a85));
    background-image: -webkit-linear-gradient(top, #3093c7, #1c5a85);
    background-image: -moz-linear-gradient(top, #3093c7, #1c5a85);
    background-image: -ms-linear-gradient(top, #3093c7, #1c5a85);
    background-image: -o-linear-gradient(top, #3093c7, #1c5a85);
}

p.pre-con {
    color: #2b2b2b;
    font-size: 25px;
    margin: 5px;
    font-weight: bold;
}

p.like-count {
    height: 20px;
    padding: 11px 0 0;
    text-align: right;
    font-weight: bold;
}

p.fb-like {
    background: url(../images/icons/ShipEarly_fb.png) no-repeat scroll center center transparent;
    background-size: 116px 30px;
}

.fb-root {
    margin-top: 20px;
}

.defult-img {
    background: none repeat scroll 0 0 #CCCCCC;
    height: 130px;
    line-height: 130px;
}

p.tw-like {
    background: url(../images/icons/ShipEarly_tweet.png) no-repeat scroll center center transparent;
    background-size: 116px 30px;
}

p.social-box {
    color: #8a8a8a;
    font-size: 20px;
    margin: 10px;
    padding: 0 10px 0 0;
    text-align: center;
    font-weight: bold;
    word-spacing: 10px;
}

.box-text.cont-cmpny-desc > h3 {
    margin-top: 35px !important;
}

td.order-rate p {
    color: #8a8a8a;
}

.order-tbl tbody tr td {
    width: auto;
    padding: 10px 8px;
}

button:active, button:focus {
    outline: none;
}

.content-head {
    font-weight: bold;
    font-size: 25px;
    color: #5E5E5E;
    margin-bottom: 30px;
    text-transform: uppercase;
    padding-left: 15px;
}

.con1-head1 .sub-head1 {
    float: left;
    width: 50%;
}

.con1-head1 .sub-head2 {
    font-size: 28px;
    margin-top: -8px;
}

h3.cmpny-head a {
    font-weight: bold;
    color: #3FA9F5;
    font-size: 16px;
}

.pro-other {
    color: #000;
    font-size: 16px;
}

.sec-header {
    margin-top: -1px;
    width: 50%;
}

.sec-header .pro-other {
    color: #292929;
    font-size: 25px;
    font-weight: bold;
}

.shadow-box .order-det {
    padding: 0;
    position: relative;
    text-align: center;
    top: 50%;
    transform: translateY(-50%);
    width: 74%;
    word-wrap: break-word;
    -webkit-transform: translateY(-50%);
}

/*Column 1*/

.con1-head1 {
    float: left;
    width: 50%;
}

.con1-head2 {
    float: left;
    width: 20%;
}

.product-image {
    float: left;
}

.product-image > p {
    margin-top: 20px;
}

/*Column 1*/

/*Column 2*/
.sub-con2 {
    float: left;
    margin-top: 10px;
    width: 30%;
}

.right-box {
    margin-left: 15px;
}

.con2-head {
    width: 25%;
    float: left;
    margin-top: 7px;
}

.con2-head img {
    float: left;
}

.social-count {
    background: none repeat scroll 0 0 #FFFFFF;
    border: 4px solid #E6E6E6;
    box-shadow: -2px 3px 5px #D4D4D4;
    float: left;
    margin-bottom: 15px;
    padding: 10px 5px 5px 5px;
    text-align: center;
    width: 50%;
    min-height: 255px;
}

/*Column 2*/
.msrp-align {
    float: left;
    font-size: 18px;
}

.widget-cls {
    line-height: 1.25;
    float: left;
    text-align: left !important;
}

.dollar-align {
    width: 50%;
}

.widget-edit {
    background: url(../images/icons/ShipEarly_edit.png) no-repeat 34% 0;
    width: 34%;
}

.btn-fun {
    border: medium none;
    color: #FFFFFF;
    font-size: 14px;
    font-weight: bold;
    height: 45px;
    width: 128px;
    border-radius: 4px;
}

.export-box button {
    float: right;
    margin-left: 10px;
}
.export-box.retailer_export_box button{
    margin: 0 0 0 10px;
}

.add-btn {
    color: #FFFFFF;
    border: 1px solid #1090c3;
    background-color: #1ab0ec;
    background-image: -webkit-gradient(linear, left top, left bottom, from(#1ab0ec), to(#1a92c2));
    background-image: -webkit-linear-gradient(top, #1ab0ec, #1a92c2);
    background-image: -moz-linear-gradient(top, #1ab0ec, #1a92c2);
    background-image: -ms-linear-gradient(top, #1ab0ec, #1a92c2);
    background-image: -o-linear-gradient(top, #1ab0ec, #1a92c2);
    border-radius: 3px;
}

.widget-contact {
    background: url(../images/icons/ShipEarly_conatct.png) no-repeat 60% 0;
    border-radius: 4px 4px 4px 4px;
    padding: 0 10px;
    text-align: left !important;
}

.widget-btn {
    border: medium none;
    color: #FFFFFF;
    font-size: 14px;
    font-weight: bold;
    height: 27px;
    float: left;
    width: 75px;
}

.sold-count span img {
    padding-right: 5px;
    padding-top: 12px;
    float: left;
}

.sold-count h3 {
    font-size: 30px !important;
    font-weight: bold;
    text-align: center;
    width: 45%;
    color: #00A0FC;
}

.widget-box {
    background: #fff;
    height: inherit;
    float: left;
    padding-left: 20px;
}

.sold-count {
    width: 40%;
    float: left;
}

.purchase-details {
    clear: both;
    width: 100%;
}

.purchase-details .wid-pic-avail {
    margin: 15px;
}

.product-show {
    width: 22%;
    margin: 10px 5% 0 0;
    height: 190px;
}

.form-details input, .form-details textarea, .maparea {
    border: 4px solid #E6E6E6;
    border-radius: 4px;
    padding: 10px;
    background: #fff;
}

.form-details input {
    width: 16%;
}

.form-details textarea {
    max-height: 140px;
    max-width: 57%;
    min-height: 140px;
    min-width: 57%;
    text-align: justify;
}

.ui-datepicker-trigger {
    vertical-align: middle;
}

.maparea {
    width: 100%;
    height: 300px;
    clear: both;
}

.contact_logo {
    width: 205px;
    height: 110px;
    clear: both;
}

.form-submit input {
    width: 46.5%;
}

#purchase-form input {
    width: 30%;
}

.clear {
    clear: both;
}

.place_order.col-md-9 .col-md-4, .place_order.col-md-9 .col-md-7, .place_order.col-md-9 .col-md-12 {
    background-color: #FFFFFF;
    margin: 10px 0 0;
    padding: 10px;
}

.place_order .col-md-4 {
    float: right;
    margin-right: 0 !important;
}

.place_order .col-md-12 {
    min-height: 110px;
}

#purchase-form textarea {
    max-height: 140px;
    max-width: 101%;
    min-height: 140px;
    min-width: 101%;
    text-align: justify;
}

.form-submit textarea {
    max-height: 140px;
    max-width: 95%;
    min-height: 140px;
    min-width: 95%;
    text-align: justify;
}

address p {
    padding: 10px;
    color: #000 !important;
}

address a {
    color: #428bca;
}
address a:hover {
    text-decoration: underline;
}

.sub-con2 .social-icons {
    text-align: left;
    padding: 0;
}

.uploader {
    margin: 5px;
}

.uploader li {
    width: 50%;
    float: left;
}

#profile_pic {
    border: 3px solid #FFFFFF;
    border-radius: 4px 4px 4px 4px;
    height: 100px;
    width: 100px;
}

/*popup*/

.modal-content {
    background-color: #ebebeb !important;
}
.modal span.close {
    color: rgb(255, 255, 255);
    font-family: arial, sans-serif;
    font-size: 25px;
    font-weight: bold;
    margin-top: 47px;
    opacity: 1;
}

/* shipping rate popups */
.modal--shipping .modal-content {
    background-color: #fff !important;
}
.modal--shipping .modal-title {
    color: #212b36;
}
.modal--shipping .modal-header {
    border-bottom: 1px solid #dfe3e8;
}
.modal--shipping .modal-body .section {
    margin-left: -20px;
    margin-right: -20px;
    padding-left: 20px;
    padding-right: 20px;
    border-bottom: 1px solid #dfe3e8;
}

.pop-text,
.bootstrap-select:not([class*="col-"]):not([class*="form-control"]):not(.input-group-btn) > .btn.dropdown-toggle.btn-default {
    border: 1px solid #B5B5B5 !important;
    border-radius: 3px 3px 3px 3px !important;
    height: 40px;
    margin: 5px 0;
    padding: 10px !important;
    width: 100%;
}

.export-dropdown .dropdown-toggle {
    height: 40px;
    margin-top: 5px;
}

.help-block {
      display: block;
    margin-top: 5px;
    margin-bottom: 10px;
    color: #FF0000;
}

.col-md-10 .form-group .controls > .help-block{
    margin-left: 26%;
	width: 50%;
}
.label.label-info {
    margin-left: 26% !important;
    font-size: 80%;
    font-weight: 400;
    word-wrap: break-word;
    color: #000;
    text-align: left;
}

.controls label {
    font-size: 14px;
    font-weight: normal;
    margin: 0 10px 5px 0;
    vertical-align: middle;
    width: 24%;
    position: relative;
    top: 3px;
}

.col-md-10 .pop-text, .btn-group.bootstrap-select.select-input {
    width: 50% !important;
}

/* Autocomplete Dropdown */

.ui-front {
    z-index: 10000;
}
.ui-autocomplete {
    max-height: 300px;
    overflow-y: auto;
    overflow-x: hidden;
}
.ui-menu-item .product-thumbnail {
    float: left;
    margin-right: 0.4em;
}
.ui-menu-item:before,
.ui-menu-item:after {
    display: table;
    content: " ";
}
.ui-menu-item:after {
    clear: both;
}
.ui-autocomplete-category {
    font-weight: bold;
}

/* Pills around variant options */

.pill-product-variant{
	background-color: #e3e3e3;
	padding: 0px 10px;
	border-radius: 20px;
	display: inline-block;
}

/* Pills around Fulfillment status and Payment status */

.fulfillment-pill-unfulfilled {
    display: inline-block;
    padding: 3px 16px;
    background-color: #FFEB79;
    color: black;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    border-radius: 15px;
	position: relative;
}
.fulfillment-pill__circle-filled {
    width: 9px;
    height: 9px;
	border: 1px solid black;
    background-color: black;
    border-radius: 50%;
    position: absolute;
    top: 50%;
    left: 4px;
    transform: translateY(-50%);
}
.fulfillment-pill__circle-half-filled {
    width: 9px;
    height: 9px;
	border: 1px solid black;
    background-color: white;
    border-radius: 50%;
    position: absolute;
    top: 50%;
    left: 4px;
    transform: translateY(-50%);
    overflow: hidden;
}
.fulfillment-pill__circle-half-filled::before {
    content: '';
    width: 100%;
    height: 100%;
    background-color: black;
    border-radius: 0 100% 100% 0;
    position: absolute;
    top: 0;
    left: 0;
    transform-origin: right;
    transform: rotate(-45deg);
}
.fulfillment-pill__circle {
    width: 9px;
    height: 9px;
    border: 1px solid black;
    border-radius: 50%;
    position: absolute;
    top: 50%;
    left: 4px;
    transform: translateY(-50%);
}
.fulfillment-pill-partialfulfilled {
    display: inline-block;
    padding: 3px 16px;
    background-color: #FED7A4;
    color: black;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border-radius: 15px;
	position: relative;
}
.fulfillment-pill {
    display: inline-block;
    padding: 3px 16px;
    background-color: #F0F0F0;
    color: black;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border-radius: 15px;
	position: relative;
}

/* Search and filter button on Orders Page */

.fa-arrow-right:before {
    content: none !important;
}
.fa-arrow-left:before {
    content: none !important;
}
.simple-pagination li a, .simple-pagination li span {
    display: block !important;
    width: 30px !important;
    height: 30px !important;
    line-height: 30px !important;
    text-align: center !important;
    border: 0px solid #d8d7d7 !important;
    border-radius: 5px !important;
    background-color: #efefef !important;
    font-size: 18px !important;
}
.simple-pagination li a:hover {
    background-color: #bbb !important;
}

.simple-pagination li .prev, .simple-pagination li .next {
    font-family: Arial, sans-serif !important;
}

.simple-pagination li .prev::before {
    content: "<" !important;
}

.simple-pagination li .next::before {
    content: ">" !important;
}
.light-theme .current {
	box-shadow: none !important;
}
/* Hide the numbers */
.simple-pagination li a:not(.prev):not(.next),
.simple-pagination li span:not(.prev):not(.next) {
    display: none !important;
}

/* Pill around dropdown */

.dropdown-pill {
    border: none;
    box-shadow: none;
	background-color: #d3d3d3;
	padding: 0px 10px;
	border-radius: 10px;
	display: inline-block;
    width: 250px;
}
.dropdown-pill option:first-child {
    font-size: 20px;
}
.dropdown-pill option {
    font-size: 16px;
    color: #333;
}

/* Box around Product Image on Product/Inventory Tabs */

.image-box {
    border: 1px solid #ccc;
    border-radius: 5px;
    margin-right: 5px;
    width: 52px;
    height: 45px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
	background-color: white;
}

/* hellip dropdown on Order Page */

@media (max-width: 576px) {
  .dropdown-hellip {
	transform: translate3d(-122.5px, 35.5px, 0px) !important;
  }
}

/* Override .controls for using .form-control */

.controls-horizontal-form-control > label {
    display: inline-block;
    font-size: 14px;
    font-weight: normal;
    margin: 0 10px 5px 0;
    vertical-align: middle;
    width: 100%;
}

.controls-horizontal-form-control > .controls-input-container,
.controls-horizontal-form-control > .form-control {
    display: inline-block;
    vertical-align: middle;
    width: 100%;
}


/* END Override .controls for using .form-control */

.controls .agree {
    vertical-align: top;
    width: 75%;
}

#termsConditions {
    margin: 0;
}

.create-link {
    width: 100%;
}

.create-link a {
    color: #000000;
    text-decoration: underline;
}

.create-link a.forgotlink {
    float: right;
}

.reset_but{
    width: 100%;
    overflow: visible;
    text-align: center;
}
.save_but{
   top: auto;
   position: relative;

}

#create-popup .reset-con {
    margin: 25px 0 0 25%;
}

/*popup*/
/*pro-preview*/
.pro-review {
    float: left;
    height: 60px;
    margin: 10px 0;
    padding: 0 !important;
    width: 100%;
}

.pro-review:not(:last-child) {
    border-bottom: 4px solid #ebebeb;
}

.pro-review div {
    float: left;
}

.pro-review .pro-rating {
    float: right;
}

/*pro preview*/
.shadow-box.box-text tr td {
    vertical-align: top;
}

.shadow-box.box-text .pro-title td {
    vertical-align: middle !important;
}

.wid-container {
    background: none repeat scroll 0 0 #FFFFFF;
    border: 1px solid #BBBBBB;
    box-shadow: 5px 5px 5px #BBBBBB;
    width: 95%;
    float: left;
    height: 375px;
    margin-left: 4px;
}

.wid-container.wid-single-con {
    margin-right: 10px;
    width: 30%;
    height: 365px;
    box-shadow: 1px 1px 5px #888888;
}

.wid-details {
    padding: 2px 5px;
    margin-bottom: 10px;
    background: none repeat scroll 0 0 #EEEEEE;
    height: 130px;
}

.wid-details h5 {
    font-size: 15px;
    margin-top: 10px !important;

}

.wid-details h5 a {
    color: #2A6496;
}

.wid-ratings {
    margin-bottom: 10px;
    padding: 0px 10px 20px
}

.wid-pic {
    background: none repeat scroll 0 0 #CCCCCC;
    height: 160px;
    line-height: 160px;
    text-align: center;
    margin: 10px;
}

.wid-pic-avail img {
    margin-top: 6px;
    border: none;
}

.wid-pic-avail {
    height: 174px;
    line-height: 11;
    text-align: center;
}

.product-image {
    margin-right: 10px;
}

.loc-icon {
    background: url(../images/icons/ShipEarly_location.png) no-repeat scroll 0 0 rgba(0, 0, 0, 0);
    padding-left: 12px;
}

.wid-rate {
    float: left;
    width: 50%;
}

.wid-rate > h6 {
    padding: 0 0 5px;
}

.wid-ratings img {
    float: right;
    margin: -4px 0;
    padding: 0 !important;
    width: 14px;
}

.wid-ratings p {
    width: 30%;
    margin: 0px 5px;
}

.wid-ratings p img {
    width: 10px;
    height: 10px;
}

.pro-image {
    clear: both;
    margin: 5% 0;
    padding-top: 10px;
    width: 100%;
}

.pro-image:not(:first-child) {
    border-top: 4px solid #ccc;
}

.pro-image p {
    color: #000 !important;
    float: left;
    font-size: 20px !important;
    text-align: left !important;
    width: 75%;
}

.pro-image p span, .file_up span {
    height: 32px;
    padding: 5px 20px 7px 25px;
    width: 32px;
}

.file_up span {
    padding: 0 20px 20px 25px;
}

.sell-icon {
    background: url(../images/icons/productstripe.png) repeat scroll -8px 0 rgba(0, 0, 0, 0);
}

.popular-icon {
    background: url(../images/icons/productstripe.png) repeat scroll -8px -61px rgba(0, 0, 0, 0);
}

.recent-icon {
    background: url(../images/icons/productstripe.png) repeat scroll -8px -127px rgba(0, 0, 0, 0);
}

.bx-loading {
    background: no-repeat scroll center center #ebebeb !important;
}

.dash-column, .dash-column1 {
    float: left;
    padding-right: 10px;
    width: 50%;
}

.dash-column div p {
    text-align: center;
    font-size: 16px;
    font-weight: bold;
}

.pro-image > a {
    color: #00A0FC;
    float: left;
    font-size: 16px;
    font-weight: bold;
    margin-top: 12px;
    text-align: right;
    width: 20%;
}

.dash-column div.social-count {
    height: 100px;
}

.row-img {
    float: left;
    margin-left: -40px;
    position: relative;
}

.row-img, .not-accordion {
    display: none;
}

.manu-option {
    margin: 16px !important;
    text-align: center;
    vertical-align: middle;
}

.category_tbl {
    width: 100% !important;
}

.prev-next {
    float: left;
    margin-left: 25%;
    width: 100%;
}

.prev-next .btn-fun {
    float: left !important;
    left: 50%;
    margin-left: 10%;
}

/* Content */

/* Footer */

#footer {
    background-color: #292929;
    color: #b3b3b3;
    margin-top: 25px;
    width: 100%;
}



#footer ul {
    float: left;
    width: 155px;
    margin: 20px 20px 0 0;
}

.footer-menu {
    padding-left: 200px;
}

#footer ul li {
    width: 100%;
    background: url(../images/icons/bg-footer-nav.png) 0 bottom no-repeat;
    padding-bottom: 2px;
}

#footer ul li.title {
    text-transform: uppercase;
    padding-bottom: 10px;
}

#footer ul li a {
    line-height: 25px;
    background: url(../images/icons/bg-footer-bullet.png) 2px center no-repeat;
    padding-left: 10px;
    color: inherit;
}

#google_translate_element {
    background-color: var(--bs-body-bg);
    height: 25px;
}
#google_translate_element img {
    display: inline;
}

.social-icons {
    text-align: center;
    width: 260px;
    float: left;
    padding-top: 20px;
    height: 115px;
}

.subscribe {
    width: 328px;
    float: left;
    border: 1px solid;
    border-color: #090909 #2d2d2d #2d2d2d #090909;
    float: right;
    margin: 20px 40px 24px 0;
    height: 63px;
}

.inner {
    border: 1px solid;
    border-color: #2d2d2d #090909 #090909 #2d2d2d;
    padding: 15px 10px;
}

.subscribe .input {
    width: 70%;
    height: 19px;
    line-height: 19px;
    background-color: #fff;
    border: 1px solid #4d4d4d;
    color: #666;
}

.subscribe .submit {
    width: 25%;
    height: 31px;
    background: url(../images/icons/footer-button-subscribe.png) no-repeat;
    border: 0;
    color: #fff;
    cursor: pointer;
    border-radius: 4px;
}

.copyright {
    width: 100%;
    clear: both;
    padding: 10px;
    text-align: center;
}

.footer-logo {
    height: 40px;
    line-height: 30px;
    padding-bottom: 10px;
    text-align: center;
}

.footer-logo img {
    max-height: 100%;
    max-width: 100%;
    width: auto;
    margin: auto;
}

/* /Footer */

/*Should be removed*/

.main-box {
    height: auto;
    float: left;
    position: relative;
}

.map-box {
    min-height: 270px;
}

.form-field {
    width: 28% !important;
}

.box-text .greet-cont {
    font-size: 15px;
    line-height: 35px;
    margin: 10px 30px;
}

/*Should be removed*/

/*Media query*/
.shadow-box.main-box table {
    margin-top: -55px;
    width: 100%;
}

.pro-title td {
    margin: 10px;
}

.pro-title .box-text > p {
    max-height: 220px;
    overflow: hidden;
}

.perfect-scrollbar {
    position: relative;
}

.retailer-tbl {
    background: #fff;
}

.retailer-tbl td p.retail-home {
    background: url("../images/icons/home-icon.png") no-repeat scroll center top rgba(0, 0, 0, 0);
}

.retailer-tbl td p.retail_order {
    background: url("../images/icons/blackbox.png") no-repeat scroll center top rgba(0, 0, 0, 0);
}

.retailer-tbl td p.new_retail {
    background: url("../images/icons/Add.png") no-repeat scroll center top rgba(0, 0, 0, 0);
}

.retailer-tbl td p, td a p {
    text-align: center;
}

.retailer-tbl td p, td a p span {
    color: #000000;
    padding-top: 15px;

    font-weight: normal;
}

.tbl-link {
    color: #428BCA;
    font-size: 14px;
}

.btn-warning {
    background-color: #333;
    border-color: #333;
    color: #FFFFFF;
}

.btn-warning:hover, .btn-warning:focus, .btn-warning:active, .btn-warning.active, .open .dropdown-toggle.btn-warning {
    background-color: #000000;
    border-color: #000000;
    color: #FFFFFF;
    outline: medium none;
}

.retailer-tbl a, .header-link {
    color: #3FA9F5;
    font-size: 14px;
    font-weight: bold;
}

.discount_margin_top,
.sidebar {
    float: left;
    margin-left: -15px;
}

/*Contact*/
.cont-cmpny-logo {
    float: left;
    margin-right: 10px;
}

.cont-cmpny-logo img {
    min-width: 150px;
    max-height: 120px;
}

.cont-online-details {
    float: left;
    text-align: center;
    width: 35% !important;
}

.cont-cmpny-logo.cont-online-details > h3 {
    text-align: left;
}

.cont-online-details a {
    padding: 0 10px 20px 20px;
}

.cont-online-details .cont-cmpy-details p,
.cont-online-details .cont-cmpy-details a {
    margin-top: 10px;
}

.comm-skype {
    background: url("../images/icons/skype-icon.png") no-repeat scroll 0 0 rgba(0, 0, 0, 0);
    background-size:25px 25px;
}

.comm-link {
    background: url("../images/icons/in-icon.png") no-repeat scroll 0 0 rgba(0, 0, 0, 0);
    background-size:25px 25px;
}

.comm-timing {
    background: url("../images/icons/timing.png") no-repeat scroll 0 0 rgba(0, 0, 0, 0);
    background-size:25px 25px;
    width: 400px !important;
}

.comm {
    width: 100%;
}

.cont-cmpny-head {
    width: 55% !important;
    float: left;
    margin: 0 10px;
}

.cont-details h4, .cont-details h2, .comp-online-details {
    text-align: center;
}

.cont-cmpny-desc p {
    color: #000;
    font-size: 16px;
    padding: 10px 0 10px 10px;
}

.cont-details h2 {
    color: #00A0FC;
    font-weight: bold;
}


.cont-addr div address {
    background: url(../images/icons/blog.png) no-repeat scroll 0 0 rgba(0, 0, 0, 0);
    background-size:20px 30px;
    min-height: 30px;
    padding-left: 30px;
    text-align: left;
}

.cont-mail {
    background: url(../images/icons/msg-icon.png) no-repeat scroll 0 0 rgba(0, 0, 0, 0);
    background-size:25px 20px;
    width: 100%;
    height: 20px;
}

span.cont-support a {
    width: fit-content;
    margin-left: 5px;
    padding-left: 0;
}

.cont-currency {
    background: url(../images/icons/currency-icon.png) no-repeat scroll 0 0 rgba(0, 0, 0, 0);
    background-size:25px 20px;
    width: 100%;
    height: 20px;
}

.cont-phn {
    background: url(../images/icons/phon-icon.png) no-repeat scroll 0 0 rgba(0, 0, 0, 0);
    background-size:25px 30px;
}

.cont-cmpy-details p,
.cont-cmpy-details>a {
    font-size: 16px;
    height: 30px;
    margin: 0 0 15px;
    padding-left: 30px;
    width: 100%;
    word-break: break-all;
}

.cont-cmpy-details a {
    display: inline-block;
}

.cont-addr div address {
    color: #8A8A8A;
    font-size: 16px;
}

.cmpy-contact {
    width: 50%;
}

.cmpy-contact .cont-cmpy-details {
    padding-top: 10px;
}

.comp-online-details a {
    padding-top: 5px;
}

.wid-details .box-text > p {
    max-height: 100px;
    overflow: hidden;
}

#avatar {
    display: none;
}
.avatar-preview {
    max-height: 80px;
    max-width: 100%;
}

textarea.pop-text {
    max-height: 200px;
    min-height: 150px;
}

.filter-point, .comm a {
    cursor: pointer;
}

.filtermenu .filter-pointer, .filtermenu .filter-pointer:hover {
    cursor: pointer;
}

.filter-default {
    background-color: #5A9FFA;
}

.box-text.shadow-box h2 {
    margin-top: 10px;
}

.msg-dis {
    text-align: center;
    height: 290px;
}

.msg-dis h5 {
    margin-top: 20px;
}

.clear-cls {
    clear: both;
    padding-top: 10px;
}

.product-thumbnail {
    max-width: 50px;
    max-height: 50px;
}

.msg_panel {
    display: block;
}

.retailer-notes-body {
    clear: both;
}

.retailer-note__author,
.retailer-note__date {
    min-height: 1em;
}
.retailer-note__date {
    margin-bottom: 1rem;
}

.retailer-note-header, .retailer-note-footer {
    display: flex;
    justify-content: space-between;
}

.retailer-note__body {
    position: relative;
}

.retailer-note__body textarea {
    width: 100%;
    resize: vertical;
    min-height: 5em;
}

.retailer-note__body .retailer-note__actions {
    position: relative;
    display: block;
}
.retailer-note__body .retailer-note__actions > .dropdown-toggle {
    position: absolute;
    top: 0;
    right: 0;
}

.retailer-note__body .retailer-note__actions > .dropdown-menu {
    margin-top: 30px;
}

.website-box {
    max-width: 60%;
    margin-left: 105px;
}

@media (min-width: 1025px) and (max-width: 1440px) {
    .website-box {
        max-width: 61%;
        margin-left: 79px;
    }
}

@media (min-width: 769px) and (max-width: 1024px) {
    .website-box {
        max-width: 61%;
        margin-left: 56px;
    }
}

@media (max-width: 768px) {
    .website-box {
        max-width: 61%;
		margin-left: 50px;
    }
}

@media (max-width: 576px) {
    .website-box {
        max-width: 95%;
		margin-left: 0;
    }
}

/*Product temp Slider*/
.product-con .bx-wrapper {
    margin: 0 auto 60px;
    padding: 0;
    position: relative;
}

.product-con .bx-wrapper .bx-viewport {
    float: left;
}

.product-con .bx-wrapper .bx-viewport {
    background: none repeat scroll 0 0 rgba(0, 0, 0, 0);
    border: medium none;
    box-shadow: none;
    left: 0;
}

.wid-pic img {
    display: inline !important;
}

/*Product temp Slider*/

/*Contact*/

.meaage_title {
    color: #009FFF;
}

.message_con {
    margin: 10px 0;
    font-size: 16px;
    line-height: 24px
}

.bottom_seemore.clear, .bottom_seemore1.clear {
    color: #2A6496;
    font-size: 18px;
    font-weight: bold;
    padding: 10px;
    text-align: center;
    background: #FFFFFF none repeat scroll 0 0;
    border: 4px solid #EEEEEE;
    box-shadow: -2px 3px 5px #BBBBBB;
}

#products_list.clear {
    margin-bottom: 10px;
}

.bottom_seemore, .bottom_seemore1 {
    cursor: pointer;
}

.no-of-retails span {
    color: #009FFF;
    font-weight: bold;
}

.modal-body .reset-con {
    width: auto;
}

.fL {
    float: left;
}

.fR {
    float: right;
}

/* for bug */
.shadow-box-dash {
    width: 112%;
    padding: 20px 15px;
    float: left;
    background: none repeat scroll 0 0 #FFFFFF;
    margin-left: -57px;
}

.modal-footer, .modal-header {
    border-top: none;
    border-bottom: none;
}

.headfontcl {
    font-size: 40px;
    font-weight: bold;
}

.orderpop {
    margin: 0;
}

.orderhead {
    font-size: 22px;
}








.notification-con {
    min-height: 50px;
    background-color: #F1F1F1;
    border-bottom: 1px solid #ccc;
    padding-bottom: 10px;
    padding-top: 5px;
    min-width: 340px;
}

a:hover .notification-con {
    background-color: #D1D1D1;
}

.unread {
    background-color: #DDDDDD;
}

.notification-cont {
    color: #000;
}

.notification-con a {
    color: #3FA9F5;
    padding-top: 0 !important;
}

.popover.header-popover .popover-body {
    max-height: 400px;
    padding: 0;
    overflow-y: scroll;
}

.popover.header-popover {
    width: auto;
    max-width: 364px;
    min-width: 320px;
    text-align: left;
}

.popover-body a {
    display: inherit !important;
}

#notification_list .media, #notification_list .media .media, .popover-body .media, .popover-body .media .media {
    margin-top: 0;
}

.popover-footer {
    margin: 0;
    padding: 8px 14px;
    font-size: 14px;
    font-weight: 400;
    line-height: 18px;
    background-color: #F7F7F7;
    border-bottom: 1px solid #EBEBEB;
    border-radius: 5px 5px 0 0;
}

.no-of-retails {
    margin-bottom: 0;
}

.discounticon {
    display: inline;
    width: 18px;
    height: 15px;
    padding-left:2px;
}

#order_frm .box-text .clear-cls {
    padding-top: 0;
}

#order_frm .Order_select .select_div {
    padding: 0;
    width: 100%;
}


.content-head {
    margin-bottom: 0;
}

.dashbrd .header-link:hover,
.dashbrd .header-link:focus {
    text-decoration: underline;
}

.inputBlock .input {
    width: 75%;
}

.inputBlock .submit {
    width: 45px !important;
    height: 45px !important;
    float: right;
    margin: 0 !important;
}

.bx-wrapper {
    margin: 0 auto !important;
}

.paddingAlign .title {
    margin-bottom: 0;
}

.sub-con2, .dropDownHeight button {
    margin-top: 0px !important;
}

.paddingAlign .title a {
    margin-right: 0px;
}

.paddingAlign .title p {
    float: left;
    margin-top: 3px;
}

.tw-share {
    box-shadow: 0 3px 5px #d4d4d4;
}

#top_seller, #recently_add {
    margin: 0;
    padding: 0;
}

.paddingAlign {
    padding: 15px 15px 0 15px;
}

.wid-pic {
    margin: 0;
}

.productName {
    width: 24%;
    padding: 0 0 15px 0px;
}

.social-count {
    width: 50%;
    margin-left: 0;
}

.fixedflash {
    position: absolute;
    width: 98%;
    top: 250px;
    border: solid;
    z-index: 2;
}

.btn-base .icon {
    font-size: 25px;
    height: 25px;
    width: 25px;
    display: inline-block;
}

.btn-success1 .icon {
    color: #0075F8;
    border-color: #0075F8;
    font-size: 25px;
}

.btn-success4 .icon {
    color: #B94612;
    border-color: #B94612;
    font-size: 25px;
}

.btn-success5 .icon {
    color: #FF9801;
    border-color: #FF9801;
    font-size: 25px;
}

.btn-success1:hover, .btn-success4:hover, .btn-success5:hover {
    opacity: 0.75;
    transition-duration: 0.5s;
}

.button-checkbox.bootstrap-checkbox.large {
    margin-left: 35px;
}

input:focus, textarea:focus, select:focus, .bootstrap-select button:focus {
    border: 1px solid #3FA9F5;
    box-shadow: 0 1px 2px #ddd inset, 0 0 5px #3FA9F5;
    outline: 0 none;
}

.modal-vertical-centered {
    transform: translate(0, 50%) !important;
    -ms-transform: translate(0, 50%) !important; /* IE 9 */
    -webkit-transform: translate(0, 50%) !important; /* Safari and Chrome */
}
.progress-bar.animate {
    width: 100%;
}
#permission .contactLink {
    display: inline-block;
}
.slide .wid-container.wid-single-con{
    width: 95% !important;
}

textarea, input[type="text"], input[type="password"], input[type="datetime"], input[type="datetime-local"], input[type="date"], input[type="month"], input[type="time"], input[type="week"], input[type="number"], input[type="email"], input[type="url"], input[type="search"], input[type="tel"], input[type="color"], .uneditable-input {
    background-color: #ffffff;
    border: 1px solid #cccccc;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.075) inset;
    transition: border 0.2s linear 0s, box-shadow 0.2s linear 0s;
}
.tooltip {
    color: #000;
    opacity: 1;
    font-size: inherit;
    position: relative;
    text-decoration: none;
}
.up{
    text-transform: uppercase;
}
.low{
    text-transform: lowercase;
}

/* Place icon inside text box */
.inner-addon {
    position: relative;
}
.inner-addon .glyphicon,
.inner-addon .fas {
    position: absolute;
    padding: 10px;
    pointer-events: none;
}
.left-addon .glyphicon,
.left-addon .fas {
    left:  0;
    z-index: 10;
}
.left-addon input {
    padding-left:  30px;
}
.right-addon .glyphicon,
.right-addon .fas {
    right: 0;
    z-index: 10;
}
.right-addon input {
    padding-right: 30px;
}

/** For Tabs In My Account Page ****/
#mtab .filtermenu li {
    width: 33.33%;
}
#mtab .filtermenu li.filter-point > a:focus,
#mtab .filtermenu li.filter-point > a:active,
#mtab .filtermenu li.filter-point > a:hover {
    color: #FFFFFF;
}

/***** For Enable And Disable Button *****/
.onoffswitch {
    margin-left: 15px;
    padding-left: 5px;
    position: relative; width: 95px;
    -webkit-user-select:none; -moz-user-select:none; -ms-user-select: none;
}
.onoffswitch-checkbox {
    display: none;
}
.onoffswitch-label {
    display: block; overflow: hidden; cursor: pointer;
    border: 2px solid #9E9E9E; border-radius: 15px;
}
.onoffswitch-inner {
    display: block; width: 200%; margin-left: -100%;
    -moz-transition: margin 0.3s ease-in 0s; -webkit-transition: margin 0.3s ease-in 0s;
    -o-transition: margin 0.3s ease-in 0s; transition: margin 0.3s ease-in 0s;
}
.onoffswitch-inner:before, .onoffswitch-inner:after {
    display: block; float: left; width: 50%; height: 30px; padding: 0; line-height: 30px;
    font-size: 14px; color: white; font-family: Trebuchet, Arial, sans-serif; font-weight: bold;
    -moz-box-sizing: border-box; -webkit-box-sizing: border-box; box-sizing: border-box;
}
.onoffswitch-inner:before {
    content: "Disable";
    padding-left: 6px;
    text-align: left;
    background-color: #DB1F1F; color: #FFFFFF;
}
.onoffswitch-inner:after {
    content: "Enable";
    padding-right: 10px;
    background-color: #20C42D; color: #FFFFFF;
    text-align: right;
}
.onoffswitch-switch {
    display: block; width: 25px; height: 25px; margin: 3px;
    background: #f2f6f8;
     /* Old browsers */
    /* IE9 SVG, needs conditional override of 'filter' to 'none' */
    background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/Pgo8c3ZnIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgdmlld0JveD0iMCAwIDEgMSIgcHJlc2VydmVBc3BlY3RSYXRpbz0ibm9uZSI+CiAgPGxpbmVhckdyYWRpZW50IGlkPSJncmFkLXVjZ2ctZ2VuZXJhdGVkIiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgeDE9IjAlIiB5MT0iMCUiIHgyPSIwJSIgeTI9IjEwMCUiPgogICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iI2YyZjZmOCIgc3RvcC1vcGFjaXR5PSIxIi8+CiAgICA8c3RvcCBvZmZzZXQ9IjUwJSIgc3RvcC1jb2xvcj0iI2Q4ZTFlNyIgc3RvcC1vcGFjaXR5PSIxIi8+CiAgICA8c3RvcCBvZmZzZXQ9IjUxJSIgc3RvcC1jb2xvcj0iI2I1YzZkMCIgc3RvcC1vcGFjaXR5PSIxIi8+CiAgICA8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0b3AtY29sb3I9IiNlMGVmZjkiIHN0b3Atb3BhY2l0eT0iMSIvPgogIDwvbGluZWFyR3JhZGllbnQ+CiAgPHJlY3QgeD0iMCIgeT0iMCIgd2lkdGg9IjEiIGhlaWdodD0iMSIgZmlsbD0idXJsKCNncmFkLXVjZ2ctZ2VuZXJhdGVkKSIgLz4KPC9zdmc+);
    background: -moz-linear-gradient(top, #f2f6f8 0%, #d8e1e7 50%, #b5c6d0 51%, #e0eff9 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#f2f6f8), color-stop(50%,#d8e1e7), color-stop(51%,#b5c6d0), color-stop(100%,#e0eff9)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, #f2f6f8 0%,#d8e1e7 50%,#b5c6d0 51%,#e0eff9 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, #f2f6f8 0%,#d8e1e7 50%,#b5c6d0 51%,#e0eff9 100%); /* Opera 11.10+ */
    background: -ms-linear-gradient(top, #f2f6f8 0%,#d8e1e7 50%,#b5c6d0 51%,#e0eff9 100%); /* IE10+ */
    background: linear-gradient(to bottom, #f2f6f8 0%,#d8e1e7 50%,#b5c6d0 51%,#e0eff9 100%); /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#f2f6f8', endColorstr='#e0eff9',GradientType=0 ); /* IE6-8 */
    border: 2px solid #9E9E9E; border-radius: 15px;
    position: absolute; top: 0; bottom: 0; /* right: 61px;*/
    -moz-transition: all 0.3s ease-in 0s; -webkit-transition: all 0.3s ease-in 0s;
    -o-transition: all 0.3s ease-in 0s; transition: all 0.3s ease-in 0s;
}
.onoffswitch-checkbox:checked + .onoffswitch-label .onoffswitch-inner {
    margin-left: 0;
}
.onoffswitch-checkbox:checked + .onoffswitch-label .onoffswitch-switch {
    right: 0px;
}
.empty-avatar {
    height: 80px;
    width: 100px;
    display: inline-block;
    border: 1px solid #ccc;
    vertical-align: middle;
}

 #legend-box, #legend-box1{
    height:440px !important;
    }
.jqplot-table-legend tbody{
    display:block;
    }

#addMedialink{float:right;}



.toggle-light .toggle-blob{border:none !important}


.toggle-light .toggle-on, .toggle-light .toggle-off {
   white-space: nowrap;
 }

.toggle-light .toggle-on {
   font-size: 13px !important;
 }
.toggle-light .toggle-off {
   font-size: 11px !important;
 }

.brandControls label{display: inline; position: relative; top: 2px;}

.multiSelectRainBox a,
.multiSelectRainBox .slash {
    color: #428bca;
    font-weight: bold;
}

.multiSelectRainBox  label{vertical-align: top;}



.error-class {
    border: 1px solid red !important;
}
.submit-price{
    clear:both;
	text-align: right;
}
.flat-align {
	right: 8px;
}
.currency-align{
	right: 7px;
}

.add-button{
    width: 100%;
}
.icon ul {
	display:none;
	z-index:1000;
}


.icon:hover ul {
	display:block;
}
.edit-img .edit-options li a {
    transition:background-color 0.2s ease-in-out;
    -webkit-transition:background-color 0.2s ease-in-out;
    -moz-transition:background-color 0.2s ease-in-out;
    display:block;
    color:#fff;
    font-size: 12px;
    padding: 3px;
    text-decoration:none;
    border: 1px solid #d1d1d1;
}
.edit-options{
	position:absolute;
	padding: 0px;
	background: #333333;
	text-align: left;
	font-weight: 900;
    width: 80px;
    right: 0;
    z-index:100;
}
.edit-options li {
    line-height: 1rem;
}
.edit-options li a:hover{
    background-color: #009FFF;
	transition:background-color 0.2s ease-in-out;
	-webkit-transition:background-color 0.2s ease-in-out;
	-moz-transition:background-color 0.2s ease-in-out;
	display:block;
	color:#fff;
	font-size: 11px;
	text-decoration:none;
	border: 1px solid #d1d1d1;
}
.edit-options li a:hover,.edit-options li a:active{
	background-color: #5a9ffa;
}

.courier-name {
    float:left;
    margin-left: 11%;
    margin-right: 15%;
}

.track-button {
    float:left;
    margin-left: 11%;
}
button.close{
	vertical-align: middle;
    transform: translateY(-20%);
}
.useredit .col-md-10 .pop-text {
    width: 30% !important;
    font-family: 'OpenSans-Regular', Sans-Serif;
    font-size: 13px;
}
.useredit .select-input {
    width: 34% !important;
    color: #000000 !important;
	height: 42px;
}

.useredit .col-md-10 .form-group .controls > .help-block{
    margin-left: 0% !important;
    width: 50%;
}
.useredit .col-md-10 .form-group .controls > .error{
	border: 1px solid red !important;
}
.msg_panel .alert .close {top: 4px !important;}
.useredit .col-md-10 .form-group .controls > input:focus:invalid:focus, select:focus:invalid:focus{
	border: 1px solid red !important;
	box-shadow: none !important;
}
/*new*/
 #retail_id{
	 margin-top: 1px;
	 font-size:14px;
}
#brand_id{
	 margin-top: 1px;
	 font-size:14px;
}

.header-card {
    z-index: 1;
    box-shadow: 0 2px 0 rgba(0, 0, 0, 0.07);
    margin-bottom: 15px;
    min-height: 50px;
}
.header-card-controls,
.header-card .breadcrumb-header {
    line-height: 50px;
    margin: 0;
    vertical-align: middle;
    white-space: nowrap;
}
.header-card-controls {
    text-align: right;
}

.footer-card {
    border-top: 1px solid #ccc;
    min-height: 50px;
    line-height: 50px;
    margin-bottom: 10px;
}
.next-card {
    background-color: #ffffff;
    border-radius: 3px;
    border: 1px solid #ccc;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 15px;
    margin-bottom: 15px;
}
.next-card > .table:last-child,
.next-card > .table-responsive:last-child,
.next-card > .table-responsive:last-child > .table {
    margin-bottom: 0;
}

.margingap {
    padding: 5px;
    margin-bottom: 0;
    margin-right: 5px;
}
.margingap .table {
    margin-bottom: 20px;
}

.modal-open[style] {
    padding-right: 0px !important;
}
.help_alert
{
  margin-bottom: 0px !important;
}
.form_margin
{
  margin-bottom: 10px;
}
.modal-dialog .modal-body .courier .btn.dropdown-toggle.btn-default, .courier .btn-group.bootstrap-select.select-input, #trackingno
{
 width:100% !important;
}
.modal:focus {
    outline: 0;
}
.new_side1{
	width:50%;
	padding-right:20px;
	word-break: break-all;
}
.new_side2{
	width:50%;
	padding-left:60px;
	word-break:break-all;
}
.new_side2 address{
	word-break: initial;
}

.thumbImg img {
	float: right;
}


.wholesale-topup-infographic {
    width: 100%;
    margin: 10px 0;
}
#wholesaleTopupInfographicPreview {
    background-color: #ebebeb;
}


.b2bTax {
    width: 100%;
    text-align: center;
    max-width: 100px;
    padding-left: 15px;
}
.rp-sbtn{
    margin: 10px 0 10px 5px;
}
#return_policy {
    width:95%;
    font-size:13px;
}
.return_policy a {
        color: #428bca;
}
.pricingError {
    text-align: center;
    color: #FF0000;
}

/************Shipment Settings Css ****************/
.brandcheckalign > input[type="checkbox"],
.brandControls > input[type="checkbox"] {
    vertical-align: middle;
}
.brandcheckalign > label {
    margin-top: 3px;
}
.brandControls {
    margin-bottom: 10px;
}
.offerprice_col > label {
    width: auto;
}

.status_pill {
    border-radius: 30px;
    padding: 0px 10px;
}

.status_pill--expired {
    background-color: #e2e0d3;
    color: #333;
}

.status_pill--active {
    background-color: #77933b;
    color: #fff;
}

.status_pill--disabled {
    background-color: #d3dbe2;
    color: #5d6063;
}

.status_pill--danger {
    background-color: #dc3545;
    color: #fff;
}

.status_pill--warning {
    background-color: #ffc107;
    color: #333;
}

/************************Shipment Settings css *******************/
.dealer-options,
.wbsprice {
    display: inline-block;
}
.edit-img > .addRate_edit{
    color: #009fff;
    cursor: pointer;
    padding-right: 15px;
}
.edit-img > .addRate_delete{
    color: #7c7c7c;
    cursor: pointer;
}
.shipment-setting-modal{
    left: 0;
}
.price-bd-section,
.wbsp-addrate-panel{
    background: #fff none repeat scroll 0 0;
    border: 1px solid #ccc;
    height: auto;
    padding: 25px;
}
.dealer-options > h2,
.wbsprice > h2{
    font-size: 16px;
}
.wbsp-addrate-header{
    display: inline-block;
    margin-left: 0;
}
.price-bd-header > .header,
.wbsp-addrate-header > .header{
    font-size: 15px;
    font-weight: bold;
}
.ship-stgshrline{
    display: block;
    border: 0;
    border-top: 1px solid #ccc;
}
.shipmentmodaltitle{
    color: #000;
}


.countryflag {
    width: 24px;
    height: 24px;
}

/****************Discounts Css*************************************************/
.discounted .Currency {
    text-decoration: line-through;
}
.discount {
    clear: both;
    color: #FF0000;
}

.text-overflow {
    overflow: hidden;
    text-overflow: ellipsis;
}

/* webkit support on non supporting browsers */
.discount-description {
    display: inline-block;
    line-height: 1.4em;
    height: 1.4em;
    cursor: default;
}

.discount-description.discount-description-line-2{
    height: 2.8em;
}

@supports (-webkit-line-clamp: 1) 
    and (-webkit-line-clamp: 2) {
    .discount-description {
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        height: 1.4em;
    }
    .discount-description.discount-description-line-2{
        -webkit-line-clamp: 2;
    }
}

/***********B2B Cart CSS ******************/
div.existing-carts-table{
    max-height:208px;
    overflow-y:auto;
    display:block;
}

.existing-carts-table tr .row {
    height: 4rem;
    cursor: pointer;
}

/****************Validations Messages on weight based rate ********************/
.errorclr{
    color: #FF0000 !important;
}

.spinner input {
  text-align: right;
}

.input-group-btn-vertical {
  position: relative;
  white-space: nowrap;
  width: 2%;
  vertical-align: middle;
  display: table-cell;
  padding-top: 25px;
  border-radius: 0;
}

.input-group-btn-vertical > .btn {
  display: block;
  float: none;
  width: 100%;
  max-width: 100%;
  padding: 8px;
  margin-left: -1px;
  position: relative;
  border-radius: 0;
}

.input-group-btn-vertical > .btn:first-child {
  border-top-right-radius: 4px;
}

.input-group-btn-vertical > .btn:last-child {
  margin-top: -1px;
  border-bottom-right-radius: 4px;
  border-right: 0;
}

.input-group-btn-vertical i {
  position: absolute;
  top: 0;
  left: 4px;
}

.mininputbox,
.maxinputbox{
    border-top-left-radius: 5px !important;
    border-bottom-left-radius: 5px !important;
    text-align: left !important;
}
.brandcheckalign > input,
.brandcheckalign > label{
    display: inline;
}
.ratetable thead>tr>th{
    font-family: Arial;
    border-bottom: none !important;
}
.ratetable thead>tr>td{
    word-wrap: break-word;
}
.addRateFooter {
    padding: 0;
}
.ratetable thead>tr>th{
    word-wrap: break-word;
}
.ratetable{
    table-layout: fixed;
}
.ratetable tbody>tr>td{
    width: 100%;
    word-wrap: break-word;
}
.angle-btn{
    border-right: 0px;
    border-radius: 0px ;
    margin: 0px;
}
.lb-span{
    margin-top: 25px;
    display: inline-block;
    border: 1px solid #cccccc;
    border-left: 0px;
    padding: 6px 15px 6px 10px;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    margin-left: 0px;
    position: relative;
    left: -3px;
}
.wbsp-addrate-panel{
    margin-bottom: 35px;
}
.input-group-btn-vertical > .angletop-btn{
    border-left: medium none;
    outline: 0 none;
    border-radius: 0;
}
.input-group-btn-vertical > .anglebottom-btn{
    border-left: medium none;
    border-left: medium none;
    border-top: medium none navy;
    margin-top: -1px;
    outline: 0 none;
    border-radius: 0;
}
.formgroup > .col-lg-12 > input,
.formgroup > .col-lg-6 > input,
.input-group.spinner > input{
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}
.price-bd-section.row {
    margin-left: 0;
    margin-right: 0;
}
.price-bd-section > .table-responsive:last-child,
.price-bd-section > .table-responsive:last-child > .table {
    margin-bottom: 0;
}
.modal-body { -webkit-overflow-scrolling: touch; }

.modal--view .modal-header .modal-title {
    font-weight: bold;
    color: #3FA9F5;
    text-shadow: 1px 1px #999;
    text-transform: uppercase;
}
.modal--view .modal-body .con1-head1 {
    display: none;
}

.bootbox--form .modal-body form input[type="submit"],
.bootbox--form .modal-body form button[type="submit"] {
    display: none;
}

.bootbox--form-disabled .modal-footer button[data-bb-handler="Submit"] {
    display: none;
}

/* Override with bootstrap.min.css styles */
.bs-font-family {
    font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
}
.modal--terms a,
a.link {
    color: #428bca;
}
.modal--terms a:hover,
.modal--terms a:focus,
a.link:hover,
a.link:focus {
    color: #2a6496;
    text-decoration: underline;
}
.modal--terms a:focus,
a.link:focus {
    outline: thin dotted #333;
    outline: 5px auto -webkit-focus-ring-color;
    outline-offset: -2px;
}

.dropdown-menu > li > a:hover,
.dropdown-menu > li > a:focus,
.dropdown-menu > .active > a,
.dropdown-menu > .active > a:hover,
.dropdown-menu > .active > a:focus {
    color: #333333;
    background-color: #ebebeb;
}

#signup .form-group label~.select2.select2-container,
#signup .form-group label~.form-control
{
    display: inline-block;
    width: 50% !important;
}
@media (max-width: 767px) {
    #signup .form-group label~.select2.select2-container,
    #signup .form-group label~.form-control
    {
        width: 94% !important;
    }
}

/** checkout settings ***/

.map-marker__preview {
    padding: 0;
}
.map-marker__preview img {
    height: 2rem;
}

/** BOOTSTRAP MIGRATION SHIMS **/

.hide,
.hidden {
    display: none !important;
}

.close {
  float: right;
  font-size: 1.5em;
  font-weight: bold;
  line-height: 1;
  color: #000;
  text-shadow: 0 1px 0 #fff;
  opacity: .2;
}

.popover {
    --bs-popover-max-width: 500px;
}

.form-group {
    margin-bottom: 1rem;
}

.btn.btn-default {
    --bs-btn-color: #6c757d;
    --bs-btn-border-color: #6c757d;
    --bs-btn-hover-color: #fff;
    --bs-btn-hover-bg: #6c757d;
    --bs-btn-hover-border-color: #6c757d;
    --bs-btn-focus-shadow-rgb: 108,117,125;
    --bs-btn-active-color: #fff;
    --bs-btn-active-bg: #6c757d;
    --bs-btn-active-border-color: #6c757d;
    --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
    --bs-btn-disabled-color: #6c757d;
    --bs-btn-disabled-bg: transparent;
    --bs-btn-disabled-border-color: #6c757d;
    --bs-gradient: none;
}

.btn.btn-default.btn-secondary {
    --bs-btn-bg: var(--bs-body-bg);
}

.form-control[readonly] {
    background-color: var(--bs-gray-200);
    opacity: 1;
}

.btn-content-header {
    --bs-btn-padding-y: 0.67rem;
}
