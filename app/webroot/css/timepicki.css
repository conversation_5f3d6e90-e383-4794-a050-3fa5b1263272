
/* 
Created on: 17 Sep, 2014, 4:29:37 PM
Author: senthil
*/

.time_pick .ti_tx,
.time_pick .mi_tx,
.time_pick .mer_tx {
	margin: 5px auto;
    text-align: center;
    width: 82%;
	height: 23px;
}

.time_pick .time,
.time_pick .mins,
.time_pick .meridian {
	width: 40px;
	float: left;
	margin: 0;
	font-size: 20px;
	color: #2d2e2e;
	font-family: arial;
	font-weight: 700;
}

.time_pick .prev,
.time_pick .next {
	cursor: pointer;
	padding: 10px 13px;
	width: 28%;
	border: 1px solid #ccc;
	margin: auto;
	background: url(../images/arrow.png) no-repeat;
	border-radius: 5px;
}

.time_pick .prev:hover,
.time_pick .next:hover {
	background-color: #ccc;
}

.time_pick .next {
	background-position: 50% -32%;
    background-size: 16px 40px;
}

.time_pick .prev {
	background-position: 50% 122%;
	background-size: 16px 40px;
}

.time_pick {
	position: relative;
	display: inline-block;
}

.time_pick .timepicker_wrap {
	padding: 10px 5px;
	border-radius: 5px;
	z-index: 998;
	display: none;
	box-shadow: 2px 2px 5px 0 rgba(50,50,50,0.35);
	background: #f6f6f6;
	border: 1px solid #ccc;
	float: left;
	position: absolute;
	top: 27px;
	left: 0;
	width: 132px;
}

.time_pick .arrow_top {
	position: absolute;
	top: -10px;
	left: 20px;
	background: url(../images/top_arr.png) no-repeat;
	width: 18px;
	height: 10px;
	z-index: 999;
}
.time_pick input.timepicki-input {
	background: none repeat scroll 0 0 #FFFFFF;
	border: 1px solid #CCCCCC;
	border-radius: 5px 5px 5px 5px;
	float: none;
	margin: 0;
	text-align: center;
	width: 87%;
	vertical-align: top;
	font-size: 14px !important;	
}
.time_pick a.reset_time {
	float: left;
	margin-top: 5px;
	color: #000;
}
