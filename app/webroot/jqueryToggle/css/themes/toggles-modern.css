.toggle-modern .toggle-slide {
  border-radius: 4px;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.25), 0 0 1px rgba(0, 0, 0, 0.2);
  background: -webkit-gradient(linear, left top, left bottom, from(#c0c5c9), to(#a1a9af));
  background: -webkit-linear-gradient(#c0c5c9, #a1a9af);
  background: linear-gradient(#c0c5c9, #a1a9af);
  -webkit-box-shadow: inset 0 2px 1px rgba(0, 0, 0, 0.2), inset 0 0 0 1px rgba(0, 0, 0, 0.15), 0 1px 0 rgba(255, 255, 255, 0.15);
          box-shadow: inset 0 2px 1px rgba(0, 0, 0, 0.2), inset 0 0 0 1px rgba(0, 0, 0, 0.15), 0 1px 0 rgba(255, 255, 255, 0.15);
}
.toggle-modern .toggle-on,
.toggle-modern .toggle-off {
  -webkit-transition: all 0.1s ease-out;
          transition: all 0.1s ease-out;
  color: white;
  text-shadow: 1px 1px rgba(0, 0, 0, 0.1);
  font-size: 11px;
  -webkit-box-shadow: inset 0 2px 0 rgba(255, 255, 255, 0.2), inset 0 0 0 1px rgba(0, 0, 0, 0.2), inset 0 -1px 1px rgba(0, 0, 0, 0.1), 0 1px 1px rgba(0, 0, 0, 0.2);
          box-shadow: inset 0 2px 0 rgba(255, 255, 255, 0.2), inset 0 0 0 1px rgba(0, 0, 0, 0.2), inset 0 -1px 1px rgba(0, 0, 0, 0.1), 0 1px 1px rgba(0, 0, 0, 0.2);
}
.toggle-modern .toggle-select .toggle-off,
.toggle-modern .toggle-select .toggle-on {
  background: none;
}
.toggle-modern .toggle-off,
.toggle-modern .toggle-off.active {
  background: -webkit-gradient(linear, left top, left bottom, from(#737e8d), to(#3f454e));
  background: -webkit-linear-gradient(#737e8d, #3f454e);
  background: linear-gradient(#737e8d, #3f454e);
}
.toggle-modern .toggle-on,
.toggle-modern .toggle-on.active {
  background: -webkit-gradient(linear, left top, left bottom, from(#4894cd), to(#2852a6));
  background: -webkit-linear-gradient(#4894cd, #2852a6);
  background: linear-gradient(#4894cd, #2852a6);
}
.toggle-modern .toggle-blob {
  background: -webkit-gradient(linear, left top, left bottom, from(#c0c6c9), to(#81898f));
  background: -webkit-linear-gradient(#c0c6c9, #81898f);
  background: linear-gradient(#c0c6c9, #81898f);
  -webkit-box-shadow: inset 0 2px 0 rgba(255, 255, 255, 0.2), inset 0 0 0 1px rgba(0, 0, 0, 0.2), inset 0 -1px 1px rgba(0, 0, 0, 0.1), 1px 1px 2px rgba(0, 0, 0, 0.2);
          box-shadow: inset 0 2px 0 rgba(255, 255, 255, 0.2), inset 0 0 0 1px rgba(0, 0, 0, 0.2), inset 0 -1px 1px rgba(0, 0, 0, 0.1), 1px 1px 2px rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}
.toggle-modern .toggle-blob:hover {
  background-image: -webkit-gradient(linear, left top, left bottom, from(#a1a9af), to(#a1a9af));
  background-image: -webkit-linear-gradient(#a1a9af, #a1a9af);
  background-image: linear-gradient(#a1a9af, #a1a9af);
}
