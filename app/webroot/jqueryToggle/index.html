<html>
<head>
    <title>My cool page</title>
  
    <link rel="stylesheet" href="css/toggles.css">
	<link href="css/themes/toggles-light.css" rel="stylesheet">

<!-- ALL OF THE THEMES -->
<!-- <link rel="stylesheet" href="css/toggles-full.css"> -->

<!-- ALL OF THE CSS AND THEMES IN ONE FILE -->
	<!-- <link rel="stylesheet" href="css/toggles-full.css" /> -->
</head>
<body>
<div style="width: 110px;">
	<span>	
		<div class="toggles toggle-light" data-toggle-on="true" data-id="10">
			
		</div>
	</span>
</div>
<style type="text/css">
	.toggle-on {
		text-indent: 15px !important;
	}
	.toggle-off {
		text-indent: 25px !important;
	}
</style>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/1.11.2/jquery.min.js"></script>
<script src="js/toggles.min.js" type="text/javascript"></script>
<script type="text/javascript">
// With options (defaults shown below)
$('.toggles').toggles({
	drag: true, // allow dragging the toggle between positions
	click: true, // allow clicking on the toggle
	text: {
		on: 'Enabled', // text for the ON position
		off: 'Disabled' // and off
	},
	//on: true, // is the toggle ON on init
	animate: 250, // animation time
	transition: 'swing', // animation transition,
	checkbox: null, // the checkbox to toggle (for use in forms)
	clicker: null, // element that can be clicked on to toggle. removes binding from the toggle itself (use nesting)
	width: 120, // width used if not set in css
	height: 30, // height if not set in css
	type: 'compact' // if this is set to 'select' then the select style toggle will be used
});

$('.toggles').on('toggle', function (e, active) {
	if (active) {
		foo($(this));
	} else {
		bar($(this));
	}
});

function foo(data)
{
	alert(data.data('id'));
}

function bar(data)
{
	alert(data.data('id'));
}
</script>
</body>
</html>