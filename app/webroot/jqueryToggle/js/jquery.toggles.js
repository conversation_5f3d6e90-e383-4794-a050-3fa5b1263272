$.fn['toggles']=function(options){options=options||{};var opts=$.extend({'drag':true,'click':true,'text':{'on':'ON','off':'OFF'},'on':false,'animate':250,'transition':'ease-in-out','checkbox':null,'clicker':null,'width':50,'height':20,'type':'compact'},options);var selectType=(opts['type']=='select');opts['checkbox']=$(opts['checkbox']);if(opts['clicker'])opts['clicker']=$(opts['clicker']);var transition='margin-left '+opts['animate']+'ms '+opts['transition'];var transitions={'-webkit-transition':transition,'-moz-transition':transition,'transition':transition};var notransitions={'-webkit-transition':'','-moz-transition':'','transition':''};var doToggle=function(slide,width,height,state){var active=slide.toggleClass('active').hasClass('active');if(state===active)return;var inner=slide.find('.toggle-inner').css(transitions);slide.find('.toggle-off').toggleClass('active');slide.find('.toggle-on').toggleClass('active');opts['checkbox'].prop('checked',active);if(selectType)return;var margin=active?0:-width+ height;inner.css('margin-left',margin);setTimeout(function(){inner.css(notransitions);inner.css('margin-left',margin);},opts['animate']);};return this.each(function(){var toggle=$(this);var height=toggle.height();var width=toggle.width();if(!height||!width){toggle.height(height=opts.height);toggle.width(width=opts.width);}
var div='<div class="toggle-';var slide=$(div+'slide">');var inner=$(div+'inner">');var on=$(div+'on">');var off=$(div+'off">');var blob=$(div+'blob">');var halfheight=height/2;var onoffwidth=width- halfheight;on.css({height:height,width:onoffwidth,textAlign:'center',textIndent:selectType?'':-halfheight,lineHeight:height+'px'}).html(opts['text']['on']);off.css({height:height,width:onoffwidth,marginLeft:selectType?'':-halfheight,textAlign:'center',textIndent:selectType?'':halfheight,lineHeight:height+'px'}).html(opts['text']['off']).addClass('active');blob.css({height:height,width:height,marginLeft:-halfheight});inner.css({width:width*2- height,marginLeft:selectType?0:-width+ height});if(selectType){slide.addClass('toggle-select');toggle.css('width',onoffwidth*2);blob.hide();}
toggle.html(slide.html(inner.append(on,blob,off)));slide.on('toggle',function(e,active){if(e)e.stopPropagation();doToggle(slide,width,height);toggle.trigger('toggle',!active);});toggle.on('toggleOn',function(){doToggle(slide,width,height,false);});toggle.on('toggleOff',function(){doToggle(slide,width,height,true);});if(opts['on']){doToggle(slide,width,height);}
if(opts['click']&&(!opts['clicker']||!opts['clicker'].has(toggle).length)){toggle.on('click',function(e){if(e.target!=blob[0]||!opts['drag']){slide.trigger('toggle',slide.hasClass('active'));}});}
if(opts['clicker']){opts['clicker'].on('click',function(e){if(e.target!=blob[0]||!opts['drag']){slide.trigger('toggle',slide.hasClass('active'));}});}
if(!opts['drag']||selectType)return;var diff;var slideLimit=(width- height)/4;var upLeave=function(e){toggle.off('mousemove');slide.off('mouseleave');blob.off('mouseup');var active=slide.hasClass('active');if(!diff&&opts.click&&e.type!=='mouseleave'){slide.trigger('toggle',active);return;}
if(active){if(diff<-slideLimit){slide.trigger('toggle',active);}else{inner.animate({marginLeft:0},opts.animate/2);}}else{if(diff>slideLimit){slide.trigger('toggle',active);}else{inner.animate({marginLeft:-width+ height},opts.animate/2);}}};var wh=-width+ height;blob.on('mousedown',function(e){diff=0;blob.off('mouseup');slide.off('mouseleave');var cursor=e.pageX;toggle.on('mousemove',blob,function(e){diff=e.pageX- cursor;var marginLeft;if(slide.hasClass('active')){marginLeft=diff;if(diff>0)marginLeft=0;if(diff<wh)marginLeft=wh;}else{marginLeft=diff+ wh;if(diff<0)marginLeft=wh;if(diff>-wh)marginLeft=0;}
inner.css('margin-left',marginLeft);});blob.on('mouseup',upLeave);slide.on('mouseleave',upLeave);});});};