(function() {
    'use strict';

    // Template based on https://tailwindcss.com/plus/ui-blocks/ecommerce/components/shopping-carts#component-ee213165d75da7e921c0bf15f3ab054b
    const template = document.createElement('template');
    template.innerHTML = `
<link rel="stylesheet" href="" id="TailwindCssStylesheet">
<style>
    :host {
        display: block;
    }
    :host([hidden]) {
        display: none;
    }
</style>
<button type="button" class="px-3 py-2 rounded-l-sm rounded-r-none cursor-pointer bg-(--accent-color) hover:bg-(--accent-color-hover) text-white fixed top-1/2 right-0 -translate-y-1/2" data-element="cart.open">
    <div class="text-lg mb-2" aria-label="Cart count" data-element="toggle.count" translate="no">0</div>
    <svg xmlns="http://www.w3.org/2000/svg" class="size-5" viewBox="0 0 25 25">
        <g fill="white">
            <path d="M24.6 3.6c-.3-.4-.8-.6-1.3-.6h-18.4l-.1-.5c-.3-1.5-1.7-1.5-2.5-1.5h-1.3c-.6 0-1 .4-1 1s.4 1 1 1h1.8l3 13.6c.2 1.2 1.3 2.4 2.5 2.4h12.7c.6 0 1-.4 1-1s-.4-1-1-1h-12.7c-.2 0-.5-.4-.6-.8l-.2-1.2h12.6c1.3 0 2.3-1.4 2.5-2.4l2.4-7.4v-.2c.1-.5-.1-1-.4-1.4zm-4 8.5v.2c-.1.3-.4.8-.5.8h-13l-1.8-8.1h17.6l-2.3 7.1z"></path>
            <circle cx="9" cy="22" r="2"></circle>
            <circle cx="19" cy="22" r="2"></circle>
        </g>
    </svg>
    <span class="sr-only">Open cart</span>
</button>
<div class="relative z-10" aria-labelledby="slide-over-title" role="dialog" aria-modal="true">
    <div id="backdrop" class="fixed inset-0 bg-gray-500/75 transition-opacity" aria-hidden="true"></div>
    <div class="fixed inset-0 overflow-hidden">
        <div class="absolute inset-0 overflow-hidden">
            <div class="pointer-events-none fixed inset-y-0 right-0 flex max-w-full pl-10">
                <div id="slide-over-panel" class="pointer-events-auto w-screen max-w-md" style="box-shadow: -4px 0 12px rgba(0, 0, 0, 0.25);">
                    <form method="post" action="javascript:void(0);" class="flex h-full flex-col overflow-y-scroll bg-white shadow-xl">
                        <div class="flex-1 overflow-y-auto px-4 py-6 sm:px-6">
                            <div class="flex items-start justify-between">
                                <h2 class="text-lg font-medium text-gray-900" id="slide-over-title">Shopping cart</h2>
                                <div class="ml-3 flex h-7 items-center">
                                    <button type="button" class="relative -m-2 p-2 text-gray-400 hover:text-gray-500" autofocus data-element="cart.close">
                                        <span class="absolute -inset-0.5"></span>
                                        <span class="sr-only">Close panel</span>
                                        <svg class="size-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true" data-slot="icon">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18 18 6M6 6l12 12" />
                                        </svg>
                                    </button>
                                </div>
                            </div>
                            <div class="mt-8">
                                <div class="flow-root">
                                    <ul id="cart-items" role="list" class="-my-6 divide-y divide-gray-200"><!-- Replaced by cartItemTemplateString --></ul>
                                    <p id="cart-items-empty" class="py-6 text-center text-gray-500">No items in cart</p>
                                </div>
                            </div>
                        </div>
                        <div class="border-t border-gray-200 px-4 py-6 sm:px-6">
                            <div class="flex justify-between text-base font-medium text-gray-900">
                                <p>Subtotal</p>
                                <p id="subtotal" translate="no">$0.00</p>
                            </div>
                            <p class="mt-0.5 text-sm text-gray-500">Shipping and taxes calculated at checkout.</p>
                            <div class="mt-6">
                                <button type="submit" class="w-full rounded-md border border-transparent px-6 py-3 text-base font-medium text-white shadow-xs bg-(--accent-color) hover:bg-(--accent-color-hover) disabled:bg-gray-500">Checkout</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
`;

    customElements.define('shipearly-buy-button', class extends HTMLElement {
        static get observedAttributes() {
            return [
                'backdrop',
                'cart-items',
                'currency',
                'labels',
                'open',
                'tw-href',
            ];
        }

        get twHref() {
            return this.shadowRoot.getElementById('TailwindCssStylesheet').getAttribute('href');
        }

        set twHref(value) {
            this.shadowRoot.getElementById('TailwindCssStylesheet').setAttribute('href', value);
        }

        get isDialogShown() {
            return !this.shadowRoot.querySelector('[role="dialog"]').hidden;
        }

        set isDialogShown(value) {
            this.shadowRoot.querySelector('[role="dialog"]').hidden = !value;
        }

        get open() {
            return this.hasAttribute('open');
        }

        set open(value) {
            if (value) {
                this.setAttribute('open', '');
            } else {
                this.removeAttribute('open');
            }
        }

        get backdrop() {
            return this.hasAttribute('backdrop');
        }

        set backdrop(value) {
            if (value) {
                this.setAttribute('backdrop', '');
            } else {
                this.removeAttribute('backdrop');
            }
        }

        get cartItems() {
            return JSON.parse(this.getAttribute('cart-items') || '[]');
        }

        set cartItems(value) {
            if (value) {
                this.setAttribute('cart-items', JSON.stringify(value));
            } else {
                this.removeAttribute('cart-items');
            }
        }

        get currency() {
            return this.getAttribute('currency') || 'USD';
        }

        set currency(value) {
            if (value) {
                this.setAttribute('currency', value);
            } else {
                this.removeAttribute('currency');
            }
        }

        get labels() {
            return JSON.parse(this.getAttribute('labels') || '{}');
        }

        set labels(value) {
            if (value) {
                this.setAttribute('labels', JSON.stringify(value));
            } else {
                this.removeAttribute('labels');
            }
        }

        /** @type {boolean} */
        #isTransitioning = false;

        /** @type {Object} */
        #oldQuantityByKey = {};

        /** @type {Intl.NumberFormat} */
        #priceFormatter = new Intl.NumberFormat(undefined, {
            style: 'currency',
            currency: this.currency,
            currencyDisplay: 'narrowSymbol',
        });

        constructor() {
            super();
            this.attachShadow({ mode: 'open' });
            this.shadowRoot.appendChild(template.content.cloneNode(true));

            this._onExternalClick = this._onExternalClick.bind(this);
            this._onClick = this._onClick.bind(this);
            this._onFocusIn = this._onFocusIn.bind(this);
            this._onFocusOut = this._onFocusOut.bind(this);
            this._onKeyDown = this._onKeyDown.bind(this);
            this._onSubmit = this._onSubmit.bind(this);

            this.#initTwHref()
                .then(() => this.#fixTailwindCssCustomProperties());
        }

        connectedCallback() {
            this.#initAccentColorCssVars();
            if (!this.backdrop) {
                this.enableBackdrop(false);
            }
            this.isDialogShown = this.open;

            document.addEventListener('click', this._onExternalClick);
            this.shadowRoot.addEventListener('click', this._onClick);
            this.shadowRoot.addEventListener('focusin', this._onFocusIn);
            this.shadowRoot.addEventListener('focusout', this._onFocusOut);
            this.shadowRoot.addEventListener('keydown', this._onKeyDown);
            this.shadowRoot.addEventListener('submit', this._onSubmit);
        }

        disconnectedCallback() {
            document.removeEventListener('click', this._onExternalClick);
            this.shadowRoot.removeEventListener('click', this._onClick);
            this.shadowRoot.removeEventListener('focusin', this._onFocusIn);
            this.shadowRoot.removeEventListener('focusout', this._onFocusOut);
            this.shadowRoot.removeEventListener('keydown', this._onKeyDown);
            this.shadowRoot.removeEventListener('submit', this._onSubmit);
        }

        attributeChangedCallback(name, oldValue, newValue) {
            switch (name) {
                case 'backdrop':
                    this.enableBackdrop(newValue !== null);
                    break;
                case 'currency':
                    this.renderCurrency(this.currency);
                    break;
                case 'cart-items':
                    this.renderCartItems(this.cartItems);
                    break;
                case 'labels':
                    this.renderLabels(this.labels);
                    break;
                case 'open':
                    if (newValue !== null) {
                        this.show();
                    } else {
                        this.close();
                    }
                    break;
                case 'tw-href':
                    this.twHref = newValue;
                    break;
            }
        }

        _onExternalClick(event) {
            if (!event.target.closest(this.tagName)) {
                this.close();
                return;
            }
        }

        _onClick(event) {
            if (event.target.closest('[data-element="cart.open"]')) {
                event.preventDefault();
                this.show();
                return;
            }
            if (event.target.closest('[data-element="cart.close"]') || !event.target.closest('#slide-over-panel')) {
                event.preventDefault();
                this.close();
                return;
            }
            if (event.target.closest('[data-element="lineItem.quantityDecrement"], [data-element="lineItem.quantityIncrement"]')) {
                const quantityInput = event.target.closest('[data-element="lineItem.quantity"]').querySelector('[data-element="lineItem.quantityInput"]');
                quantityInput.dispatchEvent(new FocusEvent('focusin', { bubbles: true, composed: true, view: window }));
                quantityInput.stepUp(event.target.closest('[data-element="lineItem.quantityIncrement"]') ? 1 : -1);
                quantityInput.dispatchEvent(new FocusEvent('focusout', { bubbles: true, composed: true, view: window }));
                return;
            }
            if (event.target.closest('[data-element="lineItem.remove"]')) {
                const quantityInput = event.target.closest('[data-element="lineItem.quantityContainer"]').querySelector('[data-element="lineItem.quantityInput"]');
                quantityInput.dispatchEvent(new FocusEvent('focusin', { bubbles: true, composed: true, view: window }));
                quantityInput.value = 0;
                quantityInput.dispatchEvent(new FocusEvent('focusout', { bubbles: true, composed: true, view: window }));
                return;
            }
        }

        _onFocusIn(event) {
            if (event.target.closest('[data-element="lineItem.quantityInput"]')) {
                const key = event.target.closest('[data-key]').getAttribute('data-key');
                this.#oldQuantityByKey[key] = event.target.value;
            }
        }

        _onFocusOut(event) {
            if (event.target.closest('[data-element="lineItem.quantityInput"]')) {
                const key = event.target.closest('[data-key]').getAttribute('data-key');

                const oldValue = this.#oldQuantityByKey[key] || '0';
                const newValue = event.target.value;
                if (oldValue === newValue) {
                    return;
                }
                delete this.#oldQuantityByKey[key];

                let quantity = parseInt(newValue);
                if (isNaN(quantity)) {
                    event.target.value = oldValue;
                    return;
                }

                const max = parseInt(event.target.max);
                if (!isNaN(max) && quantity > max) {
                    event.target.value = max;
                    quantity = max;
                }
                const min = parseInt(event.target.min);
                if (!isNaN(min) && quantity < min) {
                    event.target.value = min;
                    quantity = min;
                }

                this.cartItems = this.cartItems.map(item => {
                    if (item.id.toString() === key) {
                        item.quantity = quantity;
                    }
                    return item;
                }).filter(item => item.quantity > 0);

                this.dispatchEvent(new CustomEvent('change', {
                    bubbles: true,
                    cancelable: false,
                    composed: true,
                    detail: { originalEvent: event },
                }));
            }
        }

        _onKeyDown(event) {
            if (event.key === 'Escape') {
                event.preventDefault();
                this.close();
            }
        }

        _onSubmit(event) {
            this.dispatchEvent(new CustomEvent(event.type, {
                bubbles: event.bubbles,
                cancelable: event.cancelable,
                composed: true,
                detail: { originalEvent: event },
            }));
        }

        async show() {
            if (this.isDialogShown || this.#isTransitioning) {
                return;
            }
            this.#isTransitioning = true;

            this.isDialogShown = true;
            await Promise.allSettled([
                this.#transitionElement(
                    this.shadowRoot.getElementById('backdrop'),
                    ['ease-in-out', 'duration-500'],
                    ['opacity-0'],
                    ['opacity-100']
                ),
                this.#transitionElement(
                    this.shadowRoot.getElementById('slide-over-panel'),
                    ['transform', 'transition', 'ease-in-out', 'duration-500', 'sm:duration-700'],
                    ['translate-x-full'],
                    ['translate-x-0']
                ),
            ]);
            // Reset leftover toClasses after elements are visible
            this.shadowRoot.getElementById('backdrop').classList.remove('opacity-100')
            this.shadowRoot.getElementById('slide-over-panel').classList.remove('translate-x-0')

            this.#isTransitioning = false;

            // Focus on an element for accessibility
            this.shadowRoot.querySelector('[autofocus]').focus();
        }

        async close() {
            if (!this.isDialogShown || this.#isTransitioning) {
                return;
            }
            this.#isTransitioning = true;

            await Promise.allSettled([
                this.#transitionElement(
                    this.shadowRoot.getElementById('backdrop'),
                    ['ease-in-out', 'duration-500'],
                    ['opacity-100'],
                    ['opacity-0']
                ),
                this.#transitionElement(
                    this.shadowRoot.getElementById('slide-over-panel'),
                    ['transform', 'transition', 'ease-in-out', 'duration-500', 'sm:duration-700'],
                    ['translate-x-0'],
                    ['translate-x-full']
                ),
            ]);
            this.isDialogShown = false;
            // Reset leftover toClasses after elements are hidden
            this.shadowRoot.getElementById('backdrop').classList.remove('opacity-0')
            this.shadowRoot.getElementById('slide-over-panel').classList.remove('translate-x-full')

            this.#isTransitioning = false;
        }

        async #transitionElement(element, transitionClasses, fromClasses, toClasses) {
            const durationPadding = 5;

            element.classList.remove(...toClasses);
            element.classList.add(...fromClasses);

            await new Promise(resolve => setTimeout(resolve, durationPadding));

            element.classList.add(...transitionClasses);
            element.classList.remove(...fromClasses);
            element.classList.add(...toClasses);

            await new Promise(resolve => setTimeout(resolve, this.#getTransitionDurationFromElement(element) + durationPadding));

            element.classList.remove(...transitionClasses);
        }

        /**
         * @param element
         * @returns {number}
         * @see https://github.com/twbs/bootstrap/blob/v5.3.5/js/src/util/index.js#L47-L68
         */
        #getTransitionDurationFromElement(element) {
            if (!element) {
                return 0;
            }

            // Get transition-duration of the element
            let { transitionDuration, transitionDelay } = window.getComputedStyle(element);

            const floatTransitionDuration = Number.parseFloat(transitionDuration);
            const floatTransitionDelay = Number.parseFloat(transitionDelay);

            // Return 0 if element or transition duration is not found
            if (!floatTransitionDuration && !floatTransitionDelay) {
                return 0;
            }

            // If multiple durations are defined, take the first
            transitionDuration = transitionDuration.split(',')[0];
            transitionDelay = transitionDelay.split(',')[0];

            return (Number.parseFloat(transitionDuration) + Number.parseFloat(transitionDelay)) * 1000;
        }

        enableBackdrop(enabled = true) {
            const positionStyle = enabled ? null : 'static';
            this.shadowRoot.querySelectorAll('[role="dialog"] > .fixed')
                .forEach(element => element.style.position = positionStyle);
        }

        renderCurrency(currency) {
            this.#priceFormatter = new Intl.NumberFormat(undefined, {
                style: 'currency',
                currency: currency,
                currencyDisplay: 'narrowSymbol',
            });
            this.renderCartItems(this.cartItems);
        }

        /**
         * @param {Array} cartItems
         */
        renderCartItems(cartItems) {
            if (!Array.isArray(cartItems)) {
                throw new TypeError('cartItems must be an array');
            }

            const cartItemsElement = this.shadowRoot.getElementById('cart-items');
            const cartItemsEmptyElement = this.shadowRoot.getElementById('cart-items-empty');
            if (cartItems.length > 0) {
                cartItemsElement.innerHTML = cartItems.map(product => {
                    product.total_price = this.#priceFormatter.format(product.price * product.quantity);

                    return cartItemTemplateString(product);
                }).join('').trim();
                cartItemsEmptyElement.hidden = true;
                this.renderLabels(this.labels, cartItemsElement);
            } else {
                cartItemsElement.innerHTML = '';
                cartItemsEmptyElement.hidden = false;
            }

            this.shadowRoot.getElementById('subtotal').innerText = this.#priceFormatter.format(
                cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0)
            );

            const itemsCount = cartItems.reduce((sum, item) => sum + parseInt(item.quantity), 0);
            const toggleCount = this.shadowRoot.querySelector('[data-element="toggle.count"]');
            toggleCount.innerText = itemsCount;
            toggleCount.closest('button').hidden = (itemsCount <= 0);
            const submitButton = this.shadowRoot.querySelector('button[type="submit"]');
            if (itemsCount <= 0) {
                submitButton.setAttribute('disabled', '');
            } else {
                submitButton.removeAttribute('disabled');
            }
        }

        /**
         * @param {Object} labels
         * @param {ParentNode} [root]
         */
        renderLabels(labels, root = this.shadowRoot) {
            if (typeof labels !== 'object' || labels === null || Array.isArray(labels)) {
                throw new TypeError('labels must be an object');
            }

            const missingLabelSet = new Set();

            const textNodeWalker = document.createTreeWalker(root, NodeFilter.SHOW_TEXT, textNode => (
                textNode.textContent?.trim().length
                && textNode.parentElement?.translate
                && !textNode.parentElement.closest('script, style')
            ) ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_REJECT);
            const textElements = [];
            while (textNodeWalker.nextNode()) {
                textElements.push(textNodeWalker.currentNode.parentElement);
            }
            textElements.forEach(element => {
                const labelKey = element.dataset.originalTextContent || element.textContent.trim();
                const label = labels[labelKey];
                if (typeof label === 'string') {
                    element.dataset.originalTextContent = labelKey;
                    element.textContent = label;
                } else {
                    missingLabelSet.add(labelKey);
                }
            });
            root.querySelectorAll('[aria-label]').forEach(element => {
                const labelKey = element.dataset.originalAriaLabel || element.ariaLabel;
                const label = labels[labelKey];
                if (typeof label === 'string') {
                    element.dataset.originalAriaLabel = labelKey;
                    element.ariaLabel = label;
                } else {
                    missingLabelSet.add(labelKey);
                }
            });

            if (missingLabelSet.size > 0) {
                console.warn('Keys missing from labels attribute', missingLabelSet);
            }
        }

        #initTwHref() {
            const defaultTwUrl = new URL('/css/dist/tailwind.min.css', document.currentScript.src);
            this.twHref = defaultTwUrl.toString();
            this.#fetchLastModifiedTs(defaultTwUrl).then(ts => {
                if (this.twHref === defaultTwUrl.toString()) {
                    defaultTwUrl.searchParams.set('v', ts);
                    this.twHref = defaultTwUrl.toString();
                }
            }).catch((error) => console.warn(error));

            return new Promise((resolve, reject) => {
                const twEl = this.shadowRoot.getElementById('TailwindCssStylesheet');
                twEl.addEventListener('load', resolve);
                twEl.addEventListener('error', reject);
            });
        }

        async #fetchLastModifiedTs(url) {
            const response = await fetch(url, { method: 'HEAD', cache: 'no-store' });
            if (!response.ok) {
                throw new Error(`Failed to fetch Last-Modified of ${url}: ${response.status} ${response.statusText}`);
            }

            return (new Date(response.headers.get('Last-Modified'))).getTime().toString();
        }

        /**
         * Fix Tailwind CSS @property declarations not working in the Shadow DOM by registering them in the Light DOM.
         * @see https://github.com/tailwindlabs/tailwindcss/issues/15005
         */
        #fixTailwindCssCustomProperties() {
            const duplicates = [];
            let duplicatesErrorMessage = '';

            const styleSheet = this.shadowRoot.getElementById('TailwindCssStylesheet').sheet;
            [...styleSheet.cssRules]
                .filter(rule => rule instanceof CSSPropertyRule)
                .forEach(property => {
                    try {
                        window.CSS.registerProperty(property);
                    } catch (error) {
                        if (error.name === 'InvalidModificationError') {
                            duplicatesErrorMessage = error.toString();
                            duplicates.push(property);
                        } else {
                            throw error;
                        }
                    }
                });

            if (duplicates.length > 0) {
                console.warn(duplicatesErrorMessage, duplicates);
            }
        }

        #initAccentColorCssVars() {
            const computedStyle = window.getComputedStyle(this);
            if (!computedStyle.getPropertyValue('--accent-color')) {
                this.style.setProperty('--accent-color', '#009fff');
            }
            if (!computedStyle.getPropertyValue('--accent-color-hover')) {
                this.style.setProperty('--accent-color-hover', 'oklch(from var(--accent-color) calc(l * .80) c h)');
            }
        }
    });

    /**
     * @param {Object} product
     * @returns {string}
     */
    function cartItemTemplateString(product) {
        return `
<li data-key="${product.id}" class="flex py-6">
    <div class="size-24 shrink-0 overflow-hidden rounded-md border border-gray-200">
        <img src="${product.image_src}" alt="${product.image_alt}" class="size-full object-contain">
    </div>
    <div class="ml-4 flex flex-1 flex-col">
        <div translate="no">
            <div class="flex justify-between text-base font-medium text-gray-900">
                <h3>
                    <a href="${product.href}" data-element="lineItem.productTitle">${product.product_title}</a>
                </h3>
                <p class="ml-4" data-element="lineItem.totalPrice">${product.total_price}</p>
            </div>
            <p class="mt-1 text-sm text-gray-500" data-element="lineItem.variantTitle">${product.variant_title}</p>
        </div>
        <div class="flex flex-1 items-end justify-between text-sm" data-element="lineItem.quantityContainer">
            <div class="flex" data-element="lineItem.quantity">
                <button type="button" class="rounded-l-sm rounded-r-none border border-neutral-500 w-[26px] h-[30px] relative text-[18px]/[16px] text-neutral-600 text-center cursor-pointer" data-element="lineItem.quantityDecrement">
                    <svg class="size-[14px] absolute top-1/2 left-1/2 mt-[-6px] ml-[-7px]" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 16 16">
                        <path d="M4 7h8v2H4z"></path>
                    </svg>
                    <span class="sr-only">Reduce item quantity by one</span>
                </button>
                <input type="number" min="0" max="${product.max_quantity || ''}" name="lineItem[${product.id}][quantity]" value="${product.quantity}" class="border-y border-neutral-500 w-[45px] h-[30px] inline-block text-[16px] text-black text-center appearance-textfield" aria-label="Quantity" data-element="lineItem.quantityInput">
                <button type="button" class="rounded-r-sm rounded-l-none border border-neutral-500 w-[26px] h-[30px] relative text-[18px]/[16px] text-neutral-600 text-center cursor-pointer" data-element="lineItem.quantityIncrement">
                    <svg class="size-[14px] absolute top-1/2 left-1/2 mt-[-6px] ml-[-7px]" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 16 16">
                        <path d="M12 7H9V4H7v3H4v2h3v3h2V9h3z"></path>
                    </svg>
                    <span class="sr-only">Increase item quantity by one</span>
                </button>
            </div>
            <div class="flex">
                <button type="button" class="font-medium text-(--accent-color) hover:text-(--accent-color-hover)" data-element="lineItem.remove">Remove</button>
            </div>
        </div>
    </div>
</li>
`;
    }
})();
