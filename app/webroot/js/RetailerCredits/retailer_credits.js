var retailerCreditsConfig = {
    itemsOnPage: 10,
    totalItems: 10,
    currentPage: 1,
    updateCreditUrl: '',
    noBankAccountMessage: 'You must select a bank account.',
    paymentModalTitle: 'Make Payment',
    newTransactionModalTitle: 'New Transaction',
    newVoucherModalTitle: 'New Voucher'
};

$(function () {
    $('#pagination').pagination({
        items: retailerCreditsConfig.totalItems,
        itemsOnPage: retailerCreditsConfig.itemsOnPage,
        currentPage: retailerCreditsConfig.currentPage,
        cssStyle: 'light-theme',
        onPageClick: function (pageNumber) {
            $('#pageNumber').val(pageNumber);
            updateTable();
        },
        onInit: window.paginationInit,
    });

    $('.js-add-transaction').click(function (e) {
        e.preventDefault();
        $.get(this.href, function (message) {
            bootbox.dialog({
                size: 'large',
                title: retailerCreditsConfig.newTransactionModalTitle,
                message: message,
                buttons: {
                    Cancel: function () {},
                    OK: function () {
                        $('#RetailerCreditFormSubmit').click();
                        return false;
                    },
                },
            });
            $('#RetailerCreditCreditDate').datepicker({ "language": moment.locale(), orientation: 'bottom' });
        });
    });

    $('.js-add-voucher').click(function (e) {
        e.preventDefault();
        $.get(this.href, function (message) {
            bootbox.dialog({
                size: 'small',
                title: retailerCreditsConfig.newVoucherModalTitle,
                message: message,
                buttons: {
                    Cancel: function () {},
                    OK: function () {
                        $('#RetailerCreditVoucherFormSubmit').click();
                        return false;
                    },
                },
            });
        });
    });

    $('.js-credit-vouchers-link').click(function (e) {
        e.preventDefault();
        $.get(this.href, function (message) {
            bootbox.dialog({
                size: 'small',
                title: retailerCreditsConfig.newVoucherModalTitle,
                message: message,
                buttons: {
                    Cancel: function () {},
                    OK: function () {
                        $('#RetailerCreditVoucherFormSubmit').click();
                        return false;
                    },
                },
            });
        });
    });

    $('body')
        .on('submit', '#RetailerCreditAddForm', function () {
            var $form = $(this);
            var $inputs = $form.find('input');

            $inputs.each(function () {
                $(this).removeClass('error-class');
            });

            var $emptyInputs = $inputs.filter(function () {
                var value = $.trim($(this).val());
                return !value || Number(value) === 0;
            });
            if ($emptyInputs.length > 0) {
                $emptyInputs.each(function () {
                    $(this).addClass('error-class');
                });
                return false;
            }

            return true;
        })
        .on('blur', '#RetailerCreditAddForm input[type="number"]', function () {
            var $this = $(this);
            var value = Number($this.val()) || 0;
            $this.val(value.toFixed(2));
        });

    $('#locationId, #selectedStatus').on('change', function () {
        $('#retailerCreditForm').submit();
    });

    if (
        /Android|webOS|iPhone|iPad|iPod|BlackBerry/i.test(navigator.userAgent)
    ) {
        $('.selectpicker').selectpicker('mobile');
    } else {
        $('.selectpicker').selectpicker();
    }
    var $daterange = $('input.daterange');
    var $startDate = $daterange.filter('#startDate');
    var $endDate = $daterange.filter('#endDate');

    $daterange.daterangepicker(
        {
            startDate: $startDate.val(),
            endDate: $endDate.val(),
            alwaysShowCalendars: true,
            linkedCalendars: false,
            showDropdowns: true,
            autoApply: true,
            autoUpdateInput: false,
            ranges: {
                Today: [moment(), moment()],
                Yesterday: [
                    moment().subtract(1, 'days'),
                    moment().subtract(1, 'days'),
                ],
                'Last 7 Days': [moment().subtract(6, 'days'), moment()],
                'Last 30 Days': [moment().subtract(29, 'days'), moment()],
                'This Month': [
                    moment().startOf('month'),
                    moment().endOf('month'),
                ],
                'Last Month': [
                    moment().subtract(1, 'month').startOf('month'),
                    moment().subtract(1, 'month').endOf('month'),
                ],
            },
            locale: {
                format: 'YYYY-MM-DD',
            },
        },
        function (start, end) {
            $startDate.val(start.format('YYYY-MM-DD'));
            $endDate.val(end.format('YYYY-MM-DD'));

            $('#retailerCreditForm').submit();
        }
    );

    function preventBlur(e) {
        e.stopPropagation();
    }

    $('.js-due-date')
        .datepicker({ "language": moment.locale() })
        .on('hide', function () {
            saveChanges(this);
        })
        .on('blur', preventBlur)
        .on('changeDate', function (e) {
            $(this).off('blur', preventBlur);
            $(this).blur();
            $(this).on('blur', preventBlur);
        });

    $('.js-due-date, .js-invoice-number')
        .clickToEdit()
        .on('blur', function () {
            saveChanges(this);
        });

    function saveChanges(input) {
        var url = retailerCreditsConfig.updateCreditUrl;
        var $inputs = $(input).closest('tr').find(':input');
        var $status = $(input).closest('tr').find('.js-credit-status');
        var $error = $(input).closest('.help-block');
        $.ajax({
            type: 'POST',
            url: url,
            data: $inputs.serialize(),
            dataType: 'json',
            beforeSend: function () {
                $inputs.prop('disabled', true);
            },
            success: function () {
                $error.empty();
            },
            error: function (jqXHR) {
                if (jqXHR.status >= 500) {
                    $error.text(jqXHR.statusText);
                } else if (jqXHR.responseJSON) {
                    $error.text(jqXHR.responseJSON.message);
                }
            },
            complete: function () {
                $inputs.prop('disabled', false);
            },
        });
    }

    $(document).on('click', '.js-make-payment', function () {
        var url = $(this).data('url');
        var availableItems = $('#retailerCreditForm').find(
            'input.js-make-payment-check'
        );
        var selectedItems = availableItems.filter(':checked');
        if (selectedItems.length === 0) {
            availableItems.addClass('has-error');
            alert('You must select at least one invoice to make a payment.');
            return;
        }
        var data = $('#retailerCreditForm')
            .find('input.js-make-payment-check')
            .serialize();
        $.get(url, data, function (data) {
            bootbox
                .dialog({
                    size: 'large',
                    title: retailerCreditsConfig.paymentModalTitle,
                    message: data,
                    buttons: {
                        Cancel: function () {},
                        OK: function () {
                            var form = $('#RetailerCreditPaymentForm');
                            form.validate({
                                rules: {
                                    paymentMethod: {
                                        required: true,
                                    }
                                },
                                messages: {
                                    paymentMethod: {
                                        required: retailerCreditsConfig.noBankAccountMessage
                                    }
                                },
                                highlight: function(element) {
                                    $(element).closest('.form-group').addClass('has-error');
                                },
                                unhighlight: function(element) {
                                    $(element).closest('.form-group').removeClass('has-error');
                                },
                                errorElement: 'span',
                                errorClass: 'help-block',
                                errorPlacement: function(error, element) {
                                    if(element.parent('.input-group').length) {
                                        error.insertAfter(element.parent());
                                    } else {
                                        error.insertAfter(element);
                                    }
                                },
                                success: function(label) {
                                    label.text('');
                                }
                            });
                            $('.payment-amount').each(function() {
                                $(this).rules('add', {
                                    min: 1,
                                    messages: {
                                        min: "Must be greater than $1.00"
                                    }
                                });
                            });
                            if(form.valid()){
                                $('#RetailerCreditPaymentForm').submit();
                            } 
                            return false;
                        },
                    },
                })
                .on('shown.bs.modal', function () {
                    $('#RetailerCreditAddForm input[type="number"]').change();
                    $('.remove-account').on('click', function () {
                        BankAccount.removeAccount(this);
                    });
                });
        });
    });

    $('.js-paid.has-payment').tooltipster({
        content: 'Loading...',
        trigger: 'click',
        interactive: true,
        theme: 'tooltipster-payment-list',
        updateAnimation: 'fade',
        side: 'left',
        functionBefore: function (instance, helper) {
            var $origin = $(helper.origin);
            if ($origin.data('loaded') !== true) {
                var url = $origin.attr('data-url');
                var creditId = $origin
                    .closest('tr')
                    .find('td input.js-retailer-credit-id')
                    .val();
                $.get(url, function (data) {
                    instance.content($(data));
                    $origin.data('loaded', true);
                });

                return 'Loading...';
            }
        },
    });
});

function updateTable() {
    $('#RetailerCreditsIndex input').prop('disabled', true);
    $('form#retailerCreditForm').submit();
}

function shipearlySort(field) {
    var $field = $('#sortField');
    var $order = $('#sortOrder');

    var order = 'DESC';
    if (field === $field.val() && order === $order.val()) {
        order = 'ASC';
    }

    $field.val(field);
    $order.val(order);

    updateTable();
}
