var BankAccount = (function () {
    var bankAccount = {};

    bankAccount.config = {
        stripePublishableKey: '',
        bankAccountSetupTokenUrl: '',
        bankAccountAddUrl: '',
        bankAccountSetupToken: '',
        connectedAccount: '',
        removeAccountUrl: '',
        verifyModalTitle: 'Verify Bank Account',
        newModalTitle: 'New Account Details',
    }

    var stripeClient;

    function getStripeClient() {
        if (!stripeClient) {
            stripeClient = Stripe(bankAccount.config.stripePublishableKey, {
                apiVersion: '2020-08-27',
                betas: ['us_bank_account_beta_2'],
                stripeAccount: bankAccount.config.connectedAccount,
            });
        }
        return stripeClient;
    }

    $(document)
        .on('mouseup', '.js-add-bank-account', function () {
            $(this).removeClass('btn btn-primary');
            $.get(bankAccount.config.bankAccountAddUrl, function (message) {
                bootbox.dialog({
                    title: 'New Account Details',
                    message: message,
                    buttons: {
                        Cancel: {
                            className: 'btn-default add-account-input js-add-account-cancel',
                            callback: function () {},
                        },
                        OK: {
                            className: 'btn-primary add-account-input js-add-account-ok hidden',
                            callback: function () {
                                location.reload();
                            },
                        },
                    },
                });
            });
        })
        .on('click', '.remove-account', function () {
            bankAccount.removeAccount(this);
        });

    $('.js-verify-bank-account').on('click', function () {
        $.get($(this).attr('data-url'), function (message) {
            bootbox.dialog({
                size: 'small',
                title: 'Verify Bank Account',
                message: message,
                buttons: {
                    Cancel: function () {},
                    OK: function () {
                        $('#bank_account_verify_form').submit();
                        return false;
                    },
                },
            });
        });
    });

    $(document).on('change', '#country', function () {
        var country = $(this).find(':selected').val();
        $('.add-account-info').addClass('hidden');
        if (country !== '') {
            hideCountrySelect();
            disableInputs();
            $('#stripe-loading').removeClass('hidden');
            getSetupToken(country);
        }
    });

    function getSetupToken(country) {
        $.getJSON(bankAccount.config.bankAccountSetupTokenUrl, { country: country }, function (result) {
            $('#billing-email').text(result.billing_email);
            if (country === 'US') {
                getStripeClient()
                    .collectUsBankAccountForSetup(result.client_secret, {
                        billing_details: {
                            email: result.billing_email,
                            name: result.billing_name,
                        },
                    })
                    .then((result) => {
                        handleSetupIntentResult(result);
                    });
            } else if (country === 'CA') {
                getStripeClient()
                    .confirmAcssDebitSetup(result.client_secret, {
                        payment_method: {
                            billing_details: {
                                email: result.billing_email,
                                name: result.billing_name,
                            },
                        },
                    })
                    .then((result) => {
                        handleSetupIntentResult(result);
                    });
            }
        });
    }

    function handleSetupIntentResult(result) {
        showOk();
        hideInfo();
        enabledInputs();
        if (result.error) {
            $('#error-info').removeClass('hidden');
        } else if (result.setupIntent.status === 'requires_payment_method') {
            // Customer canceled the Connections modal.
            resetAddAccountForm();
        } else if (result.setupIntent.status === 'succeeded') {
            // Confirmation succeeded! The account is now saved.
            // Display a message to customer.
            $('#success-info').removeClass('hidden');
        } else if (result.setupIntent.status === 'requires_confirmation') {
            // We collected an account. Display mandate text
            // to the customer and confirm the intent once they accept
            // the mandate.
            showCancel();
            $('#stripe-loading').addClass('hidden');
            $('#mandate-info').removeClass('hidden');
            $('#accept-mandate').on('click', function () {
                $('#stripe-loading').removeClass('hidden');
                getStripeClient()
                    .confirmUsBankAccountSetup(result.setupIntent.client_secret)
                    .then((result) => {
                        handleSetupIntentResult(result);
                    });
            });
        } else if (result.setupIntent.next_action?.type === 'verify_with_microdeposits') {
            // The account needs to be verified via microdeposits.
            // Display a message to consumer with next steps (consumer waits for
            // microdeposits, then enters an amount on a page sent to them via email).
            $('#micro-deposit-info').removeClass('hidden');
        }
    }

    function showOk() {
        $('.js-add-account-ok').removeClass('hidden');
        $('.js-add-account-cancel').addClass('hidden');
    }

    function showCancel() {
        $('.js-add-account-cancel').removeClass('hidden');
        $('.js-add-account-ok').addClass('hidden');
    }

    function resetAddAccountForm() {
        $('#country').val('').change();
        enabledInputs();
        showCancel();
        showCountrySelect();
    }

    function hideInfo() {
        $('.add-account-info').addClass('hidden');
    }

    function hideCountrySelect() {
        $('#country-select').addClass('hidden');
    }
    function showCountrySelect() {
        $('#country-select').removeClass('hidden');
    }

    function disableInputs() {
        $('.add-account-input').prop('disabled', true).selectpicker('refresh');
    }
    function enabledInputs() {
        $('.add-account-input').prop('disabled', false).selectpicker('refresh');
    }
    bankAccount.removeAccount = function(target){
        var paymentMethodId = $(target).attr('data-id');
        $.post(bankAccount.config.removeAccountUrl, { paymentMethodId: paymentMethodId }, function (result) {
            if(result.success){
                var selectPicker = $("#paymentMethod");
                selectPicker.find('option[value=' + paymentMethodId + ']').remove();
                selectPicker.selectpicker('refresh');

            }
        }, 'json');
    }

    return bankAccount;
}($));
