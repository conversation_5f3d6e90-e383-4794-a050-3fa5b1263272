<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>

  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>amCharts Data Loader Example</title>
    <script src="http://www.amcharts.com/lib/3/amcharts.js"></script>
    <script src="http://www.amcharts.com/lib/3/serial.js"></script>
    <script src="../dataloader.min.js"></script>
    <style>
    body, html {
      font-family: Verdana;
      font-size: 12px;
    }
    #chartdiv {
      width: 100%;
      height: 500px;
    }
    </style>
    <script>
    var chart = AmCharts.makeChart("chartdiv", {
     "type": "serial",
     "dataLoader": {
        "url": "data/serial.csv",
        "format": "csv",
        "showCurtain": true,
        "showErrors": true,
        "async": true,
        "reload": 10,
        "timestamp": true,
        "delimiter": ",",
        "useColumnNames": true
     },
     "rotate": false,
     "marginTop": 10,
     "categoryField": "year",
     "categoryAxis": {
       "gridAlpha": 0.07,
       "axisColor": "#DADADA",
       "startOnAxis": false,
       "title": "Year",
       "guides": [{
         "category": "2001",
         "lineColor": "#CC0000",
         "lineAlpha": 1,
         "dashLength": 2,
         "inside": true,
         "labelRotation": 90,
         "label": "fines for speeding increased"
       }, {
         "category": "2007",
         "lineColor": "#CC0000",
         "lineAlpha": 1,
         "dashLength": 2,
         "inside": true,
         "labelRotation": 90,
         "label": "motorcycle fee introduced"
       }]
     },
     "valueAxes": [{
       "stackType": "regular",
       "gridAlpha": 0.07,
       "title": "Traffic incidents"
     }],
     "graphs": [{
       "id": "g1",
       "type": "column",
       "title": "Cars",
       "valueField": "cars",
       "bullet": "round",
       "lineAlpha": 0,
       "fillAlphas": 0.6
     }, {
       "id": "g2",
       "type": "column",
       "title": "Motorcycles",
       "valueField": "motorcycles",
       "lineAlpha": 0,
       "fillAlphas": 0.6
     }, {
       "id": "g3",
       "type": "column",
       "title": "Bicycles",
       "valueField": "bicycles",
       "lineAlpha": 0,
       "fillAlphas": 0.6
     }],
     "legend": {
       "position": "bottom",
       "valueText": "[[value]]",
       "valueWidth": 100,
       "valueAlign": "left",
       "equalWidths": false,
       "periodValueText": "total: [[value.sum]]"
     },
     "chartCursor": {
       "cursorAlpha": 0
     },
     "chartScrollbar": {
       "color": "FFFFFF"
     }

    });
    </script>
  </head>

  <body>
    <div id="chartdiv"></div>
  </body>

</html>