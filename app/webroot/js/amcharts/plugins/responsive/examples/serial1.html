<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>

  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>amCharts Responsive Example</title>
    <script src="http://www.amcharts.com/lib/3/amcharts.js"></script>
    <script src="http://www.amcharts.com/lib/3/serial.js"></script>
    <script src="../responsive.min.js"></script>
    <style>
    body, html {
        height: 100%;
        padding: 0;
        margin: 0;
    }
    </style>
    <script>
      var chartData = [{
        "year": 2000,
        "cars": 1587,
        "motorcycles": 650,
        "bicycles": 121
     }, {
        "year": 1995,
        "cars": 1567,
        "motorcycles": 683,
        "bicycles": 146
     }, {
        "year": 1996,
        "cars": 1617,
        "motorcycles": 691,
        "bicycles": 138
     }, {
        "year": 1997,
        "cars": 1630,
        "motorcycles": 642,
        "bicycles": 127
     }, {
        "year": 1998,
        "cars": 1660,
        "motorcycles": 699,
        "bicycles": 105
     }, {
        "year": 1999,
        "cars": 1683,
        "motorcycles": 721,
        "bicycles": 109
     }, {
        "year": 2000,
        "cars": 1691,
        "motorcycles": 737,
        "bicycles": 112
     }, {
        "year": 2001,
        "cars": 1298,
        "motorcycles": 680,
        "bicycles": 101
     }, {
        "year": 2002,
        "cars": 1275,
        "motorcycles": 664,
        "bicycles": 97
     }, {
        "year": 2003,
        "cars": 1246,
        "motorcycles": 648,
        "bicycles": 93
     }, {
        "year": 2004,
        "cars": 1218,
        "motorcycles": 637,
        "bicycles": 101
     }, {
        "year": 2005,
        "cars": 1213,
        "motorcycles": 633,
        "bicycles": 87
     }, {
        "year": 2006,
        "cars": 1199,
        "motorcycles": 621,
        "bicycles": 79
     }, {
        "year": 2007,
        "cars": 1110,
        "motorcycles": 210,
        "bicycles": 81
     }, {
        "year": 2008,
        "cars": 1165,
        "motorcycles": 232,
        "bicycles": 75
     }, {
        "year": 2009,
        "cars": 1145,
        "motorcycles": 219,
        "bicycles": 88
     }, {
        "year": 2010,
        "cars": 1163,
        "motorcycles": 201,
        "bicycles": 82
     }, {
        "year": 2011,
        "cars": 1180,
        "motorcycles": 285,
        "bicycles": 87
     }, {
        "year": 2012,
        "cars": 1159,
        "motorcycles": 277,
        "bicycles": 71
     }];

     AmCharts.makeChart("chartdiv", {
       "type": "serial",
       "dataProvider": chartData,
       "rotate": false,
       "marginTop": 10,
       "categoryField": "year",
       "categoryAxis": {
         "gridAlpha": 0.07,
         "axisColor": "#DADADA",
         "startOnAxis": true,
         "title": "Year",
         "guides": [{
           "category": "2001",
           "lineColor": "#CC0000",
           "lineAlpha": 1,
           "dashLength": 2,
           "inside": true,
           "labelRotation": 90,
           "label": "fines for speeding increased"
         }, {
           "category": "2007",
           "lineColor": "#CC0000",
           "lineAlpha": 1,
           "dashLength": 2,
           "inside": true,
           "labelRotation": 90,
           "label": "motorcycle fee introduced"
         }]
       },
       "valueAxes": [{
         "stackType": "regular",
         "gridAlpha": 0.07,
         "title": "Traffic incidents"
       }],
       "graphs": [{
         "id": "g1",
         "type": "column",
         "title": "Cars",
         "valueField": "cars",
         "bullet": "round",
         "lineAlpha": 0,
         "fillAlphas": 0.6,
         "balloonText": "<img src='images/car.png' style='vertical-align:bottom; margin-right: 10px; width:28px; height:21px;'><span style='font-size:14px; color:#000000;'><b>[[value]]</b></span>"
       }, {
         "id": "g2",
         "type": "column",
         "title": "Motorcycles",
         "valueField": "motorcycles",
         "lineAlpha": 0,
         "fillAlphas": 0.6,
         "balloonText": "<img src='images/motorcycle.png' style='vertical-align:bottom; margin-right: 10px; width:28px; height:21px;'><span style='font-size:14px; color:#000000;'><b>[[value]]</b></span>"
       }, {
         "id": "g3",
         "type": "column",
         "title": "Bicycles",
         "valueField": "bicycles",
         "lineAlpha": 0,
         "fillAlphas": 0.6,
         "balloonText": "<img src='images/bicycle.png' style='vertical-align:bottom; margin-right: 10px; width:28px; height:21px;'><span style='font-size:14px; color:#000000;'><b>[[value]]</b></span>"
       }],
       "legend": {
         "position": "bottom",
         "valueText": "[[value]]",
         "valueWidth": 100,
         "valueAlign": "left",
         "equalWidths": false,
         "periodValueText": "total: [[value.sum]]"
       },
       "chartCursor": {
         "cursorAlpha": 0
       },
       "chartScrollbar": {
         "color": "FFFFFF"
       },
      "responsive": {
        "enabled": true
      }

     });

    </script>
  </head>

  <body>
    <div id="chartdiv" style="width: 100%; height: 100%;"></div>
  </body>

</html>