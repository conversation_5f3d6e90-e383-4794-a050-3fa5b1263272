/*!
 * Bootstrap YouTube Popup Player Plugin
 * http://lab.abhinayrathore.com/bootstrap-youtube/
 * https://github.com/abhinayrathore/Bootstrap-Youtube-Popup-Player-Plugin
 */
(function(g){var j=null,b=null,h=null,o=null,f=5;var d={init:function(p){p=g.extend({},g.fn.YouTubeModal.defaults,p);if(j==null){j=g('<div class="modal fade '+p.cssClass+'" id="YouTubeModal" role="dialog" aria-hidden="true">');var q='<div class="modal-dialog" id="YouTubeModalDialog"><div class="modal-content" id="YouTubeModalContent"><div class="modal-header"><button type="button" class="close" data-dismiss="modal">&times;</button><h4 class="modal-title" id="YouTubeModalTitle"></h4></div><div class="modal-body" id="YouTubeModalBody" style="padding:0;"></div></div></div>';j.html(q).hide().appendTo("body");b=g("#YouTubeModalDialog");h=g("#YouTubeModalTitle");o=g("#YouTubeModalBody");j.modal({show:false}).on("hide.bs.modal",m)}return this.each(function(){var s=g(this);var r=s.data("YouTube");if(!r){s.data("YouTube",{target:s});g(s).bind("click.YouTubeModal",function(){var w=p.youtubeId;if(g.trim(w)==""&&s.is("a")){w=i(s.attr("href"))}if(g.trim(w)==""||w===false){w=s.attr(p.idAttribute)}var t=g.trim(p.title);if(t==""){if(p.useYouTubeTitle){c(w)}else{t=s.attr("title")}}if(t){n(t)}e(p.width);var v=l(w,p);var u=a(v,p.width,p.height);k(u);j.modal("show");return false})}})},destroy:function(){return this.each(function(){g(this).unbind(".YouTubeModal").removeData("YouTube")})}};function n(p){h.html(g.trim(p))}function k(p){o.html(p)}function m(){n("");k("")}function e(p){b.css({width:p+(f*2)})}function l(q,p){return["//www.youtube.com/embed/",q,"?rel=0&showsearch=0&autohide=",p.autohide,"&autoplay=",p.autoplay,"&controls=",p.controls,"&fs=",p.fs,"&loop=",p.loop,"&showinfo=",p.showinfo,"&color=",p.color,"&theme=",p.theme,"&wmode=transparent"].join("")}function a(q,r,p){return['<iframe title="YouTube video player" width="',r,'" height="',p,'" ','style="margin:0; padding:0; box-sizing:border-box; border:0; -webkit-border-radius:5px; -moz-border-radius:5px; border-radius:5px; margin:',(f-1),'px;" ','src="',q,'" frameborder="0" allowfullscreen seamless></iframe>'].join("")}function c(q){var p=["https://gdata.youtube.com/feeds/api/videos/",q,"?v=2&alt=json"].join("");g.ajax({url:p,dataType:"jsonp",cache:true,success:function(r){n(r.entry.title.$t)}})}function i(p){var r=/^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=)([^#\&\?]*).*/;var q=p.match(r);if(q&&q[2].length==11){return q[2]}else{return false}}g.fn.YouTubeModal=function(p){if(d[p]){return d[p].apply(this,Array.prototype.slice.call(arguments,1))}else{if(typeof p==="object"||!p){return d.init.apply(this,arguments)}else{g.error("Method "+p+" does not exist on Bootstrap.YouTubeModal")}}};g.fn.YouTubeModal.defaults={youtubeId:"",title:"",useYouTubeTitle:true,idAttribute:"rel",cssClass:"YouTubeModal",width:640,height:480,autohide:2,autoplay:1,color:"red",controls:1,fs:1,loop:0,showinfo:0,theme:"light"}})(jQuery);
