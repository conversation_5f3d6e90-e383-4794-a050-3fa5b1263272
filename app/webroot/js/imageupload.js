$(function(){
	var btnUpload=$('#upload');
	var status=$('#status');
	var thumb = $('img#image');	
	var allowedlimit = $('#allowedlimit').val();
	var uploadpath = $('#uploadpath').val();
	new AjaxUpload(btnUpload, {
		action: 'upload_image.php?path='+uploadpath,
		name: 'uploadfile',
		onSubmit: function(file, ext){
			if (! (ext && /^(jpg|png|jpeg|gif)$/.test(ext))){ 				
				status.text('Upload Only Images!');// other extensions not allowed 
				return false;
			}
			status.text('Uploading...');
		},
		onComplete: function(file, response){
			response2 = response.split('|');
			status.text(''); //On completion clear the status	
			if(response2[0]==="success"){ //Add uploaded file to list
				thumb.load(function(){
				
				thumb.unbind();
			});
			thumb.attr('src', 'uploads/photos/'+response2[1]);		
			

				if(allowedlimit == '1') var chtml = '';
				else var chtml = $('#files').html();
				$('#files').html(chtml+'<p><img  src="images/icons/actionInactive.gif" id="remove" alt="eee" rel="'+response2[1]+'" /><input name="allfiles[]" type="text" readonly value="'+response2[1]+'"></p>'); 
				
			} else{
				$('<li></li>').appendTo('#files').text(file).addClass('error');
			}
		},
	});
});

