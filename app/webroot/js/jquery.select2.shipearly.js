$(function () {
    const select2Function = $.fn.select2;

    //shim to cover selectpicker uses
    $.fn.selectpicker = function (options) {
        return this.select2(options);
    };

    /**
     * Placeholder for ShipearlyMultipleSelectionAdapter
     * Inserts it's own default placeholder if not placeholder was provided
     */
    $.fn.select2.amd.define("ShipearlyMultiplePlaceholder", [
        "select2/selection/placeholder",
    ],
        function (Placeholder) {
            // Wrap Placeholder.call() to prevent modifications here from reaching the parent
            function ShipearlyMultiplePlaceholder(decorated, $element, options) {
                Placeholder.call(this, decorated, $element, options);
            }

            ShipearlyMultiplePlaceholder.prototype.createPlaceholder = Placeholder.prototype.createPlaceholder;
            ShipearlyMultiplePlaceholder.prototype.update = Placeholder.prototype.update;

            // Insert a default placeholder if there is no configured option provided
            ShipearlyMultiplePlaceholder.prototype.normalizePlaceholder = function (_, placeholder) {
                if (placeholder === undefined) {
                    placeholder = NOTHING_SELECTED;
                }
                return Placeholder.prototype.normalizePlaceholder.call(this, _, placeholder);
            };

            return ShipearlyMultiplePlaceholder;
        }
    );

    /**
     * A multiselect that behaves more like selectpicker()'s multiselect
     */
    $.fn.select2.amd.define("ShipearlyMultipleSelectionAdapter", [
        "select2/utils",
        "select2/selection/multiple",
        "select2/selection/single",
        "select2/selection/eventRelay",
        "ShipearlyMultiplePlaceholder",
    ],
        function (Utils, MultipleSelection, SingleSelection, EventRelay, ShipearlyMultiplePlaceholder) {
            function ShipearlyMultipleSelectionAdapter($element, options) {
                MultipleSelection.prototype.constructor.call(this, $element, options);
            }

            Utils.Extend(ShipearlyMultipleSelectionAdapter, MultipleSelection);

            ShipearlyMultipleSelectionAdapter.prototype.render = SingleSelection.prototype.render;
            ShipearlyMultipleSelectionAdapter.prototype.selectionContainer = SingleSelection.prototype.selectionContainer;


            ShipearlyMultipleSelectionAdapter.prototype.display = function (selection, container) {
                var template = this.options.get('templateSelection');
                var escapeMarkup = this.options.get('escapeMarkup');

                return escapeMarkup(template(selection, container));
            };

            ShipearlyMultipleSelectionAdapter.prototype.update = function (data) {
                this.clear();

                if (data.length === 0) {
                    return;
                }
                let $rendered = this.$selection.find(".select2-selection__rendered");

                // aggregate selections into comma separated list for display
                let selection = {
                    text: data.map((x) => x.text).join(', ')
                };
                let formatted = this.display(selection, $rendered);
                $rendered.empty().append(formatted);
                $rendered.prop("title", selection.text);
            };

            ShipearlyMultipleSelectionAdapter = Utils.Decorate(ShipearlyMultipleSelectionAdapter, EventRelay);
            ShipearlyMultipleSelectionAdapter = Utils.Decorate(ShipearlyMultipleSelectionAdapter, ShipearlyMultiplePlaceholder);
            return ShipearlyMultipleSelectionAdapter;
        }
    );

    $.fn.select2.amd.define("ShipearlyMultipleDropdownAdapter", [
        "select2/utils",
        "select2/dropdown",
        "select2/dropdown/attachBody",
        "select2/dropdown/search",
        "select2/dropdown/minimumResultsForSearch"
    ],
        function (Utils, Dropdown, AttachBody, Search, MinimumResultsForSearch) {
            var ShipearlyMultipleDropdownAdapter = Dropdown;

            ShipearlyMultipleDropdownAdapter = Utils.Decorate(ShipearlyMultipleDropdownAdapter, Search);
            ShipearlyMultipleDropdownAdapter = Utils.Decorate(ShipearlyMultipleDropdownAdapter, MinimumResultsForSearch);
            ShipearlyMultipleDropdownAdapter = Utils.Decorate(ShipearlyMultipleDropdownAdapter, AttachBody);

            return ShipearlyMultipleDropdownAdapter;
        }
    );

    $.fn.select2.amd.define("ShipearlyResultsAdapter", [
        "select2/utils",
        "select2/results",
    ],
        function (Utils, Results) {
            function ShipearlyResultsAdapter($element, options, dataAdapter) {
                Results.prototype.constructor.call(this, $element, options, dataAdapter);
            }

            Utils.Extend(ShipearlyResultsAdapter, Results);

            ShipearlyResultsAdapter.prototype.option = function (data) {
                data.text = $(data.element).data("content") || data.text;
                return Results.prototype.option.call(this, data);
            };

            return ShipearlyResultsAdapter;
        }
    );

    /**
     * Resolve configurations
     * Handle converting selectpicker config options to select2 equivalents
     * Handle setting the selection and dropdown adapters for multi select elements
     */
    function resolveConfiguration($element, config) {
        const configResolvers = [
            {
                func: selectpickerToSelect2,
                args: {
                    selectpickerName: "noneSelectedText",
                    select2Name: "placeholder",
                }
            },
            {
                func: selectpickerToSelect2,
                args: {
                    selectpickerName: "liveSearch",
                    select2Name: "minimumResultsForSearch",
                    toSelect2: (value) => value ? 0 : Infinity,
                }
            },
            {
                func: resolveDropdownAdapter,
            },
            {
                func: resolveSelectionAdapter,
            },
            {
                func: resolveDropdownParent,
            }
        ];

        configResolvers.forEach((resolver) => resolver.func($element, config, resolver.args || {}));
    }

    function resolveDropdownParent($element, config) {
        $key = 'dropdownParent';
        if (!($element.attr(toDataAttributeName($key)) || config[$key])) {
            config[$key] = $element.parent();
        }
    }

    function resolveSelectionAdapter($element, config) {
        if ($element.attr("multiple") || config.multiple) {
            config.selectionAdapter = select2Function.amd.require("ShipearlyMultipleSelectionAdapter");
        }

        return;
    }

    function resolveDropdownAdapter($element, config) {
        if ($element.attr("multiple") || config.multiple) {
            config.dropdownAdapter = select2Function.amd.require("ShipearlyMultipleDropdownAdapter");
        }

        return;
    }

    /**
     * convert old selectpicker options to select2 alternative
     * inspects both the provided config object and the elements data-* attributes
     */
    function selectpickerToSelect2($element, config, args) {
        migrateAttributeOption($element, args);
        migrateConfigOption(config, args);
    }

    function defaultMigration(value) {
        return value;
    }

    function migrateAttributeOption($element, optionMigration) {
        const migrationFunction = optionMigration.toSelect2 || defaultMigration;
        const sourceOptionName = toDataAttributeName(optionMigration.selectpickerName);
        const targetOptionName = toDataAttributeName(optionMigration.select2Name);
        const optionValue = $element.attr(sourceOptionName);
        if (optionValue) {
            $element.attr(targetOptionName, migrationFunction(optionValue));
            $element.removeAttr(sourceOptionName);
        }
    }

    function migrateConfigOption(config, optionMigration) {
        const migrationFunction = optionMigration.toSelect2 || defaultMigration;
        if (config.hasOwnProperty(optionMigration.selectpickerName)) {
            config[optionMigration.select2Name] = migrationFunction(config[optionMigration.selectpickerName]);
            delete config[optionMigration.selectpickerName];
        }
    }

    const toDataAttributeName = (str) => 'data-' + kebabize(str);

    const kebabize = (str) => str.replace(/[A-Z]+(?![a-z])|[A-Z]/g, ($, ofs) => (ofs ? "-" : "") + $.toLowerCase());

    const templateSelection = function (selection) {
        const template = (text, textClass) => `<span class="${textClass}">${text}</span>`;
        var textClass = "";
        if (selection.id == "") {
            textClass = "text-muted";
        }

        return $(template(selection.text, textClass));
    };

    const templateResult = function (result, container) {
        const template = (text, textClass, iconClass) => `
<div class="d-flex align-items-center flex-nowrap">
<div class"${textClass}">${text}</div><div class="${iconClass} ms-auto invisible"></div>
</div>
`;
        var textClass = "";
        var iconClass = "fas fa-check";

        if (result.id === "") {
            textClass = "text-muted";
        }

        return $(template(result.text, textClass, iconClass));
    };

    $.fn.select2.defaults.set("theme", "bootstrap-5");
    // hide search by default
    $.fn.select2.defaults.set("minimumResultsForSearch", Infinity);
    // width:100% matches the bootstrap 5 .form-control width for consistency
    $.fn.select2.defaults.set("width", "100%");
    $.fn.select2.defaults.set("templateSelection", templateSelection);
    $.fn.select2.defaults.set("templateResult", templateResult);
    $.fn.select2.defaults.set("resultsAdapter", $.fn.select2.amd.require("ShipearlyResultsAdapter"));

    /**
     * Wrap the select2 jquery extension to:
     * resolve configuration,
     * intercept and redirect selectpicker method calls to select2 equivalents
     * generally recreate legacy selectpicker behaviour
     */
    $.fn.select2 = function (options) {
        options = options || {};
        options = options == "mobile" ? {} : options;
        const $selectInputs = this.filter("select");
        if (options == "refresh") {
            return $selectInputs;
        }

        // https://stackoverflow.com/a/55440561
        $selectInputs.on('select2:open', function (e) {
            const evt = "scroll.select2";
            $(e.target).parents().off(evt);
            $(window).off(evt);
        });
        if (typeof options === "object") {
            options.dropdownPosition = 'below';
            $selectInputs.each((key, element) => {
                // create a new options instance for each element 
                // so that resolveConfiguration() only affects current element
                const instanceOptions = Object.assign({}, options);
                resolveConfiguration($(element), instanceOptions);
                select2Function.call($(element), instanceOptions);
                resolveWidth($(element));
            });
            return;
        }

        return select2Function.call($selectInputs, options);
    };

    //resolves the width of the select button to be as wide as the largest option.
    function resolveWidth($element) {
        const select2 = $element.data('select2');
        if (select2.options.get('width') === 'auto' || select2.options.get('width').includes('%')) {
            // collect settings that we will override
            const oldDropdownAutoWidth = select2.options.get('dropdownAutoWidth');
            const oldFocus = select2.$selection[0].focus;

            // unset focus to prevent screen from scrolling to input on close
            select2.$selection[0].focus = false;
            select2.options.set('dropdownAutoWidth', true);

            // temporarily open the dropdown to measure its width with rendered content
            select2.toggleDropdown();
            const selectionWidth = select2.$container.outerWidth(false);
            const dropdownWidth = select2.$dropdown.outerWidth(false);
            select2.toggleDropdown();
            select2.trigger('blur');

            const paddingWidth = 44; // x axis padding inside the select2 input element + border width
            select2.$selection.children('.select2-selection__rendered').css("min-width", Math.max(selectionWidth, dropdownWidth) - paddingWidth + 'px');

            // reset overridden settings
            select2.options.set('dropdownAutoWidth', oldDropdownAutoWidth);
            select2.$selection[0].focus = oldFocus;
        }
    }
});
