jQuery.validator.setDefaults({
	errorPlacement: function(error,element) {
		element.closest('.form-group').find('.help-block').html(error.text());
	}
});

$(document).ready(function() {
	$('#login').validate({
		submitHandler: function(form) {
		$("#resetsubmit").attr("disabled", "true");
			form.submit();
		},
		rules: {
			email: {
				required: true,
				email: true
			},
			password: {
				required: true,
				//minlength: 8
			}
		},
		messages: {
			email: {
				required: ENTER_USER,
				email: VALID_EMAIL
			},
			password: {
				required: ENTER_PASS,
				//minlength: PASS_MIN
			}
		}
	});
	
	$('#forgot_password').validate({
		submitHandler: function(form) {
		$("#resetsubmit").attr("disabled", "true");
			form.submit();
		},
		rules: {
			email: {
				required: true,
				email: true
			}
		},
		messages: {
			email: {
				required: ENTER_EMAIL,
				email: VALID_EMAIL
			}
		}
	});
});
