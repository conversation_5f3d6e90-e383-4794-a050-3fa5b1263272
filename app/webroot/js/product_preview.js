jQuery.validator.setDefaults({
	errorPlacement: function(error,element) {
		element.closest('.form-group').find('.help-block').html(error.text());
	}
});

$.validator.addMethod('maxGreaterThanMin', function() {
	var minQuantity = parseInt($('#b2b_min_order_quantity').val());
	var maxQuantity = parseInt($('#b2b_max_order_quantity').val());
	if (isNaN(minQuantity) || isNaN(maxQuantity) || (minQuantity <= maxQuantity)) {
		return true;
	}
	return false;
}, MAX_LESS_THAN_MIN);

$(document).ready(function() {
	$('#product_update_form').validate({
		submitHandler: function(form) {
		$("#update_product").attr("disabled", "true");
			form.submit();
		},
		rules: {
			invoice_amount: {
				required: true
			},
			min_order_qty: {
				required: true
			},
			min_shipping: {
				required: true
			},
			product_upc: {
				required: false
			},
			product_partno: {
				required: true
			},
			product_cat: {
				required: true
			},
			b2b_max_order_quantity:{
				maxGreaterThanMin: true
			},
			b2b_min_order_quantity:{
				maxGreaterThanMin: true
			}
		},
		messages: {
			invoice_amount: {
				required: ENTER_INVOICE_AMT
			},
			min_order_qty: {
				required: ENTER_MIN_QTY
			},
			min_shipping: {
				required: ENTER_MIN_SHIPPING
			},
			product_upc: {
				required: ENTER_UPC
			},
			product_partno: {
				required: ENTER_PARTNO
			},
			product_cat: {
				required: ENTER_PRO_CAT
			},
			b2b_max_order_quantity: {
				maxGreaterThanMin: MAX_LESS_THAN_MIN
			},
			b2b_min_order_quantity: {
				maxGreaterThanMin: MAX_LESS_THAN_MIN
			}
		}
	});
	$(".msg_panel .close").on('click', function(){
	    $(this).parent().hide();
	});
});
