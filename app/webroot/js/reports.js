var chartoptions = {
	"type": "serial",
	"theme": "light",
	"marginRight": 70,
	"valueAxes": [{
		"axisAlpha": 0,
		"position": "left",
		"title": ""
	}],
	"data": [],
	"startDuration": 1,
	"graphs": [{
		"balloonText": "<b>[[category]] <br/> [[value]]</b>",
		"fillColorsField": "color",
		"fillAlphas": 0.9,
		"lineAlpha": 0.0,
		"type": "column",
		"valueField": ""
	}],
	"chartCursor": {
		"categoryBalloonEnabled": false,
		"cursorAlpha": 0,
		"zoomable": true
	},
	"categoryField": "country",
	"categoryAxis": {
		"gridPosition": "start",
		"labelRotation": 45
	},
	"export": {
		"enabled": true
	}
};

var data = {};

var tableoptions = {
	"data": {},
	"columns": {},
	"columndefs": {
		"numClass": "",
		"cols": [],
		"cols_datatype": [],
		"hyperlink_cols": [],
		"hyperlink_url": []
	},
	"order": [[]],
	"base_url": "",
	"currencyCode":""
};
var _Instance;
var col_num = [];
var table = "";
var Reports = function(container) {
	this.container = container;
	_Instance = this;
	this.col_num = [];
	this.table = "";
};

$.extend(Reports.prototype, {
	_container: function() {
		return this.container;
	},

	chartOptions: function() {
		return chartoptions;
	},

	setChartOption: function(fieldoptions) {
		if (fieldoptions.startDuration) {
			chartoptions.startDuration = fieldoptions.startDuration;
		}
		if (fieldoptions.valueAxes.title) {
			chartoptions.valueAxes[0].title = fieldoptions.valueAxes.title;
		}
		if (fieldoptions.data) {
			chartoptions.data = fieldoptions.data;
		}
		if (fieldoptions.graphs) {
			chartoptions.graphs[0].valueField = fieldoptions.graphs.valueField;
		}
		if (fieldoptions.categoryField) {
			chartoptions.categoryField = fieldoptions.categoryField;
		}
	},

	drawChart: function() {
		var chart = AmCharts.makeChart("chartdiv", {
			"type": "serial",
			"theme": "light",
			"marginRight": 70,
			"dataProvider": chartoptions.data,
			"valueAxes": [{
				"axisAlpha": 0,
				"position": "left",
				"title": chartoptions.valueAxes[0].title
			}],
			"startDuration": chartoptions.startDuration,
			"graphs": [{
				"balloonText": "<b>[[category]] <br/> [[value]]</b>",
				"fillColorsField": "color",
				"fillAlphas": 0.9,
				"lineAlpha": 0.0,
				"type": "column",
				"valueField": chartoptions.graphs[0].valueField,
				"balloonFunction": function(graphDataItem, graph) {
					var label = graphDataItem.dataContext.cat_label
					var value = chartoptions.graphs[0].valueField == "count" ? graphDataItem.values.value : _Instance.formatNumberSeperator('2', '.', ',', graphDataItem.values.value);

					return "<b>" + label + "<br>" + value + "</b>";
				}
			}],
			"chartCursor": {
				"categoryBalloonEnabled": false,
				"cursorAlpha": 0,
				"zoomable": true
			},
			"categoryField": chartoptions.categoryField,
			"categoryAxis": {
				"gridPosition": "start",
				"labelRotation": 0,
				"gridAlpha": 0,
				"autoGridCount": true,
				"autoWrap": true
			},
			"export": {
				"enabled": true
			}
		});
	},

	setTableOption: function(fieldoptions) {
		if (fieldoptions.data) {
			tableoptions.data = fieldoptions.data;
		}
		if (fieldoptions.columns) {
			tableoptions.columns = fieldoptions.columns;
		}
		if (fieldoptions.columndefs) {
			tableoptions.columndefs = fieldoptions.columndefs;
		}
	},

	drawTable: function() {
		//Draw Table
		var table_footer = "";
		if (tableoptions.columns.length > 0) {
			var col_td = "";
			for (var i = 0; i < tableoptions.columns.length; i++) {
				col_td += "<td>" + ((i == 0) ? "Total" : "") + "</td>";
			}
			table_footer = "<tfoot role='row' class='tablefooter'><tr>" + col_td + "</tr></tfoot>";
		}
		$('#table').append(table_footer);

		_Instance.table = $('#table').on('error.dt', function(e, settings, techNote, message) {
			console.log('An error has been reported by DataTables: ', message);
		}).dataTable({
			"data": tableoptions.data,
			"paging": false,
			"searching": false,
			"bInfo": false,
			"columns": tableoptions.columns,
			"aoColumnDefs": [
				{
					"mRender": function(data, type, row) {},
					"aTargets": col_num
				},
				{
					"sClass": tableoptions.columndefs.numClass,
					"aTargets": _Instance.selectFloatColumns(tableoptions.columndefs.cols, tableoptions.columndefs.cols_datatype, "float")
				},
				{
					"sClass": "numericInt",
					"aTargets": _Instance.selectFloatColumns(tableoptions.columndefs.cols, tableoptions.columndefs.cols_datatype, "int")
				}
			],
			"bRetrieve": true,
			"order": [],
			"initComplete": function(settings, json) {
				_Instance.tableComplete(settings, this.api(settings));
			},
			"drawCallback": function(settings) {
				_Instance.exportCSV(settings, this.api(settings));
			},
			"footerCallback": function(row, data, start, end, display) {
				var api = this.api();
				_Instance.tableFooter(row, data, start, end, display, api);
			},
			"fnRowCallback": function(nRow, aData, iDisplayIndex) {
				tableoptions.columndefs.hyperlink_cols.forEach(function(value, index) {
					var col_data;
					if (tableoptions.columndefs.hyperlinkvalues == undefined) {
						col_data = "<a href='" + tableoptions.base_url + tableoptions.columndefs.hyperlink_url[value] + aData[value] + "' >" + aData[value] + "</a>";
						$('td:eq(' + value + ')', nRow).html(col_data);
					}
					else {
						var url = tableoptions.columndefs.hyperlink_url[value].replace("{id}", tableoptions.columndefs.hyperlinkvalues[iDisplayIndex]);
						col_data = "<a href='" + tableoptions.base_url + url + "' >" + aData[value] + "</a>";
						$('td:eq(' + value + ')', nRow).html(col_data);
					}
				})
			}
		});
	},

	selectFloatColumns: function(column_list, column_types, type) {
		var data = [];
		for (var i = 0; i < column_list.length; i++) {
			if (column_types[i] == type) {
				data.push(column_list[i]);
			}
		}
		return data;
	},

	reDrawTable: function() {
		$('#table').dataTable().fnClearTable();
		if (typeof(tableoptions.data) !== 'undefined' && tableoptions.data.length > 0) {
			$('#table').dataTable().fnAddData(tableoptions.data);
		}
	},

	tableComplete: function(settings, api) {
		var rows = api.rows().data();
	},

	exportCSV: function(settings, api) {
		var columnnames = tableoptions.columns.map(function(column) {
			return column.title;
		});
		var columnfooter = tableoptions.columns.map(function(column, index) {
			return '"' + $('tfoot').find('tr').eq(0).find('td').eq(index).html() + '"';
		});

		var rows = api.rows().data();

		var contentParts = [];
		contentParts.push(columnnames.join(","));
		for (var i = 0; i < rows.length; i++) {
			var rowParts = rows[i].map(function(column) {
				return '"' + String(column).replace(/"/g, '""') + '"';
			});
			contentParts.push(rowParts.join(","));
		}
		contentParts.push(columnfooter.join(","));

		$('#export_report')
			.attr("href", "data:text/csv;charset=utf-8," + encodeURIComponent(contentParts.join("\n")))
			.attr("download", tableoptions.file_name);
		return false;
	},

	tableFooter: function(row, data, start, end, display, api) {
		tableoptions.columndefs.cols.forEach(function(colIndex, i) {
			var total = 0;
			var count = 0;
			var tdclass = "";
			var colData = api.column(colIndex).data();

			for (var j = 0; j < colData.length; j++) {
				if (colData[j] === undefined || colData[j] === null || colData[j] === '') {
					continue;
				}
				var strVal = colData[j].toString().replace(',', '');
				if (!isNaN(strVal) && strVal.trim() !== '') {
					var parsed = parseFloat(strVal);
					if (!isNaN(parsed)) {
						total += parsed;
						count++;
					}
				}
			}

			var columnConfig = tableoptions.columns[colIndex] || {};
			var isAverage = columnConfig.average === true;

			var result = isAverage && count > 0 ? (total / count) : total;
			result = parseFloat(result.toFixed(2));

			if (tableoptions.columndefs.cols_datatype[i] === "float") {
				result = _Instance.formatCurrency(result.toFixed(2), tableoptions.currencyCode);
				tdclass = 'numericCol';
			} else if (tableoptions.columndefs.cols_datatype[i] === "int") {
				tdclass = 'numericInt';
			}
			$(api.column(colIndex).footer()).html(result).attr('class', tdclass);
		});
	},

	formatCurrency: function(total, currencyCode) {
		var neg = false;
		if (total < 0) {
			neg = true;
			total = Math.abs(total);
		}
		return currencyCode + (neg ? "-$" : '$') + parseFloat(total, 10).toFixed(2).replace(/(\d)(?=(\d{3})+\.)/g, "$1,").toString();
	},

	formatNumberSeperator: function(fractionDigits, decimal, separator, total) {
		fractionDigits = isNaN(fractionDigits = Math.abs(fractionDigits)) ? 2 : fractionDigits;
		decimal = typeof(decimal) === "undefined" ? "." : decimal;
		separator = typeof(separator) === "undefined" ? "," : separator;
		var number = total;
		var neg = number < 0 ? "-" : "";
		var wholePart = parseInt(number = Math.abs(+number || 0).toFixed(fractionDigits)) + "";
		var separtorIndex = (separtorIndex = wholePart.length) > 3 ? separtorIndex % 3 : 0;
		return neg +
			(separtorIndex ? wholePart.substr(0, separtorIndex) + separator : "") +
			wholePart.substr(separtorIndex).replace(/(\d{3})(?=\d)/g, "$1" + separator) +
			(fractionDigits ? decimal + Math.abs(number - wholePart).toFixed(fractionDigits).slice(2) : "");
	}

});

$(window).resize(function() {
	setTimeout(function() {
		if ($(window).width() >= 1200) {
			$('.report-header').css({ width: $('#content').width() });
		} else {
			$('.report-header').removeAttr('style');
		}
	}, 1);
});
