(function($) {
    $.tooltipster.setDefaults({
        // @see https://calebjacob.github.io/tooltipster/#styling
        theme: ['tooltipster-shadow', 'tooltipster-shadow-customized'],
        maxWidth: 500,
    });

    /**
     * Global Tooltipster initialization for elements with a data-tooltipster attribute.
     *
     * The data-tooltipster attribute can be a JSON string with key-value pairs of options to override.
     *
     * @param [context] - A DOM Element, Document, jQuery or selector to use as context.
     * @see https://calebjacob.github.io/tooltipster/#data-attributes
     */
    window.initTooltipster = function(context) {
        // Prefer not selecting by '.tooltip' as shown in most examples because that class is coupled with CSS styles.
        // Exclude elements that have already been initialized as indicated by the '.tooltipstered' class.
        $('[data-tooltipster]:not(.tooltipstered)', context).tooltipster({
            functionInit: function(instance, helper) {
                var $origin = $(helper.origin),
                    dataOptions = $origin.attr('data-tooltipster');

                if (dataOptions) {
                    dataOptions = JSON.parse(dataOptions);
                    $.each(dataOptions, function(name, option) {
                        instance.option(name, option);
                    });
                }
            },
        });
    }
    $(function() {
        window.initTooltipster();
    }).on('ajaxSuccess', function() {
        window.initTooltipster();
    });

    // Allow non-mouse navigation to trigger tooltipster tooltips if the element is focusable.
    // @see https://calebjacob.github.io/tooltipster/#accessibility
    $(document).on('focus', '.tooltipstered', function() {
        $(this).tooltipster('open');
    }).on('blur', '.tooltipstered', function() {
        $(this).tooltipster('close');
    })
})(jQuery);
