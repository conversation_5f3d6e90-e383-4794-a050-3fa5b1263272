options:
    dynamic_substitutions: true
steps:
  # <PERSON><PERSON> docker builds by pushing then pulling the latest version of each image.
  # https://cloud.google.com/build/docs/optimize-builds/speeding-up-builds#using_a_cached_docker_image
  # Caching a multi-stage build requires pushing then pulling a separate image for each stage.
  # https://stackoverflow.com/a/52940692
- name: 'gcr.io/cloud-builders/docker'
  id: build-dependencies
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      docker pull "us-central1-docker.pkg.dev/${PROJECT_ID}/shipearly/dependencies:latest" || true
      docker build \
        --target dependencies \
        --build-arg DEV_MODE=${_DEV_MODE} \
        --cache-from "us-central1-docker.pkg.dev/${PROJECT_ID}/shipearly/dependencies:latest" \
        -t "us-central1-docker.pkg.dev/${PROJECT_ID}/shipearly/dependencies:latest" \
        .

- name: 'gcr.io/cloud-builders/docker'
  id: build-image
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      docker pull "us-central1-docker.pkg.dev/${PROJECT_ID}/shipearly/shipearlyapp:latest" || true
      docker build \
        --build-arg DEV_MODE=${_DEV_MODE} \
        --cache-from "us-central1-docker.pkg.dev/${PROJECT_ID}/shipearly/dependencies:latest" \
        --cache-from "us-central1-docker.pkg.dev/${PROJECT_ID}/shipearly/shipearlyapp:latest" \
        -t "us-central1-docker.pkg.dev/${PROJECT_ID}/shipearly/shipearlyapp:latest" \
        -t "us-central1-docker.pkg.dev/${PROJECT_ID}/shipearly/shipearlyapp:${_VERSION}" \
        .

- name: 'gcr.io/cloud-builders/docker'
  id: push-image
  args:
  - 'push'
  - 'us-central1-docker.pkg.dev/${PROJECT_ID}/shipearly/shipearlyapp:${_VERSION}'

  # Create the new instance template
- name: gcr.io/cloud-builders/gcloud
  id: create-instance-template
  args:
    - compute
    - instance-templates
    - create-with-container
    - shipearlyapp-template-${_VERSION}
    -  --subnet=projects/${PROJECT_ID}/regions/us-central1/subnetworks/${_ENV}-network-private
    -  --machine-type=${_MACHINE_TYPE} # Eg. e2-medium, e2-standard-4, or t2d-standard-4
    -  --no-address
    -  --metadata=google-logging-enabled=true
    -  --maintenance-policy=MIGRATE
    -  --service-account=shipearlyapp@${PROJECT_ID}.iam.gserviceaccount.com
    -  --scopes=https://www.googleapis.com/auth/cloud-platform
    -  --region=us-central1
    -  --tags=shipearly-mig,http-server,https-server
    -  --image=cos-stable-89-16108-534-13
    -  --image-project=cos-cloud
    -  --boot-disk-size=10GB
    -  --boot-disk-type=pd-balanced
    -  --boot-disk-device-name=shipearlyapp-template
    -  --no-shielded-secure-boot
    -  --shielded-vtpm
    -  --shielded-integrity-monitoring
    -  --container-image=us-central1-docker.pkg.dev/${PROJECT_ID}/shipearly/shipearlyapp:${_VERSION}
    -  --container-restart-policy=always
    -  --container-env-file=instance-template-${_ENV}.env

  # Rolling update on MIG
- name: gcr.io/cloud-builders/gcloud
  id: update-mig
  args:
    - beta
    - compute
    - instance-groups
    - managed
    - rolling-action
    - start-update
    - ${_ENV}-shipearlyapp-migs
    -  --project=${PROJECT_ID}
    -  --type=proactive
    -  --max-surge=8
    -  --max-unavailable=0
    -  --min-ready=30
    -  --minimal-action=replace
    -  --most-disruptive-allowed-action=replace
    -  --replacement-method=substitute
    -  --version=template=projects/${PROJECT_ID}/global/instanceTemplates/shipearlyapp-template-${_VERSION}
    -  --region=us-central1

images:
  - 'us-central1-docker.pkg.dev/${PROJECT_ID}/shipearly/dependencies:latest'
  - 'us-central1-docker.pkg.dev/${PROJECT_ID}/shipearly/shipearlyapp:latest'
  - 'us-central1-docker.pkg.dev/${PROJECT_ID}/shipearly/shipearlyapp:${_VERSION}'
