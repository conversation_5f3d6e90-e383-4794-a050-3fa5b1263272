<?php

use Phinx\Migration\AbstractMigration;

class Ship1844PercentageBasedConsumerShippingRates extends AbstractMigration
{
    public function change()
    {
        $this
            ->table('price_based_shipping_rates')
            ->addColumn('amount_type', 'enum', [
                'values' => ['flat', 'percent'],
                'null' => false,
                'default' => 'flat',
                'after' => 'unit',
            ])
            ->update();
        $this
            ->table('weight_based_shipping_prices')
            ->addColumn('amount_type', 'enum', [
                'values' => ['flat', 'percent'],
                'null' => false,
                'default' => 'flat',
                'after' => 'unit',
            ])
            ->update();
    }
}
