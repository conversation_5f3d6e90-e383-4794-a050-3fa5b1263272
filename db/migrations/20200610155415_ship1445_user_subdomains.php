<?php

use Phinx\Migration\AbstractMigration;

class Ship1445UserSubdomains extends AbstractMigration
{
    public function up()
    {
        $this->execute(<<<'SQL'
CREATE TABLE `ship_user_subdomains` (
  `user_id` INT PRIMARY KEY,
  `subdomain` VARCHAR(63) NULL,
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP,
  CONSTRAINT `unique_subdomain` UNIQUE (`subdomain`),
  CONSTRAINT `fk_ship_user_subdomains_user_id` FOREIGN KEY (`user_id`) REFERENCES `ship_users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
);
SQL
        );
    }

    public function down()
    {
        $this->table('user_subdomains')->drop()->save();
    }
}
