<?php

use Phinx\Migration\AbstractMigration;

class Ship1954UsersShopRedirectUrl extends AbstractMigration
{
    public function change()
    {
        $this
            ->table('users')
            ->addColumn('shop_home_url', 'string', [
                'null' => false,
                'default' => '',
                'after' => 'shop_url',
            ])
            ->addColumn('shop_cart_url', 'string', [
                'null' => false,
                'default' => '',
                'after' => 'shop_home_url',
            ])
            ->update();
    }
}
