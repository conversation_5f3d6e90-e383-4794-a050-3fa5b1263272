<?php

use Phinx\Migration\AbstractMigration;

class Ship1957AdminTaxSettings extends AbstractMigration
{
    public function up()
    {
        $this->execute(<<<'SQL'
CREATE TABLE `ship_country_taxes` (
  `country_id` INT PRIMARY KEY,
  `name` VARCHAR(30) DEFAULT '' NOT NULL,
  `rate` DECIMAL(6, 5) DEFAULT 0 NOT NULL,
  `includes_shipping` BOOL DEFAULT TRUE NOT NULL,
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP,
  CONSTRAINT `fk_ship_country_taxes_country_id` FOREIGN KEY (`country_id`) REFERENCES `ship_countries` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
);
SQL
        );
        $this->execute(<<<'SQL'
CREATE TABLE `ship_state_taxes` (
  `state_id` INT PRIMARY KEY,
  `country_id` INT NOT NULL,
  `name` VARCHAR(30) DEFAULT '' NOT NULL,
  `rate` DECIMAL(6, 5) DEFAULT 0 NOT NULL,
  `includes_shipping` BOOL DEFAULT TRUE NOT NULL,
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP,
  CONSTRAINT `fk_ship_state_taxes_state_id` FOREIGN KEY (`state_id`) REFERENCES `ship_states` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_ship_state_taxes_country_id` FOREIGN KEY (`country_id`) REFERENCES `ship_country_taxes` (`country_id`) ON DELETE CASCADE ON UPDATE CASCADE
);
SQL
        );
    }

    public function down()
    {
        $this->table('state_taxes')->drop()->save();
        $this->table('country_taxes')->drop()->save();
    }
}
