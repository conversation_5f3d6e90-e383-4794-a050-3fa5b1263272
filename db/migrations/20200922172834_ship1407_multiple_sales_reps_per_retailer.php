<?php

use Phinx\Migration\AbstractMigration;

class Ship1407MultipleSalesRepsPerRetailer extends AbstractMigration
{
    public function up()
    {
        $this->execute(<<<'SQL'
ALTER TABLE `ship_manufacturer_retailers` 
  ADD COLUMN `distributor_id` INT NULL AFTER `is_commission_tier`,
  ADD CONSTRAINT `fk_ship_manufacturer_retailers_distributor_id` FOREIGN KEY (`distributor_id`) REFERENCES `ship_users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;
SQL
        );
        $this->execute(<<<'SQL'
UPDATE `ship_manufacturer_retailers` AS `ManufacturerRetailer`
  INNER JOIN `ship_manufacturer_retailer_sales_reps` AS `ManufacturerRetailerSalesRep` ON (`ManufacturerRetailerSalesRep`.`manufacturer_retailer_id` = `ManufacturerRetailer`.`id`)
  INNER JOIN `ship_manufacturer_sales_reps` AS `ManufacturerSalesRep` ON (
    `ManufacturerSalesRep`.`user_id` = `ManufacturerRetailer`.`user_id` AND
    `ManufacturerSalesRep`.`sales_rep_id` = `ManufacturerRetailerSalesRep`.`sales_rep_id`
  )
SET `ManufacturerRetailer`.`distributor_id` = `ManufacturerRetailerSalesRep`.`sales_rep_id`
WHERE `ManufacturerSalesRep`.`is_distributor` = TRUE;
SQL
        );
        $this->execute(<<<'SQL'
DELETE `ManufacturerRetailerSalesRep` 
FROM `ship_manufacturer_retailers` AS `ManufacturerRetailer`
  INNER JOIN `ship_manufacturer_retailer_sales_reps` AS `ManufacturerRetailerSalesRep` ON (`ManufacturerRetailerSalesRep`.`manufacturer_retailer_id` = `ManufacturerRetailer`.`id`)
  INNER JOIN `ship_manufacturer_sales_reps` AS `ManufacturerSalesRep` ON (
    `ManufacturerSalesRep`.`user_id` = `ManufacturerRetailer`.`user_id` AND
    `ManufacturerSalesRep`.`sales_rep_id` = `ManufacturerRetailerSalesRep`.`sales_rep_id`
  )
WHERE `ManufacturerSalesRep`.`is_distributor` = TRUE;
SQL
        );
        $this->execute(<<<'SQL'
CREATE TABLE `ship_order_sales_reps` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `order_id` INT NOT NULL,
  `sales_rep_id` INT NOT NULL,
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL,
  CONSTRAINT `unique_order_id_sales_rep_id` UNIQUE (`order_id`, `sales_rep_id`),
  CONSTRAINT `fk_ship_order_sales_reps_order_id` FOREIGN KEY (`order_id`) REFERENCES `ship_orders` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_ship_order_sales_reps_sales_rep_id` FOREIGN KEY (`sales_rep_id`) REFERENCES `ship_users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
);
SQL
        );
        $this->execute(<<<'SQL'
INSERT INTO `ship_order_sales_reps` (`order_id`, `sales_rep_id`)
  SELECT `id`, `sales_rep_id`
  FROM `ship_orders`
  WHERE `sales_rep_id` IS NOT NULL AND `has_distributor` = FALSE
  ORDER BY `id`, `sales_rep_id`;
SQL
        );
        $this->execute(<<<'SQL'
ALTER TABLE `ship_orders`
  ADD COLUMN `distributor_id` INT NULL AFTER `sales_rep_id`,
  ADD CONSTRAINT `fk_ship_orders_distributor_id` FOREIGN KEY (`distributor_id`) REFERENCES `ship_users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;
SQL
        );
        $this->execute(<<<'SQL'
UPDATE `ship_orders`
SET `distributor_id` = `sales_rep_id`
WHERE `sales_rep_id` IS NOT NULL AND `has_distributor` = TRUE;
SQL
        );
        $this->execute(<<<'SQL'
ALTER TABLE `ship_orders`
  MODIFY COLUMN `sales_rep_id` INT NULL COMMENT 'Deprecated since Version1.40. Can be removed in Version1.41.',
  MODIFY COLUMN `has_distributor` BOOL DEFAULT FALSE NOT NULL COMMENT 'Deprecated since Version1.40. Can be removed in Version1.41.';
SQL
        );
    }

    public function down()
    {
        $this->execute(<<<'SQL'
ALTER TABLE `ship_orders`
  MODIFY COLUMN `sales_rep_id` INT NULL COMMENT '',
  MODIFY COLUMN `has_distributor` BOOL DEFAULT FALSE NOT NULL COMMENT '';
SQL
        );
        $this->execute(<<<'SQL'
UPDATE `ship_orders`
SET `sales_rep_id` = `distributor_id`, `has_distributor` = TRUE
WHERE `distributor_id` IS NOT NULL AND `sales_rep_id` IS NULL;
SQL
        );
        $this->execute(<<<'SQL'
ALTER TABLE `ship_orders`
  DROP FOREIGN KEY `fk_ship_orders_distributor_id`,
  DROP COLUMN `distributor_id`;
SQL
        );
        $this->execute(<<<'SQL'
UPDATE `ship_orders` AS `Order`
  INNER JOIN (
    SELECT `order_id`, MIN(`id`) AS `id`
    FROM `ship_order_sales_reps`
    GROUP BY `order_id`
  ) AS `FirstOrderSalesRep` ON (`Order`.`id` = `FirstOrderSalesRep`.`order_id`)
  INNER JOIN `ship_order_sales_reps` AS `OrderSalesRep` ON (`FirstOrderSalesRep`.`id` = `OrderSalesRep`.`id`)
SET
  `Order`.`sales_rep_id` = `OrderSalesRep`.`sales_rep_id`,
  `Order`.`has_distributor` = FALSE
WHERE `Order`.`sales_rep_id` IS NULL;
SQL
        );
        $this->execute(<<<'SQL'
DROP TABLE `ship_order_sales_reps`;
SQL
        );
        $this->execute(<<<'SQL'
INSERT INTO `ship_manufacturer_retailer_sales_reps` (`manufacturer_retailer_id`, `sales_rep_id`)
  SELECT `ManufacturerRetailer`.`id`, `ManufacturerRetailer`.`distributor_id`
  FROM `ship_manufacturer_retailers` AS `ManufacturerRetailer`
    LEFT JOIN `ship_manufacturer_retailer_sales_reps` AS `ManufacturerRetailerSalesRep` ON (`ManufacturerRetailerSalesRep`.`manufacturer_retailer_id` = `ManufacturerRetailer`.`id`)
  WHERE `ManufacturerRetailer`.`distributor_id` IS NOT NULL 
    AND `ManufacturerRetailerSalesRep`.`id` IS NULL;
SQL
        );
        $this->execute(<<<'SQL'
ALTER TABLE `ship_manufacturer_retailers` 
  DROP FOREIGN KEY `fk_ship_manufacturer_retailers_distributor_id`,
  DROP COLUMN `distributor_id`;
SQL
        );
    }
}
