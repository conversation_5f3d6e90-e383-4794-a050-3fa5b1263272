<?php

use Phinx\Migration\AbstractMigration;

class Ship2028ProductTags extends AbstractMigration
{
    public function up()
    {
        $this->execute(<<<'SQL'
CREATE TABLE `ship_tags` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `user_id` INT NOT NULL,
  `name` VARCHAR(255) NOT NULL,
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP,
  CONSTRAINT `unique_user_id_name` UNIQUE (`user_id`, `name`),
  CONSTRAINT `fk_ship_tags_user_id` FOREIGN KEY (`user_id`) REFERENCES `ship_users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
);
SQL
        );
        $this->execute(<<<'SQL'
CREATE TABLE `ship_product_tags` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `product_id` INT NOT NULL,
  `tag_id` INT NOT NULL,
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL,
  CONSTRAINT `unique_product_id_tag_id` UNIQUE (`product_id`, `tag_id`),
  CONSTRAINT `fk_ship_product_tags_product_id` FOREIGN KEY (`product_id`) REFERENCES `ship_products` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_ship_product_tags_tag_id` FOREIGN KEY (`tag_id`) REFERENCES `ship_tags` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
);
SQL
        );

        $this->table('products')
            ->removeColumn('tags')
            ->update();
    }

    public function down()
    {
        $this->table('products')
            ->addColumn('tags', 'string', [
                'limit' => 100,
                'null' => true,
                'after' => 'vendor',
            ])
            ->update();

        $this->table('product_tags')->drop()->save();
        $this->table('tags')->drop()->save();
    }
}
