<?php

use Phinx\Migration\AbstractMigration;

class Ship2064Fulfillments extends AbstractMigration
{
    public function up()
    {
        $this->execute(<<<'SQL'
CREATE TABLE `ship_fulfillments` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `order_id` INT NOT NULL,
  `warehouse_id` INT NOT NULL,
  `source_id` VARCHAR(255) NULL,
  `name` VARCHAR(255) NOT NULL,
  `courier_id` INT NULL,
  `tracking_number` VARCHAR(100) NULL,
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP,
  CONSTRAINT `unique_order_id_name` UNIQUE (`order_id`, `name`),
  CONSTRAINT `fk_ship_fulfillments_order_id` FOREIGN KEY (`order_id`) REFERENCES `ship_orders` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_ship_fulfillments_warehouse_id` FOREIGN KEY (`warehouse_id`) REFERENCES `ship_warehouses` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_ship_fulfillments_courier_id` FOREIGN KEY (`courier_id`) REFERENCES `ship_couriers` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
);
CREATE TABLE `ship_fulfillment_products` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `fulfillment_id` INT NOT NULL,
  `order_product_id` INT NOT NULL,
  `quantity` INT NOT NULL,
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP,
  CONSTRAINT `unique_fulfillment_id_order_product_id` UNIQUE (`fulfillment_id`, `order_product_id`),
  CONSTRAINT `fk_ship_fulfillment_products_fulfillment_id` FOREIGN KEY (`fulfillment_id`) REFERENCES `ship_fulfillments` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_ship_fulfillment_products_order_product_id` FOREIGN KEY (`order_product_id`) REFERENCES `ship_order_products` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
);
SQL
        );
    }

    public function down()
    {
        $this->table('fulfillment_products')->drop()->save();
        $this->table('fulfillments')->drop()->save();
    }
}
