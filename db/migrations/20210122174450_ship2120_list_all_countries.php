<?php

use Phinx\Migration\AbstractMigration;

class Ship2120ListAllCountries extends AbstractMigration
{
    public function up()
    {
        $country_code_order = [
            'us' => 1,
            'ca' => 2,
        ];

        $this->execute(<<<SQL
UPDATE `ship_countries` SET `display_order` = (CASE `country_code` {$this->display_order_cases($country_code_order)} ELSE 250 END) WHERE 1=1;
SQL
        );

        $this->table('countries')
            ->addIndex(['display_order', 'country_name'], ['name' => 'index_display_order_country_name'])
            ->update();

        $this->table('countries')
            ->removeColumn('active')
            ->update();
    }

    public function down()
    {
        $country_code_order = [
            'us' => 1,
            'ca' => 2,
            'au' => 3,
            'at' => 4,
            'be' => 5,
            'dk' => 6,
            'fi' => 7,
            'fr' => 8,
            'de' => 9,
            'ie' => 10,
            'it' => 11,
            'jp' => 12,
            'lu' => 13,
            'mx' => 14,
            'nl' => 15,
            'no' => 16,
            'es' => 17,
            'se' => 18,
            'ch' => 19,
            'gb' => 20,
        ];

        $this->execute(<<<SQL
ALTER TABLE `ship_countries` ADD COLUMN `active` TINYINT(2) NOT NULL COMMENT '1:Stripe restriction 2: All users' AFTER `country_name`; 
UPDATE `ship_countries` SET `active` = IF(`country_code` IN ({$this->active_country_codes($country_code_order)}), 1, 0) WHERE 1=1;
SQL
        );

        $this->table('countries')
            ->removeIndexByName('index_display_order_country_name')
            ->update();

        $this->execute(<<<SQL
UPDATE `ship_countries` SET `display_order` = (CASE `country_code` {$this->display_order_cases($country_code_order)} ELSE 250 END) WHERE 1=1;
SQL
        );
    }

    private function display_order_cases(array $country_code_order): string
    {
        $cases = [];
        foreach ($country_code_order as $country_code => $display_order) {
            $cases[] = "WHEN '{$country_code}' THEN {$display_order}";
        }

        return implode(' ', $cases);
    }

    private function active_country_codes(array $country_code_order): string
    {
        $activeList = array_map(
            function($country_code) {
                return "'{$country_code}'";
            },
            array_keys($country_code_order)
        );

        return implode(',', $activeList);
    }
}
