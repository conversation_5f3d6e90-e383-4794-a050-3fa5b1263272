<?php

use Phinx\Migration\AbstractMigration;

class Ship2148SyncFulfillments extends AbstractMigration
{
    public function up()
    {
        $this->table('fulfillments')
            ->addColumn('tracking_url', 'string', [
                'null' => true,
                'after' => 'tracking_number',
            ])
            ->update();

        // 20210212161644_ship2112_dealer_fulfillments.php put off initializing
        // the fulfillment status of legacy shipped dealer orders tracked in
        // the ecommerce platform until the fulfillment sync was understood.
        // It is necessary to mark them fulfilled before synchronizing to avoid
        // triggering actions already performed when they were first shipped.
        $this->execute(<<<'SQL'
UPDATE `ship_dealer_orders` AS `DealerOrder`
SET `DealerOrder`.`fulfillment_status` = 'fulfilled'
WHERE `DealerOrder`.`shipment_date` IS NOT NULL;
SQL
        );
    }

    public function down()
    {
        // Revert above by rerunning the fulfillment_status initializer from
        // 20210212161644_ship2112_dealer_fulfillments.php
        $this->execute(<<<'SQL'
UPDATE `ship_dealer_orders` AS `DealerOrder`
  INNER JOIN (
    SELECT
      `DealerOrderProduct`.`dealer_order_id`,
      SUM(`DealerOrderProduct`.`product_quantity`) AS `total_quantity`,
      IFNULL(SUM(`FulfillmentProduct`.`quantity`), 0) AS `total_fulfilled`,
      IFNULL(SUM(`RefundProduct`.`quantity`), 0) AS `total_refunded`,
      SUM(GREATEST(0, `DealerOrderProduct`.`product_quantity` - IFNULL(`FulfillmentProduct`.`quantity`, 0) - IFNULL(`RefundProduct`.`quantity`, 0))) AS `total_unfulfilled`
    FROM `ship_dealer_order_products` AS `DealerOrderProduct`
      LEFT JOIN (
        SELECT `dealer_order_product_id`, SUM(quantity) AS `quantity` FROM `ship_fulfillment_products` GROUP BY `dealer_order_product_id`
      ) AS `FulfillmentProduct` ON (`DealerOrderProduct`.`id` = `FulfillmentProduct`.`dealer_order_product_id`)
      LEFT JOIN (
        SELECT `dealer_order_product_id`, SUM(quantity) AS `quantity` FROM `ship_dealer_order_refund_products` GROUP BY `dealer_order_product_id`
      ) AS `RefundProduct` ON (`DealerOrderProduct`.`id` = `RefundProduct`.`dealer_order_product_id`)
    GROUP BY `DealerOrderProduct`.`dealer_order_id`
  ) AS `FulfillmentTotals` ON (`DealerOrder`.`id` = `FulfillmentTotals`.`dealer_order_id`)
SET `DealerOrder`.`fulfillment_status` = (CASE
  WHEN `FulfillmentTotals`.`total_refunded` = `FulfillmentTotals`.`total_quantity` THEN 'cancelled'
  WHEN `FulfillmentTotals`.`total_unfulfilled` = 0 THEN 'fulfilled'
  WHEN `FulfillmentTotals`.`total_fulfilled` > 0 THEN 'partially_fulfilled'
  ELSE 'unfulfilled'
END);
SQL
        );

        $this->table('fulfillments')
            ->removeColumn('tracking_url')
            ->update();
    }
}
