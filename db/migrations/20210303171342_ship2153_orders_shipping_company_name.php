<?php

use Phinx\Migration\AbstractMigration;

class Ship2153OrdersShippingCompanyName extends AbstractMigration
{
    public function up()
    {
        $this->table('orders')
            ->addColumn('shipping_company_name', 'string', [
                'limit' => 50,
                'null' => true,
                'after' => 'billing_lastname',
            ])
            ->update();

        $this->execute(<<<'SQL'
UPDATE `ship_orders`
SET `shipping_company_name` = `billing_lastname`, `billing_lastname` = ''
WHERE `order_type` = 'wholesale';
SQL
        );
    }

    public function down()
    {
        $this->execute(<<<'SQL'
UPDATE `ship_orders`
SET `billing_firstname` = '', `billing_lastname` = `shipping_company_name`
WHERE `order_type` = 'wholesale';
SQL
        );

        $this->table('orders')
            ->removeColumn('shipping_company_name')
            ->update();
    }
}
