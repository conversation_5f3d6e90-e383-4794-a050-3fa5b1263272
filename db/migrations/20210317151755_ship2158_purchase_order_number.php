<?php

use Phinx\Migration\AbstractMigration;

class Ship2158PurchaseOrderNumber extends AbstractMigration
{
    public function change()
    {
        $this->table('b2b_carts')
            ->addColumn('purchase_order_number', 'string', [
                'limit' => 50,
                'null' => true,
                'after' => 'requested_ship_date',
            ])
            ->addColumn('notes', 'text', [
                'null' => true,
                'after' => 'purchase_order_number',
            ])
            ->update();

        $this->table('orders')
            ->addColumn('purchase_order_number', 'string', [
                'limit' => 50,
                'null' => true,
                'after' => 'source_order_name',
            ])
            ->update();
    }
}
