<?php

use Phinx\Migration\AbstractMigration;

class Ship1446InventoryTransfers extends AbstractMigration
{
    public function up()
    {
        $this->execute(<<<'SQL'
CREATE TABLE `ship_inventory_transfers` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `user_id` INT NOT NULL,
  `destination_warehouse_id` INT NOT NULL,
  `name` VARCHAR(255) NOT NULL,
  `status` ENUM('pending', 'partial', 'completed') DEFAULT 'pending' NOT NULL,
  `expected_arrival_date` DATE NOT NULL,
  `reference` VARCHAR(255) NULL,
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP,
  INDEX `index_user_id_expected_arrival_date` (`user_id`, `expected_arrival_date`),
  CONSTRAINT `fk_ship_inventory_transfers_user_id` FOREI<PERSON><PERSON> KEY (`user_id`) REFERENCES `ship_users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_ship_inventory_transfers_destination_warehouse_id` FOREIGN KEY (`destination_warehouse_id`) REFERENCES `ship_warehouses` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
);
SQL
        );
        $this->execute(<<<'SQL'
CREATE TABLE `ship_inventory_transfer_products` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `inventory_transfer_id` INT NOT NULL,
  `product_id` INT NOT NULL,
  `quantity` INT NOT NULL,
  `accepted_quantity` INT DEFAULT 0 NOT NULL,
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL,
  `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP,
  CONSTRAINT `unique_inventory_transfer_id_product_id` UNIQUE (`inventory_transfer_id`, `product_id`),
  CONSTRAINT `fk_ship_inventory_transfer_products_inventory_transfer_id` FOREIGN KEY (`inventory_transfer_id`) REFERENCES `ship_inventory_transfers` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_ship_inventory_transfer_products_product_id` FOREIGN KEY (`product_id`) REFERENCES `ship_products` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
);
SQL
        );
    }

    public function down()
    {
        $this->table('inventory_transfer_products')->drop()->save();
        $this->table('inventory_transfers')->drop()->save();
    }
}
