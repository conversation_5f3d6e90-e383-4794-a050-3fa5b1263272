<?php

use Phinx\Migration\AbstractMigration;

class Ship2243OrderProductsInventoryTransferId extends AbstractMigration
{
    public function change()
    {
        $this->table('b2b_cart_products')
            ->addColumn('inventory_transfer_id', 'integer', [
                'null' => true,
                'default' => null,
                'after' => 'warehouse_id',
            ])
            ->addForeignKeyWithName(
                'fk_ship_b2b_cart_products_inventory_transfer_id',
                'inventory_transfer_id',
                'inventory_transfers',
                'id',
                ['delete' => 'SET_NULL', 'update' => 'CASCADE']
            )
            ->update();

        $this->table('order_products')
            ->addColumn('inventory_transfer_id', 'integer', [
                'null' => true,
                'default' => null,
                'after' => 'warehouse_id',
            ])
            ->addForeignKeyWithName(
                'fk_ship_order_products_inventory_transfer_id',
                'inventory_transfer_id',
                'inventory_transfers',
                'id',
                ['delete' => 'SET_NULL', 'update' => 'CASCADE']
            )
            ->update();

        $this->table('dealer_order_products')
            ->addColumn('inventory_transfer_id', 'integer', [
                'null' => true,
                'default' => null,
                'after' => 'warehouse_id',
            ])
            ->addForeignKeyWithName(
                'fk_ship_dealer_order_products_inventory_transfer_id',
                'inventory_transfer_id',
                'inventory_transfers',
                'id',
                ['delete' => 'SET_NULL', 'update' => 'CASCADE']
            )
            ->update();
    }
}
