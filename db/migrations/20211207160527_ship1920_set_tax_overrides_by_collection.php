<?php

use Phinx\Migration\AbstractMigration;

class Ship1920SetTaxOverridesByCollection extends AbstractMigration
{
    public function change()
    {
        $this->table('zone_tax_override_collections')
            ->addColumn('collection_id', 'integer')
            ->addColumn('zone_tax_override_id', 'integer')
            ->addColumn('created_at', 'datetime', ['default' => 'CURRENT_TIMESTAMP'])
            ->addIndex(['collection_id', 'zone_tax_override_id'], ['unique' => true, 'name' => 'uindex_collection_id_zone_tax_override_id'])
            ->addForeignKeyWithName('fk_ship_zone_tax_override_collections_collection_id', 'collection_id', 'collections', 'id', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->addForeignKeyWithName('fk_ship_zone_tax_override_collections_zone_tax_override_id', 'zone_tax_override_id', 'zone_tax_overrides', 'id', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->create();
    }
}
