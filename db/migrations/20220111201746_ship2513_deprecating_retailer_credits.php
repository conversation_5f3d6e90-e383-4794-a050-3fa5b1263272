<?php

use Phinx\Migration\AbstractMigration;

class Ship2513DeprecatingRetailerCredits extends AbstractMigration
{
    public function up(){

        $this->table('retailer_credits')
            ->rename('ship_legacy_retailer_credits')
            ->dropForeignKey('credit_term_id')
            ->addForeignKeyWithName('fk_ship_legacy_retailer_credits_credit_term_id', 'credit_term_id', 'credit_terms', 'id')
            ->update();

    }

    public function down(){

        $this->table('legacy_retailer_credits')
            ->rename('ship_retailer_credits')
            ->dropForeignKey('credit_term_id')
            ->addForeignKeyWithName('fk_ship_retailer_credits_credit_term_id', 'credit_term_id', 'credit_terms', 'id')
            ->update();
    }
}
