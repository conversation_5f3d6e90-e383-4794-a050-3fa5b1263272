<?php

use Phinx\Migration\AbstractMigration;

class Ship2513CreatingNewRetailerCreditsTable extends AbstractMigration
{
    public function change()
    {
        $this->table('retailer_credits')
            ->addColumn('user_id', 'integer', [
                'default' => null,
                'null' => false,
            ])
            ->addColumn('retailer_id', 'integer', [
                'default' => null,
                'null' => false,
            ])
            ->addColumn('dealer_order_id', 'integer', [
                'default' => null,
                'null' => true,
            ])
            ->addColumn('description', 'string', [
                'default' => null,
                'null' => true,
                'limit' => 255
            ])
            ->addColumn('invoice_number', 'string', [
                'default' => null,
                'null' => true,
                'limit' => 50
            ])
            ->addColumn('credit_date', 'date')
            ->addColumn('due_date', 'date')
            ->addColumn('credit_term_id', 'integer', [
                'default' => null,
                'null' => true,
            ])
            ->addColumn('created_at', 'datetime', ['default' => 'CURRENT_TIMESTAMP'])
            ->addColumn('updated_at', 'datetime', ['default' => 'CURRENT_TIMESTAMP', 'update' => 'CURRENT_TIMESTAMP'])
            ->addForeignKeyWithName('fk_ship_retailer_credits_user_id', 'user_id', 'users', 'id', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->addForeignKeyWithName('fk_ship_retailer_credits_retailer_id', 'retailer_id', 'users', 'id', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->addForeignKeyWithName('fk_ship_retailer_credits_dealer_order_id', 'dealer_order_id', 'dealer_orders', 'id', ['delete' => 'NO_ACTION', 'update' => 'CASCADE'])
            ->addForeignKeyWithName('fk_ship_retailer_credits_credit_term_id', 'credit_term_id', 'credit_terms', 'id', ['delete' => 'NO_ACTION', 'update' => 'CASCADE'])
            ->create();

        $this->table('retailer_credit_items')
            ->addColumn('retailer_credit_id', 'integer', [
                'default' => null,
                'null' => false,
            ])
            ->addColumn('type', 'string', [
                'default' => null,
                'null' => false,
                'limit' => 20
            ])
            ->addColumn('fulfillment_id', 'integer', [
                'default' => null,
                'null' => true,
            ])
            ->addColumn('amount', 'decimal', ['precision' => '13', 'scale' => '4'], [
                'default' => null,
                'null' => false,
            ])
            ->addColumn('created_at', 'datetime', ['default' => 'CURRENT_TIMESTAMP'])
            ->addColumn('updated_at', 'datetime', ['default' => 'CURRENT_TIMESTAMP', 'update' => 'CURRENT_TIMESTAMP'])
            ->addForeignKeyWithName('fk_ship_retailer_credit_items_retailer_credit_id', 'retailer_credit_id', 'retailer_credits', 'id', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->addForeignKeyWithName('fk_ship_retailer_credit_items_fulfillment_id', 'fulfillment_id', 'fulfillments', 'id', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->create();

        $this->table('retailer_credit_payments')
            ->addColumn('retailer_credit_id', 'integer', [
                'default' => null,
                'null' => false,
            ])
            ->addColumn('amount', 'decimal', ['precision' => '13', 'scale' => '4'], [
                'default' => null,
                'null' => false,
            ])
            ->addColumn('created_at', 'datetime', ['default' => 'CURRENT_TIMESTAMP'])
            ->addColumn('updated_at', 'datetime', ['default' => 'CURRENT_TIMESTAMP', 'update' => 'CURRENT_TIMESTAMP'])
            ->addForeignKeyWithName('fk_ship_retailer_credit_payments_retailer_credit_id', 'retailer_credit_id', 'retailer_credits', 'id', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->create();
    }
}
