<?php

use Phinx\Migration\AbstractMigration;

class Ship2236AddAscendIntegration extends AbstractMigration
{
    public function up()
    {
        $this->table('users')
            ->changeColumn('inventory_type', 'enum', [
                'values' => ['lightspeed_cloud', 'vend_pos', 'shopify_pos', 'quickbook_pos', 'square', 'other', 'ascend_rms'],
                'null' => true,
                'default' => null
            ])
            ->update();
    }

    public function down()
    {
        $this->execute(<<<SQL
UPDATE `ship_users` AS `User`
SET `User`.`inventory_type` = 'other',
    `User`.`otherInventory` = 'ascend_rms'
WHERE `User`.`inventory_type` = 'ascend_rms'
SQL
        );

        $this->table('users')
            ->changeColumn('inventory_type', 'enum', [
                'values' => ['lightspeed_cloud', 'vend_pos', 'shopify_pos', 'quickbook_pos', 'square', 'other'],
                'null' => true,
                'default' => null
            ])
            ->update();
    }
}
