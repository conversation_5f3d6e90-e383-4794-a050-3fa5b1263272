<?php

use Phinx\Migration\AbstractMigration;

class Ship2775RetailerNotesTable extends AbstractMigration
{

    public function change()
    {
        $this->table('manufacturer_retailer_notes')
            ->addColumn('manufacturer_retailer_id', 'integer')
            ->addColumn('content', 'text')
            ->addColumn('author_id', 'integer')
            ->addColumn('created_at', 'datetime', ['default' => 'CURRENT_TIMESTAMP'])
            ->addColumn('updated_at', 'datetime', ['default' => 'CURRENT_TIMESTAMP', 'update' => 'CURRENT_TIMESTAMP'])
            ->addForeignKeyWithName('fk_ship_manufacturer_retailer_notes_manufacturer_retailer_id', 'manufacturer_retailer_id', 'manufacturer_retailers', 'id', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->addForeignKeyWithName('fk_ship_manufacturer_retailer_notes_author_id', 'author_id', 'users', 'id', ['delete' => 'NO_ACTION', 'update' => 'CASCADE'])
            ->create();
    }
}
