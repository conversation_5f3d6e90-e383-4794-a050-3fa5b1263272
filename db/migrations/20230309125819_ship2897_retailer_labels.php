<?php

use Phinx\Migration\AbstractMigration;

class Ship2897RetailerLabels extends AbstractMigration
{
    public function change()
    {
        $this->table('labels')
            ->addColumn('user_id', 'integer')
            ->addColumn('name', 'string')
            ->addColumn('created_at', 'datetime', ['default' => 'CURRENT_TIMESTAMP'])
            ->addColumn('updated_at', 'datetime', ['default' => 'CURRENT_TIMESTAMP', 'update' => 'CURRENT_TIMESTAMP'])
            ->addForeignKeyWithName('fk_labels_user_id', 'user_id', 'users', 'id', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->create();

        $this->table('manufacturer_retailer_labels')
            ->addColumn('manufacturer_retailer_id', 'integer')
            ->addColumn('label_id', 'integer')
            ->addColumn('created_at', 'datetime', ['default' => 'CURRENT_TIMESTAMP'])
            ->addColumn('updated_at', 'datetime', ['default' => 'CURRENT_TIMESTAMP', 'update' => 'CURRENT_TIMESTAMP'])
            ->addForeignKeyWithName('fk_manufacturer_retailer_labels_manufacturer_retailer_id', 'manufacturer_retailer_id', 'manufacturer_retailers', 'id', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->addForeignKeyWithName('fk_manufacturer_retailer_labels_label_id', 'label_id', 'labels', 'id', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->create();
    }
}
