<?php

use Phinx\Migration\AbstractMigration;

class Ship2911CustomMapMarkers extends AbstractMigration
{
    public function change()
    {
        $this->table('user_map_markers')
            ->addColumn('user_id', 'integer', [
                'null' => false,
                'default' => null,
            ])
            ->addColumn('uri', 'string', [
                'null' => false,
                'default' => null,
            ])
            ->addColumn('type', 'string', [
                'null' => false,
                'default' => null,
            ])
            ->addColumn('created_at', 'datetime', ['default' => 'CURRENT_TIMESTAMP'])
            ->addColumn('updated_at', 'datetime', ['default' => 'CURRENT_TIMESTAMP', 'update' => 'CURRENT_TIMESTAMP'])
            ->addIndex(['user_id', 'type'], ['unique' => true, 'name' => 'unique_user_id_type'])
            ->addForeignKeyWithName('fk_ship_user_map_markers_user_id', ['user_id'], 'users', 'id', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->create();
    }
}
