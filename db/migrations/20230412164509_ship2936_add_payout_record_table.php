<?php

use Phinx\Migration\AbstractMigration;

class Ship2936AddPayoutRecordTable extends AbstractMigration
{
    public function change()
    {
        $this->table('order_payouts')
            ->addColumn('stripe_payout_id', 'string', [
                'default' => null,
                'null' => false,
            ])
            ->addColumn('stripe_balance_transaction_id', 'string', [
                'default' => null,
                'null' => false,
            ])
            ->addColumn('order_id', 'integer', [
                'default' => null,
                'null' => false,
            ])
            ->addColumn('amount', 'decimal', [
                'precision' => '15',
                'scale' => '2',
                'default' => null,
                'null' => false,
            ])
            ->addColumn('currency', 'string', [
                'default' => null,
                'null' => false,
            ])
            ->addColumn('payout_total_amount', 'decimal', [
                'precision' => '15',
                'scale' => '2',
                'default' => null,
                'null' => false,
            ])
            ->addColumn('payout_currency', 'string', [
                'default' => null,
                'null' => false,
            ])
            ->addColumn('date', 'date', [
                'default' => null,
                'null' => false,
            ])
            ->addColumn('status', 'string', [
                'default' => null,
                'null' => false,
            ])
            ->addColumn('last_four_digits', 'string', [
                'default' => null,
                'null' => false,
            ])
            ->addColumn('institution', 'string', [
                'default' => null,
                'null' => false,
            ])
            ->addColumn('destination_type', 'string', [
                'default' => null,
                'null' => false,
            ])
            ->addColumn('created_at', 'datetime', [
                'default' => 'CURRENT_TIMESTAMP',
            ])
            ->addColumn('updated_at', 'datetime', [
                'default' => 'CURRENT_TIMESTAMP',
                'update' => 'CURRENT_TIMESTAMP',
            ])
            ->addForeignKeyWithName('fk_ship_order_payouts_order_id', 'order_id', 'orders', 'id', ['update' => 'CASCADE', 'delete' => 'CASCADE'])
            ->create();

        $this->table('orders')
            ->addIndex('balance_transaction_id', [
                'name' => 'index_balance_transaction_id',
            ])
            ->update();
    }
}
