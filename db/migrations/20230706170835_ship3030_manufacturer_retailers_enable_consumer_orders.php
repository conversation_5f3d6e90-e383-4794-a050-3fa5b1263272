<?php

use Phinx\Migration\AbstractMigration;

class Ship3030ManufacturerRetailersEnableConsumerOrders extends AbstractMigration
{
    public function change()
    {
        $this->table('manufacturer_retailers')
            ->addColumn('enable_consumer_orders', 'boolean', [
                'default' => true,
                'after' => 'enable_b2b_external_payment',
            ])
            ->update();
    }
}
