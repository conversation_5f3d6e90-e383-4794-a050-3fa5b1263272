<?php

use Phinx\Migration\AbstractMigration;

class Ship761DeprecateProductCategoriesType extends AbstractMigration
{
    public function up()
    {
        $this->table('product_categories')
            ->changeColumn('type', 'enum', [
                'values' => ['shipearly', 'ecommerce'],
                'default' => 'shipearly',
                'comment' => 'Deprecated',
            ])
            ->update();
    }

    public function down()
    {
        $this->table('product_categories')
            ->changeColumn('type', 'enum', [
                'values' => ['shipearly', 'ecommerce'],
            ])
            ->update();
    }
}
