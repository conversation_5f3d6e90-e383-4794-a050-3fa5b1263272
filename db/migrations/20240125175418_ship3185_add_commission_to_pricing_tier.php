<?php

use Phinx\Migration\AbstractMigration;

class Ship3185AddCommissionToPricingTier extends AbstractMigration
{
    public function change()
    {
        $table = $this->table('product_tiers');
        $table->addColumn('commission', 'decimal', [
            'precision' => 18,
            'scale' => 2,
            'default' => '0.00',
            'null' => false,
            'after' => 'alt_nonstock_dealer_price',
        ])->update();
    
        if ($this->isMigratingUp()) {
            $populateExistingProductTierCommissionsSql = <<<'SQL'
UPDATE `ship_product_tiers`
INNER JOIN `ship_products` ON (`ship_product_tiers`.`product_id` = `ship_products`.`id`)
SET `ship_product_tiers`.`commission` = `ship_products`.`assembly_commission`
WHERE `ship_products`.`assembly_commission` > 0.00;
SQL;
            $this->execute(trim(preg_replace('/\s+/', ' ', $populateExistingProductTierCommissionsSql)));
        }
    }
}
