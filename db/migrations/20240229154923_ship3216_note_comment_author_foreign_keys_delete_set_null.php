<?php

use Phinx\Migration\AbstractMigration;

class Ship3216NoteCommentAuthorForeignKeysDeleteSetNull extends AbstractMigration
{
    public function up()
    {
        $this->table('manufacturer_retailer_notes')
            ->changeColumn('author_id', 'integer', ['null' => true])
            ->dropForeignKey('author_id', 'fk_ship_manufacturer_retailer_notes_author_id')
            ->update();
        $this->table('manufacturer_retailer_notes')
            ->addForeignKeyWithName('fk_ship_manufacturer_retailer_notes_author_id', 'author_id', 'users', 'id', ['delete' => 'SET_NULL', 'update' => 'CASCADE'])
            ->update();

        $this->table('order_comments')
            ->changeColumn('author_id', 'integer', ['null' => true])
            ->dropForeignKey('author_id', 'fk_ship_order_comments_user_id')
            ->removeIndexByName('fk_ship_order_comments_user_id')
            ->update();
        $this->table('order_comments')
            ->addForeignKeyWithName('fk_ship_order_comments_author_id', 'author_id', 'users', 'id', ['delete' => 'SET_NULL', 'update' => 'CASCADE'])
            ->addIndex(['author_id'], ['name' => 'fk_ship_order_comments_author_id'])
            ->update();

        $this->table('order_customer_messages')
            ->changeColumn('author_id', 'integer', ['null' => true])
            ->dropForeignKey('author_id', 'fk_ship_order_customer_messages_user_id')
            ->removeIndexByName('fk_ship_order_customer_messages_user_id')
            ->update();
        $this->table('order_customer_messages')
            ->addForeignKeyWithName('fk_ship_order_customer_messages_author_id', 'author_id', 'users', 'id', ['delete' => 'SET_NULL', 'update' => 'CASCADE'])
            ->addIndex(['author_id'], ['name' => 'fk_ship_order_customer_messages_author_id'])
            ->update();
    }

    public function down()
    {
        $this->table('order_customer_messages')
            ->removeIndexByName('fk_ship_order_customer_messages_author_id')
            ->dropForeignKey('author_id', 'fk_ship_order_customer_messages_author_id')
            ->update();
        $this->table('order_customer_messages')
            ->changeColumn('author_id', 'integer')
            ->addForeignKeyWithName('fk_ship_order_customer_messages_user_id', 'author_id', 'users', 'id', ['delete' => 'NO_ACTION', 'update' => 'CASCADE'])
            ->addIndex(['author_id'], ['name' => 'fk_ship_order_customer_messages_user_id'])
            ->update();

        $this->table('order_comments')
            ->dropForeignKey('author_id', 'fk_ship_order_comments_author_id')
            ->removeIndexByName('fk_ship_order_comments_author_id')
            ->update();
        $this->table('order_comments')
            ->changeColumn('author_id', 'integer')
            ->addForeignKeyWithName('fk_ship_order_comments_user_id', 'author_id', 'users', 'id', ['delete' => 'NO_ACTION', 'update' => 'CASCADE'])
            ->addIndex(['author_id'], ['name' => 'fk_ship_order_comments_user_id'])
            ->update();

        $this->table('manufacturer_retailer_notes')
            ->dropForeignKey('author_id', 'fk_ship_manufacturer_retailer_notes_author_id')
            ->update();
        $this->table('manufacturer_retailer_notes')
            ->changeColumn('author_id', 'integer')
            ->addForeignKeyWithName('fk_ship_manufacturer_retailer_notes_author_id', 'author_id', 'users', 'id', ['delete' => 'NO_ACTION', 'update' => 'CASCADE'])
            ->update();
    }
}
