<?php

use Phinx\Migration\AbstractMigration;

class Ship3160TaxIncludedInPrices extends AbstractMigration
{
    public function change()
    {
        $this->table('country_taxes')
            ->addColumn('included_in_prices', 'boolean', ['default' => false, 'after' => 'includes_shipping'])
            ->update();
        $this->table('user_country_taxes')
            ->addColumn('included_in_prices', 'boolean', ['default' => false, 'after' => 'includes_shipping'])
            ->update();
    }
}
