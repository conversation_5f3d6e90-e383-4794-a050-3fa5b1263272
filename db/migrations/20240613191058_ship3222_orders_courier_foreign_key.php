<?php

use Phinx\Migration\AbstractMigration;

class Ship3222OrdersCourierForeignKey extends AbstractMigration
{
    public function up()
    {
        $this->table('orders')
            ->changeColumn('courier', 'integer', ['null' => true])
            ->update();

        $sql = <<<'SQL'
UPDATE `ship_orders` AS `Order`
SET `Order`.`courier` = NULL
WHERE NOT EXISTS(
    SELECT *
    FROM `ship_couriers` AS `Courier`
    WHERE `Courier`.`id` = `Order`.`courier`
)
SQL;
        $this->execute(trim(preg_replace('/\s+/', ' ', $sql)));

        $this->table('orders')
            ->addForeignKeyWithName('fk_ship_orders_courier', 'courier', 'couriers', 'id', ['delete' => 'SET_NULL', 'update' => 'CASCADE'])
            ->update();
    }

    public function down()
    {
        $this->table('orders')
            ->dropForeignKey('courier', 'fk_ship_orders_courier')
            ->removeIndexByName('fk_ship_orders_courier')
            ->update();

        $this->table('orders')
            ->changeColumn('courier', 'string', ['limit' => 50])
            ->update();
    }
}
