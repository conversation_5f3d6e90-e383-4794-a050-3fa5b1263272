<?php

use Phinx\Migration\AbstractMigration;

class Ship3197ProductAncillaryFeeExportHasTooManyColumns extends AbstractMigration
{
    public function up()
    {
        $this->table('product_state_fees')
            ->addColumn('country_id', 'integer', [
                'null' => false,
                'after' => 'product_id',
            ])
            ->changeColumn('state_id', 'integer', [
                'null' => true,
            ])
            ->update();

        $assignCountryIdsSql = <<<'SQL'
UPDATE `ship_product_state_fees`
INNER JOIN `ship_states` ON (`ship_states`.`id` = `ship_product_state_fees`.`state_id`)
SET `ship_product_state_fees`.`country_id` = `ship_states`.`country_id`
SQL;
        $this->execute(trim(preg_replace('/\s+/', ' ', $assignCountryIdsSql)));

        $this->table('product_state_fees')
            ->addForeignKeyWithName('fk_ship_product_state_fees_country_id', 'country_id', 'countries', 'id', ['update'=> 'CASCADE', 'delete'=> 'CASCADE'])
            ->update();
    }

    public function down()
    {
        $this->table('product_state_fees')
            ->dropForeignKey('country_id', 'fk_ship_product_state_fees_country_id')
            ->removeIndexByName('fk_ship_product_state_fees_country_id')
            ->update();

        $this->execute('DELETE FROM `ship_product_state_fees` WHERE `state_id` IS NULL');
        $this->table('product_state_fees')
            ->dropForeignKey('state_id', 'fk_ship_product_state_fees_state_id')
            ->update();

        $this->table('product_state_fees')
            ->changeColumn('state_id', 'integer', [
                'null' => false,
            ])
            ->removeColumn('country_id')
            ->update();

        $this->table('product_state_fees')
            ->addForeignKeyWithName('fk_ship_product_state_fees_state_id', 'state_id', 'states', 'id', ['update'=> 'CASCADE', 'delete'=> 'CASCADE'])
            ->update();
    }
}
