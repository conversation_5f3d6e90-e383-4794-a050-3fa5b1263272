<?php

use Phinx\Migration\AbstractMigration;

class Ship3086ProductRetailersResetIndex extends AbstractMigration
{
    public function up()
    {
        $this->table('product_retailers')
            ->addIndex(['user_id', 'inventoryItemId', 'inventory_count'], ['name' => 'index_user_id_inventoryItemId_inventory_count'])
            ->removeIndexByName('fk_ship_product_retailers_user_id')
            ->update();
    }

    public function down()
    {
        $this->table('product_retailers')
            ->addIndex(['user_id'], ['name' => 'fk_ship_product_retailers_user_id'])
            ->removeIndexByName('index_user_id_inventoryItemId_inventory_count')
            ->update();
    }
}
