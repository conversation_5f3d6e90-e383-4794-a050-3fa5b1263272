<?php

use Phinx\Migration\AbstractMigration;

class Ship3051UsersFbpixelAccessToken extends AbstractMigration
{
    public function change()
    {
        $this->table('users')
            ->addColumn('fbpixel_access_token', 'string', [
                'limit' => 255,
                'default' => '',
                'after' => 'fbpixelId',
            ])
            ->addColumn('fbpixel_test_event_code', 'string', [
                'limit' => 50,
                'default' => '',
                'after' => 'fbpixel_access_token',
            ])
            ->update();
    }
}
