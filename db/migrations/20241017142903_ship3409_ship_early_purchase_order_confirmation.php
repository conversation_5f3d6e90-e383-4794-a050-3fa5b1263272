<?php

use Phinx\Migration\AbstractMigration;

class Ship3409ShipEarlyPurchaseOrderConfirmation extends AbstractMigration
{
    public function up()
    {
        $content = <<<'HTML'
<p>Hello</p>
<p>{brand_name}</p>
<p>{retailer_name}</p>
<p>{order_summary}</p>
<p>{payment_method}</p>
<p>{order_id}</p>
<p>{site_name}</p>
<meta name="skip-new-line-conversion">
HTML;
        $rows = [
            [
                'template_name' => 'Purchase Order Confirmation',
                'description' => 'Notifies the retailer when the brand confirms a purchase order',
                'subject' => '{order_id} confirmed by {brand_name}',
                'content' => $content,
            ],
        ];

        $this->table('email_templates')->insert($rows)->save();
    }

    public function down()
    {
        $this->execute(<<<'SQL'
DELETE FROM `ship_email_templates` WHERE `template_name` = 'Purchase Order Confirmation';
ALTER TABLE `ship_email_templates` AUTO_INCREMENT = 1;
SQL
        );
    }
}
