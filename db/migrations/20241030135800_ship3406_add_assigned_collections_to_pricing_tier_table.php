<?php

use Phinx\Migration\AbstractMigration;

class Ship3406AddAssignedCollectionsToPricingTierTable extends AbstractMigration
{
    public function change()
    {
        $table = $this->table('pricing_tiers_collections', ['id' => true, 'primary_key' => ['id']]);
        $table->addColumn('pricing_tier_id', 'integer', ['null' => false])
              ->addColumn('collection_id', 'integer', ['null' => false])
              ->addColumn('created_at', 'datetime', ['default' => 'CURRENT_TIMESTAMP'])
              ->addIndex(['pricing_tier_id', 'collection_id'], ['unique' => true, 'name' => 'uindex_pricing_tier_id_collection_id'])
              ->addForeignKey('pricing_tier_id', 'pricing_tiers', 'id', ['delete'=> 'CASCADE', 'update'=> 'CASCADE'])
              ->addForeignKey('collection_id', 'collections', 'id', ['delete'=> 'CASCADE', 'update'=> 'CASCADE'])
              ->create();
    }
}
