<?php

use Phinx\Migration\AbstractMigration;

class Ship3441DragAndDropProductOrderInCollection extends AbstractMigration
{
    public function up() 
    {
        $this->table('collections_products')
            ->addColumn('product_order', 'integer', [
                'default' => 0,
                'limit' => 11,
                'null' => false,
                'after' => 'product_id',
            ])
            ->update();
    }

    public function down()
    {
        $this->table('collections_products')
             ->removeColumn('product_order')
             ->update();
    }
}
