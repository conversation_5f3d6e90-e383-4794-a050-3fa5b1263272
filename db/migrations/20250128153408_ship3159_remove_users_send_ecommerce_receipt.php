<?php

use Phinx\Migration\AbstractMigration;

class Ship3159RemoveUsersSendEcommerceReceipt extends AbstractMigration
{
    public function up()
    {
        $this->table('users')
            ->removeColumn('send_ecommerce_receipt')
            ->update();
    }

    public function down()
    {
        $this->table('users')
            ->addColumn('send_ecommerce_receipt', 'boolean', [
                'default' => true,
                'after' => 'enable_product_discovery',
            ])
            ->update();
    }
}
