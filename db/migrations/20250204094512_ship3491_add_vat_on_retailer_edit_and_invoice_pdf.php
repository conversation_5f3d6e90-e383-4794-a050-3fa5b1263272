<?php

use Phinx\Migration\AbstractMigration;

class Ship3491AddVatOnRetailerEditAndInvoicePdf extends AbstractMigration
{
    public function up()
    {
        $table = $this->table('manufacturer_retailers');
        $table->addColumn('vat_number', 'string', [
            'limit' => 50,
            'default' => null,
            'null' => true,
            'after' => 'b2b_tax'
        ])
        ->update();
    }

    public function down()
    {
        $table = $this->table('manufacturer_retailers');
        $table->removeColumn('vat_number')
              ->update();
    }
}
