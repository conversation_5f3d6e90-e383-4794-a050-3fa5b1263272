<?php

use Phinx\Migration\AbstractMigration;

class Ship3496UserDomains extends AbstractMigration
{
    public function change()
    {
        $this->table('user_domains')
            ->addColumn('user_id', 'integer')
            ->addColumn('domain', 'string')
            ->addColumn('created_at', 'datetime', ['default' => 'CURRENT_TIMESTAMP'])
            ->addColumn('updated_at', 'datetime', ['default' => 'CURRENT_TIMESTAMP', 'update' => 'CURRENT_TIMESTAMP'])
            ->addIndex(['user_id', 'domain'], ['unique' => true, 'name' => 'unique_user_id_domain'])
            ->addForeignKey('user_id', 'users', 'id', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->create();

        $this->table('users')
            ->addColumn('checkout_domain_id', 'integer', ['null' => true, 'after' => 'shop_app_id'])
            ->addForeignKey('checkout_domain_id', 'user_domains', 'id', ['delete' => 'SET_NULL', 'update' => 'CASCADE'])
            ->update();
    }
}
