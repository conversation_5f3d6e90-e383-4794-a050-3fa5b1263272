<?php

use Phinx\Migration\AbstractMigration;

class Ship3560WarrantyOrder extends AbstractMigration
{

    public function change()
    {
        $table = $this->table('order_warranty_images');
        $table->addColumn('order_id', 'integer')
              ->addColumn('warranty_image', 'string')
              ->addColumn('created_at', 'datetime', ['default' => 'CURRENT_TIMESTAMP'])
              ->addIndex(['order_id', 'warranty_image'], ['unique' => true, 'name' => 'unique_order_id_warranty_image'])
              ->addForeignKey('order_id', 'orders', 'id', ['delete'=> 'CASCADE', 'update'=> 'CASCADE'])
              ->create();

        $table = $this->table('orders');
        $table->addColumn('serial_number', 'string', [
            'null' => true,
            'limit' => 100,
            'default' => null,
            'after' => 'purchase_order_number',
        ]);
        $table->update();
    }
}
