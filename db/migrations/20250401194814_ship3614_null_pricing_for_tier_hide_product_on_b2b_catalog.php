<?php

use Phinx\Migration\AbstractMigration;

class Ship3614NullPricingForTierHideProductOnB2bCatalog extends AbstractMigration
{
    public function up()
    {
        $this->table('product_tiers')
            ->changeColumn('dealer_price', 'decimal', [
                'precision' => 18,
                'scale' => 2,
                'null' => true,
            ])
            ->update();
    }

    public function down()
    {
        $this->execute('UPDATE `ship_product_tiers` SET `updated` = `updated`, `dealer_price` = 0.00 WHERE `dealer_price` IS NULL');
        $this->table('product_tiers')
            ->changeColumn('dealer_price', 'decimal', [
                'precision' => 18,
                'scale' => 2,
                'null' => false,
            ])
            ->update();
    }
}
