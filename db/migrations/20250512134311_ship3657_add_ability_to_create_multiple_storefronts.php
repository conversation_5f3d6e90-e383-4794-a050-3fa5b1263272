<?php

use Phinx\Migration\AbstractMigration;

class Ship3657AddAbilityToCreateMultipleStorefronts extends AbstractMigration
{
    public function change(): void
    {
        $this->table('storefront_sets')
            ->addColumn('user_id', 'integer')
            ->addColumn('name', 'string', ['limit' => 255])
            ->addColumn('created', 'datetime', ['default' => 'CURRENT_TIMESTAMP'])
            ->addColumn('modified', 'datetime', ['default' => 'CURRENT_TIMESTAMP'])
            ->addForeignKey('user_id', 'users', 'id', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->create();

        $this->table('storefronts')
            ->addColumn('storefront_set_id', 'integer', ['null' => true, 'after' => 'user_id'])
            ->update();
        if ($this->isMigratingUp()) {
            $this->execute(<<<'SQL'
INSERT INTO `ship_storefront_sets` (`user_id`, `name`)
SELECT DISTINCT `user_id`, 'Default' AS `name`
FROM `ship_storefronts`
WHERE `storefront_set_id` IS NULL
SQL
            );
            $this->execute(<<<'SQL'
UPDATE `ship_storefronts` AS `Storefront`
INNER JOIN `ship_storefront_sets` AS `StorefrontSet` ON (`StorefrontSet`.`user_id` = `Storefront`.`user_id`)
SET `Storefront`.`storefront_set_id` = `StorefrontSet`.`id`
WHERE `Storefront`.`storefront_set_id` IS NULL
SQL
            );
            $this->table('storefronts')
                ->changeColumn('storefront_set_id', 'integer', ['null' => false])
                ->update();
        }
        $this->table('storefronts')
            ->addForeignKey('storefront_set_id', 'storefront_sets', 'id', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->update();

        $this->table('pricing_tiers_storefronts', ['id' => true, 'primary_key' => ['id']])
            ->addColumn('pricing_tier_id', 'integer', ['null' => false])
            ->addColumn('storefront_set_id', 'integer', ['null' => false])
            ->addColumn('created_at', 'datetime', ['default' => 'CURRENT_TIMESTAMP'])
            ->addIndex(['pricing_tier_id', 'storefront_set_id'], ['unique' => true, 'name' => 'uindex_pricing_tier_id_storefront_set_id'])
            ->addForeignKey('pricing_tier_id', 'pricing_tiers', 'id', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->addForeignKey('storefront_set_id', 'storefront_sets', 'id', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->create();
    }

    public function execute($sql)
    {
        return parent::execute(trim(preg_replace('/\s+/', ' ', $sql)));
    }
}
