<?php

use Phinx\Migration\AbstractMigration;

class Ship3657RenameStorefrontModels extends AbstractMigration
{
    public function up()
    {
        $this->table('storefronts')
            ->renameColumn('storefront_set_id', 'storefront_id')
            ->addIndex('storefront_id', ['name' => 'storefront_id'])
            ->removeIndexByName('storefront_set_id')
            ->update();

        $this->table('pricing_tiers_storefronts')
            ->renameColumn('storefront_set_id', 'storefront_id')
            ->addIndex('storefront_id', ['name' => 'storefront_id'])
            ->removeIndexByName('storefront_set_id')
            ->addIndex(['pricing_tier_id', 'storefront_id'], ['unique' => true, 'name' => 'unique_pricing_tier_id_storefront_id'])
            ->removeIndexByName('uindex_pricing_tier_id_storefront_set_id')
            ->update();

        $this->table('storefronts')->rename('ship_storefront_slides')->update();
        $this->execute("UPDATE `ship_i18n` SET `model` = 'StorefrontSlide', `updated_at` = `updated_at` WHERE `model` = 'Storefront';");
        $this->table('storefront_sets')->rename('ship_storefronts')->update();
        $this->execute("UPDATE `ship_i18n` SET `model` = 'Storefront', `updated_at` = `updated_at` WHERE `model` = 'StorefrontSet';");
    }

    public function down()
    {
        $this->table('storefronts')->rename('ship_storefront_sets')->update();
        $this->execute("UPDATE `ship_i18n` SET `model` = 'StorefrontSet', `updated_at` = `updated_at` WHERE `model` = 'Storefront';");
        $this->table('storefront_slides')->rename('ship_storefronts')->update();
        $this->execute("UPDATE `ship_i18n` SET `model` = 'Storefront', `updated_at` = `updated_at` WHERE `model` = 'StorefrontSlide';");

        $this->table('pricing_tiers_storefronts')
            ->renameColumn('storefront_id', 'storefront_set_id')
            ->addIndex('storefront_set_id', ['name' => 'storefront_set_id'])
            ->removeIndexByName('storefront_id')
            ->addIndex(['pricing_tier_id', 'storefront_set_id'], ['unique' => true, 'name' => 'uindex_pricing_tier_id_storefront_set_id'])
            ->removeIndexByName('unique_pricing_tier_id_storefront_id')
            ->update();

        $this->table('storefronts')
            ->renameColumn('storefront_id', 'storefront_set_id')
            ->addIndex('storefront_set_id', ['name' => 'storefront_set_id'])
            ->removeIndexByName('storefront_id')
            ->update();
    }
}
