<?php

use Phinx\Migration\AbstractMigration;

class Ship3661ContentTabMenus extends AbstractMigration
{
    public function change()
    {
        $table = $this->table('menus');
        $table->addColumn('user_id', 'integer')
              ->addColumn('name', 'string', ['limit' => 255])
              ->addColumn('created', 'datetime', ['default' => 'CURRENT_TIMESTAMP'])
              ->addColumn('modified', 'datetime', ['default' => 'CURRENT_TIMESTAMP'])
              ->addForeignKey('user_id', 'users', 'id', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
              ->create();

        $table = $this->table('menu_items');
        $table->addColumn('menu_id', 'integer')
            ->addColumn('user_id', 'integer')
            ->addColumn('label', 'string', ['limit' => 255])
            ->addColumn('url', 'string', ['limit' => 500])
            ->addColumn('menu_item_order', 'integer', ['default' => 0])
            ->addColumn('created', 'datetime', ['default' => 'CURRENT_TIMESTAMP'])
            ->addColumn('modified', 'datetime', ['default' => 'CURRENT_TIMESTAMP'])
            ->addForeignKey('menu_id', 'menus', 'id', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->addForeignKey('user_id', 'users', 'id', ['delete' => 'CASCADE', 'update' => 'CASCADE'])
            ->create();
    }
}
