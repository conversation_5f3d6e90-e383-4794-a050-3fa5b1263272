<?php

use Phinx\Migration\AbstractMigration;

class Ship3104CreateCartsTable extends AbstractMigration
{
    public function up()
    {
        $this->table('order_addresses')
            ->changeColumn('order_id', 'integer', ['null' => true])
            ->update();

        $this->table('carts')
            ->addColumn('uuid', 'uuid')
            ->addColumn('user_id', 'integer')
            ->addColumn('client_id', 'string', ['length' => 40, 'null' => true])
            ->addColumn('retailer_id', 'integer', ['null' => true])
            ->addColumn('shipping_address_id', 'integer', ['null' => true])
            ->addColumn('billing_address_id', 'integer', ['null' => true])
            ->addColumn('delivery_method', 'enum', ['values' => ['in_store', 'local_delivery', 'local_install', 'ship_from_store', 'sell_direct'], 'null' => true])
            ->addColumn('email_address', 'string', ['null' => true])
            ->addColumn('accepts_marketing', 'boolean', ['default' => false])
            ->addColumn('preferred_language', 'string', ['length' => 50, 'null' => true])
            ->addColumn('currency_code', 'char', ['limit' => 3])
            ->addColumn('shipping_amount', 'decimal', ['precision' => 13, 'scale' => 4])
            ->addColumn('shipping_tax_rate', 'decimal', ['precision' => 6, 'scale' => 5])
            ->addColumn('status', 'enum', ['values' => ['open', 'abandoned', 'closed'], 'default' => 'open'])
            ->addColumn('created_at', 'datetime', ['default' => 'CURRENT_TIMESTAMP'])
            ->addColumn('updated_at', 'datetime', ['default' => 'CURRENT_TIMESTAMP', 'update' => 'CURRENT_TIMESTAMP'])
            ->addIndex('uuid', ['unique' => true, 'name' => 'unique_uuid'])
            ->addForeignKeyWithName('fk_ship_carts_user_id', 'user_id', 'users', 'id', ['update' => 'CASCADE', 'delete' => 'CASCADE'])
            ->addForeignKeyWithName('fk_ship_carts_retailer_id', 'retailer_id', 'users', 'id', ['update' => 'CASCADE', 'delete' => 'CASCADE'])
            ->addForeignKeyWithName('fk_ship_carts_client_id', 'client_id', 'api_clients', 'client_id', ['update' => 'CASCADE', 'delete' => 'SET_NULL'])
            ->addForeignKeyWithName('fk_ship_carts_shipping_address_id', 'shipping_address_id', 'order_addresses', 'id', ['update' => 'CASCADE', 'delete' => 'SET_NULL'])
            ->addForeignKeyWithName('fk_ship_carts_billing_address_id', 'billing_address_id', 'order_addresses', 'id', ['update' => 'CASCADE', 'delete' => 'SET_NULL'])
            ->create();

        $this->table('cart_items')
            ->addColumn('cart_id', 'integer')
            ->addColumn('product_id', 'integer')
            ->addColumn('quantity', 'integer')
            ->addColumn('price', 'decimal', ['precision' => 13, 'scale' => 4])
            ->addColumn('compare_at_price', 'decimal', ['precision' => 13, 'scale' => 4, 'null' => true])
            ->addColumn('total_discount', 'decimal', ['precision' => 13, 'scale' => 4])
            ->addColumn('tax_rate', 'decimal', ['precision' => 6, 'scale' => 5])
            ->addColumn('created_at', 'datetime', ['default' => 'CURRENT_TIMESTAMP'])
            ->addColumn('updated_at', 'datetime', ['default' => 'CURRENT_TIMESTAMP', 'update' => 'CURRENT_TIMESTAMP'])
            ->addIndex(['cart_id', 'product_id'], ['unique' => true, 'name' => 'unique_cart_id_product_id'])
            ->addForeignKeyWithName('fk_ship_cart_items_cart_id', 'cart_id', 'carts', 'id', ['update' => 'CASCADE', 'delete' => 'CASCADE'])
            ->addForeignKeyWithName('fk_ship_cart_items_product_id', 'product_id', 'products', 'id', ['update' => 'CASCADE'])
            ->create();
    }

    public function down()
    {
        $this->table('cart_items')->drop()->save();

        $this->table('carts')->drop()->save();

        $this->execute('/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;');

        $this->execute('DELETE FROM `ship_order_addresses` WHERE `order_id` IS NULL');
        $this->table('order_addresses')
            ->changeColumn('order_id', 'integer', ['null' => false])
            ->update();

        $this->execute('/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;');
    }
}
