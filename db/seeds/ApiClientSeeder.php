<?php

use Phinx\Seed\AbstractSeed;

class ApiClientSeeder extends AbstractSeed
{
    public function getDependencies()
    {
        return array_merge(parent::getDependencies(), [
            'UserSeeder',
        ]);
    }

    public function run()
    {
        parent::run();
        $data = [
            [
                'client_id' => 'public_3d7f8754c15bcaab135fd5bc8ab72187',
                'client_secret' => 'secret_08738dfe60abb1fce0e8d0425f90fc4a',
                'user_id' => 7,
                'name' => 'Example Retailer App',
                'created_at' => '2024-01-19 19:01:01',
                'updated_at' => '2024-01-19 19:01:01',
            ],
            [
                'client_id' => 'public_353a3fe9e09d0e0ba732e435fe4c7a4b',
                'client_secret' => 'secret_8dd64794d7cfb1010a4dc3bd38b0e5c3',
                'user_id' => 8,
                'name' => 'Example App',
                'created_at' => '2023-08-02 15:16:07',
                'updated_at' => '2023-08-09 21:15:15',
            ],
            [
                'client_id' => 'public_27ddf8ab53dd55ece9d5c28a748013ef',
                'client_secret' => 'secret_de108dcc3046703dc2afc2429fe92439',
                'user_id' => 12,
                'name' => 'Test API Client 1',
                'created_at' => '2022-05-02 18:35:30',
                'updated_at' => '2022-05-02 18:35:30',
            ],
            [
                'client_id' => 'public_b0216fe11ceeef0746695318608890da',
                'client_secret' => 'secret_03b86bcfa7bfb25bb83c63e926120da4',
                'user_id' => 15,
                'name' => 'Test Retailer Client 1',
                'created_at' => '2023-08-04 14:29:57',
                'updated_at' => '2023-09-07 16:38:09',
            ],
            [
                'client_id' => 'public_a77f1682b70522ac9e0f81d9ba2f95f3',
                'client_secret' => 'secret_45983d067c61390e1b30206fa0fd2568',
                'user_id' => 19,
                'name' => 'ShipEarly WooCommerce Plugin',
                'created_at' => '2023-08-15 17:46:49',
                'updated_at' => '2023-08-15 17:46:49',
            ],
        ];

        $this
            ->getAdapter()
            ->getQueryBuilder()
            ->delete('ship_api_clients')
            ->execute();

        $this
            ->table('api_clients')
            ->insert($data)
            ->save();
    }
}
