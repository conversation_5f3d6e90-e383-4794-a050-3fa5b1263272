<?php

use Phinx\Seed\AbstractSeed;

class BrandStaffPermissionSeeder extends AbstractSeed
{
    public function getDependencies()
    {
        return array_merge(parent::getDependencies(), [
            'BrandStaffSeeder',
        ]);
    }

    public function run()
    {
        parent::run();
        $data = [
            [
                'id' => '1',
                'brand_staff_id' => '1',
                'name' => 'products',
                'level' => '1',
                'created_at' => '2020-10-28 19:59:27',
                'updated_at' => '2020-10-28 19:59:27',
            ],
            [
                'id' => '2',
                'brand_staff_id' => '1',
                'name' => 'orders',
                'level' => '2',
                'created_at' => '2020-10-28 19:59:27',
                'updated_at' => '2020-10-28 19:59:27',
            ],
            [
                'id' => '3',
                'brand_staff_id' => '1',
                'name' => 'retailers',
                'level' => '0',
                'created_at' => '2020-10-28 19:59:27',
                'updated_at' => '2020-10-28 19:59:27',
            ],
            [
                'id' => '4',
                'brand_staff_id' => '2',
                'name' => 'products',
                'level' => '1',
                'created_at' => '2020-10-28 19:59:27',
                'updated_at' => '2020-10-28 19:59:27',
            ],
            [
                'id' => '5',
                'brand_staff_id' => '2',
                'name' => 'orders',
                'level' => '2',
                'created_at' => '2020-10-28 19:59:27',
                'updated_at' => '2020-10-28 19:59:27',
            ],
            [
                'id' => '6',
                'brand_staff_id' => '2',
                'name' => 'retailers',
                'level' => '0',
                'created_at' => '2020-10-28 19:59:27',
                'updated_at' => '2020-10-28 19:59:27',
            ],
            [
                'id' => '7',
                'brand_staff_id' => '3',
                'name' => 'products',
                'level' => '1',
                'created_at' => '2020-10-28 19:59:27',
                'updated_at' => '2020-10-28 19:59:27',
            ],
            [
                'id' => '8',
                'brand_staff_id' => '3',
                'name' => 'orders',
                'level' => '2',
                'created_at' => '2020-10-28 19:59:27',
                'updated_at' => '2020-10-28 19:59:27',
            ],
            [
                'id' => '9',
                'brand_staff_id' => '3',
                'name' => 'retailers',
                'level' => '0',
                'created_at' => '2020-10-28 19:59:27',
                'updated_at' => '2020-10-28 19:59:27',
            ],
        ];

        $this->getAdapter()
            ->getQueryBuilder()
            ->delete('ship_brand_staff_permissions')
            ->whereInList('id', array_column($data, 'id'))
            ->execute();

        $this->table('brand_staff_permissions')
            ->insert($data)
            ->save();
    }
}
