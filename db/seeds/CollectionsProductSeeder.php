<?php

use Phinx\Seed\AbstractSeed;

class CollectionsProductSeeder extends AbstractSeed
{
    public function getDependencies()
    {
        return array_merge(parent::getDependencies(), [
            'CollectionSeeder',
            'ProductSeeder',
        ]);
    }

    public function run()
    {
        parent::run();
        $data = [
            [
                'id' => '1',
                'collection_id' => '1',
                'product_id' => '20',
                'created_at' => '2020-06-03 15:23:17',
            ],
            [
                'id' => '2',
                'collection_id' => '1',
                'product_id' => '57',
                'created_at' => '2020-06-03 15:23:17',
            ],
            [
                'id' => '3',
                'collection_id' => '1',
                'product_id' => '21',
                'created_at' => '2020-06-03 15:23:17',
            ],
            [
                'id' => '4',
                'collection_id' => '1',
                'product_id' => '23',
                'created_at' => '2020-06-03 15:23:17',
            ],
            [
                'id' => '5',
                'collection_id' => '1',
                'product_id' => '34',
                'created_at' => '2020-06-03 15:23:17',
            ],
            [
                'id' => '6',
                'collection_id' => '1',
                'product_id' => '37',
                'created_at' => '2020-06-03 15:23:17',
            ],
            [
                'id' => '7',
                'collection_id' => '1',
                'product_id' => '58',
                'created_at' => '2020-06-03 15:23:17',
            ],
            [
                'id' => '8',
                'collection_id' => '2',
                'product_id' => '24',
                'created_at' => '2020-06-03 15:23:35',
            ],
            [
                'id' => '9',
                'collection_id' => '2',
                'product_id' => '25',
                'created_at' => '2020-06-03 15:23:35',
            ],
            [
                'id' => '10',
                'collection_id' => '2',
                'product_id' => '26',
                'created_at' => '2020-06-03 15:23:35',
            ],
            [
                'id' => '11',
                'collection_id' => '2',
                'product_id' => '27',
                'created_at' => '2020-06-03 15:23:35',
            ],
            [
                'id' => '12',
                'collection_id' => '2',
                'product_id' => '31',
                'created_at' => '2020-06-03 15:23:35',
            ],
            [
                'id' => '13',
                'collection_id' => '2',
                'product_id' => '28',
                'created_at' => '2020-06-03 15:23:35',
            ],
            [
                'id' => '14',
                'collection_id' => '2',
                'product_id' => '29',
                'created_at' => '2020-06-03 15:23:35',
            ],
            [
                'id' => '15',
                'collection_id' => '2',
                'product_id' => '39',
                'created_at' => '2020-06-03 15:23:35',
            ],
            [
                'id' => '16',
                'collection_id' => '2',
                'product_id' => '40',
                'created_at' => '2020-06-03 15:23:35',
            ],
            [
                'id' => '17',
                'collection_id' => '2',
                'product_id' => '30',
                'created_at' => '2020-06-03 15:23:35',
            ],
            [
                'id' => '18',
                'collection_id' => '2',
                'product_id' => '32',
                'created_at' => '2020-06-03 15:23:35',
            ],
            [
                'id' => '19',
                'collection_id' => '2',
                'product_id' => '33',
                'created_at' => '2020-06-03 15:23:35',
            ],
            [
                'id' => '20',
                'collection_id' => '2',
                'product_id' => '41',
                'created_at' => '2020-06-03 15:23:35',
            ],
            [
                'id' => '21',
                'collection_id' => '2',
                'product_id' => '42',
                'created_at' => '2020-06-03 15:23:35',
            ],
            [
                'id' => '22',
                'collection_id' => '3',
                'product_id' => '25',
                'created_at' => '2020-06-03 15:23:35',
            ],
        ];
        $this
            ->getAdapter()
            ->getQueryBuilder()
            ->delete('ship_collections_products')
            ->execute();
        $this
            ->table('collections_products')
            ->insert($data)
            ->save();
    }
}
