<?php

use Cake\Database\StatementInterface;
use Phinx\Seed\AbstractSeed;

class DealerOrderRefundSeeder extends AbstractSeed
{
    public function getDependencies()
    {
        return array_merge(parent::getDependencies(), [
            'DealerOrderSeeder',
        ]);
    }

    public function run()
    {
        $dealerOrderCaseIds = $this->fetchDealerOrderCaseIds();

        $data = array_values([
            'split_payment_refunded_1_of_2' => [
                'DealerOrderRefund' => [
                    'id' => '1',
                    'dealer_order_id' => $dealerOrderCaseIds['split_payment_refunded'],
                    'source_id' => null,
                    'transaction_id' => 'fr_fake1of2',
                    'amount' => '631.95',
                    'shipping_portion' => '0.00',
                    'tax_portion' => '32.95',
                    'reason' => null,
                    'created_at' => '2020-03-15 23:59:59',
                    'updated_at' => '2020-03-15 23:59:59',
                ],
                'DealerOrderRefundProduct' => array_map(
                    function($row) {
                        return [
                            'dealer_order_product_id' => $row['id'],
                            'quantity' => $row['quantity'],
                            'created_at' => '2020-03-15 23:59:59',
                            'updated_at' => '2020-03-15 23:59:59',
                        ];
                    },
                    $this->fetchDealerOrderProducts($dealerOrderCaseIds['split_payment_refunded'], ['id', '(1) AS quantity'])
                ),
            ],
            'api_wholesale' => [
                'DealerOrderRefund' => [
                    'id' => '2',
                    'dealer_order_id' => $dealerOrderCaseIds['api_wholesale'],
                    'source_id' => null,
                    'transaction_id' => 'Fake_tx_id1234',
                    'amount' => '36.92',
                    'shipping_portion' => '15.00',
                    'tax_portion' => '1.93',
                    'reason' => null,
                    'created_at' => '2020-03-16 00:00:00',
                    'updated_at' => '2020-03-16 00:00:00',
                ],
                'DealerOrderRefundProduct' => [
                    [
                        'dealer_order_product_id' => '17',
                        'quantity' => '1',
                        'created_at' => '2020-03-16 00:00:00',
                        'updated_at' => '2020-03-16 00:00:00',
                    ],
                ],
            ],
            'split_payment_refunded_2_of_2' => [
                'DealerOrderRefund' => [
                    'id' => '3',
                    'dealer_order_id' => $dealerOrderCaseIds['split_payment_refunded'],
                    'source_id' => null,
                    'transaction_id' => 'fr_fake2of2',
                    'amount' => '647.77',
                    'shipping_portion' => '15.00',
                    'tax_portion' => '33.77',
                    'reason' => null,
                    'created_at' => '2020-03-16 00:00:00',
                    'updated_at' => '2020-03-16 00:00:00',
                ],
                'DealerOrderRefundProduct' => array_map(
                    function($row) {
                        return [
                            'dealer_order_product_id' => $row['id'],
                            'quantity' => $row['quantity'],
                            'created_at' => '2020-03-16 00:00:00',
                            'updated_at' => '2020-03-16 00:00:00',
                        ];
                    },
                    array_filter($this->fetchDealerOrderProducts($dealerOrderCaseIds['split_payment_refunded'], ['id', '(product_quantity - 1) AS quantity']), function($row) {
                        return $row['quantity'] > 0;
                    })
                ),
            ],
        ]);

        $dealerOrderRefunds = array_column($data, 'DealerOrderRefund');
        $this->getAdapter()
             ->getQueryBuilder()
             ->delete('ship_dealer_order_refunds')
             ->whereInList('id', array_column($dealerOrderRefunds, 'id'))
             ->execute();
        $this->table('dealer_order_refunds')
             ->insert($dealerOrderRefunds)
             ->save();

        $dealerOrderRefundProducts = array_reduce($data, function($list, $record) {
            $dealer_order_refund_id = $record['DealerOrderRefund']['id'];

            foreach ($record['DealerOrderRefundProduct'] as $orderProduct) {
                $list[] = $orderProduct + compact('dealer_order_refund_id');
            }

            return $list;
        }, []);
        $this->table('dealer_order_refund_products')
             ->insert($dealerOrderRefundProducts)
             ->save();
    }

    private function fetchDealerOrderCaseIds(): array
    {
        $orderCaseIds = OrderSeeder::CASE_IDS;

        $rows = $this->getAdapter()
            ->getQueryBuilder()
            ->select(['id', 'order_id'])
            ->from('ship_dealer_orders')
            ->whereInList('order_id', $orderCaseIds)
            ->execute()
            ->fetchAll(StatementInterface::FETCH_TYPE_ASSOC);

        $dealerOrderIdByOrderId = array_combine(array_column($rows, 'order_id'), array_column($rows, 'id'));

        return array_map(
            function($orderId) use ($dealerOrderIdByOrderId) {
                return $dealerOrderIdByOrderId[$orderId];
            },
            array_intersect($orderCaseIds, array_keys($dealerOrderIdByOrderId))
        );
    }

    private function fetchDealerOrderProducts($dealerOrderId, array $fields = ['*']): array
    {
        return $this->getAdapter()
            ->getQueryBuilder()
            ->select($fields)
            ->from('ship_dealer_order_products')
            ->where(['dealer_order_id' => $dealerOrderId])
            ->execute()
            ->fetchAll(StatementInterface::FETCH_TYPE_ASSOC);
    }
}
