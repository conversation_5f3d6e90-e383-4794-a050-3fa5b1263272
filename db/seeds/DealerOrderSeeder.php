<?php

use Phinx\Seed\AbstractSeed;

class DealerOrderSeeder extends AbstractSeed
{
    public function getDependencies()
    {
        return array_merge(parent::getDependencies(), [
            'InventoryTransferSeeder',
            'OrderSeeder',
            'ProductSeeder',
            'WarehouseSeeder',
        ]);
    }

    public function run()
    {
        $data = array_values([
            'split_payment' => [
                'DealerOrder' => [
                    'id' => '10',
                    'user_id' => '8',
                    'order_id' => OrderSeeder::CASE_IDS['split_payment'],
                    'source_id' => null,
                    'orderNO' => null,
                    'source_order_name' => null,
                    'total_price' => '1279.72',
                    'product_total_price' => '1198.00',
                    'total_discount' => '0.00',
                    'tax' => '5.50000',
                    'total_tax' => '66.72',
                    'shipping_name' => 'Domestic Price Rate',
                    'shipping_amount' => '15.00',
                    'shipping_tax_amount' => '0.83',
                    'splitpayment_percentage' => '2.00',
                    'splitpayment_amount' => '25.59',
                    'wholesale_charge_id' => '',
                    'wholesale_charge_amount' => '0.00',
                    'product_details' => '{"products":{"21":{"quantity":2,"dealer_price":"499.00"},"23":{"quantity":2,"dealer_price":"100.00"}},"currencytype":"CAD","b2b_tax":"5.5000","shipping_amount":"15.00","b2b_shipping_tax_option":1,"splitpayment_percentage":"2.00","splitpayment_amount":"25.59","shipment_date":"2020-03-01 00:00:00"}',
                    'payment_status' => '0',
                    'payment_method' => '',
                    'payment_reference_id' => '',
                    'fulfillment_status' => 'fulfilled',
                    'shipment_date' => '2020-03-01 00:00:00',
                    'created_at' => '2020-02-29 23:59:59',
                    'updated_at' => '2020-03-01 00:00:00',
                ],
                'DealerOrderProduct' => [
                    [
                        'product_id' => '21',
                        'warehouse_id' => null,
                        'inventory_transfer_id' => null,
                        'product_price' => '499.00',
                        'product_quantity' => '2',
                        'total_discount' => '0.00',
                        'tax' => '5.50000',
                        'created_at' => '2020-02-29 23:59:59',
                        'updated_at' => '2020-02-29 23:59:59',
                    ],
                    [
                        'product_id' => '23',
                        'warehouse_id' => null,
                        'inventory_transfer_id' => null,
                        'product_price' => '100.00',
                        'product_quantity' => '2',
                        'total_discount' => '0.00',
                        'tax' => '5.50000',
                        'created_at' => '2020-02-29 23:59:59',
                        'updated_at' => '2020-02-29 23:59:59',
                    ],
                ],
            ],
            'split_payment_refunded' => [
                'DealerOrder' => [
                    'id' => '11',
                    'user_id' => '8',
                    'order_id' => OrderSeeder::CASE_IDS['split_payment_refunded'],
                    'source_id' => null,
                    'orderNO' => null,
                    'source_order_name' => null,
                    'total_price' => '1279.72',
                    'product_total_price' => '1198.00',
                    'total_discount' => '0.00',
                    'tax' => '5.50000',
                    'total_tax' => '66.72',
                    'shipping_name' => 'Domestic Price Rate',
                    'shipping_amount' => '15.00',
                    'shipping_tax_amount' => '0.83',
                    'splitpayment_percentage' => '2.00',
                    'splitpayment_amount' => '25.59',
                    'wholesale_charge_id' => '',
                    'wholesale_charge_amount' => '0.00',
                    'product_details' => '{"products":{"21":{"quantity":2,"dealer_price":"499.00"},"23":{"quantity":2,"dealer_price":"100.00"}},"currencytype":"CAD","b2b_tax":"5.5000","shipping_amount":"15.00","b2b_shipping_tax_option":1,"splitpayment_percentage":"2.00","splitpayment_amount":"25.59","shipment_date":"2020-03-15 23:59:59"}',
                    'payment_status' => '0',
                    'payment_method' => '',
                    'payment_reference_id' => '',
                    'fulfillment_status' => 'fulfilled',
                    'shipment_date' => '2020-03-15 23:59:59',
                    'created_at' => '2020-03-15 19:37:34',
                    'updated_at' => '2020-03-16 00:00:00',
                ],
                'DealerOrderProduct' => [
                    [
                        'product_id' => '21',
                        'warehouse_id' => null,
                        'inventory_transfer_id' => null,
                        'product_price' => '499.00',
                        'product_quantity' => '2',
                        'total_discount' => '0.00',
                        'tax' => '5.50000',
                        'created_at' => '2020-03-15 19:37:34',
                        'updated_at' => '2020-03-15 19:37:34',
                    ],
                    [
                        'product_id' => '23',
                        'warehouse_id' => null,
                        'inventory_transfer_id' => null,
                        'product_price' => '100.00',
                        'product_quantity' => '2',
                        'total_discount' => '0.00',
                        'tax' => '5.50000',
                        'created_at' => '2020-03-15 19:37:34',
                        'updated_at' => '2020-03-15 19:37:34',
                    ],
                ],
            ],
            'api_wholesale' => [
                'DealerOrder' => [
                    'id' => '12',
                    'user_id' => '12',
                    'order_id' => OrderSeeder::CASE_IDS['api_wholesale'],
                    'source_id' => null,
                    'orderNO' => null,
                    'source_order_name' => null,
                    'total_price' => '119.98',
                    'product_total_price' => '148.48',
                    'total_discount' => '0.00',
                    'tax' => '5.50000',
                    'total_tax' => '13.50',
                    'shipping_name' => null,
                    'shipping_amount' => '15.00',
                    'shipping_tax_amount' => '0.83',
                    'splitpayment_percentage' => '2.00',
                    'splitpayment_amount' => '2.4',
                    'wholesale_charge_id' => '',
                    'wholesale_charge_amount' => '0.00',
                    'product_details' => '{"products":{"48":{"quantity":5,"dealer_price":"40.00"},"53":{"quantity":5,"dealer_price":"19.99"}},"currencytype":"CAD","b2b_tax":"5.5000","shipping_amount":"15.00","b2b_shipping_tax_option":1,"splitpayment_percentage":"2.00","splitpayment_amount":"2.4","shipment_date":"2020-03-15 23:59:59"}',
                    'payment_status' => '0',
                    'payment_method' => '',
                    'payment_reference_id' => '',
                    'fulfillment_status' => 'partially_fulfilled',
                    'shipment_date' => '2020-03-15 23:59:59',
                    'created_at' => '2020-03-15 19:37:34',
                    'updated_at' => '2020-03-16 00:00:00',
                ],
                'DealerOrderProduct' => [
                    [
                        'product_id' => '48',
                        'warehouse_id' => '5',
                        'inventory_transfer_id' => null,
                        'product_price' => '40.00',
                        'product_quantity' => '5',
                        'total_discount' => '0.00',
                        'tax' => '5.50000',
                        'created_at' => '2021-05-10 20:24:41',
                        'updated_at' => '2021-05-10 20:24:41',
                    ],
                    [
                        'product_id' => '53',
                        'warehouse_id' => '5',
                        'inventory_transfer_id' => null,
                        'product_price' => '19.99',
                        'product_quantity' => '5',
                        'total_discount' => '0.00',
                        'tax' => '5.50000',
                        'created_at' => '2021-05-10 20:24:41',
                        'updated_at' => '2021-05-10 20:24:41',
                    ],
                ]
            ],
            'wholesale_credit_order' => [
                'DealerOrder' => [
                    'id' => '13',
                    'user_id' => '12',
                    'order_id' => OrderSeeder::CASE_IDS['wholesale_credit_order'],
                    'source_id' => null,
                    'orderNO' => null,
                    'source_order_name' => null,
                    'total_price' => '119.98',
                    'product_total_price' => '148.48',
                    'total_discount' => '0.00',
                    'tax' => '5.50000',
                    'total_tax' => '13.50',
                    'shipping_name' => 'Domestic Price Rate',
                    'shipping_amount' => '15.00',
                    'shipping_tax_amount' => '0.83',
                    'splitpayment_percentage' => '2.00',
                    'splitpayment_amount' => '2.4',
                    'wholesale_charge_id' => '',
                    'wholesale_charge_amount' => '0.00',
                    'product_details' => '{"products":{"48":{"quantity":5,"dealer_price":"40.00"},"53":{"quantity":5,"dealer_price":"19.99"}},"currencytype":"CAD","b2b_tax":"5.5000","shipping_amount":"15.00","b2b_shipping_tax_option":1,"splitpayment_percentage":"2.00","splitpayment_amount":"2.4","shipment_date":"2020-03-15 23:59:59"}',
                    'payment_status' => '0',
                    'payment_method' => '',
                    'payment_reference_id' => '',
                    'fulfillment_status' => 'partially_fulfilled',
                    'shipment_date' => '2020-03-15 23:59:59',
                    'created_at' => '2020-03-15 19:37:34',
                    'updated_at' => '2020-03-16 00:00:00',
                ],
                'DealerOrderProduct' => [
                    [
                        'product_id' => '48',
                        'warehouse_id' => '5',
                        'inventory_transfer_id' => null,
                        'product_price' => '40.00',
                        'product_quantity' => '5',
                        'total_discount' => '0.00',
                        'tax' => '5.50000',
                        'created_at' => '2021-05-10 20:24:41',
                        'updated_at' => '2021-05-10 20:24:41',
                    ],
                    [
                        'product_id' => '53',
                        'warehouse_id' => '5',
                        'inventory_transfer_id' => null,
                        'product_price' => '19.99',
                        'product_quantity' => '5',
                        'total_discount' => '0.00',
                        'tax' => '5.50000',
                        'created_at' => '2021-05-10 20:24:41',
                        'updated_at' => '2021-05-10 20:24:41',
                    ],
                ]
            ],
        ]);

        $dealerOrders = array_column($data, 'DealerOrder');
        $this->table('dealer_orders')
             ->insert($dealerOrders)
             ->save();

        $rows = $this->getAdapter()
                     ->getQueryBuilder()
                     ->select(['order_id', 'id'])
                     ->from('ship_dealer_orders')
                     ->whereInList('order_id', array_column($dealerOrders, 'order_id'))
                     ->execute()
                     ->fetchAll();
        $idsByOrderId = array_combine(array_column($rows, 0), array_column($rows, 1));

        $dealerOrderProducts = array_reduce($data, function($list, $record) use ($idsByOrderId) {
            $user_id = $record['DealerOrder']['user_id'];
            $order_id = $record['DealerOrder']['order_id'];
            $dealer_order_id = $idsByOrderId[$order_id];
            foreach ($record['DealerOrderProduct'] as $dealerOrderProduct) {
                $list[] = $dealerOrderProduct + compact('user_id', 'order_id', 'dealer_order_id');
            }
            return $list;
        }, []);

        $this->table('dealer_order_products')
             ->insert($dealerOrderProducts)
             ->save();
    }
}
