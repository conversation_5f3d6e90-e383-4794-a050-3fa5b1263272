<?php

use Phinx\Seed\AbstractSeed;

class InventoryTransferProductSeeder extends AbstractSeed
{
    public function getDependencies()
    {
        return array_merge(parent::getDependencies(), [
            'InventoryTransferSeeder',
            'ProductSeeder',
        ]);
    }

    public function run()
    {
        parent::run();
        $data = [
            1 => [
                'id' => '1',
                'inventory_transfer_id' => '1',
                'product_id' => '23',
                'quantity' => '24',
                'accepted_quantity' => '0',
                'created_at' => '2021-04-27 14:25:32',
                'updated_at' => '2021-04-27 14:25:32',
            ],
            2 => [
                'id' => '2',
                'inventory_transfer_id' => '1',
                'product_id' => '21',
                'quantity' => '13',
                'accepted_quantity' => '0',
                'created_at' => '2021-04-27 14:25:32',
                'updated_at' => '2021-04-27 14:25:32',
            ],
            3 => [
                'id' => '3',
                'inventory_transfer_id' => '2',
                'product_id' => '23',
                'quantity' => '25',
                'accepted_quantity' => '25',
                'created_at' => '2021-04-27 14:25:32',
                'updated_at' => '2021-04-27 14:25:32',
            ],
            4 => [
                'id' => '4',
                'inventory_transfer_id' => '2',
                'product_id' => '21',
                'quantity' => '23',
                'accepted_quantity' => '23',
                'created_at' => '2021-04-27 14:25:32',
                'updated_at' => '2021-04-27 14:25:32',
            ],
            5 => [
                'id' => '5',
                'inventory_transfer_id' => '3',
                'product_id' => '20',
                'quantity' => '23',
                'accepted_quantity' => '0',
                'created_at' => '2021-04-27 14:25:32',
                'updated_at' => '2021-04-27 14:25:32',
            ],
            6 => [
                'id' => '6',
                'inventory_transfer_id' => '3',
                'product_id' => '21',
                'quantity' => '12',
                'accepted_quantity' => '0',
                'created_at' => '2021-04-27 14:25:32',
                'updated_at' => '2021-04-27 14:25:32',
            ],
            7 => [
                'id' => '7',
                'inventory_transfer_id' => '4',
                'product_id' => '29',
                'quantity' => '33',
                'accepted_quantity' => '0',
                'created_at' => '2021-04-27 14:25:32',
                'updated_at' => '2021-04-27 14:25:32',
            ],
            8 => [
                'id' => '8',
                'inventory_transfer_id' => '4',
                'product_id' => '25',
                'quantity' => '29',
                'accepted_quantity' => '0',
                'created_at' => '2021-04-27 14:25:32',
                'updated_at' => '2021-04-27 14:25:32',
            ],
            9 => [
                'id' => '9',
                'inventory_transfer_id' => '5',
                'product_id' => '29',
                'quantity' => '34',
                'accepted_quantity' => '34',
                'created_at' => '2021-04-27 14:25:32',
                'updated_at' => '2021-04-27 14:25:32',
            ],
            10 => [
                'id' => '10',
                'inventory_transfer_id' => '5',
                'product_id' => '25',
                'quantity' => '30',
                'accepted_quantity' => '30',
                'created_at' => '2021-04-27 14:25:32',
                'updated_at' => '2021-04-27 14:25:32',
            ],
            11 => [
                'id' => '11',
                'inventory_transfer_id' => '6',
                'product_id' => '29',
                'quantity' => '35',
                'accepted_quantity' => '0',
                'created_at' => '2021-04-27 14:25:32',
                'updated_at' => '2021-04-27 14:25:32',
            ],
            12 => [
                'id' => '12',
                'inventory_transfer_id' => '6',
                'product_id' => '25',
                'quantity' => '31',
                'accepted_quantity' => '0',
                'created_at' => '2021-04-27 14:25:32',
                'updated_at' => '2021-04-27 14:25:32',
            ],
            13 => [
                'id' => '13',
                'inventory_transfer_id' => '7',
                'product_id' => '53',
                'quantity' => '60',
                'accepted_quantity' => '0',
                'created_at' => '2021-04-27 14:25:32',
                'updated_at' => '2021-04-27 14:25:32',
            ],
            14 => [
                'id' => '14',
                'inventory_transfer_id' => '7',
                'product_id' => '48',
                'quantity' => '55',
                'accepted_quantity' => '0',
                'created_at' => '2021-04-27 14:25:32',
                'updated_at' => '2021-04-27 14:25:32',
            ],
            15 => [
                'id' => '15',
                'inventory_transfer_id' => '8',
                'product_id' => '53',
                'quantity' => '61',
                'accepted_quantity' => '61',
                'created_at' => '2021-04-27 14:25:32',
                'updated_at' => '2021-04-27 14:25:32',
            ],
            16 => [
                'id' => '16',
                'inventory_transfer_id' => '8',
                'product_id' => '48',
                'quantity' => '56',
                'accepted_quantity' => '56',
                'created_at' => '2021-04-27 14:25:32',
                'updated_at' => '2021-04-27 14:25:32',
            ],
            17 => [
                'id' => '17',
                'inventory_transfer_id' => '9',
                'product_id' => '53',
                'quantity' => '62',
                'accepted_quantity' => '0',
                'created_at' => '2021-04-27 14:25:32',
                'updated_at' => '2021-04-27 14:25:32',
            ],
            18 => [
                'id' => '18',
                'inventory_transfer_id' => '9',
                'product_id' => '48',
                'quantity' => '57',
                'accepted_quantity' => '0',
                'created_at' => '2021-04-27 14:25:32',
                'updated_at' => '2021-04-27 14:25:32',
            ],
        ];

        $this->getAdapter()
            ->getQueryBuilder()
            ->delete('ship_inventory_transfer_products')
            ->whereInList('id', array_column($data, 'id'))
            ->execute();

        $this->table('inventory_transfer_products')
             ->insert($data)
             ->save();
    }
}
