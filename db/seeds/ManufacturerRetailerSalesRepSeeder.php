<?php

use Phinx\Seed\AbstractSeed;

class ManufacturerRetailerSalesRepSeeder extends AbstractSeed
{
    public function getDependencies()
    {
        return array_merge(parent::getDependencies(), [
            'ManufacturerRetailerSeeder',
            'UserSeeder',
        ]);
    }

    public function run()
    {
        parent::run();
        $data = [
            1 => [
                'id' => '1',
                'manufacturer_retailer_id' => '3',
                'sales_rep_id' => '21',
                'created_at' => '2019-06-06 13:30:40',
                'updated_at' => '2019-06-06 13:30:40',
            ],
            2 => [
                'id' => '2',
                'manufacturer_retailer_id' => '18',
                'sales_rep_id' => '21',
                'created_at' => '2019-06-06 13:30:40',
                'updated_at' => '2019-06-06 13:30:40',
            ],
            3 => [
                'id' => '3',
                'manufacturer_retailer_id' => '6',
                'sales_rep_id' => '21',
                'created_at' => '2019-06-06 13:30:40',
                'updated_at' => '2019-06-06 13:30:40',
            ],
            4 => [
                'id' => '4',
                'manufacturer_retailer_id' => '19',
                'sales_rep_id' => '21',
                'created_at' => '2021-08-09 14:52:42',
                'updated_at' => '2021-08-09 14:52:42',
            ],
        ];

        $this->getAdapter()
            ->getQueryBuilder()
            ->delete('ship_manufacturer_retailer_sales_reps')
            ->whereInList('id', array_column($data, 'id'))
            ->execute();

        $this->table('manufacturer_retailer_sales_reps')
            ->insert($data)
            ->save();
    }
}
