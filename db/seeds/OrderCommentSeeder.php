<?php

use Phinx\Seed\AbstractSeed;

class OrderCommentSeeder extends AbstractSeed
{
    const USER_ID_RETAILER = '7';
    const USER_ID_BRAND = '8';
    const USER_ID_BRANCH = '17';
    const USER_ID_STAFF = '20';
    const USER_ID_SALES_REP = '21';

    public function getDependencies()
    {
        return array_merge(parent::getDependencies(), [
            'OrderSeeder',
            'UserSeeder',
        ]);
    }

    public function run()
    {
        parent::run();
        $data = array_values([
            [
                'id' => '1',
                'order_id' => OrderSeeder::CASE_IDS['split_payment_refunded'],
                'author_id' => static::USER_ID_BRAND,
                'author_name' => 'My Local Brand',
                'is_internal' => false,
                'body' => 'This is a brand comment',
                'created_at' => '2020-08-01 00:00:00',
                'updated_at' => '2020-08-01 00:00:00',
            ],
            [
                'id' => '2',
                'order_id' => OrderSeeder::CASE_IDS['split_payment_refunded'],
                'author_id' => static::USER_ID_RETAILER,
                'author_name' => 'Local Shop',
                'is_internal' => false,
                'body' => 'This is a retailer comment',
                'created_at' => '2020-08-30 00:00:00',
                'updated_at' => '2020-08-30 00:00:00',
            ],
            [
                'id' => '3',
                'order_id' => OrderSeeder::CASE_IDS['split_payment_refunded'],
                'author_id' => static::USER_ID_BRANCH,
                'author_name' => 'Local Shop Branch',
                'is_internal' => false,
                'body' => 'This is a branch comment',
                'created_at' => '2020-08-30 00:00:00',
                'updated_at' => '2020-08-30 00:00:00',
            ],
            [
                'id' => '4',
                'order_id' => OrderSeeder::CASE_IDS['split_payment_refunded'],
                'author_id' => static::USER_ID_STAFF,
                'author_name' => 'Aron Schmidt',
                'is_internal' => false,
                'body' => 'This is a retailer staff comment',
                'created_at' => '2020-08-30 23:59:59',
                'updated_at' => '2020-08-30 23:59:59',
            ],
            [
                'id' => '5',
                'order_id' => OrderSeeder::CASE_IDS['split_payment_refunded'],
                'author_id' => static::USER_ID_SALES_REP,
                'author_name' => 'Sales Rep',
                'is_internal' => false,
                'body' => 'This is a sales rep comment',
                'created_at' => '2020-09-01 00:00:00',
                'updated_at' => '2020-09-01 00:00:00',
            ],
            [
                'id' => '6',
                'order_id' => OrderSeeder::CASE_IDS['split_payment_refunded'],
                'author_id' => static::USER_ID_BRAND,
                'author_name' => 'My Local Brand',
                'is_internal' => true,
                'body' => 'This is an internal brand comment',
                'created_at' => '2021-03-19 15:56:46',
                'updated_at' => '2021-03-19 15:56:46',
            ],
            [
                'id' => '7',
                'order_id' => OrderSeeder::CASE_IDS['split_payment'],
                'author_id' => static::USER_ID_BRAND,
                'author_name' => 'My Local Brand',
                'is_internal' => true,
                'body' => 'This is order only has internal comments',
                'created_at' => '2021-03-19 15:56:46',
                'updated_at' => '2021-03-19 15:56:46',
            ],
        ]);
        $this
            ->getAdapter()
            ->getQueryBuilder()
            ->delete('ship_order_comments')
            ->execute();
        $this
            ->table('order_comments')
            ->insert($data)
            ->save();
    }
}
