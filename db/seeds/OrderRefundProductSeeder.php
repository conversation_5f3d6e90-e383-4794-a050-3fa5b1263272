<?php

use Phinx\Seed\AbstractSeed;

class OrderRefundProductSeeder extends AbstractSeed
{
    public function getDependencies()
    {
        return array_merge(parent::getDependencies(), [
            'OrderSeeder', // Source of order_products
            'OrderRefundSeeder',
        ]);
    }

    public function run()
    {
        $data = [
            [
                'id' => 1,
                'order_refund_id' => 38,
                'order_product_id' => 1383,
                'quantity' => 1,
            ],
            [
                'id' => 2,
                'order_refund_id' => 38,
                'order_product_id' => 1384,
                'quantity' => 1,
            ],
        ];

        $this->getAdapter()
            ->getQueryBuilder()
            ->delete('ship_order_refund_products')
            ->whereInList('id', array_column($data, 'id'))
            ->execute();

        $this->table('order_refund_products')
            ->insert($data)
            ->save();
    }
}
