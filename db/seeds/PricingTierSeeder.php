<?php

use Phinx\Seed\AbstractSeed;

class PricingTierSeeder extends AbstractSeed
{
    public function getDependencies()
    {
        return array_merge(parent::getDependencies(), [
            'UserSeeder',
        ]);
    }

    public function run()
    {
        parent::run();
        $data = [
            1 => [
                'id' => '1',
                'user_id' => '8',
                'pricingtiername' => 'Tier 1',
                'freeshipping' => '100',
                'flatrate' => '10',
                'currencytype' => 'CAD',
                'created' => '2015-12-29 21:31:01',
                'updated' => '2016-01-20 15:26:41',
            ],
            2 => [
                'id' => '2',
                'user_id' => '8',
                'pricingtiername' => 'Tier 2',
                'freeshipping' => '200',
                'flatrate' => '20',
                'currencytype' => 'CAD',
                'created' => '2015-12-29 21:46:58',
                'updated' => '2016-01-20 15:26:41',
            ],
            3 => [
                'id' => '3',
                'user_id' => '8',
                'pricingtiername' => 'Tier 3',
                'freeshipping' => '10',
                'flatrate' => '10',
                'currencytype' => 'CAD',
                'created' => '2016-01-20 15:26:41',
                'updated' => '2016-01-20 15:26:41',
            ],
            4 => [
                'id' => '4',
                'user_id' => '19',
                'pricingtiername' => 'Tier 1',
                'freeshipping' => '100000',
                'flatrate' => '10',
                'currencytype' => 'CAD',
                'created' => '2016-06-08 15:43:31',
                'updated' => '2016-06-08 15:43:31',
            ],
            5 => [
                'id' => '5',
                'user_id' => '12',
                'pricingtiername' => 'Tier 1',
                'freeshipping' => '100000000000',
                'flatrate' => '10',
                'currencytype' => 'CAD',
                'created' => '2016-06-10 20:22:36',
                'updated' => '2016-06-10 20:22:36',
            ],
            6 => [
                'id' => 6,
                'user_id' => 12,
                'pricingtiername' => 'Tier 2',
                'freeshipping' => 100000,
                'flatrate' => 20,
                'currencytype' => 'CAD',
            ]
        ];

        $this->getAdapter()
            ->getQueryBuilder()
            ->delete('ship_pricing_tiers')
            ->whereInList('id', array_column($data, 'id'))
            ->execute();
            
        $this->table('pricing_tiers')
            ->insert($data)
            ->save();
    }
}
