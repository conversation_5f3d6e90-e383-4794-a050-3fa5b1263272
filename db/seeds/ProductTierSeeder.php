<?php

use Phinx\Seed\AbstractSeed;

class ProductTierSeeder extends AbstractSeed
{
    public function getDependencies()
    {
        return array_merge(parent::getDependencies(), [
            'PricingTierSeeder',
            'ProductSeeder',
        ]);
    }

    public function run()
    {
        parent::run();
        $data = [
            1 => [
                'id' => '1',
                'pricingtierid' => '1',
                'product_id' => '4',
                'dealer_price' => '5.00',
                'alt_nonstock_dealer_price' => null,
                'commission' => '0.00',
                'created' => '2015-12-30 19:47:03',
                'updated' => '2016-02-03 23:29:57',
            ],
            2 => [
                'id' => '2',
                'pricingtierid' => '2',
                'product_id' => '4',
                'dealer_price' => '7.50',
                'alt_nonstock_dealer_price' => null,
                'commission' => '0.00',
                'created' => '2015-12-30 19:47:03',
                'updated' => '2016-02-03 23:29:57',
            ],
            3 => [
                'id' => '3',
                'pricingtierid' => '1',
                'product_id' => '5',
                'dealer_price' => '500.00',
                'alt_nonstock_dealer_price' => null,
                'commission' => '0.00',
                'created' => '2016-01-12 20:10:07',
                'updated' => '2016-01-12 20:10:07',
            ],
            4 => [
                'id' => '4',
                'pricingtierid' => '2',
                'product_id' => '5',
                'dealer_price' => '750.00',
                'alt_nonstock_dealer_price' => null,
                'commission' => '0.00',
                'created' => '2016-01-12 20:10:07',
                'updated' => '2016-01-12 20:10:07',
            ],
            5 => [
                'id' => '5',
                'pricingtierid' => '3',
                'product_id' => '4',
                'dealer_price' => '1.00',
                'alt_nonstock_dealer_price' => null,
                'commission' => '0.00',
                'created' => '2016-02-03 23:29:48',
                'updated' => '2016-02-03 23:29:57',
            ],
            6 => [
                'id' => '6',
                'pricingtierid' => '1',
                'product_id' => '20',
                'dealer_price' => '249.99',
                'alt_nonstock_dealer_price' => null,
                'commission' => '0.00',
                'created' => '2016-04-14 19:35:59',
                'updated' => '2016-04-14 19:35:59',
            ],
            7 => [
                'id' => '7',
                'pricingtierid' => '2',
                'product_id' => '20',
                'dealer_price' => '300.00',
                'alt_nonstock_dealer_price' => null,
                'commission' => '0.00',
                'created' => '2016-04-14 19:35:59',
                'updated' => '2016-04-14 19:35:59',
            ],
            8 => [
                'id' => '8',
                'pricingtierid' => '3',
                'product_id' => '20',
                'dealer_price' => '200.00',
                'alt_nonstock_dealer_price' => null,
                'commission' => '0.00',
                'created' => '2016-04-14 19:35:59',
                'updated' => '2016-04-14 19:35:59',
            ],
            12 => [
                'id' => '12',
                'pricingtierid' => '1',
                'product_id' => '21',
                'dealer_price' => '499.99',
                'alt_nonstock_dealer_price' => null,
                'commission' => '20.00',
                'created' => '2016-04-26 21:34:59',
                'updated' => '2017-04-18 20:48:30',
            ],
            13 => [
                'id' => '13',
                'pricingtierid' => '2',
                'product_id' => '21',
                'dealer_price' => '399.00',
                'alt_nonstock_dealer_price' => null,
                'commission' => '20.00',
                'created' => '2016-04-26 21:34:59',
                'updated' => '2017-04-18 20:48:30',
            ],
            14 => [
                'id' => '14',
                'pricingtierid' => '3',
                'product_id' => '21',
                'dealer_price' => '1000.00',
                'alt_nonstock_dealer_price' => null,
                'commission' => '20.00',
                'created' => '2016-04-26 21:34:59',
                'updated' => '2017-04-18 20:48:30',
            ],
            15 => [
                'id' => '15',
                'pricingtierid' => '1',
                'product_id' => '23',
                'dealer_price' => '499.00',
                'alt_nonstock_dealer_price' => null,
                'commission' => '0.00',
                'created' => '2016-04-26 21:36:37',
                'updated' => '2016-04-26 21:36:37',
            ],
            16 => [
                'id' => '16',
                'pricingtierid' => '2',
                'product_id' => '23',
                'dealer_price' => '200.00',
                'alt_nonstock_dealer_price' => null,
                'commission' => '0.00',
                'created' => '2016-04-26 21:36:37',
                'updated' => '2016-04-26 21:36:37',
            ],
            17 => [
                'id' => '17',
                'pricingtierid' => '3',
                'product_id' => '23',
                'dealer_price' => '300.00',
                'alt_nonstock_dealer_price' => null,
                'commission' => '0.00',
                'created' => '2016-04-26 21:36:37',
                'updated' => '2016-04-26 21:36:37',
            ],
            18 => [
                'id' => '18',
                'pricingtierid' => '1',
                'product_id' => '34',
                'dealer_price' => '375.00',
                'alt_nonstock_dealer_price' => null,
                'commission' => '0.00',
                'created' => '2016-06-01 01:52:31',
                'updated' => '2016-06-01 01:52:31',
            ],
            19 => [
                'id' => '19',
                'pricingtierid' => '2',
                'product_id' => '34',
                'dealer_price' => '2.00',
                'alt_nonstock_dealer_price' => null,
                'commission' => '0.00',
                'created' => '2016-06-01 01:52:31',
                'updated' => '2016-06-01 01:52:31',
            ],
            20 => [
                'id' => '20',
                'pricingtierid' => '3',
                'product_id' => '34',
                'dealer_price' => '3.00',
                'alt_nonstock_dealer_price' => null,
                'commission' => '0.00',
                'created' => '2016-06-01 01:52:31',
                'updated' => '2016-06-01 01:52:31',
            ],
            21 => [
                'id' => '21',
                'pricingtierid' => '5',
                'product_id' => '29',
                'dealer_price' => '60.00',
                'alt_nonstock_dealer_price' => null,
                'commission' => '12.00',
                'created' => '2016-06-11 00:25:38',
                'updated' => '2016-06-11 00:25:38',
            ],
            22 => [
                'id' => '22',
                'pricingtierid' => '5',
                'product_id' => '25',
                'dealer_price' => '349.99',
                'alt_nonstock_dealer_price' => null,
                'commission' => '70.00',
                'created' => '2016-06-11 00:25:38',
                'updated' => '2016-06-11 00:25:38',
            ],
            23 => [
                'id' => '23',
                'pricingtierid' => '5',
                'product_id' => '41',
                'dealer_price' => '0.00',
                'alt_nonstock_dealer_price' => null,
                'commission' => '20.00',
                'created' => '2024-07-03 16:00:00',
                'updated' => '2024-07-03 16:00:00',
            ],
            24 => [
                'id' => '24',
                'pricingtierid' => '5',
                'product_id' => '42',
                'dealer_price' => '0.00',
                'alt_nonstock_dealer_price' => null,
                'commission' => '20.00',
                'created' => '2024-07-03 16:00:00',
                'updated' => '2024-07-03 16:00:00',
            ],
        ];

        $this->getAdapter()
            ->getQueryBuilder()
            ->delete('ship_product_tiers')
            ->whereInList('id', array_column($data, 'id'))
            ->execute();

        $this->table('product_tiers')
            ->insert($data)
            ->save();
    }
}
