<?php

use Phinx\Seed\AbstractSeed;

class ProductTitleSeeder extends AbstractSeed
{
    const FeeProduct_MyLocalBrand = 29;
    const FeeProduct_Sirisha = 30;

    public function getDependencies()
    {
        return array_merge(parent::getDependencies(), [
            'UserSeeder',
        ]);
    }

    public function run()
    {
        $this->updateRecordsCreatedByMigration();

        $data = [
            static::FeeProduct_MyLocalBrand => [
                'id' => static::FeeProduct_MyLocalBrand,
                'user_id' => 8,
                'source_id' => '4477679697971',
                'source_type' => 'shopify',
                'handle' => 'ancillary-fee',
                'title' => 'Ancillary Fees',
                'description' => '<meta http-equiv="content-type" content="text/html; charset=utf-8">PaintCare makes it easy to recycle leftover, unwanted paint. We operate paint stewardship programs on behalf of paint manufacturers in states that have passed paint stewardship laws. Visit <a href="https://www.paintcare.org/" target="_blank" title="PaintCare.org" rel="noopener noreferrer">PaintCare.org</a> to learn more.',
                'deleted' => false,
                'created_at' => '2023-10-12 18:35:00',
                'updated_at' => '2023-10-12 18:35:00',
            ],
            static::FeeProduct_Sirisha => [
                'id' => static::FeeProduct_Sirisha,
                'user_id' => 12,
                'source_id' => '4801756233773',
                'source_type' => 'shopify',
                'handle' => 'ancillary-fees',
                'title' => 'Ancillary Fees',
                'description' => 'Order fees required by legislation in select regions.',
                'deleted' => false,
                'created_at' => '2023-10-12 18:35:00',
                'updated_at' => '2023-10-12 18:35:00',
            ],
        ];

        $this->getAdapter()
            ->getQueryBuilder()
            ->delete('ship_product_titles')
            ->whereInList('id', array_column($data, 'id'))
            ->execute();

        $this->table('product_titles')
            ->insert($data)
            ->save();
    }

    /**
     * Recreates the initial records generated by the product_titles migration.
     *
     * This method primarily serves as a reference of the initial records but can also be used to change any of them.
     *
     * @see Ship761CreateProductTitlesTable::populateExistingProductTitleRecords()
     */
    private function updateRecordsCreatedByMigration(): void
    {
        $recordsCreatedByMigration = [
            1 => [
                'id' => 1,
                'user_id' => 8,
                'source_id' => '5572320903',
                'source_type' => 'shopify',
                'handle' => 'super-bell',
                'title' => 'Super Bell',
                'deleted' => false,
                'created_at' => '2023-10-12 18:35:00',
                'updated_at' => '2023-10-12 18:35:00',
            ],
            2 => [
                'id' => 2,
                'user_id' => 8,
                'source_id' => '5572298951',
                'source_type' => 'shopify',
                'handle' => 'super-bike',
                'title' => 'Super Bike',
                'deleted' => false,
                'created_at' => '2023-10-12 18:35:00',
                'updated_at' => '2023-10-12 18:35:00',
            ],
            3 => [
                'id' => 3,
                'user_id' => 12,
                'source_id' => '3672060673',
                'source_type' => 'shopify',
                'handle' => 'bell',
                'title' => 'Always Sell Direct',
                'deleted' => false,
                'created_at' => '2023-10-12 18:35:00',
                'updated_at' => '2023-10-12 18:35:00',
            ],
            4 => [
                'id' => 4,
                'user_id' => 12,
                'source_id' => '3672056577',
                'source_type' => 'shopify',
                'handle' => 'bicycle',
                'title' => 'Bicycle',
                'deleted' => false,
                'created_at' => '2023-10-12 18:35:00',
                'updated_at' => '2023-10-12 18:35:00',
            ],
            5 => [
                'id' => 5,
                'user_id' => 12,
                'source_id' => '5510560257',
                'source_type' => 'shopify',
                'handle' => 'in-stock-dealer-protect',
                'title' => 'In-Stock Dealer Protect',
                'deleted' => false,
                'created_at' => '2023-10-12 18:35:00',
                'updated_at' => '2023-10-12 18:35:00',
            ],
            6 => [
                'id' => 6,
                'user_id' => 12,
                'source_id' => '5510567041',
                'source_type' => 'shopify',
                'handle' => 'in-stock-dealer-protect-2',
                'title' => 'In-Stock Dealer Protect 2',
                'deleted' => false,
                'created_at' => '2023-10-12 18:35:00',
                'updated_at' => '2023-10-12 18:35:00',
            ],
            7 => [
                'id' => 7,
                'user_id' => 12,
                'source_id' => '5510586241',
                'source_type' => 'shopify',
                'handle' => 'no-upc-1',
                'title' => 'Missing UPC',
                'deleted' => false,
                'created_at' => '2023-10-12 18:35:00',
                'updated_at' => '2023-10-12 18:35:00',
            ],
            8 => [
                'id' => 8,
                'user_id' => 12,
                'source_id' => '3841410817',
                'source_type' => 'shopify',
                'handle' => 'non-stock',
                'title' => 'No Stock',
                'deleted' => false,
                'created_at' => '2023-10-12 18:35:00',
                'updated_at' => '2023-10-12 18:35:00',
            ],
            9 => [
                'id' => 9,
                'user_id' => 12,
                'source_id' => '5510611841',
                'source_type' => 'shopify',
                'handle' => 'retail-exclusive',
                'title' => 'Retail Exclusive Product',
                'deleted' => false,
                'created_at' => '2023-10-12 18:35:00',
                'updated_at' => '2023-10-12 18:35:00',
            ],
            10 => [
                'id' => 10,
                'user_id' => 12,
                'source_id' => '5510624065',
                'source_type' => 'shopify',
                'handle' => 'sell-direct-and-in-store-pick-up',
                'title' => 'In-Stock Only',
                'deleted' => false,
                'created_at' => '2023-10-12 18:35:00',
                'updated_at' => '2023-10-12 18:35:00',
            ],
            11 => [
                'id' => 11,
                'user_id' => 12,
                'source_id' => '5510643393',
                'source_type' => 'shopify',
                'handle' => 'sell-direct-exclusively',
                'title' => 'Sell Direct Exclusively',
                'deleted' => false,
                'created_at' => '2023-10-12 18:35:00',
                'updated_at' => '2023-10-12 18:35:00',
            ],
            12 => [
                'id' => 12,
                'user_id' => 12,
                'source_id' => '5510647553',
                'source_type' => 'shopify',
                'handle' => 'sell-direct-unless-protected',
                'title' => 'Sell Direct Unless Bundled',
                'deleted' => false,
                'created_at' => '2023-10-12 18:35:00',
                'updated_at' => '2023-10-12 18:35:00',
            ],
            13 => [
                'id' => 13,
                'user_id' => 12,
                'source_id' => '6676765121',
                'source_type' => 'shopify',
                'handle' => 'no-upc',
                'title' => 'No UPC',
                'deleted' => false,
                'created_at' => '2023-10-12 18:35:00',
                'updated_at' => '2023-10-12 18:35:00',
            ],
            14 => [
                'id' => 14,
                'user_id' => 12,
                'source_id' => '6676988865',
                'source_type' => 'shopify',
                'handle' => 'no-upc-2',
                'title' => 'No UPC 2',
                'deleted' => false,
                'created_at' => '2023-10-12 18:35:00',
                'updated_at' => '2023-10-12 18:35:00',
            ],
            15 => [
                'id' => 15,
                'user_id' => 12,
                'source_id' => '8239299521',
                'source_type' => 'shopify',
                'handle' => 'variant',
                'title' => 'Variant',
                'deleted' => false,
                'created_at' => '2023-10-12 18:35:00',
                'updated_at' => '2023-10-12 18:35:00',
            ],
            16 => [
                'id' => 16,
                'user_id' => 19,
                'source_id' => '23',
                'source_type' => 'woocommerce',
                'handle' => null,
                'title' => 'Sell Direct Exclusively',
                'deleted' => false,
                'created_at' => '2023-10-12 18:35:00',
                'updated_at' => '2023-10-12 18:35:00',
            ],
            17 => [
                'id' => 17,
                'user_id' => 19,
                'source_id' => '12',
                'source_type' => 'woocommerce',
                'handle' => null,
                'title' => 'Woo Bike',
                'deleted' => false,
                'created_at' => '2023-10-12 18:35:00',
                'updated_at' => '2023-10-12 18:35:00',
            ],
            18 => [
                'id' => 18,
                'user_id' => 19,
                'source_id' => '37',
                'source_type' => 'woocommerce',
                'handle' => 'no-upc',
                'title' => 'No UPC',
                'deleted' => false,
                'created_at' => '2023-10-12 18:35:00',
                'updated_at' => '2023-10-12 18:35:00',
            ],
            19 => [
                'id' => 19,
                'user_id' => 19,
                'source_id' => '36',
                'source_type' => 'woocommerce',
                'handle' => 'no-stock',
                'title' => 'No Stock',
                'deleted' => false,
                'created_at' => '2023-10-12 18:35:00',
                'updated_at' => '2023-10-12 18:35:00',
            ],
            20 => [
                'id' => 20,
                'user_id' => 19,
                'source_id' => '35',
                'source_type' => 'woocommerce',
                'handle' => 'in-stock-dealer-protect',
                'title' => 'In Stock Dealer Protect',
                'deleted' => false,
                'created_at' => '2023-10-12 18:35:00',
                'updated_at' => '2023-10-12 18:35:00',
            ],
            21 => [
                'id' => 21,
                'user_id' => 19,
                'source_id' => '34',
                'source_type' => 'woocommerce',
                'handle' => 'always-sell-direct',
                'title' => 'Always Sell Direct',
                'deleted' => false,
                'created_at' => '2023-10-12 18:35:00',
                'updated_at' => '2023-10-12 18:35:00',
            ],
            22 => [
                'id' => 22,
                'user_id' => 19,
                'source_id' => '33',
                'source_type' => 'woocommerce',
                'handle' => 'sell-direct-unless-bundled',
                'title' => 'Sell Direct Unless Bundled',
                'deleted' => false,
                'created_at' => '2023-10-12 18:35:00',
                'updated_at' => '2023-10-12 18:35:00',
            ],
            23 => [
                'id' => 23,
                'user_id' => 19,
                'source_id' => '32',
                'source_type' => 'woocommerce',
                'handle' => 'sell-direct-and-in-store-pickup',
                'title' => 'Sell Direct and In-Store Pickup',
                'deleted' => false,
                'created_at' => '2023-10-12 18:35:00',
                'updated_at' => '2023-10-12 18:35:00',
            ],
            24 => [
                'id' => 24,
                'user_id' => 19,
                'source_id' => '31',
                'source_type' => 'woocommerce',
                'handle' => 'sell-direct-exclusively',
                'title' => 'Sell Direct Exclusively',
                'deleted' => false,
                'created_at' => '2023-10-12 18:35:00',
                'updated_at' => '2023-10-12 18:35:00',
            ],
            25 => [
                'id' => 25,
                'user_id' => 19,
                'source_id' => '30',
                'source_type' => 'woocommerce',
                'handle' => 'retail-product-only',
                'title' => 'Retail Product Only',
                'deleted' => false,
                'created_at' => '2023-10-12 18:35:00',
                'updated_at' => '2023-10-12 18:35:00',
            ],
            26 => [
                'id' => 26,
                'user_id' => 19,
                'source_id' => '19',
                'source_type' => 'woocommerce',
                'handle' => 'test',
                'title' => 'Test',
                'deleted' => false,
                'created_at' => '2023-10-12 18:35:00',
                'updated_at' => '2023-10-12 18:35:00',
            ],
            27 => [
                'id' => 27,
                'user_id' => 8,
                'source_id' => '11934244490',
                'source_type' => 'shopify',
                'handle' => 'super-generic-accessory',
                'title' => 'Generic Accessory',
                'deleted' => false,
                'created_at' => '2023-10-12 18:35:00',
                'updated_at' => '2023-10-12 18:35:00',
            ],
            28 => [
                'id' => 28,
                'user_id' => 8,
                'source_id' => '1588942700595',
                'source_type' => 'shopify',
                'handle' => 'e-shirt',
                'title' => 'T-Shirt',
                'deleted' => false,
                'created_at' => '2023-10-12 18:35:00',
                'updated_at' => '2023-10-12 18:35:00',
            ],
        ];

        $ids = array_keys($recordsCreatedByMigration);
        $allFields = $this->getAdapter()->getQueryBuilder()
            ->select('*')
            ->from('ship_product_titles')
            ->whereInList('id', $ids)
            ->execute()
            ->fetchAll('assoc');
        $allFieldsById = array_combine(array_column($allFields, 'id'), $allFields);
        $missingFieldsLog = [];
        foreach ($ids as $id) {
            $missingFields = array_diff_key($allFieldsById[$id], $recordsCreatedByMigration[$id]);
            if ($missingFields) {
                $missingFieldsLog[$id] = $missingFields;
                $recordsCreatedByMigration[$id] = array_merge($allFieldsById[$id], $recordsCreatedByMigration[$id]);
            }
        }
        if ($missingFieldsLog) {
            $isVeryVerbose = $this->getOutput()->isVeryVerbose();
            $this->getOutput()->writeln(sprintf(
                '<comment>%s() missing fields:</comment> %s',
                __METHOD__,
                $isVeryVerbose
                    ? json_encode($missingFieldsLog, JSON_PRETTY_PRINT)
                    : implode(', ', array_keys(array_reduce($missingFieldsLog, fn(array $set, array $fields): array => $set + $fields, [])))
            ));
            if (!$isVeryVerbose) {
                $this->getOutput()->writeln('<comment>Run this command with -vv to see the missing field values that were filled in.</comment>');
            }
        }

        // Temporarily disable FK checks to avoid breaking FK references from tables not managed by seeders
        $this->execute('/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;');

        $this->table('product_titles')->truncate();
        $this->table('product_titles')->insert($recordsCreatedByMigration)->save();

        $this->execute('/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;');
    }
}
