<?php

use Phinx\Seed\AbstractSeed;

class <PERSON>eUser<PERSON>eeder extends AbstractSeed
{
    public function getDependencies()
    {
        return array_merge(parent::getDependencies(), [
            'UserSeeder',
        ]);
    }

    public function run()
    {
        $data = [
            1 => [
                'id' => 1,
                'user_id' => 1,
                'cus_user_id' => null,
                'charges_enabled' => 1,
                'payouts_enabled' => 1,
                'is_activated' => 1,
                'stripe_user_id' => 'acct_194DCmJKdns8gzMQ',
                'stripe_cus_id' => 'cus_CxpukRJo3jGdmK',
                'stripe_secret_key' => 'sk_test_Sg2DjL1u3ZTDj5lAdhyK6MQ8',
                'stripe_publishable_key' => 'pk_test_XJENDrAJANWWe6igu2bd2euY',
                'refresh_token' => 'rt_CxpwqvEnFIXNBDHJLz2tnIdUxsPNYrFdF31cm7DNwPq6tfnf',
                'subscription_period_start' => '2018-05-31 19:06:48',
                'subscription_period_end' => '2018-06-30 19:06:48',
                'stripe_email' => '<EMAIL>',
                'created' => '2018-05-31 19:06:49',
                'updated' => '2018-05-31 19:09:08',
            ],
            2 => [
                'id' => 2,
                'user_id' => 2,
                'cus_user_id' => null,
                'charges_enabled' => 1,
                'payouts_enabled' => 1,
                'is_activated' => 1,
                'stripe_user_id' => 'acct_17FVVxLBFYPhk3Kp',
                'stripe_cus_id' => null,
                'stripe_secret_key' => 'sk_test_FxFe1A3QV3uhgpPYFh8WT8S6',
                'stripe_publishable_key' => 'pk_test_RLe6G1keKeNkHFNMgKzHbADh',
                'refresh_token' => 'rt_CxqDdZub6YeS7FiaLOuMoQAfAd7YDtfl2Nxwq61s1RVysmZs',
                'subscription_period_start' => null,
                'subscription_period_end' => null,
                'stripe_email' => '<EMAIL>',
                'created' => '2018-05-31 19:26:18',
                'updated' => '2018-05-31 19:26:18',
            ],
            3 => [
                'id' => 3,
                'user_id' => 7,
                'cus_user_id' => null,
                'charges_enabled' => 1,
                'payouts_enabled' => 1,
                'is_activated' => 1,
                'stripe_user_id' => 'acct_17FVVxLBFYPhk3Kp',
                'stripe_cus_id' => null,
                'stripe_secret_key' => 'sk_test_FxFe1A3QV3uhgpPYFh8WT8S6',
                'stripe_publishable_key' => 'pk_test_RLe6G1keKeNkHFNMgKzHbADh',
                'refresh_token' => 'rt_CxqDdZub6YeS7FiaLOuMoQAfAd7YDtfl2Nxwq61s1RVysmZs',
                'subscription_period_start' => null,
                'subscription_period_end' => null,
                'stripe_email' => '<EMAIL>',
                'created' => '2018-05-31 19:26:18',
                'updated' => '2018-05-31 19:26:18',
            ],
            4 => [
                'id' => 4,
                'user_id' => 8,
                'cus_user_id' => null,
                'charges_enabled' => 1,
                'payouts_enabled' => 1,
                'is_activated' => 1,
                'stripe_user_id' => 'acct_194DCmJKdns8gzMQ',
                'stripe_cus_id' => 'cus_CxpukRJo3jGdmK',
                'stripe_secret_key' => 'sk_test_Sg2DjL1u3ZTDj5lAdhyK6MQ8',
                'stripe_publishable_key' => 'pk_test_XJENDrAJANWWe6igu2bd2euY',
                'refresh_token' => 'rt_CxpwqvEnFIXNBDHJLz2tnIdUxsPNYrFdF31cm7DNwPq6tfnf',
                'subscription_period_start' => '2018-05-31 19:06:48',
                'subscription_period_end' => '2018-06-30 19:06:48',
                'stripe_email' => '<EMAIL>',
                'created' => '2018-05-31 19:06:49',
                'updated' => '2018-05-31 19:09:08',
            ],
            5 => [
                'id' => 5,
                'user_id' => 10,
                'cus_user_id' => null,
                'charges_enabled' => 1,
                'payouts_enabled' => 1,
                'is_activated' => 1,
                'stripe_user_id' => 'acct_17FVVxLBFYPhk3Kp',
                'stripe_cus_id' => null,
                'stripe_secret_key' => 'sk_test_FxFe1A3QV3uhgpPYFh8WT8S6',
                'stripe_publishable_key' => 'pk_test_RLe6G1keKeNkHFNMgKzHbADh',
                'refresh_token' => 'rt_CxqDdZub6YeS7FiaLOuMoQAfAd7YDtfl2Nxwq61s1RVysmZs',
                'subscription_period_start' => null,
                'subscription_period_end' => null,
                'stripe_email' => '<EMAIL>',
                'created' => '2018-05-31 19:26:18',
                'updated' => '2018-05-31 19:26:18',
            ],
            6 => [
                'id' => 6,
                'user_id' => 12,
                'cus_user_id' => null,
                'charges_enabled' => 1,
                'payouts_enabled' => 1,
                'is_activated' => 1,
                'stripe_user_id' => 'acct_194DCmJKdns8gzMQ',
                'stripe_cus_id' => 'cus_CxpukRJo3jGdmK',
                'stripe_secret_key' => 'sk_test_Sg2DjL1u3ZTDj5lAdhyK6MQ8',
                'stripe_publishable_key' => 'pk_test_XJENDrAJANWWe6igu2bd2euY',
                'refresh_token' => 'rt_CxpwqvEnFIXNBDHJLz2tnIdUxsPNYrFdF31cm7DNwPq6tfnf',
                'subscription_period_start' => '2018-05-31 19:06:48',
                'subscription_period_end' => '2018-06-30 19:06:48',
                'stripe_email' => '<EMAIL>',
                'created' => '2018-05-31 19:06:49',
                'updated' => '2018-05-31 19:09:08',
            ],
            7 => [
                'id' => 7,
                'user_id' => 15,
                'cus_user_id' => null,
                'charges_enabled' => 1,
                'payouts_enabled' => 1,
                'is_activated' => 1,
                'stripe_user_id' => 'acct_17FVVxLBFYPhk3Kp',
                'stripe_cus_id' => null,
                'stripe_secret_key' => 'sk_test_FxFe1A3QV3uhgpPYFh8WT8S6',
                'stripe_publishable_key' => 'pk_test_RLe6G1keKeNkHFNMgKzHbADh',
                'refresh_token' => 'rt_CxqDdZub6YeS7FiaLOuMoQAfAd7YDtfl2Nxwq61s1RVysmZs',
                'subscription_period_start' => null,
                'subscription_period_end' => null,
                'stripe_email' => '<EMAIL>',
                'created' => '2018-05-31 19:26:18',
                'updated' => '2018-05-31 19:26:18',
            ],
            8 => [
                'id' => 8,
                'user_id' => 16,
                'cus_user_id' => null,
                'charges_enabled' => 1,
                'payouts_enabled' => 1,
                'is_activated' => 1,
                'stripe_user_id' => 'acct_17FVVxLBFYPhk3Kp',
                'stripe_cus_id' => null,
                'stripe_secret_key' => 'sk_test_FxFe1A3QV3uhgpPYFh8WT8S6',
                'stripe_publishable_key' => 'pk_test_RLe6G1keKeNkHFNMgKzHbADh',
                'refresh_token' => 'rt_CxqDdZub6YeS7FiaLOuMoQAfAd7YDtfl2Nxwq61s1RVysmZs',
                'subscription_period_start' => null,
                'subscription_period_end' => null,
                'stripe_email' => '<EMAIL>',
                'created' => '2018-05-31 19:26:18',
                'updated' => '2018-05-31 19:26:18',
            ],
            9 => [
                'id' => 9,
                'user_id' => 18,
                'cus_user_id' => null,
                'charges_enabled' => 1,
                'payouts_enabled' => 1,
                'is_activated' => 1,
                'stripe_user_id' => 'acct_17FVVxLBFYPhk3Kp',
                'stripe_cus_id' => null,
                'stripe_secret_key' => 'sk_test_FxFe1A3QV3uhgpPYFh8WT8S6',
                'stripe_publishable_key' => 'pk_test_RLe6G1keKeNkHFNMgKzHbADh',
                'refresh_token' => 'rt_CxqDdZub6YeS7FiaLOuMoQAfAd7YDtfl2Nxwq61s1RVysmZs',
                'subscription_period_start' => null,
                'subscription_period_end' => null,
                'stripe_email' => '<EMAIL>',
                'created' => '2018-05-31 19:26:18',
                'updated' => '2018-05-31 19:26:18',
            ],
            10 => [
                'id' => 10,
                'user_id' => 19,
                'cus_user_id' => null,
                'charges_enabled' => 1,
                'payouts_enabled' => 1,
                'is_activated' => 1,
                'stripe_user_id' => 'acct_194DCmJKdns8gzMQ',
                'stripe_cus_id' => 'cus_CxpukRJo3jGdmK',
                'stripe_secret_key' => 'sk_test_Sg2DjL1u3ZTDj5lAdhyK6MQ8',
                'stripe_publishable_key' => 'pk_test_XJENDrAJANWWe6igu2bd2euY',
                'refresh_token' => 'rt_CxpwqvEnFIXNBDHJLz2tnIdUxsPNYrFdF31cm7DNwPq6tfnf',
                'subscription_period_start' => '2018-05-31 19:06:48',
                'subscription_period_end' => '2018-06-30 19:06:48',
                'stripe_email' => '<EMAIL>',
                'created' => '2018-05-31 19:06:49',
                'updated' => '2018-05-31 19:09:08',
            ],
            11 => [
                'id' => 11,
                'user_id' => 22,
                'cus_user_id' => null,
                'charges_enabled' => 1,
                'payouts_enabled' => 1,
                'is_activated' => 1,
                'stripe_user_id' => 'acct_1O7hhlCsFCCXfy3E',
                'stripe_cus_id' => null,
                'stripe_secret_key' => 'sk_test_51O7hhlCsFCCXfy3EsUBljZ3ICDqlNcPbpDw4kfSUNyGTAdbRAPEF1RgazRsfXGn68SU1NIapWEGcezoVjFyrElKw000dIxmErA',
                'stripe_publishable_key' => 'pk_test_51O7hhlCsFCCXfy3EFLxjoTzMhq0C8mGsgVEUdmbXnWiHVa9smkDVdyq5uHnWiQKIi9ng6G5DYtlXSwJCO7HazdUF00erAgTZQX',
                'refresh_token' => 'rt_OvYpomf63NbU5qHZRATe3F5h25cT8IWYBfOINMnzuE2becq0',
                'subscription_period_start' => null,
                'subscription_period_end' => null,
                'stripe_email' => '<EMAIL>',
                'created' => '2023-11-01 16:56:14',
                'updated' => '2023-11-01 16:56:14',
            ],
        ];

        $this->getAdapter()->getQueryBuilder()
            ->delete('ship_stripe_users')
            ->whereInList('id', array_column($data, 'id'))
            ->execute();

        $this->table('stripe_users')
            ->insert($data)
            ->save();
    }
}
