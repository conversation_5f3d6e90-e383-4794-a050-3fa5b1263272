<?php

use Phinx\Seed\AbstractSeed;

class UnitBasedShippingRateSeeder extends AbstractSeed
{
    public function getDependencies()
    {
        return array_merge(parent::getDependencies(), [
            'UserSeeder',
            'ShippingZoneSeeder',
        ]);
    }

    public function run()
    {
        parent::run();
        $data = [
            [
                'id' => '1',
                'user_id' => '8',
                'zone_id' => '1',
                'name' => 'Standard Unit Rate',
                'min' => '0',
                'max' => '10',
                'free_shipping' => false,
                'amount' => '20.00',
                'created_at' => '2017-04-05 19:36:24',
                'updated_at' => '2017-04-05 19:36:24',
            ],
            [
                'id' => '2',
                'user_id' => '12',
                'zone_id' => '3',
                'name' => 'Bulk Unit Shipping',
                'min' => '11',
                'max' => '20',
                'free_shipping' => false,
                'amount' => '10.00',
                'created_at' => '2017-08-31 04:21:05',
                'updated_at' => '2020-05-28 18:03:34',
            ],
            [
                'id' => '3',
                'user_id' => '12',
                'zone_id' => '3',
                'name' => 'Free Shipping',
                'min' => '21',
                'max' => '10000',
                'free_shipping' => true,
                'amount' => '0.00',
                'created_at' => '2017-08-31 04:21:05',
                'updated_at' => '2020-05-28 18:03:34',
            ],
        ];
        $this
            ->getAdapter()
            ->getQueryBuilder()
            ->delete('ship_unit_based_shipping_rates')
            ->execute();
        $this
            ->table('unit_based_shipping_rates')
            ->insert($data)
            ->save();
    }
}
