<?php

use Phinx\Seed\AbstractSeed;

class UserCategorySeeder extends AbstractSeed
{
    public function getDependencies()
    {
        return array_merge(parent::getDependencies(), [
            'UserSeeder',
            // Dependencies not yet implemented
            //'CategorySeeder',
        ]);
    }

    public function run()
    {
        parent::run();
        $data = [
            1 => ['id' => '1', 'user_id' => '1', 'category_id' => '34', 'user_type' => 'Manufacturer'],
            50 => ['id' => '50', 'user_id' => '8', 'category_id' => '4', 'user_type' => 'Manufacturer'],
            52 => ['id' => '52', 'user_id' => '8', 'category_id' => '28', 'user_type' => 'Manufacturer'],
            53 => ['id' => '53', 'user_id' => '7', 'category_id' => '4', 'user_type' => 'Retailer'],
            54 => ['id' => '54', 'user_id' => '7', 'category_id' => '29', 'user_type' => 'Retailer'],
            55 => ['id' => '55', 'user_id' => '7', 'category_id' => '10', 'user_type' => 'Retailer'],
            56 => ['id' => '56', 'user_id' => '7', 'category_id' => '13', 'user_type' => 'Retailer'],
            57 => ['id' => '57', 'user_id' => '7', 'category_id' => '28', 'user_type' => 'Retailer'],
            60 => ['id' => '60', 'user_id' => '10', 'category_id' => '4', 'user_type' => 'Retailer'],
            61 => ['id' => '61', 'user_id' => '10', 'category_id' => '13', 'user_type' => 'Retailer'],
            62 => ['id' => '62', 'user_id' => '10', 'category_id' => '28', 'user_type' => 'Retailer'],
            64 => ['id' => '64', 'user_id' => '11', 'category_id' => '4', 'user_type' => 'Retailer'],
            65 => ['id' => '65', 'user_id' => '11', 'category_id' => '28', 'user_type' => 'Retailer'],
            66 => ['id' => '66', 'user_id' => '2', 'category_id' => '4', 'user_type' => 'Retailer'],
            67 => ['id' => '67', 'user_id' => '2', 'category_id' => '28', 'user_type' => 'Retailer'],
            68 => ['id' => '68', 'user_id' => '12', 'category_id' => '28', 'user_type' => 'Manufacturer'],
            69 => ['id' => '69', 'user_id' => '12', 'category_id' => '42', 'user_type' => 'Manufacturer'],
            70 => ['id' => '70', 'user_id' => '15', 'category_id' => '28', 'user_type' => 'Retailer'],
            71 => ['id' => '71', 'user_id' => '15', 'category_id' => '1', 'user_type' => 'Retailer'],
            72 => ['id' => '72', 'user_id' => '15', 'category_id' => '42', 'user_type' => 'Retailer'],
            73 => ['id' => '73', 'user_id' => '16', 'category_id' => '28', 'user_type' => 'Retailer'],
            74 => ['id' => '74', 'user_id' => '16', 'category_id' => '1', 'user_type' => 'Retailer'],
            75 => ['id' => '75', 'user_id' => '16', 'category_id' => '42', 'user_type' => 'Retailer'],
            76 => ['id' => '76', 'user_id' => '17', 'category_id' => '4', 'user_type' => 'Retailer'],
            77 => ['id' => '77', 'user_id' => '17', 'category_id' => '10', 'user_type' => 'Retailer'],
            78 => ['id' => '78', 'user_id' => '17', 'category_id' => '13', 'user_type' => 'Retailer'],
            79 => ['id' => '79', 'user_id' => '17', 'category_id' => '28', 'user_type' => 'Retailer'],
            80 => ['id' => '80', 'user_id' => '17', 'category_id' => '29', 'user_type' => 'Retailer'],
            82 => ['id' => '82', 'user_id' => '10', 'category_id' => '29', 'user_type' => 'Retailer'],
            84 => ['id' => '84', 'user_id' => '8', 'category_id' => '34', 'user_type' => 'Manufacturer'],
            85 => ['id' => '85', 'user_id' => '18', 'category_id' => '4', 'user_type' => 'Retailer'],
            86 => ['id' => '86', 'user_id' => '18', 'category_id' => '28', 'user_type' => 'Retailer'],
            87 => ['id' => '87', 'user_id' => '18', 'category_id' => '32', 'user_type' => 'Retailer'],
            88 => ['id' => '88', 'user_id' => '19', 'category_id' => '4', 'user_type' => 'Manufacturer'],
            89 => ['id' => '89', 'user_id' => '19', 'category_id' => '28', 'user_type' => 'Manufacturer'],
            90 => ['id' => '90', 'user_id' => '19', 'category_id' => '44', 'user_type' => 'Manufacturer'],
            91 => ['id' => '91', 'user_id' => '27', 'category_id' => '1', 'user_type' => 'Retailer'],
            92 => ['id' => '92', 'user_id' => '27', 'category_id' => '28', 'user_type' => 'Retailer'],
            93 => ['id' => '93', 'user_id' => '27', 'category_id' => '42', 'user_type' => 'Retailer'],
        ];

        $this->getAdapter()
            ->getQueryBuilder()
            ->delete('ship_user_categories')
            ->whereInList('id', array_column($data, 'id'))
            ->execute();

        $this->table('user_categories')
            ->insert($data)
            ->save();
    }
}
