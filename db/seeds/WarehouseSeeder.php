<?php

use Phinx\Seed\AbstractSeed;

class WarehouseSeeder extends AbstractSeed
{
    public function run()
    {
        parent::run();
        $data = [
            [
                'id' => '4',
                'user_id' => '8',
                'source_id' => '36179574835',
                'name' => 'Reserved',
                'is_active' => true,
                'created_at' => '2020-05-05 16:41:02',
                'updated_at' => '2020-05-05 16:41:02',
            ],
            [
                'id' => '5',
                'user_id' => '12',
                'source_id' => '46599372845',
                'name' => 'Reserved',
                'is_active' => true,
                'created_at' => '2020-05-08 14:29:11',
                'updated_at' => '2020-05-08 14:29:11',
            ],
            // Product::getBestWarehouse() test data
            [
                'id' => '6',
                'user_id' => '13',
                'source_id' => '574372',
                'name' => 'Reserved',
                'is_active' => true,
                'created_at' => '2020-05-08 14:29:11',
                'updated_at' => '2020-05-08 14:29:11',
            ],
            [
                'id' => '7',
                'user_id' => '13',
                'source_id' => '5742346372',
                'name' => 'EAST',
                'is_active' => true,
                'created_at' => '2020-05-08 14:29:11',
                'updated_at' => '2020-05-08 14:29:11',
            ],
            [
                'id' => '8',
                'user_id' => '13',
                'source_id' => '23462346',
                'name' => 'WEST',
                'is_active' => true,
                'created_at' => '2020-05-08 14:29:11',
                'updated_at' => '2020-05-08 14:29:11',
            ],
            // Default record generated for WooCommerce brand
            [
                'id' => '9',
                'user_id' => '19',
                'source_id' => null,
                'name' => 'Default',
                'is_active' => true,
                'created_at' => '2021-02-11 20:45:45',
                'updated_at' => '2021-02-11 20:45:45',
            ],
        ];

        $this->getAdapter()
             ->getQueryBuilder()
             ->delete('ship_warehouses')
             ->whereInList('id', array_column($data, 'id'))
             ->execute();

        $this->table('warehouses')
             ->insert($data)
             ->save();
    }
}
