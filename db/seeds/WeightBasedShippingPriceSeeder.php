<?php

use Phinx\Seed\AbstractSeed;

class WeightBasedShippingPriceSeeder extends AbstractSeed
{
    public function getDependencies()
    {
        return array_merge(parent::getDependencies(), [
            'UserSeeder',
            'ShippingZoneSeeder',
        ]);
    }

    public function run()
    {
        parent::run();
        $data = [
            [
                'id' => '1',
                'user_id' => '8',
                'zone_id' => '1',
                'name' => 'Standard Weights',
                'min' => '0.00',
                'max' => '1000.00',
                'unit' => 'lb',
                'amount' => '6.66',
                'created_at' => '2017-04-05 19:36:24',
                'updated_at' => '2017-04-05 19:36:24',
            ],
            [
                'id' => '2',
                'user_id' => '12',
                'zone_id' => '3',
                'name' => 'Standard Shipping',
                'min' => '0.00',
                'max' => '1000.00',
                'unit' => 'lb',
                'amount' => '15.00',
                'created_at' => '2017-05-24 21:08:06',
                'updated_at' => '2017-05-24 21:08:06',
            ],
            [
                'id' => '3',
                'user_id' => '12',
                'zone_id' => '2',
                'name' => 'Standard Weights',
                'min' => '0.00',
                'max' => '1000.00',
                'unit' => 'lb',
                'amount' => '25.00',
                'created_at' => '2017-08-31 04:21:05',
                'updated_at' => '2017-08-31 04:21:05',
            ],
            [
                'id' => '4',
                'user_id' => '12',
                'zone_id' => '3',
                'name' => 'Heavy Freight Shipping',
                'min' => '1000.00',
                'max' => '10000.00',
                'unit' => 'lb',
                'amount_type' => 'flat',
                'amount' => '250.00',
                'created_at' => '2020-05-28 18:03:34',
                'updated_at' => '2020-05-28 18:03:34',
            ],
        ];
        $this
            ->getAdapter()
            ->getQueryBuilder()
            ->delete('ship_weight_based_shipping_prices')
            ->execute();
        $this
            ->table('weight_based_shipping_prices')
            ->insert($data)
            ->save();
    }
}
