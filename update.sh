#!/bin/sh
REMOTE_BRANCH=origin/Demo

has_update() {
  git fetch origin --prune
  if [ "$(git rev-list HEAD..$REMOTE_BRANCH --count)" != "0" ]; then
    true
  else
    echo "---------------------------------------------------------------"
    echo "App in '$PWD' is up-to-date with '$REMOTE_BRANCH'."
    echo "---------------------------------------------------------------"
    false
  fi
}

do_update() {
  echo "---------------------------------------------------------------"
  echo "Updating app in '$PWD'."
  echo "---------------------------------------------------------------"
  git rebase $REMOTE_BRANCH --autostash
  sh setup.sh
}

if has_update; then
  do_update
fi

if [ -f "app/webroot/_shopify/setup.sh" ] && ( cd app/webroot/_shopify && has_update ); then
  echo "Temporarily renaming app/webroot/_shopify to app/webroot/shopify for update"
  mv app/webroot/_shopify app/webroot/shopify

  ( cd app/webroot/shopify && do_update )

  echo "Renaming app/webroot/shopify to app/webroot/_shopify to simulate deletion"
  mv app/webroot/shopify app/webroot/_shopify

  echo "Flushing memcached in case something was saved from app/webroot/shopify..."
  echo 'flush_all' | nc localhost 11211
  echo ""
fi


exit
# Reference to previous version of this script

git fetch origin
cd app/webroot/shopify
git fetch origin
git rebase origin/master --autostash
sh setup.sh

cd ../../../
git fetch origin
git rebase origin/master --autostash
sh setup.sh


cd app/webroot/shopify
sh setup.sh

cd ../../../
sh resetcache.sh
sh resetcache.sh
